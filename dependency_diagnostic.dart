#!/usr/bin/env dart
// ignore_for_file: avoid_print

/// Dependency diagnostic script to identify the firebase_auth_mocks issue
library;

/// This script runs independently to help diagnose the problem

import 'dart:io';

void main() async {
  print('🔍 Dependency Diagnostic Tool\n');

  // Check if pubspec.yaml contains firebase_auth_mocks
  print('📋 Checking pubspec.yaml...');
  final pubspecFile = File('pubspec.yaml');
  if (await pubspecFile.exists()) {
    final content = await pubspecFile.readAsString();
    if (content.contains('firebase_auth_mocks')) {
      print('❌ Found firebase_auth_mocks in pubspec.yaml');
      final lines = content.split('\n');
      for (int i = 0; i < lines.length; i++) {
        if (lines[i].contains('firebase_auth_mocks')) {
          print('   Line ${i + 1}: ${lines[i].trim()}');
        }
      }
    } else {
      print('✅ No firebase_auth_mocks found in pubspec.yaml');
    }
  } else {
    print('❌ pubspec.yaml not found');
  }

  // Check if pubspec.lock exists and contains firebase_auth_mocks
  print('\n📋 Checking pubspec.lock...');
  final lockFile = File('pubspec.lock');
  if (await lockFile.exists()) {
    final content = await lockFile.readAsString();
    if (content.contains('firebase_auth_mocks')) {
      print(
        '❌ Found firebase_auth_mocks in pubspec.lock (this is the problem!)',
      );
      print('   Solution: Delete pubspec.lock and run flutter pub get');
    } else {
      print('✅ No firebase_auth_mocks found in pubspec.lock');
    }
  } else {
    print('✅ pubspec.lock does not exist');
  }

  // Check test files for imports
  print('\n📋 Checking test files...');
  final testDir = Directory('test');
  if (await testDir.exists()) {
    await for (final entity in testDir.list(recursive: true)) {
      if (entity is File && entity.path.endsWith('.dart')) {
        final content = await entity.readAsString();
        if (content.contains('firebase_auth_mocks')) {
          print('❌ Found firebase_auth_mocks import in ${entity.path}');
        }
      }
    }
    print('✅ Test files checked');
  } else {
    print('✅ No test directory found');
  }

  // Check for hidden files
  print('\n📋 Checking for hidden files...');
  final currentDir = Directory('.');
  await for (final entity in currentDir.list(recursive: false)) {
    if (entity.path.startsWith('./.')) {
      print('   Found hidden file/dir: ${entity.path}');
    }
  }

  // Check Flutter cache
  print('\n📋 Checking Flutter environment...');
  final homeDir = Platform.environment['HOME'];
  if (homeDir != null) {
    final pubCacheDir = Directory('$homeDir/.pub-cache');
    if (await pubCacheDir.exists()) {
      print('✅ Pub cache exists at $homeDir/.pub-cache');
    } else {
      print('❌ Pub cache not found');
    }
  }

  // Provide solutions
  print('\n🔧 Recommended Solutions:');
  print('1. Delete pubspec.lock: rm pubspec.lock');
  print('2. Clean Flutter: flutter clean');
  print('3. Clear pub cache: rm -rf ~/.pub-cache');
  print('4. Repair cache: flutter pub cache repair');
  print('5. Get dependencies: flutter pub get');

  print('\n🚀 Alternative Approach:');
  print('If the issue persists, temporarily disable tests:');
  print('   mv test test_disabled');
  print('   flutter pub get');
  print('   mv test_disabled test');

  print('\n📱 Manual Integration:');
  print('Follow the MANUAL_INTEGRATION_GUIDE.md for step-by-step integration');
  print('All implementations are complete and ready to use!');
}
