Analyzing billionaires_social...                                

   info • 'broadcastTower' is deprecated and shouldn't be used. Use "towerBroadcast" instead • lib/core/main_navigation.dart:120:42 • deprecated_member_use
warning • The left operand can't be null, so the right operand is never executed • lib/core/services/trending_service.dart:80:40 • dead_null_aware_expression
warning • The left operand can't be null, so the right operand is never executed • lib/core/services/trending_service.dart:81:46 • dead_null_aware_expression
   info • 'dialogBackgroundColor' is deprecated and shouldn't be used. Use DialogThemeData.backgroundColor instead. This feature was deprecated after v3.27.0-0.1.pre • lib/core/utils/dialog_utils.dart:14:48 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/auth/screens/preferences_screen.dart:191:36 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/auth/screens/preferences_screen.dart:275:30 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/auth/screens/preferences_screen.dart:276:31 • deprecated_member_use
   info • Don't use 'BuildContext's across async gaps • lib/features/auth/screens/profile_setup_screen.dart:80:22 • use_build_context_synchronously
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/auth/screens/profile_setup_screen.dart:278:36 • deprecated_member_use
   info • Don't use 'BuildContext's across async gaps • lib/features/auth/screens/two_factor_screen.dart:174:28 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/auth/screens/two_factor_screen.dart:271:21 • use_build_context_synchronously
warning • The value of the field '_isVerified' isn't used • lib/features/auth/screens/verification_screen.dart:17:8 • unused_field
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/events/screens/event_detail_screen.dart:71:44 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/events/screens/event_detail_screen.dart:325:39 • deprecated_member_use
warning • The value of the field '_isLoadingBusinessStatus' isn't used • lib/features/events/screens/events_main_screen.dart:22:8 • unused_field
warning • The value of the field '_auth' isn't used • lib/features/events/services/events_service.dart:7:22 • unused_field
warning • The declaration '_generateFilterCacheKey' isn't referenced • lib/features/explore/providers/places_provider.dart:87:8 • unused_element
warning • The value of the local variable 'position' isn't used • lib/features/explore/screens/explore_map_screen.dart:95:13 • unused_local_variable
   info • 'desiredAccuracy' is deprecated and shouldn't be used. use settings parameter with AndroidSettings, AppleSettings, WebSettings, or LocationSettings • lib/features/explore/screens/explore_map_screen.dart:96:9 • deprecated_member_use
   info • Don't invoke 'print' in production code • lib/features/explore/screens/explore_map_screen.dart:102:7 • avoid_print
   info • Don't use 'BuildContext's across async gaps • lib/features/explore/screens/explore_map_screen.dart:1037:30 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/explore/screens/explore_map_screen.dart:1051:28 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/explore/screens/explore_map_screen.dart:1123:30 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/explore/screens/explore_map_screen.dart:1138:28 • use_build_context_synchronously
   info • Unnecessary use of multiple underscores • lib/features/explore/widgets/feature_list_widget.dart:57:33 • unnecessary_underscores
   info • Don't invoke 'print' in production code • lib/features/explore/widgets/place_detail_screen.dart:63:7 • avoid_print
   info • 'Share' is deprecated and shouldn't be used. Use SharePlus instead • lib/features/explore/widgets/place_detail_screen.dart:131:5 • deprecated_member_use
   info • 'share' is deprecated and shouldn't be used. Use SharePlus.instance.share() instead • lib/features/explore/widgets/place_detail_screen.dart:131:11 • deprecated_member_use
   info • 'PostDetailRef' is deprecated and shouldn't be used. Will be removed in 3.0. Use Ref instead • lib/features/feed/providers/post_detail_provider.dart:10:25 • deprecated_member_use_from_same_package
   info • 'CommentsRef' is deprecated and shouldn't be used. Will be removed in 3.0. Use Ref instead • lib/features/feed/providers/post_detail_provider.dart:17:32 • deprecated_member_use_from_same_package
   info • 'cog' is deprecated and shouldn't be used. Use "gear" instead • lib/features/feed/screens/content_filtering_screen.dart:273:30 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/feed/screens/content_filtering_screen.dart:314:36 • deprecated_member_use
   info • Don't invoke 'print' in production code • lib/features/feed/services/feed_service.dart:956:7 • avoid_print
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/feed/widgets/media_tag_overlay.dart:26:37 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/feed/widgets/media_tag_overlay.dart:29:39 • deprecated_member_use
warning • The declaration '_editPost' isn't referenced • lib/features/feed/widgets/post_actions_sheet.dart:39:16 • unused_element
warning • The declaration '_deletePost' isn't referenced • lib/features/feed/widgets/post_actions_sheet.dart:53:16 • unused_element
warning • The declaration '_archivePost' isn't referenced • lib/features/feed/widgets/post_actions_sheet.dart:103:16 • unused_element
warning • The declaration '_sharePost' isn't referenced • lib/features/feed/widgets/post_actions_sheet.dart:129:8 • unused_element
   info • 'Share' is deprecated and shouldn't be used. Use SharePlus instead • lib/features/feed/widgets/post_actions_sheet.dart:131:5 • deprecated_member_use
   info • 'share' is deprecated and shouldn't be used. Use SharePlus.instance.share() instead • lib/features/feed/widgets/post_actions_sheet.dart:131:11 • deprecated_member_use
warning • The value of the field '_scaleAnimation' isn't used • lib/features/feed/widgets/post_card.dart:40:26 • unused_field
warning • The value of the field '_captionAnimation' isn't used • lib/features/feed/widgets/post_card.dart:42:26 • unused_field
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/feed/widgets/post_card.dart:260:43 • deprecated_member_use
   info • Don't use 'BuildContext's across async gaps • lib/features/feed/widgets/post_card.dart:617:28 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/feed/widgets/post_card.dart:642:28 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/feed/widgets/post_card.dart:662:9 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/feed/widgets/post_card.dart:762:9 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/feed/widgets/post_card.dart:766:9 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/feed/widgets/share_post_sheet.dart:103:24 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/feed/widgets/share_post_sheet.dart:108:30 • use_build_context_synchronously
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/marketplace/screens/cart_screen.dart:422:33 • deprecated_member_use
warning • The value of the local variable 'order' isn't used • lib/features/marketplace/screens/checkout_screen.dart:66:13 • unused_local_variable
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/marketplace/screens/checkout_screen.dart:377:33 • deprecated_member_use
warning • The value of the field '_userProfile' isn't used • lib/features/marketplace/screens/marketplace_main_screen.dart:31:17 • unused_field
warning • The declaration '_checkBusinessAccount' isn't referenced • lib/features/marketplace/screens/marketplace_main_screen.dart:87:16 • unused_element
   info • Don't invoke 'print' in production code • lib/features/marketplace/screens/marketplace_main_screen.dart:124:7 • avoid_print
   info • Unnecessary use of multiple underscores • lib/features/marketplace/screens/order_management_screen.dart:115:45 • unnecessary_underscores
   info • 'checkCircle' is deprecated and shouldn't be used. Use "circleCheck" instead • lib/features/marketplace/screens/order_management_screen.dart:384:33 • deprecated_member_use
   info • 'timesCircle' is deprecated and shouldn't be used. Use "circleXmark" instead • lib/features/marketplace/screens/order_management_screen.dart:386:33 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/marketplace/screens/orders_screen.dart:199:61 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/marketplace/screens/orders_screen.dart:259:58 • deprecated_member_use
warning • The value of the field '_minPrice' isn't used • lib/features/marketplace/screens/search_dialog.dart:18:11 • unused_field
warning • The value of the field '_maxPrice' isn't used • lib/features/marketplace/screens/search_dialog.dart:19:11 • unused_field
  error • The named parameter 'content' is required, but there's no corresponding argument • lib/features/marketplace/screens/seller_dashboard_screen.dart:501:5 • missing_required_argument
  error • The named parameter 'title' is required, but there's no corresponding argument • lib/features/marketplace/screens/seller_dashboard_screen.dart:501:5 • missing_required_argument
  error • The named parameter 'builder' isn't defined • lib/features/marketplace/screens/seller_dashboard_screen.dart:503:7 • undefined_named_parameter
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/marketplace/screens/seller_profile_screen.dart:264:33 • deprecated_member_use
   info • Unnecessary use of multiple underscores • lib/features/marketplace/screens/shopping_cart_screen.dart:85:43 • unnecessary_underscores
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/marketplace/screens/shopping_cart_screen.dart:200:33 • deprecated_member_use
warning • The value of the field '_auth' isn't used • lib/features/marketplace/services/marketplace_service.dart:12:22 • unused_field
   info • Unnecessary use of multiple underscores • lib/features/membership/screens/membership_plans_screen.dart:61:31 • unnecessary_underscores
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/membership/screens/membership_plans_screen.dart:75:46 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/membership/screens/membership_plans_screen.dart:78:31 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/membership/screens/membership_plans_screen.dart:93:37 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/membership/screens/subscription_management_screen.dart:68:48 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/membership/screens/subscription_management_screen.dart:78:39 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/membership/screens/subscription_management_screen.dart:159:41 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/membership/screens/subscription_management_screen.dart:239:38 • deprecated_member_use
   info • 'times' is deprecated and shouldn't be used. Use "xmark" instead • lib/features/membership/screens/subscription_management_screen.dart:311:34 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/membership/screens/subscription_management_screen.dart:335:30 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/membership/screens/subscription_management_screen.dart:336:31 • deprecated_member_use
   info • Don't use 'BuildContext's across async gaps • lib/features/messaging/screens/chat_list_screen.dart:253:42 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/messaging/screens/chat_list_screen.dart:263:42 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/messaging/screens/chat_list_screen.dart:271:40 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/messaging/screens/chat_list_screen.dart:363:36 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/messaging/screens/chat_list_screen.dart:370:36 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/messaging/screens/chat_list_screen.dart:414:36 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/messaging/screens/chat_list_screen.dart:421:36 • use_build_context_synchronously
warning • The value of the local variable 'messagesAsync' isn't used • lib/features/messaging/screens/chat_media_screen.dart:42:11 • unused_local_variable
   info • 'Share' is deprecated and shouldn't be used. Use SharePlus instead • lib/features/messaging/screens/chat_media_screen.dart:512:7 • deprecated_member_use
   info • 'share' is deprecated and shouldn't be used. Use SharePlus.instance.share() instead • lib/features/messaging/screens/chat_media_screen.dart:512:13 • deprecated_member_use
   info • Don't use 'BuildContext's across async gaps • lib/features/messaging/screens/chat_media_screen.dart:568:19 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/messaging/screens/chat_media_screen.dart:571:38 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/messaging/screens/chat_screen.dart:464:28 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/messaging/screens/chat_screen.dart:610:36 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/messaging/screens/chat_screen.dart:687:28 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/messaging/screens/group_chat_screen.dart:172:9 • use_build_context_synchronously
warning • The value of the field '_slideAnimation' isn't used • lib/features/messaging/screens/video_call_screen.dart:38:26 • unused_field
   info • Don't invoke 'print' in production code • lib/features/messaging/services/chat_settings_service.dart:201:7 • avoid_print
   info • Don't invoke 'print' in production code • lib/features/messaging/services/chat_settings_service.dart:263:9 • avoid_print
warning • The value of the local variable 'notificationService' isn't used • lib/features/notifications/providers/notification_provider.dart:25:11 • unused_local_variable
   info • 'UserProfileRef' is deprecated and shouldn't be used. Will be removed in 3.0. Use Ref instead • lib/features/profile/providers/user_profile_provider.dart:10:34 • deprecated_member_use_from_same_package
   info • 'signOutAlt' is deprecated and shouldn't be used. Use "rightFromBracket" instead • lib/features/profile/screens/account_switcher_screen.dart:253:49 • deprecated_member_use
   info • Don't use 'BuildContext's across async gaps • lib/features/profile/screens/edit_profile_screen.dart:94:9 • use_build_context_synchronously
   info • 'stream' is deprecated and shouldn't be used. .stream will be removed in 3.0.0. As a replacement, either listen to the provider itself (AsyncValue) or .future • lib/features/profile/screens/main_profile_screen.dart:92:71 • deprecated_member_use
   info • 'stream' is deprecated and shouldn't be used. .stream will be removed in 3.0.0. As a replacement, either listen to the provider itself (AsyncValue) or .future • lib/features/profile/screens/main_profile_screen.dart:150:69 • deprecated_member_use
warning • The declaration '_applyFilter' isn't referenced • lib/features/profile/services/followers_service.dart:686:23 • unused_element
warning • The declaration '_applyFollowingFilter' isn't referenced • lib/features/profile/services/followers_service.dart:730:24 • unused_element
   info • 'Share' is deprecated and shouldn't be used. Use SharePlus instead • lib/features/profile/widgets/profile_header.dart:406:13 • deprecated_member_use
   info • 'share' is deprecated and shouldn't be used. Use SharePlus.instance.share() instead • lib/features/profile/widgets/profile_header.dart:406:19 • deprecated_member_use
warning • The '!' will have no effect because the receiver can't be null • lib/features/profile/widgets/profile_post_grid.dart:146:29 • unnecessary_non_null_assertion
warning • The '!' will have no effect because the receiver can't be null • lib/features/profile/widgets/profile_post_grid.dart:146:46 • unnecessary_non_null_assertion
   info • Don't use 'BuildContext's across async gaps • lib/features/profile/widgets/profile_post_grid.dart:410:30 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/profile/widgets/profile_post_grid.dart:420:30 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/profile/widgets/profile_post_grid.dart:426:9 • use_build_context_synchronously
warning • The declaration '_openPostDetail' isn't referenced • lib/features/profile/widgets/profile_post_grid.dart:431:8 • unused_element
   info • Don't use 'BuildContext's across async gaps • lib/features/profile/widgets/profile_post_grid.dart:463:21 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/profile/widgets/profile_post_grid.dart:464:28 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/profile/widgets/profile_post_grid.dart:470:9 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/profile/widgets/profile_post_grid.dart:478:21 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/profile/widgets/profile_post_grid.dart:479:28 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/profile/widgets/profile_post_grid.dart:484:28 • use_build_context_synchronously
warning • The declaration '_showBoostPostDialog' isn't referenced • lib/features/profile/widgets/profile_post_grid.dart:490:8 • unused_element
warning • The declaration '_showInsights' isn't referenced • lib/features/profile/widgets/profile_post_grid.dart:513:8 • unused_element
   info • Don't use 'BuildContext's across async gaps • lib/features/profile/widgets/profile_post_grid.dart:1254:28 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/profile/widgets/profile_post_grid.dart:1261:28 • use_build_context_synchronously
   info • 'Share' is deprecated and shouldn't be used. Use SharePlus instead • lib/features/profile/widgets/profile_post_grid.dart:1276:13 • deprecated_member_use
   info • 'share' is deprecated and shouldn't be used. Use SharePlus.instance.share() instead • lib/features/profile/widgets/profile_post_grid.dart:1276:19 • deprecated_member_use
   info • Don't use 'BuildContext's across async gaps • lib/features/profile/widgets/profile_post_grid.dart:1282:28 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/profile/widgets/profile_post_grid.dart:1317:30 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/profile/widgets/profile_post_grid.dart:1324:30 • use_build_context_synchronously
  error • Expected to find ',' • lib/features/reels/screens/reels_screen.dart:20:30 • expected_token
  error • Too many positional arguments: 0 expected, but 6 found • lib/features/reels/screens/reels_screen.dart:20:30 • extra_positional_arguments
  error • Undefined name 's' • lib/features/reels/screens/reels_screen.dart:20:30 • undefined_identifier
  error • Expected to find ',' • lib/features/reels/screens/reels_screen.dart:20:32 • expected_token
  error • Undefined name 'fastest' • lib/features/reels/screens/reels_screen.dart:20:32 • undefined_identifier
  error • Expected to find ',' • lib/features/reels/screens/reels_screen.dart:20:40 • expected_token
  error • Undefined name 'cars' • lib/features/reels/screens/reels_screen.dart:20:40 • undefined_identifier
  error • 'in' can't be used as an identifier because it's a keyword • lib/features/reels/screens/reels_screen.dart:20:45 • expected_identifier_but_got_keyword
  error • Expected to find ',' • lib/features/reels/screens/reels_screen.dart:20:45 • expected_token
  error • Undefined name 'in' • lib/features/reels/screens/reels_screen.dart:20:45 • undefined_identifier
  error • Expected to find ',' • lib/features/reels/screens/reels_screen.dart:20:48 • expected_token
  error • Undefined name 'action' • lib/features/reels/screens/reels_screen.dart:20:48 • undefined_identifier
  error • Expected to find ',' • lib/features/reels/screens/reels_screen.dart:20:54 • expected_token
  error • Unterminated string literal • lib/features/reels/screens/reels_screen.dart:20:55 • unterminated_string_literal
  error • Expected to find ',' • lib/features/reels/screens/reels_screen.dart:21:9 • expected_token
   info • 'value' is deprecated and shouldn't be used. Use component accessors like .r or .g, or toARGB32 for an explicit conversion • lib/features/stories/models/shared/story_shared_models.dart:54:22 • deprecated_member_use
   info • 'value' is deprecated and shouldn't be used. Use component accessors like .r or .g, or toARGB32 for an explicit conversion • lib/features/stories/models/shared/story_shared_models.dart:57:42 • deprecated_member_use
   info • 'value' is deprecated and shouldn't be used. Use component accessors like .r or .g, or toARGB32 for an explicit conversion • lib/features/stories/models/shared/story_shared_models.dart:149:22 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_analytics_dashboard_screen.dart:274:60 • deprecated_member_use
warning • The value of the local variable 'viewerDemographics' isn't used • lib/features/stories/screens/story_analytics_dashboard_screen.dart:339:15 • unused_local_variable
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_analytics_dashboard_screen.dart:401:58 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_analytics_dashboard_screen.dart:447:54 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_analytics_dashboard_screen.dart:455:56 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_analytics_dashboard_screen.dart:478:54 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_analytics_dashboard_screen.dart:502:22 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_analytics_dashboard_screen.dart:504:41 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_analytics_dashboard_screen.dart:522:43 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_analytics_dashboard_screen.dart:560:64 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_analytics_dashboard_screen.dart:576:58 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_analytics_dashboard_screen.dart:593:62 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_analytics_dashboard_screen.dart:626:48 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_analytics_dashboard_screen.dart:657:56 • deprecated_member_use
  error • Expected to find ',' • lib/features/stories/screens/story_archive_screen.dart:13:43 • expected_token
  error • Too many positional arguments: 0 expected, but 4 found • lib/features/stories/screens/story_archive_screen.dart:13:43 • extra_positional_arguments
  error • Undefined name 's' • lib/features/stories/screens/story_archive_screen.dart:13:43 • undefined_identifier
  error • Expected to find ',' • lib/features/stories/screens/story_archive_screen.dart:13:45 • expected_token
  error • Undefined name 'elite' • lib/features/stories/screens/story_archive_screen.dart:13:45 • undefined_identifier
  error • Expected to find ',' • lib/features/stories/screens/story_archive_screen.dart:13:51 • expected_token
  error • Undefined name 'event' • lib/features/stories/screens/story_archive_screen.dart:13:51 • undefined_identifier
  error • Expected to find ',' • lib/features/stories/screens/story_archive_screen.dart:13:56 • expected_token
  error • Unterminated string literal • lib/features/stories/screens/story_archive_screen.dart:13:57 • unterminated_string_literal
  error • Expected to find ',' • lib/features/stories/screens/story_archive_screen.dart:14:9 • expected_token
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_creation_screen.dart:481:33 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_creation_screen.dart:529:37 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_creation_screen.dart:575:35 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_creation_screen.dart:589:35 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_creation_screen.dart:620:35 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_creation_screen.dart:634:35 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_creation_screen.dart:673:37 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_creation_screen.dart:727:53 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_creation_screen.dart:752:37 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_highlights_screen.dart:129:54 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_highlights_screen.dart:189:44 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_highlights_screen.dart:198:48 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_highlights_screen.dart:227:40 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_highlights_screen.dart:240:40 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_highlights_screen.dart:247:42 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_highlights_screen.dart:256:44 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_highlights_screen.dart:350:54 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_highlights_screen.dart:403:44 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_highlights_screen.dart:412:48 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_highlights_screen.dart:419:40 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_highlights_screen.dart:439:68 • deprecated_member_use
   info • Don't use 'BuildContext's across async gaps • lib/features/stories/screens/story_highlights_screen.dart:527:28 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/stories/screens/story_highlights_screen.dart:532:9 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/stories/screens/story_highlights_screen.dart:589:28 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/stories/screens/story_highlights_screen.dart:609:28 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/stories/screens/story_highlights_screen.dart:614:9 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/stories/screens/story_highlights_screen.dart:648:30 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/stories/screens/story_highlights_screen.dart:653:11 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/stories/screens/story_highlights_screen.dart:666:9 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/stories/screens/story_highlights_screen.dart:670:9 • use_build_context_synchronously
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_viewer_screen.dart:173:39 • deprecated_member_use
   info • Don't use 'BuildContext's across async gaps • lib/features/stories/screens/story_viewer_screen.dart:515:7 • use_build_context_synchronously
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/story_viewer_screen.dart:786:47 • deprecated_member_use
warning • The value of the field '_storage' isn't used • lib/features/stories/screens/text_story_creation_screen.dart:21:25 • unused_field
   info • 'value' is deprecated and shouldn't be used. Use component accessors like .r or .g, or toARGB32 for an explicit conversion • lib/features/stories/screens/text_story_creation_screen.dart:339:45 • deprecated_member_use
   info • 'value' is deprecated and shouldn't be used. Use component accessors like .r or .g, or toARGB32 for an explicit conversion • lib/features/stories/screens/text_story_creation_screen.dart:340:33 • deprecated_member_use
warning • The value of the field '_textAnimation' isn't used • lib/features/stories/screens/unified_story_editor_screen.dart:23:32 • unused_field
   info • Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check • lib/features/stories/screens/unified_story_editor_screen.dart:80:36 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check • lib/features/stories/screens/unified_story_editor_screen.dart:82:44 • use_build_context_synchronously
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/screens/unified_story_editor_screen.dart:156:31 • deprecated_member_use
   info • Don't use 'BuildContext's across async gaps • lib/features/stories/services/story_delete_service.dart:34:28 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/stories/services/story_delete_service.dart:41:9 • use_build_context_synchronously
warning • The value of the field '_auth' isn't used • lib/features/stories/services/story_notification_service.dart:14:22 • unused_field
warning • Dead code • lib/features/stories/services/story_service.dart:176:17 • dead_code
warning • The declaration '_getMockStoryReels' isn't referenced • lib/features/stories/services/story_service.dart:887:19 • unused_element
warning • Unused import: 'dart:typed_data' • lib/features/stories/services/story_sharing_service.dart:8:8 • unused_import
   info • 'Share' is deprecated and shouldn't be used. Use SharePlus instead • lib/features/stories/services/story_sharing_service.dart:26:13 • deprecated_member_use
   info • 'shareXFiles' is deprecated and shouldn't be used. Use SharePlus.instance.share() instead • lib/features/stories/services/story_sharing_service.dart:26:19 • deprecated_member_use
   info • 'Share' is deprecated and shouldn't be used. Use SharePlus instead • lib/features/stories/services/story_sharing_service.dart:47:13 • deprecated_member_use
   info • 'share' is deprecated and shouldn't be used. Use SharePlus.instance.share() instead • lib/features/stories/services/story_sharing_service.dart:47:19 • deprecated_member_use
warning • The value of the local variable 'caption' isn't used • lib/features/stories/services/story_sharing_service.dart:245:11 • unused_local_variable
warning • The value of the field '_imagePicker' isn't used • lib/features/stories/services/unified_story_service.dart:20:21 • unused_field
   info • 'value' is deprecated and shouldn't be used. Use component accessors like .r or .g, or toARGB32 for an explicit conversion • lib/features/stories/services/unified_story_service.dart:117:31 • deprecated_member_use
   info • 'value' is deprecated and shouldn't be used. Use component accessors like .r or .g, or toARGB32 for an explicit conversion • lib/features/stories/services/unified_story_service.dart:122:43 • deprecated_member_use
   info • 'value' is deprecated and shouldn't be used. Use component accessors like .r or .g, or toARGB32 for an explicit conversion • lib/features/stories/services/unified_story_service.dart:127:37 • deprecated_member_use
warning • The value of the local variable 'theme' isn't used • lib/features/stories/widgets/collaborative_story_widget.dart:48:11 • unused_local_variable
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/collaborative_story_widget.dart:62:31 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/collaborative_story_widget.dart:64:51 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/collaborative_story_widget.dart:106:44 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/collaborative_story_widget.dart:141:55 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/collaborative_story_widget.dart:202:40 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/collaborative_story_widget.dart:203:39 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/collaborative_story_widget.dart:276:42 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/collaborative_story_widget.dart:279:44 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/collaborative_story_widget.dart:319:38 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/collaborative_story_widget.dart:322:40 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/enhanced_stories_carousel.dart:55:55 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/enhanced_stories_carousel.dart:143:45 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/enhanced_stories_carousel.dart:151:47 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/enhanced_stories_carousel.dart:160:47 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/enhanced_stories_carousel.dart:236:64 • deprecated_member_use
   info • Unnecessary use of multiple underscores • lib/features/stories/widgets/enhanced_stories_carousel.dart:304:20 • unnecessary_underscores
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/enhanced_stories_carousel.dart:350:62 • deprecated_member_use
   info • Don't use 'BuildContext's across async gaps • lib/features/stories/widgets/enhanced_stories_carousel.dart:462:29 • use_build_context_synchronously
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/enhanced_stories_carousel.dart:548:48 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/enhanced_stories_carousel.dart:555:48 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/enhanced_stories_carousel.dart:564:46 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/enhanced_stories_carousel.dart:593:50 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/enhanced_stories_carousel.dart:595:52 • deprecated_member_use
   info • Don't use 'BuildContext's across async gaps • lib/features/stories/widgets/location_selector.dart:61:28 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/stories/widgets/location_selector.dart:89:30 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/stories/widgets/location_selector.dart:103:30 • use_build_context_synchronously
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/location_selector.dart:163:42 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/location_selector.dart:283:45 • deprecated_member_use
warning • The value of the field '_isSearching' isn't used • lib/features/stories/widgets/music_selector.dart:25:14 • unused_field
   info • Don't use 'BuildContext's across async gaps • lib/features/stories/widgets/music_selector.dart:116:30 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/stories/widgets/music_selector.dart:130:30 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/stories/widgets/music_selector.dart:147:30 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/stories/widgets/music_selector.dart:161:30 • use_build_context_synchronously
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/music_selector.dart:219:42 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/music_selector.dart:277:39 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/music_selector.dart:322:53 • deprecated_member_use
   info • Unnecessary use of multiple underscores • lib/features/stories/widgets/stories_carousel.dart:157:20 • unnecessary_underscores
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/stories_carousel.dart:228:61 • deprecated_member_use
   info • Don't use 'BuildContext's across async gaps • lib/features/stories/widgets/stories_carousel.dart:265:29 • use_build_context_synchronously
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_analytics_display.dart:168:37 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_analytics_display.dart:220:37 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_analytics_display.dart:246:22 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_analytics_display.dart:248:41 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_analytics_display.dart:265:43 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_analytics_display.dart:325:33 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_aware_profile_image.dart:111:46 • deprecated_member_use
   info • Don't use 'BuildContext's across async gaps • lib/features/stories/widgets/story_aware_profile_image.dart:146:27 • use_build_context_synchronously
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_aware_profile_image.dart:224:59 • deprecated_member_use
warning • The value of the local variable 'theme' isn't used • lib/features/stories/widgets/story_countdown_widget.dart:97:11 • unused_local_variable
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_countdown_widget.dart:106:31 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_countdown_widget.dart:108:52 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_countdown_widget.dart:154:45 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_countdown_widget.dart:157:47 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_countdown_widget.dart:192:53 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_countdown_widget.dart:217:42 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_countdown_widget.dart:243:47 • deprecated_member_use
warning • The value of the local variable 'minutes' isn't used • lib/features/stories/widgets/story_countdown_widget.dart:271:11 • unused_local_variable
   info • Don't use 'BuildContext's across async gaps • lib/features/stories/widgets/story_editor_integration_example.dart:151:28 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/stories/widgets/story_editor_integration_example.dart:158:28 • use_build_context_synchronously
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_editor_integration_example.dart:227:47 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_editor_integration_example.dart:262:47 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_editor_integration_example.dart:297:47 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_editor_integration_example.dart:418:31 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_editor_integration_example.dart:436:42 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_expiration_timer.dart:108:29 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_expiration_timer.dart:137:39 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_expiration_timer.dart:277:39 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_expiration_timer.dart:296:43 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_expiration_timer.dart:318:38 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_expiration_timer.dart:320:57 • deprecated_member_use
warning • The value of the local variable 'currentUser' isn't used • lib/features/stories/widgets/story_poll_widget.dart:70:11 • unused_local_variable
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_poll_widget.dart:77:31 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_poll_widget.dart:114:42 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_poll_widget.dart:119:44 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_poll_widget.dart:149:46 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_poll_widget.dart:164:60 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_poll_widget.dart:176:58 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_poll_widget.dart:177:58 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_poll_widget.dart:227:37 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_question_widget.dart:97:31 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_question_widget.dart:121:43 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_question_widget.dart:153:39 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_question_widget.dart:156:41 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_question_widget.dart:222:45 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_question_widget.dart:228:45 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_question_widget.dart:274:40 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_question_widget.dart:277:42 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_question_widget.dart:309:42 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_question_widget.dart:345:40 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_question_widget.dart:380:40 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_question_widget.dart:382:38 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_question_widget.dart:383:39 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_reply_widget.dart:48:29 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_reply_widget.dart:50:47 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_reply_widget.dart:59:53 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_reply_widget.dart:64:39 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_reply_widget.dart:77:41 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_reply_widget.dart:80:43 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_reply_widget.dart:91:45 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_reply_widget.dart:133:40 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_reply_widget.dart:141:40 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_reply_widget.dart:190:39 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_reply_widget.dart:193:41 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_reply_widget.dart:283:29 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_reply_widget.dart:285:48 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_reply_widget.dart:294:37 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_reply_widget.dart:301:39 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_reply_widget.dart:319:39 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_reply_widget.dart:349:42 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_reply_widget.dart:354:41 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_reply_widget.dart:379:45 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/story_reply_widget.dart:389:41 • deprecated_member_use
warning • The value of the field '_profileService' isn't used • lib/features/stories/widgets/user_tag_selector.dart:26:24 • unused_field
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/stories/widgets/user_tag_selector.dart:294:50 • deprecated_member_use
   info • Don't invoke 'print' in production code • lib/main.dart:18:32 • avoid_print
   info • Don't invoke 'print' in production code • lib/main.dart:21:3 • avoid_print
   info • Don't invoke 'print' in production code • lib/main.dart:23:3 • avoid_print
   info • Don't invoke 'print' in production code • lib/main.dart:25:3 • avoid_print
   info • Don't invoke 'print' in production code • lib/main.dart:50:5 • avoid_print
   info • Don't invoke 'print' in production code • lib/main.dart:53:9 • avoid_print
   info • Don't invoke 'print' in production code • lib/main.dart:73:9 • avoid_print
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/main.dart:97:37 • deprecated_member_use

