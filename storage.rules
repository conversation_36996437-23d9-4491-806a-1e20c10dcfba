rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isAdmin() {
      return isAuthenticated() && 
        firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.isAdmin == true;
    }
    
    function isValidImageType() {
      return request.resource.contentType.matches('image/.*');
    }
    
    function isValidVideoType() {
      return request.resource.contentType.matches('video/.*');
    }
    
    function isValidFileSize(maxSizeMB) {
      return request.resource.size <= maxSizeMB * 1024 * 1024;
    }

    // Profile images - Only owner can upload, authenticated users can read
    match /profile_images/{userId}/{fileName} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() &&
        isOwner(userId) &&
        isValidImageType() &&
        isValidFileSize(5); // 5MB limit
    }
    
    // Post images - Authenticated users can upload, read access based on post privacy
    match /post_images/{postId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && 
        isValidImageType() && 
        isValidFileSize(10); // 10MB limit for posts
    }
    
    // User uploads - Authenticated users can upload to their own folder
    match /user_uploads/{userId}/{allPaths=**} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && 
        isOwner(userId) && 
        (isValidImageType() || isValidVideoType()) && 
        isValidFileSize(25); // 25MB limit for user uploads
    }
    
    // General images - Authenticated users can upload, read access controlled
    match /images/{fileName} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && 
        (isValidImageType() || isValidVideoType()) && 
        isValidFileSize(15); // 15MB limit for general uploads
    }
    
    // Story media - Enhanced privacy controls
    match /stories/{userId}/{fileName} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && 
        (isValidImageType() || isValidVideoType()) && 
        isValidFileSize(20); // 20MB limit for stories
    }
    
    // Legacy story path for backward compatibility
    match /stories/{storyId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && 
        (isValidImageType() || isValidVideoType()) && 
        isValidFileSize(20); // 20MB limit for stories
    }
    
    // Reel media - For video content
    match /reels/{reelId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && 
        isValidVideoType() && 
        isValidFileSize(50); // 50MB limit for reels
    }
    
    // Marketplace images - Enhanced with verification
    match /marketplace/{itemId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && 
        isValidImageType() && 
        isValidFileSize(8); // 8MB limit for marketplace
    }
    
    // Event images - Enhanced with verification
    match /events/{eventId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && 
        isValidImageType() && 
        isValidFileSize(12); // 12MB limit for events
    }
    
    // Banner images - Only owner can upload
    match /banner_images/{userId}/{fileName} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() &&
        isOwner(userId) &&
        isValidImageType() &&
        isValidFileSize(8); // 8MB limit
    }
    
    // Chat media - Private access
    match /chat_media/{chatId}/{messageId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && 
        (isValidImageType() || isValidVideoType()) && 
        isValidFileSize(25); // 25MB limit for chat media
    }
    
    // Admin uploads - Admin only
    match /admin/{fileName} {
      allow read, write: if isAdmin();
    }
    
    // System assets - Public read, admin write
    match /system/{fileName} {
      allow read: if true; // Public read access for system assets
      allow write: if isAdmin();
    }
  }
} 