name: billionaires_social
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  camera: ^0.11.0
  flutter_sound: ^9.2.13
  giphy_picker: ^3.0.2
  file_picker: ^10.2.0
  stick_it: ^1.0.0+2

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  shared_preferences: ^2.2.3
  flutter_riverpod: ^2.4.9
  riverpod_annotation: ^2.3.5
  freezed_annotation: ^3.1.0
  json_annotation: ^4.9.0
  shimmer: ^3.0.0
  cached_network_image: ^3.3.0
  font_awesome_flutter: ^10.6.0
  uuid: ^4.4.0
  get_it: ^8.0.3
  intl: ^0.20.2
  image_picker: ^1.0.4
  faker: ^2.1.0
  video_player: ^2.8.1
  permission_handler: ^12.0.1
  fl_chart: ^1.0.0
  photo_view: ^0.15.0
  
  # Firebase dependencies
  firebase_core: ^3.14.0
  firebase_auth: ^5.6.0
  cloud_firestore: ^5.6.9
  firebase_storage: ^12.4.7
  firebase_messaging: ^15.2.7
  firebase_analytics: ^11.5.0
  firebase_crashlytics: ^4.3.7
  firebase_performance: ^0.10.1+7
  
  # Local notifications
  flutter_local_notifications: ^19.3.0
  riverpod: ^2.5.1
  path_provider: ^2.1.4
  package_info_plus: ^8.0.2
  url_launcher: ^6.2.2
  share_plus: ^11.0.0
  timeago: ^3.6.1
  flutter_svg: ^2.0.10
  local_auth: ^2.1.7
  geolocator: ^14.0.1
  chewie: ^1.7.4
  flutter_secure_storage: ^10.0.0-beta.4
  image: ^4.1.3
  http: ^1.1.0
  audioplayers: ^6.5.0
  flutter_image_compress: ^2.4.0
  record: ^6.0.0
  audio_waveforms: ^1.3.0
  just_audio: ^0.10.4
  crop_your_image: ^2.0.0
  path: ^1.8.3
  crypto: ^3.0.3

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0

  # Testing infrastructure
  mockito: ^5.4.2
  build_runner: ^2.4.11
  freezed: ^3.1.0
  riverpod_generator: ^2.6.4
  json_serializable: ^6.8.0
  integration_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

  assets:
    - assets/