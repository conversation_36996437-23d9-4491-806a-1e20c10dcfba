import 'package:flutter_test/flutter_test.dart';
import 'package:billionaires_social/features/feed/providers/feed_filter_provider.dart';

void main() {
  group('Close Friends Filter Tests', () {
    test('FeedFilterType should include closeFriends', () {
      // Test that the closeFriends enum value exists
      expect(FeedFilterType.values.contains(FeedFilterType.closeFriends), true);
    });

    test('FeedFilterNotifier should handle closeFriends filter', () {
      final notifier = FeedFilterNotifier();
      
      // Test setting close friends filter
      notifier.setFilter(FeedFilterType.closeFriends);
      expect(notifier.state, FeedFilterType.closeFriends);
      
      // Test filter title
      expect(notifier.getFilterTitle(), 'Close Friends');
      
      // Test filter description
      expect(notifier.getFilterDescription(), 'Posts from your close friends');
      
      // Test filter icon
      expect(notifier.getFilterIcon(), '💚');
    });

    test('getFilterOptions should include close friends option', () {
      final notifier = FeedFilterNotifier();
      final options = notifier.getFilterOptions();
      
      // Find close friends option
      final closeFriendsOption = options.firstWhere(
        (option) => option['type'] == FeedFilterType.closeFriends,
        orElse: () => {},
      );
      
      expect(closeFriendsOption.isNotEmpty, true);
      expect(closeFriendsOption['title'], 'Close Friends');
      expect(closeFriendsOption['icon'], '💚');
      expect(closeFriendsOption['description'], 'Your close friends only');
    });

    test('All FeedFilterType values should be handled in switch statements', () {
      final notifier = FeedFilterNotifier();
      
      // Test that all enum values can be processed without throwing
      for (final filterType in FeedFilterType.values) {
        notifier.setFilter(filterType);
        
        // These should not throw exceptions
        expect(() => notifier.getFilterTitle(), returnsNormally);
        expect(() => notifier.getFilterDescription(), returnsNormally);
        expect(() => notifier.getFilterIcon(), returnsNormally);
      }
    });
  });
}
