import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:billionaires_social/main.dart' as app;

/// End-to-end tests for complete user journeys
void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('User Journey E2E Tests', () {
    testWidgets('Complete user registration and first post flow', (tester) async {
      // Launch the app
      app.main();
      await tester.pumpAndSettle();

      // Test user registration flow
      await _testUserRegistration(tester);
      
      // Test profile setup
      await _testProfileSetup(tester);
      
      // Test first post creation
      await _testFirstPostCreation(tester);
      
      // Test feed interaction
      await _testFeedInteraction(tester);
    });

    testWidgets('User authentication and security flow', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Test login flow
      await _testUserLogin(tester);
      
      // Test security features
      await _testSecurityFeatures(tester);
      
      // Test logout
      await _testUserLogout(tester);
    });

    testWidgets('Social interaction flow', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Login as test user
      await _testUserLogin(tester);
      
      // Test following users
      await _testFollowingFlow(tester);
      
      // Test post interactions
      await _testPostInteractions(tester);
      
      // Test messaging
      await _testMessagingFlow(tester);
    });

    testWidgets('Content creation and management flow', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Login as content creator
      await _testUserLogin(tester);
      
      // Test story creation
      await _testStoryCreation(tester);
      
      // Test post creation with media
      await _testMediaPostCreation(tester);
      
      // Test content management
      await _testContentManagement(tester);
    });
  });
}

/// Test user registration flow
Future<void> _testUserRegistration(WidgetTester tester) async {
  // Find and tap register button
  final registerButton = find.text('Create Account');
  expect(registerButton, findsOneWidget);
  await tester.tap(registerButton);
  await tester.pumpAndSettle();

  // Fill registration form
  await tester.enterText(find.byKey(const Key('email_field')), '<EMAIL>');
  await tester.enterText(find.byKey(const Key('username_field')), 'testuser');
  await tester.enterText(find.byKey(const Key('password_field')), 'SecurePass123!');
  await tester.enterText(find.byKey(const Key('confirm_password_field')), 'SecurePass123!');

  // Submit registration
  await tester.tap(find.byKey(const Key('register_submit_button')));
  await tester.pumpAndSettle(const Duration(seconds: 3));

  // Verify registration success
  expect(find.text('Welcome to Billionaires Social'), findsOneWidget);
}

/// Test profile setup flow
Future<void> _testProfileSetup(WidgetTester tester) async {
  // Navigate to profile setup
  expect(find.text('Set Up Your Profile'), findsOneWidget);

  // Fill profile information
  await tester.enterText(find.byKey(const Key('display_name_field')), 'Test Billionaire');
  await tester.enterText(find.byKey(const Key('bio_field')), 'Testing the billionaire lifestyle');

  // Select profile picture (mock)
  await tester.tap(find.byKey(const Key('profile_picture_button')));
  await tester.pumpAndSettle();

  // Complete profile setup
  await tester.tap(find.byKey(const Key('complete_profile_button')));
  await tester.pumpAndSettle(const Duration(seconds: 2));

  // Verify profile setup completion
  expect(find.text('Profile Created Successfully'), findsOneWidget);
}

/// Test first post creation
Future<void> _testFirstPostCreation(WidgetTester tester) async {
  // Navigate to create post
  await tester.tap(find.byKey(const Key('create_post_fab')));
  await tester.pumpAndSettle();

  // Enter post content
  await tester.enterText(
    find.byKey(const Key('post_content_field')),
    'My first post on Billionaires Social! #FirstPost #Billionaire',
  );

  // Add location (optional)
  await tester.tap(find.byKey(const Key('add_location_button')));
  await tester.pumpAndSettle();
  await tester.enterText(find.byKey(const Key('location_field')), 'Silicon Valley');

  // Publish post
  await tester.tap(find.byKey(const Key('publish_post_button')));
  await tester.pumpAndSettle(const Duration(seconds: 3));

  // Verify post creation
  expect(find.text('Post Published Successfully'), findsOneWidget);
}

/// Test feed interaction
Future<void> _testFeedInteraction(WidgetTester tester) async {
  // Navigate to feed
  await tester.tap(find.byKey(const Key('feed_tab')));
  await tester.pumpAndSettle();

  // Verify feed loads
  expect(find.byType(ListView), findsOneWidget);

  // Test scrolling
  await tester.drag(find.byType(ListView), const Offset(0, -300));
  await tester.pumpAndSettle();

  // Test post interaction
  final firstPost = find.byKey(const Key('post_card_0')).first;
  
  // Like post
  await tester.tap(find.descendant(
    of: firstPost,
    matching: find.byKey(const Key('like_button')),
  ));
  await tester.pumpAndSettle();

  // Comment on post
  await tester.tap(find.descendant(
    of: firstPost,
    matching: find.byKey(const Key('comment_button')),
  ));
  await tester.pumpAndSettle();

  await tester.enterText(
    find.byKey(const Key('comment_input')),
    'Great post! 🔥',
  );
  await tester.tap(find.byKey(const Key('send_comment_button')));
  await tester.pumpAndSettle();
}

/// Test user login flow
Future<void> _testUserLogin(WidgetTester tester) async {
  // Find and tap login button
  final loginButton = find.text('Sign In');
  expect(loginButton, findsOneWidget);
  await tester.tap(loginButton);
  await tester.pumpAndSettle();

  // Enter credentials
  await tester.enterText(find.byKey(const Key('login_email_field')), '<EMAIL>');
  await tester.enterText(find.byKey(const Key('login_password_field')), 'SecurePass123!');

  // Submit login
  await tester.tap(find.byKey(const Key('login_submit_button')));
  await tester.pumpAndSettle(const Duration(seconds: 3));

  // Verify login success
  expect(find.byKey(const Key('main_navigation')), findsOneWidget);
}

/// Test security features
Future<void> _testSecurityFeatures(WidgetTester tester) async {
  // Navigate to security settings
  await tester.tap(find.byKey(const Key('profile_tab')));
  await tester.pumpAndSettle();
  
  await tester.tap(find.byKey(const Key('settings_button')));
  await tester.pumpAndSettle();
  
  await tester.tap(find.text('Security'));
  await tester.pumpAndSettle();

  // Test biometric authentication toggle
  final biometricToggle = find.byKey(const Key('biometric_toggle'));
  if (biometricToggle.evaluate().isNotEmpty) {
    await tester.tap(biometricToggle);
    await tester.pumpAndSettle();
  }

  // Test two-factor authentication
  await tester.tap(find.text('Two-Factor Authentication'));
  await tester.pumpAndSettle();
}

/// Test user logout
Future<void> _testUserLogout(WidgetTester tester) async {
  // Navigate to profile
  await tester.tap(find.byKey(const Key('profile_tab')));
  await tester.pumpAndSettle();

  // Open settings
  await tester.tap(find.byKey(const Key('settings_button')));
  await tester.pumpAndSettle();

  // Logout
  await tester.tap(find.text('Logout'));
  await tester.pumpAndSettle();

  // Confirm logout
  await tester.tap(find.text('Confirm'));
  await tester.pumpAndSettle(const Duration(seconds: 2));

  // Verify logout
  expect(find.text('Sign In'), findsOneWidget);
}

/// Test following flow
Future<void> _testFollowingFlow(WidgetTester tester) async {
  // Navigate to explore
  await tester.tap(find.byKey(const Key('explore_tab')));
  await tester.pumpAndSettle();

  // Find and follow a user
  final followButton = find.byKey(const Key('follow_button_0')).first;
  await tester.tap(followButton);
  await tester.pumpAndSettle();

  // Verify follow action
  expect(find.text('Following'), findsOneWidget);
}

/// Test post interactions
Future<void> _testPostInteractions(WidgetTester tester) async {
  // Navigate to feed
  await tester.tap(find.byKey(const Key('feed_tab')));
  await tester.pumpAndSettle();

  // Test various interactions
  final firstPost = find.byKey(const Key('post_card_0')).first;

  // Double tap to like
  await tester.tap(firstPost);
  await tester.tap(firstPost);
  await tester.pumpAndSettle();

  // Share post
  await tester.tap(find.descendant(
    of: firstPost,
    matching: find.byKey(const Key('share_button')),
  ));
  await tester.pumpAndSettle();

  // Bookmark post
  await tester.tap(find.descendant(
    of: firstPost,
    matching: find.byKey(const Key('bookmark_button')),
  ));
  await tester.pumpAndSettle();
}

/// Test messaging flow
Future<void> _testMessagingFlow(WidgetTester tester) async {
  // Navigate to messages
  await tester.tap(find.byKey(const Key('messages_tab')));
  await tester.pumpAndSettle();

  // Start new conversation
  await tester.tap(find.byKey(const Key('new_message_button')));
  await tester.pumpAndSettle();

  // Select recipient
  await tester.tap(find.byKey(const Key('user_search_result_0')));
  await tester.pumpAndSettle();

  // Send message
  await tester.enterText(
    find.byKey(const Key('message_input')),
    'Hello! Great to connect with you.',
  );
  await tester.tap(find.byKey(const Key('send_message_button')));
  await tester.pumpAndSettle();
}

/// Test story creation
Future<void> _testStoryCreation(WidgetTester tester) async {
  // Navigate to camera/story creation
  await tester.tap(find.byKey(const Key('camera_tab')));
  await tester.pumpAndSettle();

  // Create story (mock camera)
  await tester.tap(find.byKey(const Key('capture_button')));
  await tester.pumpAndSettle();

  // Add story text
  await tester.tap(find.byKey(const Key('add_text_button')));
  await tester.pumpAndSettle();
  
  await tester.enterText(
    find.byKey(const Key('story_text_input')),
    'Living the dream! ✨',
  );

  // Publish story
  await tester.tap(find.byKey(const Key('publish_story_button')));
  await tester.pumpAndSettle(const Duration(seconds: 2));
}

/// Test media post creation
Future<void> _testMediaPostCreation(WidgetTester tester) async {
  // Navigate to create post
  await tester.tap(find.byKey(const Key('create_post_fab')));
  await tester.pumpAndSettle();

  // Add media
  await tester.tap(find.byKey(const Key('add_media_button')));
  await tester.pumpAndSettle();

  // Select from gallery (mock)
  await tester.tap(find.byKey(const Key('gallery_option')));
  await tester.pumpAndSettle();

  // Add caption
  await tester.enterText(
    find.byKey(const Key('post_content_field')),
    'Check out this amazing view! #Luxury #Lifestyle',
  );

  // Publish
  await tester.tap(find.byKey(const Key('publish_post_button')));
  await tester.pumpAndSettle(const Duration(seconds: 3));
}

/// Test content management
Future<void> _testContentManagement(WidgetTester tester) async {
  // Navigate to profile
  await tester.tap(find.byKey(const Key('profile_tab')));
  await tester.pumpAndSettle();

  // View own posts
  final firstOwnPost = find.byKey(const Key('own_post_0')).first;
  
  // Open post options
  await tester.tap(find.descendant(
    of: firstOwnPost,
    matching: find.byKey(const Key('post_options_button')),
  ));
  await tester.pumpAndSettle();

  // Test edit post
  await tester.tap(find.text('Edit'));
  await tester.pumpAndSettle();

  // Update caption
  await tester.enterText(
    find.byKey(const Key('edit_post_content')),
    'Updated caption with new hashtags #Updated #Fresh',
  );

  // Save changes
  await tester.tap(find.byKey(const Key('save_post_button')));
  await tester.pumpAndSettle();
}
