import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:billionaires_social/main.dart' as app;

/// Accessibility tests for ensuring app is usable by all users
void main() {
  group('Accessibility Tests', () {
    testWidgets('App has proper semantic labels', (tester) async {
      await tester.pumpWidget(app.MyApp());
      await tester.pumpAndSettle();

      // Test main navigation accessibility
      await _testNavigationAccessibility(tester);

      // Test feed accessibility
      await _testFeedAccessibility(tester);

      // Test form accessibility
      await _testFormAccessibility(tester);

      // Test button accessibility
      await _testButtonAccessibility(tester);
    });

    testWidgets('Screen reader compatibility', (tester) async {
      await tester.pumpWidget(app.MyApp());
      await tester.pumpAndSettle();

      // Enable screen reader simulation
      tester.binding.defaultBinaryMessenger.setMockMethodCallHandler(
        const MethodChannel('flutter/accessibility'),
        (call) async {
          if (call.method == 'announce') {
            return null;
          }
          return null;
        },
      );

      await _testScreenReaderAnnouncements(tester);
    });

    testWidgets('Color contrast and visual accessibility', (tester) async {
      await tester.pumpWidget(app.MyApp());
      await tester.pumpAndSettle();

      await _testColorContrast(tester);
      await _testTextScaling(tester);
      await _testHighContrastMode(tester);
    });

    testWidgets('Keyboard navigation', (tester) async {
      await tester.pumpWidget(app.MyApp());
      await tester.pumpAndSettle();

      await _testKeyboardNavigation(tester);
      await _testFocusManagement(tester);
    });

    testWidgets('Touch target sizes', (tester) async {
      await tester.pumpWidget(app.MyApp());
      await tester.pumpAndSettle();

      await _testTouchTargetSizes(tester);
    });
  });
}

/// Test navigation accessibility
Future<void> _testNavigationAccessibility(WidgetTester tester) async {
  // Find bottom navigation
  final bottomNav = find.byType(BottomNavigationBar);
  expect(bottomNav, findsOneWidget);

  // Check each navigation item has semantic labels
  final navItemFinder = find.descendant(
    of: bottomNav,
    matching: find.byType(BottomNavigationBarItem),
  );

  final navItemCount = navItemFinder.evaluate().length;

  for (int i = 0; i < navItemCount; i++) {
    final semantics = find.descendant(
      of: find.byType(BottomNavigationBar),
      matching: find.bySemanticsLabel(RegExp(r'.+')),
    );
    expect(semantics, findsWidgets);
  }

  // Test navigation announcements
  await tester.tap(find.byIcon(Icons.home));
  await tester.pumpAndSettle();

  // Verify semantic announcements
  final scaffoldSemantics = tester.getSemantics(find.byType(Scaffold));
  expect(scaffoldSemantics, isNotNull);
}

/// Test feed accessibility
Future<void> _testFeedAccessibility(WidgetTester tester) async {
  // Navigate to feed
  await tester.tap(find.byIcon(Icons.home));
  await tester.pumpAndSettle();

  // Find post cards
  final postCards = find.byKey(const ValueKey('post_card'));

  if (postCards.evaluate().isNotEmpty) {
    final firstPost = postCards.first;

    // Check post has semantic information
    final postSemantics = tester.getSemantics(firstPost);
    expect(postSemantics, isNotNull);

    // Test post action buttons accessibility
    final likeButton = find.descendant(
      of: firstPost,
      matching: find.byIcon(Icons.favorite_border),
    );

    if (likeButton.evaluate().isNotEmpty) {
      final likeSemantics = tester.getSemantics(likeButton);
      expect(likeSemantics, isNotNull);
    }

    // Test comment button
    final commentButton = find.descendant(
      of: firstPost,
      matching: find.byIcon(Icons.chat_bubble_outline),
    );

    if (commentButton.evaluate().isNotEmpty) {
      final commentSemantics = tester.getSemantics(commentButton);
      expect(commentSemantics, isNotNull);
    }
  }
}

/// Test form accessibility
Future<void> _testFormAccessibility(WidgetTester tester) async {
  // Navigate to login/register form
  final loginButton = find.text('Sign In');
  if (loginButton.evaluate().isNotEmpty) {
    await tester.tap(loginButton);
    await tester.pumpAndSettle();

    // Test form field accessibility
    final emailField = find.byType(TextFormField).first;
    final emailSemantics = tester.getSemantics(emailField);
    expect(emailSemantics, isNotNull);

    // Test form validation messages
    await tester.tap(find.byType(ElevatedButton));
    await tester.pumpAndSettle();

    // Check for error announcements
    final errorMessages = find
        .byType(Text)
        .evaluate()
        .where(
          (element) =>
              (element.widget as Text).data?.contains('required') == true,
        );

    for (final error in errorMessages) {
      final errorSemantics = tester.getSemantics(find.byWidget(error.widget));
      expect(errorSemantics, isNotNull);
    }
  }
}

/// Test button accessibility
Future<void> _testButtonAccessibility(WidgetTester tester) async {
  // Find all buttons in the current view
  final buttons = [
    find.byType(ElevatedButton),
    find.byType(TextButton),
    find.byType(IconButton),
    find.byType(FloatingActionButton),
  ];

  for (final buttonFinder in buttons) {
    final buttonWidgets = buttonFinder.evaluate();

    for (final button in buttonWidgets) {
      expect(
        tester.getSemantics(find.byWidget(button.widget)),
        matchesSemantics(
          hasEnabledState: true,
          hasTapAction: true,
          isButton: true,
        ),
      );
    }
  }
}

/// Test screen reader announcements
Future<void> _testScreenReaderAnnouncements(WidgetTester tester) async {
  // Test navigation announcements
  await tester.tap(find.byIcon(Icons.search));
  await tester.pumpAndSettle();

  // Verify page change announcement
  final pageTitle = find.text('Explore');
  if (pageTitle.evaluate().isNotEmpty) {
    expect(
      tester.getSemantics(pageTitle),
      matchesSemantics(label: 'Explore', isHeader: true),
    );
  }

  // Test dynamic content announcements
  final refreshButton = find.byIcon(Icons.refresh);
  if (refreshButton.evaluate().isNotEmpty) {
    await tester.tap(refreshButton);
    await tester.pumpAndSettle();

    // Should announce loading state
    final loadingIndicator = find.byType(CircularProgressIndicator);
    if (loadingIndicator.evaluate().isNotEmpty) {
      final loadingSemantics = tester.getSemantics(loadingIndicator);
      expect(loadingSemantics, isNotNull);
    }
  }
}

/// Test color contrast
Future<void> _testColorContrast(WidgetTester tester) async {
  // Get theme colors
  final context = tester.element(find.byType(MaterialApp));
  final theme = Theme.of(context);

  // Test primary color contrast
  final primaryColor = theme.primaryColor;
  final backgroundColor = theme.scaffoldBackgroundColor;

  // Calculate contrast ratio (simplified)
  final contrastRatio = _calculateContrastRatio(primaryColor, backgroundColor);
  expect(contrastRatio, greaterThan(3.0)); // WCAG AA minimum

  // Test text color contrast
  final textColor = theme.textTheme.bodyLarge?.color ?? Colors.black;
  final textContrastRatio = _calculateContrastRatio(textColor, backgroundColor);
  expect(textContrastRatio, greaterThan(4.5)); // WCAG AA for normal text
}

/// Test text scaling
Future<void> _testTextScaling(WidgetTester tester) async {
  // Test with different text scale factors
  final scaleFactors = [1.0, 1.5, 2.0, 3.0];

  for (final scaleFactor in scaleFactors) {
    await tester.pumpWidget(
      MediaQuery(
        data: MediaQueryData(textScaler: TextScaler.linear(scaleFactor)),
        child: app.MyApp(),
      ),
    );
    await tester.pumpAndSettle();

    // Verify text is still readable and doesn't overflow
    final textWidgets = find.byType(Text);
    for (final textWidget in textWidgets.evaluate()) {
      final renderObject = textWidget.renderObject as RenderParagraph?;
      if (renderObject != null) {
        // Check if text fits within bounds
        final size = renderObject.size;
        expect(
          size.width,
          greaterThan(0),
          reason: 'Text has no width at scale factor $scaleFactor',
        );
        expect(
          size.height,
          greaterThan(0),
          reason: 'Text has no height at scale factor $scaleFactor',
        );
      }
    }
  }
}

/// Test high contrast mode
Future<void> _testHighContrastMode(WidgetTester tester) async {
  await tester.pumpWidget(
    MediaQuery(
      data: const MediaQueryData(highContrast: true),
      child: app.MyApp(),
    ),
  );
  await tester.pumpAndSettle();

  // Verify high contrast adaptations
  final context = tester.element(find.byType(MaterialApp));
  final theme = Theme.of(context);

  // In high contrast mode, ensure sufficient contrast
  final primaryColor = theme.primaryColor;
  final backgroundColor = theme.scaffoldBackgroundColor;
  final contrastRatio = _calculateContrastRatio(primaryColor, backgroundColor);
  expect(contrastRatio, greaterThan(7.0)); // WCAG AAA
}

/// Test keyboard navigation
Future<void> _testKeyboardNavigation(WidgetTester tester) async {
  // Test tab navigation
  await tester.sendKeyEvent(LogicalKeyboardKey.tab);
  await tester.pumpAndSettle();

  // Verify focus moves to next focusable element
  final focusedWidget = FocusManager.instance.primaryFocus;
  expect(focusedWidget, isNotNull);

  // Test arrow key navigation in lists
  final listView = find.byType(ListView);
  if (listView.evaluate().isNotEmpty) {
    await tester.sendKeyEvent(LogicalKeyboardKey.arrowDown);
    await tester.pumpAndSettle();

    // Verify focus moved within list
    expect(FocusManager.instance.primaryFocus, isNotNull);
  }
}

/// Test focus management
Future<void> _testFocusManagement(WidgetTester tester) async {
  // Test modal focus trapping
  final dialogButton = find.text('Show Dialog');
  if (dialogButton.evaluate().isNotEmpty) {
    await tester.tap(dialogButton);
    await tester.pumpAndSettle();

    // Focus should be trapped within dialog
    await tester.sendKeyEvent(LogicalKeyboardKey.tab);
    await tester.pumpAndSettle();

    final focusedWidget = FocusManager.instance.primaryFocus;
    expect(
      focusedWidget?.context?.widget.runtimeType.toString(),
      contains('Dialog'),
    );
  }
}

/// Test touch target sizes
Future<void> _testTouchTargetSizes(WidgetTester tester) async {
  // Find all interactive elements
  final interactiveElements = [
    find.byType(ElevatedButton),
    find.byType(TextButton),
    find.byType(IconButton),
    find.byType(GestureDetector),
  ];

  for (final elementFinder in interactiveElements) {
    final elements = elementFinder.evaluate();

    for (final element in elements) {
      final renderBox = element.renderObject as RenderBox?;
      if (renderBox != null) {
        final size = renderBox.size;

        // WCAG recommends minimum 44x44 logical pixels
        expect(
          size.width,
          greaterThanOrEqualTo(44.0),
          reason: 'Touch target width too small: ${size.width}',
        );
        expect(
          size.height,
          greaterThanOrEqualTo(44.0),
          reason: 'Touch target height too small: ${size.height}',
        );
      }
    }
  }
}

/// Calculate contrast ratio between two colors
double _calculateContrastRatio(Color color1, Color color2) {
  final luminance1 = color1.computeLuminance();
  final luminance2 = color2.computeLuminance();

  final lighter = luminance1 > luminance2 ? luminance1 : luminance2;
  final darker = luminance1 > luminance2 ? luminance2 : luminance1;

  return (lighter + 0.05) / (darker + 0.05);
}
