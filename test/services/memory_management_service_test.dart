import 'dart:async';
import 'package:flutter_test/flutter_test.dart';
import 'package:billionaires_social/core/services/memory_management_service.dart';

void main() {
  group('MemoryManagementService Tests', () {
    late MemoryManagementService memoryService;

    setUp(() {
      memoryService = MemoryManagementService();
    });

    tearDown(() {
      memoryService.dispose();
    });

    group('Timer Management Tests', () {
      test('should register and track timers', () {
        final timer = Timer.periodic(const Duration(seconds: 1), (timer) {});
        
        memoryService.registerTimer(timer);
        
        final stats = memoryService.getMemoryStats();
        expect(stats.activeTimers, equals(1));
        
        timer.cancel();
        memoryService.unregisterTimer(timer);
        
        final statsAfter = memoryService.getMemoryStats();
        expect(statsAfter.activeTimers, equals(0));
      });

      test('should cancel all active timers', () {
        final timers = <Timer>[];
        
        // Create multiple timers
        for (int i = 0; i < 5; i++) {
          final timer = Timer.periodic(const Duration(seconds: 1), (timer) {});
          timers.add(timer);
          memoryService.registerTimer(timer);
        }
        
        final statsBefore = memoryService.getMemoryStats();
        expect(statsBefore.activeTimers, equals(5));
        
        // Cancel all timers
        memoryService.cancelAllTimers();
        
        final statsAfter = memoryService.getMemoryStats();
        expect(statsAfter.activeTimers, equals(0));
        
        // Verify timers are actually cancelled
        for (final timer in timers) {
          expect(timer.isActive, isFalse);
        }
      });

      test('should handle timer registration and unregistration correctly', () {
        final timer1 = Timer.periodic(const Duration(seconds: 1), (timer) {});
        final timer2 = Timer.periodic(const Duration(seconds: 1), (timer) {});
        
        memoryService.registerTimer(timer1);
        memoryService.registerTimer(timer2);
        
        expect(memoryService.getMemoryStats().activeTimers, equals(2));
        
        memoryService.unregisterTimer(timer1);
        expect(memoryService.getMemoryStats().activeTimers, equals(1));
        
        timer1.cancel();
        timer2.cancel();
        memoryService.unregisterTimer(timer2);
      });
    });

    group('Stream Subscription Management Tests', () {
      test('should register and track stream subscriptions', () {
        final controller = StreamController<int>();
        final subscription = controller.stream.listen((data) {});
        
        memoryService.registerSubscription(subscription);
        
        final stats = memoryService.getMemoryStats();
        expect(stats.activeSubscriptions, equals(1));
        
        subscription.cancel();
        memoryService.unregisterSubscription(subscription);
        
        final statsAfter = memoryService.getMemoryStats();
        expect(statsAfter.activeSubscriptions, equals(0));
        
        controller.close();
      });

      test('should cancel all active subscriptions', () {
        final controllers = <StreamController<int>>[];
        final subscriptions = <StreamSubscription<int>>[];
        
        // Create multiple subscriptions
        for (int i = 0; i < 3; i++) {
          final controller = StreamController<int>();
          final subscription = controller.stream.listen((data) {});
          
          controllers.add(controller);
          subscriptions.add(subscription);
          memoryService.registerSubscription(subscription);
        }
        
        final statsBefore = memoryService.getMemoryStats();
        expect(statsBefore.activeSubscriptions, equals(3));
        
        // Cancel all subscriptions
        memoryService.cancelAllSubscriptions();
        
        final statsAfter = memoryService.getMemoryStats();
        expect(statsAfter.activeSubscriptions, equals(0));
        
        // Clean up controllers
        for (final controller in controllers) {
          controller.close();
        }
      });
    });

    group('Memory Monitoring Tests', () {
      test('should start and stop monitoring', () async {
        expect(memoryService.getMemoryStats().memoryHistory.isEmpty, isTrue);
        
        await memoryService.startMonitoring();
        
        // Wait a bit for monitoring to collect data
        await Future.delayed(const Duration(milliseconds: 100));
        
        memoryService.stopMonitoring();
        
        // Should have collected some memory data
        final stats = memoryService.getMemoryStats();
        expect(stats.memoryHistory.length, greaterThanOrEqualTo(0));
      });

      test('should not start monitoring twice', () async {
        await memoryService.startMonitoring();
        await memoryService.startMonitoring(); // Should not crash or create duplicate monitoring
        
        memoryService.stopMonitoring();
      });

      test('should get current memory usage', () async {
        final memoryUsage = await memoryService.getCurrentMemoryUsage();
        expect(memoryUsage, greaterThan(0));
        expect(memoryUsage, lessThan(1000)); // Should be reasonable for tests
      });
    });

    group('Memory Statistics Tests', () {
      test('should provide comprehensive memory statistics', () {
        final stats = memoryService.getMemoryStats();
        
        expect(stats.currentMemoryMB, greaterThanOrEqualTo(0));
        expect(stats.maxMemoryMB, equals(200)); // Default max
        expect(stats.warningThresholdMB, equals(150));
        expect(stats.criticalThresholdMB, equals(180));
        expect(stats.memoryUsagePercentage, greaterThanOrEqualTo(0));
        expect(stats.memoryUsagePercentage, lessThanOrEqualTo(100));
        expect(stats.activeTimers, greaterThanOrEqualTo(0));
        expect(stats.activeSubscriptions, greaterThanOrEqualTo(0));
        expect(stats.memoryHistory, isA<List>());
        expect(stats.isNearLimit, isA<bool>());
        expect(stats.isCritical, isA<bool>());
        expect(stats.trend, isA<String>());
      });

      test('should detect when memory is near limit', () {
        // This test would require mocking memory usage to simulate high usage
        // For now, we'll test the logic with the current implementation
        final stats = memoryService.getMemoryStats();
        
        if (stats.currentMemoryMB > 150) {
          expect(stats.isNearLimit, isTrue);
        } else {
          expect(stats.isNearLimit, isFalse);
        }
      });

      test('should detect critical memory usage', () {
        final stats = memoryService.getMemoryStats();
        
        if (stats.currentMemoryMB > 180) {
          expect(stats.isCritical, isTrue);
        } else {
          expect(stats.isCritical, isFalse);
        }
      });

      test('should calculate memory trend', () {
        final validTrends = ['stable', 'increasing', 'decreasing'];
        final stats = memoryService.getMemoryStats();
        
        expect(validTrends.contains(stats.trend), isTrue);
      });
    });

    group('Memory Cleanup Tests', () {
      test('should perform memory cleanup', () async {
        // Create some timers and subscriptions to clean up
        final timer = Timer.periodic(const Duration(seconds: 1), (timer) {});
        final controller = StreamController<int>();
        final subscription = controller.stream.listen((data) {});
        
        memoryService.registerTimer(timer);
        memoryService.registerSubscription(subscription);
        
        // Cancel timer but don't unregister (simulate inactive timer)
        timer.cancel();
        
        await memoryService.performMemoryCleanup();
        
        // Cleanup should have removed inactive timer
        final stats = memoryService.getMemoryStats();
        expect(stats.activeTimers, equals(0));
        
        // Clean up
        subscription.cancel();
        controller.close();
      });

      test('should force garbage collection', () {
        // This test mainly ensures the method doesn't crash
        expect(() => memoryService.forceGarbageCollection(), returnsNormally);
      });
    });

    group('Service Lifecycle Tests', () {
      test('should dispose properly', () {
        final timer = Timer.periodic(const Duration(seconds: 1), (timer) {});
        final controller = StreamController<int>();
        final subscription = controller.stream.listen((data) {});
        
        memoryService.registerTimer(timer);
        memoryService.registerSubscription(subscription);
        
        // Dispose should clean everything up
        memoryService.dispose();
        
        final stats = memoryService.getMemoryStats();
        expect(stats.activeTimers, equals(0));
        expect(stats.activeSubscriptions, equals(0));
        expect(stats.memoryHistory.isEmpty, isTrue);
        
        // Verify timer and subscription are cancelled
        expect(timer.isActive, isFalse);
        
        controller.close();
      });

      test('should handle multiple dispose calls', () {
        memoryService.dispose();
        expect(() => memoryService.dispose(), returnsNormally);
      });
    });

    group('Edge Cases Tests', () {
      test('should handle registering null-like objects gracefully', () {
        // These tests ensure the service doesn't crash with edge cases
        final timer = Timer.periodic(const Duration(seconds: 1), (timer) {});
        
        expect(() => memoryService.registerTimer(timer), returnsNormally);
        expect(() => memoryService.unregisterTimer(timer), returnsNormally);
        
        timer.cancel();
      });

      test('should handle unregistering non-existent timers', () {
        final timer = Timer.periodic(const Duration(seconds: 1), (timer) {});
        
        // Unregister without registering first
        expect(() => memoryService.unregisterTimer(timer), returnsNormally);
        
        timer.cancel();
      });

      test('should handle memory monitoring edge cases', () async {
        // Start monitoring
        await memoryService.startMonitoring();
        
        // Stop immediately
        memoryService.stopMonitoring();
        
        // Start again
        await memoryService.startMonitoring();
        
        // Stop again
        memoryService.stopMonitoring();
        
        expect(() => memoryService.getMemoryStats(), returnsNormally);
      });
    });
  });
}
