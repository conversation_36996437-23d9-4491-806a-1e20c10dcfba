import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:billionaires_social/core/services/cache_service.dart';

void main() {
  group('CacheService Tests', () {
    late CacheService cacheService;

    setUp(() async {
      // Initialize SharedPreferences with empty data for testing
      SharedPreferences.setMockInitialValues({});
      cacheService = CacheService();
      await cacheService.initialize();
    });

    test('setData and getData work correctly', () async {
      const testKey = 'test_key';
      const testData = {'name': '<PERSON>', 'age': 30};

      // Set data
      await cacheService.setData(testKey, testData);

      // Get data
      final retrievedData = await cacheService.getData<Map<String, dynamic>>(
        testKey,
      );

      expect(retrievedData, isNotNull);
      expect(retrievedData!['name'], 'John');
      expect(retrievedData['age'], 30);
    });

    test('getData returns null for non-existent key', () async {
      final result = await cacheService.getData('non_existent_key');
      expect(result, isNull);
    });

    test('removeData removes cached item', () async {
      const testKey = 'test_remove';
      const testData = 'test_value';

      // Set data
      await cacheService.setData(testKey, testData);

      // Verify data exists
      var result = await cacheService.getData(testKey);
      expect(result, testData);

      // Remove data
      await cacheService.removeData(testKey);

      // Verify data is removed
      result = await cacheService.getData(testKey);
      expect(result, isNull);
    });

    test('clearAll removes all cached data', () async {
      // Set multiple items
      await cacheService.setData('key1', 'value1');
      await cacheService.setData('key2', 'value2');
      await cacheService.setData('key3', 'value3');

      // Verify items exist
      expect(await cacheService.getData('key1'), 'value1');
      expect(await cacheService.getData('key2'), 'value2');
      expect(await cacheService.getData('key3'), 'value3');

      // Clear cache
      await cacheService.clearAll();

      // Verify all items are removed
      expect(await cacheService.getData('key1'), isNull);
      expect(await cacheService.getData('key2'), isNull);
      expect(await cacheService.getData('key3'), isNull);
    });

    test('data expires correctly', () async {
      const testKey = 'expiring_key';
      const testData = 'expiring_value';

      // Set data with very short expiry
      await cacheService.setData(
        testKey,
        testData,
        expiry: const Duration(milliseconds: 100),
      );

      // Data should be available immediately
      var result = await cacheService.getData(testKey);
      expect(result, testData);

      // Wait for expiry
      await Future.delayed(const Duration(milliseconds: 150));

      // Data should be expired and return null
      result = await cacheService.getData(testKey);
      expect(result, isNull);
    });

    test('cacheUserProfile and getCachedUserProfile work correctly', () async {
      const userId = 'test_user_123';
      final profileData = {
        'name': 'Test User',
        'email': '<EMAIL>',
        'avatar': 'https://example.com/avatar.jpg',
      };

      // Cache user profile
      await cacheService.cacheUserProfile(userId, profileData);

      // Retrieve cached profile
      final cachedProfile = await cacheService.getCachedUserProfile(userId);

      expect(cachedProfile, isNotNull);
      expect(cachedProfile!['name'], 'Test User');
      expect(cachedProfile['email'], '<EMAIL>');
      expect(cachedProfile['avatar'], 'https://example.com/avatar.jpg');
    });

    test('cacheFeedPosts and getCachedFeedPosts work correctly', () async {
      const userId = 'test_user_123';
      final feedPosts = [
        {
          'id': 'post1',
          'content': 'First post',
          'timestamp': DateTime.now().toIso8601String(),
        },
        {
          'id': 'post2',
          'content': 'Second post',
          'timestamp': DateTime.now().toIso8601String(),
        },
      ];

      // Cache feed posts
      await cacheService.cacheFeedPosts(userId, feedPosts);

      // Retrieve cached posts
      final cachedPosts = await cacheService.getCachedFeedPosts(userId);

      expect(cachedPosts, isNotNull);
      expect(cachedPosts!.length, 2);
      expect(cachedPosts[0]['id'], 'post1');
      expect(cachedPosts[1]['id'], 'post2');
    });

    test('memory management works correctly', () async {
      // Test that cache stats are available
      final stats = cacheService.getCacheStats();

      expect(stats, isNotNull);
      expect(stats.containsKey('memoryItems'), isTrue);
      expect(stats.containsKey('currentSizeBytes'), isTrue);
      expect(stats.containsKey('maxSizeBytes'), isTrue);
      expect(stats.containsKey('utilizationPercent'), isTrue);
    });

    test('optimizeMemoryUsage runs without errors', () async {
      // Add some data to cache
      for (int i = 0; i < 10; i++) {
        await cacheService.setData('key_$i', 'value_$i');
      }

      // Run optimization
      expect(() async {
        await cacheService.optimizeMemoryUsage();
      }, returnsNormally);
    });

    test('cache handles different data types', () async {
      // Test string
      await cacheService.setData('string_key', 'string_value');
      final stringResult = await cacheService.getData<String>('string_key');
      expect(stringResult, 'string_value');

      // Test number
      await cacheService.setData('number_key', 42);
      final numberResult = await cacheService.getData<int>('number_key');
      expect(numberResult, 42);

      // Test boolean
      await cacheService.setData('bool_key', true);
      final boolResult = await cacheService.getData<bool>('bool_key');
      expect(boolResult, isTrue);

      // Test list
      const listData = [1, 2, 3, 4, 5];
      await cacheService.setData('list_key', listData);
      final listResult = await cacheService.getData<List>('list_key');
      expect(listResult, listData);

      // Test map
      const mapData = {'key1': 'value1', 'key2': 'value2'};
      await cacheService.setData('map_key', mapData);
      final mapResult = await cacheService.getData<Map>('map_key');
      expect(mapResult, mapData);
    });

    test('cache handles large data sets', () async {
      // Create a large data set
      final largeData = List.generate(
        1000,
        (index) => {
          'id': index,
          'name': 'Item $index',
          'description':
              'This is item number $index with some additional text to make it larger',
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      // Cache large data
      await cacheService.setData('large_data', largeData);

      // Retrieve large data
      final retrievedData = await cacheService.getData<List>('large_data');

      expect(retrievedData, isNotNull);
      expect(retrievedData!.length, 1000);
      expect(retrievedData[0]['id'], 0);
      expect(retrievedData[999]['id'], 999);
    });

    test('cache handles concurrent operations', () async {
      // Perform multiple concurrent cache operations
      final futures = <Future>[];

      for (int i = 0; i < 20; i++) {
        futures.add(cacheService.setData('concurrent_$i', 'value_$i'));
      }

      // Wait for all operations to complete
      await Future.wait(futures);

      // Verify all data was cached correctly
      for (int i = 0; i < 20; i++) {
        final result = await cacheService.getData('concurrent_$i');
        expect(result, 'value_$i');
      }
    });

    test('cache cleanup works correctly', () async {
      // Add some data
      await cacheService.setData('cleanup_test', 'test_value');

      // Verify data exists
      var result = await cacheService.getData('cleanup_test');
      expect(result, 'test_value');

      // Clear cache
      await cacheService.clearAll();

      // Verify data is cleaned up
      result = await cacheService.getData('cleanup_test');
      expect(result, isNull);
    });
  });

  group('CacheService Error Handling', () {
    test('handles invalid data gracefully', () async {
      final cacheService = CacheService();
      await cacheService.initialize();

      // Test with null key (should handle gracefully)
      expect(() async {
        await cacheService.setData('', 'value');
      }, returnsNormally);

      // Test with very long key
      final longKey = 'a' * 1000;
      expect(() async {
        await cacheService.setData(longKey, 'value');
      }, returnsNormally);
    });

    test('handles storage errors gracefully', () async {
      final cacheService = CacheService();
      await cacheService.initialize();

      // These tests would require more sophisticated mocking to simulate storage errors
      // For now, we ensure the service doesn't crash with edge cases

      expect(() async {
        await cacheService.getData('non_existent_key');
      }, returnsNormally);

      expect(() async {
        await cacheService.removeData('non_existent_key');
      }, returnsNormally);
    });
  });
}
