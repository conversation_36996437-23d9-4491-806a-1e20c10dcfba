import 'package:flutter_test/flutter_test.dart';
import 'package:billionaires_social/core/services/rate_limiting_service.dart';

void main() {
  group('RateLimitingService Tests', () {
    late RateLimitingService rateLimitingService;

    setUp(() {
      rateLimitingService = RateLimitingService();
      // Clear any existing state
      rateLimitingService.clearAllRateLimits();
    });

    tearDown(() {
      rateLimitingService.clearAllRateLimits();
    });

    group('Basic Rate Limiting Tests', () {
      test('should allow requests within rate limit', () {
        final result = rateLimitingService.checkRateLimit('login', 'user1');
        expect(result.allowed, isTrue);
        expect(result.remainingRequests, greaterThan(0));
      });

      test('should block requests exceeding rate limit', () {
        // Make maximum allowed requests for login (5 requests in 15 minutes)
        for (int i = 0; i < 5; i++) {
          final result = rateLimitingService.checkRateLimit('login', 'user1');
          expect(result.allowed, isTrue);
        }

        // Next request should be blocked
        final blockedResult = rateLimitingService.checkRateLimit(
          'login',
          'user1',
        );
        expect(blockedResult.allowed, isFalse);
        expect(blockedResult.remainingRequests, equals(0));
        expect(blockedResult.message, isNotNull);
        expect(blockedResult.message!, contains('Rate limit exceeded'));
      });

      test('should handle different actions independently', () {
        // Exhaust login rate limit
        for (int i = 0; i < 5; i++) {
          rateLimitingService.checkRateLimit('login', 'user1');
        }

        // Login should be blocked
        final loginResult = rateLimitingService.checkRateLimit(
          'login',
          'user1',
        );
        expect(loginResult.allowed, isFalse);

        // But post_creation should still be allowed
        final postResult = rateLimitingService.checkRateLimit(
          'post_creation',
          'user1',
        );
        expect(postResult.allowed, isTrue);
      });

      test('should handle different users independently', () {
        // Exhaust rate limit for user1
        for (int i = 0; i < 5; i++) {
          rateLimitingService.checkRateLimit('login', 'user1');
        }

        // User1 should be blocked
        final user1Result = rateLimitingService.checkRateLimit(
          'login',
          'user1',
        );
        expect(user1Result.allowed, isFalse);

        // User2 should still be allowed
        final user2Result = rateLimitingService.checkRateLimit(
          'login',
          'user2',
        );
        expect(user2Result.allowed, isTrue);
      });
    });

    group('Rate Limit Status Tests', () {
      test('should provide accurate rate limit status', () {
        // Make some requests
        rateLimitingService.checkRateLimit('login', 'user1');
        rateLimitingService.checkRateLimit('login', 'user1');

        final status = rateLimitingService.getRateLimitStatus('login', 'user1');
        expect(status.maxRequests, equals(5)); // Login limit is 5
        expect(status.currentRequests, equals(2));
        expect(status.remainingRequests, equals(3));
        expect(status.resetTime, isA<DateTime>());
      });

      test('should calculate usage percentage correctly', () {
        // Make 3 out of 5 requests
        for (int i = 0; i < 3; i++) {
          rateLimitingService.checkRateLimit('login', 'user1');
        }

        final status = rateLimitingService.getRateLimitStatus('login', 'user1');
        expect(status.usagePercentage, equals(0.6)); // 3/5 = 0.6
        expect(status.isNearLimit, isTrue); // > 0.8 threshold
      });

      test('should detect when near limit', () {
        // Make 4 out of 5 requests (80%)
        for (int i = 0; i < 4; i++) {
          rateLimitingService.checkRateLimit('login', 'user1');
        }

        final status = rateLimitingService.getRateLimitStatus('login', 'user1');
        expect(status.isNearLimit, isTrue);
      });
    });

    group('Rate Limit Configuration Tests', () {
      test('should have correct configurations for different actions', () {
        final configs = rateLimitingService.getRateLimitConfigs();

        expect(configs['login']?.maxRequests, equals(5));
        expect(configs['login']?.windowMinutes, equals(15));

        expect(configs['post_creation']?.maxRequests, equals(10));
        expect(configs['post_creation']?.windowMinutes, equals(60));

        expect(configs['like']?.maxRequests, equals(100));
        expect(configs['like']?.windowMinutes, equals(60));
      });

      test('should check if action has rate limiting', () {
        expect(rateLimitingService.hasRateLimit('login'), isTrue);
        expect(rateLimitingService.hasRateLimit('post_creation'), isTrue);
        expect(
          rateLimitingService.hasRateLimit('non_existent_action'),
          isFalse,
        );
      });

      test('should handle unknown actions gracefully', () {
        final result = rateLimitingService.checkRateLimit(
          'unknown_action',
          'user1',
        );
        expect(result.allowed, isTrue);
        expect(result.remainingRequests, equals(999)); // Default fallback
      });
    });

    group('Rate Limit Management Tests', () {
      test('should clear rate limit for specific action and user', () {
        // Exhaust rate limit
        for (int i = 0; i < 5; i++) {
          rateLimitingService.checkRateLimit('login', 'user1');
        }

        // Should be blocked
        expect(
          rateLimitingService.checkRateLimit('login', 'user1').allowed,
          isFalse,
        );

        // Clear rate limit
        rateLimitingService.clearRateLimit('login', 'user1');

        // Should be allowed again
        expect(
          rateLimitingService.checkRateLimit('login', 'user1').allowed,
          isTrue,
        );
      });

      test('should clear all rate limits', () {
        // Create rate limit data for multiple users and actions
        rateLimitingService.checkRateLimit('login', 'user1');
        rateLimitingService.checkRateLimit('login', 'user2');
        rateLimitingService.checkRateLimit('post_creation', 'user1');

        // Clear all
        rateLimitingService.clearAllRateLimits();

        // All should be reset
        final stats = rateLimitingService.getRateLimitingStats();
        expect(stats['total_tracked_keys'], equals(0));
      });
    });

    group('Statistics Tests', () {
      test('should provide rate limiting statistics', () {
        // Generate some activity
        rateLimitingService.checkRateLimit('login', 'user1');
        rateLimitingService.checkRateLimit('login', 'user2');
        rateLimitingService.checkRateLimit('post_creation', 'user1');
        rateLimitingService.checkRateLimit('like', 'user1');

        final stats = rateLimitingService.getRateLimitingStats();

        expect(stats['total_tracked_keys'], greaterThan(0));
        expect(stats['requests_by_action'], isA<Map>());
        expect(stats['memory_usage_estimate'], greaterThan(0));
      });

      test('should track requests by action', () {
        // Make multiple requests for different actions
        for (int i = 0; i < 3; i++) {
          rateLimitingService.checkRateLimit('login', 'user1');
        }
        for (int i = 0; i < 2; i++) {
          rateLimitingService.checkRateLimit('post_creation', 'user1');
        }

        final stats = rateLimitingService.getRateLimitingStats();
        final requestsByAction = stats['requests_by_action'] as Map;

        expect(requestsByAction['login'], equals(3));
        expect(requestsByAction['post_creation'], equals(2));
      });
    });

    group('Edge Cases Tests', () {
      test('should handle empty identifier', () {
        final result = rateLimitingService.checkRateLimit('login', '');
        expect(result.allowed, isTrue); // Should not crash
      });

      test('should handle null-like identifiers', () {
        final result1 = rateLimitingService.checkRateLimit('login', 'null');
        final result2 = rateLimitingService.checkRateLimit(
          'login',
          'undefined',
        );

        expect(result1.allowed, isTrue);
        expect(result2.allowed, isTrue);
      });

      test('should handle very long identifiers', () {
        final longIdentifier = 'a' * 1000;
        final result = rateLimitingService.checkRateLimit(
          'login',
          longIdentifier,
        );
        expect(result.allowed, isTrue);
      });

      test('should handle special characters in identifiers', () {
        final specialIdentifiers = [
          '<EMAIL>',
          'user-with-dashes',
          'user_with_underscores',
          'user.with.dots',
          'user with spaces',
        ];

        for (final identifier in specialIdentifiers) {
          final result = rateLimitingService.checkRateLimit(
            'login',
            identifier,
          );
          expect(
            result.allowed,
            isTrue,
            reason: 'Should handle identifier: $identifier',
          );
        }
      });
    });

    group('Reset Time Tests', () {
      test('should provide accurate reset time', () {
        final beforeRequest = DateTime.now();
        final result = rateLimitingService.checkRateLimit('login', 'user1');
        final afterRequest = DateTime.now();

        expect(result.resetTime.isAfter(beforeRequest), isTrue);
        expect(result.resetTime.isAfter(afterRequest), isTrue);

        // Reset time should be approximately 15 minutes from now (login window)
        final expectedResetTime = afterRequest.add(const Duration(minutes: 15));
        final timeDifference = result.resetTime
            .difference(expectedResetTime)
            .abs();
        expect(
          timeDifference.inMinutes,
          lessThan(2),
        ); // Allow 2 minute tolerance
      });

      test('should update reset time with new requests', () async {
        final result1 = rateLimitingService.checkRateLimit('login', 'user1');

        // Wait a bit (simulate time passing)
        await Future.delayed(const Duration(milliseconds: 10));

        final result2 = rateLimitingService.checkRateLimit('login', 'user1');

        // Reset time should be updated
        expect(result2.resetTime.isAfter(result1.resetTime), isTrue);
      });
    });
  });
}
