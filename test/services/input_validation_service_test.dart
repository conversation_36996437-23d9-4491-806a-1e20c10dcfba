import 'package:flutter_test/flutter_test.dart';
import 'package:billionaires_social/core/services/input_validation_service.dart';

void main() {
  group('InputValidationService Tests', () {
    late InputValidationService validationService;

    setUp(() {
      validationService = InputValidationService();
    });

    group('Email Validation Tests', () {
      test('should validate correct email addresses', () {
        final validEmails = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ];

        for (final email in validEmails) {
          final result = validationService.validateEmail(email);
          expect(
            result.isValid,
            isTrue,
            reason: 'Email $email should be valid',
          );
        }
      });

      test('should reject invalid email addresses', () {
        final invalidEmails = [
          '',
          'invalid-email',
          '@domain.com',
          'user@',
          '<EMAIL>',
          'user@domain',
          'user@.com',
          'user <EMAIL>', // space
        ];

        for (final email in invalidEmails) {
          final result = validationService.validateEmail(email);
          expect(
            result.isValid,
            isFalse,
            reason: 'Email $email should be invalid',
          );
        }
      });

      test('should reject emails that are too long', () {
        final longEmail = '${'a' * 250}@example.com';
        final result = validationService.validateEmail(longEmail);
        expect(result.isValid, isFalse);
        expect(result.message, contains('too long'));
      });

      test('should detect dangerous patterns in emails', () {
        final dangerousEmails = [
          'test<script>@example.com',
          '<EMAIL><script>alert(1)</script>',
          'javascript:alert(1)@domain.com',
        ];

        for (final email in dangerousEmails) {
          final result = validationService.validateEmail(email);
          expect(
            result.isValid,
            isFalse,
            reason: 'Email $email should be rejected as dangerous',
          );
        }
      });
    });

    group('Password Validation Tests', () {
      test('should validate strong passwords', () {
        final strongPasswords = [
          'StrongPass123!',
          'MySecure@Password1',
          'Complex#Pass2023',
          'Billionaire\$App99',
        ];

        for (final password in strongPasswords) {
          final result = validationService.validatePassword(password);
          expect(
            result.isValid,
            isTrue,
            reason: 'Password $password should be valid',
          );
        }
      });

      test('should reject weak passwords', () {
        final weakPasswords = [
          '',
          'short',
          'nouppercase123!',
          'NOLOWERCASE123!',
          'NoNumbers!',
          'NoSpecialChars123',
          'password', // common password
          '123456', // common password
        ];

        for (final password in weakPasswords) {
          final result = validationService.validatePassword(password);
          expect(
            result.isValid,
            isFalse,
            reason: 'Password $password should be invalid',
          );
        }
      });

      test('should reject passwords that are too long', () {
        final longPassword = 'A' * 130 + 'a1!';
        final result = validationService.validatePassword(longPassword);
        expect(result.isValid, isFalse);
        expect(result.message, contains('too long'));
      });

      test('should provide specific error messages', () {
        expect(
          validationService.validatePassword('').message,
          contains('required'),
        );
        expect(
          validationService.validatePassword('short').message,
          contains('8 characters'),
        );
        expect(
          validationService.validatePassword('nouppercase123!').message,
          contains('uppercase'),
        );
        expect(
          validationService.validatePassword('NOLOWERCASE123!').message,
          contains('lowercase'),
        );
        expect(
          validationService.validatePassword('NoNumbers!').message,
          contains('number'),
        );
        expect(
          validationService.validatePassword('NoSpecialChars123').message,
          contains('special character'),
        );
      });
    });

    group('Username Validation Tests', () {
      test('should validate correct usernames', () {
        final validUsernames = [
          'user123',
          'john.doe',
          'billionaire_user',
          'test.user.123',
          'a1b2c3',
        ];

        for (final username in validUsernames) {
          final result = validationService.validateUsername(username);
          expect(
            result.isValid,
            isTrue,
            reason: 'Username $username should be valid',
          );
        }
      });

      test('should reject invalid usernames', () {
        final invalidUsernames = [
          '',
          'ab', // too short
          'a' * 31, // too long
          'user@invalid', // invalid characters
          'user space', // space
          'user-dash', // dash not allowed
          'admin', // reserved
          'root', // reserved
        ];

        for (final username in invalidUsernames) {
          final result = validationService.validateUsername(username);
          expect(
            result.isValid,
            isFalse,
            reason: 'Username $username should be invalid',
          );
        }
      });

      test('should reject reserved usernames', () {
        final reservedUsernames = [
          'admin',
          'administrator',
          'root',
          'system',
          'billionaire',
          'billionaires',
        ];

        for (final username in reservedUsernames) {
          final result = validationService.validateUsername(username);
          expect(result.isValid, isFalse);
          expect(result.message, contains('not available'));
        }
      });
    });

    group('Text Content Validation Tests', () {
      test('should validate normal text content', () {
        final validTexts = [
          'This is a normal post content.',
          'Hello world! How are you today?',
          'Check out this amazing view 🌅',
          'Just had the best meal at @restaurant',
        ];

        for (final text in validTexts) {
          final result = validationService.validateTextContent(text);
          expect(
            result.isValid,
            isTrue,
            reason: 'Text "$text" should be valid',
          );
        }
      });

      test('should reject dangerous content', () {
        final dangerousTexts = [
          '<script>alert("xss")</script>',
          'javascript:void(0)',
          'onclick="malicious()"',
          '<iframe src="evil.com"></iframe>',
          'eval("malicious code")',
        ];

        for (final text in dangerousTexts) {
          final result = validationService.validateTextContent(text);
          expect(
            result.isValid,
            isFalse,
            reason: 'Text "$text" should be rejected as dangerous',
          );
        }
      });

      test('should reject spam content', () {
        final spamTexts = [
          'BUY NOW! LIMITED TIME OFFER!',
          'FREE MONEY! CLICK HERE!',
          'CONGRATULATIONS! YOU WON THE LOTTERY!',
          'AAAAAAAAAAAAAAAAAAAAAA', // excessive repetition
          'THIS IS ALL CAPS AND LOOKS LIKE SPAM',
        ];

        for (final text in spamTexts) {
          final result = validationService.validateTextContent(text);
          expect(
            result.isValid,
            isFalse,
            reason: 'Text "$text" should be rejected as spam',
          );
        }
      });

      test('should respect max length limits', () {
        final longText = 'a' * 2001; // Default max is 2000
        final result = validationService.validateTextContent(longText);
        expect(result.isValid, isFalse);
        expect(result.message, contains('too long'));
      });

      test('should allow custom max length', () {
        final text = 'a' * 150;
        final result = validationService.validateTextContent(
          text,
          maxLength: 100,
        );
        expect(result.isValid, isFalse);
        expect(result.message, contains('too long'));
      });
    });

    group('URL Validation Tests', () {
      test('should validate correct URLs', () {
        final validUrls = [
          'https://example.com',
          'http://test.org/path/to/page',
          'https://subdomain.domain.com/page?param=value',
          'http://localhost:3000',
        ];

        for (final url in validUrls) {
          final result = validationService.validateUrl(url);
          expect(result.isValid, isTrue, reason: 'URL $url should be valid');
        }
      });

      test('should reject invalid URLs', () {
        final invalidUrls = [
          '',
          'not-a-url',
          'ftp://example.com', // not http/https
          'javascript:alert(1)',
          'data:text/html,<script>alert(1)</script>',
          'example.com', // no protocol
        ];

        for (final url in invalidUrls) {
          final result = validationService.validateUrl(url);
          expect(result.isValid, isFalse, reason: 'URL $url should be invalid');
        }
      });
    });

    group('Phone Number Validation Tests', () {
      test('should validate correct phone numbers', () {
        final validPhones = [
          '+1234567890',
          '+44 20 7946 0958',
          '(*************',
          '************',
          '+86 138 0013 8000',
        ];

        for (final phone in validPhones) {
          final result = validationService.validatePhoneNumber(phone);
          expect(
            result.isValid,
            isTrue,
            reason: 'Phone $phone should be valid',
          );
        }
      });

      test('should reject invalid phone numbers', () {
        final invalidPhones = [
          '',
          '123', // too short
          'not-a-phone',
          '0000000000', // starts with 0
          '+0123456789', // starts with 0 after country code
        ];

        for (final phone in invalidPhones) {
          final result = validationService.validatePhoneNumber(phone);
          expect(
            result.isValid,
            isFalse,
            reason: 'Phone $phone should be invalid',
          );
        }
      });
    });
  });
}
