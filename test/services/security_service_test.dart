import 'package:flutter_test/flutter_test.dart';
import 'package:billionaires_social/core/services/security_service.dart';

void main() {
  group('SecurityService Tests', () {
    late SecurityService securityService;

    setUp(() {
      securityService = SecurityService();
      securityService.clearSecurityData(); // Start with clean state
    });

    tearDown(() {
      securityService.clearSecurityData();
    });

    test('singleton pattern works correctly', () {
      final service1 = SecurityService();
      final service2 = SecurityService();

      expect(identical(service1, service2), isTrue);
    });

    group('Rate Limiting Tests', () {
      test('allows requests under limit', () {
        for (int i = 0; i < 5; i++) {
          final result = securityService.checkRateLimit(
            'test_user',
            maxRequests: 10,
          );
          expect(result, isTrue);
        }
      });

      test('blocks requests over limit', () {
        // Fill up the rate limit
        for (int i = 0; i < 3; i++) {
          securityService.checkRateLimit('test_user', maxRequests: 3);
        }

        // Next request should be blocked
        final result = securityService.checkRateLimit(
          'test_user',
          maxRequests: 3,
        );
        expect(result, isFalse);
      });

      test('rate limit resets after window', () async {
        // Fill up the rate limit
        for (int i = 0; i < 3; i++) {
          securityService.checkRateLimit(
            'test_user',
            maxRequests: 3,
            window: const Duration(milliseconds: 100),
          );
        }

        // Should be blocked
        expect(
          securityService.checkRateLimit(
            'test_user',
            maxRequests: 3,
            window: const Duration(milliseconds: 100),
          ),
          isFalse,
        );

        // Wait for window to expire
        await Future.delayed(const Duration(milliseconds: 150));

        // Should be allowed again
        expect(
          securityService.checkRateLimit(
            'test_user',
            maxRequests: 3,
            window: const Duration(milliseconds: 100),
          ),
          isTrue,
        );
      });

      test('different users have separate rate limits', () {
        // Fill up rate limit for user1
        for (int i = 0; i < 3; i++) {
          securityService.checkRateLimit('user1', maxRequests: 3);
        }

        // user1 should be blocked
        expect(
          securityService.checkRateLimit('user1', maxRequests: 3),
          isFalse,
        );

        // user2 should still be allowed
        expect(securityService.checkRateLimit('user2', maxRequests: 3), isTrue);
      });
    });

    group('Authentication Tracking Tests', () {
      test('tracks failed attempts correctly', () {
        securityService.recordFailedAttempt('test_user', 'password');
        securityService.recordFailedAttempt('test_user', 'password');

        final summary = securityService.getSecuritySummary();
        expect(summary.failedLogins, 2);
      });

      test('locks account after max failed attempts', () {
        // Record max failed attempts
        for (int i = 0; i < 5; i++) {
          securityService.recordFailedAttempt('test_user', 'password');
        }

        expect(securityService.isLockedOut('test_user'), isTrue);
      });

      test('successful auth clears failed attempts', () {
        // Record some failed attempts
        securityService.recordFailedAttempt('test_user', 'password');
        securityService.recordFailedAttempt('test_user', 'password');

        // Successful auth should clear them
        securityService.recordSuccessfulAuth('test_user', 'password');

        expect(securityService.isLockedOut('test_user'), isFalse);
      });

      test('lockout expires after duration', () {
        // This test would require mocking time or waiting
        // For now, we test the basic lockout functionality
        for (int i = 0; i < 5; i++) {
          securityService.recordFailedAttempt('test_user', 'password');
        }

        expect(securityService.isLockedOut('test_user'), isTrue);
      });
    });

    group('Input Validation Tests', () {
      test('validates email addresses correctly', () {
        expect(
          securityService
              .validateInput('<EMAIL>', InputType.email)
              .isValid,
          isTrue,
        );
        expect(
          securityService
              .validateInput('invalid-email', InputType.email)
              .isValid,
          isFalse,
        );
        expect(
          securityService.validateInput('test@', InputType.email).isValid,
          isFalse,
        );
        expect(
          securityService
              .validateInput('@example.com', InputType.email)
              .isValid,
          isFalse,
        );
      });

      test('validates usernames correctly', () {
        expect(
          securityService
              .validateInput('validuser123', InputType.username)
              .isValid,
          isTrue,
        );
        expect(
          securityService.validateInput('ab', InputType.username).isValid,
          isFalse,
        ); // Too short
        expect(
          securityService
              .validateInput(
                'user@name',
                InputType.username,
                allowSpecialChars: false,
              )
              .isValid,
          isFalse,
        );
        expect(
          securityService
              .validateInput(
                'user_name',
                InputType.username,
                allowSpecialChars: true,
              )
              .isValid,
          isTrue,
        );
      });

      test('validates passwords correctly', () {
        expect(
          securityService
              .validateInput('StrongPass123!', InputType.password)
              .isValid,
          isTrue,
        );
        expect(
          securityService.validateInput('weak', InputType.password).isValid,
          isFalse,
        ); // Too short
        expect(
          securityService
              .validateInput('nouppercase123!', InputType.password)
              .isValid,
          isFalse,
        );
        expect(
          securityService
              .validateInput('NOLOWERCASE123!', InputType.password)
              .isValid,
          isFalse,
        );
        expect(
          securityService
              .validateInput('NoNumbers!', InputType.password)
              .isValid,
          isFalse,
        );
        expect(
          securityService
              .validateInput('NoSpecialChars123', InputType.password)
              .isValid,
          isFalse,
        );
      });

      test('validates URLs correctly', () {
        expect(
          securityService
              .validateInput('https://example.com', InputType.url)
              .isValid,
          isTrue,
        );
        expect(
          securityService
              .validateInput('http://example.com', InputType.url)
              .isValid,
          isTrue,
        );
        expect(
          securityService
              .validateInput('ftp://example.com', InputType.url)
              .isValid,
          isFalse,
        );
        expect(
          securityService.validateInput('not-a-url', InputType.url).isValid,
          isFalse,
        );
      });

      test('validates phone numbers correctly', () {
        expect(
          securityService
              .validateInput('+1234567890', InputType.phoneNumber)
              .isValid,
          isTrue,
        );
        expect(
          securityService
              .validateInput('1234567890', InputType.phoneNumber)
              .isValid,
          isTrue,
        );
        expect(
          securityService.validateInput('123', InputType.phoneNumber).isValid,
          isFalse,
        ); // Too short
        expect(
          securityService
              .validateInput('abc123', InputType.phoneNumber)
              .isValid,
          isFalse,
        );
      });

      test('validates text input correctly', () {
        expect(
          securityService.validateInput('Normal text', InputType.text).isValid,
          isTrue,
        );
        expect(
          securityService
              .validateInput('Text with <script>', InputType.text)
              .isValid,
          isFalse,
        );
        expect(
          securityService
              .validateInput('Text with javascript:', InputType.text)
              .isValid,
          isFalse,
        );
        expect(
          securityService
              .validateInput('Text with onclick=', InputType.text)
              .isValid,
          isFalse,
        );
      });

      test('respects length limits', () {
        expect(
          securityService
              .validateInput('short', InputType.text, maxLength: 10)
              .isValid,
          isTrue,
        );
        expect(
          securityService
              .validateInput('this is too long', InputType.text, maxLength: 10)
              .isValid,
          isFalse,
        );
        expect(
          securityService
              .validateInput('ab', InputType.text, minLength: 5)
              .isValid,
          isFalse,
        );
        expect(
          securityService
              .validateInput('abcdef', InputType.text, minLength: 5)
              .isValid,
          isTrue,
        );
      });
    });

    group('Input Sanitization Tests', () {
      test('sanitizes HTML characters', () {
        expect(securityService.sanitizeInput('<script>'), '&lt;script&gt;');
        expect(
          securityService.sanitizeInput('Hello & goodbye'),
          'Hello &amp; goodbye',
        );
        expect(securityService.sanitizeInput('"quoted"'), '&quot;quoted&quot;');
        expect(securityService.sanitizeInput("'single'"), '&#x27;single&#x27;');
        expect(
          securityService.sanitizeInput('path/to/file'),
          'path&#x2F;to&#x2F;file',
        );
      });
    });

    group('Token Generation Tests', () {
      test('generates tokens of correct length', () {
        final token = securityService.generateSecureToken(length: 16);
        expect(token.length, 16);

        final defaultToken = securityService.generateSecureToken();
        expect(defaultToken.length, 32);
      });

      test('generates unique tokens', () {
        final token1 = securityService.generateSecureToken();
        final token2 = securityService.generateSecureToken();

        expect(token1, isNot(equals(token2)));
      });

      test('tokens contain only valid characters', () {
        final token = securityService.generateSecureToken();
        final validChars = RegExp(r'^[a-zA-Z0-9]+$');

        expect(validChars.hasMatch(token), isTrue);
      });
    });

    group('Hashing Tests', () {
      test('hashes data correctly', () {
        final data = 'sensitive data';
        final hash = securityService.hashData(data);

        expect(hash.contains(':'), isTrue); // Should contain salt separator
        expect(hash.length, greaterThan(data.length));
      });

      test('verifies hashed data correctly', () {
        final data = 'test data';
        final hash = securityService.hashData(data);

        expect(securityService.verifyHash(data, hash), isTrue);
        expect(securityService.verifyHash('wrong data', hash), isFalse);
      });

      test('same data produces different hashes with different salts', () {
        final data = 'test data';
        final hash1 = securityService.hashData(data);
        final hash2 = securityService.hashData(data);

        expect(hash1, isNot(equals(hash2)));

        // But both should verify correctly
        expect(securityService.verifyHash(data, hash1), isTrue);
        expect(securityService.verifyHash(data, hash2), isTrue);
      });
    });

    group('Audit Log Tests', () {
      test('records security events', () {
        securityService.recordFailedAttempt('test_user', 'password');

        final auditLog = securityService.getAuditLog();
        expect(auditLog.length, 1);
        expect(auditLog.first.type, SecurityEventType.authenticationFailed);
        expect(auditLog.first.identifier, 'test_user');
      });

      test('limits audit log size', () {
        // This would require adding many events to test the limit
        // For now, we test that the method works
        final auditLog = securityService.getAuditLog(limit: 5);
        expect(auditLog.length, lessThanOrEqualTo(5));
      });
    });

    group('Security Summary Tests', () {
      test('provides accurate security summary', () {
        // Generate some security events
        securityService.recordFailedAttempt('user1', 'password');
        securityService.recordFailedAttempt('user2', 'password');
        securityService.recordSuccessfulAuth('user3', 'password');

        final summary = securityService.getSecuritySummary();

        expect(summary.totalEvents, greaterThan(0));
        expect(summary.failedLogins, greaterThanOrEqualTo(2));
        expect(summary.successfulLogins, greaterThanOrEqualTo(1));
        expect(summary.securityScore, greaterThanOrEqualTo(0));
        expect(summary.securityScore, lessThanOrEqualTo(100));
      });

      test('calculates security score correctly', () {
        // Start with clean state - should have high score
        final cleanSummary = securityService.getSecuritySummary();
        expect(cleanSummary.securityScore, 100.0);

        // Add some failed attempts - score should decrease
        for (int i = 0; i < 3; i++) {
          securityService.recordFailedAttempt('test_user', 'password');
        }

        final degradedSummary = securityService.getSecuritySummary();
        expect(
          degradedSummary.securityScore,
          lessThan(cleanSummary.securityScore),
        );
      });
    });

    group('Edge Cases Tests', () {
      test('handles empty input validation', () {
        expect(
          securityService.validateInput('', InputType.email).isValid,
          isFalse,
        );
        expect(
          securityService.validateInput('', InputType.username).isValid,
          isFalse,
        );
        expect(
          securityService.validateInput('', InputType.password).isValid,
          isFalse,
        );
      });

      test('handles malformed hash verification', () {
        expect(securityService.verifyHash('data', 'malformed_hash'), isFalse);
        expect(securityService.verifyHash('data', 'no:colon'), isFalse);
        expect(securityService.verifyHash('data', ''), isFalse);
      });

      test('clears security data correctly', () {
        // Add some data
        securityService.recordFailedAttempt('test_user', 'password');
        securityService.checkRateLimit('test_user');

        // Verify data exists
        expect(
          securityService.getSecuritySummary().totalEvents,
          greaterThan(0),
        );

        // Clear data
        securityService.clearSecurityData();

        // Verify data is cleared
        final summary = securityService.getSecuritySummary();
        expect(summary.totalEvents, 0);
        expect(summary.failedLogins, 0);
        expect(summary.securityScore, 100.0);
      });
    });
  });
}
