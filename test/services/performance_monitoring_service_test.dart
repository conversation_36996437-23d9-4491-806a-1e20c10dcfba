import 'package:flutter_test/flutter_test.dart';
import 'package:billionaires_social/core/services/performance_monitoring_service.dart';

void main() {
  group('PerformanceMonitoringService Tests', () {
    late PerformanceMonitoringService service;

    setUp(() {
      service = PerformanceMonitoringService();
    });

    tearDown(() {
      service.stopMonitoring();
    });

    test('singleton pattern works correctly', () {
      final service1 = PerformanceMonitoringService();
      final service2 = PerformanceMonitoringService();

      expect(identical(service1, service2), isTrue);
    });

    test('trackMetric stores metrics correctly', () {
      service.trackMetric('test_metric', 42.0, category: 'test');

      final summary = service.getPerformanceSummary();
      expect(summary.totalMetrics, 1);
      expect(summary.recentMetrics.first.name, 'test_metric');
      expect(summary.recentMetrics.first.value, 42.0);
      expect(summary.recentMetrics.first.category, 'test');
    });

    test('trackOperation measures duration correctly', () async {
      final result = await service.trackOperation('test_operation', () async {
        await Future.delayed(const Duration(milliseconds: 100));
        return 'success';
      }, category: 'test');

      expect(result, 'success');

      final summary = service.getPerformanceSummary();
      expect(summary.totalMetrics, 1);

      final metric = summary.recentMetrics.first;
      expect(metric.name, 'test_operation_duration');
      expect(metric.value, greaterThan(90)); // Should be around 100ms
      expect(metric.metadata['success'], isTrue);
      expect(metric.metadata['operation'], 'test_operation');
    });

    test('trackOperation handles errors correctly', () async {
      bool exceptionThrown = false;
      try {
        await service.trackOperation('failing_operation', () async {
          throw Exception('Test error');
        });
      } catch (e) {
        exceptionThrown = true;
        expect(e.toString(), contains('Test error'));
      }

      expect(
        exceptionThrown,
        isTrue,
        reason: 'Expected exception to be thrown',
      );

      final summary = service.getPerformanceSummary();
      expect(summary.totalMetrics, 1);

      final metric = summary.recentMetrics.first;
      expect(metric.name, 'failing_operation_duration');
      expect(metric.metadata['success'], isFalse);
      expect(metric.metadata['error'], contains('Test error'));
    });

    test('performance summary provides correct data', () {
      // Add some test metrics
      service.trackMetric('metric1', 10.0);
      service.trackMetric('metric2', 20.0);
      service.trackMetric('metric3', 30.0);

      final summary = service.getPerformanceSummary();

      expect(summary.totalMetrics, 3);
      expect(summary.memoryUsageMB, greaterThan(0));
      expect(summary.averageFrameTimeMs, greaterThanOrEqualTo(0));
      expect(summary.recentMetrics.length, lessThanOrEqualTo(10));
      expect(summary.performanceScore, greaterThanOrEqualTo(0));
      expect(summary.performanceScore, lessThanOrEqualTo(100));
      expect(
        ['stable', 'increasing', 'decreasing'].contains(summary.memoryTrend),
        isTrue,
      );
    });

    test('detailed report contains all required fields', () {
      service.trackMetric('test_metric', 42.0);

      final report = service.getDetailedReport();

      expect(report['timestamp'], isNotNull);
      expect(report['monitoring_duration'], isNotNull);
      expect(report['metrics_count'], 1);
      expect(report['memory_snapshots'], isNotNull);
      expect(report['frame_timings'], isNotNull);
      expect(report['current_memory_mb'], greaterThan(0));
      expect(report['performance_score'], isNotNull);
      expect(report['top_metrics'], isA<List>());
      expect(report['memory_trend'], isNotNull);
      expect(report['frame_performance'], isA<Map>());
      expect(report['recommendations'], isA<List>());
    });

    test('monitoring can be started and stopped', () async {
      expect(service.getDetailedReport()['monitoring_duration'], 'inactive');

      await service.startMonitoring();
      expect(service.getDetailedReport()['monitoring_duration'], 'active');

      service.stopMonitoring();
      expect(service.getDetailedReport()['monitoring_duration'], 'inactive');
    });

    test('metrics are limited to prevent memory leaks', () {
      // Add more metrics than the limit
      for (int i = 0; i < 1200; i++) {
        service.trackMetric('metric_$i', i.toDouble());
      }

      final summary = service.getPerformanceSummary();
      expect(
        summary.totalMetrics,
        lessThanOrEqualTo(1000),
      ); // maxMetricsHistory
    });

    test('performance score calculation works', () {
      final summary = service.getPerformanceSummary();
      final score = summary.performanceScore;

      expect(score, greaterThanOrEqualTo(0));
      expect(score, lessThanOrEqualTo(100));
    });

    test('memory trend detection works', () {
      final summary = service.getPerformanceSummary();
      final trend = summary.memoryTrend;

      expect(['stable', 'increasing', 'decreasing'].contains(trend), isTrue);
    });

    test('frame performance stats are generated', () {
      final report = service.getDetailedReport();
      final frameStats = report['frame_performance'] as Map<String, dynamic>;

      if (frameStats['status'] != 'no_data') {
        expect(frameStats['average_frame_time_ms'], isNotNull);
        expect(frameStats['max_frame_time_ms'], isNotNull);
        expect(frameStats['min_frame_time_ms'], isNotNull);
        expect(frameStats['target_fps'], 60);
        expect(frameStats['actual_fps'], isNotNull);
        expect(frameStats['frame_drops'], isA<int>());
      }
    });

    test('recommendations are generated based on performance', () {
      final report = service.getDetailedReport();
      final recommendations = report['recommendations'] as List<dynamic>;

      expect(recommendations, isA<List>());
      // Recommendations should be strings
      for (final rec in recommendations) {
        expect(rec, isA<String>());
      }
    });

    test('top metrics are correctly sorted and limited', () {
      // Add metrics with different timestamps
      for (int i = 0; i < 10; i++) {
        service.trackMetric('metric_$i', i.toDouble());
        // Small delay to ensure different timestamps
      }

      final report = service.getDetailedReport();
      final topMetrics = report['top_metrics'] as List<dynamic>;

      expect(topMetrics.length, lessThanOrEqualTo(5));

      for (final metric in topMetrics) {
        expect(metric['name'], isNotNull);
        expect(metric['value'], isNotNull);
        expect(metric['category'], isNotNull);
        expect(metric['timestamp'], isNotNull);
      }
    });

    test('metadata is correctly stored with metrics', () {
      final metadata = {
        'user_id': 'test_user',
        'screen': 'home',
        'action': 'load_data',
      };

      service.trackMetric(
        'test_with_metadata',
        100.0,
        category: 'user_action',
        metadata: metadata,
      );

      final summary = service.getPerformanceSummary();
      final metric = summary.recentMetrics.first;

      expect(metric.metadata['user_id'], 'test_user');
      expect(metric.metadata['screen'], 'home');
      expect(metric.metadata['action'], 'load_data');
    });

    test('service handles concurrent operations safely', () async {
      final futures = <Future>[];

      // Start multiple concurrent operations
      for (int i = 0; i < 10; i++) {
        futures.add(
          service.trackOperation('concurrent_op_$i', () async {
            await Future.delayed(Duration(milliseconds: 10 + i));
            return i;
          }),
        );
      }

      final results = await Future.wait(futures);

      expect(results.length, 10);
      for (int i = 0; i < 10; i++) {
        expect(results[i], i);
      }

      final summary = service.getPerformanceSummary();
      expect(summary.totalMetrics, 10);
    });
  });
}
