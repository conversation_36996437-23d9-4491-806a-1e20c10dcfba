// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyA0uI0bZsnI6sgOBxccnbPwxBM1OQtXKK8',
    appId: '1:409365364995:web:279c38a92d715ae7b5a936',
    messagingSenderId: '409365364995',
    projectId: 'billionaires-social',
    authDomain: 'billionaires-social.firebaseapp.com',
    storageBucket: 'billionaires-social.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBKKHEdrSLIDdv2dYYLeNq7TA0wMbSHa0A',
    appId: '1:409365364995:android:1916b6938f51039cb5a936',
    messagingSenderId: '409365364995',
    projectId: 'billionaires-social',
    storageBucket: 'billionaires-social.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyB8RDQMERrfyD4QZrHoMwlmk5-zKJZXeOE',
    appId: '1:409365364995:ios:cf4a26c29b14b9cab5a936',
    messagingSenderId: '409365364995',
    projectId: 'billionaires-social',
    storageBucket: 'billionaires-social.firebasestorage.app',
    iosBundleId: 'com.example.billionairesSocial',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyB8RDQMERrfyD4QZrHoMwlmk5-zKJZXeOE',
    appId: '1:409365364995:ios:cf4a26c29b14b9cab5a936',
    messagingSenderId: '409365364995',
    projectId: 'billionaires-social',
    storageBucket: 'billionaires-social.firebasestorage.app',
    iosBundleId: 'com.example.billionairesSocial',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyA0uI0bZsnI6sgOBxccnbPwxBM1OQtXKK8',
    appId: '1:409365364995:web:6ab31c22062efdd3b5a936',
    messagingSenderId: '409365364995',
    projectId: 'billionaires-social',
    authDomain: 'billionaires-social.firebaseapp.com',
    storageBucket: 'billionaires-social.firebasestorage.app',
  );
}
