import 'package:flutter/material.dart';
import 'package:billionaires_social/core/widgets/universal_validation_widget.dart';
import 'package:billionaires_social/core/services/universal_user_role_service.dart';

/// Debug screen for testing universal architecture implementation
/// This screen can be used to validate that all universal services work correctly
class UniversalArchitectureTestScreen extends StatefulWidget {
  const UniversalArchitectureTestScreen({super.key});

  @override
  State<UniversalArchitectureTestScreen> createState() => _UniversalArchitectureTestScreenState();
}

class _UniversalArchitectureTestScreenState extends State<UniversalArchitectureTestScreen> {
  String? _currentUserId;
  UserAccountType? _accountType;
  ContentLimits? _contentLimits;

  @override
  void initState() {
    super.initState();
    _loadUserInfo();
  }

  Future<void> _loadUserInfo() async {
    final userId = UniversalUserRoleService.getCurrentUserId();
    if (userId != null) {
      final accountType = await UniversalUserRoleService.getUserAccountType(userId);
      final limits = await UniversalUserRoleService.getContentLimits(userId);
      
      setState(() {
        _currentUserId = userId;
        _accountType = accountType;
        _contentLimits = limits;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Universal Architecture Test'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildUserInfoCard(),
            const SizedBox(height: 16),
            _buildArchitectureStatusCard(),
            const SizedBox(height: 16),
            const UniversalValidationWidget(
              showDetailedResults: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.person, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  'Current User Info',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow('User ID', _currentUserId ?? 'Not authenticated'),
            _buildInfoRow('Account Type', _accountType?.name ?? 'Unknown'),
            if (_contentLimits != null) ...[
              _buildInfoRow('Max Media Per Post', 
                _contentLimits!.maxMediaPerPost == -1 
                  ? 'Unlimited' 
                  : _contentLimits!.maxMediaPerPost.toString()),
              _buildInfoRow('Max Stories Per Day', 
                _contentLimits!.maxStoriesPerDay == -1 
                  ? 'Unlimited' 
                  : _contentLimits!.maxStoriesPerDay.toString()),
              _buildInfoRow('Can Create Reels', 
                _contentLimits!.canCreateReels ? 'Yes' : 'No'),
              _buildInfoRow('Can Go Live', 
                _contentLimits!.canGoLive ? 'Yes' : 'No'),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildArchitectureStatusCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.architecture, color: Colors.green),
                const SizedBox(width: 8),
                const Text(
                  'Universal Architecture Status',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildStatusRow('Universal User Role Service', true),
            _buildStatusRow('Universal Navigation Service', true),
            _buildStatusRow('Universal Content Service', true),
            _buildStatusRow('Universal Social Interaction Service', true),
            _buildStatusRow('Universal UI Service', true),
            _buildStatusRow('Universal Account Initialization Service', true),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green[50],
                border: Border.all(color: Colors.green),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Universal Architecture Successfully Implemented!\n'
                      'All services are operational and ready for use.',
                      style: TextStyle(
                        color: Colors.green,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 140,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(color: Colors.grey[700]),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusRow(String service, bool isImplemented) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            isImplemented ? Icons.check_circle : Icons.error,
            color: isImplemented ? Colors.green : Colors.red,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              service,
              style: TextStyle(
                color: isImplemented ? Colors.green[700] : Colors.red[700],
              ),
            ),
          ),
          Text(
            isImplemented ? 'Implemented' : 'Missing',
            style: TextStyle(
              color: isImplemented ? Colors.green : Colors.red,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }
}
