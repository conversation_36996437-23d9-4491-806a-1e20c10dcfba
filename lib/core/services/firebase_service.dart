import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'dart:io';
import 'package:billionaires_social/features/profile/models/profile_model.dart';
import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'dart:math';

final firebaseServiceProvider = Provider<FirebaseService>((ref) {
  return getIt<FirebaseService>();
});

class FirebaseService {
  static final FirebaseService _instance = FirebaseService._internal();
  factory FirebaseService() => _instance;
  FirebaseService._internal();

  // Firebase instances
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;

  // Auth methods
  User? get currentUser => _auth.currentUser;
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Sign up with email and password
  Future<void> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
  }) async {
    try {
      final userCredential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Create user profile in Firestore
      if (userCredential.user != null) {
        await _firestore.collection('users').doc(userCredential.user!.uid).set({
          'id': userCredential.user!.uid,
          'email': email,
          'name': name,
          'username': email.split('@')[0], // Default username
          'bio': '',
          'profilePictureUrl': '',
          'postCount': 0,
          'followerCount': 0,
          'followingCount': 0,
          'isVerified': false,
          'isBillionaire': false,
          'isAdmin': false,
          'isBusinessAccount': false,
          'businessName': '',
          'businessCategory': '',
          'website': '',
          'createdAt': FieldValue.serverTimestamp(),
        });
      }
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw Exception('An unexpected error occurred during sign up');
    }
  }

  // Sign in with email and password
  Future<void> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      await _auth.signInWithEmailAndPassword(email: email, password: password);
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw Exception('An unexpected error occurred during sign in');
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _auth.signOut();
    } catch (e) {
      throw Exception('An error occurred during sign out');
    }
  }

  // Reset password
  Future<void> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw Exception('An error occurred while resetting password');
    }
  }

  // Verify backup code for 2FA recovery
  Future<void> verifyBackupCode(String backupCode) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('No user is currently signed in');
      }

      // Get user's backup codes from Firestore
      final userDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .get();
      if (!userDoc.exists) {
        throw Exception('User profile not found');
      }

      final userData = userDoc.data()!;
      final List<String> backupCodes = List<String>.from(
        userData['backupCodes'] ?? [],
      );

      if (backupCodes.isEmpty) {
        throw Exception('No backup codes found for this account');
      }

      // Check if the provided code matches any backup code
      final isValidCode = backupCodes.contains(backupCode);
      if (!isValidCode) {
        throw Exception('Invalid backup code');
      }

      // Remove the used backup code
      backupCodes.remove(backupCode);
      await _firestore.collection('users').doc(currentUser.uid).update({
        'backupCodes': backupCodes,
        'lastBackupCodeUsed': FieldValue.serverTimestamp(),
      });

      // Mark 2FA as verified for this session
      await _firestore.collection('users').doc(currentUser.uid).update({
        'twoFactorVerified': true,
        'twoFactorVerifiedAt': FieldValue.serverTimestamp(),
      });
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw Exception('Backup code verification failed: ${e.toString()}');
    }
  }

  // Generate backup codes for 2FA
  Future<List<String>> generateBackupCodes(String userId) async {
    try {
      final List<String> backupCodes = [];
      const String chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
      final random = Random();

      // Generate 10 backup codes, each 8 characters long
      for (int i = 0; i < 10; i++) {
        String code = '';
        for (int j = 0; j < 8; j++) {
          code += chars[random.nextInt(chars.length)];
        }
        backupCodes.add(code);
      }

      // Store backup codes in Firestore
      await _firestore.collection('users').doc(userId).update({
        'backupCodes': backupCodes,
        'backupCodesGeneratedAt': FieldValue.serverTimestamp(),
      });

      return backupCodes;
    } catch (e) {
      throw Exception('Failed to generate backup codes: ${e.toString()}');
    }
  }

  // Get user profile
  Future<ProfileModel?> getUserProfile(String userId) async {
    try {
      debugPrint('🔥 Firebase: Fetching user profile for: $userId');

      final doc = await _firestore.collection('users').doc(userId).get();
      if (doc.exists) {
        final data = doc.data()!;
        debugPrint('🔥 Firebase: Raw profile data: $data');

        // Map Firestore fields to ProfileModel fields
        final mappedData = <String, dynamic>{
          'id': doc.id,
          'username':
              data['username'] ?? data['email']?.split('@')[0] ?? 'user',
          'name': data['name'] ?? '',
          'profilePictureUrl': data['profilePictureUrl'] ?? '',
          'bio': data['bio'] ?? '',
          'postCount': data['postCount'] ?? 0,
          'followerCount': data['followerCount'] ?? 0,
          'followingCount': data['followingCount'] ?? 0,
          'isVerified': data['isVerified'] ?? false,
          'isBillionaire': data['isBillionaire'] ?? false,
          'isAdmin': data['isAdmin'] ?? false,
          'isBusinessAccount': data['isBusinessAccount'] ?? false,
          'businessName': data['businessName'] ?? '',
          'businessCategory': data['businessCategory'] ?? '',
          'website': data['website'] ?? '',
          'email': data['email'] ?? '',
          'createdAt': data['createdAt'] != null
              ? (data['createdAt'] as Timestamp).toDate().toIso8601String()
              : DateTime.now().toIso8601String(),
        };

        debugPrint('🔥 Firebase: Mapped profile data: $mappedData');
        debugPrint(
          '🔥 Firebase: Profile picture URL: ${mappedData['profilePictureUrl']}',
        );

        return ProfileModel.fromJson(mappedData);
      }
      debugPrint(
        '❌ Firebase: User profile document does not exist for: $userId',
      );
      return null;
    } catch (e) {
      debugPrint('❌ Firebase: Failed to fetch user profile: $e');
      throw Exception('Failed to fetch user profile: ${e.toString()}');
    }
  }

  // Update user profile
  Future<void> updateUserProfile(
    String userId,
    Map<String, dynamic> updates,
  ) async {
    try {
      debugPrint('🔥 Firebase: Updating user profile for: $userId');
      debugPrint('🔥 Firebase: Update data: $updates');

      await _firestore.collection('users').doc(userId).update(updates);

      debugPrint('✅ Firebase: User profile updated successfully');
    } catch (e) {
      debugPrint('❌ Firebase: Failed to update user profile: $e');
      throw Exception('Failed to update user profile: $e');
    }
  }

  // Upload profile image
  Future<String> uploadProfileImage(String userId, File imageFile) async {
    try {
      debugPrint(
        '🔥 Firebase: Starting profile image upload for user: $userId',
      );
      final fileSizeInBytes = await imageFile.length();
      final fileSizeInMB = fileSizeInBytes / (1024 * 1024);
      debugPrint(
        '🔥 Firebase: Uploading file of size: ${fileSizeInMB.toStringAsFixed(2)} MB',
      );

      final fileName = const Uuid().v4();
      final ref = _storage.ref().child('profile_images/$userId/$fileName.jpg');
      debugPrint('🔥 Firebase: Storage reference created: ${ref.fullPath}');

      final uploadTask = ref.putFile(imageFile);
      debugPrint('🔥 Firebase: Upload task started...');

      final snapshot = await uploadTask;
      debugPrint('🔥 Firebase: Upload completed, getting download URL...');

      final downloadUrl = await snapshot.ref.getDownloadURL();
      debugPrint('🔥 Firebase: Download URL obtained: $downloadUrl');

      return downloadUrl;
    } catch (e) {
      debugPrint('❌ Firebase: Profile image upload failed: $e');
      throw Exception('Failed to upload profile image: $e');
    }
  }

  // Upload image (compatibility method)
  Future<String> uploadImage(String filePath, String fileName) async {
    try {
      // Check if user is authenticated
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('User must be authenticated to upload files');
      }
      
      // Check if user's auth token is still valid
      try {
        await user.getIdToken(true); // Force refresh token
        debugPrint('🔥 Firebase: Auth token refreshed successfully');
      } catch (e) {
        debugPrint('❌ Firebase: Auth token refresh failed: $e');
        throw Exception('Authentication session expired. Please sign in again.');
      }
      
      debugPrint('🔥 Firebase: Starting upload for authenticated user: ${user.uid}');
      debugPrint('🔥 Firebase: User email: ${user.email}');
      debugPrint('🔥 Firebase: File path: $filePath');
      debugPrint('🔥 Firebase: Target filename: $fileName');
      
      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('File does not exist: $filePath');
      }
      
      // Log file info
      final fileSizeInBytes = await file.length();
      final fileSizeInMB = fileSizeInBytes / (1024 * 1024);
      debugPrint('🔥 Firebase: File size: ${fileSizeInMB.toStringAsFixed(2)} MB');
      
      // Create storage reference with user-specific path for security
      final ref = _storage.ref().child('user_uploads/${user.uid}/$fileName');
      debugPrint('🔥 Firebase: Storage reference: ${ref.fullPath}');
      
      // Upload file
      final uploadTask = ref.putFile(file);
      final snapshot = await uploadTask;
      
      // Get download URL
      final downloadUrl = await snapshot.ref.getDownloadURL();
      debugPrint('🔥 Firebase: Upload successful: $downloadUrl');
      
      return downloadUrl;
    } on FirebaseException catch (e) {
      debugPrint('❌ Firebase Storage error: ${e.code} - ${e.message}');
      
      // Handle specific Firebase Storage errors
      switch (e.code) {
        case 'storage/unauthorized':
          throw Exception('Upload failed: You do not have permission to upload files. Please check your authentication.');
        case 'storage/canceled':
          throw Exception('Upload was canceled');
        case 'storage/quota-exceeded':
          throw Exception('Upload failed: Storage quota exceeded');
        case 'storage/invalid-format':
          throw Exception('Upload failed: Invalid file format');
        case 'storage/object-not-found':
          throw Exception('Upload failed: Storage location not found');
        default:
          throw Exception('Upload failed: ${e.message ?? e.code}');
      }
    } catch (e) {
      debugPrint('❌ Upload error: $e');
      throw Exception('Upload failed: ${e.toString()}');
    }
  }

  // Create post
  Future<void> createPost({
    required String userId,
    required String content,
    List<String>? imageUrls,
    String? location,
  }) async {
    try {
      await _firestore.collection('posts').add({
        'userId': userId,
        'content': content,
        'imageUrls': imageUrls ?? [],
        'location': location,
        'likes': [],
        'comments': [],
        'createdAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('Failed to create post');
    }
  }

  // Upload post image
  Future<String> uploadPostImage(String postId, File imageFile) async {
    try {
      final ref = _storage.ref().child('post_images/$postId.jpg');
      final uploadTask = ref.putFile(imageFile);
      final snapshot = await uploadTask;
      return await snapshot.ref.getDownloadURL();
    } catch (e) {
      throw Exception('Failed to upload post image');
    }
  }

  // Handle Firebase Auth exceptions
  String _handleAuthException(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return 'No user found with this email address';
      case 'wrong-password':
        return 'Incorrect password';
      case 'email-already-in-use':
        return 'An account with this email already exists';
      case 'weak-password':
        return 'Password is too weak';
      case 'invalid-email':
        return 'Invalid email address';
      case 'user-disabled':
        return 'This account has been disabled';
      case 'too-many-requests':
        return 'Too many failed attempts. Please try again later';
      default:
        return 'Authentication failed: ${e.message}';
    }
  }

  // Check Firebase connection status
  Future<bool> isFirebaseConnected() async {
    try {
      // Perform a simple read operation to check the connection
      await _firestore.collection('app_status').doc('connection_test').get();
      return true;
    } catch (e) {
      // Any exception during the read indicates a connection problem
      return false;
    }
  }
}
