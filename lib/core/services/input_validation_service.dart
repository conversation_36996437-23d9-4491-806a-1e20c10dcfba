/// Comprehensive input validation service
/// Provides secure validation for all user inputs across the app
class InputValidationService {
  static final InputValidationService _instance =
      InputValidationService._internal();
  factory InputValidationService() => _instance;
  InputValidationService._internal();

  /// Email validation with comprehensive checks
  ValidationResult validateEmail(String email) {
    if (email.isEmpty) {
      return ValidationResult(false, 'Email is required');
    }

    // Basic format check
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(email)) {
      return ValidationResult(false, 'Invalid email format');
    }

    // Length check
    if (email.length > 254) {
      return ValidationResult(false, 'Email is too long');
    }

    // Check for dangerous patterns
    if (_containsDangerousPatterns(email)) {
      return ValidationResult(false, 'Email contains invalid characters');
    }

    return ValidationResult(true, 'Valid email');
  }

  /// Password validation with security requirements
  ValidationResult validatePassword(String password) {
    if (password.isEmpty) {
      return ValidationResult(false, 'Password is required');
    }

    if (password.length < 8) {
      return ValidationResult(false, 'Password must be at least 8 characters');
    }

    if (password.length > 128) {
      return ValidationResult(false, 'Password is too long');
    }

    // Check for at least one uppercase letter
    if (!RegExp(r'[A-Z]').hasMatch(password)) {
      return ValidationResult(
        false,
        'Password must contain at least one uppercase letter',
      );
    }

    // Check for at least one lowercase letter
    if (!RegExp(r'[a-z]').hasMatch(password)) {
      return ValidationResult(
        false,
        'Password must contain at least one lowercase letter',
      );
    }

    // Check for at least one digit
    if (!RegExp(r'[0-9]').hasMatch(password)) {
      return ValidationResult(
        false,
        'Password must contain at least one number',
      );
    }

    // Check for at least one special character
    if (!RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) {
      return ValidationResult(
        false,
        'Password must contain at least one special character',
      );
    }

    // Check for common weak passwords
    if (_isCommonPassword(password)) {
      return ValidationResult(
        false,
        'Password is too common, please choose a stronger password',
      );
    }

    return ValidationResult(true, 'Strong password');
  }

  /// Username validation
  ValidationResult validateUsername(String username) {
    if (username.isEmpty) {
      return ValidationResult(false, 'Username is required');
    }

    if (username.length < 3) {
      return ValidationResult(false, 'Username must be at least 3 characters');
    }

    if (username.length > 30) {
      return ValidationResult(false, 'Username is too long');
    }

    // Allow only alphanumeric characters, underscores, and dots
    final usernameRegex = RegExp(r'^[a-zA-Z0-9._]+$');
    if (!usernameRegex.hasMatch(username)) {
      return ValidationResult(
        false,
        'Username can only contain letters, numbers, dots, and underscores',
      );
    }

    // Check for reserved usernames
    if (_isReservedUsername(username)) {
      return ValidationResult(false, 'This username is not available');
    }

    return ValidationResult(true, 'Valid username');
  }

  /// Text content validation (posts, comments, etc.)
  ValidationResult validateTextContent(String content, {int maxLength = 2000}) {
    if (content.isEmpty) {
      return ValidationResult(false, 'Content cannot be empty');
    }

    if (content.length > maxLength) {
      return ValidationResult(
        false,
        'Content is too long (max $maxLength characters)',
      );
    }

    // Check for dangerous patterns
    if (_containsDangerousPatterns(content)) {
      return ValidationResult(
        false,
        'Content contains invalid or dangerous characters',
      );
    }

    // Check for spam patterns
    if (_isSpamContent(content)) {
      return ValidationResult(false, 'Content appears to be spam');
    }

    return ValidationResult(true, 'Valid content');
  }

  /// URL validation
  ValidationResult validateUrl(String url) {
    if (url.isEmpty) {
      return ValidationResult(false, 'URL is required');
    }

    try {
      final uri = Uri.parse(url);

      if (!uri.hasScheme) {
        return ValidationResult(
          false,
          'URL must include protocol (http:// or https://)',
        );
      }

      if (!['http', 'https'].contains(uri.scheme.toLowerCase())) {
        return ValidationResult(false, 'Only HTTP and HTTPS URLs are allowed');
      }

      if (!uri.hasAuthority) {
        return ValidationResult(false, 'Invalid URL format');
      }

      // Check for dangerous domains
      if (_isDangerousDomain(uri.host)) {
        return ValidationResult(false, 'This domain is not allowed');
      }

      return ValidationResult(true, 'Valid URL');
    } catch (e) {
      return ValidationResult(false, 'Invalid URL format');
    }
  }

  /// Phone number validation
  ValidationResult validatePhoneNumber(String phone) {
    if (phone.isEmpty) {
      return ValidationResult(false, 'Phone number is required');
    }

    // Remove common formatting characters
    final cleanPhone = phone.replaceAll(RegExp(r'[\s\-\(\)]'), '');

    // Basic international format check
    final phoneRegex = RegExp(r'^\+?[1-9]\d{1,14}$');
    if (!phoneRegex.hasMatch(cleanPhone)) {
      return ValidationResult(false, 'Invalid phone number format');
    }

    return ValidationResult(true, 'Valid phone number');
  }

  /// Private helper methods
  bool _containsDangerousPatterns(String input) {
    final dangerousPatterns = [
      r'<script[^>]*>.*?</script>',
      r'javascript:',
      r'onclick\s*=',
      r'onerror\s*=',
      r'onload\s*=',
      r'<iframe[^>]*>',
      r'<object[^>]*>',
      r'<embed[^>]*>',
      r'eval\s*\(',
      r'document\.cookie',
      r'window\.location',
    ];

    for (final pattern in dangerousPatterns) {
      if (RegExp(pattern, caseSensitive: false).hasMatch(input)) {
        return true;
      }
    }

    return false;
  }

  bool _isCommonPassword(String password) {
    final commonPasswords = [
      'password',
      '123456',
      '123456789',
      'qwerty',
      'abc123',
      'password123',
      'admin',
      'letmein',
      'welcome',
      'monkey',
      'dragon',
      'master',
      'shadow',
      'superman',
      'michael',
    ];

    return commonPasswords.contains(password.toLowerCase());
  }

  bool _isReservedUsername(String username) {
    final reservedUsernames = [
      'admin',
      'administrator',
      'root',
      'system',
      'api',
      'www',
      'mail',
      'email',
      'support',
      'help',
      'info',
      'contact',
      'about',
      'privacy',
      'terms',
      'legal',
      'security',
      'test',
      'demo',
      'guest',
      'user',
      'null',
      'undefined',
      'billionaire',
      'billionaires',
      'social',
      'app',
      'mobile',
      'ios',
      'android',
    ];

    return reservedUsernames.contains(username.toLowerCase());
  }

  bool _isSpamContent(String content) {
    final spamPatterns = [
      r'(buy now|click here|free money|get rich|make money fast)',
      r'(viagra|cialis|pharmacy|pills)',
      r'(lottery|winner|congratulations.*won)',
      r'(urgent.*reply|act now|limited time)',
    ];

    for (final pattern in spamPatterns) {
      if (RegExp(pattern, caseSensitive: false).hasMatch(content)) {
        return true;
      }
    }

    // Check for excessive repetition
    if (RegExp(r'(.)\1{10,}').hasMatch(content)) {
      return true;
    }

    // Check for excessive capitalization
    final upperCaseCount = content
        .split('')
        .where((c) => c == c.toUpperCase() && c != c.toLowerCase())
        .length;
    if (upperCaseCount > content.length * 0.7 && content.length > 10) {
      return true;
    }

    return false;
  }

  bool _isDangerousDomain(String domain) {
    final dangerousDomains = [
      'malware.com', 'phishing.com', 'spam.com',
      // Add more known dangerous domains
    ];

    return dangerousDomains.contains(domain.toLowerCase());
  }
}

/// Validation result class
class ValidationResult {
  final bool isValid;
  final String message;

  ValidationResult(this.isValid, this.message);

  @override
  String toString() => 'ValidationResult(isValid: $isValid, message: $message)';
}
