import 'dart:convert';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import 'package:url_launcher/url_launcher.dart';

class VersionInfo {
  final String version;
  final String buildNumber;
  final bool forceUpdate;
  final String? updateMessage;
  final String? updateUrl;
  final DateTime? releaseDate;
  final List<String>? changelog;

  VersionInfo({
    required this.version,
    required this.buildNumber,
    this.forceUpdate = false,
    this.updateMessage,
    this.updateUrl,
    this.releaseDate,
    this.changelog,
  });

  factory VersionInfo.fromJson(Map<String, dynamic> json) {
    return VersionInfo(
      version: json['version'] ?? '',
      buildNumber: json['buildNumber'] ?? '',
      forceUpdate: json['forceUpdate'] ?? false,
      updateMessage: json['updateMessage'],
      updateUrl: json['updateUrl'],
      releaseDate: json['releaseDate'] != null
          ? DateTime.parse(json['releaseDate'])
          : null,
      changelog: json['changelog'] != null
          ? List<String>.from(json['changelog'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'version': version,
      'buildNumber': buildNumber,
      'forceUpdate': forceUpdate,
      'updateMessage': updateMessage,
      'updateUrl': updateUrl,
      'releaseDate': releaseDate?.toIso8601String(),
      'changelog': changelog,
    };
  }
}

class VersionControlService {
  static final VersionControlService _instance =
      VersionControlService._internal();
  factory VersionControlService() => _instance;
  VersionControlService._internal();

  static const String _versionKey = 'app_version_info';
  static const String _lastCheckKey = 'last_version_check';
  static const Duration _checkInterval = Duration(hours: 24);

  late SharedPreferences _prefs;
  VersionInfo? _currentVersion;
  VersionInfo? _latestVersion;

  // Initialize version control service
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    await _loadCurrentVersion();
  }

  // Load current app version
  Future<void> _loadCurrentVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      _currentVersion = VersionInfo(
        version: packageInfo.version,
        buildNumber: packageInfo.buildNumber,
      );
    } catch (e) {
      debugPrint('Error loading current version: $e');
    }
  }

  // Check for app updates
  Future<VersionInfo?> checkForUpdates({bool force = false}) async {
    try {
      // Check if we should skip this check
      if (!force && !_shouldCheckForUpdates()) {
        return null;
      }

      // In a real app, this would fetch from your backend
      // For now, we'll simulate a version check
      await _simulateVersionCheck();

      // Save last check time
      await _prefs.setString(_lastCheckKey, DateTime.now().toIso8601String());

      return _latestVersion;
    } catch (e) {
      debugPrint('Error checking for updates: $e');
      return null;
    }
  }

  // Simulate version check (replace with actual API call)
  Future<void> _simulateVersionCheck() async {
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));

    // Mock latest version data
    _latestVersion = VersionInfo(
      version: '1.1.0',
      buildNumber: '2',
      forceUpdate: false,
      updateMessage: 'New features and bug fixes available!',
      updateUrl: 'https://apps.apple.com/app/id123456789',
      releaseDate: DateTime.now().subtract(const Duration(days: 1)),
      changelog: [
        '✨ New story filters and effects',
        '🎨 Enhanced UI/UX improvements',
        '🐛 Bug fixes and performance optimizations',
        '🔒 Improved security features',
      ],
    );

    // Save to local storage
    await _prefs.setString(_versionKey, jsonEncode(_latestVersion!.toJson()));
  }

  // Check if we should perform a version check
  bool _shouldCheckForUpdates() {
    final lastCheckString = _prefs.getString(_lastCheckKey);
    if (lastCheckString == null) return true;

    try {
      final lastCheck = DateTime.parse(lastCheckString);
      return DateTime.now().difference(lastCheck) > _checkInterval;
    } catch (e) {
      return true;
    }
  }

  // Check if update is required
  bool isUpdateRequired() {
    if (_currentVersion == null || _latestVersion == null) return false;

    final current = _currentVersion!;
    final latest = _latestVersion!;

    // Compare versions
    final currentVersion = _parseVersion(current.version);
    final latestVersion = _parseVersion(latest.version);

    return _compareVersions(currentVersion, latestVersion) < 0;
  }

  // Check if force update is required
  bool isForceUpdateRequired() {
    return _latestVersion?.forceUpdate ?? false;
  }

  // Parse version string to comparable format
  List<int> _parseVersion(String version) {
    return version.split('.').map((e) => int.tryParse(e) ?? 0).toList();
  }

  // Compare two version arrays
  int _compareVersions(List<int> v1, List<int> v2) {
    final maxLength = v1.length > v2.length ? v1.length : v2.length;

    for (int i = 0; i < maxLength; i++) {
      final num1 = i < v1.length ? v1[i] : 0;
      final num2 = i < v2.length ? v2[i] : 0;

      if (num1 < num2) return -1;
      if (num1 > num2) return 1;
    }

    return 0;
  }

  // Get current version info
  VersionInfo? getCurrentVersion() => _currentVersion;

  // Get latest version info
  VersionInfo? getLatestVersion() => _latestVersion;

  // Launch app store for update
  Future<bool> launchAppStore() async {
    final updateUrl = _latestVersion?.updateUrl;
    if (updateUrl == null) return false;

    try {
      final uri = Uri.parse(updateUrl);
      return await launchUrl(uri, mode: LaunchMode.externalApplication);
    } catch (e) {
      debugPrint('Error launching app store: $e');
      return false;
    }
  }

  // Get update message
  String getUpdateMessage() {
    return _latestVersion?.updateMessage ?? 'A new version is available!';
  }

  // Get changelog
  List<String> getChangelog() {
    return _latestVersion?.changelog ?? [];
  }

  // Check if app is up to date
  bool isUpToDate() {
    if (_currentVersion == null || _latestVersion == null) return true;

    final current = _currentVersion!;
    final latest = _latestVersion!;

    final currentVersion = _parseVersion(current.version);
    final latestVersion = _parseVersion(latest.version);

    return _compareVersions(currentVersion, latestVersion) >= 0;
  }

  // Get version comparison result
  String getVersionComparison() {
    if (_currentVersion == null || _latestVersion == null) {
      return 'Unknown';
    }

    final current = _currentVersion!;
    final latest = _latestVersion!;

    final currentVersion = _parseVersion(current.version);
    final latestVersion = _parseVersion(latest.version);

    final comparison = _compareVersions(currentVersion, latestVersion);

    if (comparison < 0) {
      return '${current.version} → ${latest.version}';
    } else if (comparison > 0) {
      return '${current.version} (ahead of ${latest.version})';
    } else {
      return '${current.version} (latest)';
    }
  }

  // Clear cached version info
  Future<void> clearCache() async {
    await _prefs.remove(_versionKey);
    await _prefs.remove(_lastCheckKey);
    _latestVersion = null;
  }

  // Get version statistics
  Map<String, dynamic> getVersionStats() {
    return {
      'current_version': _currentVersion?.version,
      'current_build': _currentVersion?.buildNumber,
      'latest_version': _latestVersion?.version,
      'latest_build': _latestVersion?.buildNumber,
      'is_up_to_date': isUpToDate(),
      'update_required': isUpdateRequired(),
      'force_update_required': isForceUpdateRequired(),
      'version_comparison': getVersionComparison(),
      'last_check': _prefs.getString(_lastCheckKey),
    };
  }
}
