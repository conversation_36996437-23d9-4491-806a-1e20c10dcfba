/// Analytics Event Documentation for QA/Compliance
///
/// This file lists all analytics events tracked in the app, including
/// event names, descriptions, and parameter schemas.
library;

class AnalyticsEventDoc {
  static const List<Map<String, dynamic>> events = [
    {
      'event': 'post_created',
      'description': 'User creates a new post',
      'parameters': [
        {'name': 'post_id', 'type': 'String', 'desc': 'Unique post identifier'},
        {
          'name': 'content_type',
          'type': 'String',
          'desc': 'Type of content (text, image, video, etc.)',
        },
        {
          'name': 'has_media',
          'type': 'bool',
          'desc': 'Whether the post contains media',
        },
        {
          'name': 'location',
          'type': 'String',
          'desc': 'Location of the post (optional)',
        },
        {'name': 'timestamp', 'type': 'String', 'desc': 'ISO8601 timestamp'},
      ],
    },
    {
      'event': 'post_liked',
      'description': 'User likes a post',
      'parameters': [
        {'name': 'post_id', 'type': 'String'},
        {'name': 'post_author_id', 'type': 'String'},
        {'name': 'timestamp', 'type': 'String'},
      ],
    },
    {
      'event': 'post_shared',
      'description': 'User shares a post',
      'parameters': [
        {'name': 'post_id', 'type': 'String'},
        {'name': 'share_method', 'type': 'String'},
        {'name': 'timestamp', 'type': 'String'},
      ],
    },
    {
      'event': 'post_bookmarked',
      'description': 'User bookmarks a post',
      'parameters': [
        {'name': 'post_id', 'type': 'String'},
        {'name': 'timestamp', 'type': 'String'},
      ],
    },
    {
      'event': 'story_created',
      'description': 'User creates a story',
      'parameters': [
        {'name': 'story_id', 'type': 'String'},
        {'name': 'media_type', 'type': 'String'},
        {'name': 'has_filter', 'type': 'bool'},
        {'name': 'has_text', 'type': 'bool'},
        {'name': 'timestamp', 'type': 'String'},
      ],
    },
    {
      'event': 'story_viewed',
      'description': 'User views a story',
      'parameters': [
        {'name': 'story_id', 'type': 'String'},
        {'name': 'story_author_id', 'type': 'String'},
        {'name': 'view_duration_ms', 'type': 'int'},
        {'name': 'timestamp', 'type': 'String'},
      ],
    },
    {
      'event': 'event_created',
      'description': 'User creates an event',
      'parameters': [
        {'name': 'event_id', 'type': 'String'},
        {'name': 'event_title', 'type': 'String'},
        {'name': 'price', 'type': 'double', 'desc': 'Optional'},
        {'name': 'location', 'type': 'String', 'desc': 'Optional'},
        {'name': 'timestamp', 'type': 'String'},
      ],
    },
    {
      'event': 'event_rsvp',
      'description': 'User RSVPs to an event',
      'parameters': [
        {'name': 'event_id', 'type': 'String'},
        {'name': 'event_title', 'type': 'String'},
        {'name': 'timestamp', 'type': 'String'},
      ],
    },
    {
      'event': 'product_listed',
      'description': 'User lists a product in the marketplace',
      'parameters': [
        {'name': 'product_id', 'type': 'String'},
        {'name': 'product_name', 'type': 'String'},
        {'name': 'price', 'type': 'double'},
        {'name': 'category', 'type': 'String', 'desc': 'Optional'},
        {'name': 'timestamp', 'type': 'String'},
      ],
    },
    {
      'event': 'product_purchased',
      'description': 'User purchases a product',
      'parameters': [
        {'name': 'product_id', 'type': 'String'},
        {'name': 'product_name', 'type': 'String'},
        {'name': 'price', 'type': 'double'},
        {'name': 'seller_id', 'type': 'String', 'desc': 'Optional'},
        {'name': 'timestamp', 'type': 'String'},
      ],
    },
    {
      'event': 'profile_viewed',
      'description': 'User views a profile',
      'parameters': [
        {'name': 'profile_id', 'type': 'String'},
        {'name': 'viewer_id', 'type': 'String'},
        {'name': 'timestamp', 'type': 'String'},
      ],
    },
    {
      'event': 'user_followed',
      'description': 'User follows another user',
      'parameters': [
        {'name': 'follower_id', 'type': 'String'},
        {'name': 'followed_id', 'type': 'String'},
        {'name': 'timestamp', 'type': 'String'},
      ],
    },
    {
      'event': 'user_unfollowed',
      'description': 'User unfollows another user',
      'parameters': [
        {'name': 'follower_id', 'type': 'String'},
        {'name': 'followed_id', 'type': 'String'},
        {'name': 'timestamp', 'type': 'String'},
      ],
    },
    {
      'event': 'message_sent',
      'description': 'User sends a message',
      'parameters': [
        {'name': 'chat_id', 'type': 'String'},
        {'name': 'recipient_id', 'type': 'String'},
        {'name': 'message_type', 'type': 'String'},
        {'name': 'timestamp', 'type': 'String'},
      ],
    },
    {
      'event': 'search_performed',
      'description': 'User performs a search',
      'parameters': [
        {'name': 'search_term', 'type': 'String'},
        {'name': 'search_type', 'type': 'String'},
        {'name': 'result_count', 'type': 'int', 'desc': 'Optional'},
        {'name': 'timestamp', 'type': 'String'},
      ],
    },
    {
      'event': 'app_error',
      'description': 'App error is logged',
      'parameters': [
        {'name': 'error_type', 'type': 'String'},
        {'name': 'error_message', 'type': 'String'},
        {'name': 'stack_trace', 'type': 'String', 'desc': 'Optional'},
        {'name': 'timestamp', 'type': 'String'},
      ],
    },
    {
      'event': 'performance_metric',
      'description': 'Performance metric is logged',
      'parameters': [
        {'name': 'metric_name', 'type': 'String'},
        {'name': 'value', 'type': 'double'},
        {'name': 'unit', 'type': 'String'},
        {'name': 'timestamp', 'type': 'String'},
      ],
    },
    {
      'event': 'user_engagement',
      'description': 'User engagement event',
      'parameters': [
        {'name': 'engagement_type', 'type': 'String'},
        {'name': 'duration_ms', 'type': 'int', 'desc': 'Optional'},
        {'name': 'timestamp', 'type': 'String'},
      ],
    },
    {
      'event': 'feature_used',
      'description': 'Feature usage event',
      'parameters': [
        {'name': 'feature_name', 'type': 'String'},
        {'name': 'timestamp', 'type': 'String'},
      ],
    },
    {
      'event': 'conversion',
      'description': 'Conversion event (e.g. upgrade, purchase)',
      'parameters': [
        {'name': 'conversion_type', 'type': 'String'},
        {'name': 'value', 'type': 'double', 'desc': 'Optional'},
        {'name': 'currency', 'type': 'String', 'desc': 'Optional'},
        {'name': 'timestamp', 'type': 'String'},
      ],
    },
    {
      'event': 'app_lifecycle',
      'description': 'App lifecycle event',
      'parameters': [
        {'name': 'lifecycle_event', 'type': 'String'},
        {'name': 'session_duration_ms', 'type': 'int', 'desc': 'Optional'},
        {'name': 'timestamp', 'type': 'String'},
      ],
    },
    // Billionaire-specific events
    {
      'event': 'vip_membership_upgrade',
      'description': 'User upgrades to VIP membership',
      'parameters': [
        {'name': 'user_id', 'type': 'String'},
        {'name': 'membership_type', 'type': 'String'},
        {'name': 'price', 'type': 'double'},
        {'name': 'currency', 'type': 'String'},
        {'name': 'timestamp', 'type': 'String'},
      ],
    },
    {
      'event': 'investment_made',
      'description': 'User makes an investment',
      'parameters': [
        {'name': 'user_id', 'type': 'String'},
        {'name': 'investment_type', 'type': 'String'},
        {'name': 'amount', 'type': 'double'},
        {'name': 'currency', 'type': 'String'},
        {'name': 'investment_target', 'type': 'String', 'desc': 'Optional'},
        {'name': 'timestamp', 'type': 'String'},
      ],
    },
    {
      'event': 'exclusive_event_attendance',
      'description': 'User attends an exclusive event',
      'parameters': [
        {'name': 'user_id', 'type': 'String'},
        {'name': 'event_id', 'type': 'String'},
        {'name': 'event_title', 'type': 'String'},
        {'name': 'ticket_price', 'type': 'double'},
        {'name': 'timestamp', 'type': 'String'},
      ],
    },
    {
      'event': 'luxury_purchase',
      'description': 'User makes a luxury purchase (villa, island, etc.)',
      'parameters': [
        {'name': 'user_id', 'type': 'String'},
        {'name': 'item_type', 'type': 'String'},
        {'name': 'price', 'type': 'double'},
        {'name': 'brand', 'type': 'String', 'desc': 'Optional'},
        {'name': 'category', 'type': 'String', 'desc': 'Optional'},
        {'name': 'timestamp', 'type': 'String'},
      ],
    },
    {
      'event': 'networking_connection',
      'description': 'User makes a networking connection',
      'parameters': [
        {'name': 'user_id', 'type': 'String'},
        {'name': 'connection_id', 'type': 'String'},
        {'name': 'connection_type', 'type': 'String'},
        {'name': 'industry', 'type': 'String', 'desc': 'Optional'},
        {'name': 'timestamp', 'type': 'String'},
      ],
    },
    {
      'event': 'business_deal',
      'description': 'User completes a business deal',
      'parameters': [
        {'name': 'user_id', 'type': 'String'},
        {'name': 'deal_type', 'type': 'String'},
        {'name': 'deal_value', 'type': 'double'},
        {'name': 'partner_id', 'type': 'String', 'desc': 'Optional'},
        {'name': 'industry', 'type': 'String', 'desc': 'Optional'},
        {'name': 'timestamp', 'type': 'String'},
      ],
    },
    {
      'event': 'philanthropy_donation',
      'description': 'User donates to philanthropy',
      'parameters': [
        {'name': 'user_id', 'type': 'String'},
        {'name': 'cause', 'type': 'String'},
        {'name': 'amount', 'type': 'double'},
        {'name': 'organization', 'type': 'String', 'desc': 'Optional'},
        {'name': 'timestamp', 'type': 'String'},
      ],
    },
    {
      'event': 'exclusive_content_access',
      'description': 'User accesses exclusive content',
      'parameters': [
        {'name': 'user_id', 'type': 'String'},
        {'name': 'content_type', 'type': 'String'},
        {'name': 'content_id', 'type': 'String'},
        {'name': 'access_level', 'type': 'String', 'desc': 'Optional'},
        {'name': 'timestamp', 'type': 'String'},
      ],
    },
    {
      'event': 'private_jet_booking',
      'description': 'User books a private jet',
      'parameters': [
        {'name': 'user_id', 'type': 'String'},
        {'name': 'destination', 'type': 'String'},
        {'name': 'cost', 'type': 'double'},
        {'name': 'aircraft_type', 'type': 'String', 'desc': 'Optional'},
        {'name': 'timestamp', 'type': 'String'},
      ],
    },
    {
      'event': 'yacht_charter',
      'description': 'User charters a yacht',
      'parameters': [
        {'name': 'user_id', 'type': 'String'},
        {'name': 'destination', 'type': 'String'},
        {'name': 'cost', 'type': 'double'},
        {'name': 'duration_days', 'type': 'int', 'desc': 'Optional'},
        {'name': 'timestamp', 'type': 'String'},
      ],
    },
    // ... (add all other billionaire-specific events as in AnalyticsService)
  ];
}
