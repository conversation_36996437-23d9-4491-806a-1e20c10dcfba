import 'package:flutter/foundation.dart';

/// Rate limiting service to prevent abuse and ensure fair usage
class RateLimitingService {
  static final RateLimitingService _instance = RateLimitingService._internal();
  factory RateLimitingService() => _instance;
  RateLimitingService._internal();

  // Storage for rate limiting data
  final Map<String, List<DateTime>> _requestHistory = {};
  final Map<String, DateTime> _lastCleanup = {};

  // Rate limiting configurations
  static const Map<String, RateLimitConfig> _rateLimits = {
    'login': RateLimitConfig(maxRequests: 5, windowMinutes: 15),
    'register': RateLimitConfig(maxRequests: 3, windowMinutes: 60),
    'post_creation': RateLimitConfig(maxRequests: 10, windowMinutes: 60),
    'story_creation': RateLimitConfig(maxRequests: 20, windowMinutes: 60),
    'comment': RateLimitConfig(maxRequests: 30, windowMinutes: 60),
    'like': RateLimitConfig(maxRequests: 100, windowMinutes: 60),
    'follow': RateLimitConfig(maxRequests: 50, windowMinutes: 60),
    'message': RateLimitConfig(maxRequests: 100, windowMinutes: 60),
    'search': RateLimitConfig(maxRequests: 50, windowMinutes: 60),
    'profile_update': RateLimitConfig(maxRequests: 5, windowMinutes: 60),
    'password_reset': RateLimitConfig(maxRequests: 3, windowMinutes: 60),
    'email_verification': RateLimitConfig(maxRequests: 5, windowMinutes: 60),
    'api_general': RateLimitConfig(maxRequests: 1000, windowMinutes: 60),
  };

  /// Check if a request is allowed under rate limiting
  RateLimitResult checkRateLimit(String action, String identifier) {
    final config = _rateLimits[action];
    if (config == null) {
      debugPrint('⚠️ No rate limit config found for action: $action');
      return RateLimitResult(
        allowed: true,
        remainingRequests: 999,
        resetTime: DateTime.now().add(const Duration(hours: 1)),
      );
    }

    final key = '${action}_$identifier';
    final now = DateTime.now();
    final windowStart = now.subtract(Duration(minutes: config.windowMinutes));

    // Clean up old entries periodically
    _cleanupOldEntries(key, windowStart);

    // Initialize request history if not exists
    _requestHistory[key] ??= [];

    // Remove requests outside the current window
    _requestHistory[key]!.removeWhere((time) => time.isBefore(windowStart));

    final currentRequests = _requestHistory[key]!.length;
    final remainingRequests = config.maxRequests - currentRequests;

    if (currentRequests >= config.maxRequests) {
      // Rate limit exceeded
      final oldestRequest = _requestHistory[key]!.first;
      final resetTime = oldestRequest.add(Duration(minutes: config.windowMinutes));
      
      debugPrint('🚨 Rate limit exceeded for $action by $identifier');
      debugPrint('   Current requests: $currentRequests/${config.maxRequests}');
      debugPrint('   Reset time: $resetTime');

      return RateLimitResult(
        allowed: false,
        remainingRequests: 0,
        resetTime: resetTime,
        message: 'Rate limit exceeded. Try again in ${_formatDuration(resetTime.difference(now))}',
      );
    }

    // Request is allowed, record it
    _requestHistory[key]!.add(now);

    debugPrint('✅ Rate limit check passed for $action by $identifier');
    debugPrint('   Requests used: ${currentRequests + 1}/${config.maxRequests}');

    return RateLimitResult(
      allowed: true,
      remainingRequests: remainingRequests - 1,
      resetTime: now.add(Duration(minutes: config.windowMinutes)),
    );
  }

  /// Get current rate limit status for an action and identifier
  RateLimitStatus getRateLimitStatus(String action, String identifier) {
    final config = _rateLimits[action];
    if (config == null) {
      return RateLimitStatus(
        maxRequests: 999,
        currentRequests: 0,
        remainingRequests: 999,
        resetTime: DateTime.now().add(const Duration(hours: 1)),
      );
    }

    final key = '${action}_$identifier';
    final now = DateTime.now();
    final windowStart = now.subtract(Duration(minutes: config.windowMinutes));

    // Clean up old entries
    _requestHistory[key]?.removeWhere((time) => time.isBefore(windowStart));

    final currentRequests = _requestHistory[key]?.length ?? 0;
    final remainingRequests = config.maxRequests - currentRequests;

    DateTime resetTime;
    if (_requestHistory[key]?.isNotEmpty == true) {
      final oldestRequest = _requestHistory[key]!.first;
      resetTime = oldestRequest.add(Duration(minutes: config.windowMinutes));
    } else {
      resetTime = now.add(Duration(minutes: config.windowMinutes));
    }

    return RateLimitStatus(
      maxRequests: config.maxRequests,
      currentRequests: currentRequests,
      remainingRequests: remainingRequests,
      resetTime: resetTime,
    );
  }

  /// Clear rate limit history for a specific action and identifier
  void clearRateLimit(String action, String identifier) {
    final key = '${action}_$identifier';
    _requestHistory.remove(key);
    debugPrint('🧹 Cleared rate limit history for $key');
  }

  /// Clear all rate limit history (use with caution)
  void clearAllRateLimits() {
    _requestHistory.clear();
    _lastCleanup.clear();
    debugPrint('🧹 Cleared all rate limit history');
  }

  /// Get all available rate limit configurations
  Map<String, RateLimitConfig> getRateLimitConfigs() {
    return Map.from(_rateLimits);
  }

  /// Check if an action has rate limiting configured
  bool hasRateLimit(String action) {
    return _rateLimits.containsKey(action);
  }

  /// Private helper methods
  void _cleanupOldEntries(String key, DateTime windowStart) {
    final lastCleanup = _lastCleanup[key];
    final now = DateTime.now();

    // Only cleanup every 5 minutes to avoid excessive processing
    if (lastCleanup == null || now.difference(lastCleanup).inMinutes >= 5) {
      _requestHistory[key]?.removeWhere((time) => time.isBefore(windowStart));
      _lastCleanup[key] = now;
    }
  }

  String _formatDuration(Duration duration) {
    if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes % 60}m';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes}m';
    } else {
      return '${duration.inSeconds}s';
    }
  }

  /// Get rate limiting statistics for monitoring
  Map<String, dynamic> getRateLimitingStats() {
    final stats = <String, dynamic>{};
    
    for (final entry in _requestHistory.entries) {
      final parts = entry.key.split('_');
      if (parts.length >= 2) {
        final action = parts[0];
        stats[action] = (stats[action] ?? 0) + entry.value.length;
      }
    }

    return {
      'total_tracked_keys': _requestHistory.length,
      'requests_by_action': stats,
      'last_cleanup_times': _lastCleanup.length,
      'memory_usage_estimate': _requestHistory.values
          .map((list) => list.length)
          .fold(0, (sum, count) => sum + count),
    };
  }
}

/// Rate limit configuration
class RateLimitConfig {
  final int maxRequests;
  final int windowMinutes;

  const RateLimitConfig({
    required this.maxRequests,
    required this.windowMinutes,
  });

  @override
  String toString() => 'RateLimitConfig(maxRequests: $maxRequests, windowMinutes: $windowMinutes)';
}

/// Rate limit check result
class RateLimitResult {
  final bool allowed;
  final int remainingRequests;
  final DateTime resetTime;
  final String? message;

  RateLimitResult({
    required this.allowed,
    required this.remainingRequests,
    required this.resetTime,
    this.message,
  });

  @override
  String toString() => 'RateLimitResult(allowed: $allowed, remaining: $remainingRequests, resetTime: $resetTime)';
}

/// Rate limit status
class RateLimitStatus {
  final int maxRequests;
  final int currentRequests;
  final int remainingRequests;
  final DateTime resetTime;

  RateLimitStatus({
    required this.maxRequests,
    required this.currentRequests,
    required this.remainingRequests,
    required this.resetTime,
  });

  double get usagePercentage => currentRequests / maxRequests;
  bool get isNearLimit => usagePercentage > 0.8;

  @override
  String toString() => 'RateLimitStatus(current: $currentRequests/$maxRequests, remaining: $remainingRequests)';
}
