import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/core/services/content_moderation_service.dart';

final analyticsServiceProvider = Provider<AnalyticsService>((ref) {
  return getIt<AnalyticsService>();
});

class AnalyticsService {
  static final AnalyticsService _instance = AnalyticsService._internal();
  factory AnalyticsService() => _instance;
  AnalyticsService._internal();

  final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;

  // Initialize analytics
  Future<void> initialize() async {
    await _analytics.setAnalyticsCollectionEnabled(true);
    await _analytics.setSessionTimeoutDuration(const Duration(minutes: 30));
  }

  // Set user properties
  Future<void> setUserProperties({
    required String userId,
    String? userType,
    bool? isVerified,
    bool? isBillionaire,
    String? location,
  }) async {
    await _analytics.setUserId(id: userId);
    await _analytics.setUserProperty(name: 'user_type', value: userType);
    await _analytics.setUserProperty(
      name: 'is_verified',
      value: isVerified?.toString(),
    );
    await _analytics.setUserProperty(
      name: 'is_billionaire',
      value: isBillionaire?.toString(),
    );
    await _analytics.setUserProperty(name: 'location', value: location);
  }

  // Track app events
  Future<void> logAppOpen() async {
    await _analytics.logAppOpen();
  }

  Future<void> logLogin({required String method}) async {
    await _analytics.logLogin(loginMethod: method);
  }

  Future<void> logSignUp({required String method}) async {
    await _analytics.logSignUp(signUpMethod: method);
  }

  // Track post events
  Future<void> logPostCreated({
    required String postId,
    String? contentType,
    bool hasMedia = false,
    String? location,
  }) async {
    final parameters = <String, Object>{
      'post_id': postId,
      'content_type': contentType ?? 'text',
      'has_media': hasMedia.toString(),
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (location != null) {
      parameters['location'] = location;
    }

    await _analytics.logEvent(name: 'post_created', parameters: parameters);
  }

  Future<void> logPostLiked({
    required String postId,
    required String postAuthorId,
  }) async {
    await _analytics.logEvent(
      name: 'post_liked',
      parameters: {
        'post_id': postId,
        'post_author_id': postAuthorId,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> logPostShared({
    required String postId,
    required String shareMethod,
  }) async {
    await _analytics.logEvent(
      name: 'post_shared',
      parameters: {
        'post_id': postId,
        'share_method': shareMethod,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> logPostRemixed({
    required String originalPostId,
    required String remixPostId,
    required String originalAuthorId,
  }) async {
    await _analytics.logEvent(
      name: 'post_remixed',
      parameters: {
        'original_post_id': originalPostId,
        'remix_post_id': remixPostId,
        'original_author_id': originalAuthorId,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> logPostBookmarked({required String postId}) async {
    await _analytics.logEvent(
      name: 'post_bookmarked',
      parameters: {
        'post_id': postId,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> logPostReported({
    required String postId,
    required String reason,
    String? reportedBy,
  }) async {
    final parameters = <String, Object>{
      'post_id': postId,
      'reason': reason,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (reportedBy != null) {
      parameters['reported_by'] = reportedBy;
    }

    await _analytics.logEvent(name: 'post_reported', parameters: parameters);
  }

  // Content moderation analytics
  Future<void> logContentFlagged({
    required String contentType,
    required String pattern,
    required ContentSeverity severity,
  }) async {
    await _analytics.logEvent(
      name: 'content_flagged',
      parameters: {
        'content_type': contentType,
        'pattern': pattern,
        'severity': severity.name,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> logRateLimitExceeded({
    required String limitType,
    required String userId,
  }) async {
    await _analytics.logEvent(
      name: 'rate_limit_exceeded',
      parameters: {
        'limit_type': limitType,
        'user_id': userId,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  // Story interaction analytics
  Future<void> logStoryReaction({
    required String storyId,
    required String reactionType,
    required String userId,
  }) async {
    await _analytics.logEvent(
      name: 'story_reaction',
      parameters: {
        'story_id': storyId,
        'reaction_type': reactionType,
        'user_id': userId,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> logStoryReply({
    required String storyId,
    required bool hasMedia,
    required String userId,
  }) async {
    await _analytics.logEvent(
      name: 'story_reply',
      parameters: {
        'story_id': storyId,
        'has_media': hasMedia.toString(),
        'user_id': userId,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> logStoryPrivacyChanged({
    required String storyId,
    required String privacy,
    required String userId,
  }) async {
    await _analytics.logEvent(
      name: 'story_privacy_changed',
      parameters: {
        'story_id': storyId,
        'privacy': privacy,
        'user_id': userId,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> logStoryReported({
    required String storyId,
    required String reason,
    required String reportedBy,
  }) async {
    await _analytics.logEvent(
      name: 'story_reported',
      parameters: {
        'story_id': storyId,
        'reason': reason,
        'reported_by': reportedBy,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  // Track story events
  Future<void> logStoryCreated({
    required String storyId,
    String? mediaType,
    bool hasFilter = false,
    bool hasText = false,
  }) async {
    final parameters = <String, Object>{
      'story_id': storyId,
      'media_type': mediaType ?? 'image',
      'has_filter': hasFilter.toString(),
      'has_text': hasText.toString(),
      'timestamp': DateTime.now().toIso8601String(),
    };

    await _analytics.logEvent(name: 'story_created', parameters: parameters);
  }

  Future<void> logStoryViewed({
    required String storyId,
    required String storyAuthorId,
    Duration viewDuration = Duration.zero,
  }) async {
    await _analytics.logEvent(
      name: 'story_viewed',
      parameters: {
        'story_id': storyId,
        'story_author_id': storyAuthorId,
        'view_duration_ms': viewDuration.inMilliseconds,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  // Track event events
  Future<void> logEventCreated({
    required String eventId,
    required String eventTitle,
    double? price,
    String? location,
  }) async {
    final parameters = <String, Object>{
      'event_id': eventId,
      'event_title': eventTitle,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (price != null) {
      parameters['price'] = price;
    }

    if (location != null) {
      parameters['location'] = location;
    }

    await _analytics.logEvent(name: 'event_created', parameters: parameters);
  }

  Future<void> logEventRSVP({
    required String eventId,
    required String eventTitle,
  }) async {
    await _analytics.logEvent(
      name: 'event_rsvp',
      parameters: {
        'event_id': eventId,
        'event_title': eventTitle,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  // Track marketplace events
  Future<void> logProductListed({
    required String productId,
    required String productName,
    required double price,
    String? category,
  }) async {
    final parameters = <String, Object>{
      'product_id': productId,
      'product_name': productName,
      'price': price,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (category != null) {
      parameters['category'] = category;
    }

    await _analytics.logEvent(name: 'product_listed', parameters: parameters);
  }

  Future<void> logProductPurchased({
    required String productId,
    required String productName,
    required double price,
    String? sellerId,
  }) async {
    final parameters = <String, Object>{
      'product_id': productId,
      'product_name': productName,
      'price': price,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (sellerId != null) {
      parameters['seller_id'] = sellerId;
    }

    await _analytics.logEvent(
      name: 'product_purchased',
      parameters: parameters,
    );
  }

  // Track profile events
  Future<void> logProfileViewed({
    required String profileId,
    required String viewerId,
  }) async {
    await _analytics.logEvent(
      name: 'profile_viewed',
      parameters: {
        'profile_id': profileId,
        'viewer_id': viewerId,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> logFollow({
    required String followerId,
    required String followedId,
  }) async {
    await _analytics.logEvent(
      name: 'user_followed',
      parameters: {
        'follower_id': followerId,
        'followed_id': followedId,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> logUnfollow({
    required String followerId,
    required String followedId,
  }) async {
    await _analytics.logEvent(
      name: 'user_unfollowed',
      parameters: {
        'follower_id': followerId,
        'followed_id': followedId,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  // Track messaging events
  Future<void> logMessageSent({
    required String chatId,
    required String recipientId,
    String? messageType,
  }) async {
    final parameters = <String, Object>{
      'chat_id': chatId,
      'recipient_id': recipientId,
      'message_type': messageType ?? 'text',
      'timestamp': DateTime.now().toIso8601String(),
    };

    await _analytics.logEvent(name: 'message_sent', parameters: parameters);
  }

  // Track search events
  Future<void> logSearch({
    required String searchTerm,
    String? searchType,
    int? resultCount,
  }) async {
    final parameters = <String, Object>{
      'search_term': searchTerm,
      'search_type': searchType ?? 'general',
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (resultCount != null) {
      parameters['result_count'] = resultCount;
    }

    await _analytics.logEvent(name: 'search_performed', parameters: parameters);
  }

  // Track navigation events
  Future<void> logScreenView({
    required String screenName,
    String? screenClass,
  }) async {
    await _analytics.logScreenView(
      screenName: screenName,
      screenClass: screenClass,
    );
  }

  // Track error events
  Future<void> logError({
    required String errorType,
    required String errorMessage,
    String? stackTrace,
  }) async {
    final parameters = <String, Object>{
      'error_type': errorType,
      'error_message': errorMessage,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (stackTrace != null) {
      parameters['stack_trace'] = stackTrace;
    }

    await _analytics.logEvent(name: 'app_error', parameters: parameters);
  }

  // Track performance events
  Future<void> logPerformance({
    required String metricName,
    required double value,
    String? unit,
  }) async {
    final parameters = <String, Object>{
      'metric_name': metricName,
      'value': value,
      'unit': unit ?? 'ms',
      'timestamp': DateTime.now().toIso8601String(),
    };

    await _analytics.logEvent(
      name: 'performance_metric',
      parameters: parameters,
    );
  }

  // Track engagement events
  Future<void> logEngagement({
    required String engagementType,
    Duration? duration,
    Map<String, Object>? additionalParams,
  }) async {
    final parameters = <String, Object>{
      'engagement_type': engagementType,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (duration != null) {
      parameters['duration_ms'] = duration.inMilliseconds;
    }

    if (additionalParams != null) {
      parameters.addAll(additionalParams);
    }

    await _analytics.logEvent(name: 'user_engagement', parameters: parameters);
  }

  // Track feature usage
  Future<void> logFeatureUsage({
    required String featureName,
    Map<String, Object>? parameters,
  }) async {
    final eventParams = <String, Object>{
      'feature_name': featureName,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (parameters != null) {
      eventParams.addAll(parameters);
    }

    await _analytics.logEvent(name: 'feature_used', parameters: eventParams);
  }

  // Track conversion events
  Future<void> logConversion({
    required String conversionType,
    double? value,
    String? currency,
  }) async {
    final parameters = <String, Object>{
      'conversion_type': conversionType,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (value != null) {
      parameters['value'] = value;
    }

    if (currency != null) {
      parameters['currency'] = currency;
    }

    await _analytics.logEvent(name: 'conversion', parameters: parameters);
  }

  // Track app lifecycle events
  Future<void> logAppLifecycle({
    required String lifecycleEvent,
    Duration? sessionDuration,
  }) async {
    final parameters = <String, Object>{
      'lifecycle_event': lifecycleEvent,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (sessionDuration != null) {
      parameters['session_duration_ms'] = sessionDuration.inMilliseconds;
    }

    await _analytics.logEvent(name: 'app_lifecycle', parameters: parameters);
  }

  // ===== BILLIONAIRE-SPECIFIC EVENTS =====

  // Track VIP membership events
  Future<void> logVIPMembershipUpgrade({
    required String userId,
    required String membershipType,
    required double price,
    String? currency,
  }) async {
    final parameters = <String, Object>{
      'user_id': userId,
      'membership_type': membershipType,
      'price': price,
      'currency': currency ?? 'USD',
      'timestamp': DateTime.now().toIso8601String(),
    };

    await _analytics.logEvent(
      name: 'vip_membership_upgrade',
      parameters: parameters,
    );
  }

  // Track investment events
  Future<void> logInvestmentMade({
    required String userId,
    required String investmentType,
    required double amount,
    String? currency,
    String? investmentTarget,
  }) async {
    final parameters = <String, Object>{
      'user_id': userId,
      'investment_type': investmentType,
      'amount': amount,
      'currency': currency ?? 'USD',
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (investmentTarget != null) {
      parameters['investment_target'] = investmentTarget;
    }

    await _analytics.logEvent(name: 'investment_made', parameters: parameters);
  }

  // Track exclusive event attendance
  Future<void> logExclusiveEventAttendance({
    required String userId,
    required String eventId,
    required String eventTitle,
    required double ticketPrice,
  }) async {
    final parameters = <String, Object>{
      'user_id': userId,
      'event_id': eventId,
      'event_title': eventTitle,
      'ticket_price': ticketPrice,
      'timestamp': DateTime.now().toIso8601String(),
    };

    await _analytics.logEvent(
      name: 'exclusive_event_attendance',
      parameters: parameters,
    );
  }

  // Track luxury purchase
  Future<void> logLuxuryPurchase({
    required String userId,
    required String itemType,
    required double price,
    String? brand,
    String? category,
  }) async {
    final parameters = <String, Object>{
      'user_id': userId,
      'item_type': itemType,
      'price': price,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (brand != null) {
      parameters['brand'] = brand;
    }

    if (category != null) {
      parameters['category'] = category;
    }

    await _analytics.logEvent(name: 'luxury_purchase', parameters: parameters);
  }

  // Track networking connection
  Future<void> logNetworkingConnection({
    required String userId,
    required String connectionId,
    required String connectionType,
    String? industry,
  }) async {
    final parameters = <String, Object>{
      'user_id': userId,
      'connection_id': connectionId,
      'connection_type': connectionType,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (industry != null) {
      parameters['industry'] = industry;
    }

    await _analytics.logEvent(
      name: 'networking_connection',
      parameters: parameters,
    );
  }

  // Track business deal
  Future<void> logBusinessDeal({
    required String userId,
    required String dealType,
    required double dealValue,
    String? partnerId,
    String? industry,
  }) async {
    final parameters = <String, Object>{
      'user_id': userId,
      'deal_type': dealType,
      'deal_value': dealValue,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (partnerId != null) {
      parameters['partner_id'] = partnerId;
    }

    if (industry != null) {
      parameters['industry'] = industry;
    }

    await _analytics.logEvent(name: 'business_deal', parameters: parameters);
  }

  // Track philanthropy donation
  Future<void> logPhilanthropyDonation({
    required String userId,
    required String cause,
    required double amount,
    String? organization,
  }) async {
    final parameters = <String, Object>{
      'user_id': userId,
      'cause': cause,
      'amount': amount,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (organization != null) {
      parameters['organization'] = organization;
    }

    await _analytics.logEvent(
      name: 'philanthropy_donation',
      parameters: parameters,
    );
  }

  // Track exclusive content access
  Future<void> logExclusiveContentAccess({
    required String userId,
    required String contentType,
    required String contentId,
    String? accessLevel,
  }) async {
    final parameters = <String, Object>{
      'user_id': userId,
      'content_type': contentType,
      'content_id': contentId,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (accessLevel != null) {
      parameters['access_level'] = accessLevel;
    }

    await _analytics.logEvent(
      name: 'exclusive_content_access',
      parameters: parameters,
    );
  }

  // Track private jet booking
  Future<void> logPrivateJetBooking({
    required String userId,
    required String destination,
    required double cost,
    String? aircraftType,
  }) async {
    final parameters = <String, Object>{
      'user_id': userId,
      'destination': destination,
      'cost': cost,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (aircraftType != null) {
      parameters['aircraft_type'] = aircraftType;
    }

    await _analytics.logEvent(
      name: 'private_jet_booking',
      parameters: parameters,
    );
  }

  // Track yacht charter
  Future<void> logYachtCharter({
    required String userId,
    required String destination,
    required double cost,
    int? durationDays,
  }) async {
    final parameters = <String, Object>{
      'user_id': userId,
      'destination': destination,
      'cost': cost,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (durationDays != null) {
      parameters['duration_days'] = durationDays;
    }

    await _analytics.logEvent(name: 'yacht_charter', parameters: parameters);
  }

  // ===== ADDITIONAL BILLIONAIRE EVENTS =====

  // Track art auction participation
  Future<void> logArtAuctionParticipation({
    required String userId,
    required String auctionId,
    required String artworkName,
    required double bidAmount,
    String? artist,
  }) async {
    final parameters = <String, Object>{
      'user_id': userId,
      'auction_id': auctionId,
      'artwork_name': artworkName,
      'bid_amount': bidAmount,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (artist != null) {
      parameters['artist'] = artist;
    }

    await _analytics.logEvent(
      name: 'art_auction_participation',
      parameters: parameters,
    );
  }

  // Track wine collection purchase
  Future<void> logWineCollectionPurchase({
    required String userId,
    required String wineName,
    required String vintage,
    required double price,
    String? vineyard,
  }) async {
    final parameters = <String, Object>{
      'user_id': userId,
      'wine_name': wineName,
      'vintage': vintage,
      'price': price,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (vineyard != null) {
      parameters['vineyard'] = vineyard;
    }

    await _analytics.logEvent(
      name: 'wine_collection_purchase',
      parameters: parameters,
    );
  }

  // Track watch collection purchase
  Future<void> logWatchCollectionPurchase({
    required String userId,
    required String watchBrand,
    required String watchModel,
    required double price,
    String? limitedEdition,
  }) async {
    final parameters = <String, Object>{
      'user_id': userId,
      'watch_brand': watchBrand,
      'watch_model': watchModel,
      'price': price,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (limitedEdition != null) {
      parameters['limited_edition'] = limitedEdition;
    }

    await _analytics.logEvent(
      name: 'watch_collection_purchase',
      parameters: parameters,
    );
  }

  // Track car collection purchase
  Future<void> logCarCollectionPurchase({
    required String userId,
    required String carBrand,
    required String carModel,
    required double price,
    String? year,
  }) async {
    final parameters = <String, Object>{
      'user_id': userId,
      'car_brand': carBrand,
      'car_model': carModel,
      'price': price,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (year != null) {
      parameters['year'] = year;
    }

    await _analytics.logEvent(
      name: 'car_collection_purchase',
      parameters: parameters,
    );
  }

  // Track exclusive club membership
  Future<void> logExclusiveClubMembership({
    required String userId,
    required String clubName,
    required double membershipFee,
    String? membershipType,
  }) async {
    final parameters = <String, Object>{
      'user_id': userId,
      'club_name': clubName,
      'membership_fee': membershipFee,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (membershipType != null) {
      parameters['membership_type'] = membershipType;
    }

    await _analytics.logEvent(
      name: 'exclusive_club_membership',
      parameters: parameters,
    );
  }

  // Track luxury travel booking
  Future<void> logLuxuryTravelBooking({
    required String userId,
    required String destination,
    required double cost,
    required String travelType,
    String? accommodationType,
  }) async {
    final parameters = <String, Object>{
      'user_id': userId,
      'destination': destination,
      'cost': cost,
      'travel_type': travelType,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (accommodationType != null) {
      parameters['accommodation_type'] = accommodationType;
    }

    await _analytics.logEvent(
      name: 'luxury_travel_booking',
      parameters: parameters,
    );
  }

  // Track exclusive dining experience
  Future<void> logExclusiveDiningExperience({
    required String userId,
    required String restaurantName,
    required double cost,
    required String chefName,
    String? winePairing,
  }) async {
    final parameters = <String, Object>{
      'user_id': userId,
      'restaurant_name': restaurantName,
      'cost': cost,
      'chef_name': chefName,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (winePairing != null) {
      parameters['wine_pairing'] = winePairing;
    }

    await _analytics.logEvent(
      name: 'exclusive_dining_experience',
      parameters: parameters,
    );
  }

  // Track wellness and spa experience
  Future<void> logWellnessSpaExperience({
    required String userId,
    required String spaName,
    required double cost,
    required String treatmentType,
    String? therapistName,
  }) async {
    final parameters = <String, Object>{
      'user_id': userId,
      'spa_name': spaName,
      'cost': cost,
      'treatment_type': treatmentType,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (therapistName != null) {
      parameters['therapist_name'] = therapistName;
    }

    await _analytics.logEvent(
      name: 'wellness_spa_experience',
      parameters: parameters,
    );
  }

  // Track exclusive entertainment booking
  Future<void> logExclusiveEntertainmentBooking({
    required String userId,
    required String eventName,
    required double cost,
    required String eventType,
    String? performerName,
  }) async {
    final parameters = <String, Object>{
      'user_id': userId,
      'event_name': eventName,
      'cost': cost,
      'event_type': eventType,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (performerName != null) {
      parameters['performer_name'] = performerName;
    }

    await _analytics.logEvent(
      name: 'exclusive_entertainment_booking',
      parameters: parameters,
    );
  }

  // Track luxury service booking
  Future<void> logLuxuryServiceBooking({
    required String userId,
    required String serviceType,
    required double cost,
    required String providerName,
    String? serviceDetails,
  }) async {
    final parameters = <String, Object>{
      'user_id': userId,
      'service_type': serviceType,
      'cost': cost,
      'provider_name': providerName,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (serviceDetails != null) {
      parameters['service_details'] = serviceDetails;
    }

    await _analytics.logEvent(
      name: 'luxury_service_booking',
      parameters: parameters,
    );
  }

  // ===== ANALYTICS VALIDATION METHODS =====

  // Validate event parameters
  bool validateEventParameters(Map<String, Object> parameters) {
    // Check parameter count (max 25 for Firebase Analytics)
    if (parameters.length > 25) {
      // Use debugPrint instead of print for production safety
      debugPrint('Warning: Event has more than 25 parameters');
      return false;
    }

    // Check parameter names (should be snake_case)
    for (final key in parameters.keys) {
      if (!RegExp(r'^[a-z][a-z0-9_]*$').hasMatch(key)) {
        debugPrint('Warning: Parameter name "$key" should be snake_case');
        return false;
      }
    }

    return true;
  }

  // Safe event logging with validation
  Future<void> logEventSafely({
    required String eventName,
    required Map<String, Object> parameters,
  }) async {
    try {
      if (validateEventParameters(parameters)) {
        await _analytics.logEvent(name: eventName, parameters: parameters);
      } else {
        // Log with limited parameters if validation fails
        final limitedParams = <String, Object>{};
        int count = 0;
        for (final entry in parameters.entries) {
          if (count >= 25) break;
          limitedParams[entry.key] = entry.value;
          count++;
        }
        await _analytics.logEvent(name: eventName, parameters: limitedParams);
      }
    } catch (e) {
      debugPrint('Error logging analytics event: $e');
      // Don't throw - analytics errors shouldn't break the app
    }
  }

  // Get analytics instance for direct access
  FirebaseAnalytics get analytics => _analytics;
}
