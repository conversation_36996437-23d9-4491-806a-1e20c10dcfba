import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:billionaires_social/core/services/universal_user_role_service.dart';

/// Universal service for social interactions (follow, like, comment, etc.)
/// Eliminates hardcoded social interaction logic throughout the app
class UniversalSocialInteractionService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Universal follow action - works for any user relationship
  static Future<bool> followUser(String targetUserId) async {
    final currentUserId = UniversalUserRoleService.getCurrentUserId();
    if (currentUserId == null) {
      throw Exception('User must be authenticated to follow others');
    }

    if (currentUserId == targetUserId) {
      throw Exception('Cannot follow yourself');
    }

    try {
      // Check if already following
      final relationship = await UniversalUserRoleService.getUserRelationship(
        targetUserId,
      );
      if (relationship == UserRelationship.following ||
          relationship == UserRelationship.closeFriend) {
        debugPrint('⚠️ Already following user: $targetUserId');
        return false;
      }

      // Check if user is blocked
      if (relationship == UserRelationship.blocked) {
        throw Exception('Cannot follow blocked user');
      }

      final batch = _firestore.batch();

      // Add to current user's following collection
      final followingRef = _firestore
          .collection('users')
          .doc(currentUserId)
          .collection('following')
          .doc(targetUserId);

      batch.set(followingRef, {
        'userId': targetUserId,
        'followedAt': FieldValue.serverTimestamp(),
        'isCloseFriend': false,
        'notificationsEnabled': true,
      });

      // Add to target user's followers collection
      final followerRef = _firestore
          .collection('users')
          .doc(targetUserId)
          .collection('followers')
          .doc(currentUserId);

      batch.set(followerRef, {
        'userId': currentUserId,
        'followedAt': FieldValue.serverTimestamp(),
      });

      // Update follower/following counts
      final currentUserRef = _firestore.collection('users').doc(currentUserId);
      batch.update(currentUserRef, {
        'followingCount': FieldValue.increment(1),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      final targetUserRef = _firestore.collection('users').doc(targetUserId);
      batch.update(targetUserRef, {
        'followerCount': FieldValue.increment(1),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      await batch.commit();

      // Create follow notification
      await _createFollowNotification(currentUserId, targetUserId);

      debugPrint('✅ Successfully followed user: $targetUserId');
      return true;
    } catch (e) {
      debugPrint('❌ Error following user: $e');
      rethrow;
    }
  }

  /// Universal unfollow action - works for any user relationship
  static Future<bool> unfollowUser(String targetUserId) async {
    final currentUserId = UniversalUserRoleService.getCurrentUserId();
    if (currentUserId == null) {
      throw Exception('User must be authenticated to unfollow others');
    }

    try {
      // Check if currently following
      final relationship = await UniversalUserRoleService.getUserRelationship(
        targetUserId,
      );
      if (relationship != UserRelationship.following &&
          relationship != UserRelationship.closeFriend) {
        debugPrint('⚠️ Not following user: $targetUserId');
        return false;
      }

      final batch = _firestore.batch();

      // Remove from current user's following collection
      final followingRef = _firestore
          .collection('users')
          .doc(currentUserId)
          .collection('following')
          .doc(targetUserId);

      batch.delete(followingRef);

      // Remove from target user's followers collection
      final followerRef = _firestore
          .collection('users')
          .doc(targetUserId)
          .collection('followers')
          .doc(currentUserId);

      batch.delete(followerRef);

      // Update follower/following counts
      final currentUserRef = _firestore.collection('users').doc(currentUserId);
      batch.update(currentUserRef, {
        'followingCount': FieldValue.increment(-1),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      final targetUserRef = _firestore.collection('users').doc(targetUserId);
      batch.update(targetUserRef, {
        'followerCount': FieldValue.increment(-1),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      await batch.commit();

      debugPrint('✅ Successfully unfollowed user: $targetUserId');
      return true;
    } catch (e) {
      debugPrint('❌ Error unfollowing user: $e');
      rethrow;
    }
  }

  /// Universal like post action
  static Future<bool> likePost(String postId, String postOwnerId) async {
    final currentUserId = UniversalUserRoleService.getCurrentUserId();
    if (currentUserId == null) {
      throw Exception('User must be authenticated to like posts');
    }

    try {
      // Check if already liked
      final likeDoc = await _firestore
          .collection('posts')
          .doc(postId)
          .collection('likes')
          .doc(currentUserId)
          .get();

      if (likeDoc.exists) {
        debugPrint('⚠️ Post already liked: $postId');
        return false;
      }

      final batch = _firestore.batch();

      // Add like document
      final likeRef = _firestore
          .collection('posts')
          .doc(postId)
          .collection('likes')
          .doc(currentUserId);

      batch.set(likeRef, {
        'userId': currentUserId,
        'likedAt': FieldValue.serverTimestamp(),
      });

      // Update post like count
      final postRef = _firestore.collection('posts').doc(postId);
      batch.update(postRef, {
        'likeCount': FieldValue.increment(1),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      await batch.commit();

      // Create like notification (if not own post)
      if (currentUserId != postOwnerId) {
        await _createLikeNotification(currentUserId, postOwnerId, postId);
      }

      debugPrint('✅ Successfully liked post: $postId');
      return true;
    } catch (e) {
      debugPrint('❌ Error liking post: $e');
      rethrow;
    }
  }

  /// Universal unlike post action
  static Future<bool> unlikePost(String postId) async {
    final currentUserId = UniversalUserRoleService.getCurrentUserId();
    if (currentUserId == null) {
      throw Exception('User must be authenticated to unlike posts');
    }

    try {
      // Check if currently liked
      final likeDoc = await _firestore
          .collection('posts')
          .doc(postId)
          .collection('likes')
          .doc(currentUserId)
          .get();

      if (!likeDoc.exists) {
        debugPrint('⚠️ Post not liked: $postId');
        return false;
      }

      final batch = _firestore.batch();

      // Remove like document
      final likeRef = _firestore
          .collection('posts')
          .doc(postId)
          .collection('likes')
          .doc(currentUserId);

      batch.delete(likeRef);

      // Update post like count
      final postRef = _firestore.collection('posts').doc(postId);
      batch.update(postRef, {
        'likeCount': FieldValue.increment(-1),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      await batch.commit();

      debugPrint('✅ Successfully unliked post: $postId');
      return true;
    } catch (e) {
      debugPrint('❌ Error unliking post: $e');
      rethrow;
    }
  }

  /// Universal block user action
  static Future<bool> blockUser(String targetUserId) async {
    final currentUserId = UniversalUserRoleService.getCurrentUserId();
    if (currentUserId == null) {
      throw Exception('User must be authenticated to block others');
    }

    if (currentUserId == targetUserId) {
      throw Exception('Cannot block yourself');
    }

    try {
      final batch = _firestore.batch();

      // Add to blocked users collection
      final blockedRef = _firestore
          .collection('users')
          .doc(currentUserId)
          .collection('blocked')
          .doc(targetUserId);

      batch.set(blockedRef, {
        'userId': targetUserId,
        'blockedAt': FieldValue.serverTimestamp(),
        'reason': 'user_initiated',
      });

      // Remove from following if currently following
      final relationship = await UniversalUserRoleService.getUserRelationship(
        targetUserId,
      );
      if (relationship == UserRelationship.following ||
          relationship == UserRelationship.closeFriend) {
        await unfollowUser(targetUserId);
      }

      await batch.commit();

      debugPrint('✅ Successfully blocked user: $targetUserId');
      return true;
    } catch (e) {
      debugPrint('❌ Error blocking user: $e');
      rethrow;
    }
  }

  /// Universal unblock user action
  static Future<bool> unblockUser(String targetUserId) async {
    final currentUserId = UniversalUserRoleService.getCurrentUserId();
    if (currentUserId == null) {
      throw Exception('User must be authenticated to unblock others');
    }

    try {
      // Remove from blocked users collection
      await _firestore
          .collection('users')
          .doc(currentUserId)
          .collection('blocked')
          .doc(targetUserId)
          .delete();

      debugPrint('✅ Successfully unblocked user: $targetUserId');
      return true;
    } catch (e) {
      debugPrint('❌ Error unblocking user: $e');
      rethrow;
    }
  }

  /// Check if post is liked by current user
  static Future<bool> isPostLiked(String postId) async {
    final currentUserId = UniversalUserRoleService.getCurrentUserId();
    if (currentUserId == null) return false;

    try {
      final likeDoc = await _firestore
          .collection('posts')
          .doc(postId)
          .collection('likes')
          .doc(currentUserId)
          .get();

      return likeDoc.exists;
    } catch (e) {
      debugPrint('❌ Error checking if post is liked: $e');
      return false;
    }
  }

  // Private helper methods
  static Future<void> _createFollowNotification(
    String followerId,
    String targetUserId,
  ) async {
    try {
      await _firestore.collection('notifications').add({
        'type': 'follow',
        'fromUserId': followerId,
        'toUserId': targetUserId,
        'createdAt': FieldValue.serverTimestamp(),
        'read': false,
        'data': {'action': 'follow'},
      });
    } catch (e) {
      debugPrint('⚠️ Failed to create follow notification: $e');
    }
  }

  static Future<void> _createLikeNotification(
    String likerId,
    String postOwnerId,
    String postId,
  ) async {
    try {
      await _firestore.collection('notifications').add({
        'type': 'like',
        'fromUserId': likerId,
        'toUserId': postOwnerId,
        'createdAt': FieldValue.serverTimestamp(),
        'read': false,
        'data': {'action': 'like', 'postId': postId},
      });
    } catch (e) {
      debugPrint('⚠️ Failed to create like notification: $e');
    }
  }
}
