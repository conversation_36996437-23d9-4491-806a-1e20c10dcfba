import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';

class TrendingService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Calculate trending score for a post
  double calculateTrendingScore({
    required int likeCount,
    required int commentCount,
    required int shareCount,
    required int viewCount,
    required DateTime timestamp,
  }) {
    final hoursSincePost = DateTime.now().difference(timestamp).inHours + 1;
    final score =
        (likeCount * 3 + commentCount * 2 + shareCount * 4 + viewCount * 1) /
        hoursSincePost;
    return score;
  }

  /// Update trending status for all posts
  Future<void> updateTrendingPosts({double threshold = 10.0}) async {
    final postsSnapshot = await _firestore.collection('posts').get();
    final batch = _firestore.batch();
    int trendingCount = 0;

    for (final doc in postsSnapshot.docs) {
      final data = doc.data();
      final likeCount = data['likeCount'] as int? ?? 0;
      final commentCount = data['commentCount'] as int? ?? 0;
      final shareCount = data['shareCount'] as int? ?? 0;
      final viewCount = data['viewCount'] as int? ?? 0;
      final timestamp =
          (data['timestamp'] as Timestamp?)?.toDate() ?? DateTime.now();
      final score = calculateTrendingScore(
        likeCount: likeCount,
        commentCount: commentCount,
        shareCount: shareCount,
        viewCount: viewCount,
        timestamp: timestamp,
      );
      final isTrending = score > threshold;
      if (isTrending) trendingCount++;
      batch.update(doc.reference, {
        'trending': isTrending,
        'trendingScore': score,
      });
    }
    await batch.commit();
    debugPrint('🔥 TrendingService: Updated $trendingCount trending posts.');
  }

  /// Automatically detect and update trending posts
  Future<void> detectAndUpdateTrendingPosts() async {
    try {
      debugPrint('🔥 Starting trending detection...');

      // Get posts from the last 24 hours
      final cutoffTime = DateTime.now().subtract(const Duration(hours: 24));

      final postsSnapshot = await _firestore
          .collection('posts')
          .where('createdAt', isGreaterThan: cutoffTime)
          .orderBy('createdAt', descending: true)
          .limit(100) // Process recent posts first
          .get();

      final batch = _firestore.batch();
      int updatedCount = 0;
      final List<Map<String, dynamic>> trendingPosts = [];

      for (final doc in postsSnapshot.docs) {
        final postData = doc.data();
        final post = Post.fromJson({...postData, 'id': doc.id});

        // Calculate trending score
        final trendingScore = calculateTrendingScore(
          likeCount: post.likeCount,
          commentCount: post.commentCount,
          shareCount: post.shareCount ?? 0,
          viewCount: post.viewCount ?? 0,
          timestamp: post.timestamp,
        );

        // Determine if post is trending (threshold: 10.0)
        final isTrending = trendingScore > 10.0;

        // Only update if status changed or score is significant
        if (post.trending != isTrending ||
            (post.trendingScore ?? 0) != trendingScore) {
          batch.update(doc.reference, {
            'trending': isTrending,
            'trendingScore': trendingScore,
            'lastTrendingUpdate': FieldValue.serverTimestamp(),
          });

          updatedCount++;

          if (isTrending) {
            trendingPosts.add({
              'postId': doc.id,
              'score': trendingScore,
              'username': post.username,
              'caption': post.caption,
            });
          }
        }
      }

      if (updatedCount > 0) {
        await batch.commit();
        debugPrint('✅ Updated trending status for $updatedCount posts');

        if (trendingPosts.isNotEmpty) {
          debugPrint('🔥 Trending posts detected:');
          for (final post in trendingPosts.take(5)) {
            debugPrint(
              '   - ${post['username']}: ${post['caption']} (score: ${post['score'].toStringAsFixed(2)})',
            );
          }
        }
      } else {
        debugPrint('✅ No trending updates needed');
      }
    } catch (e) {
      debugPrint('❌ Error detecting trending posts: $e');
      throw Exception('Failed to detect trending posts: $e');
    }
  }

  /// Get trending posts for the feed
  Future<List<Post>> getTrendingPosts({int limit = 10}) async {
    try {
      final postsSnapshot = await _firestore
          .collection('posts')
          .where('trending', isEqualTo: true)
          .orderBy('trendingScore', descending: true)
          .limit(limit)
          .get();

      return postsSnapshot.docs.map((doc) {
        final data = doc.data();
        return Post.fromJson({...data, 'id': doc.id});
      }).toList();
    } catch (e) {
      debugPrint('❌ Error fetching trending posts: $e');
      return [];
    }
  }

  /// Get posts with trending scores (for analytics)
  Future<List<Map<String, dynamic>>> getPostsWithTrendingScores({
    int limit = 20,
  }) async {
    try {
      final postsSnapshot = await _firestore
          .collection('posts')
          .where('trendingScore', isGreaterThan: 0)
          .orderBy('trendingScore', descending: true)
          .limit(limit)
          .get();

      return postsSnapshot.docs.map((doc) {
        final data = doc.data();
        return {
          'id': doc.id,
          'username': data['username'] ?? '',
          'caption': data['caption'] ?? '',
          'trendingScore': data['trendingScore'] ?? 0.0,
          'trending': data['trending'] ?? false,
          'likeCount': data['likeCount'] ?? 0,
          'commentCount': data['commentCount'] ?? 0,
          'shareCount': data['shareCount'] ?? 0,
          'viewCount': data['viewCount'] ?? 0,
          'createdAt': data['createdAt'],
        };
      }).toList();
    } catch (e) {
      debugPrint('❌ Error fetching posts with trending scores: $e');
      return [];
    }
  }

  /// Manual trigger for trending detection (for testing/admin use)
  Future<void> triggerTrendingDetection() async {
    debugPrint('🚀 Manual trending detection triggered');
    await detectAndUpdateTrendingPosts();
  }
}
