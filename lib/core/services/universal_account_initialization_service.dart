import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// Universal service for initializing new accounts with proper defaults
/// Ensures all accounts inherit universal functionality automatically
class UniversalAccountInitializationService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Initialize a new account with universal defaults
  /// This ensures every new account gets all features and proper settings
  static Future<void> initializeNewAccount({
    required String userId,
    required String email,
    required String name,
    required String username,
    String? profilePictureUrl,
    AccountType accountType = AccountType.regular,
  }) async {
    try {
      debugPrint('🚀 Initializing universal account for: $userId');

      // Create universal user document with all required fields
      final userData = _createUniversalUserData(
        userId: userId,
        email: email,
        name: name,
        username: username,
        profilePictureUrl: profilePictureUrl,
        accountType: accountType,
      );

      // Save user document
      await _firestore.collection('users').doc(userId).set(userData);

      // Initialize universal collections
      await _initializeUserCollections(userId);

      // Set universal preferences
      await _setUniversalPreferences(userId, accountType);

      // Initialize universal settings
      await _initializeUniversalSettings(userId);

      // Run universal validation
      await _validateAccountInitialization(userId);

      debugPrint('✅ Universal account initialization complete for: $userId');
    } catch (e) {
      debugPrint('❌ Error initializing universal account: $e');
      rethrow;
    }
  }

  /// Create universal user data structure
  static Map<String, dynamic> _createUniversalUserData({
    required String userId,
    required String email,
    required String name,
    required String username,
    String? profilePictureUrl,
    required AccountType accountType,
  }) {
    final now = FieldValue.serverTimestamp();

    return {
      // Core identity fields
      'uid': userId,
      'email': email,
      'name': name,
      'username': username,
      'profilePictureUrl': profilePictureUrl ?? '',
      'bio': '',
      'website': '',
      'location': '',
      'phone': '',

      // Account type and permissions
      'isAdmin': accountType == AccountType.admin,
      'isVerified':
          accountType == AccountType.verified ||
          accountType == AccountType.billionaire,
      'isBillionaire': accountType == AccountType.billionaire,
      'isBusinessAccount': accountType == AccountType.business,
      'userType': accountType.name,

      // Universal counters
      'followerCount': 0,
      'followingCount': 0,
      'postCount': 0,
      'storyCount': 0,

      // Universal status flags
      'isPrivate': false,
      'allowMessages': true,
      'showActivityStatus': true,
      'isNewUser': true,
      'onboardingCompleted': false,
      'accountVerified': false,

      // Universal timestamps
      'createdAt': now,
      'updatedAt': now,
      'lastActiveAt': now,

      // Universal preferences (will be expanded)
      'feedPreferences': _getDefaultFeedPreferences(),
      'uiPreferences': _getDefaultUIPreferences(),
      'privacySettings': _getDefaultPrivacySettings(),
      'notificationSettings': _getDefaultNotificationSettings(),

      // Universal feature flags
      'features': _getDefaultFeatureFlags(accountType),

      // Universal metadata
      'version': '1.0.0',
      'platform': 'flutter',
    };
  }

  /// Initialize universal user collections
  static Future<void> _initializeUserCollections(String userId) async {
    final batch = _firestore.batch();

    // Initialize followers collection with placeholder
    final followersRef = _firestore
        .collection('users')
        .doc(userId)
        .collection('followers')
        .doc('_placeholder');
    batch.set(followersRef, {
      'isPlaceholder': true,
      'createdAt': FieldValue.serverTimestamp(),
    });

    // Initialize following collection with placeholder
    final followingRef = _firestore
        .collection('users')
        .doc(userId)
        .collection('following')
        .doc('_placeholder');
    batch.set(followingRef, {
      'isPlaceholder': true,
      'createdAt': FieldValue.serverTimestamp(),
    });

    // Initialize blocked users collection with placeholder
    final blockedRef = _firestore
        .collection('users')
        .doc(userId)
        .collection('blocked')
        .doc('_placeholder');
    batch.set(blockedRef, {
      'isPlaceholder': true,
      'createdAt': FieldValue.serverTimestamp(),
    });

    // Initialize saved posts collection with placeholder
    final savedRef = _firestore
        .collection('users')
        .doc(userId)
        .collection('saved')
        .doc('_placeholder');
    batch.set(savedRef, {
      'isPlaceholder': true,
      'createdAt': FieldValue.serverTimestamp(),
    });

    await batch.commit();
    debugPrint('✅ Universal collections initialized for: $userId');
  }

  /// Set universal preferences based on account type
  static Future<void> _setUniversalPreferences(
    String userId,
    AccountType accountType,
  ) async {
    // Universal feed preferences
    final feedPrefs = _getDefaultFeedPreferences();

    // Adjust based on account type
    if (accountType == AccountType.admin ||
        accountType == AccountType.billionaire) {
      feedPrefs['defaultFilter'] = 'all'; // See all content
      feedPrefs['showSuggestedContent'] = true;
      feedPrefs['discoveryPercentage'] = 0.1; // 10% discovery content
    } else {
      feedPrefs['defaultFilter'] = 'all'; // New users start with all content
      feedPrefs['autoSwitchToFollowed'] =
          true; // Switch when they follow people
      feedPrefs['discoveryPercentage'] =
          0.3; // 30% discovery content for new users
    }

    await _firestore.collection('users').doc(userId).update({
      'feedPreferences': feedPrefs,
    });
  }

  /// Initialize universal settings
  static Future<void> _initializeUniversalSettings(String userId) async {
    // Create user settings document
    await _firestore.collection('userSettings').doc(userId).set({
      'userId': userId,
      'theme': 'system',
      'language': 'en',
      'timezone': 'UTC',
      'accessibility': {
        'highContrast': false,
        'largeText': false,
        'reduceMotion': false,
      },
      'security': {
        'twoFactorEnabled': false,
        'loginNotifications': true,
        'deviceTracking': true,
      },
      'createdAt': FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
    });
  }

  /// Validate account initialization
  static Future<void> _validateAccountInitialization(String userId) async {
    final userDoc = await _firestore.collection('users').doc(userId).get();

    if (!userDoc.exists) {
      throw Exception(
        'Account initialization failed: User document not created',
      );
    }

    final userData = userDoc.data()!;
    final requiredFields = [
      'uid',
      'email',
      'name',
      'username',
      'feedPreferences',
      'uiPreferences',
      'privacySettings',
      'notificationSettings',
      'isNewUser',
      'onboardingCompleted',
      'createdAt',
    ];

    for (final field in requiredFields) {
      if (!userData.containsKey(field)) {
        throw Exception(
          'Account initialization failed: Missing required field: $field',
        );
      }
    }

    debugPrint('✅ Account validation passed for: $userId');
  }

  // Default preference getters
  static Map<String, dynamic> _getDefaultFeedPreferences() {
    return {
      'defaultFilter': 'all',
      'autoSwitchToFollowed': true,
      'showSuggestedContent': true,
      'discoveryPercentage': 0.3,
      'contentTypes': {
        'images': true,
        'videos': true,
        'text': true,
        'reels': true,
      },
      'autoPlayVideos': true,
      'showCaptions': true,
    };
  }

  static Map<String, dynamic> _getDefaultUIPreferences() {
    return {
      'theme': 'system',
      'feedLayout': 'card',
      'autoPlayVideos': true,
      'showPreviewImages': true,
      'compactMode': false,
      'showTimestamps': true,
      'showReadReceipts': true,
    };
  }

  static Map<String, dynamic> _getDefaultPrivacySettings() {
    return {
      'profileVisibility': 'public',
      'allowMessages': true,
      'allowFollowRequests': true,
      'showActivityStatus': true,
      'showLastSeen': true,
      'allowTagging': true,
      'allowMentions': true,
      'dataSharing': {
        'analytics': true,
        'personalization': true,
        'marketing': false,
      },
    };
  }

  static Map<String, dynamic> _getDefaultNotificationSettings() {
    return {
      'push': {
        'enabled': true,
        'likes': true,
        'comments': true,
        'follows': true,
        'messages': true,
        'mentions': true,
        'stories': true,
      },
      'email': {
        'enabled': true,
        'weekly_digest': true,
        'security_alerts': true,
        'product_updates': false,
      },
      'inApp': {
        'enabled': true,
        'sound': true,
        'vibration': true,
        'badge': true,
      },
    };
  }

  static Map<String, dynamic> _getDefaultFeatureFlags(AccountType accountType) {
    final baseFeatures = {
      'canCreatePosts': true,
      'canCreateStories': true,
      'canCreateReels': true,
      'canGoLive': false,
      'canCreatePolls': false,
      'canAddLinks': false,
      'canMentionUnlimited': false,
      'canAccessAnalytics': false,
      'canPromotePosts': false,
    };

    // Enhance features based on account type
    switch (accountType) {
      case AccountType.admin:
        baseFeatures.addAll({
          'canGoLive': true,
          'canCreatePolls': true,
          'canAddLinks': true,
          'canMentionUnlimited': true,
          'canAccessAnalytics': true,
          'canPromotePosts': true,
          'canModerateContent': true,
        });
        break;
      case AccountType.billionaire:
        baseFeatures.addAll({
          'canGoLive': true,
          'canCreatePolls': true,
          'canAddLinks': true,
          'canMentionUnlimited': true,
          'canAccessAnalytics': true,
          'canPromotePosts': true,
        });
        break;
      case AccountType.verified:
        baseFeatures.addAll({
          'canGoLive': true,
          'canCreatePolls': true,
          'canAddLinks': true,
          'canAccessAnalytics': true,
        });
        break;
      case AccountType.business:
        baseFeatures.addAll({
          'canCreatePolls': true,
          'canAddLinks': true,
          'canAccessAnalytics': true,
          'canPromotePosts': true,
        });
        break;
      case AccountType.regular:
        // Base features only
        break;
    }

    return baseFeatures;
  }
}

/// Account types for initialization
enum AccountType { regular, business, verified, billionaire, admin }
