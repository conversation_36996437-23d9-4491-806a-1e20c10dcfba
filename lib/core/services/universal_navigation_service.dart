import 'package:flutter/material.dart';
import 'package:billionaires_social/core/services/universal_user_role_service.dart';
import 'package:billionaires_social/features/profile/screens/user_profile_screen.dart';
import 'package:billionaires_social/features/profile/screens/main_profile_screen.dart';

import 'package:billionaires_social/features/creation/screens/post_creation_screen.dart';
import 'package:billionaires_social/features/stories/screens/story_creation_screen.dart';
import 'package:billionaires_social/core/main_navigation.dart';
import 'package:billionaires_social/features/messaging/services/chat_service.dart';
import 'package:billionaires_social/features/messaging/models/chat_model.dart';
import 'package:billionaires_social/features/messaging/screens/chat_screen.dart';
import 'package:billionaires_social/features/settings/screens/main_settings_screen.dart';
import 'package:billionaires_social/core/service_locator.dart';

/// Universal navigation service that handles routing based on user context
/// Eliminates hardcoded navigation logic throughout the app
class UniversalNavigationService {
  /// Navigate to user profile - automatically determines if current user or other user
  static Future<void> navigateToProfile(
    BuildContext context,
    String userId, {
    bool replace = false,
  }) async {
    final isCurrentUser = UniversalUserRoleService.isCurrentUser(userId);

    Widget targetScreen;
    if (isCurrentUser) {
      // Navigate to own profile (MainProfileScreen)
      targetScreen = const MainProfileScreen();
    } else {
      // Navigate to other user's profile (UserProfileScreen)
      targetScreen = UserProfileScreen(userId: userId);
    }

    if (replace) {
      await Navigator.of(
        context,
      ).pushReplacement(MaterialPageRoute(builder: (context) => targetScreen));
    } else {
      await Navigator.of(
        context,
      ).push(MaterialPageRoute(builder: (context) => targetScreen));
    }
  }

  /// Navigate to messaging - handles permissions and context
  static Future<void> navigateToMessage(
    BuildContext context,
    String targetUserId, {
    String? targetUsername,
  }) async {
    final permissions = await UniversalUserRoleService.getUserPermissions(
      targetUserId,
    );

    if (!context.mounted) return;

    if (!permissions.canSendMessage) {
      _showPermissionDeniedDialog(
        context,
        'Cannot send message',
        'This user has disabled messages or you don\'t have permission to message them.',
      );
      return;
    }

    try {
      // Get or create direct chat
      final chatService = getIt<ChatService>();
      final currentUserId = UniversalUserRoleService.getCurrentUserId();

      if (currentUserId == null) {
        _showPermissionDeniedDialog(
          context,
          'Authentication Required',
          'Please log in to send messages.',
        );
        return;
      }

      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      // Get or create chat
      final chatId = await chatService.getOrCreateDirectChat(
        targetUserId,
        targetUsername ?? 'User',
        '', // avatar URL - can be fetched if needed
      );

      // Close loading dialog
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // Navigate to chat screen
      if (context.mounted) {
        final chatModel = ChatModel(
          id: chatId,
          name: targetUsername ?? 'User',
          avatarUrl: '',
          lastMessage: '',
          lastMessageTime: DateTime.now(),
          isGroup: false,
          participants: [currentUserId, targetUserId],
          isHidden: false,
          isBlocked: false,
          privacySettings: const ChatPrivacySettings(
            blockScreenshots: false,
            screenshotAlerts: true,
            readReceipts: true,
            typingIndicators: true,
            messageReactions: true,
            selfDestructEnabled: false,
            defaultSelfDestructSeconds: 10,
          ),
          unreadCount: 0,
          lastMessageStatus: MessageStatus.sent,
        );

        Navigator.of(context).push(
          MaterialPageRoute(builder: (context) => ChatScreen(chat: chatModel)),
        );
      }
    } catch (e) {
      // Close loading dialog if still open
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // Show error message
      if (context.mounted) {
        _showPermissionDeniedDialog(
          context,
          'Error',
          'Failed to open chat: ${e.toString()}',
        );
      }
    }
  }

  /// Navigate to content creation based on user permissions
  static Future<void> navigateToContentCreation(
    BuildContext context,
    ContentCreationType type,
  ) async {
    final currentUserId = UniversalUserRoleService.getCurrentUserId();

    if (currentUserId == null) {
      _showAuthRequiredDialog(context);
      return;
    }

    final limits = await UniversalUserRoleService.getContentLimits(
      currentUserId,
    );

    if (!context.mounted) return;

    Widget targetScreen;
    switch (type) {
      case ContentCreationType.post:
        targetScreen = const PostCreationScreen();
        break;
      case ContentCreationType.story:
        targetScreen = const StoryCreationScreen();
        break;
      case ContentCreationType.reel:
        if (!limits.canCreateReels) {
          _showFeatureNotAvailableDialog(context, 'Reels');
          return;
        }
        // Navigate to reel creation via story creation screen with reel mode
        targetScreen = const StoryCreationScreen(
          initialMode: CreationMode.reel,
        );
        break;
      case ContentCreationType.live:
        if (!limits.canGoLive) {
          _showFeatureNotAvailableDialog(context, 'Live streaming');
          return;
        }
        // TODO: Navigate to live setup when implemented
        _showFeatureComingSoonDialog(context, 'Live streaming');
        return;
    }

    await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => targetScreen,
        fullscreenDialog: true,
      ),
    );
  }

  /// Navigate to follow/following lists with proper permissions
  static Future<void> navigateToFollowList(
    BuildContext context,
    String userId,
    FollowListType type,
  ) async {
    final permissions = await UniversalUserRoleService.getUserPermissions(
      userId,
    );

    if (!context.mounted) return;

    if (!permissions.canViewProfile) {
      _showPermissionDeniedDialog(
        context,
        'Cannot view ${type.name}',
        'This user\'s profile is private.',
      );
      return;
    }

    // TODO: Navigate to follow list screen when implemented
    _showFeatureComingSoonDialog(context, '${type.name} list');
  }

  /// Universal back navigation with context awareness
  static void navigateBack(BuildContext context, {dynamic result}) {
    if (Navigator.of(context).canPop()) {
      Navigator.of(context).pop(result);
    } else {
      // If can't pop, navigate to main screen
      Navigator.of(context).pushNamedAndRemoveUntil('/', (route) => false);
    }
  }

  /// Navigate to settings based on user permissions
  static Future<void> navigateToSettings(
    BuildContext context,
    String userId,
  ) async {
    final isCurrentUser = UniversalUserRoleService.isCurrentUser(userId);

    if (!isCurrentUser) {
      _showPermissionDeniedDialog(
        context,
        'Access Denied',
        'You can only access your own settings.',
      );
      return;
    }

    // Navigate to main settings screen
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const MainSettingsScreen()));
  }

  /// Navigate to edit profile with permissions check
  static Future<void> navigateToEditProfile(
    BuildContext context,
    String userId,
  ) async {
    final permissions = await UniversalUserRoleService.getUserPermissions(
      userId,
    );

    if (!context.mounted) return;

    if (!permissions.canEdit) {
      _showPermissionDeniedDialog(
        context,
        'Cannot Edit',
        'You can only edit your own profile.',
      );
      return;
    }

    // TODO: Navigate to edit profile screen when implemented
    _showFeatureComingSoonDialog(context, 'Edit Profile');
  }

  // Helper methods for dialogs
  static void _showPermissionDeniedDialog(
    BuildContext context,
    String title,
    String message,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  static void _showAuthRequiredDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign In Required'),
        content: const Text('Please sign in to create content.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Navigate to sign in screen
            },
            child: const Text('Sign In'),
          ),
        ],
      ),
    );
  }

  static void _showFeatureNotAvailableDialog(
    BuildContext context,
    String featureName,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Feature Not Available'),
        content: Text(
          '$featureName is not available for your account type. Consider upgrading your account.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  static void _showFeatureComingSoonDialog(
    BuildContext context,
    String featureName,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Coming Soon'),
        content: Text('$featureName will be available in a future update.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

/// Content creation types
enum ContentCreationType { post, story, reel, live }

/// Follow list types
enum FollowListType { followers, following }
