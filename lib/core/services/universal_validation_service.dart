import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:billionaires_social/core/services/universal_account_service.dart';

/// Service to validate that all accounts have universal behavior
/// and that new accounts inherit all fixes automatically
class UniversalValidationService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Comprehensive validation of account universality
  static Future<ValidationResult> validateAccountUniversality(String userId) async {
    final result = ValidationResult(userId: userId);
    
    try {
      debugPrint('🔍 Validating account universality for: $userId');
      
      // 1. Validate account document structure
      await _validateAccountStructure(userId, result);
      
      // 2. Validate universal defaults
      await _validateUniversalDefaults(userId, result);
      
      // 3. Validate navigation logic
      await _validateNavigationLogic(userId, result);
      
      // 4. Validate feed behavior
      await _validateFeedBehavior(userId, result);
      
      // 5. Validate UI consistency
      await _validateUIConsistency(userId, result);
      
      result.isValid = result.errors.isEmpty;
      
      if (result.isValid) {
        debugPrint('✅ Account $userId passed all universality checks');
      } else {
        debugPrint('❌ Account $userId failed ${result.errors.length} universality checks');
        for (final error in result.errors) {
          debugPrint('   - $error');
        }
      }
      
    } catch (e) {
      result.errors.add('Validation failed with error: $e');
      debugPrint('❌ Validation error for $userId: $e');
    }
    
    return result;
  }

  /// Validate account document has all required fields
  static Future<void> _validateAccountStructure(String userId, ValidationResult result) async {
    final userDoc = await _firestore.collection('users').doc(userId).get();
    
    if (!userDoc.exists) {
      result.errors.add('User document does not exist');
      return;
    }
    
    final userData = userDoc.data()!;
    
    // Required universal fields
    final requiredFields = [
      'uid', 'email', 'name', 'username',
      'isAdmin', 'isVerified', 'isBillionaire',
      'followerCount', 'followingCount', 'postCount',
      'createdAt', 'updatedAt',
      'isNewUser', 'onboardingCompleted',
      'feedPreferences', 'uiPreferences', 
      'privacySettings', 'notificationSettings',
    ];
    
    for (final field in requiredFields) {
      if (!userData.containsKey(field)) {
        result.errors.add('Missing required field: $field');
      }
    }
    
    // Validate field types and values
    if (userData['followerCount'] is! int || userData['followerCount'] < 0) {
      result.errors.add('Invalid followerCount value');
    }
    
    if (userData['followingCount'] is! int || userData['followingCount'] < 0) {
      result.errors.add('Invalid followingCount value');
    }
    
    if (userData['postCount'] is! int || userData['postCount'] < 0) {
      result.errors.add('Invalid postCount value');
    }
    
    result.checks.add('Account structure validation');
  }

  /// Validate universal defaults are properly set
  static Future<void> _validateUniversalDefaults(String userId, ValidationResult result) async {
    final userDoc = await _firestore.collection('users').doc(userId).get();
    final userData = userDoc.data()!;
    
    // Validate feed preferences
    final feedPrefs = userData['feedPreferences'] as Map<String, dynamic>?;
    if (feedPrefs == null) {
      result.errors.add('Missing feedPreferences');
    } else {
      if (!feedPrefs.containsKey('defaultFilter')) {
        result.errors.add('Missing feedPreferences.defaultFilter');
      }
      if (!feedPrefs.containsKey('autoSwitchToFollowed')) {
        result.errors.add('Missing feedPreferences.autoSwitchToFollowed');
      }
    }
    
    // Validate UI preferences
    final uiPrefs = userData['uiPreferences'] as Map<String, dynamic>?;
    if (uiPrefs == null) {
      result.errors.add('Missing uiPreferences');
    }
    
    // Validate privacy settings
    final privacySettings = userData['privacySettings'] as Map<String, dynamic>?;
    if (privacySettings == null) {
      result.errors.add('Missing privacySettings');
    }
    
    // Validate notification settings
    final notificationSettings = userData['notificationSettings'] as Map<String, dynamic>?;
    if (notificationSettings == null) {
      result.errors.add('Missing notificationSettings');
    }
    
    result.checks.add('Universal defaults validation');
  }

  /// Validate navigation logic works universally
  static Future<void> _validateNavigationLogic(String userId, ValidationResult result) async {
    // Test that navigation logic can properly detect current vs other user
    final currentUserId = _auth.currentUser?.uid;
    
    if (currentUserId == null) {
      result.warnings.add('No current user - cannot test navigation logic');
      return;
    }
    
    // Navigation should work for any user ID
    final isCurrentUser = currentUserId == userId;
    
    if (isCurrentUser) {
      // Should navigate to own profile
      result.checks.add('Navigation: Current user detection works');
    } else {
      // Should navigate to other user profile
      result.checks.add('Navigation: Other user detection works');
    }
    
    result.checks.add('Navigation logic validation');
  }

  /// Validate feed behavior is consistent
  static Future<void> _validateFeedBehavior(String userId, ValidationResult result) async {
    final userDoc = await _firestore.collection('users').doc(userId).get();
    final userData = userDoc.data()!;
    
    // Check if feed preferences are set correctly
    final feedPrefs = userData['feedPreferences'] as Map<String, dynamic>?;
    if (feedPrefs != null) {
      final defaultFilter = feedPrefs['defaultFilter'] as String?;
      
      // New users should start with 'all' filter
      if (userData['isNewUser'] == true && defaultFilter != 'all') {
        result.warnings.add('New user should have defaultFilter set to "all"');
      }
      
      // Users with following should be able to use 'followed' filter
      final followingCount = userData['followingCount'] as int? ?? 0;
      if (followingCount > 0 && defaultFilter == 'all') {
        result.warnings.add('User with following might benefit from "followed" filter');
      }
    }
    
    result.checks.add('Feed behavior validation');
  }

  /// Validate UI consistency
  static Future<void> _validateUIConsistency(String userId, ValidationResult result) async {
    // Check that UI preferences are set
    final userDoc = await _firestore.collection('users').doc(userId).get();
    final userData = userDoc.data()!;
    
    final uiPrefs = userData['uiPreferences'] as Map<String, dynamic>?;
    if (uiPrefs != null) {
      // Validate required UI preferences
      final requiredUIFields = ['theme', 'feedLayout', 'autoPlayVideos'];
      for (final field in requiredUIFields) {
        if (!uiPrefs.containsKey(field)) {
          result.errors.add('Missing UI preference: $field');
        }
      }
    }
    
    result.checks.add('UI consistency validation');
  }

  /// Batch validate multiple accounts
  static Future<List<ValidationResult>> batchValidateAccounts(List<String> userIds) async {
    debugPrint('🔍 Batch validating ${userIds.length} accounts');
    
    final results = <ValidationResult>[];
    
    for (final userId in userIds) {
      final result = await validateAccountUniversality(userId);
      results.add(result);
      
      // Small delay to avoid overwhelming Firestore
      await Future.delayed(const Duration(milliseconds: 100));
    }
    
    // Summary
    final validAccounts = results.where((r) => r.isValid).length;
    final invalidAccounts = results.length - validAccounts;
    
    debugPrint('✅ Batch validation complete: $validAccounts valid, $invalidAccounts invalid');
    
    return results;
  }

  /// Auto-repair any accounts that fail validation
  static Future<void> repairFailedAccounts(List<ValidationResult> results) async {
    final failedAccounts = results.where((r) => !r.isValid).toList();
    
    if (failedAccounts.isEmpty) {
      debugPrint('✅ No accounts need repair');
      return;
    }
    
    debugPrint('🔧 Repairing ${failedAccounts.length} failed accounts');
    
    for (final result in failedAccounts) {
      await UniversalAccountService.repairAccountUniversality(result.userId);
    }
    
    debugPrint('✅ Account repair complete');
  }

  /// Test new account creation process
  static Future<bool> testNewAccountCreation() async {
    debugPrint('🧪 Testing new account creation process');
    
    // This would be used in testing environments
    // to ensure new accounts get all universal defaults
    
    // For now, just validate the template
    final template = UniversalAccountService.getUniversalAccountTemplate(
      uid: 'test-uid',
      email: '<EMAIL>',
      name: 'Test User',
    );
    
    // Check that template has all required fields
    final requiredFields = [
      'feedPreferences', 'uiPreferences', 
      'privacySettings', 'notificationSettings',
      'isNewUser', 'onboardingCompleted',
    ];
    
    for (final field in requiredFields) {
      if (!template.containsKey(field)) {
        debugPrint('❌ Template missing field: $field');
        return false;
      }
    }
    
    debugPrint('✅ New account creation template is valid');
    return true;
  }
}

/// Result of universality validation
class ValidationResult {
  final String userId;
  bool isValid = false;
  final List<String> errors = [];
  final List<String> warnings = [];
  final List<String> checks = [];
  
  ValidationResult({required this.userId});
  
  @override
  String toString() {
    return 'ValidationResult(userId: $userId, isValid: $isValid, '
           'errors: ${errors.length}, warnings: ${warnings.length}, '
           'checks: ${checks.length})';
  }
}
