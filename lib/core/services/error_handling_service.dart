import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:billionaires_social/core/services/analytics_service.dart';
import 'package:billionaires_social/core/service_locator.dart';

/// Centralized error handling service for the app
class ErrorHandlingService {
  static final ErrorHandlingService _instance =
      ErrorHandlingService._internal();
  factory ErrorHandlingService() => _instance;
  ErrorHandlingService._internal();

  // Error tracking
  final List<ErrorEvent> _errorHistory = [];
  static const int maxErrorHistory = 100;

  /// Handle and format errors for user display with enhanced tracking
  String handleError(dynamic error, {String? context}) {
    final errorEvent = ErrorEvent(
      error: error,
      context: context ?? 'unknown',
      timestamp: DateTime.now(),
      stackTrace: StackTrace.current,
    );

    _trackError(errorEvent);
    debugPrint('🚨 Error in ${context ?? 'unknown context'}: $error');

    // Track error in analytics if available
    _trackErrorInAnalytics(errorEvent);

    if (error is FirebaseAuthException) {
      return _handleFirebaseAuthError(error);
    } else if (error is FirebaseException) {
      return _handleFirebaseError(error);
    } else if (error is Exception) {
      return _handleGenericException(error);
    } else {
      return _handleUnknownError(error);
    }
  }

  /// Enhanced error handling with user-friendly messages and recovery suggestions
  ErrorResponse handleErrorWithResponse(dynamic error, {String? context}) {
    final userMessage = handleError(error, context: context);
    final errorType = _categorizeError(error);
    final recoverySuggestions = _getRecoverySuggestions(error);
    final canRetry = _canRetryError(error);

    return ErrorResponse(
      userMessage: userMessage,
      errorType: errorType,
      recoverySuggestions: recoverySuggestions,
      canRetry: canRetry,
      timestamp: DateTime.now(),
      context: context,
    );
  }

  /// Handle Firebase Auth specific errors
  String _handleFirebaseAuthError(FirebaseAuthException error) {
    switch (error.code) {
      case 'user-not-found':
        return 'No account found with this email address. Please check your email or sign up.';
      case 'wrong-password':
        return 'Incorrect password. Please try again or reset your password.';
      case 'email-already-in-use':
        return 'An account with this email already exists. Please sign in instead.';
      case 'weak-password':
        return 'Password is too weak. Please use at least 6 characters with a mix of letters and numbers.';
      case 'invalid-email':
        return 'Please enter a valid email address.';
      case 'user-disabled':
        return 'This account has been disabled. Please contact support for assistance.';
      case 'too-many-requests':
        return 'Too many failed attempts. Please wait a few minutes before trying again.';
      case 'operation-not-allowed':
        return 'This sign-in method is not enabled. Please contact support.';
      case 'invalid-credential':
        return 'Invalid credentials. Please check your email and password.';
      case 'account-exists-with-different-credential':
        return 'An account already exists with this email using a different sign-in method.';
      case 'requires-recent-login':
        return 'Please sign out and sign in again to perform this action.';
      default:
        return 'Authentication failed: ${error.message ?? 'Unknown error'}';
    }
  }

  /// Handle Firebase (Firestore/Storage) errors
  String _handleFirebaseError(FirebaseException error) {
    switch (error.code) {
      case 'permission-denied':
        return 'You don\'t have permission to perform this action.';
      case 'unavailable':
        return 'Service is currently unavailable. Please try again later.';
      case 'deadline-exceeded':
        return 'Request timed out. Please check your connection and try again.';
      case 'not-found':
        return 'The requested data was not found.';
      case 'already-exists':
        return 'This item already exists.';
      case 'resource-exhausted':
        return 'Service quota exceeded. Please try again later.';
      case 'failed-precondition':
        return 'Operation failed due to invalid conditions.';
      case 'aborted':
        return 'Operation was aborted. Please try again.';
      case 'out-of-range':
        return 'Invalid input range.';
      case 'unimplemented':
        return 'This feature is not yet available.';
      case 'internal':
        return 'Internal server error. Please try again later.';
      case 'data-loss':
        return 'Data corruption detected. Please contact support.';
      case 'unauthenticated':
        return 'Please sign in to continue.';
      // Storage specific errors
      case 'object-not-found':
        return 'File not found.';
      case 'bucket-not-found':
        return 'Storage bucket not found.';
      case 'project-not-found':
        return 'Project configuration error.';
      case 'quota-exceeded':
        return 'Storage quota exceeded.';
      case 'unauthorized':
        return 'Unauthorized access to storage.';
      case 'retry-limit-exceeded':
        return 'Upload failed after multiple attempts. Please try again.';
      case 'invalid-checksum':
        return 'File upload corrupted. Please try again.';
      case 'canceled':
        return 'Upload was canceled.';
      default:
        return 'Service error: ${error.message ?? 'Unknown error'}';
    }
  }

  /// Handle generic exceptions
  String _handleGenericException(Exception error) {
    final message = error.toString();

    // Remove "Exception: " prefix if present
    final cleanMessage = message.startsWith('Exception: ')
        ? message.substring(11)
        : message;

    // Handle common network errors
    if (cleanMessage.toLowerCase().contains('network')) {
      return 'Network error. Please check your internet connection and try again.';
    }

    // Handle timeout errors
    if (cleanMessage.toLowerCase().contains('timeout')) {
      return 'Request timed out. Please try again.';
    }

    // Handle connection errors
    if (cleanMessage.toLowerCase().contains('connection')) {
      return 'Connection failed. Please check your internet connection.';
    }

    // Handle server errors
    if (cleanMessage.toLowerCase().contains('server')) {
      return 'Server error. Please try again later.';
    }

    return cleanMessage.isNotEmpty
        ? cleanMessage
        : 'An unexpected error occurred. Please try again.';
  }

  /// Handle unknown errors
  String _handleUnknownError(dynamic error) {
    final message = error?.toString() ?? '';

    if (message.isEmpty) {
      return 'An unexpected error occurred. Please try again.';
    }

    return 'Unexpected error: $message';
  }

  /// Log error for debugging and analytics
  void logError(
    dynamic error, {
    String? context,
    Map<String, dynamic>? additionalData,
    StackTrace? stackTrace,
  }) {
    debugPrint('🚨 ERROR LOG:');
    debugPrint('Context: ${context ?? 'Unknown'}');
    debugPrint('Error: $error');
    debugPrint('Type: ${error.runtimeType}');

    if (additionalData != null) {
      debugPrint('Additional Data: $additionalData');
    }

    if (stackTrace != null) {
      debugPrint('Stack Trace: $stackTrace');
    }

    // In production, you would send this to your analytics service
    // Example: FirebaseCrashlytics.instance.recordError(error, stackTrace);
  }

  /// Check if error is recoverable
  bool isRecoverableError(dynamic error) {
    if (error is FirebaseException) {
      switch (error.code) {
        case 'unavailable':
        case 'deadline-exceeded':
        case 'aborted':
        case 'internal':
        case 'resource-exhausted':
          return true;
        default:
          return false;
      }
    }

    if (error is Exception) {
      final message = error.toString().toLowerCase();
      return message.contains('network') ||
          message.contains('timeout') ||
          message.contains('connection');
    }

    return false;
  }

  /// Get retry delay for recoverable errors
  Duration getRetryDelay(int attemptNumber) {
    // Exponential backoff: 1s, 2s, 4s, 8s, 16s (max)
    final delaySeconds = (1 << (attemptNumber - 1)).clamp(1, 16);
    return Duration(seconds: delaySeconds);
  }

  /// Format error for user notification
  Map<String, dynamic> formatErrorForNotification(
    dynamic error, {
    String? context,
  }) {
    final userMessage = handleError(error, context: context);
    final isRecoverable = isRecoverableError(error);

    return {
      'title': isRecoverable ? 'Connection Issue' : 'Error',
      'message': userMessage,
      'isRecoverable': isRecoverable,
      'canRetry': isRecoverable,
      'severity': _getErrorSeverity(error),
    };
  }

  /// Get error severity level
  String _getErrorSeverity(dynamic error) {
    if (error is FirebaseAuthException) {
      switch (error.code) {
        case 'user-disabled':
        case 'too-many-requests':
          return 'high';
        case 'wrong-password':
        case 'user-not-found':
          return 'medium';
        default:
          return 'low';
      }
    }

    if (error is FirebaseException) {
      switch (error.code) {
        case 'permission-denied':
        case 'unauthenticated':
          return 'high';
        case 'unavailable':
        case 'deadline-exceeded':
          return 'medium';
        default:
          return 'low';
      }
    }

    return 'medium';
  }

  /// Track error in internal history
  void _trackError(ErrorEvent errorEvent) {
    _errorHistory.add(errorEvent);

    // Limit history size
    if (_errorHistory.length > maxErrorHistory) {
      _errorHistory.removeAt(0);
    }
  }

  /// Track error in analytics service
  void _trackErrorInAnalytics(ErrorEvent errorEvent) {
    try {
      if (getIt.isRegistered<AnalyticsService>()) {
        final analytics = getIt<AnalyticsService>();
        analytics.logEventSafely(
          eventName: 'app_error',
          parameters: {
            'error_type': errorEvent.error.runtimeType.toString(),
            'error_message': errorEvent.error.toString(),
            'context': errorEvent.context,
            'timestamp': errorEvent.timestamp.toIso8601String(),
          },
        );
      }
    } catch (e) {
      debugPrint('Failed to track error in analytics: $e');
    }
  }

  /// Categorize error type
  ErrorType _categorizeError(dynamic error) {
    if (error is FirebaseAuthException) {
      return ErrorType.authentication;
    } else if (error is FirebaseException) {
      return ErrorType.network;
    } else if (error.toString().toLowerCase().contains('network')) {
      return ErrorType.network;
    } else if (error.toString().toLowerCase().contains('permission')) {
      return ErrorType.permission;
    } else if (error.toString().toLowerCase().contains('validation')) {
      return ErrorType.validation;
    } else {
      return ErrorType.unknown;
    }
  }

  /// Get recovery suggestions for error
  List<String> _getRecoverySuggestions(dynamic error) {
    final errorType = _categorizeError(error);

    switch (errorType) {
      case ErrorType.network:
        return [
          'Check your internet connection',
          'Try again in a few moments',
          'Switch to a different network if available',
        ];
      case ErrorType.authentication:
        return [
          'Try logging out and logging back in',
          'Check your credentials',
          'Reset your password if needed',
        ];
      case ErrorType.permission:
        return [
          'Check your account permissions',
          'Contact support if the issue persists',
          'Try refreshing the app',
        ];
      case ErrorType.validation:
        return [
          'Check your input for errors',
          'Make sure all required fields are filled',
          'Follow the format requirements',
        ];
      case ErrorType.unknown:
        return [
          'Try refreshing the app',
          'Restart the app if the problem continues',
          'Contact support if the issue persists',
        ];
    }
  }

  /// Check if error can be retried
  bool _canRetryError(dynamic error) {
    final errorType = _categorizeError(error);

    switch (errorType) {
      case ErrorType.network:
        return true;
      case ErrorType.authentication:
        return false; // Usually requires user action
      case ErrorType.permission:
        return false; // Requires permission changes
      case ErrorType.validation:
        return false; // Requires input correction
      case ErrorType.unknown:
        return true; // Safe to retry unknown errors
    }
  }

  /// Get error statistics
  Map<String, dynamic> getErrorStatistics() {
    final now = DateTime.now();
    final last24Hours = now.subtract(const Duration(hours: 24));
    final recentErrors = _errorHistory
        .where((e) => e.timestamp.isAfter(last24Hours))
        .toList();

    final errorsByType = <String, int>{};
    final errorsByContext = <String, int>{};

    for (final error in recentErrors) {
      final type = _categorizeError(error.error).toString();
      errorsByType[type] = (errorsByType[type] ?? 0) + 1;
      errorsByContext[error.context] =
          (errorsByContext[error.context] ?? 0) + 1;
    }

    return {
      'totalErrors': _errorHistory.length,
      'recentErrors': recentErrors.length,
      'errorsByType': errorsByType,
      'errorsByContext': errorsByContext,
      'lastError': _errorHistory.isNotEmpty
          ? _errorHistory.last.timestamp.toIso8601String()
          : null,
    };
  }
}

/// Error event for tracking
class ErrorEvent {
  final dynamic error;
  final String context;
  final DateTime timestamp;
  final StackTrace stackTrace;

  ErrorEvent({
    required this.error,
    required this.context,
    required this.timestamp,
    required this.stackTrace,
  });

  @override
  String toString() => 'ErrorEvent(${error.runtimeType}, $context, $timestamp)';
}

/// Enhanced error response
class ErrorResponse {
  final String userMessage;
  final ErrorType errorType;
  final List<String> recoverySuggestions;
  final bool canRetry;
  final DateTime timestamp;
  final String? context;

  ErrorResponse({
    required this.userMessage,
    required this.errorType,
    required this.recoverySuggestions,
    required this.canRetry,
    required this.timestamp,
    this.context,
  });

  @override
  String toString() => 'ErrorResponse($errorType, canRetry: $canRetry)';
}

/// Error type enumeration
enum ErrorType { network, authentication, permission, validation, unknown }
