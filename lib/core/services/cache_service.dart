import 'dart:convert';
import 'dart:io';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:billionaires_social/core/services/memory_management_service.dart';

class CacheService {
  static final CacheService _instance = CacheService._internal();
  factory CacheService() => _instance;
  CacheService._internal();

  static const String _cachePrefix = 'billionaires_social_cache';
  static const Duration _defaultExpiry = Duration(hours: 24);

  late SharedPreferences _prefs;
  final Map<String, dynamic> _memoryCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  final Map<String, int> _itemSizes = {};
  final Map<String, DateTime> _accessTimes = {};

  // Memory management constants
  static const int maxMemoryCacheSize = 50 * 1024 * 1024; // 50MB
  static const int maxCachedItems = 1000;
  static const int maxImageCacheSize = 100 * 1024 * 1024; // 100MB
  static const int maxCachedImages = 200;

  int _currentCacheSize = 0;

  // Initialize cache service
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    await _cleanupExpiredCache();
  }

  // Cache data with expiration and memory management
  Future<void> setData(String key, dynamic data, {Duration? expiry}) async {
    final cacheKey = '$_cachePrefix:$key';
    final expiryTime = DateTime.now().add(expiry ?? _defaultExpiry);

    final cacheData = {
      'data': data,
      'expiry': expiryTime.toIso8601String(),
      'timestamp': DateTime.now().toIso8601String(),
    };

    // Calculate data size for memory management
    final dataSize = _calculateDataSize(cacheData);

    // Check if adding this item would exceed memory limits
    if (_currentCacheSize + dataSize > maxMemoryCacheSize) {
      await _evictOldestItems(dataSize);
    }

    // Check global memory usage
    final memoryStats = MemoryManagementService().getMemoryStats();
    if (memoryStats.isNearLimit) {
      debugPrint('⚠️ Global memory near limit, performing cache cleanup');
      await _performEmergencyCleanup();
    }

    // Store in memory cache
    _memoryCache[cacheKey] = cacheData;
    _cacheTimestamps[cacheKey] = expiryTime;
    _itemSizes[cacheKey] = dataSize.round();
    _accessTimes[cacheKey] = DateTime.now();
    _currentCacheSize += dataSize.round();

    // Store in persistent cache
    await _prefs.setString(cacheKey, jsonEncode(cacheData));
  }

  // Get cached data
  Future<T?> getData<T>(String key) async {
    final cacheKey = '$_cachePrefix:$key';

    // Check memory cache first
    if (_memoryCache.containsKey(cacheKey)) {
      final cacheData = _memoryCache[cacheKey] as Map<String, dynamic>;
      final expiry = DateTime.parse(cacheData['expiry']);

      if (DateTime.now().isBefore(expiry)) {
        return cacheData['data'] as T;
      } else {
        // Remove expired data
        _memoryCache.remove(cacheKey);
        _cacheTimestamps.remove(cacheKey);
      }
    }

    // Check persistent cache
    final cachedString = _prefs.getString(cacheKey);
    if (cachedString != null) {
      try {
        final cacheData = jsonDecode(cachedString) as Map<String, dynamic>;
        final expiry = DateTime.parse(cacheData['expiry']);

        if (DateTime.now().isBefore(expiry)) {
          // Add to memory cache
          _memoryCache[cacheKey] = cacheData;
          _cacheTimestamps[cacheKey] = expiry;
          return cacheData['data'] as T;
        } else {
          // Remove expired data
          await _prefs.remove(cacheKey);
        }
      } catch (e) {
        debugPrint('Error parsing cached data: $e');
        await _prefs.remove(cacheKey);
      }
    }

    return null;
  }

  // Remove cached data
  Future<void> removeData(String key) async {
    final cacheKey = '$_cachePrefix:$key';
    _memoryCache.remove(cacheKey);
    _cacheTimestamps.remove(cacheKey);
    await _prefs.remove(cacheKey);
  }

  // Clear all cache
  Future<void> clearAll() async {
    _memoryCache.clear();
    _cacheTimestamps.clear();

    final keys = _prefs.getKeys();
    for (final key in keys) {
      if (key.startsWith(_cachePrefix)) {
        await _prefs.remove(key);
      }
    }
  }

  // Clear expired cache
  Future<void> _cleanupExpiredCache() async {
    final now = DateTime.now();
    final keysToRemove = <String>[];

    // Clean memory cache
    for (final entry in _cacheTimestamps.entries) {
      if (now.isAfter(entry.value)) {
        keysToRemove.add(entry.key);
      }
    }

    for (final key in keysToRemove) {
      _memoryCache.remove(key);
      _cacheTimestamps.remove(key);
    }

    // Clean persistent cache
    final keys = _prefs.getKeys();
    for (final key in keys) {
      if (key.startsWith(_cachePrefix)) {
        final cachedString = _prefs.getString(key);
        if (cachedString != null) {
          try {
            final cacheData = jsonDecode(cachedString) as Map<String, dynamic>;
            final expiry = DateTime.parse(cacheData['expiry']);
            if (now.isAfter(expiry)) {
              await _prefs.remove(key);
            }
          } catch (e) {
            await _prefs.remove(key);
          }
        }
      }
    }
  }

  // Cache user profile with 5-minute TTL for performance optimization
  Future<void> cacheUserProfile(
    String userId,
    Map<String, dynamic> profile,
  ) async {
    await setData(
      'user_profile_$userId',
      profile,
      expiry: const Duration(minutes: 5), // Reduced from 6 hours to 5 minutes
    );
  }

  // Get cached user profile
  Future<Map<String, dynamic>?> getCachedUserProfile(String userId) async {
    return await getData<Map<String, dynamic>>('user_profile_$userId');
  }

  // Cache feed posts
  Future<void> cacheFeedPosts(
    String userId,
    List<Map<String, dynamic>> posts,
  ) async {
    await setData(
      'feed_posts_$userId',
      posts,
      expiry: const Duration(minutes: 30),
    );
  }

  // Get cached feed posts
  Future<List<Map<String, dynamic>>?> getCachedFeedPosts(String userId) async {
    return await getData<List<Map<String, dynamic>>>('feed_posts_$userId');
  }

  // Cache search results
  Future<void> cacheSearchResults(
    String query,
    List<Map<String, dynamic>> results,
  ) async {
    await setData('search_$query', results, expiry: const Duration(hours: 1));
  }

  // Get cached search results
  Future<List<Map<String, dynamic>>?> getCachedSearchResults(
    String query,
  ) async {
    return await getData<List<Map<String, dynamic>>>('search_$query');
  }

  // Cache events
  Future<void> cacheEvents(List<Map<String, dynamic>> events) async {
    await setData('events', events, expiry: const Duration(hours: 2));
  }

  // Get cached events
  Future<List<Map<String, dynamic>>?> getCachedEvents() async {
    return await getData<List<Map<String, dynamic>>>('events');
  }

  // Cache marketplace items
  Future<void> cacheMarketplaceItems(List<Map<String, dynamic>> items) async {
    await setData('marketplace_items', items, expiry: const Duration(hours: 1));
  }

  // Get cached marketplace items
  Future<List<Map<String, dynamic>>?> getCachedMarketplaceItems() async {
    return await getData<List<Map<String, dynamic>>>('marketplace_items');
  }

  // Cache stories
  Future<void> cacheStories(
    String userId,
    List<Map<String, dynamic>> stories,
  ) async {
    await setData(
      'stories_$userId',
      stories,
      expiry: const Duration(minutes: 15),
    );
  }

  // Get cached stories
  Future<List<Map<String, dynamic>>?> getCachedStories(String userId) async {
    return await getData<List<Map<String, dynamic>>>('stories_$userId');
  }

  // Cache notifications
  Future<void> cacheNotifications(
    String userId,
    List<Map<String, dynamic>> notifications,
  ) async {
    await setData(
      'notifications_$userId',
      notifications,
      expiry: const Duration(minutes: 10),
    );
  }

  // Get cached notifications
  Future<List<Map<String, dynamic>>?> getCachedNotifications(
    String userId,
  ) async {
    return await getData<List<Map<String, dynamic>>>('notifications_$userId');
  }

  // Cache analytics data
  Future<void> cacheAnalyticsData(
    String userId,
    Map<String, dynamic> analytics,
  ) async {
    await setData(
      'analytics_$userId',
      analytics,
      expiry: const Duration(hours: 4),
    );
  }

  // Get cached analytics data
  Future<Map<String, dynamic>?> getCachedAnalyticsData(String userId) async {
    return await getData<Map<String, dynamic>>('analytics_$userId');
  }

  // Preload images
  Future<void> preloadImages(List<String> imageUrls) async {
    for (final url in imageUrls) {
      try {
        // Simple image preloading without BuildContext
        final httpClient = HttpClient();
        final request = await httpClient.getUrl(Uri.parse(url));
        final response = await request.close();

        if (response.statusCode == 200) {
          await consolidateHttpClientResponseBytes(response);
        }
      } catch (e) {
        debugPrint('Error preloading image $url: $e');
      }
    }
  }

  // Cache file operations
  Future<String?> cacheFile(String url, String fileName) async {
    try {
      final cacheDir = await getTemporaryDirectory();
      final filePath = '${cacheDir.path}/$_cachePrefix/$fileName';
      final file = File(filePath);

      // Create directory if it doesn't exist
      await file.parent.create(recursive: true);

      // Download and cache file
      final httpClient = HttpClient();
      final request = await httpClient.getUrl(Uri.parse(url));
      final response = await request.close();

      if (response.statusCode == 200) {
        final bytes = await consolidateHttpClientResponseBytes(response);
        await file.writeAsBytes(bytes);
        return filePath;
      }
    } catch (e) {
      debugPrint('Error caching file $url: $e');
    }

    return null;
  }

  // Get cached file path
  Future<String?> getCachedFilePath(String fileName) async {
    final cacheDir = await getTemporaryDirectory();
    final filePath = '${cacheDir.path}/$_cachePrefix/$fileName';
    final file = File(filePath);

    if (file.existsSync()) {
      return filePath;
    }

    return null;
  }

  // Clear file cache
  Future<void> clearFileCache() async {
    try {
      final cacheDir = await getTemporaryDirectory();
      final cachePath = '${cacheDir.path}/$_cachePrefix';
      final cacheDirFile = Directory(cachePath);

      if (cacheDirFile.existsSync()) {
        await cacheDirFile.delete(recursive: true);
      }
    } catch (e) {
      debugPrint('Error clearing file cache: $e');
    }
  }

  // Memory management methods

  /// Calculate the approximate size of data in bytes
  double _calculateDataSize(dynamic data) {
    try {
      final jsonString = jsonEncode(data);
      return jsonString.length * 2.0; // Approximate UTF-16 encoding
    } catch (e) {
      return 1024.0; // Default size if calculation fails
    }
  }

  /// Evict oldest items to make space for new data
  Future<void> _evictOldestItems(double requiredSpace) async {
    if (_accessTimes.isEmpty) return;

    // Sort items by access time (oldest first)
    final sortedKeys = _accessTimes.keys.toList()
      ..sort((a, b) => _accessTimes[a]!.compareTo(_accessTimes[b]!));

    double freedSpace = 0;
    final keysToRemove = <String>[];

    for (final key in sortedKeys) {
      if (freedSpace >= requiredSpace) break;

      final itemSize = _itemSizes[key] ?? 0;
      freedSpace += itemSize;
      keysToRemove.add(key);
    }

    // Remove the items
    for (final key in keysToRemove) {
      await _removeFromCache(key);
    }

    debugPrint(
      '🧹 Cache: Evicted ${keysToRemove.length} items, freed ${freedSpace.round()} bytes',
    );
  }

  /// Remove an item from all cache layers
  Future<void> _removeFromCache(String key) async {
    final itemSize = _itemSizes[key] ?? 0;

    _memoryCache.remove(key);
    _cacheTimestamps.remove(key);
    _itemSizes.remove(key);
    _accessTimes.remove(key);
    _currentCacheSize -= itemSize;

    await _prefs.remove(key);
  }

  /// Get current cache statistics
  Map<String, dynamic> getCacheStats() {
    return {
      'memoryItems': _memoryCache.length,
      'currentSizeBytes': _currentCacheSize,
      'maxSizeBytes': maxMemoryCacheSize,
      'utilizationPercent': (_currentCacheSize / maxMemoryCacheSize * 100)
          .round(),
      'oldestItemAge': _getOldestItemAge(),
    };
  }

  /// Get the age of the oldest cached item
  Duration? _getOldestItemAge() {
    if (_accessTimes.isEmpty) return null;

    final oldestTime = _accessTimes.values.reduce(
      (a, b) => a.isBefore(b) ? a : b,
    );
    return DateTime.now().difference(oldestTime);
  }

  /// Emergency cleanup when global memory is high
  Future<void> _performEmergencyCleanup() async {
    // More aggressive cleanup - target 50% of max cache size
    final targetSize = (maxMemoryCacheSize * 0.5).round();

    if (_currentCacheSize > targetSize) {
      final excessSize = _currentCacheSize - targetSize;
      await _evictOldestItems(excessSize.toDouble());
    }

    // Also clean expired items
    await _cleanupExpiredCache();

    debugPrint('🚨 Emergency cache cleanup completed');
  }

  /// Force cleanup of cache to target size
  Future<void> optimizeMemoryUsage() async {
    final targetSize = (maxMemoryCacheSize * 0.8).round(); // 80% of max

    if (_currentCacheSize > targetSize) {
      final excessSize = _currentCacheSize - targetSize;
      await _evictOldestItems(excessSize.toDouble());
    }
  }
}
