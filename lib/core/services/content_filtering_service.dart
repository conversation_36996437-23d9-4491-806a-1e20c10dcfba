import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';

// Content sensitivity levels
enum SensitivityLevel { low, medium, high, extreme }

// Content categories
enum ContentCategory {
  technology,
  finance,
  realEstate,
  luxuryLifestyle,
  travel,
  artCulture,
  sports,
  entertainment,
  business,
  investment,
  cryptocurrency,
  sustainability,
  healthWellness,
  education,
  politics,
  controversial,
  sensitive,
}

// User content preferences
class ContentPreferences {
  final Set<ContentCategory> allowedCategories;
  final SensitivityLevel maxSensitivityLevel;
  final bool showPoliticalContent;
  final bool showControversialTopics;
  final bool showSensitiveContent;

  ContentPreferences({
    required this.allowedCategories,
    required this.maxSensitivityLevel,
    this.showPoliticalContent = false,
    this.showControversialTopics = false,
    this.showSensitiveContent = false,
  });

  Map<String, dynamic> toJson() => {
    'allowedCategories': allowedCategories.map((c) => c.name).toList(),
    'maxSensitivityLevel': maxSensitivityLevel.name,
    'showPoliticalContent': showPoliticalContent,
    'showControversialTopics': showControversialTopics,
    'showSensitiveContent': showSensitiveContent,
  };

  factory ContentPreferences.fromJson(Map<String, dynamic> json) {
    final maxSensitivityLevelString =
        json['maxSensitivityLevel'] as String? ?? 'low';
    final maxSensitivityLevel = SensitivityLevel.values.firstWhere(
      (e) => e.name == maxSensitivityLevelString,
      orElse: () => SensitivityLevel.low,
    );

    return ContentPreferences(
      allowedCategories:
          (json['allowedCategories'] as List<dynamic>?)
              ?.map(
                (c) => ContentCategory.values.firstWhere(
                  (e) => e.name == c,
                  orElse: () => ContentCategory.technology,
                ),
              )
              .toSet() ??
          {ContentCategory.technology},
      maxSensitivityLevel: maxSensitivityLevel,
      showPoliticalContent: json['showPoliticalContent'] ?? false,
      showControversialTopics: json['showControversialTopics'] ?? false,
      showSensitiveContent: json['showSensitiveContent'] ?? false,
    );
  }
}

class ContentFilteringService {
  static final ContentFilteringService _instance =
      ContentFilteringService._internal();
  factory ContentFilteringService() => _instance;
  ContentFilteringService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Get user content preferences
  Future<ContentPreferences> getUserContentPreferences() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        return _getDefaultPreferences();
      }

      final doc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .collection('preferences')
          .doc('content')
          .get();

      if (doc.exists) {
        return ContentPreferences.fromJson(doc.data()!);
      } else {
        // Create default preferences
        final defaultPrefs = _getDefaultPreferences();
        await saveUserContentPreferences(defaultPrefs);
        return defaultPrefs;
      }
    } catch (e) {
      debugPrint('❌ Error getting user content preferences: $e');
      return _getDefaultPreferences();
    }
  }

  // Save user content preferences
  Future<void> saveUserContentPreferences(
    ContentPreferences preferences,
  ) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return;

      await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .collection('preferences')
          .doc('content')
          .set(preferences.toJson());

      debugPrint('✅ User content preferences saved');
    } catch (e) {
      debugPrint('❌ Error saving user content preferences: $e');
    }
  }

  // Analyze content for sensitivity and categorization
  Future<Map<String, dynamic>> analyzeContent(String text) async {
    try {
      final analysis = {
        'sensitivityLevel': _analyzeSensitivity(text),
        'categories': _categorizeContent(text),
        'hasProfanity': _checkProfanity(text),
        'hasPoliticalContent': _checkPoliticalContent(text),
        'hasControversialTopics': _checkControversialTopics(text),
        'isAppropriate': true,
        'flags': <String>[],
      };

      // Set flags based on analysis
      final List<String> flags = (analysis['flags'] as List<String>);
      if (analysis['hasProfanity'] == true) {
        flags.add('profanity');
        analysis['isAppropriate'] = false;
      }

      if (analysis['sensitivityLevel'] == SensitivityLevel.extreme.name) {
        flags.add('extreme_sensitivity');
        analysis['isAppropriate'] = false;
      }

      if (analysis['hasPoliticalContent'] == true) {
        flags.add('political_content');
      }

      if (analysis['hasControversialTopics'] == true) {
        flags.add('controversial_topics');
      }

      return analysis;
    } catch (e) {
      debugPrint('❌ Error analyzing content: $e');
      return {
        'sensitivityLevel': SensitivityLevel.low.name,
        'categories': [ContentCategory.technology.name],
        'hasProfanity': false,
        'hasPoliticalContent': false,
        'hasControversialTopics': false,
        'isAppropriate': true,
        'flags': <String>[],
      };
    }
  }

  // Filter posts based on user preferences
  Future<List<String>> filterPostIds(
    List<String> postIds,
    ContentPreferences preferences,
  ) async {
    try {
      if (postIds.isEmpty) return [];

      final filteredIds = <String>[];

      // Process posts in batches
      for (int i = 0; i < postIds.length; i += 10) {
        final batch = postIds.skip(i).take(10).toList();

        final postsSnapshot = await _firestore
            .collection('posts')
            .where(FieldPath.documentId, whereIn: batch)
            .get();

        for (final doc in postsSnapshot.docs) {
          final data = doc.data();
          final caption = data['caption'] ?? '';
          final analysis = await analyzeContent(caption);

          // Check if content matches user preferences
          if (_isContentAppropriate(analysis, preferences)) {
            filteredIds.add(doc.id);
          }
        }
      }

      debugPrint(
        '🔍 Filtered ${postIds.length} posts to ${filteredIds.length} appropriate posts',
      );
      return filteredIds;
    } catch (e) {
      debugPrint('❌ Error filtering posts: $e');
      return postIds; // Return all posts if filtering fails
    }
  }

  // Check if content is appropriate for user preferences
  bool _isContentAppropriate(
    Map<String, dynamic> analysis,
    ContentPreferences preferences,
  ) {
    // Check sensitivity level
    final sensitivityLevel = SensitivityLevel.values.firstWhere(
      (e) => e.name == analysis['sensitivityLevel'],
      orElse: () => SensitivityLevel.low,
    );

    if (sensitivityLevel.index > preferences.maxSensitivityLevel.index) {
      return false;
    }

    // Check categories
    final contentCategories = (analysis['categories'] as List<dynamic>)
        .map(
          (c) => ContentCategory.values.firstWhere(
            (e) => e.name == c,
            orElse: () => ContentCategory.technology,
          ),
        )
        .toSet();

    final hasAllowedCategory = contentCategories.any(
      (category) => preferences.allowedCategories.contains(category),
    );

    if (!hasAllowedCategory) {
      return false;
    }

    // Check political content
    if (analysis['hasPoliticalContent'] && !preferences.showPoliticalContent) {
      return false;
    }

    // Check controversial topics
    if (analysis['hasControversialTopics'] &&
        !preferences.showControversialTopics) {
      return false;
    }

    // Check sensitive content
    if (analysis['flags'].contains('extreme_sensitivity') &&
        !preferences.showSensitiveContent) {
      return false;
    }

    return true;
  }

  // Analyze text sensitivity
  SensitivityLevel _analyzeSensitivity(String text) {
    final lowerText = text.toLowerCase();

    // Extreme sensitivity keywords
    final extremeKeywords = [
      'kill',
      'death',
      'suicide',
      'murder',
      'hate',
      'racist',
      'nazi',
      'terrorist',
      'bomb',
      'weapon',
      'drugs',
      'illegal',
    ];

    // High sensitivity keywords
    final highKeywords = [
      'violence',
      'abuse',
      'harassment',
      'bully',
      'threat',
      'danger',
      'scam',
      'fraud',
      'cheat',
      'lie',
      'fake',
    ];

    // Medium sensitivity keywords
    final mediumKeywords = [
      'politics',
      'election',
      'government',
      'protest',
      'demonstration',
      'controversy',
      'scandal',
      'accusation',
      'allegation',
    ];

    // Check for extreme keywords
    for (final keyword in extremeKeywords) {
      if (lowerText.contains(keyword)) {
        return SensitivityLevel.extreme;
      }
    }

    // Check for high keywords
    for (final keyword in highKeywords) {
      if (lowerText.contains(keyword)) {
        return SensitivityLevel.high;
      }
    }

    // Check for medium keywords
    for (final keyword in mediumKeywords) {
      if (lowerText.contains(keyword)) {
        return SensitivityLevel.medium;
      }
    }

    return SensitivityLevel.low;
  }

  // Categorize content
  List<ContentCategory> _categorizeContent(String text) {
    final lowerText = text.toLowerCase();
    final categories = <ContentCategory>{};

    // Technology
    if (lowerText.contains('tech') ||
        lowerText.contains('ai') ||
        lowerText.contains('blockchain') ||
        lowerText.contains('startup')) {
      categories.add(ContentCategory.technology);
    }

    // Finance
    if (lowerText.contains('money') ||
        lowerText.contains('investment') ||
        lowerText.contains('stock') ||
        lowerText.contains('trading')) {
      categories.add(ContentCategory.finance);
    }

    // Real Estate
    if (lowerText.contains('property') ||
        lowerText.contains('real estate') ||
        lowerText.contains('house') ||
        lowerText.contains('apartment')) {
      categories.add(ContentCategory.realEstate);
    }

    // Luxury Lifestyle
    if (lowerText.contains('luxury') ||
        lowerText.contains('billionaire') ||
        lowerText.contains('yacht') ||
        lowerText.contains('private jet')) {
      categories.add(ContentCategory.luxuryLifestyle);
    }

    // Travel
    if (lowerText.contains('travel') ||
        lowerText.contains('vacation') ||
        lowerText.contains('trip') ||
        lowerText.contains('destination')) {
      categories.add(ContentCategory.travel);
    }

    // Business
    if (lowerText.contains('business') ||
        lowerText.contains('company') ||
        lowerText.contains('entrepreneur') ||
        lowerText.contains('ceo')) {
      categories.add(ContentCategory.business);
    }

    // Politics
    if (lowerText.contains('politics') ||
        lowerText.contains('election') ||
        lowerText.contains('government') ||
        lowerText.contains('policy')) {
      categories.add(ContentCategory.politics);
    }

    // Default to technology if no categories found
    if (categories.isEmpty) {
      categories.add(ContentCategory.technology);
    }

    return categories.toList();
  }

  // Check for profanity
  bool _checkProfanity(String text) {
    final lowerText = text.toLowerCase();

    // Basic profanity filter (expand this list)
    final profanityWords = [
      'fuck', 'shit', 'bitch', 'ass', 'damn', 'hell',
      // Add more profanity words as needed
    ];

    for (final word in profanityWords) {
      if (lowerText.contains(word)) {
        return true;
      }
    }

    return false;
  }

  // Check for political content
  bool _checkPoliticalContent(String text) {
    final lowerText = text.toLowerCase();

    final politicalKeywords = [
      'politics',
      'election',
      'vote',
      'democrat',
      'republican',
      'government',
      'policy',
      'law',
      'congress',
      'senate',
      'president',
      'politician',
      'campaign',
    ];

    for (final keyword in politicalKeywords) {
      if (lowerText.contains(keyword)) {
        return true;
      }
    }

    return false;
  }

  // Check for controversial topics
  bool _checkControversialTopics(String text) {
    final lowerText = text.toLowerCase();

    final controversialKeywords = [
      'abortion',
      'gun control',
      'climate change',
      'vaccine',
      'religion',
      'race',
      'gender',
      'lgbtq',
      'immigration',
      'conspiracy',
      'fake news',
      'deep state',
    ];

    for (final keyword in controversialKeywords) {
      if (lowerText.contains(keyword)) {
        return true;
      }
    }

    return false;
  }

  // Get default content preferences
  ContentPreferences _getDefaultPreferences() {
    return ContentPreferences(
      allowedCategories: {
        ContentCategory.technology,
        ContentCategory.finance,
        ContentCategory.realEstate,
        ContentCategory.luxuryLifestyle,
        ContentCategory.travel,
        ContentCategory.artCulture,
        ContentCategory.sports,
        ContentCategory.entertainment,
        ContentCategory.business,
        ContentCategory.investment,
        ContentCategory.sustainability,
        ContentCategory.healthWellness,
        ContentCategory.education,
      },
      maxSensitivityLevel: SensitivityLevel.medium,
      showPoliticalContent: false,
      showControversialTopics: false,
      showSensitiveContent: false,
    );
  }
}
