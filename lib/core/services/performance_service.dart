import 'package:firebase_performance/firebase_performance.dart';

class PerformanceService {
  static final PerformanceService _instance = PerformanceService._internal();
  factory PerformanceService() => _instance;
  PerformanceService._internal();

  final FirebasePerformance _performance = FirebasePerformance.instance;

  // Initialize performance monitoring
  Future<void> initialize() async {
    await _performance.setPerformanceCollectionEnabled(true);
  }

  // Get a trace instance
  Trace startTrace(String traceName) {
    return _performance.newTrace(traceName);
  }

  // Track screen load time
  Future<void> trackScreenLoad(String screenName) async {
    final trace = startTrace('screen_load_$screenName');
    await trace.start();
    await trace.stop();
  }

  // Track API call performance
  Future<void> trackApiCall(
    String endpoint,
    Duration duration, {
    bool success = true,
  }) async {
    final trace = startTrace('api_call_$endpoint');
    await trace.start();
    await trace.stop();
  }

  // Track image load performance
  Future<void> trackImageLoad(
    String imageUrl,
    Duration duration, {
    bool success = true,
  }) async {
    final trace = startTrace('image_load');
    await trace.start();
    await trace.stop();
  }

  // Track user action performance
  Future<void> trackUserAction(String action, Duration duration) async {
    final trace = startTrace('user_action_$action');
    await trace.start();
    await trace.stop();
  }

  // Track database operation performance
  Future<void> trackDatabaseOperation(
    String operation,
    Duration duration, {
    bool success = true,
  }) async {
    final trace = startTrace('database_$operation');
    await trace.start();
    await trace.stop();
  }

  // Track file upload performance
  Future<void> trackFileUpload(
    String fileType,
    int fileSize,
    Duration duration, {
    bool success = true,
  }) async {
    final trace = startTrace('file_upload_$fileType');
    await trace.start();
    await trace.stop();
  }

  // Track app startup time
  Future<void> trackAppStartup(Duration duration) async {
    final trace = startTrace('app_startup');
    await trace.start();
    await trace.stop();
  }

  // Track memory usage
  Future<void> trackMemoryUsage(int memoryUsageMB) async {
    final trace = startTrace('memory_usage');
    await trace.start();
    await trace.stop();
  }

  // Track battery usage
  Future<void> trackBatteryUsage(int batteryLevel) async {
    final trace = startTrace('battery_usage');
    await trace.start();
    await trace.stop();
  }

  // Track network performance
  Future<void> trackNetworkPerformance(
    String endpoint,
    Duration responseTime,
    int dataSize,
  ) async {
    final trace = startTrace('network_performance');
    await trace.start();
    await trace.stop();
  }

  // Track UI rendering performance
  Future<void> trackUIRendering(String widgetName, Duration renderTime) async {
    final trace = startTrace('ui_rendering_$widgetName');
    await trace.start();
    await trace.stop();
  }

  // Track error performance impact
  Future<void> trackErrorImpact(
    String errorType,
    String errorMessage,
    Duration impactTime,
  ) async {
    final trace = startTrace('error_impact');
    await trace.start();
    await trace.stop();
  }

  // Track user engagement metrics
  Future<void> trackUserEngagement(
    String engagementType,
    Duration duration,
  ) async {
    final trace = startTrace('user_engagement_$engagementType');
    await trace.start();
    await trace.stop();
  }

  // Track feature usage
  Future<void> trackFeatureUsage(
    String featureName,
    Map<String, String> attributes,
  ) async {
    final trace = startTrace('feature_usage_$featureName');
    await trace.start();
    await trace.stop();
  }
}
