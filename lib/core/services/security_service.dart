import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

/// Comprehensive security service for the application
/// Handles rate limiting, input validation, audit logging, and security monitoring
class SecurityService {
  static final SecurityService _instance = SecurityService._internal();
  factory SecurityService() => _instance;
  SecurityService._internal();

  // Secure storage instance
  static const _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  // Rate limiting storage
  final Map<String, List<DateTime>> _rateLimitData = {};
  final Map<String, int> _failedAttempts = {};
  final Map<String, DateTime> _lockoutTimes = {};

  // Security audit log
  final List<SecurityEvent> _auditLog = [];

  // Configuration
  static const int maxAuditLogSize = 1000;
  static const Duration lockoutDuration = Duration(minutes: 15);
  static const int maxFailedAttempts = 5;

  /// Rate limiting for API calls and user actions
  bool checkRateLimit(
    String identifier, {
    int maxRequests = 10,
    Duration window = const Duration(minutes: 1),
  }) {
    final now = DateTime.now();
    final windowStart = now.subtract(window);

    // Clean old entries
    _rateLimitData[identifier]?.removeWhere(
      (time) => time.isBefore(windowStart),
    );

    // Initialize if not exists
    _rateLimitData[identifier] ??= [];

    // Check if under limit
    if (_rateLimitData[identifier]!.length >= maxRequests) {
      _logSecurityEvent(SecurityEventType.rateLimitExceeded, identifier, {
        'max_requests': maxRequests,
        'window_minutes': window.inMinutes,
      });
      return false;
    }

    // Add current request
    _rateLimitData[identifier]!.add(now);
    return true;
  }

  /// Secure token management
  Future<void> storeSecureToken(String key, String token) async {
    try {
      await _secureStorage.write(key: key, value: token);
      _logSecurityEvent(SecurityEventType.tokenStored, key, {
        'token_length': token.length,
      });
      debugPrint('🔐 Token stored securely: $key');
    } catch (e) {
      _logSecurityEvent(SecurityEventType.securityError, key, {
        'error': e.toString(),
        'operation': 'store_token',
      });
      debugPrint('🚨 Failed to store secure token: $e');
      rethrow;
    }
  }

  Future<String?> getSecureToken(String key) async {
    try {
      final token = await _secureStorage.read(key: key);
      if (token != null) {
        _logSecurityEvent(SecurityEventType.tokenAccessed, key, {
          'token_length': token.length,
        });
      }
      return token;
    } catch (e) {
      _logSecurityEvent(SecurityEventType.securityError, key, {
        'error': e.toString(),
        'operation': 'get_token',
      });
      debugPrint('🚨 Failed to read secure token: $e');
      return null;
    }
  }

  Future<void> deleteSecureToken(String key) async {
    try {
      await _secureStorage.delete(key: key);
      _logSecurityEvent(SecurityEventType.tokenDeleted, key, {});
      debugPrint('🔐 Token deleted securely: $key');
    } catch (e) {
      _logSecurityEvent(SecurityEventType.securityError, key, {
        'error': e.toString(),
        'operation': 'delete_token',
      });
      debugPrint('🚨 Failed to delete secure token: $e');
    }
  }

  Future<void> clearAllSecureTokens() async {
    try {
      await _secureStorage.deleteAll();
      _logSecurityEvent(SecurityEventType.allTokensCleared, 'system', {});
      debugPrint('🔐 All secure tokens cleared');
    } catch (e) {
      _logSecurityEvent(SecurityEventType.securityError, 'system', {
        'error': e.toString(),
        'operation': 'clear_all_tokens',
      });
      debugPrint('🚨 Failed to clear secure tokens: $e');
    }
  }

  /// Check if user/IP is currently locked out
  bool isLockedOut(String identifier) {
    final lockoutTime = _lockoutTimes[identifier];
    if (lockoutTime == null) return false;

    if (DateTime.now().isAfter(lockoutTime.add(lockoutDuration))) {
      // Lockout expired
      _lockoutTimes.remove(identifier);
      _failedAttempts.remove(identifier);
      return false;
    }

    return true;
  }

  /// Record failed authentication attempt
  void recordFailedAttempt(String identifier, String attemptType) {
    if (isLockedOut(identifier)) return;

    _failedAttempts[identifier] = (_failedAttempts[identifier] ?? 0) + 1;

    _logSecurityEvent(SecurityEventType.authenticationFailed, identifier, {
      'attempt_type': attemptType,
      'failed_count': _failedAttempts[identifier],
    });

    if (_failedAttempts[identifier]! >= maxFailedAttempts) {
      _lockoutTimes[identifier] = DateTime.now();
      _logSecurityEvent(SecurityEventType.accountLocked, identifier, {
        'lockout_duration_minutes': lockoutDuration.inMinutes,
      });
    }
  }

  /// Record successful authentication
  void recordSuccessfulAuth(String identifier, String authType) {
    _failedAttempts.remove(identifier);
    _lockoutTimes.remove(identifier);

    _logSecurityEvent(SecurityEventType.authenticationSuccess, identifier, {
      'auth_type': authType,
    });
  }

  /// Validate and sanitize user input
  ValidationResult validateInput(
    String input,
    InputType type, {
    int? maxLength,
    int? minLength,
    bool allowSpecialChars = false,
  }) {
    if (input.isEmpty) {
      return ValidationResult(false, 'Input cannot be empty');
    }

    // Length validation
    if (maxLength != null && input.length > maxLength) {
      return ValidationResult(
        false,
        'Input too long (max: $maxLength characters)',
      );
    }

    if (minLength != null && input.length < minLength) {
      return ValidationResult(
        false,
        'Input too short (min: $minLength characters)',
      );
    }

    // Type-specific validation
    switch (type) {
      case InputType.email:
        return _validateEmail(input);
      case InputType.username:
        return _validateUsername(input, allowSpecialChars);
      case InputType.password:
        return _validatePassword(input);
      case InputType.text:
        return _validateText(input, allowSpecialChars);
      case InputType.url:
        return _validateUrl(input);
      case InputType.phoneNumber:
        return _validatePhoneNumber(input);
    }
  }

  /// Sanitize input to prevent XSS and injection attacks
  String sanitizeInput(String input) {
    return input
        .replaceAll('<', '&lt;')
        .replaceAll('>', '&gt;')
        .replaceAll('"', '&quot;')
        .replaceAll("'", '&#x27;')
        .replaceAll('&', '&amp;')
        .replaceAll('/', '&#x2F;');
  }

  /// Generate secure random token
  String generateSecureToken({int length = 32}) {
    const chars =
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random.secure();
    return List.generate(
      length,
      (index) => chars[random.nextInt(chars.length)],
    ).join();
  }

  /// Hash sensitive data
  String hashData(String data, {String? salt}) {
    salt ??= generateSecureToken(length: 16);
    final bytes = utf8.encode(data + salt);
    final digest = sha256.convert(bytes);
    return '$salt:${digest.toString()}';
  }

  /// Verify hashed data
  bool verifyHash(String data, String hashedData) {
    final parts = hashedData.split(':');
    if (parts.length != 2) return false;

    final salt = parts[0];
    final expectedHash = parts[1];

    final bytes = utf8.encode(data + salt);
    final digest = sha256.convert(bytes);

    return digest.toString() == expectedHash;
  }

  /// Get security audit log
  List<SecurityEvent> getAuditLog({int? limit}) {
    final events = List<SecurityEvent>.from(_auditLog);
    events.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    if (limit != null && limit < events.length) {
      return events.take(limit).toList();
    }

    return events;
  }

  /// Get security summary
  SecuritySummary getSecuritySummary() {
    final now = DateTime.now();
    final last24Hours = now.subtract(const Duration(hours: 24));

    final recentEvents = _auditLog
        .where((event) => event.timestamp.isAfter(last24Hours))
        .toList();
    final failedLogins = recentEvents
        .where((e) => e.type == SecurityEventType.authenticationFailed)
        .length;
    final successfulLogins = recentEvents
        .where((e) => e.type == SecurityEventType.authenticationSuccess)
        .length;
    final rateLimitViolations = recentEvents
        .where((e) => e.type == SecurityEventType.rateLimitExceeded)
        .length;
    final accountLockouts = recentEvents
        .where((e) => e.type == SecurityEventType.accountLocked)
        .length;

    return SecuritySummary(
      totalEvents: recentEvents.length,
      failedLogins: failedLogins,
      successfulLogins: successfulLogins,
      rateLimitViolations: rateLimitViolations,
      accountLockouts: accountLockouts,
      activeRateLimits: _rateLimitData.length,
      activeLockouts: _lockoutTimes.length,
      securityScore: _calculateSecurityScore(recentEvents),
    );
  }

  /// Clear security data (for testing or reset)
  void clearSecurityData() {
    _rateLimitData.clear();
    _failedAttempts.clear();
    _lockoutTimes.clear();
    _auditLog.clear();
  }

  // Private methods
  void _logSecurityEvent(
    SecurityEventType type,
    String identifier,
    Map<String, dynamic> metadata,
  ) {
    final event = SecurityEvent(
      type: type,
      identifier: identifier,
      timestamp: DateTime.now(),
      metadata: metadata,
    );

    _auditLog.add(event);

    // Cleanup old events
    if (_auditLog.length > maxAuditLogSize) {
      _auditLog.removeAt(0);
    }

    debugPrint('🔒 Security Event: ${type.name} for $identifier');
  }

  ValidationResult _validateEmail(String email) {
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    if (!emailRegex.hasMatch(email)) {
      return ValidationResult(false, 'Invalid email format');
    }
    return ValidationResult(true, 'Valid email');
  }

  ValidationResult _validateUsername(String username, bool allowSpecialChars) {
    if (username.length < 3) {
      return ValidationResult(false, 'Username must be at least 3 characters');
    }

    final regex = allowSpecialChars
        ? RegExp(r'^[a-zA-Z0-9._-]+$')
        : RegExp(r'^[a-zA-Z0-9]+$');

    if (!regex.hasMatch(username)) {
      return ValidationResult(false, 'Username contains invalid characters');
    }

    return ValidationResult(true, 'Valid username');
  }

  ValidationResult _validatePassword(String password) {
    if (password.length < 8) {
      return ValidationResult(false, 'Password must be at least 8 characters');
    }

    if (!password.contains(RegExp(r'[A-Z]'))) {
      return ValidationResult(
        false,
        'Password must contain at least one uppercase letter',
      );
    }

    if (!password.contains(RegExp(r'[a-z]'))) {
      return ValidationResult(
        false,
        'Password must contain at least one lowercase letter',
      );
    }

    if (!password.contains(RegExp(r'[0-9]'))) {
      return ValidationResult(
        false,
        'Password must contain at least one number',
      );
    }

    if (!password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) {
      return ValidationResult(
        false,
        'Password must contain at least one special character',
      );
    }

    return ValidationResult(true, 'Strong password');
  }

  ValidationResult _validateText(String text, bool allowSpecialChars) {
    if (!allowSpecialChars) {
      final regex = RegExp(r'^[a-zA-Z0-9\s]+$');
      if (!regex.hasMatch(text)) {
        return ValidationResult(false, 'Text contains invalid characters');
      }
    }

    // Check for potential script injection
    if (text.toLowerCase().contains('<script>') ||
        text.toLowerCase().contains('javascript:') ||
        text.toLowerCase().contains('onclick=')) {
      return ValidationResult(
        false,
        'Text contains potentially dangerous content',
      );
    }

    return ValidationResult(true, 'Valid text');
  }

  ValidationResult _validateUrl(String url) {
    try {
      final uri = Uri.parse(url);
      if (!uri.hasScheme || (!uri.scheme.startsWith('http'))) {
        return ValidationResult(false, 'Invalid URL format');
      }
      return ValidationResult(true, 'Valid URL');
    } catch (e) {
      return ValidationResult(false, 'Invalid URL format');
    }
  }

  ValidationResult _validatePhoneNumber(String phone) {
    final phoneRegex = RegExp(r'^\+?[1-9]\d{1,14}$');
    if (!phoneRegex.hasMatch(phone.replaceAll(RegExp(r'[\s\-\(\)]'), ''))) {
      return ValidationResult(false, 'Invalid phone number format');
    }
    return ValidationResult(true, 'Valid phone number');
  }

  double _calculateSecurityScore(List<SecurityEvent> recentEvents) {
    if (recentEvents.isEmpty) return 100.0;

    double score = 100.0;

    // Deduct points for security incidents
    final failedLogins = recentEvents
        .where((e) => e.type == SecurityEventType.authenticationFailed)
        .length;
    final rateLimitViolations = recentEvents
        .where((e) => e.type == SecurityEventType.rateLimitExceeded)
        .length;
    final accountLockouts = recentEvents
        .where((e) => e.type == SecurityEventType.accountLocked)
        .length;

    score -= failedLogins * 2;
    score -= rateLimitViolations * 5;
    score -= accountLockouts * 10;

    return score.clamp(0.0, 100.0);
  }
}

// Data classes and enums
enum SecurityEventType {
  authenticationSuccess,
  authenticationFailed,
  rateLimitExceeded,
  accountLocked,
  suspiciousActivity,
  dataAccess,
  privilegeEscalation,
  tokenStored,
  tokenAccessed,
  tokenDeleted,
  allTokensCleared,
  securityError,
}

enum InputType { email, username, password, text, url, phoneNumber }

class SecurityEvent {
  final SecurityEventType type;
  final String identifier;
  final DateTime timestamp;
  final Map<String, dynamic> metadata;

  SecurityEvent({
    required this.type,
    required this.identifier,
    required this.timestamp,
    required this.metadata,
  });
}

class ValidationResult {
  final bool isValid;
  final String message;

  ValidationResult(this.isValid, this.message);
}

class SecuritySummary {
  final int totalEvents;
  final int failedLogins;
  final int successfulLogins;
  final int rateLimitViolations;
  final int accountLockouts;
  final int activeRateLimits;
  final int activeLockouts;
  final double securityScore;

  SecuritySummary({
    required this.totalEvents,
    required this.failedLogins,
    required this.successfulLogins,
    required this.rateLimitViolations,
    required this.accountLockouts,
    required this.activeRateLimits,
    required this.activeLockouts,
    required this.securityScore,
  });
}
