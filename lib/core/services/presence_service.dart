import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'dart:async';

// User presence status
enum PresenceStatus { online, offline, away, busy, invisible }

// Presence data model
class PresenceData {
  final String userId;
  final PresenceStatus status;
  final DateTime lastSeen;
  final DateTime? lastActive;
  final String? deviceInfo;
  final String? location;

  PresenceData({
    required this.userId,
    required this.status,
    required this.lastSeen,
    this.lastActive,
    this.deviceInfo,
    this.location,
  });

  Map<String, dynamic> toJson() => {
    'userId': userId,
    'status': status.name,
    'lastSeen': lastSeen,
    'lastActive': lastActive,
    'deviceInfo': deviceInfo,
    'location': location,
  };

  factory PresenceData.fromJson(Map<String, dynamic> json) {
    return PresenceData(
      userId: json['userId'] ?? '',
      status: PresenceStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => PresenceStatus.offline,
      ),
      lastSeen: (json['lastSeen'] as Timestamp).toDate(),
      lastActive: json['lastActive'] != null
          ? (json['lastActive'] as Timestamp).toDate()
          : null,
      deviceInfo: json['deviceInfo'],
      location: json['location'],
    );
  }
}

class PresenceService {
  static final PresenceService _instance = PresenceService._internal();
  factory PresenceService() => _instance;
  PresenceService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  Timer? _heartbeatTimer;
  StreamSubscription<DocumentSnapshot>? _presenceSubscription;
  bool _isOnline = false;

  // Initialize presence service
  Future<void> initialize() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return;

      // Set initial online status
      await _setOnlineStatus(PresenceStatus.online);

      // Start heartbeat to keep user online
      _startHeartbeat();

      // Listen for app state changes
      _setupAppStateListener();

      debugPrint('✅ Presence service initialized for user: ${currentUser.uid}');
    } catch (e) {
      debugPrint('❌ Error initializing presence service: $e');
    }
  }

  // Set user's online status
  Future<void> _setOnlineStatus(PresenceStatus status) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return;

      final presenceData = PresenceData(
        userId: currentUser.uid,
        status: status,
        lastSeen: DateTime.now(),
        lastActive: status == PresenceStatus.online ? DateTime.now() : null,
        deviceInfo: 'iOS/Android App',
        location: null, // Could be added later with location permission
      );

      await _firestore
          .collection('presence')
          .doc(currentUser.uid)
          .set(presenceData.toJson());

      _isOnline = status == PresenceStatus.online;
      debugPrint('🟢 User presence status set to: ${status.name}');
    } catch (e) {
      debugPrint('❌ Error setting online status: $e');
    }
  }

  // Start heartbeat to keep user online
  void _startHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = Timer.periodic(const Duration(minutes: 1), (timer) async {
      if (_isOnline) {
        await _updateLastActive();
      }
    });
  }

  // Update last active timestamp
  Future<void> _updateLastActive() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return;

      await _firestore.collection('presence').doc(currentUser.uid).update({
        'lastActive': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('❌ Error updating last active: $e');
    }
  }

  // Setup app state listener
  void _setupAppStateListener() {
    // This would typically use WidgetsBindingObserver
    // For now, we'll handle it manually
  }

  // Set user as online
  Future<void> setOnline() async {
    await _setOnlineStatus(PresenceStatus.online);
  }

  // Set user as away
  Future<void> setAway() async {
    await _setOnlineStatus(PresenceStatus.away);
  }

  // Set user as busy
  Future<void> setBusy() async {
    await _setOnlineStatus(PresenceStatus.busy);
  }

  // Set user as offline
  Future<void> setOffline() async {
    await _setOnlineStatus(PresenceStatus.offline);
  }

  // Get user's presence status
  Stream<PresenceData?> getUserPresence(String userId) {
    return _firestore.collection('presence').doc(userId).snapshots().map((doc) {
      if (!doc.exists) return null;

      try {
        return PresenceData.fromJson(doc.data()!);
      } catch (e) {
        debugPrint('❌ Error parsing presence data: $e');
        return null;
      }
    });
  }

  // Get multiple users' presence status
  Stream<List<PresenceData>> getMultipleUsersPresence(List<String> userIds) {
    if (userIds.isEmpty) {
      return Stream.value([]);
    }

    // For now, return an empty list
    // In a real implementation, you'd want to use a more sophisticated approach
    // to combine multiple user presence streams
    return Stream.value([]);
  }

  // Check if user is currently online
  bool isUserOnline(PresenceData? presenceData) {
    if (presenceData == null) return false;

    // Consider user online if they were active in the last 5 minutes
    final fiveMinutesAgo = DateTime.now().subtract(const Duration(minutes: 5));
    return presenceData.lastActive != null &&
        presenceData.lastActive!.isAfter(fiveMinutesAgo) &&
        presenceData.status == PresenceStatus.online;
  }

  // Get online status indicator color
  String getOnlineStatusIndicator(PresenceData? presenceData) {
    if (presenceData == null) return '⚫'; // Offline

    if (isUserOnline(presenceData)) {
      return '🟢'; // Online
    } else if (presenceData.status == PresenceStatus.away) {
      return '🟡'; // Away
    } else if (presenceData.status == PresenceStatus.busy) {
      return '🔴'; // Busy
    } else {
      return '⚫'; // Offline
    }
  }

  // Get online status text
  String getOnlineStatusText(PresenceData? presenceData) {
    if (presenceData == null) return 'Offline';

    if (isUserOnline(presenceData)) {
      return 'Online';
    } else if (presenceData.status == PresenceStatus.away) {
      return 'Away';
    } else if (presenceData.status == PresenceStatus.busy) {
      return 'Busy';
    } else {
      // Calculate time since last seen
      final timeSinceLastSeen = DateTime.now().difference(
        presenceData.lastSeen,
      );
      if (timeSinceLastSeen.inMinutes < 1) {
        return 'Just now';
      } else if (timeSinceLastSeen.inHours < 1) {
        return '${timeSinceLastSeen.inMinutes}m ago';
      } else if (timeSinceLastSeen.inDays < 1) {
        return '${timeSinceLastSeen.inHours}h ago';
      } else {
        return '${timeSinceLastSeen.inDays}d ago';
      }
    }
  }

  // Cleanup presence service
  Future<void> dispose() async {
    try {
      _heartbeatTimer?.cancel();
      _presenceSubscription?.cancel();

      // Set user as offline when disposing
      await setOffline();

      debugPrint('✅ Presence service disposed');
    } catch (e) {
      debugPrint('❌ Error disposing presence service: $e');
    }
  }
}
