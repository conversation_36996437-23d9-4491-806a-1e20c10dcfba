import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:billionaires_social/core/services/universal_user_role_service.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/features/stories/models/unified_story_model.dart';
import 'package:billionaires_social/features/stories/models/shared/story_shared_models.dart';

/// Universal service for content management that works for all users
/// Eliminates hardcoded content creation and management logic
class UniversalContentService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Universal post creation - works for any user with proper limits
  static Future<Post?> createPost({
    required String caption,
    String? mediaUrl,
    MediaType? mediaType,
    String? location,
    List<String>? coAuthorIds,
    List<String>? coAuthorUsernames,
    List<String>? coAuthorAvatars,
    List<String>? mentionedUsers,
    List<String>? hashtags,
    List<String>? mediaTags,
    List<String>? mediaUrls,
    List<MediaType>? mediaTypes,
    String visibility = 'public',
  }) async {
    final currentUserId = UniversalUserRoleService.getCurrentUserId();
    if (currentUserId == null) {
      throw Exception('User must be authenticated to create posts');
    }

    // Check content limits
    final limits = await UniversalUserRoleService.getContentLimits(
      currentUserId,
    );
    final mediaCount = (mediaUrls?.length ?? 0) + (mediaUrl != null ? 1 : 0);

    if (limits.maxMediaPerPost != -1 && mediaCount > limits.maxMediaPerPost) {
      throw Exception(
        'Media limit exceeded. Maximum ${limits.maxMediaPerPost} media items allowed.',
      );
    }

    // Check daily post limit (temporarily disabled while Firebase index is building)
    if (limits.maxPostsPerDay != -1) {
      try {
        final todayPostCount = await _getTodayPostCount(currentUserId);
        if (todayPostCount >= limits.maxPostsPerDay) {
          throw Exception(
            'Daily post limit reached. Maximum ${limits.maxPostsPerDay} posts per day.',
          );
        }
      } catch (e) {
        // If the query fails due to missing index, skip the daily limit check temporarily
        debugPrint(
          '⚠️ Daily post limit check skipped due to index building: $e',
        );
        // Continue with post creation - the index will be ready soon
      }
    }

    try {
      // Get current user data
      final userDoc = await _firestore
          .collection('users')
          .doc(currentUserId)
          .get();
      if (!userDoc.exists) {
        throw Exception('User profile not found');
      }

      final userData = userDoc.data()!;

      debugPrint('🔄 Creating post with transaction for user: $currentUserId');

      // Use transaction to ensure atomicity and prevent race conditions
      String? postId;
      await _firestore.runTransaction((transaction) async {
        // Generate post ID inside transaction to avoid conflicts
        final postRef = _firestore.collection('posts').doc();
        postId = postRef.id;
        final userRef = _firestore.collection('users').doc(currentUserId);

        // Check if user document still exists in transaction
        final userSnapshot = await transaction.get(userRef);
        if (!userSnapshot.exists) {
          throw Exception('User profile not found during transaction');
        }

        // Check if post already exists (prevent duplicate creation)
        final existingPost = await transaction.get(postRef);
        if (existingPost.exists) {
          throw Exception('Post ID conflict detected. Please try again.');
        }

        // Create universal post data inside transaction
        final postData = {
          'id': postId,
          'userId': currentUserId,
          'username': userData['username'] ?? 'unknown',
          'userAvatarUrl': userData['profilePictureUrl'] ?? '',
          'caption': caption,
          'mediaUrl': mediaUrl ?? '',
          'mediaType': mediaType?.name ?? 'text',
          'location': location ?? '',
          'coAuthorIds': coAuthorIds ?? [],
          'coAuthorUsernames': coAuthorUsernames ?? [],
          'coAuthorAvatars': coAuthorAvatars ?? [],
          'mentionedUsers': mentionedUsers ?? [],
          'hashtags': hashtags ?? [],
          'mediaTags': mediaTags ?? [],
          'mediaUrls': mediaUrls ?? [],
          'mediaTypes': mediaTypes?.map((e) => e.name).toList() ?? [],
          'visibility': visibility,
          'status': 'active',
          'likeCount': 0,
          'commentCount': 0,
          'shareCount': 0,
          'viewCount': 0,
          'isPinned': false,
          'isDeleted': false,
          'isArchived': false,
          'isPublic': visibility == 'public',
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        };

        // Create the post
        transaction.set(postRef, postData);

        // Update user's post count atomically
        transaction.update(userRef, {
          'postCount': FieldValue.increment(1),
          'updatedAt': FieldValue.serverTimestamp(),
        });

        debugPrint('✅ Post and user count updated in transaction');
      });

      if (postId == null) {
        throw Exception('Failed to generate post ID');
      }

      debugPrint('✅ Universal post created successfully: $postId');

      // Return created post (convert from Firestore data with retry logic)
      Post? result;
      int retryCount = 0;
      const maxRetries = 3;

      while (result == null && retryCount < maxRetries) {
        try {
          final createdDoc = await _firestore
              .collection('posts')
              .doc(postId)
              .get();
          if (createdDoc.exists) {
            final docData = createdDoc.data()!;
            // Handle potential null timestamp during server processing
            if (docData['createdAt'] == null) {
              debugPrint('⏳ Waiting for server timestamp to be processed...');
              await Future.delayed(
                Duration(milliseconds: 500 * (retryCount + 1)),
              );
              retryCount++;
              continue;
            }
            result = Post.fromJson(docData);
          } else {
            throw Exception('Post document not found after creation');
          }
        } catch (e) {
          debugPrint(
            '⚠️ Retry $retryCount failed to retrieve created post: $e',
          );
          retryCount++;
          if (retryCount >= maxRetries) {
            throw Exception(
              'Failed to retrieve created post after $maxRetries attempts',
            );
          }
          await Future.delayed(Duration(milliseconds: 500 * retryCount));
        }
      }

      return result;
    } on FirebaseException catch (e) {
      debugPrint('❌ Firebase error creating post: ${e.code} - ${e.message}');

      // Handle specific Firebase errors
      switch (e.code) {
        case 'failed-precondition':
          throw Exception(
            'Post creation failed due to a data conflict. Please try again.',
          );
        case 'permission-denied':
          throw Exception('You do not have permission to create posts.');
        case 'unavailable':
          throw Exception('Service temporarily unavailable. Please try again.');
        case 'deadline-exceeded':
          throw Exception(
            'Request timed out. Please check your connection and try again.',
          );
        default:
          throw Exception(
            'Failed to create post: ${e.message ?? 'Unknown error'}',
          );
      }
    } catch (e) {
      debugPrint('❌ Error creating post: $e');

      // Handle common error patterns
      final errorMessage = e.toString().toLowerCase();
      if (errorMessage.contains('network') ||
          errorMessage.contains('connection')) {
        throw Exception(
          'Network error. Please check your internet connection and try again.',
        );
      } else if (errorMessage.contains('timeout')) {
        throw Exception('Request timed out. Please try again.');
      } else if (errorMessage.contains('permission')) {
        throw Exception('Permission denied. Please check your account status.');
      }

      rethrow;
    }
  }

  /// Universal story creation - works for any user with proper limits
  static Future<UnifiedStory?> createStory({
    required String mediaUrl,
    required StoryMediaType mediaType,
    required Duration duration,
    StoryPrivacy privacy = StoryPrivacy.public,
    List<String>? allowedTo,
    List<String>? hiddenFromUserIds,
    String? caption,
    Map<String, dynamic>? stickers,
    Map<String, dynamic>? filters,
  }) async {
    final currentUserId = UniversalUserRoleService.getCurrentUserId();
    if (currentUserId == null) {
      throw Exception('User must be authenticated to create stories');
    }

    // Check content limits
    final limits = await UniversalUserRoleService.getContentLimits(
      currentUserId,
    );

    if (limits.maxStoriesPerDay != -1) {
      try {
        final todayStoryCount = await _getTodayStoryCount(currentUserId);
        if (todayStoryCount >= limits.maxStoriesPerDay) {
          throw Exception(
            'Daily story limit reached. Maximum ${limits.maxStoriesPerDay} stories per day.',
          );
        }
      } catch (e) {
        // If the query fails due to missing index, skip the daily limit check temporarily
        debugPrint(
          '⚠️ Daily story limit check skipped due to index building: $e',
        );
        // Continue with story creation - the index will be ready soon
      }
    }

    try {
      // Get current user data
      final userDoc = await _firestore
          .collection('users')
          .doc(currentUserId)
          .get();
      if (!userDoc.exists) {
        throw Exception('User profile not found');
      }

      final userData = userDoc.data()!;
      final storyId = _firestore.collection('stories').doc().id;
      final now = DateTime.now();
      final expiresAt = now.add(const Duration(hours: 24));

      // Create universal story data
      final storyData = {
        'id': storyId,
        'userId': currentUserId,
        'userName': userData['name'] ?? 'Unknown',
        'userAvatarUrl': userData['profilePictureUrl'] ?? '',
        'mediaUrl': mediaUrl,
        'mediaType': mediaType.name,
        'duration': duration.inMilliseconds,
        'privacy': privacy.name,
        'visibility': StoryVisibility.public.name,
        'allowedTo': allowedTo ?? [],
        'hiddenFromUserIds': hiddenFromUserIds ?? [],
        'caption': caption,
        'stickers': stickers ?? {},
        'filters': filters ?? {},
        'viewers': [],
        'viewCount': 0,
        'replyCount': 0,
        'createdAt': FieldValue.serverTimestamp(),
        'expiresAt': Timestamp.fromDate(expiresAt),
        'timestamp': FieldValue.serverTimestamp(),
        'isActive': true,
      };

      // Save story to Firestore
      await _firestore.collection('stories').doc(storyId).set(storyData);

      debugPrint('✅ Universal story created successfully: $storyId');

      // Return created story
      return UnifiedStory(
        id: storyId,
        userId: currentUserId,
        userName: userData['name'] ?? 'Unknown',
        userAvatarUrl: userData['profilePictureUrl'] ?? '',
        mediaUrl: mediaUrl,
        mediaType: mediaType,
        createdAt: now,
        expiresAt: expiresAt,
        duration: duration,
        timestamp: now,
        privacy: privacy,
        allowedTo: allowedTo ?? [],
        hiddenFromUserIds: hiddenFromUserIds ?? [],
        caption: caption,
      );
    } catch (e) {
      debugPrint('❌ Error creating story: $e');
      rethrow;
    }
  }

  /// Universal content deletion - works for any content the user owns
  static Future<bool> deleteContent({
    required String contentId,
    required ContentType contentType,
  }) async {
    final currentUserId = UniversalUserRoleService.getCurrentUserId();
    if (currentUserId == null) {
      throw Exception('User must be authenticated to delete content');
    }

    try {
      final collection = contentType == ContentType.post ? 'posts' : 'stories';
      final doc = await _firestore.collection(collection).doc(contentId).get();

      if (!doc.exists) {
        throw Exception('Content not found');
      }

      final contentData = doc.data()!;
      final contentOwnerId = contentData['userId'] as String;

      // Check if user owns the content
      if (contentOwnerId != currentUserId) {
        throw Exception('You can only delete your own content');
      }

      // Delete the content
      await _firestore.collection(collection).doc(contentId).delete();

      // Update user's content count
      if (contentType == ContentType.post) {
        await _firestore.collection('users').doc(currentUserId).update({
          'postCount': FieldValue.increment(-1),
          'updatedAt': FieldValue.serverTimestamp(),
        });
      }

      debugPrint('✅ Content deleted successfully: $contentId');
      return true;
    } catch (e) {
      debugPrint('❌ Error deleting content: $e');
      return false;
    }
  }

  /// Universal content archiving - works for any content the user owns
  static Future<bool> archiveContent({
    required String contentId,
    required ContentType contentType,
    required bool archive,
  }) async {
    final currentUserId = UniversalUserRoleService.getCurrentUserId();
    if (currentUserId == null) {
      throw Exception('User must be authenticated to archive content');
    }

    try {
      final collection = contentType == ContentType.post ? 'posts' : 'stories';
      final doc = await _firestore.collection(collection).doc(contentId).get();

      if (!doc.exists) {
        throw Exception('Content not found');
      }

      final contentData = doc.data()!;
      final contentOwnerId = contentData['userId'] as String;

      // Check if user owns the content
      if (contentOwnerId != currentUserId) {
        throw Exception('You can only archive your own content');
      }

      // Update archive status
      await _firestore.collection(collection).doc(contentId).update({
        'status': archive ? 'archived' : 'active',
        'updatedAt': FieldValue.serverTimestamp(),
      });

      debugPrint(
        '✅ Content ${archive ? 'archived' : 'unarchived'} successfully: $contentId',
      );
      return true;
    } catch (e) {
      debugPrint('❌ Error archiving content: $e');
      return false;
    }
  }

  // Helper methods
  static Future<int> _getTodayPostCount(String userId) async {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);

    final query = await _firestore
        .collection('posts')
        .where('userId', isEqualTo: userId)
        .where(
          'createdAt',
          isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay),
        )
        .get();

    return query.docs.length;
  }

  static Future<int> _getTodayStoryCount(String userId) async {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);

    final query = await _firestore
        .collection('stories')
        .where('userId', isEqualTo: userId)
        .where(
          'createdAt',
          isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay),
        )
        .get();

    return query.docs.length;
  }
}

/// Content types for universal operations
enum ContentType { post, story }
