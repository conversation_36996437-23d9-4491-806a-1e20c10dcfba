import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/core/services/analytics_service.dart';

enum ReportReason {
  spam,
  inappropriate,
  harassment,
  fakeNews,
  copyright,
  violence,
  other,
}

enum ContentSeverity { safe, warning, moderate, severe, blocked }

class ContentModerationService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final AnalyticsService _analyticsService = getIt<AnalyticsService>();

  // Spam detection patterns
  static const List<String> _spamPatterns = [
    'buy now',
    'click here',
    'limited time',
    'act now',
    'make money fast',
    'earn money',
    'work from home',
    'get rich quick',
    'investment opportunity',
    'crypto investment',
    'bitcoin investment',
    'forex trading',
    'mlm opportunity',
    'pyramid scheme',
  ];

  // Inappropriate content patterns
  static const List<String> _inappropriatePatterns = [
    'hate speech',
    'discrimination',
    'violence',
    'threat',
    'harassment',
    'bullying',
  ];

  // Rate limiting for content creation
  static const int _maxPostsPerHour = 10;
  static const int _maxPostsPerDay = 50;

  /// Moderate content before posting
  Future<ContentSeverity> moderateContent(String content) async {
    final lowerContent = content.toLowerCase();

    // Check for spam patterns
    for (final pattern in _spamPatterns) {
      if (lowerContent.contains(pattern)) {
        await _analyticsService.logContentFlagged(
          contentType: 'spam',
          pattern: pattern,
          severity: ContentSeverity.moderate,
        );
        return ContentSeverity.moderate;
      }
    }

    // Check for inappropriate content
    for (final pattern in _inappropriatePatterns) {
      if (lowerContent.contains(pattern)) {
        await _analyticsService.logContentFlagged(
          contentType: 'inappropriate',
          pattern: pattern,
          severity: ContentSeverity.severe,
        );
        return ContentSeverity.severe;
      }
    }

    // Check for excessive caps (shouting)
    final capsRatio =
        content.replaceAll(RegExp(r'[^A-Z]'), '').length / content.length;
    if (capsRatio > 0.7 && content.length > 20) {
      await _analyticsService.logContentFlagged(
        contentType: 'excessive_caps',
        pattern: 'caps_ratio_${capsRatio.toStringAsFixed(2)}',
        severity: ContentSeverity.warning,
      );
      return ContentSeverity.warning;
    }

    // Check for repetitive characters
    if (_hasRepetitiveCharacters(content)) {
      await _analyticsService.logContentFlagged(
        contentType: 'repetitive',
        pattern: 'repetitive_chars',
        severity: ContentSeverity.warning,
      );
      return ContentSeverity.warning;
    }

    return ContentSeverity.safe;
  }

  /// Check if user has exceeded rate limits
  Future<bool> checkRateLimit() async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return false;

    final now = DateTime.now();
    final oneHourAgo = now.subtract(const Duration(hours: 1));
    final oneDayAgo = now.subtract(const Duration(days: 1));

    try {
      // Check hourly limit
      final hourlyPosts = await _firestore
          .collection('posts')
          .where('userId', isEqualTo: currentUser.uid)
          .where('createdAt', isGreaterThan: oneHourAgo)
          .get();

      if (hourlyPosts.docs.length >= _maxPostsPerHour) {
        await _analyticsService.logRateLimitExceeded(
          limitType: 'hourly',
          userId: currentUser.uid,
        );
        return false;
      }

      // Check daily limit
      final dailyPosts = await _firestore
          .collection('posts')
          .where('userId', isEqualTo: currentUser.uid)
          .where('createdAt', isGreaterThan: oneDayAgo)
          .get();

      if (dailyPosts.docs.length >= _maxPostsPerDay) {
        await _analyticsService.logRateLimitExceeded(
          limitType: 'daily',
          userId: currentUser.uid,
        );
        return false;
      }

      return true;
    } catch (e) {
      debugPrint('Error checking rate limit: $e');
      return true; // Allow posting if rate limit check fails
    }
  }

  /// Report a post
  Future<void> reportPost(
    String postId,
    ReportReason reason,
    String? additionalInfo,
  ) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) throw Exception('User not authenticated');

    try {
      final reportData = {
        'postId': postId,
        'reportedBy': currentUser.uid,
        'reason': reason.name,
        'additionalInfo': additionalInfo,
        'timestamp': FieldValue.serverTimestamp(),
        'status': 'pending', // pending, reviewed, resolved, dismissed
        'reviewedBy': null,
        'reviewedAt': null,
        'action': null, // warn, delete, ban, none
      };

      await _firestore.collection('reports').add(reportData);

      // Update post with report count
      await _firestore.collection('posts').doc(postId).update({
        'reportCount': FieldValue.increment(1),
        'lastReportedAt': FieldValue.serverTimestamp(),
      });

      // Log analytics
      await _analyticsService.logPostReported(
        postId: postId,
        reason: reason.name,
        reportedBy: currentUser.uid,
      );

      debugPrint('✅ Post reported successfully: $postId');
    } catch (e) {
      debugPrint('❌ Error reporting post: $e');
      rethrow;
    }
  }

  /// Get user's moderation status
  Future<Map<String, dynamic>> getUserModerationStatus(String userId) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      final data = userDoc.data() ?? {};

      return {
        'isWarned': data['isWarned'] ?? false,
        'warningCount': data['warningCount'] ?? 0,
        'isSuspended': data['isSuspended'] ?? false,
        'suspensionEndDate': data['suspensionEndDate'],
        'reportCount': data['reportCount'] ?? 0,
        'lastWarningAt': data['lastWarningAt'],
        'moderationLevel':
            data['moderationLevel'] ??
            'normal', // normal, warned, suspended, banned
      };
    } catch (e) {
      debugPrint('Error getting user moderation status: $e');
      return {
        'isWarned': false,
        'warningCount': 0,
        'isSuspended': false,
        'suspensionEndDate': null,
        'reportCount': 0,
        'lastWarningAt': null,
        'moderationLevel': 'normal',
      };
    }
  }

  /// Check if content contains repetitive characters
  bool _hasRepetitiveCharacters(String content) {
    if (content.length < 10) return false;

    for (int i = 0; i < content.length - 2; i++) {
      final char = content[i];
      if (char == content[i + 1] && char == content[i + 2]) {
        return true;
      }
    }
    return false;
  }

  /// Get moderation statistics for admin dashboard
  Future<Map<String, dynamic>> getModerationStats() async {
    try {
      final now = DateTime.now();
      final oneDayAgo = now.subtract(const Duration(days: 1));
      final oneWeekAgo = now.subtract(const Duration(days: 7));

      // Get recent reports (last 24 hours)
      final recentReports = await _firestore
          .collection('reports')
          .where('timestamp', isGreaterThan: oneDayAgo)
          .get();

      // Get weekly reports
      final weeklyReports = await _firestore
          .collection('reports')
          .where('timestamp', isGreaterThan: oneWeekAgo)
          .get();

      // Get posts with high report counts
      final flaggedPosts = await _firestore
          .collection('posts')
          .where('reportCount', isGreaterThan: 3)
          .get();

      // Get suspended users
      final suspendedUsers = await _firestore
          .collection('users')
          .where('isSuspended', isEqualTo: true)
          .get();

      return {
        'reportsLast24h': recentReports.docs.length,
        'reportsLastWeek': weeklyReports.docs.length,
        'flaggedPosts': flaggedPosts.docs.length,
        'suspendedUsers': suspendedUsers.docs.length,
        'pendingReports': recentReports.docs
            .where((doc) => doc.data()['status'] == 'pending')
            .length,
      };
    } catch (e) {
      debugPrint('Error getting moderation stats: $e');
      return {
        'reportsLast24h': 0,
        'reportsLastWeek': 0,
        'flaggedPosts': 0,
        'suspendedUsers': 0,
        'pendingReports': 0,
      };
    }
  }
}
