import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';

enum UserRole { user, business, admin }

class RoleService {
  static final RoleService _instance = RoleService._internal();
  factory RoleService() => _instance;
  RoleService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Get user role from Firestore
  Future<UserRole> getUserRole(String userId) async {
    try {
      debugPrint('🔍 RoleService: Fetching role for user: $userId');

      final doc = await _firestore.collection('users').doc(userId).get();
      if (!doc.exists) {
        debugPrint(
          '⚠️ RoleService: User document not found, defaulting to user role',
        );
        return UserRole.user;
      }

      final data = doc.data()!;
      final isAdmin = data['isAdmin'] ?? false;
      final isBusinessAccount = data['isBusinessAccount'] ?? false;

      if (isAdmin) {
        debugPrint('👑 RoleService: User is ADMIN');
        return UserRole.admin;
      } else if (isBusinessAccount) {
        debugPrint('🏢 RoleService: User is BUSINESS');
        return UserRole.business;
      } else {
        debugPrint('👤 RoleService: User is REGULAR');
        return UserRole.user;
      }
    } catch (e) {
      debugPrint('❌ RoleService: Error fetching user role: $e');
      // Default to user role on error for security
      return UserRole.user;
    }
  }

  /// Check if user requires 2FA
  Future<bool> requires2FA(String userId) async {
    try {
      final doc = await _firestore.collection('users').doc(userId).get();
      if (!doc.exists) return false;

      final data = doc.data()!;
      final isAdmin = data['isAdmin'] ?? false;
      final requires2FA = data['requires2FA'] ?? false;

      // Admins always require 2FA
      if (isAdmin) return true;

      return requires2FA;
    } catch (e) {
      debugPrint('❌ RoleService: Error checking 2FA requirement: $e');
      return false;
    }
  }

  /// Get navigation route based on user role
  String getNavigationRoute(UserRole role) {
    switch (role) {
      case UserRole.admin:
        return '/admin/dashboard';
      case UserRole.business:
        return '/business/dashboard';
      case UserRole.user:
        return '/main';
    }
  }

  /// Check if social login should be shown for current user
  bool shouldShowSocialLogin(UserRole role) {
    // Admins should never see social login options
    return role != UserRole.admin;
  }

  /// Get current authenticated user
  User? getCurrentUser() {
    return _auth.currentUser;
  }

  /// Get current user's role
  Future<UserRole> getCurrentUserRole() async {
    final currentUser = getCurrentUser();
    if (currentUser == null) {
      debugPrint(
        '⚠️ RoleService: No authenticated user, defaulting to user role',
      );
      return UserRole.user;
    }
    return getUserRole(currentUser.uid);
  }

  /// Check if current user requires 2FA
  Future<bool> currentUserRequires2FA() async {
    final currentUser = getCurrentUser();
    if (currentUser == null) return false;
    return requires2FA(currentUser.uid);
  }

  /// Get navigation route for current user
  Future<String> getCurrentUserNavigationRoute() async {
    final role = await getCurrentUserRole();
    return getNavigationRoute(role);
  }

  /// Check if current user has admin privileges
  Future<bool> isCurrentUserAdmin() async {
    final role = await getCurrentUserRole();
    return role == UserRole.admin;
  }

  /// Check if current user has business privileges
  Future<bool> isCurrentUserBusiness() async {
    final role = await getCurrentUserRole();
    return role == UserRole.business;
  }

  /// Check if current user can access admin features
  Future<bool> canAccessAdminFeatures() async {
    return await isCurrentUserAdmin();
  }

  /// Check if current user can access business features
  Future<bool> canAccessBusinessFeatures() async {
    final role = await getCurrentUserRole();
    return role == UserRole.admin || role == UserRole.business;
  }
}
