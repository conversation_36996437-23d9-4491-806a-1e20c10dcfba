import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// Universal service for managing user roles and relationships
/// Eliminates hardcoded user ID checks throughout the app
class UniversalUserRoleService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Get current authenticated user ID
  static String? getCurrentUserId() {
    return _auth.currentUser?.uid;
  }

  /// Universal check: Is this the current user?
  /// Replaces all hardcoded `if (userId == currentUserId)` checks
  static bool isCurrentUser(String? userId) {
    if (userId == null) return false;
    final currentUserId = getCurrentUserId();
    return currentUserId != null && currentUserId == userId;
  }

  /// Universal check: Is user authenticated?
  static bool isAuthenticated() {
    return _auth.currentUser != null;
  }

  /// Get user relationship to current user
  static Future<UserRelationship> getUserRelationship(String targetUserId) async {
    final currentUserId = getCurrentUserId();
    
    if (currentUserId == null) {
      return UserRelationship.unauthenticated;
    }
    
    if (currentUserId == targetUserId) {
      return UserRelationship.self;
    }

    try {
      // Check if following
      final followingDoc = await _firestore
          .collection('users')
          .doc(currentUserId)
          .collection('following')
          .doc(targetUserId)
          .get();

      if (followingDoc.exists) {
        // Check if close friend
        final isCloseFriend = followingDoc.data()?['isCloseFriend'] ?? false;
        return isCloseFriend 
            ? UserRelationship.closeFriend 
            : UserRelationship.following;
      }

      // Check if follower (they follow us)
      final followerDoc = await _firestore
          .collection('users')
          .doc(targetUserId)
          .collection('following')
          .doc(currentUserId)
          .get();

      if (followerDoc.exists) {
        return UserRelationship.follower;
      }

      // Check if blocked
      final blockedDoc = await _firestore
          .collection('users')
          .doc(currentUserId)
          .collection('blocked')
          .doc(targetUserId)
          .get();

      if (blockedDoc.exists) {
        return UserRelationship.blocked;
      }

      return UserRelationship.stranger;
    } catch (e) {
      debugPrint('❌ Error getting user relationship: $e');
      return UserRelationship.stranger;
    }
  }

  /// Get user permissions for content actions
  static Future<UserPermissions> getUserPermissions(String targetUserId) async {
    final relationship = await getUserRelationship(targetUserId);
    final targetUserData = await _getUserData(targetUserId);
    
    return UserPermissions(
      canViewProfile: _canViewProfile(relationship, targetUserData),
      canViewPosts: _canViewPosts(relationship, targetUserData),
      canViewStories: _canViewStories(relationship, targetUserData),
      canSendMessage: _canSendMessage(relationship, targetUserData),
      canFollow: _canFollow(relationship),
      canUnfollow: _canUnfollow(relationship),
      canBlock: _canBlock(relationship),
      canReport: _canReport(relationship),
      canEdit: relationship == UserRelationship.self,
      canDelete: relationship == UserRelationship.self,
      canArchive: relationship == UserRelationship.self,
      canPin: relationship == UserRelationship.self,
    );
  }

  /// Get user's account type and privileges
  static Future<UserAccountType> getUserAccountType(String? userId) async {
    if (userId == null) return UserAccountType.guest;
    
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (!userDoc.exists) return UserAccountType.guest;
      
      final userData = userDoc.data()!;
      
      if (userData['isAdmin'] == true) {
        return UserAccountType.admin;
      } else if (userData['isBillionaire'] == true) {
        return UserAccountType.billionaire;
      } else if (userData['isVerified'] == true) {
        return UserAccountType.verified;
      } else if (userData['isBusinessAccount'] == true) {
        return UserAccountType.business;
      } else {
        return UserAccountType.regular;
      }
    } catch (e) {
      debugPrint('❌ Error getting user account type: $e');
      return UserAccountType.regular;
    }
  }

  /// Get universal content limits based on account type
  static Future<ContentLimits> getContentLimits(String? userId) async {
    final accountType = await getUserAccountType(userId);
    
    switch (accountType) {
      case UserAccountType.admin:
        return ContentLimits.unlimited();
      case UserAccountType.billionaire:
        return ContentLimits.premium();
      case UserAccountType.verified:
        return ContentLimits.verified();
      case UserAccountType.business:
        return ContentLimits.business();
      case UserAccountType.regular:
        return ContentLimits.regular();
      case UserAccountType.guest:
        return ContentLimits.guest();
    }
  }

  // Private helper methods
  static Future<Map<String, dynamic>?> _getUserData(String userId) async {
    try {
      final doc = await _firestore.collection('users').doc(userId).get();
      return doc.exists ? doc.data() : null;
    } catch (e) {
      return null;
    }
  }

  static bool _canViewProfile(UserRelationship relationship, Map<String, dynamic>? userData) {
    if (relationship == UserRelationship.blocked) return false;
    if (relationship == UserRelationship.self) return true;
    
    final isPrivate = userData?['isPrivate'] ?? false;
    if (!isPrivate) return true;
    
    return relationship == UserRelationship.following || 
           relationship == UserRelationship.closeFriend;
  }

  static bool _canViewPosts(UserRelationship relationship, Map<String, dynamic>? userData) {
    return _canViewProfile(relationship, userData);
  }

  static bool _canViewStories(UserRelationship relationship, Map<String, dynamic>? userData) {
    if (relationship == UserRelationship.blocked) return false;
    if (relationship == UserRelationship.self) return true;
    
    // Stories require following relationship
    return relationship == UserRelationship.following || 
           relationship == UserRelationship.closeFriend;
  }

  static bool _canSendMessage(UserRelationship relationship, Map<String, dynamic>? userData) {
    if (relationship == UserRelationship.blocked) return false;
    if (relationship == UserRelationship.self) return false;
    
    final allowMessages = userData?['allowMessages'] ?? true;
    return allowMessages;
  }

  static bool _canFollow(UserRelationship relationship) {
    return relationship == UserRelationship.stranger || 
           relationship == UserRelationship.follower;
  }

  static bool _canUnfollow(UserRelationship relationship) {
    return relationship == UserRelationship.following || 
           relationship == UserRelationship.closeFriend;
  }

  static bool _canBlock(UserRelationship relationship) {
    return relationship != UserRelationship.self && 
           relationship != UserRelationship.blocked;
  }

  static bool _canReport(UserRelationship relationship) {
    return relationship != UserRelationship.self;
  }
}

/// Enum for user relationships
enum UserRelationship {
  unauthenticated,
  self,
  following,
  closeFriend,
  follower,
  stranger,
  blocked,
}

/// Enum for account types
enum UserAccountType {
  guest,
  regular,
  business,
  verified,
  billionaire,
  admin,
}

/// User permissions data class
class UserPermissions {
  final bool canViewProfile;
  final bool canViewPosts;
  final bool canViewStories;
  final bool canSendMessage;
  final bool canFollow;
  final bool canUnfollow;
  final bool canBlock;
  final bool canReport;
  final bool canEdit;
  final bool canDelete;
  final bool canArchive;
  final bool canPin;

  const UserPermissions({
    required this.canViewProfile,
    required this.canViewPosts,
    required this.canViewStories,
    required this.canSendMessage,
    required this.canFollow,
    required this.canUnfollow,
    required this.canBlock,
    required this.canReport,
    required this.canEdit,
    required this.canDelete,
    required this.canArchive,
    required this.canPin,
  });
}

/// Content limits based on account type
class ContentLimits {
  final int maxMediaPerPost;
  final int maxStoriesPerDay;
  final int maxPostsPerDay;
  final bool canCreateReels;
  final bool canGoLive;
  final bool canCreatePolls;
  final bool canAddLinks;
  final bool canMentionUnlimited;

  const ContentLimits({
    required this.maxMediaPerPost,
    required this.maxStoriesPerDay,
    required this.maxPostsPerDay,
    required this.canCreateReels,
    required this.canGoLive,
    required this.canCreatePolls,
    required this.canAddLinks,
    required this.canMentionUnlimited,
  });

  factory ContentLimits.unlimited() => const ContentLimits(
    maxMediaPerPost: -1, // -1 means unlimited
    maxStoriesPerDay: -1,
    maxPostsPerDay: -1,
    canCreateReels: true,
    canGoLive: true,
    canCreatePolls: true,
    canAddLinks: true,
    canMentionUnlimited: true,
  );

  factory ContentLimits.premium() => const ContentLimits(
    maxMediaPerPost: 20,
    maxStoriesPerDay: 50,
    maxPostsPerDay: 100,
    canCreateReels: true,
    canGoLive: true,
    canCreatePolls: true,
    canAddLinks: true,
    canMentionUnlimited: true,
  );

  factory ContentLimits.verified() => const ContentLimits(
    maxMediaPerPost: 15,
    maxStoriesPerDay: 30,
    maxPostsPerDay: 50,
    canCreateReels: true,
    canGoLive: true,
    canCreatePolls: true,
    canAddLinks: true,
    canMentionUnlimited: false,
  );

  factory ContentLimits.business() => const ContentLimits(
    maxMediaPerPost: 10,
    maxStoriesPerDay: 25,
    maxPostsPerDay: 30,
    canCreateReels: true,
    canGoLive: false,
    canCreatePolls: true,
    canAddLinks: true,
    canMentionUnlimited: false,
  );

  factory ContentLimits.regular() => const ContentLimits(
    maxMediaPerPost: 5,
    maxStoriesPerDay: 15,
    maxPostsPerDay: 20,
    canCreateReels: true,
    canGoLive: false,
    canCreatePolls: false,
    canAddLinks: false,
    canMentionUnlimited: false,
  );

  factory ContentLimits.guest() => const ContentLimits(
    maxMediaPerPost: 0,
    maxStoriesPerDay: 0,
    maxPostsPerDay: 0,
    canCreateReels: false,
    canGoLive: false,
    canCreatePolls: false,
    canAddLinks: false,
    canMentionUnlimited: false,
  );
}
