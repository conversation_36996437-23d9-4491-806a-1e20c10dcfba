import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

/// Enhanced image optimization service with memory management and performance optimization
class EnhancedImageOptimizationService {
  static const int maxWidth = 1080;
  static const int maxHeight = 1080;
  static const int quality = 85;
  static const int thumbnailSize = 300;
  static const int maxFileSize = 5 * 1024 * 1024; // 5MB

  /// Optimize image for upload with fallback to original
  static Future<File?> optimizeForUpload(File imageFile) async {
    try {
      // Check if file exists
      if (!await imageFile.exists()) {
        debugPrint('❌ Image file does not exist: ${imageFile.path}');
        return null;
      }

      // Check file size - if already small, return original
      final fileSize = await imageFile.length();
      if (fileSize < 1024 * 1024) {
        // Less than 1MB
        debugPrint('✅ Image already optimized ($fileSize bytes)');
        return imageFile;
      }

      // Generate optimized file path
      final tempDir = await getTemporaryDirectory();
      final fileName = path.basenameWithoutExtension(imageFile.path);
      final optimizedPath = path.join(
        tempDir.path,
        '${fileName}_optimized_${DateTime.now().millisecondsSinceEpoch}.jpg',
      );

      // Compress image
      final result = await FlutterImageCompress.compressAndGetFile(
        imageFile.absolute.path,
        optimizedPath,
        minWidth: maxWidth,
        minHeight: maxHeight,
        quality: quality,
        format: CompressFormat.jpeg,
      );

      if (result != null) {
        final resultFile = File(result.path);
        final optimizedSize = await resultFile.length();
        final compressionRatio = ((fileSize - optimizedSize) / fileSize * 100)
            .round();

        debugPrint(
          '✅ Image optimized: $fileSize → $optimizedSize bytes ($compressionRatio% reduction)',
        );
        return resultFile;
      } else {
        debugPrint('⚠️ Image compression failed, using original');
        return imageFile;
      }
    } catch (e) {
      debugPrint('❌ Image optimization error: $e');
      return imageFile; // Return original on error
    }
  }

  /// Generate thumbnail for image
  static Future<File?> generateThumbnail(File imageFile) async {
    try {
      if (!await imageFile.exists()) {
        debugPrint(
          '❌ Image file does not exist for thumbnail: ${imageFile.path}',
        );
        return null;
      }

      final tempDir = await getTemporaryDirectory();
      final fileName = path.basenameWithoutExtension(imageFile.path);
      final thumbnailPath = path.join(
        tempDir.path,
        '${fileName}_thumb_${DateTime.now().millisecondsSinceEpoch}.jpg',
      );

      final result = await FlutterImageCompress.compressAndGetFile(
        imageFile.absolute.path,
        thumbnailPath,
        minWidth: thumbnailSize,
        minHeight: thumbnailSize,
        quality: 70,
        format: CompressFormat.jpeg,
      );

      if (result != null) {
        final resultFile = File(result.path);
        debugPrint('✅ Thumbnail generated: ${await resultFile.length()} bytes');
        return resultFile;
      } else {
        debugPrint('⚠️ Thumbnail generation failed');
        return null;
      }
    } catch (e) {
      debugPrint('❌ Thumbnail generation error: $e');
      return null;
    }
  }

  /// Optimize multiple images in batch
  static Future<List<File?>> optimizeBatch(List<File> imageFiles) async {
    final results = <File?>[];

    for (int i = 0; i < imageFiles.length; i++) {
      debugPrint('🔄 Optimizing image ${i + 1}/${imageFiles.length}');
      final optimized = await optimizeForUpload(imageFiles[i]);
      results.add(optimized);

      // Add small delay to prevent overwhelming the system
      if (i < imageFiles.length - 1) {
        await Future.delayed(const Duration(milliseconds: 100));
      }
    }

    return results;
  }

  /// Get image dimensions without loading full image
  static Future<Map<String, int>?> getImageDimensions(File imageFile) async {
    try {
      // This is a simplified version - in production you might want to use
      // a more efficient method to get dimensions without loading the full image
      // final bytes = await imageFile.readAsBytes();

      // For now, we'll use a placeholder implementation
      // In a real app, you'd use a package like 'image' to get actual dimensions
      return {'width': maxWidth, 'height': maxHeight};
    } catch (e) {
      debugPrint('❌ Error getting image dimensions: $e');
      return null;
    }
  }

  /// Check if image needs optimization
  static Future<bool> needsOptimization(File imageFile) async {
    try {
      final fileSize = await imageFile.length();

      // Needs optimization if:
      // 1. File size > 2MB
      // 2. File is not JPEG format
      // 3. Dimensions are too large (would need actual dimension check)

      if (fileSize > 2 * 1024 * 1024) return true;

      final extension = path.extension(imageFile.path).toLowerCase();
      if (extension != '.jpg' && extension != '.jpeg') return true;

      return false;
    } catch (e) {
      debugPrint('❌ Error checking optimization need: $e');
      return true; // Assume needs optimization on error
    }
  }

  /// Progressive image loading - generate multiple quality levels
  static Future<Map<String, File?>> generateProgressiveImages(
    File imageFile,
  ) async {
    final results = <String, File?>{};

    try {
      final tempDir = await getTemporaryDirectory();
      final fileName = path.basenameWithoutExtension(imageFile.path);
      final timestamp = DateTime.now().millisecondsSinceEpoch;

      // Low quality for fast loading
      final lowQualityPath = path.join(
        tempDir.path,
        '${fileName}_low_$timestamp.jpg',
      );
      final lowQuality = await FlutterImageCompress.compressAndGetFile(
        imageFile.absolute.path,
        lowQualityPath,
        minWidth: 400,
        minHeight: 400,
        quality: 30,
        format: CompressFormat.jpeg,
      );
      results['low'] = lowQuality != null ? File(lowQuality.path) : null;

      // Medium quality for balanced loading
      final mediumQualityPath = path.join(
        tempDir.path,
        '${fileName}_medium_$timestamp.jpg',
      );
      final mediumQuality = await FlutterImageCompress.compressAndGetFile(
        imageFile.absolute.path,
        mediumQualityPath,
        minWidth: 720,
        minHeight: 720,
        quality: 60,
        format: CompressFormat.jpeg,
      );
      results['medium'] = mediumQuality != null
          ? File(mediumQuality.path)
          : null;

      // High quality for final display
      final highQuality = await optimizeForUpload(imageFile);
      results['high'] = highQuality;

      debugPrint('✅ Progressive images generated: ${results.keys.join(', ')}');
    } catch (e) {
      debugPrint('❌ Progressive image generation error: $e');
    }

    return results;
  }

  /// Clean up temporary optimized files
  static Future<void> cleanupTempFiles() async {
    try {
      final tempDir = await getTemporaryDirectory();
      final files = tempDir.listSync();

      int deletedCount = 0;
      for (final file in files) {
        if (file is File) {
          final fileName = path.basename(file.path);
          if (fileName.contains('_optimized_') ||
              fileName.contains('_thumb_') ||
              fileName.contains('_low_') ||
              fileName.contains('_medium_')) {
            // Delete files older than 1 hour
            final stat = await file.stat();
            final age = DateTime.now().difference(stat.modified);
            if (age.inHours > 1) {
              await file.delete();
              deletedCount++;
            }
          }
        }
      }

      if (deletedCount > 0) {
        debugPrint('🧹 Cleaned up $deletedCount temporary image files');
      }
    } catch (e) {
      debugPrint('❌ Error cleaning up temp files: $e');
    }
  }

  /// Get optimization statistics
  static Map<String, dynamic> getOptimizationStats() {
    return {
      'maxWidth': maxWidth,
      'maxHeight': maxHeight,
      'quality': quality,
      'thumbnailSize': thumbnailSize,
      'maxFileSize': maxFileSize,
      'supportedFormats': ['JPEG', 'PNG', 'WebP'],
    };
  }
}
