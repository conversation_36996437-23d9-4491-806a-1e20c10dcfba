import 'package:flutter/foundation.dart';
import 'package:billionaires_social/core/services/analytics/base_analytics_service.dart';
import 'package:billionaires_social/core/services/analytics/user_analytics_service.dart';
import 'package:billionaires_social/core/services/analytics/content_analytics_service.dart';
import 'package:billionaires_social/core/services/analytics/performance_analytics_service.dart';

/// Unified analytics service that coordinates all specialized analytics services
class UnifiedAnalyticsService extends BaseAnalyticsService {
  static final UnifiedAnalyticsService _instance =
      UnifiedAnalyticsService._internal();
  factory UnifiedAnalyticsService() => _instance;
  UnifiedAnalyticsService._internal();

  // Specialized analytics services
  late final UserAnalyticsService _userAnalytics;
  late final ContentAnalyticsService _contentAnalytics;
  late final PerformanceAnalyticsService _performanceAnalytics;

  bool _isInitialized = false;

  /// Initialize all analytics services
  @override
  Future<void> initialize() async {
    if (_isInitialized) return;

    await super.initialize();

    _userAnalytics = UserAnalyticsService();
    _contentAnalytics = ContentAnalyticsService();
    _performanceAnalytics = PerformanceAnalyticsService();

    await _userAnalytics.initialize();
    await _contentAnalytics.initialize();
    await _performanceAnalytics.initialize();

    _isInitialized = true;
    debugPrint('📊 Unified Analytics Service initialized');
  }

  /// Get user analytics service
  UserAnalyticsService get user => _userAnalytics;

  /// Get content analytics service
  ContentAnalyticsService get content => _contentAnalytics;

  /// Get performance analytics service
  PerformanceAnalyticsService get performance => _performanceAnalytics;

  /// Convenience methods for common analytics events

  // User events
  Future<void> trackUserLogin(String method) async {
    await _userAnalytics.logLogin(method: method);
  }

  Future<void> trackUserSignUp(String method) async {
    await _userAnalytics.logSignUp(method: method);
  }

  Future<void> trackUserLogout() async {
    await _userAnalytics.logLogout();
  }

  Future<void> trackProfileView(
    String viewedUserId, {
    String? sourceScreen,
  }) async {
    await _userAnalytics.logProfileView(
      viewedUserId: viewedUserId,
      sourceScreen: sourceScreen,
    );
  }

  Future<void> trackUserFollow(
    String targetUserId, {
    String? sourceScreen,
  }) async {
    await _userAnalytics.logUserFollow(
      targetUserId: targetUserId,
      sourceScreen: sourceScreen,
    );
  }

  // Content events
  Future<void> trackPostCreate({
    required String postId,
    required String postType,
    bool hasImage = false,
    bool hasVideo = false,
    bool hasText = false,
    int? contentLength,
    int? mediaCount,
  }) async {
    await _contentAnalytics.logPostCreate(
      postId: postId,
      postType: postType,
      hasImage: hasImage,
      hasVideo: hasVideo,
      hasText: hasText,
      contentLength: contentLength,
      mediaCount: mediaCount,
    );
  }

  Future<void> trackPostView({
    required String postId,
    required String authorId,
    String? sourceScreen,
    Duration? viewDuration,
  }) async {
    await _contentAnalytics.logPostView(
      postId: postId,
      authorId: authorId,
      sourceScreen: sourceScreen,
      viewDuration: viewDuration,
    );
  }

  Future<void> trackPostLike({
    required String postId,
    required String authorId,
    required bool isLiked,
    String? sourceScreen,
  }) async {
    await _contentAnalytics.logPostLike(
      postId: postId,
      authorId: authorId,
      isLiked: isLiked,
      sourceScreen: sourceScreen,
    );
  }

  Future<void> trackStoryCreate({
    required String storyId,
    required String storyType,
    bool hasImage = false,
    bool hasVideo = false,
    bool hasText = false,
    int? duration,
  }) async {
    await _contentAnalytics.logStoryCreate(
      storyId: storyId,
      storyType: storyType,
      hasImage: hasImage,
      hasVideo: hasVideo,
      hasText: hasText,
      duration: duration,
    );
  }

  Future<void> trackStoryView({
    required String storyId,
    required String authorId,
    Duration? viewDuration,
    bool viewedCompletely = false,
  }) async {
    await _contentAnalytics.logStoryView(
      storyId: storyId,
      authorId: authorId,
      viewDuration: viewDuration,
      viewedCompletely: viewedCompletely,
    );
  }

  // Performance events
  Future<void> trackAppOpen() async {
    await _performanceAnalytics.logAppOpen();
  }

  Future<void> trackAppClose({Duration? sessionDuration}) async {
    await _performanceAnalytics.logAppClose(sessionDuration: sessionDuration);
  }

  Future<void> trackLoadTime({
    required String screenName,
    required Duration loadTime,
    String? operationType,
    bool success = true,
  }) async {
    await _performanceAnalytics.logLoadTime(
      screenName: screenName,
      loadTime: loadTime,
      operationType: operationType,
      success: success,
    );
  }

  Future<void> trackError({
    required String errorType,
    required String errorMessage,
    String? context,
    bool isFatal = false,
  }) async {
    await _performanceAnalytics.logError(
      errorType: errorType,
      errorMessage: errorMessage,
      context: context,
      isFatal: isFatal,
    );
  }

  Future<void> trackMemoryUsage({
    required double memoryUsageMB,
    required int activeTimers,
    required int activeSubscriptions,
    String? context,
  }) async {
    await _performanceAnalytics.logMemoryUsage(
      memoryUsageMB: memoryUsageMB,
      activeTimers: activeTimers,
      activeSubscriptions: activeSubscriptions,
      context: context,
    );
  }

  // Search events
  Future<void> trackSearch({
    required String searchQuery,
    required String searchCategory,
    required int resultsCount,
    Duration? searchTime,
  }) async {
    await logEventSafely(
      eventName: AnalyticsEvents.search,
      parameters: {
        AnalyticsParameters.searchQuery: searchQuery.length > 50
            ? searchQuery.substring(0, 50)
            : searchQuery,
        AnalyticsParameters.searchCategory: searchCategory,
        AnalyticsParameters.resultsCount: resultsCount,
        'search_time_milliseconds': searchTime?.inMilliseconds,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> trackSearchResultClick({
    required String searchQuery,
    required String resultId,
    required int resultPosition,
  }) async {
    await logEventSafely(
      eventName: AnalyticsEvents.searchResultClick,
      parameters: {
        AnalyticsParameters.searchQuery: searchQuery.length > 50
            ? searchQuery.substring(0, 50)
            : searchQuery,
        'result_id': resultId,
        AnalyticsParameters.resultPosition: resultPosition,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  // Place events
  Future<void> trackPlaceView({
    required String placeId,
    required String placeName,
    String? placeCategory,
  }) async {
    await logEventSafely(
      eventName: AnalyticsEvents.placeView,
      parameters: {
        AnalyticsParameters.placeId: placeId,
        AnalyticsParameters.placeName: placeName,
        AnalyticsParameters.placeCategory: placeCategory,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> trackPlaceReservation({
    required String placeId,
    required String placeName,
    required DateTime reservationTime,
    required int partySize,
  }) async {
    await logEventSafely(
      eventName: AnalyticsEvents.placeReservation,
      parameters: {
        AnalyticsParameters.placeId: placeId,
        AnalyticsParameters.placeName: placeName,
        AnalyticsParameters.reservationTime: reservationTime.toIso8601String(),
        AnalyticsParameters.partySize: partySize,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  // Messaging events
  Future<void> trackMessageSend({
    required String recipientId,
    required String messageType,
    int? messageLength,
  }) async {
    await logEventSafely(
      eventName: AnalyticsEvents.messagesSend,
      parameters: {
        'recipient_id': recipientId,
        'message_type': messageType,
        'message_length': messageLength,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> trackMessagesView({
    required String conversationId,
    required int messageCount,
  }) async {
    await logEventSafely(
      eventName: AnalyticsEvents.messagesView,
      parameters: {
        'conversation_id': conversationId,
        'message_count': messageCount,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  /// Set comprehensive user properties
  Future<void> setUserProperties({
    required String userId,
    String? userType,
    bool? isVerified,
    bool? isBillionaire,
    String? location,
    DateTime? accountCreatedDate,
    int? totalPosts,
    int? totalStories,
    int? totalFollowers,
    int? totalFollowing,
    String? preferredLanguage,
  }) async {
    await _userAnalytics.setUserProperties(
      userId: userId,
      userType: userType,
      isVerified: isVerified,
      isBillionaire: isBillionaire,
      location: location,
      accountCreatedDate: accountCreatedDate,
      totalPosts: totalPosts,
      totalStories: totalStories,
      totalFollowers: totalFollowers,
      totalFollowing: totalFollowing,
      preferredLanguage: preferredLanguage,
    );
  }

  /// Get analytics summary
  Map<String, dynamic> getAnalyticsSummary() {
    return {
      'initialized': _isInitialized,
      'services': {
        'user_analytics': _isInitialized,
        'content_analytics': _isInitialized,
        'performance_analytics': _isInitialized,
      },
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}
