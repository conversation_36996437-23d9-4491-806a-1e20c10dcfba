import 'package:billionaires_social/core/services/analytics/base_analytics_service.dart';

/// Content-specific analytics service
class ContentAnalyticsService extends BaseAnalyticsService {
  static final ContentAnalyticsService _instance =
      ContentAnalyticsService._internal();
  factory ContentAnalyticsService() => _instance;
  ContentAnalyticsService._internal();

  /// Track post creation events
  Future<void> logPostCreate({
    required String postId,
    required String postType,
    bool hasImage = false,
    bool hasVideo = false,
    bool hasText = false,
    int? contentLength,
    int? mediaCount,
    List<String>? tags,
  }) async {
    await logEventSafely(
      eventName: AnalyticsEvents.postCreate,
      parameters: {
        AnalyticsParameters.contentId: postId,
        AnalyticsParameters.postType: postType,
        AnalyticsParameters.hasImage: hasImage,
        AnalyticsParameters.hasVideo: hasVideo,
        AnalyticsParameters.hasText: hasText,
        AnalyticsParameters.contentLength: contentLength,
        AnalyticsParameters.mediaCount: mediaCount,
        'tags_count': tags?.length,
        'tags': tags?.take(5).join(','), // Limit to first 5 tags
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track post interaction events
  Future<void> logPostView({
    required String postId,
    required String authorId,
    String? sourceScreen,
    Duration? viewDuration,
  }) async {
    await logEventSafely(
      eventName: AnalyticsEvents.postView,
      parameters: {
        AnalyticsParameters.contentId: postId,
        'author_id': authorId,
        AnalyticsParameters.sourceScreen: sourceScreen,
        'view_duration_seconds': viewDuration?.inSeconds,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> logPostLike({
    required String postId,
    required String authorId,
    required bool isLiked,
    String? sourceScreen,
  }) async {
    await logEventSafely(
      eventName: AnalyticsEvents.postLike,
      parameters: {
        AnalyticsParameters.contentId: postId,
        'author_id': authorId,
        'action': isLiked ? 'like' : 'unlike',
        AnalyticsParameters.sourceScreen: sourceScreen,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> logPostShare({
    required String postId,
    required String authorId,
    required String shareMethod,
    String? sourceScreen,
  }) async {
    await logEventSafely(
      eventName: AnalyticsEvents.postShare,
      parameters: {
        AnalyticsParameters.contentId: postId,
        'author_id': authorId,
        'share_method': shareMethod,
        AnalyticsParameters.sourceScreen: sourceScreen,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> logPostComment({
    required String postId,
    required String authorId,
    required String commentId,
    int? commentLength,
    String? sourceScreen,
  }) async {
    await logEventSafely(
      eventName: AnalyticsEvents.postComment,
      parameters: {
        AnalyticsParameters.contentId: postId,
        'author_id': authorId,
        'comment_id': commentId,
        'comment_length': commentLength,
        AnalyticsParameters.sourceScreen: sourceScreen,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track story events
  Future<void> logStoryCreate({
    required String storyId,
    required String storyType,
    bool hasImage = false,
    bool hasVideo = false,
    bool hasText = false,
    int? duration,
    List<String>? tags,
  }) async {
    await logEventSafely(
      eventName: AnalyticsEvents.storyCreate,
      parameters: {
        AnalyticsParameters.contentId: storyId,
        'story_type': storyType,
        AnalyticsParameters.hasImage: hasImage,
        AnalyticsParameters.hasVideo: hasVideo,
        AnalyticsParameters.hasText: hasText,
        'story_duration_seconds': duration,
        'tags_count': tags?.length,
        'tags': tags?.take(5).join(','),
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> logStoryView({
    required String storyId,
    required String authorId,
    Duration? viewDuration,
    bool viewedCompletely = false,
  }) async {
    await logEventSafely(
      eventName: AnalyticsEvents.storyView,
      parameters: {
        AnalyticsParameters.contentId: storyId,
        'author_id': authorId,
        'view_duration_seconds': viewDuration?.inSeconds,
        'viewed_completely': viewedCompletely,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> logStoryShare({
    required String storyId,
    required String authorId,
    required String shareMethod,
  }) async {
    await logEventSafely(
      eventName: AnalyticsEvents.storyShare,
      parameters: {
        AnalyticsParameters.contentId: storyId,
        'author_id': authorId,
        'share_method': shareMethod,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track feed interactions
  Future<void> logFeedView({
    required String feedType,
    int? postsCount,
    String? sourceScreen,
  }) async {
    await logEventSafely(
      eventName: AnalyticsEvents.feedView,
      parameters: {
        'feed_type': feedType,
        'posts_count': postsCount,
        AnalyticsParameters.sourceScreen: sourceScreen,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> logFeedRefresh({
    required String feedType,
    int? newPostsCount,
    Duration? refreshDuration,
  }) async {
    await logEventSafely(
      eventName: AnalyticsEvents.feedRefresh,
      parameters: {
        'feed_type': feedType,
        'new_posts_count': newPostsCount,
        'refresh_duration_seconds': refreshDuration?.inSeconds,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> logFeedScroll({
    required String feedType,
    required int postsViewed,
    required Duration scrollDuration,
    double? scrollDistance,
  }) async {
    await logEventSafely(
      eventName: AnalyticsEvents.feedScroll,
      parameters: {
        'feed_type': feedType,
        'posts_viewed': postsViewed,
        'scroll_duration_seconds': scrollDuration.inSeconds,
        'scroll_distance': scrollDistance,
        'posts_per_second':
            postsViewed / scrollDuration.inSeconds.clamp(1, double.infinity),
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track content discovery
  Future<void> logContentDiscovery({
    required String contentId,
    required String contentType,
    required String discoveryMethod,
    String? sourceScreen,
    int? position,
  }) async {
    await logEventSafely(
      eventName: 'content_discovery',
      parameters: {
        AnalyticsParameters.contentId: contentId,
        AnalyticsParameters.contentType: contentType,
        'discovery_method': discoveryMethod,
        AnalyticsParameters.sourceScreen: sourceScreen,
        'content_position': position,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track content performance metrics
  Future<void> logContentPerformance({
    required String contentId,
    required String contentType,
    required int views,
    required int likes,
    required int comments,
    required int shares,
    Duration? averageViewTime,
  }) async {
    final engagementRate =
        (likes + comments + shares) / views.clamp(1, double.infinity);

    await logEventSafely(
      eventName: 'content_performance',
      parameters: {
        AnalyticsParameters.contentId: contentId,
        AnalyticsParameters.contentType: contentType,
        'views': views,
        'likes': likes,
        'comments': comments,
        'shares': shares,
        'engagement_rate': engagementRate,
        'average_view_time_seconds': averageViewTime?.inSeconds,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track content moderation events
  Future<void> logContentModeration({
    required String contentId,
    required String contentType,
    required String moderationAction,
    String? reason,
    bool automated = false,
  }) async {
    await logEventSafely(
      eventName: 'content_moderation',
      parameters: {
        AnalyticsParameters.contentId: contentId,
        AnalyticsParameters.contentType: contentType,
        'moderation_action': moderationAction,
        'moderation_reason': reason,
        'automated_moderation': automated,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track content trends
  Future<void> logContentTrend({
    required String trendType,
    required String trendValue,
    required int popularity,
    String? category,
  }) async {
    await logEventSafely(
      eventName: 'content_trend',
      parameters: {
        'trend_type': trendType,
        'trend_value': trendValue,
        'popularity_score': popularity,
        'trend_category': category,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }
}
