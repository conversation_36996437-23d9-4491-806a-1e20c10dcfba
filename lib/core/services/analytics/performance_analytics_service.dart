import 'package:billionaires_social/core/services/analytics/base_analytics_service.dart';

/// Performance-specific analytics service
class PerformanceAnalyticsService extends BaseAnalyticsService {
  static final PerformanceAnalyticsService _instance =
      PerformanceAnalyticsService._internal();
  factory PerformanceAnalyticsService() => _instance;
  PerformanceAnalyticsService._internal();

  /// Track app lifecycle events
  Future<void> logAppOpen() async {
    await logEventSafely(
      eventName: AnalyticsEvents.appOpen,
      parameters: {
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> logAppClose({Duration? sessionDuration}) async {
    await logEventSafely(
      eventName: AnalyticsEvents.appClose,
      parameters: {
        'session_duration_seconds': sessionDuration?.inSeconds,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> logAppBackground() async {
    await logEventSafely(
      eventName: AnalyticsEvents.appBackground,
      parameters: {
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> logAppForeground() async {
    await logEventSafely(
      eventName: AnalyticsEvents.appForeground,
      parameters: {
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track performance metrics
  Future<void> logPerformanceMetric({
    required String metricName,
    required double metricValue,
    String? screenName,
    String? operationName,
    Map<String, Object?>? additionalData,
  }) async {
    final parameters = <String, Object?>{
      AnalyticsParameters.metricName: metricName,
      AnalyticsParameters.metricValue: metricValue,
      AnalyticsParameters.screenName: screenName,
      AnalyticsParameters.operationName: operationName,
      AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
    };

    if (additionalData != null) {
      parameters.addAll(additionalData);
    }

    await logEventSafely(
      eventName: AnalyticsEvents.performanceMetric,
      parameters: parameters,
    );
  }

  /// Track load times
  Future<void> logLoadTime({
    required String screenName,
    required Duration loadTime,
    String? operationType,
    bool success = true,
  }) async {
    await logEventSafely(
      eventName: AnalyticsEvents.loadTime,
      parameters: {
        AnalyticsParameters.screenName: screenName,
        'load_time_milliseconds': loadTime.inMilliseconds,
        'operation_type': operationType,
        AnalyticsParameters.success: success,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track memory usage
  Future<void> logMemoryUsage({
    required double memoryUsageMB,
    required int activeTimers,
    required int activeSubscriptions,
    String? context,
  }) async {
    await logEventSafely(
      eventName: 'memory_usage',
      parameters: {
        'memory_usage_mb': memoryUsageMB,
        'active_timers': activeTimers,
        'active_subscriptions': activeSubscriptions,
        'memory_context': context,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track network performance
  Future<void> logNetworkRequest({
    required String endpoint,
    required String method,
    required Duration responseTime,
    required int statusCode,
    int? requestSize,
    int? responseSize,
  }) async {
    await logEventSafely(
      eventName: 'network_request',
      parameters: {
        'endpoint': endpoint,
        'http_method': method,
        'response_time_milliseconds': responseTime.inMilliseconds,
        'status_code': statusCode,
        'request_size_bytes': requestSize,
        'response_size_bytes': responseSize,
        AnalyticsParameters.success: statusCode >= 200 && statusCode < 300,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track frame rendering performance
  Future<void> logFramePerformance({
    required double averageFPS,
    required int droppedFrames,
    required Duration measurementDuration,
    String? screenName,
  }) async {
    await logEventSafely(
      eventName: 'frame_performance',
      parameters: {
        'average_fps': averageFPS,
        'dropped_frames': droppedFrames,
        'measurement_duration_seconds': measurementDuration.inSeconds,
        'frame_drop_rate':
            droppedFrames /
            (averageFPS * measurementDuration.inSeconds).clamp(
              1,
              double.infinity,
            ),
        AnalyticsParameters.screenName: screenName,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track cache performance
  Future<void> logCachePerformance({
    required String cacheType,
    required int hitCount,
    required int missCount,
    required double hitRate,
    int? cacheSize,
    int? maxCacheSize,
  }) async {
    await logEventSafely(
      eventName: 'cache_performance',
      parameters: {
        'cache_type': cacheType,
        'hit_count': hitCount,
        'miss_count': missCount,
        'hit_rate': hitRate,
        'cache_size': cacheSize,
        'max_cache_size': maxCacheSize,
        'cache_utilization': cacheSize != null && maxCacheSize != null
            ? cacheSize / maxCacheSize
            : null,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track database performance
  Future<void> logDatabaseOperation({
    required String operation,
    required Duration executionTime,
    required bool success,
    String? collection,
    int? recordCount,
    String? errorType,
  }) async {
    await logEventSafely(
      eventName: 'database_operation',
      parameters: {
        'operation_type': operation,
        'execution_time_milliseconds': executionTime.inMilliseconds,
        AnalyticsParameters.success: success,
        'collection_name': collection,
        'record_count': recordCount,
        AnalyticsParameters.errorType: errorType,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track image loading performance
  Future<void> logImageLoadPerformance({
    required String imageUrl,
    required Duration loadTime,
    required bool success,
    int? imageSizeBytes,
    int? imageWidth,
    int? imageHeight,
    bool fromCache = false,
  }) async {
    await logEventSafely(
      eventName: 'image_load_performance',
      parameters: {
        'image_url_hash': imageUrl.hashCode
            .toString(), // Don't log full URL for privacy
        'load_time_milliseconds': loadTime.inMilliseconds,
        AnalyticsParameters.success: success,
        'image_size_bytes': imageSizeBytes,
        'image_width': imageWidth,
        'image_height': imageHeight,
        'loaded_from_cache': fromCache,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track search performance
  Future<void> logSearchPerformance({
    required String searchQuery,
    required Duration searchTime,
    required int resultsCount,
    required bool success,
    String? searchType,
  }) async {
    await logEventSafely(
      eventName: 'search_performance',
      parameters: {
        'search_query_length': searchQuery.length,
        'search_time_milliseconds': searchTime.inMilliseconds,
        'results_count': resultsCount,
        AnalyticsParameters.success: success,
        'search_type': searchType,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track error events
  Future<void> logError({
    required String errorType,
    required String errorMessage,
    String? context,
    String? stackTrace,
    bool isFatal = false,
  }) async {
    await logEventSafely(
      eventName: AnalyticsEvents.error,
      parameters: {
        AnalyticsParameters.errorType: errorType,
        AnalyticsParameters.errorMessage: errorMessage.length > 100
            ? errorMessage.substring(0, 100)
            : errorMessage,
        'error_context': context,
        'has_stack_trace': stackTrace != null,
        'is_fatal': isFatal,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track crash reports
  Future<void> logCrashReport({
    required String crashType,
    required String crashMessage,
    String? stackTrace,
    Map<String, Object?>? deviceInfo,
  }) async {
    final parameters = <String, Object?>{
      'crash_type': crashType,
      'crash_message': crashMessage.length > 100
          ? crashMessage.substring(0, 100)
          : crashMessage,
      'has_stack_trace': stackTrace != null,
      AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
    };

    if (deviceInfo != null) {
      parameters.addAll(deviceInfo);
    }

    await logEventSafely(
      eventName: AnalyticsEvents.crashReport,
      parameters: parameters,
    );
  }

  /// Track startup performance
  Future<void> logStartupPerformance({
    required Duration totalStartupTime,
    required Duration initializationTime,
    required Duration firstFrameTime,
    bool coldStart = true,
  }) async {
    await logEventSafely(
      eventName: 'startup_performance',
      parameters: {
        'total_startup_time_milliseconds': totalStartupTime.inMilliseconds,
        'initialization_time_milliseconds': initializationTime.inMilliseconds,
        'first_frame_time_milliseconds': firstFrameTime.inMilliseconds,
        'cold_start': coldStart,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }
}
