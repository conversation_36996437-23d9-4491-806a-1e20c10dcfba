import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';

/// Base analytics service providing core functionality
abstract class BaseAnalyticsService {
  final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;

  /// Initialize analytics
  Future<void> initialize() async {
    await _analytics.setAnalyticsCollectionEnabled(true);
    await _analytics.setSessionTimeoutDuration(const Duration(minutes: 30));
    debugPrint('📊 Analytics initialized');
  }

  /// Log event safely with error handling
  Future<void> logEventSafely({
    required String eventName,
    Map<String, Object?>? parameters,
  }) async {
    try {
      await _analytics.logEvent(
        name: eventName,
        parameters: parameters?.cast<String, Object>(),
      );
      debugPrint('📊 Event logged: $eventName');
    } catch (e) {
      debugPrint('🚨 Failed to log event $eventName: $e');
    }
  }

  /// Set user properties safely
  Future<void> setUserPropertySafely({
    required String name,
    required String? value,
  }) async {
    try {
      await _analytics.setUserProperty(name: name, value: value);
      debugPrint('📊 User property set: $name = $value');
    } catch (e) {
      debugPrint('🚨 Failed to set user property $name: $e');
    }
  }

  /// Set user ID safely
  Future<void> setUserIdSafely(String? userId) async {
    try {
      await _analytics.setUserId(id: userId);
      debugPrint('📊 User ID set: $userId');
    } catch (e) {
      debugPrint('🚨 Failed to set user ID: $e');
    }
  }

  /// Get analytics instance for direct access if needed
  FirebaseAnalytics get analytics => _analytics;
}

/// Analytics event categories
class AnalyticsEvents {
  // App lifecycle
  static const String appOpen = 'app_open';
  static const String appClose = 'app_close';
  static const String appBackground = 'app_background';
  static const String appForeground = 'app_foreground';

  // Authentication
  static const String login = 'login';
  static const String logout = 'logout';
  static const String signUp = 'sign_up';
  static const String passwordReset = 'password_reset';

  // User actions
  static const String profileView = 'profile_view';
  static const String profileEdit = 'profile_edit';
  static const String settingsView = 'settings_view';

  // Content
  static const String postCreate = 'post_create';
  static const String postView = 'post_view';
  static const String postLike = 'post_like';
  static const String postShare = 'post_share';
  static const String postComment = 'post_comment';

  // Stories
  static const String storyCreate = 'story_create';
  static const String storyView = 'story_view';
  static const String storyShare = 'story_share';

  // Social
  static const String userFollow = 'user_follow';
  static const String userUnfollow = 'user_unfollow';
  static const String messagesSend = 'message_send';
  static const String messagesView = 'messages_view';

  // Search
  static const String search = 'search';
  static const String searchResultClick = 'search_result_click';

  // Feed
  static const String feedView = 'feed_view';
  static const String feedRefresh = 'feed_refresh';
  static const String feedScroll = 'feed_scroll';

  // Places
  static const String placeView = 'place_view';
  static const String placeCheckin = 'place_checkin';
  static const String placeReservation = 'place_reservation';

  // Errors
  static const String error = 'app_error';
  static const String crashReport = 'crash_report';

  // Performance
  static const String performanceMetric = 'performance_metric';
  static const String loadTime = 'load_time';
}

/// Analytics parameter keys
class AnalyticsParameters {
  // Common
  static const String userId = 'user_id';
  static const String contentType = 'content_type';
  static const String contentId = 'content_id';
  static const String method = 'method';
  static const String success = 'success';
  static const String errorType = 'error_type';
  static const String errorMessage = 'error_message';
  static const String duration = 'duration';
  static const String timestamp = 'timestamp';

  // User properties
  static const String userType = 'user_type';
  static const String isVerified = 'is_verified';
  static const String isBillionaire = 'is_billionaire';
  static const String location = 'location';
  static const String accountAge = 'account_age';
  static const String followersCount = 'followers_count';
  static const String followingCount = 'following_count';

  // Content properties
  static const String postType = 'post_type';
  static const String hasImage = 'has_image';
  static const String hasVideo = 'has_video';
  static const String hasText = 'has_text';
  static const String contentLength = 'content_length';
  static const String mediaCount = 'media_count';

  // Interaction properties
  static const String interactionType = 'interaction_type';
  static const String targetUserId = 'target_user_id';
  static const String sourceScreen = 'source_screen';
  static const String targetScreen = 'target_screen';

  // Search properties
  static const String searchQuery = 'search_query';
  static const String searchCategory = 'search_category';
  static const String resultsCount = 'results_count';
  static const String resultPosition = 'result_position';

  // Performance properties
  static const String metricName = 'metric_name';
  static const String metricValue = 'metric_value';
  static const String screenName = 'screen_name';
  static const String operationName = 'operation_name';

  // Place properties
  static const String placeId = 'place_id';
  static const String placeName = 'place_name';
  static const String placeCategory = 'place_category';
  static const String reservationTime = 'reservation_time';
  static const String partySize = 'party_size';
}

/// User property keys
class UserProperties {
  static const String userType = 'user_type';
  static const String isVerified = 'is_verified';
  static const String isBillionaire = 'is_billionaire';
  static const String location = 'location';
  static const String accountCreatedDate = 'account_created_date';
  static const String lastActiveDate = 'last_active_date';
  static const String totalPosts = 'total_posts';
  static const String totalStories = 'total_stories';
  static const String totalFollowers = 'total_followers';
  static const String totalFollowing = 'total_following';
  static const String preferredLanguage = 'preferred_language';
  static const String appVersion = 'app_version';
  static const String deviceType = 'device_type';
  static const String osVersion = 'os_version';
}

/// Analytics configuration
class AnalyticsConfig {
  static const Duration sessionTimeout = Duration(minutes: 30);
  static const int maxEventParameters = 25;
  static const int maxParameterValueLength = 100;
  static const int maxEventNameLength = 40;
  static const int maxUserPropertyNameLength = 24;
  static const int maxUserPropertyValueLength = 36;

  /// Validate event name
  static bool isValidEventName(String eventName) {
    if (eventName.isEmpty || eventName.length > maxEventNameLength) {
      return false;
    }
    // Event names must start with a letter and contain only letters, numbers, and underscores
    return RegExp(r'^[a-zA-Z][a-zA-Z0-9_]*$').hasMatch(eventName);
  }

  /// Validate parameter key
  static bool isValidParameterKey(String key) {
    if (key.isEmpty || key.length > maxEventNameLength) {
      return false;
    }
    return RegExp(r'^[a-zA-Z][a-zA-Z0-9_]*$').hasMatch(key);
  }

  /// Validate parameter value
  static bool isValidParameterValue(Object? value) {
    if (value == null) return true;
    if (value is String && value.length > maxParameterValueLength) {
      return false;
    }
    return value is String || value is num || value is bool;
  }

  /// Sanitize event parameters
  static Map<String, Object?> sanitizeParameters(
    Map<String, Object?>? parameters,
  ) {
    if (parameters == null) return {};

    final sanitized = <String, Object?>{};
    int paramCount = 0;

    for (final entry in parameters.entries) {
      if (paramCount >= maxEventParameters) break;

      final key = entry.key;
      final value = entry.value;

      if (!isValidParameterKey(key)) continue;
      if (!isValidParameterValue(value)) continue;

      // Truncate string values if too long
      Object? sanitizedValue = value;
      if (value is String && value.length > maxParameterValueLength) {
        sanitizedValue = value.substring(0, maxParameterValueLength);
      }

      sanitized[key] = sanitizedValue;
      paramCount++;
    }

    return sanitized;
  }
}
