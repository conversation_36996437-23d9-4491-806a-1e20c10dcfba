import 'package:billionaires_social/core/services/analytics/base_analytics_service.dart';

/// User-specific analytics service
class UserAnalyticsService extends BaseAnalyticsService {
  static final UserAnalyticsService _instance =
      UserAnalyticsService._internal();
  factory UserAnalyticsService() => _instance;
  UserAnalyticsService._internal();

  /// Set comprehensive user properties
  Future<void> setUserProperties({
    required String userId,
    String? userType,
    bool? isVerified,
    bool? isBillionaire,
    String? location,
    DateTime? accountCreatedDate,
    int? totalPosts,
    int? totalStories,
    int? totalFollowers,
    int? totalFollowing,
    String? preferredLanguage,
  }) async {
    await setUserIdSafely(userId);

    final properties = <String, String?>{
      UserProperties.userType: userType,
      UserProperties.isVerified: isVerified?.toString(),
      UserProperties.isBillionaire: isBillionaire?.toString(),
      UserProperties.location: location,
      UserProperties.accountCreatedDate: accountCreatedDate?.toIso8601String(),
      UserProperties.totalPosts: totalPosts?.toString(),
      UserProperties.totalStories: totalStories?.toString(),
      UserProperties.totalFollowers: totalFollowers?.toString(),
      UserProperties.totalFollowing: totalFollowing?.toString(),
      UserProperties.preferredLanguage: preferredLanguage,
      UserProperties.lastActiveDate: DateTime.now().toIso8601String(),
    };

    for (final entry in properties.entries) {
      if (entry.value != null) {
        await setUserPropertySafely(name: entry.key, value: entry.value);
      }
    }
  }

  /// Track user authentication events
  Future<void> logLogin({required String method}) async {
    await logEventSafely(
      eventName: AnalyticsEvents.login,
      parameters: {
        AnalyticsParameters.method: method,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> logLogout() async {
    await logEventSafely(
      eventName: AnalyticsEvents.logout,
      parameters: {
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> logSignUp({required String method}) async {
    await logEventSafely(
      eventName: AnalyticsEvents.signUp,
      parameters: {
        AnalyticsParameters.method: method,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> logPasswordReset({required String method}) async {
    await logEventSafely(
      eventName: AnalyticsEvents.passwordReset,
      parameters: {
        AnalyticsParameters.method: method,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track user profile events
  Future<void> logProfileView({
    required String viewedUserId,
    String? sourceScreen,
  }) async {
    await logEventSafely(
      eventName: AnalyticsEvents.profileView,
      parameters: {
        AnalyticsParameters.targetUserId: viewedUserId,
        AnalyticsParameters.sourceScreen: sourceScreen,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> logProfileEdit({required List<String> fieldsEdited}) async {
    await logEventSafely(
      eventName: AnalyticsEvents.profileEdit,
      parameters: {
        'fields_edited': fieldsEdited.join(','),
        'fields_count': fieldsEdited.length,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track user social interactions
  Future<void> logUserFollow({
    required String targetUserId,
    String? sourceScreen,
  }) async {
    await logEventSafely(
      eventName: AnalyticsEvents.userFollow,
      parameters: {
        AnalyticsParameters.targetUserId: targetUserId,
        AnalyticsParameters.sourceScreen: sourceScreen,
        AnalyticsParameters.interactionType: 'follow',
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> logUserUnfollow({
    required String targetUserId,
    String? sourceScreen,
  }) async {
    await logEventSafely(
      eventName: AnalyticsEvents.userUnfollow,
      parameters: {
        AnalyticsParameters.targetUserId: targetUserId,
        AnalyticsParameters.sourceScreen: sourceScreen,
        AnalyticsParameters.interactionType: 'unfollow',
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track user settings and preferences
  Future<void> logSettingsView({String? section}) async {
    await logEventSafely(
      eventName: AnalyticsEvents.settingsView,
      parameters: {
        'settings_section': section,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> logSettingsChange({
    required String settingName,
    required String oldValue,
    required String newValue,
  }) async {
    await logEventSafely(
      eventName: 'settings_change',
      parameters: {
        'setting_name': settingName,
        'old_value': oldValue,
        'new_value': newValue,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track user engagement metrics
  Future<void> logUserEngagement({
    required Duration sessionDuration,
    required int screensViewed,
    required int actionsPerformed,
  }) async {
    await logEventSafely(
      eventName: 'user_engagement',
      parameters: {
        'session_duration_seconds': sessionDuration.inSeconds,
        'screens_viewed': screensViewed,
        'actions_performed': actionsPerformed,
        'engagement_rate':
            actionsPerformed / screensViewed.clamp(1, double.infinity),
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track user onboarding progress
  Future<void> logOnboardingStep({
    required String stepName,
    required int stepNumber,
    required int totalSteps,
    bool completed = false,
  }) async {
    await logEventSafely(
      eventName: 'onboarding_step',
      parameters: {
        'step_name': stepName,
        'step_number': stepNumber,
        'total_steps': totalSteps,
        'completed': completed,
        'progress_percentage': (stepNumber / totalSteps * 100).round(),
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> logOnboardingComplete({
    required Duration totalTime,
    required int stepsCompleted,
    required int totalSteps,
  }) async {
    await logEventSafely(
      eventName: 'onboarding_complete',
      parameters: {
        'total_time_seconds': totalTime.inSeconds,
        'steps_completed': stepsCompleted,
        'total_steps': totalSteps,
        'completion_rate': (stepsCompleted / totalSteps * 100).round(),
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track user retention metrics
  Future<void> logUserRetention({
    required int daysSinceInstall,
    required int daysSinceLastUse,
    required int totalSessions,
  }) async {
    await logEventSafely(
      eventName: 'user_retention',
      parameters: {
        'days_since_install': daysSinceInstall,
        'days_since_last_use': daysSinceLastUse,
        'total_sessions': totalSessions,
        'is_returning_user': daysSinceLastUse > 0,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track user preferences and behavior patterns
  Future<void> logUserPreference({
    required String preferenceName,
    required String preferenceValue,
    String? category,
  }) async {
    await logEventSafely(
      eventName: 'user_preference',
      parameters: {
        'preference_name': preferenceName,
        'preference_value': preferenceValue,
        'preference_category': category,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  /// Update user statistics
  Future<void> updateUserStats({
    int? totalPosts,
    int? totalStories,
    int? totalFollowers,
    int? totalFollowing,
    int? totalLikes,
    int? totalComments,
  }) async {
    final stats = <String, String?>{
      if (totalPosts != null) UserProperties.totalPosts: totalPosts.toString(),
      if (totalStories != null)
        UserProperties.totalStories: totalStories.toString(),
      if (totalFollowers != null)
        UserProperties.totalFollowers: totalFollowers.toString(),
      if (totalFollowing != null)
        UserProperties.totalFollowing: totalFollowing.toString(),
    };

    for (final entry in stats.entries) {
      await setUserPropertySafely(name: entry.key, value: entry.value);
    }

    // Also log as an event for trend analysis
    await logEventSafely(
      eventName: 'user_stats_update',
      parameters: {
        if (totalPosts != null) 'total_posts': totalPosts,
        if (totalStories != null) 'total_stories': totalStories,
        if (totalFollowers != null) 'total_followers': totalFollowers,
        if (totalFollowing != null) 'total_following': totalFollowing,
        if (totalLikes != null) 'total_likes': totalLikes,
        if (totalComments != null) 'total_comments': totalComments,
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }
}
