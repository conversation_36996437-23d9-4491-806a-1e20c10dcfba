import 'package:flutter/material.dart';
import 'package:billionaires_social/core/services/universal_user_role_service.dart';
import 'package:billionaires_social/core/services/universal_navigation_service.dart';
import 'package:billionaires_social/core/services/universal_social_interaction_service.dart';

/// Universal UI service that provides context-aware UI components
/// Eliminates hardcoded UI logic throughout the app
class UniversalUIService {
  /// Get universal action buttons for any user profile
  static Future<List<Widget>> getProfileActionButtons({
    required BuildContext context,
    required String targetUserId,
    String? targetUsername,
  }) async {
    final permissions = await UniversalUserRoleService.getUserPermissions(
      targetUserId,
    );
    final relationship = await UniversalUserRoleService.getUserRelationship(
      targetUserId,
    );
    final List<Widget> buttons = [];

    if (relationship == UserRelationship.self) {
      // Current user's profile - show edit and settings buttons
      buttons.addAll([
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => UniversalNavigationService.navigateToEditProfile(
              context,
              targetUserId,
            ),
            icon: const Icon(Icons.edit),
            label: const Text('Edit Profile'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey[200],
              foregroundColor: Colors.black,
            ),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => UniversalNavigationService.navigateToSettings(
              context,
              targetUserId,
            ),
            icon: const Icon(Icons.settings),
            label: const Text('Settings'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey[200],
              foregroundColor: Colors.black,
            ),
          ),
        ),
      ]);
    } else {
      // Other user's profile - show contextual buttons
      if (permissions.canFollow) {
        buttons.add(
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _handleFollowAction(context, targetUserId, true),
              icon: const Icon(Icons.person_add),
              label: const Text('Follow'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
          ),
        );
      } else if (permissions.canUnfollow) {
        buttons.add(
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () =>
                  _handleFollowAction(context, targetUserId, false),
              icon: const Icon(Icons.person_remove),
              label: Text(
                relationship == UserRelationship.closeFriend
                    ? 'Unfollow'
                    : 'Following',
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey[200],
                foregroundColor: Colors.black,
              ),
            ),
          ),
        );
      }

      if (permissions.canSendMessage) {
        if (buttons.isNotEmpty) buttons.add(const SizedBox(width: 8));
        buttons.add(
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => UniversalNavigationService.navigateToMessage(
                context,
                targetUserId,
                targetUsername: targetUsername,
              ),
              icon: const Icon(Icons.message),
              label: const Text('Message'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey[200],
                foregroundColor: Colors.black,
              ),
            ),
          ),
        );
      }
    }

    return buttons;
  }

  /// Get universal post action buttons
  static Future<List<Widget>> getPostActionButtons({
    required BuildContext context,
    required String postId,
    required String postOwnerId,
    required bool isLiked,
    required int likeCount,
    required int commentCount,
    required VoidCallback onLike,
    required VoidCallback onComment,
    required VoidCallback onShare,
  }) async {
    final permissions = await UniversalUserRoleService.getUserPermissions(
      postOwnerId,
    );

    return [
      // Like button - always available
      IconButton(
        onPressed: onLike,
        icon: Icon(
          isLiked ? Icons.favorite : Icons.favorite_border,
          color: isLiked ? Colors.red : Colors.grey[600],
        ),
      ),
      if (likeCount > 0)
        Text(likeCount.toString(), style: TextStyle(color: Colors.grey[600])),

      // Comment button - always available
      IconButton(
        onPressed: onComment,
        icon: Icon(Icons.chat_bubble_outline, color: Colors.grey[600]),
      ),
      if (commentCount > 0)
        Text(
          commentCount.toString(),
          style: TextStyle(color: Colors.grey[600]),
        ),

      // Share button - always available
      IconButton(
        onPressed: onShare,
        icon: Icon(Icons.share, color: Colors.grey[600]),
      ),

      const Spacer(),

      // More options - context-aware
      IconButton(
        onPressed: () =>
            _showPostOptionsMenu(context, postId, postOwnerId, permissions),
        icon: Icon(Icons.more_vert, color: Colors.grey[600]),
      ),
    ];
  }

  /// Get universal story action buttons
  static Future<List<Widget>> getStoryActionButtons({
    required BuildContext context,
    required String storyId,
    required String storyOwnerId,
  }) async {
    final permissions = await UniversalUserRoleService.getUserPermissions(
      storyOwnerId,
    );
    final List<Widget> buttons = [];

    if (permissions.canEdit) {
      // Story owner options
      buttons.addAll([
        IconButton(
          onPressed: () => _showStoryInsights(context, storyId),
          icon: const Icon(Icons.bar_chart, color: Colors.white),
          tooltip: 'View Insights',
        ),
        IconButton(
          onPressed: () => _showStoryOptions(context, storyId, permissions),
          icon: const Icon(Icons.more_vert, color: Colors.white),
          tooltip: 'More Options',
        ),
      ]);
    } else {
      // Viewer options
      buttons.add(
        IconButton(
          onPressed: () => _reportStory(context, storyId),
          icon: const Icon(Icons.flag, color: Colors.white),
          tooltip: 'Report',
        ),
      );
    }

    return buttons;
  }

  /// Get universal content creation options
  static Future<List<Widget>> getContentCreationOptions(
    BuildContext context,
  ) async {
    final currentUserId = UniversalUserRoleService.getCurrentUserId();
    if (currentUserId == null) return [];

    final limits = await UniversalUserRoleService.getContentLimits(
      currentUserId,
    );
    final List<Widget> options = [];

    // Post creation - always available for authenticated users
    options.add(
      ListTile(
        leading: const Icon(Icons.photo_library),
        title: const Text('Create Post'),
        subtitle: Text(
          'Up to ${limits.maxMediaPerPost == -1 ? 'unlimited' : limits.maxMediaPerPost} media items',
        ),
        onTap: () {
          Navigator.pop(context);
          UniversalNavigationService.navigateToContentCreation(
            context,
            ContentCreationType.post,
          );
        },
      ),
    );

    // Story creation - always available for authenticated users
    options.add(
      ListTile(
        leading: const Icon(Icons.camera_alt),
        title: const Text('Create Story'),
        subtitle: Text(
          '${limits.maxStoriesPerDay == -1 ? 'Unlimited' : limits.maxStoriesPerDay} stories per day',
        ),
        onTap: () {
          Navigator.pop(context);
          UniversalNavigationService.navigateToContentCreation(
            context,
            ContentCreationType.story,
          );
        },
      ),
    );

    // Reel creation - based on permissions
    if (limits.canCreateReels) {
      options.add(
        ListTile(
          leading: const Icon(Icons.video_library),
          title: const Text('Create Reel'),
          subtitle: const Text('Short video content'),
          onTap: () {
            Navigator.pop(context);
            UniversalNavigationService.navigateToContentCreation(
              context,
              ContentCreationType.reel,
            );
          },
        ),
      );
    }

    // Live streaming - based on permissions
    if (limits.canGoLive) {
      options.add(
        ListTile(
          leading: const Icon(Icons.live_tv),
          title: const Text('Go Live'),
          subtitle: const Text('Start live streaming'),
          onTap: () {
            Navigator.pop(context);
            UniversalNavigationService.navigateToContentCreation(
              context,
              ContentCreationType.live,
            );
          },
        ),
      );
    }

    return options;
  }

  // Private helper methods
  static Future<void> _handleFollowAction(
    BuildContext context,
    String targetUserId,
    bool follow,
  ) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    try {
      // Show loading state
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(follow ? 'Following user...' : 'Unfollowing user...'),
          duration: const Duration(seconds: 1),
        ),
      );

      bool success;
      if (follow) {
        success = await UniversalSocialInteractionService.followUser(
          targetUserId,
        );
      } else {
        success = await UniversalSocialInteractionService.unfollowUser(
          targetUserId,
        );
      }

      if (success) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(
              follow
                  ? 'Successfully followed user'
                  : 'Successfully unfollowed user',
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      } else {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(
              follow ? 'Already following user' : 'Not following user',
            ),
            backgroundColor: Colors.orange,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ Follow action error: $e');
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(
            'Error: ${follow ? 'Failed to follow' : 'Failed to unfollow'} user',
          ),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
          action: SnackBarAction(
            label: 'Retry',
            onPressed: () => _handleFollowAction(context, targetUserId, follow),
          ),
        ),
      );
    }
  }

  static Future<void> _showPostOptionsMenu(
    BuildContext context,
    String postId,
    String postOwnerId,
    UserPermissions permissions,
  ) async {
    final options = <Widget>[];

    if (permissions.canEdit) {
      options.add(
        ListTile(
          leading: const Icon(Icons.edit),
          title: const Text('Edit Post'),
          onTap: () {
            Navigator.pop(context);
            // TODO: Navigate to edit post
          },
        ),
      );
    }

    if (permissions.canPin) {
      options.add(
        ListTile(
          leading: const Icon(Icons.push_pin),
          title: const Text('Pin Post'),
          onTap: () {
            Navigator.pop(context);
            // TODO: Pin/unpin post
          },
        ),
      );
    }

    if (permissions.canArchive) {
      options.add(
        ListTile(
          leading: const Icon(Icons.archive),
          title: const Text('Archive Post'),
          onTap: () {
            Navigator.pop(context);
            // TODO: Archive post
          },
        ),
      );
    }

    if (permissions.canDelete) {
      options.add(
        ListTile(
          leading: const Icon(Icons.delete, color: Colors.red),
          title: const Text('Delete Post', style: TextStyle(color: Colors.red)),
          onTap: () {
            Navigator.pop(context);
            _confirmDeletePost(context, postId);
          },
        ),
      );
    }

    if (permissions.canReport) {
      options.add(
        ListTile(
          leading: const Icon(Icons.flag, color: Colors.orange),
          title: const Text('Report Post'),
          onTap: () {
            Navigator.pop(context);
            _showReportDialog(context, postId);
          },
        ),
      );
    }

    showModalBottomSheet(
      context: context,
      builder: (context) =>
          Column(mainAxisSize: MainAxisSize.min, children: options),
    );
  }

  static void _confirmDeletePost(BuildContext context, String postId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Post'),
        content: const Text(
          'Are you sure you want to delete this post? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Delete post using UniversalContentService
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  static void _showStoryInsights(BuildContext context, String storyId) {
    // TODO: Show story insights
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Story insights coming soon')));
  }

  static void _showStoryOptions(
    BuildContext context,
    String storyId,
    UserPermissions permissions,
  ) {
    // TODO: Show story options menu
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Story options coming soon')));
  }

  static void _reportStory(BuildContext context, String storyId) {
    // TODO: Report story
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Report story coming soon')));
  }

  static void _showReportDialog(BuildContext context, String postId) {
    final reportReasons = [
      'Spam',
      'Inappropriate content',
      'Harassment or bullying',
      'False information',
      'Hate speech',
      'Violence or dangerous content',
      'Copyright violation',
      'Other',
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Report Post'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Why are you reporting this post?'),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: ListView.builder(
                itemCount: reportReasons.length,
                itemBuilder: (context, index) {
                  return ListTile(
                    title: Text(reportReasons[index]),
                    onTap: () {
                      Navigator.pop(context);
                      _submitReport(context, postId, reportReasons[index]);
                    },
                  );
                },
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  static void _submitReport(
    BuildContext context,
    String postId,
    String reason,
  ) {
    // TODO: Implement actual report submission to backend
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Post reported for: $reason'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
