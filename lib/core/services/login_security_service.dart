import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:billionaires_social/core/services/security_service.dart';
import 'dart:async';

class LoginAttempt {
  final String email;
  final String ipAddress;
  final String deviceId;
  final DateTime timestamp;
  final bool success;
  final String? errorMessage;

  LoginAttempt({
    required this.email,
    required this.ipAddress,
    required this.deviceId,
    required this.timestamp,
    required this.success,
    this.errorMessage,
  });

  Map<String, dynamic> toJson() => {
    'email': email,
    'ipAddress': ipAddress,
    'deviceId': deviceId,
    'timestamp': Timestamp.fromDate(timestamp),
    'success': success,
    'errorMessage': errorMessage,
  };
}

class LoginSecurityService {
  static final LoginSecurityService _instance =
      LoginSecurityService._internal();
  factory LoginSecurityService() => _instance;
  LoginSecurityService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final SecurityService _securityService = SecurityService();

  // In-memory cache for failed attempts (for immediate response)
  final Map<String, List<DateTime>> _failedAttemptsCache = {};
  final Map<String, DateTime> _lockoutCache = {};

  static const int maxFailedAttempts = 5;
  static const Duration lockoutDuration = Duration(minutes: 10);
  static const Duration attemptWindow = Duration(minutes: 15);

  /// Check if login is allowed for the given email
  Future<bool> isLoginAllowed(String email) async {
    final normalizedEmail = email.toLowerCase().trim();

    // Check security service rate limiting and lockouts
    if (!_securityService.checkRateLimit(
      normalizedEmail,
      maxRequests: 10,
      window: const Duration(minutes: 1),
    )) {
      debugPrint('🔒 LoginSecurity: Rate limit exceeded for $normalizedEmail');
      return false;
    }

    if (_securityService.isLockedOut(normalizedEmail)) {
      debugPrint(
        '🔒 LoginSecurity: Account locked by security service for $normalizedEmail',
      );
      return false;
    }

    // Check in-memory cache first
    if (_lockoutCache.containsKey(normalizedEmail)) {
      final lockoutTime = _lockoutCache[normalizedEmail]!;
      if (DateTime.now().isBefore(lockoutTime)) {
        debugPrint('🔒 LoginSecurity: Account locked until $lockoutTime');
        return false;
      } else {
        // Lockout expired, remove from cache
        _lockoutCache.remove(normalizedEmail);
        _failedAttemptsCache.remove(normalizedEmail);
      }
    }

    // Check Firestore for persistent lockout
    try {
      final doc = await _firestore
          .collection('login_attempts')
          .doc(normalizedEmail)
          .get();

      if (doc.exists) {
        final data = doc.data()!;
        final lockoutUntil = data['lockoutUntil'] as Timestamp?;

        if (lockoutUntil != null &&
            DateTime.now().isBefore(lockoutUntil.toDate())) {
          debugPrint(
            '🔒 LoginSecurity: Account locked in Firestore until ${lockoutUntil.toDate()}',
          );
          return false;
        }
      }
    } catch (e) {
      debugPrint('❌ LoginSecurity: Error checking Firestore lockout: $e');
    }

    return true;
  }

  /// Record a login attempt
  Future<void> recordLoginAttempt({
    required String email,
    required bool success,
    required String ipAddress,
    required String deviceId,
    String? errorMessage,
  }) async {
    final normalizedEmail = email.toLowerCase().trim();
    final attempt = LoginAttempt(
      email: normalizedEmail,
      ipAddress: ipAddress,
      deviceId: deviceId,
      timestamp: DateTime.now(),
      success: success,
      errorMessage: errorMessage,
    );

    // Update security service
    if (!success) {
      _securityService.recordFailedAttempt(normalizedEmail, 'password_login');
    } else {
      _securityService.recordSuccessfulAuth(normalizedEmail, 'password_login');
    }

    // Update in-memory cache
    if (!success) {
      _failedAttemptsCache.putIfAbsent(normalizedEmail, () => []);
      _failedAttemptsCache[normalizedEmail]!.add(attempt.timestamp);

      // Clean old attempts outside the window
      final cutoff = DateTime.now().subtract(attemptWindow);
      _failedAttemptsCache[normalizedEmail]!.removeWhere(
        (timestamp) => timestamp.isBefore(cutoff),
      );

      // Check if we should lock the account
      if (_failedAttemptsCache[normalizedEmail]!.length >= maxFailedAttempts) {
        final lockoutUntil = DateTime.now().add(lockoutDuration);
        _lockoutCache[normalizedEmail] = lockoutUntil;

        debugPrint(
          '🔒 LoginSecurity: Account locked for $normalizedEmail until $lockoutUntil',
        );
      }
    } else {
      // Successful login, clear failed attempts
      _failedAttemptsCache.remove(normalizedEmail);
      _lockoutCache.remove(normalizedEmail);
    }

    // Store in Firestore
    try {
      await _firestore
          .collection('login_attempts')
          .doc(normalizedEmail)
          .collection('attempts')
          .add(attempt.toJson());

      // Update lockout status in Firestore
      if (!success &&
          _failedAttemptsCache[normalizedEmail]?.length == maxFailedAttempts) {
        await _firestore.collection('login_attempts').doc(normalizedEmail).set({
          'lockoutUntil': Timestamp.fromDate(
            DateTime.now().add(lockoutDuration),
          ),
          'lastUpdated': Timestamp.fromDate(DateTime.now()),
        });
      } else if (success) {
        // Clear lockout on successful login
        await _firestore
            .collection('login_attempts')
            .doc(normalizedEmail)
            .update({
              'lockoutUntil': FieldValue.delete(),
              'lastUpdated': Timestamp.fromDate(DateTime.now()),
            });
      }
    } catch (e) {
      debugPrint('❌ LoginSecurity: Error storing login attempt: $e');
    }
  }

  /// Get remaining attempts before lockout
  int getRemainingAttempts(String email) {
    final normalizedEmail = email.toLowerCase().trim();
    final failedAttempts = _failedAttemptsCache[normalizedEmail]?.length ?? 0;
    return maxFailedAttempts - failedAttempts;
  }

  /// Get lockout time remaining
  Duration? getLockoutTimeRemaining(String email) {
    final normalizedEmail = email.toLowerCase().trim();
    final lockoutTime = _lockoutCache[normalizedEmail];

    if (lockoutTime == null) return null;

    final remaining = lockoutTime.difference(DateTime.now());
    return remaining.isNegative ? null : remaining;
  }

  /// Get device ID (simplified implementation)
  String getDeviceId() {
    // In a real implementation, you'd use device_info_plus package
    // For now, return a placeholder
    return 'device_${DateTime.now().millisecondsSinceEpoch}';
  }

  /// Get IP address (simplified implementation)
  String getIpAddress() {
    // In a real implementation, you'd use a service to get the actual IP
    // For now, return a placeholder
    return 'ip_${DateTime.now().millisecondsSinceEpoch}';
  }

  /// Get current authenticated user
  User? getCurrentUser() {
    return _auth.currentUser;
  }

  /// Check if user is currently signed in
  bool isUserSignedIn() {
    return _auth.currentUser != null;
  }

  /// Force sign out user (useful when account is locked)
  Future<void> forceSignOut() async {
    try {
      await _auth.signOut();
      debugPrint('🔒 LoginSecurity: User signed out due to security policy');
    } catch (e) {
      debugPrint('❌ LoginSecurity: Error signing out user: $e');
    }
  }

  /// Check if current user's email matches the locked email and sign them out
  Future<void> enforceSecurityPolicy(String lockedEmail) async {
    final currentUser = getCurrentUser();
    if (currentUser != null &&
        currentUser.email?.toLowerCase().trim() ==
            lockedEmail.toLowerCase().trim()) {
      await forceSignOut();
    }
  }
}
