import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async';

class RegistrationAttempt {
  final String email;
  final String accountType;
  final String ipAddress;
  final String deviceId;
  final DateTime timestamp;
  final bool success;
  final String? errorMessage;

  RegistrationAttempt({
    required this.email,
    required this.accountType,
    required this.ipAddress,
    required this.deviceId,
    required this.timestamp,
    required this.success,
    this.errorMessage,
  });

  Map<String, dynamic> toJson() => {
    'email': email,
    'accountType': accountType,
    'ipAddress': ipAddress,
    'deviceId': deviceId,
    'timestamp': Timestamp.fromDate(timestamp),
    'success': success,
    'errorMessage': errorMessage,
  };
}

class RegistrationSecurityService {
  static final RegistrationSecurityService _instance =
      RegistrationSecurityService._internal();
  factory RegistrationSecurityService() => _instance;
  RegistrationSecurityService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // In-memory cache for registration attempts
  final Map<String, List<DateTime>> _registrationAttemptsCache = {};
  final Map<String, DateTime> _throttleCache = {};

  static const int maxRegistrationsPerDay = 3;
  static const Duration throttleDuration = Duration(hours: 24);
  static const Duration attemptWindow = Duration(hours: 24);

  /// Check if registration is allowed for the given device/IP
  Future<bool> isRegistrationAllowed(String deviceId, String ipAddress) async {
    // Check in-memory cache first
    final deviceKey = 'device_$deviceId';
    final ipKey = 'ip_$ipAddress';

    if (_throttleCache.containsKey(deviceKey) ||
        _throttleCache.containsKey(ipKey)) {
      final deviceThrottleTime = _throttleCache[deviceKey];
      final ipThrottleTime = _throttleCache[ipKey];

      if ((deviceThrottleTime != null &&
              DateTime.now().isBefore(deviceThrottleTime)) ||
          (ipThrottleTime != null && DateTime.now().isBefore(ipThrottleTime))) {
        debugPrint('🔒 RegistrationSecurity: Registration throttled');
        return false;
      } else {
        // Throttle expired, remove from cache
        _throttleCache.remove(deviceKey);
        _throttleCache.remove(ipKey);
        _registrationAttemptsCache.remove(deviceKey);
        _registrationAttemptsCache.remove(ipKey);
      }
    }

    // Check Firestore for persistent throttling
    try {
      final deviceDoc = await _firestore
          .collection('registration_attempts')
          .doc(deviceKey)
          .get();

      final ipDoc = await _firestore
          .collection('registration_attempts')
          .doc(ipKey)
          .get();

      if (deviceDoc.exists) {
        final deviceData = deviceDoc.data()!;
        final throttleUntil = deviceData['throttleUntil'] as Timestamp?;

        if (throttleUntil != null &&
            DateTime.now().isBefore(throttleUntil.toDate())) {
          debugPrint(
            '🔒 RegistrationSecurity: Device registration throttled in Firestore',
          );
          return false;
        }
      }

      if (ipDoc.exists) {
        final ipData = ipDoc.data()!;
        final throttleUntil = ipData['throttleUntil'] as Timestamp?;

        if (throttleUntil != null &&
            DateTime.now().isBefore(throttleUntil.toDate())) {
          debugPrint(
            '🔒 RegistrationSecurity: IP registration throttled in Firestore',
          );
          return false;
        }
      }
    } catch (e) {
      debugPrint(
        '❌ RegistrationSecurity: Error checking Firestore throttling: $e',
      );
    }

    return true;
  }

  /// Record a registration attempt
  Future<void> recordRegistrationAttempt({
    required String email,
    required String accountType,
    required bool success,
    required String ipAddress,
    required String deviceId,
    String? errorMessage,
  }) async {
    final attempt = RegistrationAttempt(
      email: email,
      accountType: accountType,
      ipAddress: ipAddress,
      deviceId: deviceId,
      timestamp: DateTime.now(),
      success: success,
      errorMessage: errorMessage,
    );

    final deviceKey = 'device_$deviceId';
    final ipKey = 'ip_$ipAddress';

    // Update in-memory cache
    if (success) {
      _registrationAttemptsCache.putIfAbsent(deviceKey, () => []);
      _registrationAttemptsCache.putIfAbsent(ipKey, () => []);

      _registrationAttemptsCache[deviceKey]!.add(attempt.timestamp);
      _registrationAttemptsCache[ipKey]!.add(attempt.timestamp);

      // Clean old attempts outside the window
      final cutoff = DateTime.now().subtract(attemptWindow);
      _registrationAttemptsCache[deviceKey]!.removeWhere(
        (timestamp) => timestamp.isBefore(cutoff),
      );
      _registrationAttemptsCache[ipKey]!.removeWhere(
        (timestamp) => timestamp.isBefore(cutoff),
      );

      // Check if we should throttle
      if (_registrationAttemptsCache[deviceKey]!.length >=
              maxRegistrationsPerDay ||
          _registrationAttemptsCache[ipKey]!.length >= maxRegistrationsPerDay) {
        final throttleUntil = DateTime.now().add(throttleDuration);
        _throttleCache[deviceKey] = throttleUntil;
        _throttleCache[ipKey] = throttleUntil;

        debugPrint(
          '🔒 RegistrationSecurity: Registration throttled for $deviceKey and $ipKey until $throttleUntil',
        );
      }
    }

    // Store in Firestore
    try {
      // Store attempt
      await _firestore
          .collection('registration_attempts')
          .doc(deviceKey)
          .collection('attempts')
          .add(attempt.toJson());

      await _firestore
          .collection('registration_attempts')
          .doc(ipKey)
          .collection('attempts')
          .add(attempt.toJson());

      // Update throttling status in Firestore
      if (success &&
          (_registrationAttemptsCache[deviceKey]?.length ==
                  maxRegistrationsPerDay ||
              _registrationAttemptsCache[ipKey]?.length ==
                  maxRegistrationsPerDay)) {
        final throttleUntil = DateTime.now().add(throttleDuration);

        await _firestore.collection('registration_attempts').doc(deviceKey).set(
          {
            'throttleUntil': Timestamp.fromDate(throttleUntil),
            'lastUpdated': Timestamp.fromDate(DateTime.now()),
            'attemptCount': _registrationAttemptsCache[deviceKey]?.length ?? 0,
          },
        );

        await _firestore.collection('registration_attempts').doc(ipKey).set({
          'throttleUntil': Timestamp.fromDate(throttleUntil),
          'lastUpdated': Timestamp.fromDate(DateTime.now()),
          'attemptCount': _registrationAttemptsCache[ipKey]?.length ?? 0,
        });
      }
    } catch (e) {
      debugPrint(
        '❌ RegistrationSecurity: Error storing registration attempt: $e',
      );
    }
  }

  /// Get remaining registrations before throttling
  int getRemainingRegistrations(String deviceId, String ipAddress) {
    final deviceKey = 'device_$deviceId';
    final ipKey = 'ip_$ipAddress';

    final deviceAttempts = _registrationAttemptsCache[deviceKey]?.length ?? 0;
    final ipAttempts = _registrationAttemptsCache[ipKey]?.length ?? 0;

    return maxRegistrationsPerDay -
        [deviceAttempts, ipAttempts].reduce((a, b) => a > b ? a : b);
  }

  /// Get throttle time remaining
  Duration? getThrottleTimeRemaining(String deviceId, String ipAddress) {
    final deviceKey = 'device_$deviceId';
    final ipKey = 'ip_$ipAddress';

    final deviceThrottleTime = _throttleCache[deviceKey];
    final ipThrottleTime = _throttleCache[ipKey];

    if (deviceThrottleTime == null && ipThrottleTime == null) return null;

    final now = DateTime.now();
    final deviceRemaining = deviceThrottleTime?.difference(now);
    final ipRemaining = ipThrottleTime?.difference(now);

    if (deviceRemaining != null && deviceRemaining.isNegative) return null;
    if (ipRemaining != null && ipRemaining.isNegative) return null;

    if (deviceRemaining != null && ipRemaining != null) {
      return deviceRemaining > ipRemaining ? deviceRemaining : ipRemaining;
    } else if (deviceRemaining != null) {
      return deviceRemaining;
    } else {
      return ipRemaining;
    }
  }

  /// Check if email is already registered
  /// Note: This method now uses a more secure approach as fetchSignInMethodsForEmail is deprecated
  Future<bool> isEmailRegistered(String email) async {
    try {
      // Instead of fetchSignInMethodsForEmail, we'll attempt to create a user
      // and catch the specific error if email is already in use
      // This is more secure as it doesn't reveal email enumeration

      // For now, we'll return false to allow registration attempts
      // The actual email validation will happen during the registration process
      // where Firebase will throw an appropriate error if email is already in use
      return false;
    } catch (e) {
      debugPrint(
        '❌ RegistrationSecurity: Error checking email registration: $e',
      );
      return false;
    }
  }

  /// Validate password strength
  bool validatePasswordStrength(String password) {
    if (password.length < 8) return false;
    if (!RegExp(r'[0-9]').hasMatch(password)) return false;
    if (!RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) return false;
    return true;
  }

  /// Get device ID from SharedPreferences
  Future<String> getDeviceId() async {
    final prefs = await SharedPreferences.getInstance();
    String? deviceId = prefs.getString('device_id');

    if (deviceId == null) {
      deviceId =
          'device_${DateTime.now().millisecondsSinceEpoch}_${DateTime.now().microsecondsSinceEpoch}';
      await prefs.setString('device_id', deviceId);
    }

    return deviceId;
  }

  /// Get IP address (simplified implementation)
  String getIpAddress() {
    // In a real implementation, you'd use a service to get the actual IP
    // For now, return a placeholder
    return 'ip_${DateTime.now().millisecondsSinceEpoch}';
  }
}
