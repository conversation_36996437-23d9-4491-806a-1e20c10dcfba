import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';

/// Universal service to ensure all accounts get consistent initialization
/// regardless of how they're created (registration, social login, etc.)
class UniversalAccountService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Ensure account has universal defaults applied
  static Future<void> ensureUniversalDefaults(String userId) async {
    try {
      debugPrint('🔧 Applying universal defaults for user: $userId');

      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (!userDoc.exists) {
        debugPrint('⚠️ User document not found: $userId');
        return;
      }

      final userData = userDoc.data()!;
      final Map<String, dynamic> updates = {};

      // Ensure all accounts have these universal fields
      if (!userData.containsKey('isNewUser')) {
        updates['isNewUser'] = _isNewUser(userData);
      }

      if (!userData.containsKey('onboardingCompleted')) {
        updates['onboardingCompleted'] = false;
      }

      if (!userData.containsKey('feedPreferences')) {
        updates['feedPreferences'] = {
          'defaultFilter': 'all', // New users start with all content
          'autoSwitchToFollowed': true, // Auto-switch when they start following
          'showBusinessPosts': true,
          'showPromotedContent': true,
          'contentLanguages': ['en'], // Default to English
        };
      }

      // Ensure universal UI preferences
      if (!userData.containsKey('uiPreferences')) {
        updates['uiPreferences'] = {
          'theme': 'system', // Follow system theme
          'feedLayout': 'card', // Default feed layout
          'autoPlayVideos': true,
          'showCaptions': true,
          'reducedMotion': false,
        };
      }

      // Ensure universal privacy settings
      if (!userData.containsKey('privacySettings')) {
        updates['privacySettings'] = {
          'profileVisibility': 'public',
          'allowMessages': true,
          'allowFollowRequests': true,
          'showActivityStatus': true,
          'allowTagging': true,
          'allowMentions': true,
        };
      }

      // Ensure universal notification settings
      if (!userData.containsKey('notificationSettings')) {
        updates['notificationSettings'] = {
          'pushNotifications': true,
          'emailNotifications': false,
          'followNotifications': true,
          'likeNotifications': true,
          'commentNotifications': true,
          'mentionNotifications': true,
          'messageNotifications': true,
        };
      }

      // Ensure consistent count fields
      if (!userData.containsKey('followerCount')) {
        updates['followerCount'] = 0;
      }
      if (!userData.containsKey('followingCount')) {
        updates['followingCount'] = 0;
      }
      if (!userData.containsKey('postCount')) {
        updates['postCount'] = 0;
      }

      // Ensure consistent boolean fields
      if (!userData.containsKey('isVerified')) {
        updates['isVerified'] = false;
      }
      if (!userData.containsKey('isBillionaire')) {
        updates['isBillionaire'] = false;
      }
      if (!userData.containsKey('isAdmin')) {
        updates['isAdmin'] = false;
      }

      // Apply updates if needed
      if (updates.isNotEmpty) {
        updates['updatedAt'] = FieldValue.serverTimestamp();
        await _firestore.collection('users').doc(userId).update(updates);
        debugPrint(
          '✅ Applied ${updates.length} universal defaults for user: $userId',
        );
      } else {
        debugPrint('✅ User already has all universal defaults: $userId');
      }
    } catch (e) {
      debugPrint('❌ Error applying universal defaults: $e');
    }
  }

  /// Check if user is new based on creation date and activity
  static bool _isNewUser(Map<String, dynamic> userData) {
    final createdAt = userData['createdAt'] as Timestamp?;
    if (createdAt == null) return true;

    final creationDate = createdAt.toDate();
    final daysSinceCreation = DateTime.now().difference(creationDate).inDays;

    // Consider user new if:
    // - Created less than 7 days ago
    // - Has no posts
    // - Follows no one
    return daysSinceCreation < 7 &&
        (userData['postCount'] ?? 0) == 0 &&
        (userData['followingCount'] ?? 0) == 0;
  }

  /// Initialize new user with welcome content and suggestions
  static Future<void> initializeNewUser(String userId) async {
    try {
      debugPrint('🎉 Initializing new user: $userId');

      // Apply universal defaults first
      await ensureUniversalDefaults(userId);

      // Mark onboarding as needed
      await _firestore.collection('users').doc(userId).update({
        'needsOnboarding': true,
        'onboardingStep': 'welcome',
        'updatedAt': FieldValue.serverTimestamp(),
      });

      debugPrint('✅ New user initialization complete: $userId');
    } catch (e) {
      debugPrint('❌ Error initializing new user: $e');
    }
  }

  /// Auto-apply smart feed filter based on user's following status
  static Future<String> getSmartFeedFilter(String userId) async {
    try {
      final followingSnapshot = await _firestore
          .collection('users')
          .doc(userId)
          .collection('following')
          .limit(1)
          .get();

      if (followingSnapshot.docs.isNotEmpty) {
        debugPrint('🎯 User follows people → recommending followed filter');
        return 'followed';
      } else {
        debugPrint('🎯 New user → recommending all filter for discovery');
        return 'all';
      }
    } catch (e) {
      debugPrint('❌ Error getting smart feed filter: $e');
      return 'all'; // Safe default
    }
  }

  /// Check if current user needs universal defaults applied
  static Future<void> checkCurrentUser() async {
    final currentUser = _auth.currentUser;
    if (currentUser != null) {
      await ensureUniversalDefaults(currentUser.uid);
    }
  }

  /// Batch apply universal defaults to multiple users (admin function)
  static Future<void> batchApplyDefaults(List<String> userIds) async {
    debugPrint(
      '🔧 Batch applying universal defaults to ${userIds.length} users',
    );

    for (final userId in userIds) {
      await ensureUniversalDefaults(userId);
      // Small delay to avoid overwhelming Firestore
      await Future.delayed(const Duration(milliseconds: 100));
    }

    debugPrint('✅ Batch universal defaults complete');
  }

  /// Validate account has all required universal fields
  static Future<bool> validateAccountUniversality(String userId) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (!userDoc.exists) return false;

      final userData = userDoc.data()!;

      // Check for all required universal fields
      final requiredFields = [
        'isNewUser',
        'onboardingCompleted',
        'feedPreferences',
        'uiPreferences',
        'privacySettings',
        'notificationSettings',
        'followerCount',
        'followingCount',
        'postCount',
        'isVerified',
        'isBillionaire',
        'isAdmin',
      ];

      for (final field in requiredFields) {
        if (!userData.containsKey(field)) {
          debugPrint('❌ Account $userId missing universal field: $field');
          return false;
        }
      }

      debugPrint('✅ Account $userId has all universal fields');
      return true;
    } catch (e) {
      debugPrint('❌ Error validating account universality: $e');
      return false;
    }
  }

  /// Fix any account that doesn't meet universal standards
  static Future<void> repairAccountUniversality(String userId) async {
    final isValid = await validateAccountUniversality(userId);
    if (!isValid) {
      debugPrint('🔧 Repairing account universality for: $userId');
      await ensureUniversalDefaults(userId);

      // Validate again
      final isNowValid = await validateAccountUniversality(userId);
      if (isNowValid) {
        debugPrint('✅ Account universality repaired for: $userId');
      } else {
        debugPrint('❌ Failed to repair account universality for: $userId');
      }
    }
  }

  /// Get universal account template for new accounts
  static Map<String, dynamic> getUniversalAccountTemplate({
    required String uid,
    required String email,
    required String name,
    String? username,
    bool isBusinessAccount = false,
  }) {
    return {
      'uid': uid,
      'email': email,
      'name': name,
      'username': username ?? email.split('@')[0],
      'isBusinessAccount': isBusinessAccount,
      'isAdmin': false,
      'isVerified': false,
      'isBillionaire': false,
      'requires2FA': false,
      'profilePictureUrl': '',
      'bio': '',
      'postCount': 0,
      'followerCount': 0,
      'followingCount': 0,
      'website': '',
      'createdAt': FieldValue.serverTimestamp(),
      'lastLoginAt': FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
      // Universal defaults
      'isNewUser': true,
      'onboardingCompleted': false,
      'feedPreferences': {
        'defaultFilter': 'all',
        'autoSwitchToFollowed': true,
        'showBusinessPosts': true,
        'showPromotedContent': true,
        'contentLanguages': ['en'],
      },
      'uiPreferences': {
        'theme': 'system',
        'feedLayout': 'card',
        'autoPlayVideos': true,
        'showCaptions': true,
        'reducedMotion': false,
      },
      'privacySettings': {
        'profileVisibility': 'public',
        'allowMessages': true,
        'allowFollowRequests': true,
        'showActivityStatus': true,
        'allowTagging': true,
        'allowMentions': true,
      },
      'notificationSettings': {
        'pushNotifications': true,
        'emailNotifications': false,
        'followNotifications': true,
        'likeNotifications': true,
        'commentNotifications': true,
        'mentionNotifications': true,
        'messageNotifications': true,
      },
    };
  }
}
