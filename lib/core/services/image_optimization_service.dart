import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path_provider/path_provider.dart';

class ImageOptimizationService {
  static const int _maxWidth = 1080;
  static const int _maxHeight = 1080;
  static const int _thumbnailSize = 300;
  static const int _quality = 85;
  static const int _thumbnailQuality = 70;

  /// Optimize image for upload with multiple sizes
  Future<ImageOptimizationResult> optimizeImageForUpload(File imageFile) async {
    try {
      final tempDir = await getTemporaryDirectory();
      final fileName = _getFileNameWithoutExtension(imageFile.path);
      final timestamp = DateTime.now().millisecondsSinceEpoch;

      // Generate optimized full-size image
      final optimizedPath =
          '${tempDir.path}/${fileName}_optimized_$timestamp.jpg';

      final optimizedBytes = await FlutterImageCompress.compressWithFile(
        imageFile.absolute.path,
        minWidth: _maxWidth,
        minHeight: _maxHeight,
        quality: _quality,
        format: CompressFormat.jpeg,
      );

      if (optimizedBytes == null) {
        throw Exception('Failed to compress image');
      }

      final optimizedFile = File(optimizedPath);
      await optimizedFile.writeAsBytes(optimizedBytes);

      // Generate thumbnail
      final thumbnailPath = '${tempDir.path}/${fileName}_thumb_$timestamp.jpg';

      final thumbnailBytes = await FlutterImageCompress.compressWithFile(
        imageFile.absolute.path,
        minWidth: _thumbnailSize,
        minHeight: _thumbnailSize,
        quality: _thumbnailQuality,
        format: CompressFormat.jpeg,
      );

      if (thumbnailBytes == null) {
        throw Exception('Failed to create thumbnail');
      }

      final thumbnailFile = File(thumbnailPath);
      await thumbnailFile.writeAsBytes(thumbnailBytes);

      return ImageOptimizationResult(
        originalFile: imageFile,
        optimizedFile: optimizedFile,
        thumbnailFile: thumbnailFile,
        originalSize: await imageFile.length(),
        optimizedSize: optimizedBytes.length,
        thumbnailSize: thumbnailBytes.length,
        compressionRatio: (await imageFile.length()) / optimizedBytes.length,
      );
    } catch (e) {
      debugPrint('Error optimizing image: $e');
      rethrow;
    }
  }

  /// Compress image bytes for in-memory operations
  Future<Uint8List?> compressImageBytes(
    Uint8List bytes, {
    int maxWidth = _maxWidth,
    int maxHeight = _maxHeight,
    int quality = _quality,
  }) async {
    try {
      return await FlutterImageCompress.compressWithList(
        bytes,
        minWidth: maxWidth,
        minHeight: maxHeight,
        quality: quality,
        format: CompressFormat.jpeg,
      );
    } catch (e) {
      debugPrint('Error compressing image bytes: $e');
      return null;
    }
  }

  /// Create multiple sizes for responsive loading
  Future<Map<String, File>> createMultipleSizes(File imageFile) async {
    final tempDir = await getTemporaryDirectory();
    final fileName = _getFileNameWithoutExtension(imageFile.path);
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    final sizes = <String, File>{};

    // Define different sizes
    final sizeConfigs = {
      'thumbnail': {'width': 150, 'height': 150, 'quality': 60},
      'small': {'width': 300, 'height': 300, 'quality': 70},
      'medium': {'width': 600, 'height': 600, 'quality': 80},
      'large': {'width': 1080, 'height': 1080, 'quality': 85},
    };

    for (final entry in sizeConfigs.entries) {
      final sizeName = entry.key;
      final config = entry.value;

      final sizePath = '${tempDir.path}/${fileName}_${sizeName}_$timestamp.jpg';

      final compressedBytes = await FlutterImageCompress.compressWithFile(
        imageFile.absolute.path,
        minWidth: config['width'] as int,
        minHeight: config['height'] as int,
        quality: config['quality'] as int,
        format: CompressFormat.jpeg,
      );

      if (compressedBytes != null) {
        final sizeFile = File(sizePath);
        await sizeFile.writeAsBytes(compressedBytes);
        sizes[sizeName] = sizeFile;
      }
    }

    return sizes;
  }

  /// Calculate optimal image size based on device capabilities
  Map<String, int> getOptimalImageSize() {
    // Get device pixel ratio for optimal sizing
    final pixelRatio =
        WidgetsBinding.instance.platformDispatcher.views.first.devicePixelRatio;

    // Base size adjusted for device pixel ratio
    final optimalWidth = (_maxWidth * pixelRatio).round();
    final optimalHeight = (_maxHeight * pixelRatio).round();

    return {
      'width': optimalWidth > 2160 ? 2160 : optimalWidth,
      'height': optimalHeight > 2160 ? 2160 : optimalHeight,
    };
  }

  /// Clean up temporary files
  Future<void> cleanupTempFiles(List<File> files) async {
    for (final file in files) {
      try {
        if (await file.exists()) {
          await file.delete();
        }
      } catch (e) {
        debugPrint('Error deleting temp file ${file.path}: $e');
      }
    }
  }

  /// Helper method to get filename without extension
  String _getFileNameWithoutExtension(String filePath) {
    final lastSlash = filePath.lastIndexOf('/');
    final lastDot = filePath.lastIndexOf('.');

    final fileName = lastSlash >= 0
        ? filePath.substring(lastSlash + 1)
        : filePath;
    return lastDot > lastSlash
        ? fileName.substring(0, lastDot - (lastSlash + 1))
        : fileName;
  }
}

class ImageOptimizationResult {
  final File originalFile;
  final File optimizedFile;
  final File thumbnailFile;
  final int originalSize;
  final int optimizedSize;
  final int thumbnailSize;
  final double compressionRatio;

  ImageOptimizationResult({
    required this.originalFile,
    required this.optimizedFile,
    required this.thumbnailFile,
    required this.originalSize,
    required this.optimizedSize,
    required this.thumbnailSize,
    required this.compressionRatio,
  });

  /// Get size reduction percentage
  double get sizeReductionPercentage =>
      ((originalSize - optimizedSize) / originalSize) * 100;

  /// Get human readable size strings
  String get originalSizeString => _formatBytes(originalSize);
  String get optimizedSizeString => _formatBytes(optimizedSize);
  String get thumbnailSizeString => _formatBytes(thumbnailSize);

  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  @override
  String toString() {
    return 'ImageOptimization: $originalSizeString → $optimizedSizeString '
        '(${sizeReductionPercentage.toStringAsFixed(1)}% reduction)';
  }
}
