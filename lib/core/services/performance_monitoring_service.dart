import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:billionaires_social/core/services/memory_management_service.dart';

/// Comprehensive performance monitoring service
/// Tracks app performance metrics, memory usage, and user experience
class PerformanceMonitoringService {
  static final PerformanceMonitoringService _instance =
      PerformanceMonitoringService._internal();
  factory PerformanceMonitoringService() => _instance;
  PerformanceMonitoringService._internal();

  // Performance metrics storage
  final Map<String, PerformanceMetric> _metrics = {};
  final List<MemorySnapshot> _memorySnapshots = [];
  final List<FrameTimingInfo> _frameTimings = [];

  // Monitoring state
  bool _isMonitoring = false;
  Timer? _memoryMonitorTimer;
  Timer? _performanceReportTimer;

  // Configuration
  static const int maxMetricsHistory = 1000;
  static const int maxMemorySnapshots = 100;
  static const int maxFrameTimings = 500;
  static const Duration memoryCheckInterval = Duration(seconds: 30);
  static const Duration reportInterval = Duration(minutes: 5);

  /// Start performance monitoring
  Future<void> startMonitoring() async {
    if (_isMonitoring) return;

    _isMonitoring = true;

    // Start memory monitoring
    _startMemoryMonitoring();

    // Start frame timing monitoring
    _startFrameTimingMonitoring();

    // Start periodic reporting
    _startPeriodicReporting();

    debugPrint('🚀 Performance monitoring started');
  }

  /// Stop performance monitoring
  void stopMonitoring() {
    _isMonitoring = false;

    // Cancel and unregister timers
    if (_memoryMonitorTimer != null) {
      _memoryMonitorTimer!.cancel();
      MemoryManagementService().unregisterTimer(_memoryMonitorTimer!);
      _memoryMonitorTimer = null;
    }

    if (_performanceReportTimer != null) {
      _performanceReportTimer!.cancel();
      MemoryManagementService().unregisterTimer(_performanceReportTimer!);
      _performanceReportTimer = null;
    }

    debugPrint('⏹️ Performance monitoring stopped');
  }

  /// Track a performance metric
  void trackMetric(
    String name,
    double value, {
    String? category,
    Map<String, dynamic>? metadata,
  }) {
    final metric = PerformanceMetric(
      name: name,
      value: value,
      timestamp: DateTime.now(),
      category: category ?? 'general',
      metadata: metadata ?? {},
    );

    _metrics[name] = metric;

    // Cleanup old metrics
    if (_metrics.length > maxMetricsHistory) {
      final oldestKey = _metrics.keys.first;
      _metrics.remove(oldestKey);
    }

    debugPrint('📊 Metric tracked: $name = $value');
  }

  /// Track operation duration
  Future<T> trackOperation<T>(
    String operationName,
    Future<T> Function() operation, {
    String? category,
    Map<String, dynamic>? metadata,
  }) async {
    final stopwatch = Stopwatch()..start();

    try {
      final result = await operation();
      stopwatch.stop();

      trackMetric(
        '${operationName}_duration',
        stopwatch.elapsedMilliseconds.toDouble(),
        category: category ?? 'operation',
        metadata: {...?metadata, 'success': true, 'operation': operationName},
      );

      return result;
    } catch (error) {
      stopwatch.stop();

      trackMetric(
        '${operationName}_duration',
        stopwatch.elapsedMilliseconds.toDouble(),
        category: category ?? 'operation',
        metadata: {
          ...?metadata,
          'success': false,
          'error': error.toString(),
          'operation': operationName,
        },
      );

      rethrow;
    }
  }

  /// Get current performance summary
  PerformanceSummary getPerformanceSummary() {
    final currentMemory = _getCurrentMemoryUsage();
    final recentFrameTimings = _frameTimings.take(50).toList();
    final avgFrameTime = recentFrameTimings.isEmpty
        ? 0.0
        : recentFrameTimings.map((f) => f.totalTime).reduce((a, b) => a + b) /
              recentFrameTimings.length;

    return PerformanceSummary(
      totalMetrics: _metrics.length,
      memoryUsageMB: currentMemory,
      averageFrameTimeMs: avgFrameTime,
      recentMetrics: _metrics.values.take(10).toList(),
      memoryTrend: _getMemoryTrend(),
      performanceScore: _calculatePerformanceScore(),
    );
  }

  /// Get detailed performance report
  Map<String, dynamic> getDetailedReport() {
    return {
      'timestamp': DateTime.now().toIso8601String(),
      'monitoring_duration': _isMonitoring ? 'active' : 'inactive',
      'metrics_count': _metrics.length,
      'memory_snapshots': _memorySnapshots.length,
      'frame_timings': _frameTimings.length,
      'current_memory_mb': _getCurrentMemoryUsage(),
      'performance_score': _calculatePerformanceScore(),
      'top_metrics': _getTopMetrics(),
      'memory_trend': _getMemoryTrend(),
      'frame_performance': _getFramePerformanceStats(),
      'recommendations': _getPerformanceRecommendations(),
    };
  }

  // Private methods
  void _startMemoryMonitoring() {
    _memoryMonitorTimer = Timer.periodic(memoryCheckInterval, (timer) {
      _captureMemorySnapshot();
    });

    // Register timer for memory management
    if (_memoryMonitorTimer != null) {
      MemoryManagementService().registerTimer(_memoryMonitorTimer!);
    }
  }

  void _startFrameTimingMonitoring() {
    // Note: In a real implementation, you would use Flutter's frame timing APIs
    // This is a simplified version for demonstration
    Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!_isMonitoring) {
        timer.cancel();
        return;
      }

      // Simulate frame timing data
      final frameTime = _simulateFrameTime();
      _frameTimings.add(frameTime);

      if (_frameTimings.length > maxFrameTimings) {
        _frameTimings.removeAt(0);
      }
    });
  }

  void _startPeriodicReporting() {
    _performanceReportTimer = Timer.periodic(reportInterval, (timer) {
      _generatePerformanceReport();
    });

    // Register timer for memory management
    if (_performanceReportTimer != null) {
      MemoryManagementService().registerTimer(_performanceReportTimer!);
    }
  }

  void _captureMemorySnapshot() {
    final memoryUsage = _getCurrentMemoryUsage();
    final snapshot = MemorySnapshot(
      timestamp: DateTime.now(),
      memoryUsageMB: memoryUsage,
    );

    _memorySnapshots.add(snapshot);

    if (_memorySnapshots.length > maxMemorySnapshots) {
      _memorySnapshots.removeAt(0);
    }

    // Check for memory warnings
    if (memoryUsage > 200) {
      // 200MB threshold
      debugPrint(
        '⚠️ High memory usage detected: ${memoryUsage.toStringAsFixed(1)}MB',
      );
    }
  }

  double _getCurrentMemoryUsage() {
    // In a real implementation, you would use platform-specific APIs
    // This is a simplified simulation
    return 50.0 + (DateTime.now().millisecondsSinceEpoch % 100);
  }

  FrameTimingInfo _simulateFrameTime() {
    // Simulate realistic frame timing
    final baseTime = 16.67; // 60 FPS target
    final variation = (DateTime.now().millisecondsSinceEpoch % 10) - 5;
    return FrameTimingInfo(
      timestamp: DateTime.now(),
      totalTime: baseTime + variation,
    );
  }

  double _calculatePerformanceScore() {
    if (_metrics.isEmpty) return 100.0;

    double score = 100.0;

    // Factor in memory usage
    final currentMemory = _getCurrentMemoryUsage();
    if (currentMemory > 150) {
      score -= 20;
    } else if (currentMemory > 100) {
      score -= 10;
    }

    // Factor in frame performance
    final recentFrames = _frameTimings.take(30).toList();
    if (recentFrames.isNotEmpty) {
      final avgFrameTime =
          recentFrames.map((f) => f.totalTime).reduce((a, b) => a + b) /
          recentFrames.length;
      if (avgFrameTime > 20) {
        score -= 15; // Poor frame rate
      } else if (avgFrameTime > 18) {
        score -= 5; // Slightly poor frame rate
      }
    }

    return score.clamp(0.0, 100.0);
  }

  String _getMemoryTrend() {
    if (_memorySnapshots.length < 2) return 'stable';

    final recent = _memorySnapshots.length > 5
        ? _memorySnapshots.sublist(_memorySnapshots.length - 5)
        : _memorySnapshots;
    final first = recent.first.memoryUsageMB;
    final last = recent.last.memoryUsageMB;

    final change = ((last - first) / first) * 100;

    if (change > 10) return 'increasing';
    if (change < -10) return 'decreasing';
    return 'stable';
  }

  List<Map<String, dynamic>> _getTopMetrics() {
    final sortedMetrics = _metrics.values.toList()
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));

    return sortedMetrics
        .take(5)
        .map(
          (m) => {
            'name': m.name,
            'value': m.value,
            'category': m.category,
            'timestamp': m.timestamp.toIso8601String(),
          },
        )
        .toList();
  }

  Map<String, dynamic> _getFramePerformanceStats() {
    if (_frameTimings.isEmpty) return {'status': 'no_data'};

    final recent = _frameTimings.take(100).toList();
    final avgTime =
        recent.map((f) => f.totalTime).reduce((a, b) => a + b) / recent.length;
    final maxTime = recent
        .map((f) => f.totalTime)
        .reduce((a, b) => a > b ? a : b);
    final minTime = recent
        .map((f) => f.totalTime)
        .reduce((a, b) => a < b ? a : b);

    return {
      'average_frame_time_ms': avgTime,
      'max_frame_time_ms': maxTime,
      'min_frame_time_ms': minTime,
      'target_fps': 60,
      'actual_fps': 1000 / avgTime,
      'frame_drops': recent.where((f) => f.totalTime > 20).length,
    };
  }

  List<String> _getPerformanceRecommendations() {
    final recommendations = <String>[];

    final currentMemory = _getCurrentMemoryUsage();
    if (currentMemory > 150) {
      recommendations.add(
        'Consider optimizing memory usage - current: ${currentMemory.toStringAsFixed(1)}MB',
      );
    }

    final recentFrames = _frameTimings.take(30).toList();
    if (recentFrames.isNotEmpty) {
      final avgFrameTime =
          recentFrames.map((f) => f.totalTime).reduce((a, b) => a + b) /
          recentFrames.length;
      if (avgFrameTime > 20) {
        recommendations.add(
          'Frame rate is below target - consider optimizing UI rendering',
        );
      }
    }

    if (_metrics.length > maxMetricsHistory * 0.8) {
      recommendations.add(
        'Metrics storage is getting full - consider clearing old data',
      );
    }

    return recommendations;
  }

  void _generatePerformanceReport() {
    final report = getDetailedReport();
    debugPrint(
      '📈 Performance Report Generated: Score ${report['performance_score']}',
    );

    // In a real app, you might send this to analytics or logging service
  }
}

// Data classes
class PerformanceMetric {
  final String name;
  final double value;
  final DateTime timestamp;
  final String category;
  final Map<String, dynamic> metadata;

  PerformanceMetric({
    required this.name,
    required this.value,
    required this.timestamp,
    required this.category,
    required this.metadata,
  });
}

class MemorySnapshot {
  final DateTime timestamp;
  final double memoryUsageMB;

  MemorySnapshot({required this.timestamp, required this.memoryUsageMB});
}

class FrameTimingInfo {
  final DateTime timestamp;
  final double totalTime;

  FrameTimingInfo({required this.timestamp, required this.totalTime});
}

class PerformanceSummary {
  final int totalMetrics;
  final double memoryUsageMB;
  final double averageFrameTimeMs;
  final List<PerformanceMetric> recentMetrics;
  final String memoryTrend;
  final double performanceScore;

  PerformanceSummary({
    required this.totalMetrics,
    required this.memoryUsageMB,
    required this.averageFrameTimeMs,
    required this.recentMetrics,
    required this.memoryTrend,
    required this.performanceScore,
  });
}
