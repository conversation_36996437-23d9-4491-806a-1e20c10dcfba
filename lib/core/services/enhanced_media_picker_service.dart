import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:video_player/video_player.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

/// Enhanced media picker service supporting multiple images, videos, and compression
class EnhancedMediaPickerService {
  static final EnhancedMediaPickerService _instance = EnhancedMediaPickerService._internal();
  factory EnhancedMediaPickerService() => _instance;
  EnhancedMediaPickerService._internal();

  final ImagePicker _picker = ImagePicker();

  /// Pick multiple media files (images and videos)
  Future<List<MediaFile>> pickMultipleMedia({
    int maxImages = 10,
    int maxVideos = 5,
    bool allowImages = true,
    bool allowVideos = true,
    int imageQuality = 80,
    double? maxWidth,
    double? maxHeight,
    Duration? maxVideoDuration,
  }) async {
    try {
      final List<XFile> pickedFiles = await _picker.pickMultipleMedia(
        imageQuality: imageQuality,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
      );

      if (pickedFiles.isEmpty) return [];

      final List<MediaFile> mediaFiles = [];
      int imageCount = 0;
      int videoCount = 0;

      for (final pickedFile in pickedFiles) {
        final file = File(pickedFile.path);
        final isVideo = _isVideoFile(pickedFile.path);
        final isImage = _isImageFile(pickedFile.path);

        // Check file type restrictions
        if (isVideo && !allowVideos) continue;
        if (isImage && !allowImages) continue;
        if (!isVideo && !isImage) continue;

        // Check count limits
        if (isVideo && videoCount >= maxVideos) continue;
        if (isImage && imageCount >= maxImages) continue;

        // Validate file
        if (!await file.exists() || await file.length() == 0) continue;

        // Check video duration if specified
        if (isVideo && maxVideoDuration != null) {
          final duration = await _getVideoDuration(file);
          if (duration != null && duration > maxVideoDuration) continue;
        }

        final mediaFile = MediaFile(
          file: file,
          type: isVideo ? MediaFileType.video : MediaFileType.image,
          originalPath: pickedFile.path,
          size: await file.length(),
          duration: isVideo ? await _getVideoDuration(file) : null,
        );

        mediaFiles.add(mediaFile);
        
        if (isVideo) videoCount++;
        if (isImage) imageCount++;
      }

      return mediaFiles;
    } catch (e) {
      debugPrint('❌ Error picking multiple media: $e');
      return [];
    }
  }

  /// Pick single media file
  Future<MediaFile?> pickSingleMedia({
    ImageSource source = ImageSource.gallery,
    bool allowImages = true,
    bool allowVideos = true,
    int imageQuality = 80,
    double? maxWidth,
    double? maxHeight,
    Duration? maxVideoDuration,
  }) async {
    try {
      final XFile? pickedFile = await _picker.pickMedia(
        imageQuality: imageQuality,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
      );

      if (pickedFile == null) return null;

      final file = File(pickedFile.path);
      final isVideo = _isVideoFile(pickedFile.path);
      final isImage = _isImageFile(pickedFile.path);

      // Check file type restrictions
      if (isVideo && !allowVideos) return null;
      if (isImage && !allowImages) return null;
      if (!isVideo && !isImage) return null;

      // Validate file
      if (!await file.exists() || await file.length() == 0) return null;

      // Check video duration if specified
      if (isVideo && maxVideoDuration != null) {
        final duration = await _getVideoDuration(file);
        if (duration != null && duration > maxVideoDuration) return null;
      }

      return MediaFile(
        file: file,
        type: isVideo ? MediaFileType.video : MediaFileType.image,
        originalPath: pickedFile.path,
        size: await file.length(),
        duration: isVideo ? await _getVideoDuration(file) : null,
      );
    } catch (e) {
      debugPrint('❌ Error picking single media: $e');
      return null;
    }
  }

  /// Compress image file
  Future<File?> compressImage(
    File file, {
    int quality = 80,
    int? maxWidth,
    int? maxHeight,
  }) async {
    try {
      final dir = await getTemporaryDirectory();
      final targetPath = path.join(
        dir.path,
        'compressed_${DateTime.now().millisecondsSinceEpoch}.jpg',
      );

      final compressedFile = await FlutterImageCompress.compressAndGetFile(
        file.absolute.path,
        targetPath,
        quality: quality,
        minWidth: maxWidth ?? 1080,
        minHeight: maxHeight ?? 1920,
        format: CompressFormat.jpeg,
      );

      return compressedFile != null ? File(compressedFile.path) : null;
    } catch (e) {
      debugPrint('❌ Error compressing image: $e');
      return null;
    }
  }

  /// Get video duration
  Future<Duration?> _getVideoDuration(File videoFile) async {
    try {
      final controller = VideoPlayerController.file(videoFile);
      await controller.initialize();
      final duration = controller.value.duration;
      await controller.dispose();
      return duration;
    } catch (e) {
      debugPrint('❌ Error getting video duration: $e');
      return null;
    }
  }

  /// Check if file is a video
  bool _isVideoFile(String path) {
    final extension = path.toLowerCase().split('.').last;
    return ['mp4', 'mov', 'avi', 'mkv', 'webm', '3gp'].contains(extension);
  }

  /// Check if file is an image
  bool _isImageFile(String path) {
    final extension = path.toLowerCase().split('.').last;
    return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(extension);
  }

  /// Validate media file
  Future<bool> validateMediaFile(MediaFile mediaFile, {
    int maxSizeMB = 100,
    Duration? maxVideoDuration,
  }) async {
    try {
      // Check file exists
      if (!await mediaFile.file.exists()) return false;

      // Check file size
      final sizeMB = mediaFile.size / (1024 * 1024);
      if (sizeMB > maxSizeMB) return false;

      // Check video duration
      if (mediaFile.type == MediaFileType.video && 
          maxVideoDuration != null && 
          mediaFile.duration != null) {
        if (mediaFile.duration! > maxVideoDuration) return false;
      }

      return true;
    } catch (e) {
      debugPrint('❌ Error validating media file: $e');
      return false;
    }
  }
}

/// Media file model
class MediaFile {
  final File file;
  final MediaFileType type;
  final String originalPath;
  final int size;
  final Duration? duration;

  MediaFile({
    required this.file,
    required this.type,
    required this.originalPath,
    required this.size,
    this.duration,
  });

  /// Get file size in MB
  double get sizeInMB => size / (1024 * 1024);

  /// Get file extension
  String get extension => path.extension(file.path).toLowerCase();

  /// Check if file is valid
  Future<bool> get isValid async {
    return await file.exists() && size > 0;
  }
}

/// Media file types
enum MediaFileType { image, video }
