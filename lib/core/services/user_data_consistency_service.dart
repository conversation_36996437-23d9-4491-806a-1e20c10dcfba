import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';

/// Service to ensure user data consistency across posts, stories, and profiles
/// This prevents issues where posts/stories show wrong profile pictures or user data
class UserDataConsistencyService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Validate and fix user data consistency across all content
  static Future<void> validateAndFixUserDataConsistency(String userId) async {
    try {
      debugPrint('🔧 Validating user data consistency for: $userId');

      // Get the latest user profile data
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (!userDoc.exists) {
        debugPrint('❌ User document not found: $userId');
        return;
      }

      final userData = userDoc.data()!;
      final correctName = userData['name'] as String? ?? 'Unknown';
      final correctUsername = userData['username'] as String? ?? 'unknown';
      final correctProfilePictureUrl = userData['profilePictureUrl'] as String? ?? '';

      debugPrint('✅ Correct user data:');
      debugPrint('   - Name: $correctName');
      debugPrint('   - Username: $correctUsername');
      debugPrint('   - Profile Picture: $correctProfilePictureUrl');

      // Fix posts data consistency
      await _fixPostsDataConsistency(userId, correctName, correctUsername, correctProfilePictureUrl);

      // Fix stories data consistency
      await _fixStoriesDataConsistency(userId, correctName, correctUsername, correctProfilePictureUrl);

      debugPrint('✅ User data consistency validation complete for: $userId');
    } catch (e) {
      debugPrint('❌ Error validating user data consistency: $e');
    }
  }

  /// Fix posts data consistency
  static Future<void> _fixPostsDataConsistency(
    String userId,
    String correctName,
    String correctUsername,
    String correctProfilePictureUrl,
  ) async {
    try {
      debugPrint('🔧 Fixing posts data consistency for: $userId');

      final postsQuery = await _firestore
          .collection('posts')
          .where('userId', isEqualTo: userId)
          .get();

      final batch = _firestore.batch();
      int updatedCount = 0;

      for (final doc in postsQuery.docs) {
        final postData = doc.data();
        bool needsUpdate = false;
        final Map<String, dynamic> updates = {};

        // Check username
        if (postData['username'] != correctUsername) {
          updates['username'] = correctUsername;
          needsUpdate = true;
          debugPrint('   📝 Post ${doc.id}: Updating username from "${postData['username']}" to "$correctUsername"');
        }

        // Check user avatar URL
        if (postData['userAvatarUrl'] != correctProfilePictureUrl) {
          updates['userAvatarUrl'] = correctProfilePictureUrl;
          needsUpdate = true;
          debugPrint('   🖼️ Post ${doc.id}: Updating avatar URL');
        }

        // Check if user name exists in post (some posts might have this field)
        if (postData.containsKey('userName') && postData['userName'] != correctName) {
          updates['userName'] = correctName;
          needsUpdate = true;
          debugPrint('   👤 Post ${doc.id}: Updating user name from "${postData['userName']}" to "$correctName"');
        }

        if (needsUpdate) {
          batch.update(doc.reference, updates);
          updatedCount++;
        }
      }

      if (updatedCount > 0) {
        await batch.commit();
        debugPrint('✅ Updated $updatedCount posts for user: $userId');
      } else {
        debugPrint('✅ All posts are consistent for user: $userId');
      }
    } catch (e) {
      debugPrint('❌ Error fixing posts data consistency: $e');
    }
  }

  /// Fix stories data consistency
  static Future<void> _fixStoriesDataConsistency(
    String userId,
    String correctName,
    String correctUsername,
    String correctProfilePictureUrl,
  ) async {
    try {
      debugPrint('🔧 Fixing stories data consistency for: $userId');

      final storiesQuery = await _firestore
          .collection('stories')
          .where('userId', isEqualTo: userId)
          .get();

      final batch = _firestore.batch();
      int updatedCount = 0;

      for (final doc in storiesQuery.docs) {
        final storyData = doc.data();
        bool needsUpdate = false;
        final Map<String, dynamic> updates = {};

        // Check username (if exists)
        if (storyData.containsKey('username') && storyData['username'] != correctUsername) {
          updates['username'] = correctUsername;
          needsUpdate = true;
          debugPrint('   📝 Story ${doc.id}: Updating username from "${storyData['username']}" to "$correctUsername"');
        }

        // Check user name
        if (storyData.containsKey('userName') && storyData['userName'] != correctName) {
          updates['userName'] = correctName;
          needsUpdate = true;
          debugPrint('   👤 Story ${doc.id}: Updating user name from "${storyData['userName']}" to "$correctName"');
        }

        // Check user avatar URL
        if (storyData.containsKey('userAvatarUrl') && storyData['userAvatarUrl'] != correctProfilePictureUrl) {
          updates['userAvatarUrl'] = correctProfilePictureUrl;
          needsUpdate = true;
          debugPrint('   🖼️ Story ${doc.id}: Updating avatar URL');
        }

        if (needsUpdate) {
          batch.update(doc.reference, updates);
          updatedCount++;
        }
      }

      if (updatedCount > 0) {
        await batch.commit();
        debugPrint('✅ Updated $updatedCount stories for user: $userId');
      } else {
        debugPrint('✅ All stories are consistent for user: $userId');
      }
    } catch (e) {
      debugPrint('❌ Error fixing stories data consistency: $e');
    }
  }

  /// Validate current user's data consistency
  static Future<void> validateCurrentUserDataConsistency() async {
    final currentUser = _auth.currentUser;
    if (currentUser != null) {
      await validateAndFixUserDataConsistency(currentUser.uid);
    }
  }

  /// Batch validate multiple users' data consistency
  static Future<void> batchValidateUsersDataConsistency(List<String> userIds) async {
    debugPrint('🔧 Batch validating data consistency for ${userIds.length} users');

    for (final userId in userIds) {
      await validateAndFixUserDataConsistency(userId);
      // Small delay to avoid overwhelming Firestore
      await Future.delayed(const Duration(milliseconds: 200));
    }

    debugPrint('✅ Batch data consistency validation complete');
  }

  /// Get user's current profile data for consistency checks
  static Future<Map<String, dynamic>?> getUserProfileData(String userId) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (userDoc.exists) {
        return userDoc.data();
      }
      return null;
    } catch (e) {
      debugPrint('❌ Error getting user profile data: $e');
      return null;
    }
  }

  /// Check if user data is consistent across posts and stories
  static Future<bool> isUserDataConsistent(String userId) async {
    try {
      final userData = await getUserProfileData(userId);
      if (userData == null) return false;

      final correctName = userData['name'] as String? ?? 'Unknown';
      final correctUsername = userData['username'] as String? ?? 'unknown';
      final correctProfilePictureUrl = userData['profilePictureUrl'] as String? ?? '';

      // Check posts consistency
      final postsQuery = await _firestore
          .collection('posts')
          .where('userId', isEqualTo: userId)
          .limit(5) // Sample check
          .get();

      for (final doc in postsQuery.docs) {
        final postData = doc.data();
        if (postData['username'] != correctUsername ||
            postData['userAvatarUrl'] != correctProfilePictureUrl) {
          debugPrint('❌ Inconsistent post data found for user: $userId');
          return false;
        }
      }

      // Check stories consistency
      final storiesQuery = await _firestore
          .collection('stories')
          .where('userId', isEqualTo: userId)
          .limit(5) // Sample check
          .get();

      for (final doc in storiesQuery.docs) {
        final storyData = doc.data();
        if ((storyData.containsKey('username') && storyData['username'] != correctUsername) ||
            (storyData.containsKey('userName') && storyData['userName'] != correctName) ||
            (storyData.containsKey('userAvatarUrl') && storyData['userAvatarUrl'] != correctProfilePictureUrl)) {
          debugPrint('❌ Inconsistent story data found for user: $userId');
          return false;
        }
      }

      debugPrint('✅ User data is consistent for: $userId');
      return true;
    } catch (e) {
      debugPrint('❌ Error checking user data consistency: $e');
      return false;
    }
  }

  /// Auto-fix data consistency when user updates their profile
  static Future<void> onProfileUpdate(String userId) async {
    debugPrint('🔄 Profile updated, fixing data consistency for: $userId');
    await validateAndFixUserDataConsistency(userId);
  }
}
