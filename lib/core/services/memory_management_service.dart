import 'dart:async';
import 'package:flutter/foundation.dart';

/// Global memory management service to prevent memory leaks and optimize usage
class MemoryManagementService {
  static final MemoryManagementService _instance =
      MemoryManagementService._internal();
  factory MemoryManagementService() => _instance;
  MemoryManagementService._internal();

  // Memory monitoring
  Timer? _memoryMonitorTimer;
  final List<MemorySnapshot> _memoryHistory = [];
  final Set<Timer> _activeTimers = {};
  final Set<StreamSubscription> _activeSubscriptions = {};

  // Memory limits and thresholds
  static const int maxMemoryMB = 200;
  static const int warningMemoryMB = 150;
  static const int criticalMemoryMB = 180;
  static const int maxMemoryHistory = 100;

  // Monitoring configuration
  static const Duration monitoringInterval = Duration(seconds: 30);
  bool _isMonitoring = false;

  /// Start memory monitoring
  Future<void> startMonitoring() async {
    if (_isMonitoring) return;

    _isMonitoring = true;
    _memoryMonitorTimer = Timer.periodic(monitoringInterval, (timer) {
      _checkMemoryUsage();
    });

    debugPrint('🧠 Memory monitoring started');
  }

  /// Stop memory monitoring
  void stopMonitoring() {
    _isMonitoring = false;
    _memoryMonitorTimer?.cancel();
    _memoryMonitorTimer = null;
    debugPrint('🧠 Memory monitoring stopped');
  }

  /// Register a timer for automatic cleanup tracking
  void registerTimer(Timer timer) {
    _activeTimers.add(timer);
    debugPrint('⏰ Timer registered (${_activeTimers.length} active)');
  }

  /// Register a stream subscription for automatic cleanup tracking
  void registerSubscription(StreamSubscription subscription) {
    _activeSubscriptions.add(subscription);
    debugPrint(
      '📡 Subscription registered (${_activeSubscriptions.length} active)',
    );
  }

  /// Unregister a timer
  void unregisterTimer(Timer timer) {
    _activeTimers.remove(timer);
    debugPrint('⏰ Timer unregistered (${_activeTimers.length} active)');
  }

  /// Unregister a stream subscription
  void unregisterSubscription(StreamSubscription subscription) {
    _activeSubscriptions.remove(subscription);
    debugPrint(
      '📡 Subscription unregistered (${_activeSubscriptions.length} active)',
    );
  }

  /// Cancel all active timers
  void cancelAllTimers() {
    final count = _activeTimers.length;
    for (final timer in _activeTimers.toList()) {
      timer.cancel();
    }
    _activeTimers.clear();
    debugPrint('⏰ Cancelled $count timers');
  }

  /// Cancel all active subscriptions
  void cancelAllSubscriptions() {
    final count = _activeSubscriptions.length;
    for (final subscription in _activeSubscriptions.toList()) {
      subscription.cancel();
    }
    _activeSubscriptions.clear();
    debugPrint('📡 Cancelled $count subscriptions');
  }

  /// Force garbage collection (use sparingly)
  void forceGarbageCollection() {
    if (kDebugMode) {
      debugPrint('🗑️ Forcing garbage collection');
      // Note: There's no direct way to force GC in Dart/Flutter
      // This is a placeholder for potential future implementation
    }
  }

  /// Get current memory usage in MB
  Future<double> getCurrentMemoryUsage() async {
    // Use estimation method directly to avoid platform channel issues
    // This provides consistent memory tracking across all platforms
    return _estimateMemoryUsage();
  }

  /// Get memory usage statistics
  MemoryStats getMemoryStats() {
    final currentSnapshot = _memoryHistory.isNotEmpty
        ? _memoryHistory.last
        : null;
    final currentMemory = currentSnapshot?.memoryUsageMB ?? 0.0;

    return MemoryStats(
      currentMemoryMB: currentMemory,
      maxMemoryMB: maxMemoryMB.toDouble(),
      warningThresholdMB: warningMemoryMB.toDouble(),
      criticalThresholdMB: criticalMemoryMB.toDouble(),
      memoryUsagePercentage: (currentMemory / maxMemoryMB) * 100,
      activeTimers: _activeTimers.length,
      activeSubscriptions: _activeSubscriptions.length,
      memoryHistory: List.from(_memoryHistory),
      isNearLimit: currentMemory > warningMemoryMB,
      isCritical: currentMemory > criticalMemoryMB,
      trend: _calculateMemoryTrend(),
    );
  }

  /// Clean up resources when memory is high
  Future<void> performMemoryCleanup() async {
    debugPrint('🧹 Performing memory cleanup');

    // Cancel inactive timers
    final inactiveTimers = _activeTimers
        .where((timer) => !timer.isActive)
        .toList();
    for (final timer in inactiveTimers) {
      _activeTimers.remove(timer);
    }

    // Remove old memory history
    if (_memoryHistory.length > maxMemoryHistory) {
      _memoryHistory.removeRange(0, _memoryHistory.length - maxMemoryHistory);
    }

    // Force garbage collection
    forceGarbageCollection();

    debugPrint('🧹 Memory cleanup completed');
  }

  /// Private methods
  Future<void> _checkMemoryUsage() async {
    final currentMemory = await getCurrentMemoryUsage();
    final snapshot = MemorySnapshot(
      timestamp: DateTime.now(),
      memoryUsageMB: currentMemory,
      activeTimers: _activeTimers.length,
      activeSubscriptions: _activeSubscriptions.length,
    );

    _memoryHistory.add(snapshot);

    // Limit history size
    if (_memoryHistory.length > maxMemoryHistory) {
      _memoryHistory.removeAt(0);
    }

    // Check thresholds
    if (currentMemory > criticalMemoryMB) {
      debugPrint(
        '🚨 CRITICAL: Memory usage is ${currentMemory.toStringAsFixed(1)}MB (>${criticalMemoryMB}MB)',
      );
      await performMemoryCleanup();
    } else if (currentMemory > warningMemoryMB) {
      debugPrint(
        '⚠️ WARNING: Memory usage is ${currentMemory.toStringAsFixed(1)}MB (>${warningMemoryMB}MB)',
      );
    } else {
      debugPrint('✅ Memory usage: ${currentMemory.toStringAsFixed(1)}MB');
    }
  }

  double _estimateMemoryUsage() {
    // Rough estimation based on various factors
    double estimate = 50.0; // Base app memory

    // Add memory for active timers and subscriptions
    estimate += _activeTimers.length * 0.1;
    estimate += _activeSubscriptions.length * 0.2;

    // Add memory for history
    estimate += _memoryHistory.length * 0.01;

    // Add some randomness to simulate real usage
    estimate += (DateTime.now().millisecondsSinceEpoch % 50);

    return estimate;
  }

  String _calculateMemoryTrend() {
    if (_memoryHistory.length < 3) return 'stable';

    final recent = _memoryHistory
        .skip(_memoryHistory.length - 3)
        .map((s) => s.memoryUsageMB)
        .toList();
    final isIncreasing = recent[2] > recent[1] && recent[1] > recent[0];
    final isDecreasing = recent[2] < recent[1] && recent[1] < recent[0];

    if (isIncreasing) return 'increasing';
    if (isDecreasing) return 'decreasing';
    return 'stable';
  }

  /// Dispose of the service
  void dispose() {
    stopMonitoring();
    cancelAllTimers();
    cancelAllSubscriptions();
    _memoryHistory.clear();
    debugPrint('🧠 Memory management service disposed');
  }
}

/// Memory snapshot for tracking
class MemorySnapshot {
  final DateTime timestamp;
  final double memoryUsageMB;
  final int activeTimers;
  final int activeSubscriptions;

  MemorySnapshot({
    required this.timestamp,
    required this.memoryUsageMB,
    required this.activeTimers,
    required this.activeSubscriptions,
  });

  @override
  String toString() =>
      'MemorySnapshot(${memoryUsageMB.toStringAsFixed(1)}MB, $activeTimers timers, $activeSubscriptions subs)';
}

/// Memory statistics
class MemoryStats {
  final double currentMemoryMB;
  final double maxMemoryMB;
  final double warningThresholdMB;
  final double criticalThresholdMB;
  final double memoryUsagePercentage;
  final int activeTimers;
  final int activeSubscriptions;
  final List<MemorySnapshot> memoryHistory;
  final bool isNearLimit;
  final bool isCritical;
  final String trend;

  MemoryStats({
    required this.currentMemoryMB,
    required this.maxMemoryMB,
    required this.warningThresholdMB,
    required this.criticalThresholdMB,
    required this.memoryUsagePercentage,
    required this.activeTimers,
    required this.activeSubscriptions,
    required this.memoryHistory,
    required this.isNearLimit,
    required this.isCritical,
    required this.trend,
  });

  @override
  String toString() =>
      'MemoryStats(${currentMemoryMB.toStringAsFixed(1)}MB/${maxMemoryMB.toStringAsFixed(1)}MB, $trend)';
}
