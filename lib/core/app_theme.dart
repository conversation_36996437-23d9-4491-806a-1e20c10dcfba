import 'package:flutter/material.dart';

class AppTheme {
  // Luxury Color Palette
  static const Color primaryGold = Color(0xFFD4AF37);
  static const Color secondaryGold = Color(0xFFB8860B);
  static const Color darkGold = Color(0xFF8B6914);
  static const Color luxuryBlack = Color(0xFF1A1A1A);
  static const Color luxuryWhite = Color(0xFFF8F8F8);
  static const Color luxuryGrey = Color(0xFF2C2C2C);
  static const Color accentBlue = Color(0xFF1E3A8A);
  static const Color accentPurple = Color(0xFF7C3AED);
  static const Color accentEmerald = Color(0xFF059669);

  // --- PRIMARY COLORS ---
  static const Color primaryColor = Color(
    0xFF121212,
  ); // Very dark grey, almost black
  static const Color accentColor = Color(0xFFD4AF37); // Gold accent
  static const Color secondaryAccentColor = Color(
    0xFFEAEAEA,
  ); // Light grey for text/icons

  // --- FONT STYLES ---
  static final FontStyles fontStyles = FontStyles();

  // --- UI COMPONENTS THEME ---
  static final ThemeData darkTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,
    scaffoldBackgroundColor: primaryColor,
    primaryColor: primaryColor,
    colorScheme: const ColorScheme.dark(
      primary: accentColor,
      secondary: secondaryAccentColor,
      surface: luxuryGrey,
      onPrimary: luxuryBlack,
      onSecondary: luxuryWhite,
      onSurface: luxuryWhite,
    ),
    appBarTheme: AppBarTheme(
      backgroundColor: primaryColor,
      elevation: 0,
      iconTheme: const IconThemeData(color: secondaryAccentColor),
      titleTextStyle: fontStyles.title.copyWith(color: luxuryWhite),
    ),
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      backgroundColor: luxuryBlack,
      selectedItemColor: accentColor,
      unselectedItemColor: secondaryAccentColor,
      type: BottomNavigationBarType.fixed,
      elevation: 8,
    ),
    cardTheme: CardThemeData(
      color: luxuryGrey,
      elevation: 4,
      shadowColor: Colors.black.withValues(alpha: 0.3),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: accentColor,
        foregroundColor: luxuryBlack,
        elevation: 2,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        textStyle: fontStyles.button,
      ),
    ),
    textSelectionTheme: const TextSelectionThemeData(
      cursorColor: accentColor,
      selectionColor: accentColor,
      selectionHandleColor: accentColor,
    ),
    inputDecorationTheme: InputDecorationTheme(
      // Define your input decoration theme here if needed
    ),
    textTheme: TextTheme(
      displayLarge: fontStyles.display,
      headlineMedium: fontStyles.title,
      bodyMedium: fontStyles.body,
      labelSmall: fontStyles.caption,
    ),
  );

  /// Reusable loading animation widget
  static Widget loadingIndicator() {
    return const CircularProgressIndicator(
      valueColor: AlwaysStoppedAnimation<Color>(accentColor),
    );
  }

  /// Get appropriate text color based on background for optimal contrast
  static Color getContrastTextColor(Color backgroundColor) {
    // Calculate luminance of background color
    final luminance = backgroundColor.computeLuminance();

    // If background is light, use dark text; if dark, use light text
    if (luminance > 0.5) {
      return Colors.black; // Dark text on light background
    } else {
      return Colors.white; // Light text on dark background
    }
  }

  /// Get high contrast text color for critical UI elements
  static Color getHighContrastTextColor(Color backgroundColor) {
    final luminance = backgroundColor.computeLuminance();

    if (luminance > 0.5) {
      return const Color(0xFF000000); // Pure black on light background
    } else {
      return const Color(0xFFFFFFFF); // Pure white on dark background
    }
  }

  /// Ensure text style has proper contrast against background
  static TextStyle ensureTextContrast(TextStyle style, Color backgroundColor) {
    return style.copyWith(color: getContrastTextColor(backgroundColor));
  }

  /// Luxury gradient background
  static BoxDecoration luxuryGradientDecoration() {
    return const BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Color(0xFF1A1A1A), Color(0xFF2D2D2D), Color(0xFF1A1A1A)],
        stops: [0.0, 0.5, 1.0],
      ),
    );
  }

  /// Animated luxury button style
  static ButtonStyle luxuryButtonStyle({
    Color? backgroundColor,
    Color? foregroundColor,
    double elevation = 8.0,
    double borderRadius = 16.0,
  }) {
    return ElevatedButton.styleFrom(
      backgroundColor: backgroundColor ?? primaryGold,
      foregroundColor: foregroundColor ?? luxuryBlack,
      elevation: elevation,
      shadowColor: (backgroundColor ?? primaryGold).withValues(alpha: 0.3),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius),
      ),
    );
  }

  /// Luxury text field decoration
  static InputDecoration luxuryTextFieldDecoration({
    required String hintText,
    required IconData prefixIcon,
    Widget? suffixIcon,
    Color? fillColor,
  }) {
    return InputDecoration(
      hintText: hintText,
      hintStyle: TextStyle(color: luxuryWhite.withValues(alpha: 0.5)),
      filled: true,
      fillColor: fillColor ?? luxuryGrey.withValues(alpha: 0.3),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide.none,
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: primaryGold, width: 2),
      ),
      prefixIcon: Icon(prefixIcon, color: luxuryWhite.withValues(alpha: 0.7)),
      suffixIcon: suffixIcon,
    );
  }
}

class FontStyles {
  // Instagram uses SF Pro Display/Text on iOS, fallback to system fonts
  final String displayFontFamily = 'SF Pro Display';
  final String textFontFamily = 'SF Pro Text';

  late final TextStyle display = TextStyle(
    fontFamily: displayFontFamily,
    fontWeight: FontWeight.bold,
    fontSize: 32,
    color: AppTheme.luxuryWhite,
    letterSpacing: -0.5,
  );

  late final TextStyle title = TextStyle(
    fontFamily: displayFontFamily,
    fontWeight: FontWeight.w600,
    fontSize: 18,
    color: AppTheme.luxuryWhite,
    letterSpacing: -0.2,
  );

  late final TextStyle subtitle = TextStyle(
    fontFamily: textFontFamily,
    fontWeight: FontWeight.w600,
    fontSize: 16,
    color: AppTheme.luxuryWhite,
  );

  late final TextStyle body = TextStyle(
    fontFamily: textFontFamily,
    fontWeight: FontWeight.normal,
    fontSize: 14,
    height: 1.4,
    color: AppTheme.secondaryAccentColor,
  );

  late final TextStyle bodyBold = TextStyle(
    fontFamily: textFontFamily,
    fontWeight: FontWeight.w600,
    fontSize: 14,
    color: AppTheme.luxuryWhite,
  );

  late final TextStyle button = TextStyle(
    fontFamily: textFontFamily,
    fontWeight: FontWeight.w600,
    fontSize: 14,
    color: AppTheme.luxuryBlack,
  );

  late final TextStyle caption = TextStyle(
    fontFamily: textFontFamily,
    fontWeight: FontWeight.normal,
    fontSize: 12,
    color: AppTheme.secondaryAccentColor.withValues(alpha: 0.8),
  );

  late final TextStyle small = TextStyle(
    fontFamily: textFontFamily,
    fontWeight: FontWeight.normal,
    fontSize: 12,
    color: AppTheme.secondaryAccentColor,
  );
}
