import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Unified theme system for consistent UI/UX across the app
class UnifiedThemeSystem {
  static const String _fontFamily = 'Inter';

  // Color palette
  static const Color primaryBlack = Color(0xFF000000);
  static const Color primaryWhite = Color(0xFFFFFFFF);
  static const Color primaryGold = Color(0xFFD4AF37);
  static const Color primaryGreen = Color(0xFF4CAF50);
  static const Color primaryOrangeRed = Color(0xFFFF5E3A);
  static const Color primaryBlue = Color(0xFF2196F3);
  static const Color primaryPurple = Color(0xFF9C27B0);
  static const Color primaryPink = Color(0xFFE91E63);

  // Neutral colors
  static const Color neutral50 = Color(0xFFFAFAFA);
  static const Color neutral100 = Color(0xFFF5F5F5);
  static const Color neutral200 = Color(0xFFEEEEEE);
  static const Color neutral300 = Color(0xFFE0E0E0);
  static const Color neutral400 = Color(0xFFBDBDBD);
  static const Color neutral500 = Color(0xFF9E9E9E);
  static const Color neutral600 = Color(0xFF757575);
  static const Color neutral700 = Color(0xFF616161);
  static const Color neutral800 = Color(0xFF424242);
  static const Color neutral900 = Color(0xFF212121);

  // Semantic colors
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);

  // Feed category colors (as per user preferences)
  static const Color billionairesGold = Color(0xFFD4AF37);
  static const Color placesGreen = Color(0xFF4CAF50);
  static const Color trendsOrangeRed = Color(0xFFFF5E3A);
  static const Color reelsBlack = Color(0xFF000000);

  /// Light theme
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      fontFamily: _fontFamily,
      brightness: Brightness.light,
      primarySwatch: _createMaterialColor(primaryBlack),
      primaryColor: primaryBlack,
      scaffoldBackgroundColor: primaryWhite,
      colorScheme: const ColorScheme.light(
        primary: primaryBlack,
        secondary: primaryGold,
        surface: primaryWhite,
        error: error,
        onPrimary: primaryWhite,
        onSecondary: primaryBlack,
        onSurface: primaryBlack,
        onError: primaryWhite,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: primaryWhite,
        foregroundColor: primaryBlack,
        elevation: 0,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
        titleTextStyle: TextStyle(
          color: primaryBlack,
          fontSize: 18,
          fontWeight: FontWeight.w600,
          fontFamily: _fontFamily,
        ),
      ),
      textTheme: _buildTextTheme(primaryBlack),
      elevatedButtonTheme: _buildElevatedButtonTheme(true),
      outlinedButtonTheme: _buildOutlinedButtonTheme(true),
      textButtonTheme: _buildTextButtonTheme(true),
      inputDecorationTheme: _buildInputDecorationTheme(true),
      cardTheme: _buildCardTheme(true),
      bottomNavigationBarTheme: _buildBottomNavigationBarTheme(true),
      tabBarTheme: _buildTabBarTheme(true),
    );
  }

  /// Dark theme
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      fontFamily: _fontFamily,
      brightness: Brightness.dark,
      primarySwatch: _createMaterialColor(primaryWhite),
      primaryColor: primaryWhite,
      scaffoldBackgroundColor: neutral900,
      colorScheme: const ColorScheme.dark(
        primary: primaryWhite,
        secondary: primaryGold,
        surface: neutral800,
        error: error,
        onPrimary: primaryBlack,
        onSecondary: primaryBlack,
        onSurface: primaryWhite,
        onError: primaryWhite,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: neutral900,
        foregroundColor: primaryWhite,
        elevation: 0,
        systemOverlayStyle: SystemUiOverlayStyle.light,
        titleTextStyle: TextStyle(
          color: primaryWhite,
          fontSize: 18,
          fontWeight: FontWeight.w600,
          fontFamily: _fontFamily,
        ),
      ),
      textTheme: _buildTextTheme(primaryWhite),
      elevatedButtonTheme: _buildElevatedButtonTheme(false),
      outlinedButtonTheme: _buildOutlinedButtonTheme(false),
      textButtonTheme: _buildTextButtonTheme(false),
      inputDecorationTheme: _buildInputDecorationTheme(false),
      cardTheme: _buildCardTheme(false),
      bottomNavigationBarTheme: _buildBottomNavigationBarTheme(false),
      tabBarTheme: _buildTabBarTheme(false),
    );
  }

  /// Create MaterialColor from Color
  static MaterialColor _createMaterialColor(Color color) {
    final strengths = <double>[.05];
    final swatch = <int, Color>{};
    final int r = (color.r * 255.0).round() & 0xff;
    final int g = (color.g * 255.0).round() & 0xff;
    final int b = (color.b * 255.0).round() & 0xff;

    for (int i = 1; i < 10; i++) {
      strengths.add(0.1 * i);
    }
    for (final strength in strengths) {
      final double ds = 0.5 - strength;
      swatch[(strength * 1000).round()] = Color.fromRGBO(
        r + ((ds < 0 ? r : (255 - r)) * ds).round(),
        g + ((ds < 0 ? g : (255 - g)) * ds).round(),
        b + ((ds < 0 ? b : (255 - b)) * ds).round(),
        1,
      );
    }
    return MaterialColor(color.toARGB32(), swatch);
  }

  /// Build text theme
  static TextTheme _buildTextTheme(Color textColor) {
    return TextTheme(
      displayLarge: TextStyle(
        fontSize: 32,
        fontWeight: FontWeight.w700,
        color: textColor,
        fontFamily: _fontFamily,
      ),
      displayMedium: TextStyle(
        fontSize: 28,
        fontWeight: FontWeight.w600,
        color: textColor,
        fontFamily: _fontFamily,
      ),
      displaySmall: TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.w600,
        color: textColor,
        fontFamily: _fontFamily,
      ),
      headlineLarge: TextStyle(
        fontSize: 22,
        fontWeight: FontWeight.w600,
        color: textColor,
        fontFamily: _fontFamily,
      ),
      headlineMedium: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: textColor,
        fontFamily: _fontFamily,
      ),
      headlineSmall: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: textColor,
        fontFamily: _fontFamily,
      ),
      titleLarge: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: textColor,
        fontFamily: _fontFamily,
      ),
      titleMedium: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: textColor,
        fontFamily: _fontFamily,
      ),
      titleSmall: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: textColor,
        fontFamily: _fontFamily,
      ),
      bodyLarge: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w400,
        color: textColor,
        fontFamily: _fontFamily,
      ),
      bodyMedium: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: textColor,
        fontFamily: _fontFamily,
      ),
      bodySmall: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w400,
        color: textColor,
        fontFamily: _fontFamily,
      ),
      labelLarge: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: textColor,
        fontFamily: _fontFamily,
      ),
      labelMedium: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: textColor,
        fontFamily: _fontFamily,
      ),
      labelSmall: TextStyle(
        fontSize: 10,
        fontWeight: FontWeight.w500,
        color: textColor,
        fontFamily: _fontFamily,
      ),
    );
  }

  /// Build elevated button theme
  static ElevatedButtonThemeData _buildElevatedButtonTheme(bool isLight) {
    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: isLight ? primaryBlack : primaryWhite,
        foregroundColor: isLight ? primaryWhite : primaryBlack,
        elevation: 0,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        textStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          fontFamily: _fontFamily,
        ),
      ),
    );
  }

  /// Build outlined button theme
  static OutlinedButtonThemeData _buildOutlinedButtonTheme(bool isLight) {
    return OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: isLight ? primaryBlack : primaryWhite,
        side: BorderSide(
          color: isLight ? primaryBlack : primaryWhite,
          width: 1,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        textStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          fontFamily: _fontFamily,
        ),
      ),
    );
  }

  /// Build text button theme
  static TextButtonThemeData _buildTextButtonTheme(bool isLight) {
    return TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: isLight ? primaryBlack : primaryWhite,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        textStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          fontFamily: _fontFamily,
        ),
      ),
    );
  }

  /// Build input decoration theme
  static InputDecorationTheme _buildInputDecorationTheme(bool isLight) {
    return InputDecorationTheme(
      filled: true,
      fillColor: isLight ? neutral100 : neutral800,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: isLight ? neutral300 : neutral600),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: isLight ? neutral300 : neutral600),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(
          color: isLight ? primaryBlack : primaryWhite,
          width: 2,
        ),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: error, width: 2),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      hintStyle: TextStyle(
        color: isLight ? neutral500 : neutral400,
        fontFamily: _fontFamily,
      ),
      labelStyle: TextStyle(
        color: isLight ? neutral700 : neutral300,
        fontFamily: _fontFamily,
      ),
    );
  }

  /// Build card theme
  static CardThemeData _buildCardTheme(bool isLight) {
    return CardThemeData(
      color: isLight ? primaryWhite : neutral800,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: isLight ? neutral200 : neutral700, width: 1),
      ),
    );
  }

  /// Build bottom navigation bar theme
  static BottomNavigationBarThemeData _buildBottomNavigationBarTheme(
    bool isLight,
  ) {
    return BottomNavigationBarThemeData(
      backgroundColor: isLight ? primaryWhite : neutral900,
      selectedItemColor: isLight ? primaryBlack : primaryWhite,
      unselectedItemColor: isLight ? neutral500 : neutral400,
      type: BottomNavigationBarType.fixed,
      elevation: 0,
      selectedLabelStyle: const TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w600,
        fontFamily: _fontFamily,
      ),
      unselectedLabelStyle: const TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w400,
        fontFamily: _fontFamily,
      ),
    );
  }

  /// Build tab bar theme
  static TabBarThemeData _buildTabBarTheme(bool isLight) {
    return TabBarThemeData(
      labelColor: isLight ? primaryBlack : primaryWhite,
      unselectedLabelColor: isLight ? neutral500 : neutral400,
      indicator: const BoxDecoration(), // No indicator as per user preference
      labelStyle: const TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w600,
        fontFamily: _fontFamily,
      ),
      unselectedLabelStyle: const TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        fontFamily: _fontFamily,
      ),
    );
  }

  /// Get feed category color
  static Color getFeedCategoryColor(String category) {
    switch (category.toLowerCase()) {
      case 'billionaires':
        return billionairesGold;
      case 'places':
        return placesGreen;
      case 'trends':
        return trendsOrangeRed;
      case 'reels':
        return reelsBlack;
      default:
        return neutral500;
    }
  }

  /// Get feed category icon
  static IconData getFeedCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'billionaires':
        return Icons.diamond;
      case 'places':
        return Icons.location_on;
      case 'trends':
        return Icons.trending_up;
      case 'reels':
        return Icons.play_circle_filled;
      default:
        return Icons.category;
    }
  }
}
