import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:crypto/crypto.dart';

/// Advanced caching service with intelligent cache management
class AdvancedCacheService {
  static final AdvancedCacheService _instance =
      AdvancedCacheService._internal();
  factory AdvancedCacheService() => _instance;
  AdvancedCacheService._internal();

  late SharedPreferences _prefs;
  final Map<String, CacheEntry> _memoryCache = {};
  final Map<String, Timer> _expirationTimers = {};
  bool _isInitialized = false;

  // Cache configuration
  static const int _maxMemoryCacheSize = 100; // Max items in memory
  static const int _maxDiskCacheSize = 500; // Max items on disk
  static const Duration _defaultTTL = Duration(hours: 1);
  static const Duration _cleanupInterval = Duration(minutes: 30);

  Timer? _cleanupTimer;

  /// Initialize the cache service
  Future<void> initialize() async {
    if (_isInitialized) return;

    _prefs = await SharedPreferences.getInstance();
    await _loadCacheMetadata();
    _startCleanupTimer();

    _isInitialized = true;
    _logCacheEvent('Advanced cache service initialized');
  }

  /// Store data in cache with TTL
  Future<void> set<T>(
    String key,
    T data, {
    Duration? ttl,
    CachePriority priority = CachePriority.normal,
    bool persistToDisk = true,
  }) async {
    _ensureInitialized();

    final hashedKey = _hashKey(key);
    final expiresAt = DateTime.now().add(ttl ?? _defaultTTL);

    final entry = CacheEntry<T>(
      key: hashedKey,
      data: data,
      createdAt: DateTime.now(),
      expiresAt: expiresAt,
      priority: priority,
      accessCount: 0,
      lastAccessed: DateTime.now(),
    );

    // Store in memory cache
    await _setMemoryCache(hashedKey, entry);

    // Store on disk if requested
    if (persistToDisk) {
      await _setDiskCache(hashedKey, entry);
    }

    // Set expiration timer
    _setExpirationTimer(hashedKey, expiresAt);

    _logCacheEvent('Cached item: $key (TTL: ${ttl ?? _defaultTTL})');
  }

  /// Get data from cache
  Future<T?> get<T>(String key) async {
    _ensureInitialized();

    final hashedKey = _hashKey(key);

    // Try memory cache first
    CacheEntry<T>? entry = _memoryCache[hashedKey] as CacheEntry<T>?;

    // If not in memory, try disk cache
    if (entry == null) {
      entry = await _getDiskCache<T>(hashedKey);

      // Load into memory cache if found
      if (entry != null) {
        await _setMemoryCache(hashedKey, entry);
      }
    }

    // Check if entry exists and is not expired
    if (entry != null && !_isExpired(entry)) {
      // Update access statistics
      entry.accessCount++;
      entry.lastAccessed = DateTime.now();

      _logCacheEvent('Cache hit: $key');
      return entry.data;
    }

    // Remove expired entry
    if (entry != null) {
      await remove(key);
    }

    _logCacheEvent('Cache miss: $key');
    return null;
  }

  /// Check if key exists in cache
  Future<bool> exists(String key) async {
    _ensureInitialized();

    final hashedKey = _hashKey(key);

    // Check memory cache
    if (_memoryCache.containsKey(hashedKey)) {
      final entry = _memoryCache[hashedKey]!;
      return !_isExpired(entry);
    }

    // Check disk cache
    final diskEntry = await _getDiskCache(hashedKey);
    return diskEntry != null && !_isExpired(diskEntry);
  }

  /// Remove item from cache
  Future<void> remove(String key) async {
    _ensureInitialized();

    final hashedKey = _hashKey(key);

    // Remove from memory
    _memoryCache.remove(hashedKey);

    // Remove from disk
    await _prefs.remove('cache_$hashedKey');
    await _prefs.remove('meta_$hashedKey');

    // Cancel expiration timer
    _expirationTimers[hashedKey]?.cancel();
    _expirationTimers.remove(hashedKey);

    _logCacheEvent('Removed from cache: $key');
  }

  /// Clear all cache
  Future<void> clear() async {
    _ensureInitialized();

    // Clear memory cache
    _memoryCache.clear();

    // Clear disk cache
    final keys = _prefs.getKeys().where(
      (key) => key.startsWith('cache_') || key.startsWith('meta_'),
    );

    for (final key in keys) {
      await _prefs.remove(key);
    }

    // Cancel all timers
    for (final timer in _expirationTimers.values) {
      timer.cancel();
    }
    _expirationTimers.clear();

    _logCacheEvent('Cache cleared');
  }

  /// Get cache statistics
  Future<CacheStatistics> getStatistics() async {
    _ensureInitialized();

    final memorySize = _memoryCache.length;
    final diskKeys = _prefs.getKeys().where((key) => key.startsWith('cache_'));
    final diskSize = diskKeys.length;

    int totalHits = 0;
    int totalMisses = 0;

    for (final entry in _memoryCache.values) {
      totalHits += entry.accessCount;
    }

    return CacheStatistics(
      memorySize: memorySize,
      diskSize: diskSize,
      totalHits: totalHits,
      totalMisses: totalMisses,
      hitRate: totalHits / (totalHits + totalMisses).clamp(1, double.infinity),
    );
  }

  /// Optimize cache by removing least recently used items
  Future<void> optimize() async {
    _ensureInitialized();

    // Optimize memory cache
    if (_memoryCache.length > _maxMemoryCacheSize) {
      final sortedEntries = _memoryCache.entries.toList()
        ..sort(
          (a, b) =>
              _calculateScore(a.value).compareTo(_calculateScore(b.value)),
        );

      final itemsToRemove = _memoryCache.length - _maxMemoryCacheSize;
      for (int i = 0; i < itemsToRemove; i++) {
        _memoryCache.remove(sortedEntries[i].key);
      }
    }

    // Optimize disk cache
    final diskKeys = _prefs
        .getKeys()
        .where((key) => key.startsWith('cache_'))
        .toList();
    if (diskKeys.length > _maxDiskCacheSize) {
      final entries = <String, CacheEntry>{};

      for (final key in diskKeys) {
        final entry = await _getDiskCache(
          key.substring(6),
        ); // Remove 'cache_' prefix
        if (entry != null) {
          entries[key] = entry;
        }
      }

      final sortedEntries = entries.entries.toList()
        ..sort(
          (a, b) =>
              _calculateScore(a.value).compareTo(_calculateScore(b.value)),
        );

      final itemsToRemove = diskKeys.length - _maxDiskCacheSize;
      for (int i = 0; i < itemsToRemove; i++) {
        await _prefs.remove(sortedEntries[i].key);
        await _prefs.remove('meta_${sortedEntries[i].key.substring(6)}');
      }
    }

    _logCacheEvent('Cache optimized');
  }

  /// Preload cache with frequently accessed data
  Future<void> preload(Map<String, dynamic> data) async {
    _ensureInitialized();

    for (final entry in data.entries) {
      await set(
        entry.key,
        entry.value,
        priority: CachePriority.high,
        ttl: const Duration(hours: 24),
      );
    }

    _logCacheEvent('Preloaded ${data.length} items');
  }

  /// Set memory cache entry
  Future<void> _setMemoryCache(String hashedKey, CacheEntry entry) async {
    _memoryCache[hashedKey] = entry;

    // Trigger optimization if memory cache is full
    if (_memoryCache.length > _maxMemoryCacheSize) {
      await optimize();
    }
  }

  /// Set disk cache entry
  Future<void> _setDiskCache(String hashedKey, CacheEntry entry) async {
    try {
      final serializedData = jsonEncode({
        'data': entry.data,
        'type': entry.data.runtimeType.toString(),
      });

      await _prefs.setString('cache_$hashedKey', serializedData);
      await _prefs.setString('meta_$hashedKey', jsonEncode(entry.toJson()));
    } catch (e) {
      _logCacheEvent('Failed to set disk cache for $hashedKey: $e');
    }
  }

  /// Get disk cache entry
  Future<CacheEntry<T>?> _getDiskCache<T>(String hashedKey) async {
    try {
      final metaData = _prefs.getString('meta_$hashedKey');
      final cacheData = _prefs.getString('cache_$hashedKey');

      if (metaData == null || cacheData == null) return null;

      final meta = jsonDecode(metaData);
      final data = jsonDecode(cacheData);

      return CacheEntry<T>.fromJson(meta, data['data'] as T);
    } catch (e) {
      _logCacheEvent('Failed to get disk cache for $hashedKey: $e');
      return null;
    }
  }

  /// Load cache metadata on startup
  Future<void> _loadCacheMetadata() async {
    final metaKeys = _prefs.getKeys().where((key) => key.startsWith('meta_'));

    for (final key in metaKeys) {
      final hashedKey = key.substring(5); // Remove 'meta_' prefix
      final entry = await _getDiskCache(hashedKey);

      if (entry != null && !_isExpired(entry)) {
        _setExpirationTimer(hashedKey, entry.expiresAt);
      } else if (entry != null) {
        // Remove expired entries
        await _prefs.remove('cache_$hashedKey');
        await _prefs.remove(key);
      }
    }
  }

  /// Start cleanup timer
  void _startCleanupTimer() {
    _cleanupTimer = Timer.periodic(_cleanupInterval, (_) async {
      await _performCleanup();
    });
  }

  /// Perform periodic cleanup
  Future<void> _performCleanup() async {
    final expiredKeys = <String>[];

    // Find expired memory cache entries
    for (final entry in _memoryCache.entries) {
      if (_isExpired(entry.value)) {
        expiredKeys.add(entry.key);
      }
    }

    // Remove expired entries
    for (final key in expiredKeys) {
      _memoryCache.remove(key);
      await _prefs.remove('cache_$key');
      await _prefs.remove('meta_$key');

      _expirationTimers[key]?.cancel();
      _expirationTimers.remove(key);
    }

    if (expiredKeys.isNotEmpty) {
      _logCacheEvent('Cleaned up ${expiredKeys.length} expired entries');
    }
  }

  /// Set expiration timer for cache entry
  void _setExpirationTimer(String hashedKey, DateTime expiresAt) {
    final duration = expiresAt.difference(DateTime.now());

    if (duration.isNegative) return;

    _expirationTimers[hashedKey] = Timer(duration, () async {
      await remove(_getOriginalKey(hashedKey));
    });
  }

  /// Calculate cache score for LRU optimization
  double _calculateScore(CacheEntry entry) {
    final age = DateTime.now().difference(entry.lastAccessed).inMinutes;
    final priorityMultiplier = entry.priority.multiplier;
    final accessFrequency =
        entry.accessCount /
        DateTime.now()
            .difference(entry.createdAt)
            .inHours
            .clamp(1, double.infinity);

    return (age / priorityMultiplier) - (accessFrequency * 10);
  }

  /// Hash cache key for consistent storage
  String _hashKey(String key) {
    final bytes = utf8.encode(key);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Get original key from hashed key (for logging)
  String _getOriginalKey(String hashedKey) {
    // In production, you might want to maintain a reverse mapping
    return hashedKey.substring(0, 8); // Shortened for logging
  }

  /// Check if cache entry is expired
  bool _isExpired(CacheEntry entry) {
    return DateTime.now().isAfter(entry.expiresAt);
  }

  /// Ensure cache service is initialized
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw StateError(
        'AdvancedCacheService not initialized. Call initialize() first.',
      );
    }
  }

  void _logCacheEvent(String message) {
    // TODO: Replace with proper logging framework
    // Logger.info('[CACHE] $message');
  }

  /// Dispose resources
  void dispose() {
    _cleanupTimer?.cancel();

    for (final timer in _expirationTimers.values) {
      timer.cancel();
    }

    _memoryCache.clear();
    _expirationTimers.clear();
    _isInitialized = false;
  }
}

/// Cache entry model
class CacheEntry<T> {
  final String key;
  final T data;
  final DateTime createdAt;
  final DateTime expiresAt;
  final CachePriority priority;
  int accessCount;
  DateTime lastAccessed;

  CacheEntry({
    required this.key,
    required this.data,
    required this.createdAt,
    required this.expiresAt,
    required this.priority,
    required this.accessCount,
    required this.lastAccessed,
  });

  Map<String, dynamic> toJson() {
    return {
      'key': key,
      'createdAt': createdAt.toIso8601String(),
      'expiresAt': expiresAt.toIso8601String(),
      'priority': priority.index,
      'accessCount': accessCount,
      'lastAccessed': lastAccessed.toIso8601String(),
    };
  }

  factory CacheEntry.fromJson(Map<String, dynamic> json, T data) {
    return CacheEntry<T>(
      key: json['key'],
      data: data,
      createdAt: DateTime.parse(json['createdAt']),
      expiresAt: DateTime.parse(json['expiresAt']),
      priority: CachePriority.values[json['priority']],
      accessCount: json['accessCount'],
      lastAccessed: DateTime.parse(json['lastAccessed']),
    );
  }
}

/// Cache priority levels
enum CachePriority {
  low(0.5),
  normal(1.0),
  high(2.0),
  critical(5.0);

  const CachePriority(this.multiplier);
  final double multiplier;
}

/// Cache statistics
class CacheStatistics {
  final int memorySize;
  final int diskSize;
  final int totalHits;
  final int totalMisses;
  final double hitRate;

  CacheStatistics({
    required this.memorySize,
    required this.diskSize,
    required this.totalHits,
    required this.totalMisses,
    required this.hitRate,
  });
}
