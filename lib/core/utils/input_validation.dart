import 'package:flutter/foundation.dart';

/// Comprehensive input validation utilities
class InputValidation {
  static final InputValidation _instance = InputValidation._internal();
  factory InputValidation() => _instance;
  InputValidation._internal();

  /// Email validation
  static ValidationResult validateEmail(String? email) {
    if (email == null || email.trim().isEmpty) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'Email address is required',
      );
    }

    final trimmedEmail = email.trim();
    
    // Basic email regex pattern
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );

    if (!emailRegex.hasMatch(trimmedEmail)) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'Please enter a valid email address',
      );
    }

    // Check for common email issues
    if (trimmedEmail.contains('..')) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'Email cannot contain consecutive dots',
      );
    }

    if (trimmedEmail.startsWith('.') || trimmedEmail.endsWith('.')) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'Email cannot start or end with a dot',
      );
    }

    return ValidationResult(isValid: true);
  }

  /// Password validation
  static ValidationResult validatePassword(String? password) {
    if (password == null || password.isEmpty) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'Password is required',
      );
    }

    if (password.length < 8) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'Password must be at least 8 characters long',
      );
    }

    if (password.length > 128) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'Password cannot exceed 128 characters',
      );
    }

    // Check for at least one uppercase letter
    if (!password.contains(RegExp(r'[A-Z]'))) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'Password must contain at least one uppercase letter',
      );
    }

    // Check for at least one lowercase letter
    if (!password.contains(RegExp(r'[a-z]'))) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'Password must contain at least one lowercase letter',
      );
    }

    // Check for at least one number
    if (!password.contains(RegExp(r'[0-9]'))) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'Password must contain at least one number',
      );
    }

    // Check for common weak passwords
    final weakPasswords = [
      'password',
      '12345678',
      'qwerty123',
      'abc123456',
      'password123',
    ];

    if (weakPasswords.contains(password.toLowerCase())) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'This password is too common. Please choose a stronger password',
      );
    }

    return ValidationResult(isValid: true);
  }

  /// Username validation
  static ValidationResult validateUsername(String? username) {
    if (username == null || username.trim().isEmpty) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'Username is required',
      );
    }

    final trimmedUsername = username.trim();

    if (trimmedUsername.length < 3) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'Username must be at least 3 characters long',
      );
    }

    if (trimmedUsername.length > 30) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'Username cannot exceed 30 characters',
      );
    }

    // Username can only contain letters, numbers, underscores, and dots
    final usernameRegex = RegExp(r'^[a-zA-Z0-9._]+$');
    if (!usernameRegex.hasMatch(trimmedUsername)) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'Username can only contain letters, numbers, dots, and underscores',
      );
    }

    // Cannot start or end with dot or underscore
    if (trimmedUsername.startsWith('.') || 
        trimmedUsername.startsWith('_') ||
        trimmedUsername.endsWith('.') || 
        trimmedUsername.endsWith('_')) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'Username cannot start or end with dots or underscores',
      );
    }

    // Cannot contain consecutive dots or underscores
    if (trimmedUsername.contains('..') || trimmedUsername.contains('__')) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'Username cannot contain consecutive dots or underscores',
      );
    }

    // Check for reserved usernames
    final reservedUsernames = [
      'admin', 'root', 'user', 'test', 'guest', 'api', 'www', 'mail',
      'support', 'help', 'info', 'contact', 'about', 'privacy', 'terms',
      'billionaire', 'billionaires', 'social', 'app', 'system',
    ];

    if (reservedUsernames.contains(trimmedUsername.toLowerCase())) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'This username is reserved. Please choose another one',
      );
    }

    return ValidationResult(isValid: true);
  }

  /// Name validation
  static ValidationResult validateName(String? name) {
    if (name == null || name.trim().isEmpty) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'Name is required',
      );
    }

    final trimmedName = name.trim();

    if (trimmedName.length < 2) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'Name must be at least 2 characters long',
      );
    }

    if (trimmedName.length > 50) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'Name cannot exceed 50 characters',
      );
    }

    // Name can contain letters, spaces, hyphens, and apostrophes
    final nameRegex = RegExp(r"^[a-zA-Z\s\-']+$");
    if (!nameRegex.hasMatch(trimmedName)) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'Name can only contain letters, spaces, hyphens, and apostrophes',
      );
    }

    return ValidationResult(isValid: true);
  }

  /// Bio validation
  static ValidationResult validateBio(String? bio) {
    if (bio == null || bio.trim().isEmpty) {
      return ValidationResult(isValid: true); // Bio is optional
    }

    final trimmedBio = bio.trim();

    if (trimmedBio.length > 500) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'Bio cannot exceed 500 characters',
      );
    }

    return ValidationResult(isValid: true);
  }

  /// Post content validation
  static ValidationResult validatePostContent(String? content) {
    if (content == null || content.trim().isEmpty) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'Post content cannot be empty',
      );
    }

    final trimmedContent = content.trim();

    if (trimmedContent.length > 2000) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'Post content cannot exceed 2000 characters',
      );
    }

    return ValidationResult(isValid: true);
  }

  /// Comment validation
  static ValidationResult validateComment(String? comment) {
    if (comment == null || comment.trim().isEmpty) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'Comment cannot be empty',
      );
    }

    final trimmedComment = comment.trim();

    if (trimmedComment.length > 500) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'Comment cannot exceed 500 characters',
      );
    }

    return ValidationResult(isValid: true);
  }

  /// URL validation
  static ValidationResult validateUrl(String? url) {
    if (url == null || url.trim().isEmpty) {
      return ValidationResult(isValid: true); // URL is optional
    }

    final trimmedUrl = url.trim();

    try {
      final uri = Uri.parse(trimmedUrl);
      if (!uri.hasScheme || (!uri.scheme.startsWith('http'))) {
        return ValidationResult(
          isValid: false,
          errorMessage: 'Please enter a valid URL starting with http:// or https://',
        );
      }
      return ValidationResult(isValid: true);
    } catch (e) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'Please enter a valid URL',
      );
    }
  }

  /// Phone number validation (basic)
  static ValidationResult validatePhoneNumber(String? phone) {
    if (phone == null || phone.trim().isEmpty) {
      return ValidationResult(isValid: true); // Phone is optional
    }

    final trimmedPhone = phone.trim();

    // Remove common formatting characters
    final cleanPhone = trimmedPhone.replaceAll(RegExp(r'[\s\-\(\)\+]'), '');

    if (cleanPhone.length < 10 || cleanPhone.length > 15) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'Phone number must be between 10 and 15 digits',
      );
    }

    // Check if it contains only digits
    if (!RegExp(r'^[0-9]+$').hasMatch(cleanPhone)) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'Phone number can only contain digits',
      );
    }

    return ValidationResult(isValid: true);
  }

  /// Generic required field validation
  static ValidationResult validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return ValidationResult(
        isValid: false,
        errorMessage: '$fieldName is required',
      );
    }
    return ValidationResult(isValid: true);
  }

  /// Validate multiple fields at once
  static List<ValidationResult> validateMultiple(
    Map<String, ValidationResult> validations,
  ) {
    return validations.values.where((result) => !result.isValid).toList();
  }

  /// Log validation errors for debugging
  static void logValidationError(String field, String error) {
    if (kDebugMode) {
      debugPrint('🚨 Validation Error [$field]: $error');
    }
  }
}

/// Validation result class
class ValidationResult {
  final bool isValid;
  final String? errorMessage;

  const ValidationResult({
    required this.isValid,
    this.errorMessage,
  });

  @override
  String toString() {
    return 'ValidationResult(isValid: $isValid, errorMessage: $errorMessage)';
  }
}

/// Validation extensions for common use cases
extension StringValidation on String? {
  ValidationResult get isValidEmail => InputValidation.validateEmail(this);
  ValidationResult get isValidPassword => InputValidation.validatePassword(this);
  ValidationResult get isValidUsername => InputValidation.validateUsername(this);
  ValidationResult get isValidName => InputValidation.validateName(this);
  ValidationResult get isValidBio => InputValidation.validateBio(this);
  ValidationResult get isValidUrl => InputValidation.validateUrl(this);
  ValidationResult get isValidPhone => InputValidation.validatePhoneNumber(this);
}
