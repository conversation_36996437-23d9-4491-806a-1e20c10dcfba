import 'package:flutter/material.dart';

enum AppThemeType { dark, light, pink, sky, purple, luxuryWhiteGold }

class AppThemes {
  static final Map<AppThemeType, ThemeData> _themes = {
    AppThemeType.dark: _buildDarkTheme(),
    AppThemeType.light: _buildLightTheme(),
    AppThemeType.pink: _buildPinkTheme(),
    AppThemeType.sky: _buildSkyTheme(),
    AppThemeType.purple: _buildPurpleTheme(),
    AppThemeType.luxuryWhiteGold: _buildLuxuryWhiteGoldTheme(),
  };

  static ThemeData getTheme(AppThemeType type) {
    return _themes[type] ?? _buildDarkTheme();
  }

  static ThemeData _buildDarkTheme() {
    return ThemeData.dark().copyWith(
      primaryColor: Colors.black,
      scaffoldBackgroundColor: Colors.black,
      colorScheme: const ColorScheme.dark(
        primary: Colors.black,
        secondary: Color(0xFFFFD700), // Gold
        onPrimary: Colors.white,
        onSecondary: Colors.black,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        iconTheme: IconThemeData(color: Colors.black),
        titleTextStyle: TextStyle(
          color: Colors.black,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      elevatedButtonTheme: _getButtonTheme(const Color(0xFFFFD700)),
    );
  }

  static ThemeData _buildLightTheme() {
    return ThemeData.light().copyWith(
      primaryColor: Colors.white,
      scaffoldBackgroundColor: Colors.white,
      colorScheme: const ColorScheme.light(
        primary: Colors.white,
        secondary: Color(0xFFE0BBE4), // Lavender
        onPrimary: Colors.black,
        onSecondary: Colors.black,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.white,
        elevation: 0,
        iconTheme: IconThemeData(color: Colors.black),
        titleTextStyle: TextStyle(
          color: Colors.black,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      elevatedButtonTheme: _getButtonTheme(const Color(0xFFE0BBE4)),
    );
  }

  static ThemeData _buildPinkTheme() {
    return ThemeData.light().copyWith(
      primaryColor: const Color(0xFFFDEEF4), // Light Pink
      scaffoldBackgroundColor: const Color(0xFFFDEEF4),
      colorScheme: const ColorScheme.light(
        primary: Color(0xFFFDEEF4),
        secondary: Colors.white,
        tertiary: Color(0xFF6A0DAD), // Purple
        onPrimary: Colors.black,
        onSecondary: Colors.black,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: Color(0xFFFDEEF4),
        elevation: 0,
        iconTheme: IconThemeData(color: Colors.black),
        titleTextStyle: TextStyle(
          color: Colors.black,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      elevatedButtonTheme: _getButtonTheme(Colors.white, primary: Colors.black),
    );
  }

  static ThemeData _buildSkyTheme() {
    return ThemeData.light().copyWith(
      primaryColor: const Color(0xFFE0F7FA), // Light Blue
      scaffoldBackgroundColor: const Color(0xFFE0F7FA),
      colorScheme: const ColorScheme.light(
        primary: Color(0xFFE0F7FA),
        secondary: Colors.white,
        tertiary: Color(0xFF0D47A1), // Dark Blue
        onPrimary: Colors.black,
        onSecondary: Colors.black,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: Color(0xFFE0F7FA),
        elevation: 0,
        iconTheme: IconThemeData(color: Colors.black),
        titleTextStyle: TextStyle(
          color: Colors.black,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      elevatedButtonTheme: _getButtonTheme(Colors.white, primary: Colors.black),
    );
  }

  static ThemeData _buildPurpleTheme() {
    return ThemeData.dark().copyWith(
      primaryColor: const Color(0xFF4A148C), // Deep Purple
      scaffoldBackgroundColor: const Color(0xFF4A148C),
      colorScheme: const ColorScheme.dark(
        primary: Color(0xFF4A148C),
        secondary: Colors.white,
        tertiary: Color(0xFFFFD700), // Gold
        onPrimary: Colors.white,
        onSecondary: Colors.black,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: Color(0xFF4A148C),
        elevation: 0,
      ),
      elevatedButtonTheme: _getButtonTheme(Colors.white, primary: Colors.black),
    );
  }

  static ThemeData _buildLuxuryWhiteGoldTheme() {
    return ThemeData.light().copyWith(
      primaryColor: const Color(0xFFFFFFFF), // Instagram white background
      scaffoldBackgroundColor: const Color(0xFFFFFFFF),
      colorScheme: const ColorScheme.light(
        primary: Color(0xFF000000), // Instagram black for primary elements
        secondary: Color(0xFF0095F6), // Instagram blue
        tertiary: Color(0xFFFFD700), // Gold accent for luxury features
        onPrimary: Color(0xFFFFFFFF), // White text on primary
        onSecondary: Color(0xFFFFFFFF), // White text on secondary
        surface: Color(0xFFFFFFFF), // Pure white for cards (Instagram style)
        onSurface: Color(0xFF000000), // Black text on surface
        surfaceContainerHighest: Color(
          0xFFFAFAFA,
        ), // Very light gray for subtle backgrounds
        outline: Color(0xFFDBDBDB), // Instagram's border gray
        outlineVariant: Color(0xFF8E8E8E), // Secondary text gray
        error: Color(0xFFED4956), // Instagram red
        shadow: Color(0x0A000000), // Very subtle shadow
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: Color(0xFFFFFFFF),
        elevation: 0,
        scrolledUnderElevation: 0,
        iconTheme: IconThemeData(color: Color(0xFF000000)),
        titleTextStyle: TextStyle(
          color: Color(0xFF000000),
          fontSize: 18,
          fontWeight: FontWeight.w600,
          fontFamily: 'SF Pro Display',
        ),
      ),
      cardTheme: const CardThemeData(
        color: Color(0xFFFFFFFF), // Pure white cards like Instagram
        elevation: 0, // No elevation for flat Instagram look
        shadowColor: Colors.transparent,
        margin: EdgeInsets.zero,
      ),
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          color: Color(0xFF000000),
          fontSize: 32,
          fontWeight: FontWeight.bold,
          fontFamily: 'SF Pro Display',
        ),
        headlineLarge: TextStyle(
          color: Color(0xFF000000),
          fontSize: 24,
          fontWeight: FontWeight.w600,
          fontFamily: 'SF Pro Display',
        ),
        titleLarge: TextStyle(
          color: Color(0xFF000000),
          fontSize: 18,
          fontWeight: FontWeight.w600,
          fontFamily: 'SF Pro Display',
        ),
        titleMedium: TextStyle(
          color: Color(0xFF000000),
          fontSize: 16,
          fontWeight: FontWeight.w600,
          fontFamily: 'SF Pro Display',
        ),
        bodyLarge: TextStyle(
          color: Color(0xFF000000),
          fontSize: 16,
          fontWeight: FontWeight.normal,
          fontFamily: 'SF Pro Text',
        ),
        bodyMedium: TextStyle(
          color: Color(0xFF8E8E8E),
          fontSize: 14,
          fontWeight: FontWeight.normal,
          fontFamily: 'SF Pro Text',
        ),
        bodySmall: TextStyle(
          color: Color(0xFF8E8E8E),
          fontSize: 12,
          fontWeight: FontWeight.normal,
          fontFamily: 'SF Pro Text',
        ),
        labelLarge: TextStyle(
          color: Color(0xFF000000),
          fontSize: 14,
          fontWeight: FontWeight.w600,
          fontFamily: 'SF Pro Text',
        ),
      ),
      elevatedButtonTheme: _getButtonTheme(
        const Color(0xFF0095F6), // Instagram blue for buttons
        primary: const Color(0xFFFFFFFF), // White text on buttons
      ),
      dividerTheme: const DividerThemeData(
        color: Color(0xFFDBDBDB),
        thickness: 0.5,
        space: 0.5,
      ),
    );
  }

  static ElevatedButtonThemeData _getButtonTheme(
    Color backgroundColor, {
    Color? primary,
  }) {
    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor,
        foregroundColor: primary,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
      ),
    );
  }
}
