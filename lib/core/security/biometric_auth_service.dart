import 'package:local_auth/local_auth.dart';
import 'package:local_auth/error_codes.dart' as auth_error;
import 'package:flutter/services.dart';

/// Biometric authentication service for enhanced security
class BiometricAuthService {
  static final BiometricAuthService _instance =
      BiometricAuthService._internal();
  factory BiometricAuthService() => _instance;
  BiometricAuthService._internal();

  final LocalAuthentication _localAuth = LocalAuthentication();
  bool _isInitialized = false;

  /// Initialize biometric authentication
  Future<void> initialize() async {
    if (_isInitialized) return;
    _isInitialized = true;
  }

  /// Check if biometric authentication is available
  Future<bool> isBiometricAvailable() async {
    try {
      final isAvailable = await _localAuth.canCheckBiometrics;
      final isDeviceSupported = await _localAuth.isDeviceSupported();
      return isAvailable && isDeviceSupported;
    } catch (e) {
      _logSecurityEvent('Biometric availability check failed: $e');
      return false;
    }
  }

  /// Get available biometric types
  Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      return await _localAuth.getAvailableBiometrics();
    } catch (e) {
      _logSecurityEvent('Failed to get available biometrics: $e');
      return [];
    }
  }

  /// Authenticate using biometrics
  Future<BiometricAuthResult> authenticate({
    String reason = 'Please authenticate to access your account',
    bool useErrorDialogs = true,
    bool stickyAuth = true,
  }) async {
    try {
      if (!await isBiometricAvailable()) {
        return BiometricAuthResult(
          success: false,
          errorType: BiometricErrorType.notAvailable,
          errorMessage:
              'Biometric authentication is not available on this device',
        );
      }

      final authenticated = await _localAuth.authenticate(
        localizedReason: reason,
        options: AuthenticationOptions(
          useErrorDialogs: useErrorDialogs,
          stickyAuth: stickyAuth,
          biometricOnly: true,
        ),
      );

      if (authenticated) {
        _logSecurityEvent('Biometric authentication successful');
        return BiometricAuthResult(success: true);
      } else {
        _logSecurityEvent('Biometric authentication failed - user cancelled');
        return BiometricAuthResult(
          success: false,
          errorType: BiometricErrorType.userCancel,
          errorMessage: 'Authentication was cancelled by user',
        );
      }
    } on PlatformException catch (e) {
      _logSecurityEvent(
        'Biometric authentication platform exception: ${e.code} - ${e.message}',
      );
      return _handlePlatformException(e);
    } catch (e) {
      _logSecurityEvent('Biometric authentication unexpected error: $e');
      return BiometricAuthResult(
        success: false,
        errorType: BiometricErrorType.unknown,
        errorMessage: 'An unexpected error occurred during authentication',
      );
    }
  }

  /// Check if user has enrolled biometrics
  Future<bool> hasEnrolledBiometrics() async {
    try {
      final availableBiometrics = await getAvailableBiometrics();
      return availableBiometrics.isNotEmpty;
    } catch (e) {
      _logSecurityEvent('Failed to check enrolled biometrics: $e');
      return false;
    }
  }

  /// Get biometric capability info
  Future<BiometricCapability> getBiometricCapability() async {
    final isAvailable = await isBiometricAvailable();
    final availableTypes = await getAvailableBiometrics();
    final hasEnrolled = await hasEnrolledBiometrics();

    return BiometricCapability(
      isAvailable: isAvailable,
      availableTypes: availableTypes,
      hasEnrolledBiometrics: hasEnrolled,
      supportedTypes: _getSupportedTypeNames(availableTypes),
    );
  }

  /// Authenticate for sensitive operations
  Future<BiometricAuthResult> authenticateForSensitiveOperation({
    required String operation,
  }) async {
    return authenticate(
      reason: 'Please authenticate to $operation',
      useErrorDialogs: true,
      stickyAuth: true,
    );
  }

  /// Stop authentication (if in progress)
  Future<void> stopAuthentication() async {
    try {
      await _localAuth.stopAuthentication();
      _logSecurityEvent('Biometric authentication stopped');
    } catch (e) {
      _logSecurityEvent('Failed to stop authentication: $e');
    }
  }

  BiometricAuthResult _handlePlatformException(PlatformException e) {
    switch (e.code) {
      case auth_error.notAvailable:
        return BiometricAuthResult(
          success: false,
          errorType: BiometricErrorType.notAvailable,
          errorMessage: 'Biometric authentication is not available',
        );
      case auth_error.notEnrolled:
        return BiometricAuthResult(
          success: false,
          errorType: BiometricErrorType.notEnrolled,
          errorMessage: 'No biometrics enrolled on this device',
        );
      case auth_error.lockedOut:
        return BiometricAuthResult(
          success: false,
          errorType: BiometricErrorType.lockedOut,
          errorMessage: 'Biometric authentication is temporarily locked',
        );
      case auth_error.permanentlyLockedOut:
        return BiometricAuthResult(
          success: false,
          errorType: BiometricErrorType.permanentlyLockedOut,
          errorMessage: 'Biometric authentication is permanently locked',
        );
      default:
        return BiometricAuthResult(
          success: false,
          errorType: BiometricErrorType.unknown,
          errorMessage: e.message ?? 'Unknown biometric error',
        );
    }
  }

  List<String> _getSupportedTypeNames(List<BiometricType> types) {
    return types.map((type) {
      switch (type) {
        case BiometricType.face:
          return 'Face ID';
        case BiometricType.fingerprint:
          return 'Fingerprint';
        case BiometricType.iris:
          return 'Iris';
        case BiometricType.weak:
          return 'Weak Biometric';
        case BiometricType.strong:
          return 'Strong Biometric';
      }
    }).toList();
  }

  void _logSecurityEvent(String message) {
    // TODO: Replace with proper logging framework
    // SecurityLogger.log('[BIOMETRIC] $message');
  }
}

/// Biometric authentication result
class BiometricAuthResult {
  final bool success;
  final BiometricErrorType? errorType;
  final String? errorMessage;

  BiometricAuthResult({
    required this.success,
    this.errorType,
    this.errorMessage,
  });
}

/// Biometric error types
enum BiometricErrorType {
  notAvailable,
  notEnrolled,
  userCancel,
  lockedOut,
  permanentlyLockedOut,
  unknown,
}

/// Biometric capability information
class BiometricCapability {
  final bool isAvailable;
  final List<BiometricType> availableTypes;
  final bool hasEnrolledBiometrics;
  final List<String> supportedTypes;

  BiometricCapability({
    required this.isAvailable,
    required this.availableTypes,
    required this.hasEnrolledBiometrics,
    required this.supportedTypes,
  });
}
