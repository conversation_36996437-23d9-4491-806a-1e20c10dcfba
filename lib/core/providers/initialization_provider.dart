import 'package:billionaires_social/core/services/version_control_service.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import 'package:billionaires_social/features/stories/services/unified_story_service.dart';

part 'initialization_provider.g.dart';

@Riverpod(keepAlive: true)
Future<AppStatus> initialization(Ref ref) async {
  final versionService = getIt<VersionControlService>();
  final prefs = await SharedPreferences.getInstance();

  // Clean up expired stories on app startup using service locator
  try {
    final storyService = getIt<UnifiedStoryService>();
    await storyService.cleanupExpiredStories();
  } catch (e) {
    // If story service isn't registered yet, skip cleanup
    // This prevents initialization deadlocks
    debugPrint('⚠️ Story service not available during initialization: $e');
  }

  final latestVersion = await versionService.checkForUpdates();
  final hasCompletedOnboarding =
      prefs.getBool('beta_onboarding_completed') ?? false;

  if (latestVersion != null && latestVersion.forceUpdate) {
    return AppStatus.forceUpdate;
  }

  if (!hasCompletedOnboarding) {
    return AppStatus.onboarding;
  }

  return AppStatus.authenticated;
}

enum AppStatus { loading, forceUpdate, onboarding, authenticated }
