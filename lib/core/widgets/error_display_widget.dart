import 'package:flutter/material.dart';
import 'package:billionaires_social/core/services/error_handling_service.dart';

/// Widget for displaying user-friendly error messages
class ErrorDisplayWidget extends StatelessWidget {
  final dynamic error;
  final String? context;
  final VoidCallback? onRetry;
  final bool showRetryButton;
  final bool isCompact;

  const ErrorDisplayWidget({
    super.key,
    required this.error,
    this.context,
    this.onRetry,
    this.showRetryButton = true,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    final errorService = ErrorHandlingService();
    final errorInfo = errorService.formatErrorForNotification(
      error,
      context: this.context,
    );

    if (isCompact) {
      return _buildCompactError(context, errorInfo);
    }

    return _buildFullError(context, errorInfo);
  }

  Widget _buildCompactError(
    BuildContext context,
    Map<String, dynamic> errorInfo,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: _getErrorColor(errorInfo['severity']).withValues(alpha: 0.1),
        border: Border.all(
          color: _getErrorColor(errorInfo['severity']).withValues(alpha: 0.3),
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            _getErrorIcon(errorInfo['severity']),
            color: _getErrorColor(errorInfo['severity']),
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              errorInfo['message'],
              style: TextStyle(
                color: _getErrorColor(errorInfo['severity']),
                fontSize: 14,
              ),
            ),
          ),
          if (showRetryButton && errorInfo['canRetry'] && onRetry != null) ...[
            const SizedBox(width: 8),
            TextButton(
              onPressed: onRetry,
              style: TextButton.styleFrom(
                foregroundColor: _getErrorColor(errorInfo['severity']),
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 4,
                ),
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
              child: const Text('Retry', style: TextStyle(fontSize: 12)),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFullError(BuildContext context, Map<String, dynamic> errorInfo) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _getErrorColor(errorInfo['severity']).withValues(alpha: 0.05),
        border: Border.all(
          color: _getErrorColor(errorInfo['severity']).withValues(alpha: 0.2),
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getErrorIcon(errorInfo['severity']),
            color: _getErrorColor(errorInfo['severity']),
            size: 48,
          ),
          const SizedBox(height: 16),
          Text(
            errorInfo['title'],
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _getErrorColor(errorInfo['severity']),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            errorInfo['message'],
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
              height: 1.4,
            ),
            textAlign: TextAlign.center,
          ),
          if (showRetryButton && errorInfo['canRetry'] && onRetry != null) ...[
            const SizedBox(height: 20),
            ElevatedButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh, size: 18),
              label: const Text('Try Again'),
              style: ElevatedButton.styleFrom(
                backgroundColor: _getErrorColor(errorInfo['severity']),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Color _getErrorColor(String severity) {
    switch (severity) {
      case 'high':
        return Colors.red;
      case 'medium':
        return Colors.orange;
      case 'low':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  IconData _getErrorIcon(String severity) {
    switch (severity) {
      case 'high':
        return Icons.error;
      case 'medium':
        return Icons.warning;
      case 'low':
        return Icons.info;
      default:
        return Icons.help_outline;
    }
  }
}

/// Snackbar for showing error messages
class ErrorSnackBar {
  static void show(
    BuildContext context,
    dynamic error, {
    String? contextInfo,
    VoidCallback? onRetry,
  }) {
    final errorService = ErrorHandlingService();
    final errorInfo = errorService.formatErrorForNotification(
      error,
      context: contextInfo,
    );

    final snackBar = SnackBar(
      content: Row(
        children: [
          Icon(
            _getErrorIcon(errorInfo['severity']),
            color: Colors.white,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              errorInfo['message'],
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
      backgroundColor: _getErrorColor(errorInfo['severity']),
      duration: Duration(seconds: errorInfo['severity'] == 'high' ? 6 : 4),
      action: errorInfo['canRetry'] && onRetry != null
          ? SnackBarAction(
              label: 'Retry',
              textColor: Colors.white,
              onPressed: onRetry,
            )
          : null,
      behavior: SnackBarBehavior.floating,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
    );

    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }

  static Color _getErrorColor(String severity) {
    switch (severity) {
      case 'high':
        return Colors.red[700]!;
      case 'medium':
        return Colors.orange[700]!;
      case 'low':
        return Colors.blue[700]!;
      default:
        return Colors.grey[700]!;
    }
  }

  static IconData _getErrorIcon(String severity) {
    switch (severity) {
      case 'high':
        return Icons.error;
      case 'medium':
        return Icons.warning;
      case 'low':
        return Icons.info;
      default:
        return Icons.help_outline;
    }
  }
}

/// Loading state with error handling
class LoadingStateWidget extends StatelessWidget {
  final bool isLoading;
  final dynamic error;
  final Widget child;
  final VoidCallback? onRetry;
  final String? loadingText;
  final String? errorContext;
  final bool isCompact;
  final LoadingType loadingType;

  const LoadingStateWidget({
    super.key,
    required this.isLoading,
    this.error,
    required this.child,
    this.onRetry,
    this.loadingText,
    this.errorContext,
    this.isCompact = false,
    this.loadingType = LoadingType.circular,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return _buildLoadingState(context);
    }

    if (error != null) {
      return ErrorDisplayWidget(
        error: error,
        context: errorContext,
        onRetry: onRetry,
        isCompact: isCompact,
      );
    }

    return child;
  }

  Widget _buildLoadingState(BuildContext context) {
    if (isCompact) {
      return _buildCompactLoading();
    }

    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildLoadingIndicator(),
          if (loadingText != null) ...[
            const SizedBox(height: 16),
            Text(
              loadingText!,
              style: TextStyle(color: Colors.grey[600], fontSize: 14),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCompactLoading() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(width: 20, height: 20, child: _buildLoadingIndicator()),
          if (loadingText != null) ...[
            const SizedBox(width: 12),
            Text(
              loadingText!,
              style: TextStyle(color: Colors.grey[600], fontSize: 12),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    switch (loadingType) {
      case LoadingType.circular:
        return const CircularProgressIndicator();
      case LoadingType.linear:
        return const LinearProgressIndicator();
      case LoadingType.dots:
        return const DotsLoadingIndicator();
      case LoadingType.pulse:
        return const PulseLoadingIndicator();
    }
  }
}

/// Types of loading indicators
enum LoadingType { circular, linear, dots, pulse }

/// Dots loading indicator
class DotsLoadingIndicator extends StatefulWidget {
  final Color? color;
  final double size;

  const DotsLoadingIndicator({super.key, this.color, this.size = 8.0});

  @override
  State<DotsLoadingIndicator> createState() => _DotsLoadingIndicatorState();
}

class _DotsLoadingIndicatorState extends State<DotsLoadingIndicator>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _animations = List.generate(3, (index) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: _controller,
          curve: Interval(
            index * 0.2,
            0.6 + index * 0.2,
            curve: Curves.easeInOut,
          ),
        ),
      );
    });

    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(3, (index) {
        return AnimatedBuilder(
          animation: _animations[index],
          builder: (context, child) {
            return Container(
              margin: EdgeInsets.symmetric(horizontal: widget.size * 0.2),
              child: Opacity(
                opacity: _animations[index].value,
                child: Container(
                  width: widget.size,
                  height: widget.size,
                  decoration: BoxDecoration(
                    color: widget.color ?? Theme.of(context).primaryColor,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            );
          },
        );
      }),
    );
  }
}

/// Pulse loading indicator
class PulseLoadingIndicator extends StatefulWidget {
  final Color? color;
  final double size;

  const PulseLoadingIndicator({super.key, this.color, this.size = 40.0});

  @override
  State<PulseLoadingIndicator> createState() => _PulseLoadingIndicatorState();
}

class _PulseLoadingIndicatorState extends State<PulseLoadingIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));

    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: widget.size,
          height: widget.size,
          decoration: BoxDecoration(
            color: (widget.color ?? Theme.of(context).primaryColor).withValues(alpha: 
              _animation.value,
            ),
            shape: BoxShape.circle,
          ),
        );
      },
    );
  }
}

/// Mixin for easy error handling in widgets
mixin ErrorHandlingMixin {
  void showError(
    BuildContext context,
    dynamic error, {
    String? contextInfo,
    VoidCallback? onRetry,
  }) {
    ErrorSnackBar.show(
      context,
      error,
      contextInfo: contextInfo,
      onRetry: onRetry,
    );
  }

  void logError(
    dynamic error, {
    String? context,
    Map<String, dynamic>? additionalData,
    StackTrace? stackTrace,
  }) {
    ErrorHandlingService().logError(
      error,
      context: context,
      additionalData: additionalData,
      stackTrace: stackTrace,
    );
  }

  String formatError(dynamic error, {String? context}) {
    return ErrorHandlingService().handleError(error, context: context);
  }
}
