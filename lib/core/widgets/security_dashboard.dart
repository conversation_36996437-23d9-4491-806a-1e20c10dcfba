import 'package:flutter/material.dart';
import 'package:billionaires_social/core/services/security_service.dart';

/// Security monitoring dashboard widget
/// Shows security events, audit logs, and system security health
class SecurityDashboard extends StatefulWidget {
  const SecurityDashboard({super.key});

  @override
  State<SecurityDashboard> createState() => _SecurityDashboardState();
}

class _SecurityDashboardState extends State<SecurityDashboard> {
  final SecurityService _securityService = SecurityService();
  SecuritySummary? _summary;
  List<SecurityEvent>? _auditLog;

  @override
  void initState() {
    super.initState();
    _loadSecurityData();

    // Refresh data every 10 seconds
    Future.delayed(const Duration(seconds: 10), _refreshData);
  }

  void _loadSecurityData() {
    setState(() {
      _summary = _securityService.getSecuritySummary();
      _auditLog = _securityService.getAuditLog(limit: 20);
    });
  }

  void _refreshData() {
    if (mounted) {
      _loadSecurityData();
      Future.delayed(const Duration(seconds: 10), _refreshData);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Security Dashboard'),
        backgroundColor: Colors.red.shade800,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSecurityData,
          ),
          IconButton(
            icon: const Icon(Icons.clear_all),
            onPressed: _showClearDataDialog,
          ),
        ],
      ),
      body: _summary == null
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSecurityScore(),
                  const SizedBox(height: 16),
                  _buildSecurityStats(),
                  const SizedBox(height: 16),
                  _buildActiveThreats(),
                  const SizedBox(height: 16),
                  _buildAuditLog(),
                  const SizedBox(height: 16),
                  _buildSecurityActions(),
                ],
              ),
            ),
    );
  }

  Widget _buildSecurityScore() {
    final score = _summary!.securityScore;
    final color = score >= 80
        ? Colors.green
        : score >= 60
        ? Colors.orange
        : Colors.red;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Icon(Icons.security, color: color),
                const SizedBox(width: 8),
                Text(
                  'Security Score',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            CircularProgressIndicator(
              value: score / 100,
              backgroundColor: Colors.grey.shade300,
              valueColor: AlwaysStoppedAnimation<Color>(color),
              strokeWidth: 8,
            ),
            const SizedBox(height: 8),
            Text(
              '${score.toStringAsFixed(1)}/100',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _getSecurityScoreDescription(score),
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecurityStats() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Security Statistics (Last 24 Hours)',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Total Events',
                    '${_summary!.totalEvents}',
                    Icons.event,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Failed Logins',
                    '${_summary!.failedLogins}',
                    Icons.error,
                    Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Rate Limits',
                    '${_summary!.rateLimitViolations}',
                    Icons.speed,
                    Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Lockouts',
                    '${_summary!.accountLockouts}',
                    Icons.lock,
                    Colors.red,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildActiveThreats() {
    final hasThreats =
        _summary!.activeLockouts > 0 || _summary!.activeRateLimits > 5;

    return Card(
      color: hasThreats ? Colors.red.shade50 : Colors.green.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  hasThreats ? Icons.warning : Icons.check_circle,
                  color: hasThreats ? Colors.red : Colors.green,
                ),
                const SizedBox(width: 8),
                Text(
                  'Active Security Status',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (hasThreats) ...[
              if (_summary!.activeLockouts > 0)
                _buildThreatItem(
                  'Account Lockouts Active',
                  '${_summary!.activeLockouts} accounts currently locked',
                  Icons.lock,
                  Colors.red,
                ),
              if (_summary!.activeRateLimits > 5)
                _buildThreatItem(
                  'High Rate Limit Activity',
                  '${_summary!.activeRateLimits} active rate limits',
                  Icons.speed,
                  Colors.orange,
                ),
            ] else ...[
              Row(
                children: [
                  const Icon(Icons.check, color: Colors.green),
                  const SizedBox(width: 8),
                  Text(
                    'No active security threats detected',
                    style: TextStyle(color: Colors.green.shade700),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildThreatItem(
    String title,
    String description,
    IconData icon,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(fontWeight: FontWeight.bold, color: color),
                ),
                Text(description, style: Theme.of(context).textTheme.bodySmall),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAuditLog() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recent Security Events',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12),
            if (_auditLog == null || _auditLog!.isEmpty)
              const Text('No security events recorded')
            else
              ...(_auditLog!
                  .take(10)
                  .map((event) => _buildAuditLogItem(event))),
          ],
        ),
      ),
    );
  }

  Widget _buildAuditLogItem(SecurityEvent event) {
    final color = _getEventColor(event.type);
    final icon = _getEventIcon(event.type);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getEventDescription(event),
                  style: const TextStyle(fontSize: 14),
                ),
                Text(
                  _formatTimestamp(event.timestamp),
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: Colors.grey.shade600),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Security Actions',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: _testRateLimit,
                  icon: const Icon(Icons.speed),
                  label: const Text('Test Rate Limit'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _testValidation,
                  icon: const Icon(Icons.verified_user),
                  label: const Text('Test Validation'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _generateToken,
                  icon: const Icon(Icons.key),
                  label: const Text('Generate Token'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _getSecurityScoreDescription(double score) {
    if (score >= 90) return 'Excellent security posture';
    if (score >= 80) return 'Good security with minor issues';
    if (score >= 70) return 'Moderate security concerns';
    if (score >= 60) return 'Security improvements needed';
    return 'Critical security issues detected';
  }

  Color _getEventColor(SecurityEventType type) {
    switch (type) {
      case SecurityEventType.authenticationSuccess:
        return Colors.green;
      case SecurityEventType.authenticationFailed:
        return Colors.red;
      case SecurityEventType.rateLimitExceeded:
        return Colors.orange;
      case SecurityEventType.accountLocked:
        return Colors.red;
      case SecurityEventType.suspiciousActivity:
        return Colors.purple;
      case SecurityEventType.dataAccess:
        return Colors.blue;
      case SecurityEventType.privilegeEscalation:
        return Colors.red;
      case SecurityEventType.tokenStored:
        return Colors.green;
      case SecurityEventType.tokenAccessed:
        return Colors.blue;
      case SecurityEventType.tokenDeleted:
        return Colors.orange;
      case SecurityEventType.allTokensCleared:
        return Colors.red;
      case SecurityEventType.securityError:
        return Colors.red;
    }
  }

  IconData _getEventIcon(SecurityEventType type) {
    switch (type) {
      case SecurityEventType.authenticationSuccess:
        return Icons.check_circle;
      case SecurityEventType.authenticationFailed:
        return Icons.error;
      case SecurityEventType.rateLimitExceeded:
        return Icons.speed;
      case SecurityEventType.accountLocked:
        return Icons.lock;
      case SecurityEventType.suspiciousActivity:
        return Icons.warning;
      case SecurityEventType.dataAccess:
        return Icons.visibility;
      case SecurityEventType.privilegeEscalation:
        return Icons.security;
      case SecurityEventType.tokenStored:
        return Icons.save;
      case SecurityEventType.tokenAccessed:
        return Icons.key;
      case SecurityEventType.tokenDeleted:
        return Icons.delete;
      case SecurityEventType.allTokensCleared:
        return Icons.clear_all;
      case SecurityEventType.securityError:
        return Icons.error_outline;
    }
  }

  String _getEventDescription(SecurityEvent event) {
    switch (event.type) {
      case SecurityEventType.authenticationSuccess:
        return 'Successful login: ${event.identifier}';
      case SecurityEventType.authenticationFailed:
        return 'Failed login attempt: ${event.identifier}';
      case SecurityEventType.rateLimitExceeded:
        return 'Rate limit exceeded: ${event.identifier}';
      case SecurityEventType.accountLocked:
        return 'Account locked: ${event.identifier}';
      case SecurityEventType.suspiciousActivity:
        return 'Suspicious activity: ${event.identifier}';
      case SecurityEventType.dataAccess:
        return 'Data access: ${event.identifier}';
      case SecurityEventType.privilegeEscalation:
        return 'Privilege escalation: ${event.identifier}';
      case SecurityEventType.tokenStored:
        return 'Token stored: ${event.identifier}';
      case SecurityEventType.tokenAccessed:
        return 'Token accessed: ${event.identifier}';
      case SecurityEventType.tokenDeleted:
        return 'Token deleted: ${event.identifier}';
      case SecurityEventType.allTokensCleared:
        return 'All tokens cleared: ${event.identifier}';
      case SecurityEventType.securityError:
        return 'Security error: ${event.identifier}';
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  void _testRateLimit() {
    final result = _securityService.checkRateLimit('test_user', maxRequests: 3);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(result ? 'Rate limit OK' : 'Rate limit exceeded'),
        backgroundColor: result ? Colors.green : Colors.red,
      ),
    );
    _loadSecurityData();
  }

  void _testValidation() {
    final result = _securityService.validateInput(
      '<EMAIL>',
      InputType.email,
    );
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Validation: ${result.message}'),
        backgroundColor: result.isValid ? Colors.green : Colors.red,
      ),
    );
  }

  void _generateToken() {
    final token = _securityService.generateSecureToken();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Generated token: ${token.substring(0, 8)}...'),
        backgroundColor: Colors.blue,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showClearDataDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Security Data'),
        content: const Text(
          'This will clear all security logs and reset counters. Are you sure?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              _securityService.clearSecurityData();
              _loadSecurityData();
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Security data cleared')),
              );
            },
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }
}
