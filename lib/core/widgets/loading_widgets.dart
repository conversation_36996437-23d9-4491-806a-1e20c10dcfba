import 'package:flutter/material.dart';
import 'package:billionaires_social/core/widgets/error_display_widget.dart';

/// Specialized loading widget for feed posts
class FeedLoadingWidget extends StatelessWidget {
  final bool isLoading;
  final bool isLoadingMore;
  final dynamic error;
  final List<Widget> children;
  final VoidCallback? onRetry;
  final VoidCallback? onLoadMore;
  final bool hasMore;

  const FeedLoadingWidget({
    super.key,
    required this.isLoading,
    this.isLoadingMore = false,
    this.error,
    required this.children,
    this.onRetry,
    this.onLoadMore,
    this.hasMore = true,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading && children.isEmpty) {
      return const FeedSkeletonLoader();
    }

    if (error != null && children.isEmpty) {
      return ErrorDisplayWidget(
        error: error,
        context: 'Loading feed',
        onRetry: onRetry,
      );
    }

    return Column(
      children: [
        ...children,
        if (isLoadingMore) ...[
          const Padding(
            padding: EdgeInsets.all(16.0),
            child: LoadingStateWidget(
              isLoading: true,
              loadingText: 'Loading more posts...',
              isCompact: true,
              loadingType: LoadingType.dots,
              child: SizedBox.shrink(),
            ),
          ),
        ] else if (hasMore && onLoadMore != null) ...[
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: ElevatedButton(
              onPressed: onLoadMore,
              child: const Text('Load More'),
            ),
          ),
        ] else if (!hasMore) ...[
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              'No more posts to load',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
        if (error != null && children.isNotEmpty) ...[
          ErrorDisplayWidget(
            error: error,
            context: 'Loading more posts',
            onRetry: onRetry,
            isCompact: true,
          ),
        ],
      ],
    );
  }
}

/// Skeleton loader for feed posts
class FeedSkeletonLoader extends StatelessWidget {
  final int itemCount;

  const FeedSkeletonLoader({
    super.key,
    this.itemCount = 3,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: itemCount,
      itemBuilder: (context, index) => const PostSkeletonItem(),
    );
  }
}

/// Skeleton item for individual posts
class PostSkeletonItem extends StatelessWidget {
  const PostSkeletonItem({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // User info skeleton
            Row(
              children: [
                const SkeletonBox(width: 40, height: 40, isCircle: true),
                const SizedBox(width: 12),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SkeletonBox(
                      width: 120,
                      height: 16,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    const SizedBox(height: 4),
                    SkeletonBox(
                      width: 80,
                      height: 12,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            // Content skeleton
            SkeletonBox(
              width: double.infinity,
              height: 16,
              borderRadius: BorderRadius.circular(4),
            ),
            const SizedBox(height: 8),
            SkeletonBox(
              width: MediaQuery.of(context).size.width * 0.7,
              height: 16,
              borderRadius: BorderRadius.circular(4),
            ),
            const SizedBox(height: 12),
            // Image skeleton (optional)
            SkeletonBox(
              width: double.infinity,
              height: 200,
              borderRadius: BorderRadius.circular(8),
            ),
            const SizedBox(height: 12),
            // Actions skeleton
            Row(
              children: [
                SkeletonBox(
                  width: 60,
                  height: 32,
                  borderRadius: BorderRadius.circular(16),
                ),
                const SizedBox(width: 12),
                SkeletonBox(
                  width: 60,
                  height: 32,
                  borderRadius: BorderRadius.circular(16),
                ),
                const SizedBox(width: 12),
                SkeletonBox(
                  width: 60,
                  height: 32,
                  borderRadius: BorderRadius.circular(16),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// Generic skeleton box
class SkeletonBox extends StatefulWidget {
  final double width;
  final double height;
  final BorderRadius? borderRadius;
  final bool isCircle;

  const SkeletonBox({
    super.key,
    required this.width,
    required this.height,
    this.borderRadius,
    this.isCircle = false,
  });

  @override
  State<SkeletonBox> createState() => _SkeletonBoxState();
}

class _SkeletonBoxState extends State<SkeletonBox>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _animation = Tween<double>(begin: 0.3, end: 0.7).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );

    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            color: Colors.grey[300]?.withValues(alpha: _animation.value),
            borderRadius: widget.isCircle
                ? null
                : widget.borderRadius ?? BorderRadius.circular(4),
            shape: widget.isCircle ? BoxShape.circle : BoxShape.rectangle,
          ),
        );
      },
    );
  }
}

/// Loading overlay for async operations
class LoadingOverlay extends StatelessWidget {
  final bool isLoading;
  final Widget child;
  final String? loadingText;
  final Color? overlayColor;

  const LoadingOverlay({
    super.key,
    required this.isLoading,
    required this.child,
    this.loadingText,
    this.overlayColor,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isLoading)
          Container(
            color: (overlayColor ?? Colors.black).withValues(alpha: 0.5),
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                  if (loadingText != null) ...[
                    const SizedBox(height: 16),
                    Text(
                      loadingText!,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ],
              ),
            ),
          ),
      ],
    );
  }
}

/// Button with loading state
class LoadingButton extends StatelessWidget {
  final bool isLoading;
  final VoidCallback? onPressed;
  final Widget child;
  final ButtonStyle? style;
  final String? loadingText;

  const LoadingButton({
    super.key,
    required this.isLoading,
    required this.onPressed,
    required this.child,
    this.style,
    this.loadingText,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: style,
      child: isLoading
          ? Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                if (loadingText != null) ...[
                  const SizedBox(width: 8),
                  Text(loadingText!),
                ],
              ],
            )
          : child,
    );
  }
}

/// Refresh indicator with custom styling
class CustomRefreshIndicator extends StatelessWidget {
  final Future<void> Function() onRefresh;
  final Widget child;
  final String? refreshText;

  const CustomRefreshIndicator({
    super.key,
    required this.onRefresh,
    required this.child,
    this.refreshText,
  });

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: onRefresh,
      color: Theme.of(context).primaryColor,
      backgroundColor: Colors.white,
      child: child,
    );
  }
}
