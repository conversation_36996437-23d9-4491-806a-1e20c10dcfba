import 'package:flutter/material.dart';
import 'package:billionaires_social/core/services/memory_management_service.dart';

/// Optimized ListView with performance enhancements and memory management
class OptimizedListView<T> extends StatefulWidget {
  final List<T> items;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final Widget? separator;
  final EdgeInsetsGeometry? padding;
  final ScrollController? controller;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final Widget? header;
  final Widget? footer;
  final VoidCallback? onLoadMore;
  final bool hasMore;
  final bool isLoading;
  final Widget? loadingWidget;
  final Widget? emptyWidget;
  final double? itemExtent;
  final int? semanticChildCount;
  final bool addAutomaticKeepAlives;
  final bool addRepaintBoundaries;
  final bool addSemanticIndexes;
  final double cacheExtent;
  final int loadMoreThreshold;

  const OptimizedListView({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.separator,
    this.padding,
    this.controller,
    this.shrinkWrap = false,
    this.physics,
    this.header,
    this.footer,
    this.onLoadMore,
    this.hasMore = false,
    this.isLoading = false,
    this.loadingWidget,
    this.emptyWidget,
    this.itemExtent,
    this.semanticChildCount,
    this.addAutomaticKeepAlives = true,
    this.addRepaintBoundaries = true,
    this.addSemanticIndexes = true,
    this.cacheExtent = 250.0, // Optimized cache extent
    this.loadMoreThreshold = 3, // Load more when 3 items from bottom
  });

  @override
  State<OptimizedListView<T>> createState() => _OptimizedListViewState<T>();
}

class _OptimizedListViewState<T> extends State<OptimizedListView<T>> {
  late ScrollController _scrollController;
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.controller ?? ScrollController();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _scrollController.dispose();
    } else {
      _scrollController.removeListener(_onScroll);
    }
    super.dispose();
  }

  void _onScroll() {
    // Load more functionality
    if (widget.onLoadMore != null &&
        widget.hasMore &&
        !_isLoadingMore &&
        !widget.isLoading) {
      final maxScroll = _scrollController.position.maxScrollExtent;
      final currentScroll = _scrollController.position.pixels;
      final threshold =
          maxScroll -
          (widget.loadMoreThreshold * 200); // Approximate item height

      if (currentScroll >= threshold) {
        _loadMore();
      }
    }

    // Memory management - check if we should clean up
    _checkMemoryUsage();
  }

  void _loadMore() async {
    if (_isLoadingMore) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      widget.onLoadMore?.call();
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    }
  }

  void _checkMemoryUsage() {
    final memoryStats = MemoryManagementService().getMemoryStats();
    if (memoryStats.isCritical) {
      // Force garbage collection when memory is critical
      MemoryManagementService().forceGarbageCollection();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.items.isEmpty && !widget.isLoading) {
      return widget.emptyWidget ?? _buildDefaultEmptyWidget();
    }

    return ListView.builder(
      controller: _scrollController,
      padding: widget.padding,
      shrinkWrap: widget.shrinkWrap,
      physics: widget.physics,
      itemExtent: widget.itemExtent,
      cacheExtent: widget.cacheExtent,
      addAutomaticKeepAlives: widget.addAutomaticKeepAlives,
      addRepaintBoundaries: widget.addRepaintBoundaries,
      addSemanticIndexes: widget.addSemanticIndexes,
      semanticChildCount: widget.semanticChildCount ?? _getTotalItemCount(),
      itemCount: _getTotalItemCount(),
      itemBuilder: (context, index) {
        return _buildItem(context, index);
      },
    );
  }

  int _getTotalItemCount() {
    int count = 0;

    if (widget.header != null) count++;
    count += widget.items.length;
    if (widget.separator != null && widget.items.isNotEmpty) {
      count += widget.items.length - 1; // Separators between items
    }
    if (widget.footer != null) count++;
    if ((widget.isLoading || _isLoadingMore) && widget.hasMore) count++;

    return count;
  }

  Widget _buildItem(BuildContext context, int index) {
    int currentIndex = index;

    // Header
    if (widget.header != null) {
      if (currentIndex == 0) {
        return RepaintBoundary(child: widget.header!);
      }
      currentIndex--;
    }

    // Items and separators
    if (currentIndex < _getItemsAndSeparatorsCount()) {
      if (widget.separator != null && widget.items.isNotEmpty) {
        // With separators
        if (currentIndex.isEven) {
          final itemIndex = currentIndex ~/ 2;
          return RepaintBoundary(
            child: widget.itemBuilder(
              context,
              widget.items[itemIndex],
              itemIndex,
            ),
          );
        } else {
          return RepaintBoundary(child: widget.separator!);
        }
      } else {
        // Without separators
        return RepaintBoundary(
          child: widget.itemBuilder(
            context,
            widget.items[currentIndex],
            currentIndex,
          ),
        );
      }
    }
    currentIndex -= _getItemsAndSeparatorsCount();

    // Footer
    if (widget.footer != null) {
      if (currentIndex == 0) {
        return RepaintBoundary(child: widget.footer!);
      }
      currentIndex--;
    }

    // Loading indicator
    if ((widget.isLoading || _isLoadingMore) && widget.hasMore) {
      if (currentIndex == 0) {
        return RepaintBoundary(
          child: widget.loadingWidget ?? _buildDefaultLoadingWidget(),
        );
      }
    }

    return const SizedBox.shrink();
  }

  int _getItemsAndSeparatorsCount() {
    if (widget.separator != null && widget.items.isNotEmpty) {
      return widget.items.length * 2 - 1; // Items + separators
    }
    return widget.items.length;
  }

  Widget _buildDefaultEmptyWidget() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inbox_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No items to display',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDefaultLoadingWidget() {
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: Center(child: CircularProgressIndicator()),
    );
  }
}

/// Optimized GridView with performance enhancements
class OptimizedGridView<T> extends StatefulWidget {
  final List<T> items;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final SliverGridDelegate gridDelegate;
  final EdgeInsetsGeometry? padding;
  final ScrollController? controller;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final VoidCallback? onLoadMore;
  final bool hasMore;
  final bool isLoading;
  final Widget? loadingWidget;
  final Widget? emptyWidget;
  final int? semanticChildCount;
  final bool addAutomaticKeepAlives;
  final bool addRepaintBoundaries;
  final bool addSemanticIndexes;
  final double cacheExtent;
  final int loadMoreThreshold;

  const OptimizedGridView({
    super.key,
    required this.items,
    required this.itemBuilder,
    required this.gridDelegate,
    this.padding,
    this.controller,
    this.shrinkWrap = false,
    this.physics,
    this.onLoadMore,
    this.hasMore = false,
    this.isLoading = false,
    this.loadingWidget,
    this.emptyWidget,
    this.semanticChildCount,
    this.addAutomaticKeepAlives = true,
    this.addRepaintBoundaries = true,
    this.addSemanticIndexes = true,
    this.cacheExtent = 250.0,
    this.loadMoreThreshold = 6, // Load more when 6 items from bottom
  });

  @override
  State<OptimizedGridView<T>> createState() => _OptimizedGridViewState<T>();
}

class _OptimizedGridViewState<T> extends State<OptimizedGridView<T>> {
  late ScrollController _scrollController;
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.controller ?? ScrollController();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _scrollController.dispose();
    } else {
      _scrollController.removeListener(_onScroll);
    }
    super.dispose();
  }

  void _onScroll() {
    if (widget.onLoadMore != null &&
        widget.hasMore &&
        !_isLoadingMore &&
        !widget.isLoading) {
      final maxScroll = _scrollController.position.maxScrollExtent;
      final currentScroll = _scrollController.position.pixels;
      final threshold =
          maxScroll -
          (widget.loadMoreThreshold * 100); // Approximate item height

      if (currentScroll >= threshold) {
        _loadMore();
      }
    }
  }

  void _loadMore() async {
    if (_isLoadingMore) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      widget.onLoadMore?.call();
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.items.isEmpty && !widget.isLoading) {
      return widget.emptyWidget ?? _buildDefaultEmptyWidget();
    }

    final itemCount =
        widget.items.length +
        ((widget.isLoading || _isLoadingMore) && widget.hasMore ? 1 : 0);

    return GridView.builder(
      controller: _scrollController,
      padding: widget.padding,
      shrinkWrap: widget.shrinkWrap,
      physics: widget.physics,
      gridDelegate: widget.gridDelegate,
      cacheExtent: widget.cacheExtent,
      addAutomaticKeepAlives: widget.addAutomaticKeepAlives,
      addRepaintBoundaries: widget.addRepaintBoundaries,
      addSemanticIndexes: widget.addSemanticIndexes,
      semanticChildCount: widget.semanticChildCount ?? itemCount,
      itemCount: itemCount,
      itemBuilder: (context, index) {
        if (index < widget.items.length) {
          return RepaintBoundary(
            child: widget.itemBuilder(context, widget.items[index], index),
          );
        } else {
          return RepaintBoundary(
            child: widget.loadingWidget ?? _buildDefaultLoadingWidget(),
          );
        }
      },
    );
  }

  Widget _buildDefaultEmptyWidget() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.grid_view_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No items to display',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDefaultLoadingWidget() {
    return const Center(child: CircularProgressIndicator());
  }
}
