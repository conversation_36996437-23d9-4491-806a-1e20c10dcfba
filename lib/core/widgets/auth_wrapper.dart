import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/profile/providers/user_profile_provider.dart';
import 'package:billionaires_social/core/main_navigation.dart';
import 'package:billionaires_social/features/auth/screens/register_screen.dart';
import 'package:billionaires_social/features/auth/screens/login_screen.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/core/services/analytics_service.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/features/auth/providers/auth_provider.dart';
import 'package:billionaires_social/core/widgets/error_display_widget.dart';

class AuthWrapper extends ConsumerStatefulWidget {
  const AuthWrapper({super.key});

  @override
  ConsumerState<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends ConsumerState<AuthWrapper> {
  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);
    final analyticsService = getIt<AnalyticsService>();

    return authState.when(
      data: (user) {
        if (user == null) {
          return const AuthSelectionScreen();
        }
        final userProfileState = ref.watch(userProfileProvider);
        return userProfileState.when(
          data: (userProfile) {
            if (userProfile != null) {
              // User is authenticated and profile is loaded
              debugPrint(
                '🚀 Navigating to MainNavigation - User: ${userProfile.name}',
              );
              analyticsService.logAppLifecycle(
                lifecycleEvent: 'user_authenticated',
              );
              return const MainNavigation();
            } else {
              // Profile is missing, navigate to auth selection
              return const AuthSelectionScreen();
            }
          },
          loading: () => Scaffold(
            body: LoadingStateWidget(
              isLoading: true,
              loadingText: 'Loading your profile...',
              loadingType: LoadingType.pulse,
              child: const SizedBox.shrink(),
            ),
          ),
          error: (error, stack) => Scaffold(
            body: ErrorDisplayWidget(
              error: error,
              context: 'Loading user profile',
              onRetry: () => ref.refresh(userProfileProvider),
            ),
          ),
        );
      },
      loading: () => Scaffold(
        body: LoadingStateWidget(
          isLoading: true,
          loadingText: 'Authenticating...',
          loadingType: LoadingType.circular,
          child: const SizedBox.shrink(),
        ),
      ),
      error: (error, stack) => Scaffold(
        body: ErrorDisplayWidget(
          error: error,
          context: 'Authentication',
          onRetry: () => ref.refresh(authProvider),
        ),
      ),
    );
  }
}

class AuthSelectionScreen extends ConsumerWidget {
  const AuthSelectionScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsService = getIt<AnalyticsService>();

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppTheme.luxuryBlack,
              AppTheme.luxuryGrey,
              AppTheme.luxuryBlack,
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(32),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // App Logo/Title
                Text(
                  'Billionaires Social',
                  style: Theme.of(context).textTheme.displayMedium?.copyWith(
                    color: AppTheme.primaryGold,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 2,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Join the Elite Network',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: AppTheme.luxuryWhite.withValues(alpha: 0.7),
                    fontWeight: FontWeight.w300,
                  ),
                ),
                const SizedBox(height: 80),

                // Login Button
                SizedBox(
                  width: double.infinity,
                  height: 56,
                  child: ElevatedButton(
                    onPressed: () {
                      analyticsService.logFeatureUsage(
                        featureName: 'auth_login_button_pressed',
                      );
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const LoginScreen(),
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryGold,
                      foregroundColor: AppTheme.luxuryBlack,
                      elevation: 8,
                      shadowColor: AppTheme.primaryGold.withValues(alpha: 0.3),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                    ),
                    child: Text(
                      'SIGN IN',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        letterSpacing: 2,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                // Register Button
                SizedBox(
                  width: double.infinity,
                  height: 56,
                  child: OutlinedButton(
                    onPressed: () {
                      analyticsService.logFeatureUsage(
                        featureName: 'auth_register_button_pressed',
                      );
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const RegisterScreen(),
                        ),
                      );
                    },
                    style: OutlinedButton.styleFrom(
                      foregroundColor: AppTheme.primaryGold,
                      side: BorderSide(color: AppTheme.primaryGold, width: 2),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                    ),
                    child: Text(
                      'CREATE ACCOUNT',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        letterSpacing: 2,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 40),

                // Terms and Privacy
                Text(
                  'By continuing, you agree to our Terms of Service and Privacy Policy',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.luxuryWhite.withValues(alpha: 0.5),
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
