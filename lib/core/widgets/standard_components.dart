import 'package:flutter/material.dart';
import 'package:billionaires_social/core/theme/unified_theme_system.dart';

/// Standardized UI components for consistent design
class StandardComponents {
  
  /// Standard app bar
  static PreferredSizeWidget standardAppBar({
    required String title,
    List<Widget>? actions,
    Widget? leading,
    bool centerTitle = true,
    Color? backgroundColor,
    Color? foregroundColor,
    double elevation = 0,
  }) {
    return AppBar(
      title: Text(
        title,
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: foregroundColor ?? UnifiedThemeSystem.primaryBlack,
        ),
      ),
      centerTitle: centerTitle,
      backgroundColor: backgroundColor ?? UnifiedThemeSystem.primaryWhite,
      foregroundColor: foregroundColor ?? UnifiedThemeSystem.primaryBlack,
      elevation: elevation,
      leading: leading,
      actions: actions,
    );
  }

  /// Standard button
  static Widget standardButton({
    required String text,
    required VoidCallback? onPressed,
    bool isLoading = false,
    bool isOutlined = false,
    Color? backgroundColor,
    Color? textColor,
    double? width,
    EdgeInsetsGeometry? padding,
    IconData? icon,
  }) {
    final button = isOutlined
        ? OutlinedButton.icon(
            onPressed: isLoading ? null : onPressed,
            icon: isLoading
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : (icon != null ? Icon(icon, size: 18) : const SizedBox.shrink()),
            label: Text(text),
            style: OutlinedButton.styleFrom(
              foregroundColor: textColor,
              side: BorderSide(color: textColor ?? UnifiedThemeSystem.primaryBlack),
              padding: padding ?? const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          )
        : ElevatedButton.icon(
            onPressed: isLoading ? null : onPressed,
            icon: isLoading
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : (icon != null ? Icon(icon, size: 18) : const SizedBox.shrink()),
            label: Text(text),
            style: ElevatedButton.styleFrom(
              backgroundColor: backgroundColor ?? UnifiedThemeSystem.primaryBlack,
              foregroundColor: textColor ?? UnifiedThemeSystem.primaryWhite,
              padding: padding ?? const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          );

    return width != null
        ? SizedBox(width: width, child: button)
        : button;
  }

  /// Standard text field
  static Widget standardTextField({
    required String label,
    String? hint,
    TextEditingController? controller,
    String? Function(String?)? validator,
    bool obscureText = false,
    TextInputType? keyboardType,
    Widget? suffixIcon,
    Widget? prefixIcon,
    int? maxLines,
    bool enabled = true,
    VoidCallback? onTap,
    Function(String)? onChanged,
  }) {
    return TextFormField(
      controller: controller,
      validator: validator,
      obscureText: obscureText,
      keyboardType: keyboardType,
      maxLines: maxLines ?? 1,
      enabled: enabled,
      onTap: onTap,
      onChanged: onChanged,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        suffixIcon: suffixIcon,
        prefixIcon: prefixIcon,
      ),
    );
  }

  /// Standard card
  static Widget standardCard({
    required Widget child,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    Color? color,
    double? elevation,
    VoidCallback? onTap,
  }) {
    final card = Card(
      color: color,
      elevation: elevation ?? 0,
      margin: margin ?? const EdgeInsets.all(8),
      child: Padding(
        padding: padding ?? const EdgeInsets.all(16),
        child: child,
      ),
    );

    return onTap != null
        ? InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(12),
            child: card,
          )
        : card;
  }

  /// Standard avatar
  static Widget standardAvatar({
    required String? imageUrl,
    required String fallbackText,
    double radius = 20,
    Color? backgroundColor,
    Color? textColor,
    VoidCallback? onTap,
    bool showBorder = false,
    Color? borderColor,
    double borderWidth = 2,
  }) {
    final avatar = CircleAvatar(
      radius: radius,
      backgroundColor: backgroundColor ?? UnifiedThemeSystem.neutral300,
      backgroundImage: imageUrl != null ? NetworkImage(imageUrl) : null,
      child: imageUrl == null
          ? Text(
              fallbackText.isNotEmpty ? fallbackText[0].toUpperCase() : '?',
              style: TextStyle(
                fontSize: radius * 0.6,
                fontWeight: FontWeight.w600,
                color: textColor ?? UnifiedThemeSystem.primaryBlack,
              ),
            )
          : null,
    );

    final avatarWithBorder = showBorder
        ? Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: borderColor ?? UnifiedThemeSystem.primaryGold,
                width: borderWidth,
              ),
            ),
            child: avatar,
          )
        : avatar;

    return onTap != null
        ? GestureDetector(
            onTap: onTap,
            child: avatarWithBorder,
          )
        : avatarWithBorder;
  }

  /// Standard chip
  static Widget standardChip({
    required String label,
    bool isSelected = false,
    VoidCallback? onTap,
    Color? selectedColor,
    Color? unselectedColor,
    Color? textColor,
    IconData? icon,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected
              ? (selectedColor ?? UnifiedThemeSystem.primaryBlack)
              : (unselectedColor ?? UnifiedThemeSystem.neutral100),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected
                ? (selectedColor ?? UnifiedThemeSystem.primaryBlack)
                : UnifiedThemeSystem.neutral300,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon != null) ...[
              Icon(
                icon,
                size: 16,
                color: isSelected
                    ? (textColor ?? UnifiedThemeSystem.primaryWhite)
                    : UnifiedThemeSystem.neutral600,
              ),
              const SizedBox(width: 4),
            ],
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: isSelected
                    ? (textColor ?? UnifiedThemeSystem.primaryWhite)
                    : UnifiedThemeSystem.neutral700,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Standard bottom sheet
  static Future<T?> showStandardBottomSheet<T>({
    required BuildContext context,
    required Widget child,
    bool isDismissible = true,
    bool enableDrag = true,
    double? height,
  }) {
    return showModalBottomSheet<T>(
      context: context,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: height,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: UnifiedThemeSystem.neutral300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Flexible(child: child),
          ],
        ),
      ),
    );
  }

  /// Standard dialog
  static Future<T?> showStandardDialog<T>({
    required BuildContext context,
    required String title,
    required Widget content,
    List<Widget>? actions,
    bool barrierDismissible = true,
  }) {
    return showDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) => AlertDialog(
        title: Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: content,
        actions: actions,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  /// Standard snackbar
  static void showStandardSnackBar({
    required BuildContext context,
    required String message,
    SnackBarType type = SnackBarType.info,
    Duration duration = const Duration(seconds: 3),
    String? actionLabel,
    VoidCallback? onActionPressed,
  }) {
    Color backgroundColor;
    Color textColor = UnifiedThemeSystem.primaryWhite;
    IconData icon;

    switch (type) {
      case SnackBarType.success:
        backgroundColor = UnifiedThemeSystem.success;
        icon = Icons.check_circle;
        break;
      case SnackBarType.error:
        backgroundColor = UnifiedThemeSystem.error;
        icon = Icons.error;
        break;
      case SnackBarType.warning:
        backgroundColor = UnifiedThemeSystem.warning;
        icon = Icons.warning;
        break;
      case SnackBarType.info:
        backgroundColor = UnifiedThemeSystem.info;
        icon = Icons.info;
        break;
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(icon, color: textColor, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: TextStyle(
                  color: textColor,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: backgroundColor,
        duration: duration,
        action: actionLabel != null
            ? SnackBarAction(
                label: actionLabel,
                textColor: textColor,
                onPressed: onActionPressed ?? () {},
              )
            : null,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// Standard empty state
  static Widget standardEmptyState({
    required String title,
    required String description,
    IconData? icon,
    String? actionText,
    VoidCallback? onActionPressed,
  }) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon ?? Icons.inbox_outlined,
              size: 64,
              color: UnifiedThemeSystem.neutral400,
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: UnifiedThemeSystem.neutral700,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              description,
              style: const TextStyle(
                fontSize: 14,
                color: UnifiedThemeSystem.neutral500,
              ),
              textAlign: TextAlign.center,
            ),
            if (actionText != null && onActionPressed != null) ...[
              const SizedBox(height: 24),
              standardButton(
                text: actionText,
                onPressed: onActionPressed,
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// SnackBar types
enum SnackBarType {
  success,
  error,
  warning,
  info,
}
