import 'dart:async';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

/// A widget that provides performance optimizations for child widgets
class PerformanceOptimizedWidget extends StatelessWidget {
  final Widget child;
  final bool addRepaintBoundary;
  final bool addAutomaticKeepAlive;
  final bool enableSemantics;
  final String? semanticsLabel;
  final bool optimizeRebuilds;

  const PerformanceOptimizedWidget({
    super.key,
    required this.child,
    this.addRepaintBoundary = true,
    this.addAutomaticKeepAlive = false,
    this.enableSemantics = true,
    this.semanticsLabel,
    this.optimizeRebuilds = true,
  });

  @override
  Widget build(BuildContext context) {
    Widget widget = child;

    // Add repaint boundary to prevent unnecessary repaints
    if (addRepaintBoundary) {
      widget = RepaintBoundary(child: widget);
    }

    // Add automatic keep alive for expensive widgets
    if (addAutomaticKeepAlive) {
      widget = AutomaticKeepAlive(child: widget);
    }

    // Add semantics for accessibility
    if (enableSemantics && semanticsLabel != null) {
      widget = Semantics(label: semanticsLabel, child: widget);
    }

    return widget;
  }
}

/// A StatefulWidget that automatically manages performance optimizations
abstract class OptimizedStatefulWidget extends StatefulWidget {
  const OptimizedStatefulWidget({super.key});

  @override
  OptimizedState createState();
}

abstract class OptimizedState<T extends OptimizedStatefulWidget>
    extends State<T>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => shouldKeepAlive;

  /// Override this to control whether the widget should be kept alive
  bool get shouldKeepAlive => false;

  /// Override this to control whether to add repaint boundaries
  bool get addRepaintBoundary => true;

  /// Override this to provide the optimized build method
  Widget buildOptimized(BuildContext context);

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    Widget widget = buildOptimized(context);

    if (addRepaintBoundary) {
      widget = RepaintBoundary(child: widget);
    }

    return widget;
  }
}

/// A mixin that provides performance optimization utilities
mixin PerformanceOptimizationMixin<T extends StatefulWidget> on State<T> {
  /// Debounce function calls to prevent excessive rebuilds
  void debounce(
    VoidCallback callback, {
    Duration delay = const Duration(milliseconds: 300),
  }) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(delay, callback);
  }

  Timer? _debounceTimer;

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }

  /// Throttle function calls to limit execution frequency
  void throttle(
    VoidCallback callback, {
    Duration interval = const Duration(milliseconds: 100),
  }) {
    if (_lastThrottleTime == null ||
        DateTime.now().difference(_lastThrottleTime!) >= interval) {
      _lastThrottleTime = DateTime.now();
      callback();
    }
  }

  DateTime? _lastThrottleTime;

  /// Check if the widget should rebuild based on data changes
  bool shouldRebuild(dynamic oldData, dynamic newData) {
    return oldData != newData;
  }

  /// Batch multiple state updates into a single rebuild
  void batchStateUpdates(VoidCallback updates) {
    if (!mounted) return;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(updates);
      }
    });
  }
}

/// A widget that measures and reports performance metrics
class PerformanceMeasuredWidget extends StatefulWidget {
  final Widget child;
  final String? name;
  final Function(Duration buildTime)? onBuildComplete;
  final bool enableMeasurement;

  const PerformanceMeasuredWidget({
    super.key,
    required this.child,
    this.name,
    this.onBuildComplete,
    this.enableMeasurement = true,
  });

  @override
  State<PerformanceMeasuredWidget> createState() =>
      _PerformanceMeasuredWidgetState();
}

class _PerformanceMeasuredWidgetState extends State<PerformanceMeasuredWidget> {
  late Stopwatch _stopwatch;

  @override
  void initState() {
    super.initState();
    _stopwatch = Stopwatch();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.enableMeasurement) {
      return widget.child;
    }

    _stopwatch.reset();
    _stopwatch.start();

    return LayoutBuilder(
      builder: (context, constraints) {
        final child = widget.child;

        WidgetsBinding.instance.addPostFrameCallback((_) {
          _stopwatch.stop();
          final buildTime = _stopwatch.elapsed;

          if (buildTime.inMilliseconds > 16) {
            // More than one frame
            debugPrint(
              '⚠️ Slow widget build: ${widget.name ?? 'Unknown'} took ${buildTime.inMilliseconds}ms',
            );
          }

          widget.onBuildComplete?.call(buildTime);
        });

        return child;
      },
    );
  }
}

/// A widget that provides lazy loading capabilities
class LazyLoadWidget extends StatefulWidget {
  final Widget Function() builder;
  final Widget? placeholder;
  final bool preload;
  final Duration delay;

  const LazyLoadWidget({
    super.key,
    required this.builder,
    this.placeholder,
    this.preload = false,
    this.delay = const Duration(milliseconds: 100),
  });

  @override
  State<LazyLoadWidget> createState() => _LazyLoadWidgetState();
}

class _LazyLoadWidgetState extends State<LazyLoadWidget> {
  Widget? _cachedWidget;
  bool _isLoaded = false;

  @override
  void initState() {
    super.initState();
    if (widget.preload) {
      _loadWidget();
    }
  }

  void _loadWidget() {
    if (_isLoaded) return;

    Future.delayed(widget.delay, () {
      if (mounted && !_isLoaded) {
        setState(() {
          _cachedWidget = widget.builder();
          _isLoaded = true;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (!_isLoaded) {
      _loadWidget();
      return widget.placeholder ?? const SizedBox.shrink();
    }

    return _cachedWidget ?? const SizedBox.shrink();
  }
}

/// A widget that implements efficient image loading with memory management
class OptimizedImage extends StatefulWidget {
  final String? imageUrl;
  final String? assetPath;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;
  final bool enableMemoryCache;
  final bool enableDiskCache;

  const OptimizedImage({
    super.key,
    this.imageUrl,
    this.assetPath,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
    this.enableMemoryCache = true,
    this.enableDiskCache = true,
  }) : assert(
         imageUrl != null || assetPath != null,
         'Either imageUrl or assetPath must be provided',
       );

  @override
  State<OptimizedImage> createState() => _OptimizedImageState();
}

class _OptimizedImageState extends State<OptimizedImage> {
  @override
  Widget build(BuildContext context) {
    if (widget.assetPath != null) {
      return Image.asset(
        widget.assetPath!,
        width: widget.width,
        height: widget.height,
        fit: widget.fit,
        cacheWidth: widget.width?.round(),
        cacheHeight: widget.height?.round(),
        errorBuilder: widget.errorWidget != null
            ? (context, error, stackTrace) => widget.errorWidget!
            : null,
      );
    }

    if (widget.imageUrl != null) {
      // Use CachedNetworkImage for better performance and caching
      return CachedNetworkImage(
        imageUrl: widget.imageUrl!,
        width: widget.width,
        height: widget.height,
        fit: widget.fit,
        memCacheWidth: widget.width?.round(),
        memCacheHeight: widget.height?.round(),
        useOldImageOnUrlChange: true,
        fadeInDuration: const Duration(milliseconds: 200),
        fadeOutDuration: const Duration(milliseconds: 100),
        placeholder: widget.placeholder != null
            ? (context, url) => widget.placeholder!
            : null,
        errorWidget: widget.errorWidget != null
            ? (context, url, error) => widget.errorWidget!
            : null,
      );
    }

    return widget.errorWidget ?? const SizedBox.shrink();
  }
}

/// Performance utilities
class PerformanceUtils {
  /// Measure the execution time of a function
  static Future<T> measureAsync<T>(
    String name,
    Future<T> Function() function,
  ) async {
    final stopwatch = Stopwatch()..start();
    try {
      final result = await function();
      stopwatch.stop();
      debugPrint('⏱️ $name took ${stopwatch.elapsedMilliseconds}ms');
      return result;
    } catch (e) {
      stopwatch.stop();
      debugPrint(
        '⏱️ $name failed after ${stopwatch.elapsedMilliseconds}ms: $e',
      );
      rethrow;
    }
  }

  /// Measure the execution time of a synchronous function
  static T measureSync<T>(String name, T Function() function) {
    final stopwatch = Stopwatch()..start();
    try {
      final result = function();
      stopwatch.stop();
      debugPrint('⏱️ $name took ${stopwatch.elapsedMilliseconds}ms');
      return result;
    } catch (e) {
      stopwatch.stop();
      debugPrint(
        '⏱️ $name failed after ${stopwatch.elapsedMilliseconds}ms: $e',
      );
      rethrow;
    }
  }

  /// Check if a rebuild is necessary based on data comparison
  static bool shouldRebuild<T>(T? oldData, T? newData) {
    if (oldData == null && newData == null) return false;
    if (oldData == null || newData == null) return true;
    return oldData != newData;
  }
}
