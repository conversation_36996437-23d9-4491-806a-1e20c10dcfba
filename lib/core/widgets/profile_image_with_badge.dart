import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:billionaires_social/core/widgets/billionaire_badge.dart';

class ProfileImageWithBadge extends StatelessWidget {
  final String imageUrl;
  final double size;
  final bool showBillionaireBadge;
  final VoidCallback? onTap;

  const ProfileImageWithBadge({
    super.key,
    required this.imageUrl,
    this.size = 50,
    this.showBillionaireBadge = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    Widget imageWidget = CircleAvatar(
      radius: size / 2,
      backgroundImage: _isValidImageUrl(imageUrl)
          ? CachedNetworkImageProvider(imageUrl)
          : null,
      backgroundColor: Colors.grey.shade200,
      child: !_isValidImageUrl(imageUrl)
          ? Icon(Icons.person, size: size * 0.6, color: Colors.grey.shade600)
          : null,
    );

    if (onTap != null) {
      imageWidget = GestureDetector(onTap: onTap, child: imageWidget);
    }

    if (showBillionaireBadge) {
      return Stack(
        children: [
          imageWidget,
          Positioned(
            top: 0,
            right: 0,
            child: BillionaireBadge(size: size * 0.4, showOverlay: true),
          ),
        ],
      );
    }

    return imageWidget;
  }

  bool _isValidImageUrl(String? url) {
    if (url == null || url.isEmpty) return false;
    return url.startsWith('http://') || url.startsWith('https://');
  }
}
