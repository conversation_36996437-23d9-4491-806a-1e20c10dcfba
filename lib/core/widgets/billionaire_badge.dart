import 'package:flutter/material.dart';

class BillionaireBadge extends StatefulWidget {
  final double size;
  final bool showPulse;
  final bool showOverlay;
  final VoidCallback? onTap;

  const BillionaireBadge({
    super.key,
    this.size = 20,
    this.showPulse = false,
    this.showOverlay = false,
    this.onTap,
  });

  @override
  State<BillionaireBadge> createState() => _BillionaireBadgeState();
}

class _BillionaireBadgeState extends State<BillionaireBadge>
    with SingleTickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    if (widget.showPulse) {
      _pulseController.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final badge = Container(
      width: widget.size,
      height: widget.size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFFFFD700), // Gold
            Color(0xFFFFA500), // Orange
            Color(0xFFFF8C00), // Dark Orange
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFFFFD700).withValues(alpha: 0.5),
            blurRadius: 8,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Center(
        child: CustomPaint(
          size: Size(widget.size * 0.6, widget.size * 0.6),
          painter: CrownPainter(),
        ),
      ),
    );

    Widget badgeWidget = widget.showPulse
        ? AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation.value,
                child: badge,
              );
            },
          )
        : badge;

    if (widget.onTap != null) {
      badgeWidget = GestureDetector(onTap: widget.onTap, child: badgeWidget);
    }

    if (widget.showOverlay) {
      return Positioned(top: 4, right: 4, child: badgeWidget);
    }

    return badgeWidget;
  }
}

class CrownPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill
      ..strokeWidth = 1;

    final path = Path();

    // Crown base
    path.moveTo(size.width * 0.1, size.height * 0.6);
    path.lineTo(size.width * 0.9, size.height * 0.6);
    path.lineTo(size.width * 0.8, size.height * 0.8);
    path.lineTo(size.width * 0.2, size.height * 0.8);
    path.close();

    // Crown jewels
    final jewelPaint = Paint()
      ..color =
          const Color(0xFF8B0000) // Dark red
      ..style = PaintingStyle.fill;

    // Left jewel
    canvas.drawCircle(
      Offset(size.width * 0.25, size.height * 0.5),
      size.width * 0.08,
      jewelPaint,
    );

    // Center jewel
    canvas.drawCircle(
      Offset(size.width * 0.5, size.height * 0.4),
      size.width * 0.1,
      jewelPaint,
    );

    // Right jewel
    canvas.drawCircle(
      Offset(size.width * 0.75, size.height * 0.5),
      size.width * 0.08,
      jewelPaint,
    );

    // Crown spikes
    final spikePaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    // Left spike
    final leftSpike = Path();
    leftSpike.moveTo(size.width * 0.25, size.height * 0.6);
    leftSpike.lineTo(size.width * 0.2, size.height * 0.2);
    leftSpike.lineTo(size.width * 0.3, size.height * 0.3);
    leftSpike.close();
    canvas.drawPath(leftSpike, spikePaint);

    // Center spike
    final centerSpike = Path();
    centerSpike.moveTo(size.width * 0.5, size.height * 0.6);
    centerSpike.lineTo(size.width * 0.45, size.height * 0.1);
    centerSpike.lineTo(size.width * 0.55, size.height * 0.1);
    centerSpike.close();
    canvas.drawPath(centerSpike, spikePaint);

    // Right spike
    final rightSpike = Path();
    rightSpike.moveTo(size.width * 0.75, size.height * 0.6);
    rightSpike.lineTo(size.width * 0.7, size.height * 0.3);
    rightSpike.lineTo(size.width * 0.8, size.height * 0.2);
    rightSpike.close();
    canvas.drawPath(rightSpike, spikePaint);

    // Draw crown base
    canvas.drawPath(path, paint);

    // Add "B" letter
    final textPainter = TextPainter(
      text: const TextSpan(
        text: 'B',
        style: TextStyle(
          color: Color(0xFF8B0000),
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        (size.width - textPainter.width) / 2,
        (size.height - textPainter.height) / 2,
      ),
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
