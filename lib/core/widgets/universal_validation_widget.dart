import 'package:flutter/material.dart';
import 'package:billionaires_social/core/services/universal_testing_service.dart';
import 'package:billionaires_social/core/services/universal_user_role_service.dart';

/// Widget for testing and validating universal behavior
/// Can be embedded in debug screens or used for development testing
class UniversalValidationWidget extends StatefulWidget {
  final String? testUserId;
  final bool showDetailedResults;
  
  const UniversalValidationWidget({
    super.key,
    this.testUserId,
    this.showDetailedResults = true,
  });

  @override
  State<UniversalValidationWidget> createState() => _UniversalValidationWidgetState();
}

class _UniversalValidationWidgetState extends State<UniversalValidationWidget> {
  UniversalTestResults? _testResults;
  bool _isRunningTests = false;
  String? _selectedTestCategory;

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 16),
            _buildTestControls(),
            if (_testResults != null) ...[
              const SizedBox(height: 16),
              _buildTestResults(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        const Icon(Icons.science, color: Colors.blue),
        const SizedBox(width: 8),
        const Text(
          'Universal Behavior Validation',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        if (_testResults != null)
          Icon(
            _testResults!.isSuccess ? Icons.check_circle : Icons.error,
            color: _testResults!.isSuccess ? Colors.green : Colors.red,
          ),
      ],
    );
  }

  Widget _buildTestControls() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _isRunningTests ? null : _runAllTests,
                icon: _isRunningTests 
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.play_arrow),
                label: Text(_isRunningTests ? 'Running Tests...' : 'Run All Tests'),
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton.icon(
              onPressed: _isRunningTests ? null : _runCurrentUserTest,
              icon: const Icon(Icons.person),
              label: const Text('Test Current User'),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          'Current User: ${UniversalUserRoleService.getCurrentUserId() ?? "Not authenticated"}',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildTestResults() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildResultsSummary(),
        const SizedBox(height: 16),
        if (widget.showDetailedResults) _buildDetailedResults(),
      ],
    );
  }

  Widget _buildResultsSummary() {
    final results = _testResults!;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: results.isSuccess ? Colors.green[50] : Colors.red[50],
        border: Border.all(
          color: results.isSuccess ? Colors.green : Colors.red,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                results.isSuccess ? Icons.check_circle : Icons.error,
                color: results.isSuccess ? Colors.green : Colors.red,
              ),
              const SizedBox(width: 8),
              Text(
                results.isSuccess ? 'All Tests Passed' : 'Some Tests Failed',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: results.isSuccess ? Colors.green[800] : Colors.red[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            results.summary,
            style: TextStyle(
              color: results.isSuccess ? Colors.green[700] : Colors.red[700],
            ),
          ),
          if (results.completedAt != null)
            Text(
              'Completed: ${results.completedAt!.toLocal().toString().split('.')[0]}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDetailedResults() {
    final results = _testResults!;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildCategoryFilter(),
        const SizedBox(height: 8),
        _buildTestList(results.passedTests, 'Passed Tests', Colors.green, Icons.check_circle),
        if (results.failedTests.isNotEmpty) ...[
          const SizedBox(height: 8),
          _buildTestList(results.failedTests, 'Failed Tests', Colors.red, Icons.error),
        ],
        if (results.skippedTests.isNotEmpty) ...[
          const SizedBox(height: 8),
          _buildTestList(results.skippedTests, 'Skipped Tests', Colors.orange, Icons.skip_next),
        ],
      ],
    );
  }

  Widget _buildCategoryFilter() {
    final categories = <String>{'All'};
    if (_testResults != null) {
      categories.addAll(_testResults!.passedTests.map((t) => t.category));
      categories.addAll(_testResults!.failedTests.map((t) => t.category));
      categories.addAll(_testResults!.skippedTests.map((t) => t.category));
    }

    return Wrap(
      spacing: 8,
      children: categories.map((category) {
        final isSelected = _selectedTestCategory == category || 
                          (category == 'All' && _selectedTestCategory == null);
        
        return FilterChip(
          label: Text(category),
          selected: isSelected,
          onSelected: (selected) {
            setState(() {
              _selectedTestCategory = selected ? (category == 'All' ? null : category) : null;
            });
          },
        );
      }).toList(),
    );
  }

  Widget _buildTestList(List<TestResult> tests, String title, Color color, IconData icon) {
    final filteredTests = _selectedTestCategory == null 
        ? tests 
        : tests.where((t) => t.category == _selectedTestCategory).toList();
    
    if (filteredTests.isEmpty) return const SizedBox.shrink();
    
    return ExpansionTile(
      leading: Icon(icon, color: color),
      title: Text(
        '$title (${filteredTests.length})',
        style: TextStyle(
          fontWeight: FontWeight.bold,
          color: color,
        ),
      ),
      children: filteredTests.map((test) => ListTile(
        dense: true,
        leading: Icon(icon, size: 16, color: color),
        title: Text(test.category),
        subtitle: Text(test.message),
        trailing: Text(
          '${test.timestamp.hour}:${test.timestamp.minute.toString().padLeft(2, '0')}',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      )).toList(),
    );
  }

  Future<void> _runAllTests() async {
    setState(() {
      _isRunningTests = true;
      _testResults = null;
    });

    try {
      final results = await UniversalTestingService.runUniversalTests(
        testUserId: widget.testUserId,
      );
      
      setState(() {
        _testResults = results;
      });
    } catch (e) {
      setState(() {
        _testResults = UniversalTestResults()
          ..addFailure('Test Framework', 'Failed to run tests: $e');
      });
    } finally {
      setState(() {
        _isRunningTests = false;
      });
    }
  }

  Future<void> _runCurrentUserTest() async {
    final currentUserId = UniversalUserRoleService.getCurrentUserId();
    if (currentUserId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No authenticated user to test')),
      );
      return;
    }

    setState(() {
      _isRunningTests = true;
      _testResults = null;
    });

    try {
      final results = await UniversalTestingService.testUserAccount(currentUserId);
      
      setState(() {
        _testResults = results;
      });
    } catch (e) {
      setState(() {
        _testResults = UniversalTestResults()
          ..addFailure('User Test', 'Failed to test current user: $e');
      });
    } finally {
      setState(() {
        _isRunningTests = false;
      });
    }
  }
}
