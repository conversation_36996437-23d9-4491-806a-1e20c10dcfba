import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';

class PerformanceMonitor extends StatefulWidget {
  final Widget child;
  final String screenName;
  final bool enabled;

  const PerformanceMonitor({
    super.key,
    required this.child,
    required this.screenName,
    this.enabled = kDebugMode,
  });

  @override
  State<PerformanceMonitor> createState() => _PerformanceMonitorState();
}

class _PerformanceMonitorState extends State<PerformanceMonitor> {
  final List<Duration> _frameTimes = [];
  final List<int> _memoryUsage = [];
  late DateTime _startTime;
  int _frameCount = 0;
  double _averageFrameTime = 0;
  int _droppedFrames = 0;

  @override
  void initState() {
    super.initState();
    _startTime = DateTime.now();

    if (widget.enabled) {
      _startMonitoring();
    }
  }

  void _startMonitoring() {
    SchedulerBinding.instance.addPersistentFrameCallback(_onFrame);
  }

  void _onFrame(Duration timestamp) {
    if (!mounted || !widget.enabled) return;

    _frameCount++;

    // Calculate frame time
    if (_frameTimes.isNotEmpty) {
      final frameTime = timestamp - _frameTimes.last;
      _frameTimes.add(frameTime);

      // Keep only last 60 frames for rolling average
      if (_frameTimes.length > 60) {
        _frameTimes.removeAt(0);
      }

      // Calculate average frame time
      final totalTime = _frameTimes.fold<Duration>(
        Duration.zero,
        (sum, time) => sum + time,
      );
      _averageFrameTime = totalTime.inMicroseconds / _frameTimes.length / 1000;

      // Detect dropped frames (>16.67ms for 60fps)
      if (frameTime.inMilliseconds > 16.67) {
        _droppedFrames++;
      }
    } else {
      _frameTimes.add(timestamp);
    }

    // Update memory usage periodically
    if (_frameCount % 60 == 0) {
      _updateMemoryUsage();
    }
  }

  void _updateMemoryUsage() {
    // This is a simplified memory tracking
    // In a real app, you might use more sophisticated memory profiling
    final currentMemory = _getCurrentMemoryUsage();
    _memoryUsage.add(currentMemory);

    // Keep only last 10 measurements
    if (_memoryUsage.length > 10) {
      _memoryUsage.removeAt(0);
    }
  }

  int _getCurrentMemoryUsage() {
    // Simplified memory calculation
    // In production, you'd use platform-specific memory APIs
    return DateTime.now().millisecondsSinceEpoch % 100000;
  }

  @override
  void dispose() {
    // Note: removePersistentFrameCallback doesn't exist, callbacks are automatically removed on dispose
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.enabled) {
      return widget.child;
    }

    return Stack(
      children: [
        widget.child,
        Positioned(
          top: MediaQuery.of(context).padding.top + 10,
          right: 10,
          child: _buildPerformanceOverlay(),
        ),
      ],
    );
  }

  Widget _buildPerformanceOverlay() {
    final fps = _averageFrameTime > 0 ? 1000 / _averageFrameTime : 0;
    final uptime = DateTime.now().difference(_startTime);
    final avgMemory = _memoryUsage.isNotEmpty
        ? _memoryUsage.reduce((a, b) => a + b) / _memoryUsage.length
        : 0;

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            widget.screenName,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'FPS: ${fps.toStringAsFixed(1)}',
            style: TextStyle(
              color: fps < 50 ? Colors.red : Colors.green,
              fontSize: 10,
            ),
          ),
          Text(
            'Frames: $_frameCount',
            style: const TextStyle(color: Colors.white, fontSize: 10),
          ),
          Text(
            'Dropped: $_droppedFrames',
            style: TextStyle(
              color: _droppedFrames > 10 ? Colors.red : Colors.white,
              fontSize: 10,
            ),
          ),
          Text(
            'Uptime: ${uptime.inSeconds}s',
            style: const TextStyle(color: Colors.white, fontSize: 10),
          ),
          Text(
            'Mem: ${avgMemory.toStringAsFixed(0)}',
            style: const TextStyle(color: Colors.white, fontSize: 10),
          ),
        ],
      ),
    );
  }
}

class PerformanceTracker {
  static final Map<String, PerformanceMetrics> _metrics = {};

  static void startTracking(String operation) {
    _metrics[operation] = PerformanceMetrics(
      operation: operation,
      startTime: DateTime.now(),
    );
  }

  static void endTracking(String operation) {
    final metric = _metrics[operation];
    if (metric != null) {
      metric.endTime = DateTime.now();
      metric.duration = metric.endTime!.difference(metric.startTime);

      if (kDebugMode) {
        debugPrint(
          'Performance: $operation took ${metric.duration!.inMilliseconds}ms',
        );
      }
    }
  }

  static void trackMemoryUsage(String operation, int memoryBytes) {
    final metric = _metrics[operation];
    if (metric != null) {
      metric.memoryUsage = memoryBytes;
    }
  }

  static PerformanceMetrics? getMetrics(String operation) {
    return _metrics[operation];
  }

  static Map<String, PerformanceMetrics> getAllMetrics() {
    return Map.from(_metrics);
  }

  static void clearMetrics() {
    _metrics.clear();
  }

  static void printSummary() {
    if (!kDebugMode) return;

    debugPrint('\n=== Performance Summary ===');
    for (final entry in _metrics.entries) {
      final metric = entry.value;
      debugPrint('${entry.key}: ${metric.duration?.inMilliseconds ?? 0}ms');
    }
    debugPrint('========================\n');
  }
}

class PerformanceMetrics {
  final String operation;
  final DateTime startTime;
  DateTime? endTime;
  Duration? duration;
  int? memoryUsage;

  PerformanceMetrics({
    required this.operation,
    required this.startTime,
    this.endTime,
    this.duration,
    this.memoryUsage,
  });

  bool get isCompleted => endTime != null;

  @override
  String toString() {
    return 'PerformanceMetrics(operation: $operation, duration: ${duration?.inMilliseconds}ms, memory: ${memoryUsage ?? 0} bytes)';
  }
}

// Widget to wrap expensive operations
class PerformanceWrapper extends StatelessWidget {
  final Widget child;
  final String operationName;
  final VoidCallback? onBuildStart;
  final VoidCallback? onBuildEnd;

  const PerformanceWrapper({
    super.key,
    required this.child,
    required this.operationName,
    this.onBuildStart,
    this.onBuildEnd,
  });

  @override
  Widget build(BuildContext context) {
    if (kDebugMode) {
      onBuildStart?.call();
      PerformanceTracker.startTracking(operationName);
    }

    final widget = child;

    if (kDebugMode) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        PerformanceTracker.endTracking(operationName);
        onBuildEnd?.call();
      });
    }

    return widget;
  }
}
