import 'package:flutter/material.dart';
import 'package:billionaires_social/core/app_theme.dart';

class CustomBackButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? iconColor;
  final double size;
  final bool showText;

  const CustomBackButton({
    super.key,
    this.onPressed,
    this.backgroundColor,
    this.iconColor,
    this.size = 40,
    this.showText = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? AppTheme.accentColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(size / 2),
        border: Border.all(
          color: AppTheme.accentColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed ?? () => Navigator.of(context).pop(),
          borderRadius: BorderRadius.circular(size / 2),
          child: SizedBox(
            width: size,
            height: size,
            child: Icon(
              Icons.arrow_back_ios_new,
              color: iconColor ?? AppTheme.accentColor,
              size: size * 0.4,
            ),
          ),
        ),
      ),
    );
  }
}

class AppBarBackButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final String? title;
  final List<Widget>? actions;
  final bool showBackButton;

  const AppBarBackButton({
    super.key,
    this.onPressed,
    this.title,
    this.actions,
    this.showBackButton = true,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: showBackButton
          ? CustomBackButton(onPressed: onPressed, size: 36)
          : null,
      title: title != null
          ? Text(
              title!,
              style: AppTheme.fontStyles.title.copyWith(
                color: AppTheme.accentColor,
                fontWeight: FontWeight.bold,
              ),
            )
          : null,
      actions: actions,
      centerTitle: true,
    );
  }
}

class FloatingBackButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? iconColor;

  const FloatingBackButton({
    super.key,
    this.onPressed,
    this.backgroundColor,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 10,
      left: 16,
      child: CustomBackButton(
        onPressed: onPressed,
        backgroundColor: backgroundColor,
        iconColor: iconColor,
        size: 44,
      ),
    );
  }
}
