import 'package:flutter/material.dart';
import 'package:billionaires_social/core/services/security_service.dart';

/// Security middleware for form validation and input sanitization
/// Provides secure input handling across the application
class SecurityMiddleware {
  static final SecurityService _securityService = SecurityService();

  /// Secure text form field with built-in validation and sanitization
  static Widget secureTextFormField({
    required String label,
    required InputType inputType,
    required Function(String) onChanged,
    String? initialValue,
    bool obscureText = false,
    int? maxLength,
    int? minLength,
    bool allowSpecialChars = false,
    String? Function(String?)? additionalValidator,
    TextEditingController? controller,
    Widget? suffixIcon,
    Widget? prefixIcon,
    bool enabled = true,
    TextInputType? keyboardType,
  }) {
    return TextFormField(
      controller: controller,
      initialValue: controller == null ? initialValue : null,
      obscureText: obscureText,
      enabled: enabled,
      keyboardType: keyboardType ?? _getKeyboardType(inputType),
      maxLength: maxLength,
      decoration: InputDecoration(
        labelText: label,
        suffixIcon: suffixIcon,
        prefixIcon: prefixIcon,
        border: const OutlineInputBorder(),
        counterText: '', // Hide character counter
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return '$label is required';
        }

        // Security validation
        final securityResult = _securityService.validateInput(
          value,
          inputType,
          maxLength: maxLength,
          minLength: minLength,
          allowSpecialChars: allowSpecialChars,
        );

        if (!securityResult.isValid) {
          return securityResult.message;
        }

        // Additional custom validation
        if (additionalValidator != null) {
          return additionalValidator(value);
        }

        return null;
      },
      onChanged: (value) {
        // Sanitize input before passing to callback
        final sanitized = _securityService.sanitizeInput(value);
        onChanged(sanitized);
      },
    );
  }

  /// Secure password field with strength indicator
  static Widget securePasswordField({
    required String label,
    required Function(String) onChanged,
    required Function(double) onStrengthChanged,
    TextEditingController? controller,
    String? Function(String?)? additionalValidator,
    bool showStrengthIndicator = true,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        secureTextFormField(
          label: label,
          inputType: InputType.password,
          onChanged: (value) {
            onChanged(value);
            if (showStrengthIndicator) {
              final strength = _calculatePasswordStrength(value);
              onStrengthChanged(strength);
            }
          },
          obscureText: true,
          controller: controller,
          additionalValidator: additionalValidator,
          suffixIcon: const Icon(Icons.visibility_off),
        ),
        if (showStrengthIndicator) ...[
          const SizedBox(height: 8),
          _PasswordStrengthIndicator(),
        ],
      ],
    );
  }

  /// Secure form wrapper with rate limiting
  static Widget secureForm({
    required Widget child,
    required String formIdentifier,
    required VoidCallback onSubmit,
    GlobalKey<FormState>? formKey,
    int maxSubmissions = 5,
    Duration rateLimitWindow = const Duration(minutes: 1),
  }) {
    return Builder(
      builder: (context) {
        return Form(
          key: formKey,
          child: Column(
            children: [
              child,
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  // Check rate limiting
                  if (!_securityService.checkRateLimit(
                    formIdentifier,
                    maxRequests: maxSubmissions,
                    window: rateLimitWindow,
                  )) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Too many submissions. Please wait before trying again.'),
                        backgroundColor: Colors.red,
                      ),
                    );
                    return;
                  }

                  // Validate form
                  if (formKey?.currentState?.validate() ?? true) {
                    onSubmit();
                  }
                },
                child: const Text('Submit'),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Secure file upload with validation
  static Future<bool> validateFileUpload({
    required String fileName,
    required int fileSize,
    required List<String> allowedExtensions,
    int maxSizeBytes = 10 * 1024 * 1024, // 10MB default
  }) async {
    // Check file extension
    final extension = fileName.split('.').last.toLowerCase();
    if (!allowedExtensions.contains(extension)) {
      return false;
    }

    // Check file size
    if (fileSize > maxSizeBytes) {
      return false;
    }

    // Check for suspicious file names
    final suspiciousPatterns = [
      'script',
      'executable',
      'malware',
      '.exe',
      '.bat',
      '.cmd',
      '.scr',
    ];

    for (final pattern in suspiciousPatterns) {
      if (fileName.toLowerCase().contains(pattern)) {
        return false;
      }
    }

    return true;
  }

  /// Secure URL validation and sanitization
  static String? validateAndSanitizeUrl(String url) {
    final validation = _securityService.validateInput(url, InputType.url);
    if (!validation.isValid) {
      return null;
    }

    try {
      final uri = Uri.parse(url);
      
      // Only allow HTTP and HTTPS
      if (!['http', 'https'].contains(uri.scheme)) {
        return null;
      }

      // Block suspicious domains
      final suspiciousDomains = [
        'malware.com',
        'phishing.com',
        'suspicious.net',
      ];

      if (suspiciousDomains.any((domain) => uri.host.contains(domain))) {
        return null;
      }

      return uri.toString();
    } catch (e) {
      return null;
    }
  }

  /// Generate secure session token
  static String generateSessionToken() {
    return _securityService.generateSecureToken(length: 64);
  }

  /// Hash sensitive data
  static String hashSensitiveData(String data) {
    return _securityService.hashData(data);
  }

  /// Verify hashed data
  static bool verifySensitiveData(String data, String hash) {
    return _securityService.verifyHash(data, hash);
  }

  // Private helper methods
  static TextInputType _getKeyboardType(InputType inputType) {
    switch (inputType) {
      case InputType.email:
        return TextInputType.emailAddress;
      case InputType.phoneNumber:
        return TextInputType.phone;
      case InputType.url:
        return TextInputType.url;
      case InputType.password:
        return TextInputType.visiblePassword;
      default:
        return TextInputType.text;
    }
  }

  static double _calculatePasswordStrength(String password) {
    if (password.isEmpty) return 0.0;

    double strength = 0.0;
    
    // Length check
    if (password.length >= 8) strength += 0.2;
    if (password.length >= 12) strength += 0.1;
    
    // Character variety checks
    if (password.contains(RegExp(r'[a-z]'))) strength += 0.2;
    if (password.contains(RegExp(r'[A-Z]'))) strength += 0.2;
    if (password.contains(RegExp(r'[0-9]'))) strength += 0.2;
    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) strength += 0.2;
    
    // Bonus for very long passwords
    if (password.length >= 16) strength += 0.1;
    
    return strength.clamp(0.0, 1.0);
  }
}

/// Password strength indicator widget
class _PasswordStrengthIndicator extends StatefulWidget {
  @override
  State<_PasswordStrengthIndicator> createState() => _PasswordStrengthIndicatorState();
}

class _PasswordStrengthIndicatorState extends State<_PasswordStrengthIndicator> {
  double _strength = 0.0;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        LinearProgressIndicator(
          value: _strength,
          backgroundColor: Colors.grey.shade300,
          valueColor: AlwaysStoppedAnimation<Color>(_getStrengthColor()),
        ),
        const SizedBox(height: 4),
        Text(
          _getStrengthText(),
          style: TextStyle(
            fontSize: 12,
            color: _getStrengthColor(),
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Color _getStrengthColor() {
    if (_strength < 0.3) return Colors.red;
    if (_strength < 0.6) return Colors.orange;
    if (_strength < 0.8) return Colors.yellow.shade700;
    return Colors.green;
  }

  String _getStrengthText() {
    if (_strength < 0.3) return 'Weak password';
    if (_strength < 0.6) return 'Fair password';
    if (_strength < 0.8) return 'Good password';
    return 'Strong password';
  }

  void updateStrength(double strength) {
    setState(() {
      _strength = strength;
    });
  }
}

/// Security context provider for tracking security events
class SecurityContextProvider extends InheritedWidget {
  final String userId;
  final String sessionId;
  final DateTime sessionStart;

  const SecurityContextProvider({
    super.key,
    required this.userId,
    required this.sessionId,
    required this.sessionStart,
    required super.child,
  });

  static SecurityContextProvider? of(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<SecurityContextProvider>();
  }

  @override
  bool updateShouldNotify(SecurityContextProvider oldWidget) {
    return userId != oldWidget.userId || 
           sessionId != oldWidget.sessionId ||
           sessionStart != oldWidget.sessionStart;
  }
}
