import 'package:billionaires_social/features/feed/services/feed_service.dart';
import 'package:billionaires_social/features/feed/services/content_discovery_service.dart';
import 'package:billionaires_social/features/feed/services/content_access_service.dart';
import 'package:billionaires_social/features/feed/services/discovery_analytics_service.dart';
import 'package:billionaires_social/features/feed/providers/feed_filter_provider.dart';
import 'package:billionaires_social/features/stories/services/story_service.dart';
import 'package:billionaires_social/features/stories/services/story_settings_service.dart';
import 'package:billionaires_social/features/profile/services/profile_service.dart';
import 'package:billionaires_social/features/profile/services/close_friends_group_service.dart';
import 'package:billionaires_social/features/profile/services/close_friends_service.dart';
import 'package:billionaires_social/features/profile/services/followers_service.dart';
import 'package:billionaires_social/features/profile/services/account_management_service.dart';
import 'package:billionaires_social/features/settings/services/settings_service.dart';
import 'package:billionaires_social/features/settings/services/chat_privacy_service.dart';
import 'package:billionaires_social/features/messaging/services/chat_service.dart';
import 'package:billionaires_social/features/messaging/services/chat_settings_service.dart';
import 'package:billionaires_social/features/settings/services/theme_service.dart';
import 'package:billionaires_social/core/services/firebase_service.dart';
import 'package:billionaires_social/core/services/performance_service.dart';
import 'package:billionaires_social/core/services/universal_account_service.dart';
import 'package:billionaires_social/core/services/universal_validation_service.dart';
import 'package:billionaires_social/core/services/user_data_consistency_service.dart';
import 'package:billionaires_social/core/services/analytics_service.dart';
import 'package:billionaires_social/core/services/analytics/unified_analytics_service.dart';
import 'package:billionaires_social/core/services/analytics/user_analytics_service.dart';
import 'package:billionaires_social/core/services/analytics/content_analytics_service.dart';
import 'package:billionaires_social/core/services/analytics/performance_analytics_service.dart';
import 'package:billionaires_social/core/services/cache_service.dart';
import 'package:billionaires_social/core/services/version_control_service.dart';
import 'package:billionaires_social/core/services/trending_service.dart';
import 'package:billionaires_social/features/events/services/events_service.dart';
import 'package:billionaires_social/features/notifications/services/notification_service.dart';
import 'package:billionaires_social/core/services/content_moderation_service.dart';
import 'package:billionaires_social/core/services/error_handling_service.dart';
import 'package:billionaires_social/core/services/image_optimization_service.dart';
import 'package:billionaires_social/core/services/performance_monitoring_service.dart';
import 'package:billionaires_social/core/services/memory_management_service.dart';
import 'package:billionaires_social/core/services/security_service.dart';
import 'package:billionaires_social/core/services/input_validation_service.dart';
import 'package:billionaires_social/core/services/rate_limiting_service.dart';

import 'package:billionaires_social/features/stories/services/story_interaction_service.dart';
import 'package:billionaires_social/features/auth/services/verification_service.dart';
import 'package:get_it/get_it.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';

final GetIt getIt = GetIt.instance;

Future<void> setupServiceLocator() async {
  // Core Services
  if (!getIt.isRegistered<FirebaseService>()) {
    getIt.registerSingleton<FirebaseService>(FirebaseService());
  }
  if (!getIt.isRegistered<PerformanceService>()) {
    getIt.registerSingleton<PerformanceService>(PerformanceService());
  }
  if (!getIt.isRegistered<AnalyticsService>()) {
    getIt.registerSingleton<AnalyticsService>(AnalyticsService());
  }
  if (!getIt.isRegistered<UnifiedAnalyticsService>()) {
    getIt.registerSingleton<UnifiedAnalyticsService>(UnifiedAnalyticsService());
  }
  if (!getIt.isRegistered<UserAnalyticsService>()) {
    getIt.registerSingleton<UserAnalyticsService>(UserAnalyticsService());
  }
  if (!getIt.isRegistered<ContentAnalyticsService>()) {
    getIt.registerSingleton<ContentAnalyticsService>(ContentAnalyticsService());
  }
  if (!getIt.isRegistered<PerformanceAnalyticsService>()) {
    getIt.registerSingleton<PerformanceAnalyticsService>(
      PerformanceAnalyticsService(),
    );
  }
  if (!getIt.isRegistered<CacheService>()) {
    getIt.registerSingleton<CacheService>(CacheService());
  }
  if (!getIt.isRegistered<VersionControlService>()) {
    getIt.registerSingleton<VersionControlService>(VersionControlService());
  }
  if (!getIt.isRegistered<ErrorHandlingService>()) {
    getIt.registerSingleton<ErrorHandlingService>(ErrorHandlingService());
  }
  if (!getIt.isRegistered<SecurityService>()) {
    getIt.registerSingleton<SecurityService>(SecurityService());
  }
  if (!getIt.isRegistered<InputValidationService>()) {
    getIt.registerSingleton<InputValidationService>(InputValidationService());
  }
  if (!getIt.isRegistered<RateLimitingService>()) {
    getIt.registerSingleton<RateLimitingService>(RateLimitingService());
  }
  if (!getIt.isRegistered<MemoryManagementService>()) {
    getIt.registerSingleton<MemoryManagementService>(MemoryManagementService());
  }
  if (!getIt.isRegistered<PerformanceMonitoringService>()) {
    getIt.registerSingleton<PerformanceMonitoringService>(
      PerformanceMonitoringService(),
    );
  }
  if (!getIt.isRegistered<ImageOptimizationService>()) {
    getIt.registerSingleton<ImageOptimizationService>(
      ImageOptimizationService(),
    );
  }
  if (!getIt.isRegistered<UniversalAccountService>()) {
    getIt.registerSingleton<UniversalAccountService>(UniversalAccountService());
  }
  if (!getIt.isRegistered<UniversalValidationService>()) {
    getIt.registerSingleton<UniversalValidationService>(
      UniversalValidationService(),
    );
  }
  if (!getIt.isRegistered<UserDataConsistencyService>()) {
    getIt.registerSingleton<UserDataConsistencyService>(
      UserDataConsistencyService(),
    );
  }

  // Feature Services
  if (!getIt.isRegistered<NotificationService>()) {
    getIt.registerSingleton<NotificationService>(NotificationService());
  }
  if (!getIt.isRegistered<FeedService>()) {
    getIt.registerSingleton<FeedService>(FeedService());
  }
  if (!getIt.isRegistered<ContentDiscoveryService>()) {
    getIt.registerSingleton<ContentDiscoveryService>(ContentDiscoveryService());
  }
  if (!getIt.isRegistered<ContentAccessService>()) {
    getIt.registerSingleton<ContentAccessService>(ContentAccessService());
  }
  if (!getIt.isRegistered<DiscoveryAnalyticsService>()) {
    getIt.registerSingleton<DiscoveryAnalyticsService>(
      DiscoveryAnalyticsService(),
    );
  }
  if (!getIt.isRegistered<FeedFilterNotifier>()) {
    getIt.registerSingleton<FeedFilterNotifier>(FeedFilterNotifier());
  }
  if (!getIt.isRegistered<StoryService>()) {
    getIt.registerSingleton<StoryService>(StoryService());
  }
  if (!getIt.isRegistered<StorySettingsService>()) {
    getIt.registerSingleton<StorySettingsService>(StorySettingsService());
  }
  if (!getIt.isRegistered<ProfileService>()) {
    getIt.registerSingleton<ProfileService>(ProfileService());
  }
  if (!getIt.isRegistered<CloseFriendsGroupService>()) {
    getIt.registerSingleton<CloseFriendsGroupService>(
      CloseFriendsGroupService(),
    );
  }
  if (!getIt.isRegistered<CloseFriendsService>()) {
    getIt.registerSingleton<CloseFriendsService>(CloseFriendsService());
  }
  if (!getIt.isRegistered<FollowersService>()) {
    getIt.registerSingleton<FollowersService>(FollowersService());
  }
  if (!getIt.isRegistered<AccountManagementService>()) {
    getIt.registerSingleton<AccountManagementService>(
      AccountManagementService(),
    );
  }
  if (!getIt.isRegistered<SettingsService>()) {
    getIt.registerSingleton<SettingsService>(SettingsService());
  }
  if (!getIt.isRegistered<ChatPrivacyService>()) {
    getIt.registerSingleton<ChatPrivacyService>(ChatPrivacyService());
  }
  if (!getIt.isRegistered<ChatService>()) {
    getIt.registerSingleton<ChatService>(ChatService());
  }
  if (!getIt.isRegistered<ChatSettingsService>()) {
    getIt.registerSingleton<ChatSettingsService>(ChatSettingsService());
  }
  if (!getIt.isRegistered<ThemeService>()) {
    getIt.registerSingleton<ThemeService>(ThemeService());
    debugPrint('[ServiceLocator] ThemeService registered');
  }
  if (!getIt.isRegistered<EventsService>()) {
    getIt.registerSingleton<EventsService>(EventsService());
  }
  if (!getIt.isRegistered<TrendingService>()) {
    getIt.registerSingleton<TrendingService>(TrendingService());
  }
  if (!getIt.isRegistered<ContentModerationService>()) {
    getIt.registerSingleton<ContentModerationService>(
      ContentModerationService(),
    );
  }
  if (!getIt.isRegistered<StoryInteractionService>()) {
    getIt.registerSingleton<StoryInteractionService>(StoryInteractionService());
  }
  if (!getIt.isRegistered<VerificationService>()) {
    getIt.registerSingleton<VerificationService>(VerificationService());
  }

  // Initialize services
  await getIt<PerformanceService>().initialize();
  await getIt<AnalyticsService>().initialize();
  await getIt<CacheService>().initialize();
  await getIt<VersionControlService>().initialize();
  await getIt<NotificationService>().initialize();

  // Initialize Firebase Crashlytics
  await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);

  // Set up error handling for Flutter errors
  FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterFatalError;

  // Set up error handling for async errors
  PlatformDispatcher.instance.onError = (error, stack) {
    FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
    return true;
  };
}
