enum UserType { regular, billionaire, verified, influencer }

class MockUser {
  final String id;
  final String username;
  final String name;
  final String bio;
  final String avatarUrl;
  final UserType userType;
  final bool isVerified;
  final bool isFollowed;
  final bool isBillionaire;

  const MockUser({
    required this.id,
    required this.username,
    required this.name,
    required this.bio,
    required this.avatarUrl,
    this.userType = UserType.regular,
    this.isVerified = false,
    this.isFollowed = false,
    this.isBillionaire = false,
  });
}

final List<MockUser> mockUsers = [
  const MockUser(
    id: 'user_1',
    username: 'monica_bellucci',
    name: '<PERSON>',
    bio: 'Actress and model. True beauty is timeless.',
    avatarUrl: 'https://i.pravatar.cc/150?u=monica_bellucci',
    userType: UserType.verified,
    isVerified: true,
  ),
  const Mock<PERSON>ser(
    id: 'user_2',
    username: 'sophia_loren',
    name: '<PERSON>',
    bio: 'There is a fountain of youth: it is your mind.',
    avatarUrl: 'https://i.pravatar.cc/150?u=sophia_loren',
    userType: UserType.verified,
    isVerified: true,
  ),
  const MockUser(
    id: 'user_3',
    username: 'gianni_versace',
    name: 'Gianni Versace',
    bio: 'I want to be a designer of my time.',
    avatarUrl: 'https://i.pravatar.cc/150?u=gianni_versace',
    userType: UserType.billionaire,
    isVerified: true,
    isBillionaire: true,
  ),
  const MockUser(
    id: 'user_4',
    username: 'enzo_ferrari',
    name: 'Enzo Ferrari',
    bio: 'The client is not always right.',
    avatarUrl: 'https://i.pravatar.cc/150?u=enzo_ferrari',
    userType: UserType.billionaire,
    isVerified: true,
    isBillionaire: true,
  ),
  const MockUser(
    id: 'user_5',
    username: 'leonardo_davinci',
    name: 'Leonardo da Vinci',
    bio: 'Simplicity is the ultimate sophistication.',
    avatarUrl: 'https://i.pravatar.cc/150?u=leonardo_davinci',
    userType: UserType.verified,
    isVerified: true,
  ),
  const MockUser(
    id: 'user_6',
    username: 'elite_investor',
    name: 'Alexander Rothschild',
    bio: 'Building empires, one investment at a time.',
    avatarUrl: 'https://i.pravatar.cc/150?u=elite_investor',
    userType: UserType.billionaire,
    isVerified: true,
    isBillionaire: true,
  ),
  const MockUser(
    id: 'user_7',
    username: 'tech_tycoon',
    name: 'Marcus Chen',
    bio: 'Innovation is the key to the future.',
    avatarUrl: 'https://i.pravatar.cc/150?u=tech_tycoon',
    userType: UserType.billionaire,
    isVerified: true,
    isBillionaire: true,
  ),
  const MockUser(
    id: 'user_8',
    username: 'luxury_lifestyle',
    name: 'Isabella Martinez',
    bio: 'Living life in the fast lane.',
    avatarUrl: 'https://i.pravatar.cc/150?u=luxury_lifestyle',
    userType: UserType.influencer,
    isVerified: true,
  ),
  const MockUser(
    id: 'user_9',
    username: 'art_collector',
    name: 'Victoria Dubois',
    bio: 'Art is the lie that enables us to realize the truth.',
    avatarUrl: 'https://i.pravatar.cc/150?u=art_collector',
    userType: UserType.verified,
    isVerified: true,
  ),
  const MockUser(
    id: 'user_10',
    username: 'yacht_owner',
    name: 'Sebastian von Berg',
    bio: 'The sea is calling, and I must go.',
    avatarUrl: 'https://i.pravatar.cc/150?u=yacht_owner',
    userType: UserType.billionaire,
    isVerified: true,
    isBillionaire: true,
  ),
];
