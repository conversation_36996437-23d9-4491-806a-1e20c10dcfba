import 'dart:async';
import 'package:flutter/material.dart';

/// Mixin for proper stream subscription management
/// Automatically cancels all subscriptions when widget is disposed
mixin StreamManagementMixin<T extends StatefulWidget> on State<T> {
  final List<StreamSubscription> _subscriptions = [];

  /// Add a stream subscription to be managed
  void addSubscription(StreamSubscription subscription) {
    _subscriptions.add(subscription);
  }

  /// Add multiple stream subscriptions at once
  void addSubscriptions(List<StreamSubscription> subscriptions) {
    _subscriptions.addAll(subscriptions);
  }

  /// Remove a specific subscription (useful for conditional subscriptions)
  void removeSubscription(StreamSubscription subscription) {
    subscription.cancel();
    _subscriptions.remove(subscription);
  }

  /// Get the count of active subscriptions (for debugging)
  int get activeSubscriptionCount => _subscriptions.length;

  /// Cancel all subscriptions and clear the list
  void cancelAllSubscriptions() {
    for (final subscription in _subscriptions) {
      subscription.cancel();
    }
    _subscriptions.clear();
  }

  @override
  void dispose() {
    cancelAllSubscriptions();
    super.dispose();
  }
}

/// Extension to make stream subscription management easier
extension StreamSubscriptionManagement on Stream {
  /// Listen and automatically add to management
  StreamSubscription listenManaged(
    StreamManagementMixin mixin,
    void Function(dynamic) onData, {
    Function? onError,
    void Function()? onDone,
    bool? cancelOnError,
  }) {
    final subscription = listen(
      onData,
      onError: onError,
      onDone: onDone,
      cancelOnError: cancelOnError,
    );
    mixin.addSubscription(subscription);
    return subscription;
  }
}

/// Mixin for animation controller management
mixin AnimationManagementMixin<T extends StatefulWidget>
    on State<T>, TickerProviderStateMixin<T> {
  final List<AnimationController> _controllers = [];

  /// Create and manage an animation controller
  AnimationController createAnimationController({
    required Duration duration,
    Duration? reverseDuration,
    String? debugLabel,
    double? value,
    double lowerBound = 0.0,
    double upperBound = 1.0,
    AnimationBehavior animationBehavior = AnimationBehavior.normal,
  }) {
    final controller = AnimationController(
      duration: duration,
      reverseDuration: reverseDuration,
      debugLabel: debugLabel,
      value: value,
      lowerBound: lowerBound,
      upperBound: upperBound,
      animationBehavior: animationBehavior,
      vsync: this,
    );
    _controllers.add(controller);
    return controller;
  }

  /// Remove a specific controller (useful for conditional animations)
  void removeAnimationController(AnimationController controller) {
    controller.dispose();
    _controllers.remove(controller);
  }

  /// Get the count of active controllers (for debugging)
  int get activeControllerCount => _controllers.length;

  /// Dispose all controllers and clear the list
  void disposeAllControllers() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    _controllers.clear();
  }

  @override
  void dispose() {
    disposeAllControllers();
    super.dispose();
  }
}

/// Combined mixin for both stream and animation management
mixin ResourceManagementMixin<T extends StatefulWidget>
    on State<T>, TickerProviderStateMixin<T>
    implements StreamManagementMixin<T>, AnimationManagementMixin<T> {
  @override
  final List<StreamSubscription> _subscriptions = [];
  @override
  final List<AnimationController> _controllers = [];

  // StreamManagementMixin implementation
  @override
  void addSubscription(StreamSubscription subscription) {
    _subscriptions.add(subscription);
  }

  @override
  void addSubscriptions(List<StreamSubscription> subscriptions) {
    _subscriptions.addAll(subscriptions);
  }

  @override
  void removeSubscription(StreamSubscription subscription) {
    subscription.cancel();
    _subscriptions.remove(subscription);
  }

  @override
  int get activeSubscriptionCount => _subscriptions.length;

  @override
  void cancelAllSubscriptions() {
    for (final subscription in _subscriptions) {
      subscription.cancel();
    }
    _subscriptions.clear();
  }

  // AnimationManagementMixin implementation
  @override
  AnimationController createAnimationController({
    required Duration duration,
    Duration? reverseDuration,
    String? debugLabel,
    double? value,
    double lowerBound = 0.0,
    double upperBound = 1.0,
    AnimationBehavior animationBehavior = AnimationBehavior.normal,
  }) {
    final controller = AnimationController(
      duration: duration,
      reverseDuration: reverseDuration,
      debugLabel: debugLabel,
      value: value,
      lowerBound: lowerBound,
      upperBound: upperBound,
      animationBehavior: animationBehavior,
      vsync: this,
    );
    _controllers.add(controller);
    return controller;
  }

  @override
  void removeAnimationController(AnimationController controller) {
    controller.dispose();
    _controllers.remove(controller);
  }

  @override
  int get activeControllerCount => _controllers.length;

  @override
  void disposeAllControllers() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    _controllers.clear();
  }

  @override
  void dispose() {
    cancelAllSubscriptions();
    disposeAllControllers();
    super.dispose();
  }
}
