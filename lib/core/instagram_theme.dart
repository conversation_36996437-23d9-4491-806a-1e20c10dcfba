import 'package:flutter/material.dart';

/// Instagram-inspired design system constants and utilities
class InstagramTheme {
  // Instagram Color Palette
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  static const Color instagramBlue = Color(0xFF0095F6);
  static const Color instagramRed = Color(0xFFED4956);
  static const Color borderGray = Color(0xFFDBDBDB);
  static const Color textSecondary = Color(0xFF8E8E8E);
  static const Color backgroundGray = Color(0xFFFAFAFA);
  static const Color goldAccent = Color(0xFFFFD700); // For luxury features
  
  // Story gradient colors (Instagram's signature gradients)
  static const List<Color> storyGradient = [
    Color(0xFFF58529), // Orange
    Color(0xFFDD2A7B), // Pink
    Color(0xFF8134AF), // Purple
    Color(0xFF515BD4), // Blue
  ];
  
  static const List<Color> storyGradientViewed = [
    Color(0xFFDBDBDB), // Light gray for viewed stories
    Color(0xFFC7C7C7), // Slightly darker gray
  ];
  
  static const List<Color> closeFriendsGradient = [
    Color(0xFF1BA345), // Green
    Color(0xFF4CBB17), // Light green
  ];

  // Typography (Instagram uses SF Pro Display/Text on iOS, Roboto on Android)
  static const String primaryFontFamily = 'SF Pro Display';
  static const String textFontFamily = 'SF Pro Text';
  
  // Spacing constants (Instagram's spacing system)
  static const double spacingXS = 4.0;
  static const double spacingS = 8.0;
  static const double spacingM = 12.0;
  static const double spacingL = 16.0;
  static const double spacingXL = 20.0;
  static const double spacingXXL = 24.0;
  
  // Border radius
  static const double radiusS = 4.0;
  static const double radiusM = 8.0;
  static const double radiusL = 12.0;
  static const double radiusXL = 16.0;
  
  // Story specific constants
  static const double storyAvatarSize = 66.0;
  static const double storyRingWidth = 2.0;
  static const double storySpacing = 16.0;
  
  // Post card constants
  static const double postCardRadius = 0.0; // Instagram uses no radius
  static const double postImageAspectRatio = 1.0; // Square by default
  static const double postActionButtonSize = 24.0;
  
  // Text styles matching Instagram
  static const TextStyle usernameStyle = TextStyle(
    fontFamily: textFontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w600,
    color: black,
  );
  
  static const TextStyle captionStyle = TextStyle(
    fontFamily: textFontFamily,
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: black,
  );
  
  static const TextStyle timestampStyle = TextStyle(
    fontFamily: textFontFamily,
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: textSecondary,
  );
  
  static const TextStyle buttonTextStyle = TextStyle(
    fontFamily: textFontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w600,
    color: white,
  );
  
  static const TextStyle linkStyle = TextStyle(
    fontFamily: textFontFamily,
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: instagramBlue,
  );

  // Shadow styles
  static const BoxShadow subtleShadow = BoxShadow(
    color: Color(0x0A000000),
    blurRadius: 1,
    offset: Offset(0, 1),
  );
  
  static const BoxShadow cardShadow = BoxShadow(
    color: Color(0x0F000000),
    blurRadius: 8,
    offset: Offset(0, 2),
  );

  // Story ring decoration
  static BoxDecoration storyRingDecoration({
    required bool hasUnviewedStories,
    bool isCloseFriends = false,
  }) {
    if (!hasUnviewedStories) {
      return BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: borderGray,
          width: storyRingWidth,
        ),
      );
    }
    
    return BoxDecoration(
      shape: BoxShape.circle,
      gradient: LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: isCloseFriends ? closeFriendsGradient : storyGradient,
      ),
    );
  }

  // Instagram-style button
  static ButtonStyle instagramButtonStyle({
    Color? backgroundColor,
    Color? textColor,
    EdgeInsets? padding,
  }) {
    return ElevatedButton.styleFrom(
      backgroundColor: backgroundColor ?? instagramBlue,
      foregroundColor: textColor ?? white,
      elevation: 0,
      shadowColor: Colors.transparent,
      padding: padding ?? const EdgeInsets.symmetric(
        horizontal: spacingL,
        vertical: spacingM,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(radiusM),
      ),
      textStyle: buttonTextStyle,
    );
  }

  // Instagram-style text button
  static ButtonStyle instagramTextButtonStyle({Color? textColor}) {
    return TextButton.styleFrom(
      foregroundColor: textColor ?? instagramBlue,
      padding: const EdgeInsets.symmetric(
        horizontal: spacingM,
        vertical: spacingS,
      ),
      textStyle: linkStyle,
    );
  }

  // Post card decoration
  static BoxDecoration postCardDecoration() {
    return const BoxDecoration(
      color: white,
      border: Border(
        bottom: BorderSide(
          color: borderGray,
          width: 0.5,
        ),
      ),
    );
  }

  // Input decoration (Instagram style)
  static InputDecoration instagramInputDecoration({
    required String hintText,
    Widget? suffixIcon,
    bool filled = true,
  }) {
    return InputDecoration(
      hintText: hintText,
      hintStyle: const TextStyle(
        color: textSecondary,
        fontSize: 14,
        fontFamily: textFontFamily,
      ),
      filled: filled,
      fillColor: backgroundGray,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(radiusM),
        borderSide: const BorderSide(color: borderGray),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(radiusM),
        borderSide: const BorderSide(color: borderGray),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(radiusM),
        borderSide: const BorderSide(color: instagramBlue, width: 2),
      ),
      suffixIcon: suffixIcon,
      contentPadding: const EdgeInsets.symmetric(
        horizontal: spacingL,
        vertical: spacingM,
      ),
    );
  }
}
