import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:billionaires_social/features/explore/screens/explore_main_screen.dart';
import 'package:billionaires_social/features/feed/screens/unified_feed_screen.dart';
import 'package:billionaires_social/features/profile/screens/main_profile_screen.dart';
import 'package:billionaires_social/features/reels/screens/reels_screen.dart';

import 'package:billionaires_social/features/debug/screens/debug_tools_screen.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/core/services/analytics_service.dart';
import 'package:billionaires_social/core/services/universal_navigation_service.dart';

// Import CreationMode enum from story creation screen
enum CreationMode { post, story, reel, live }

class MainNavigation extends StatefulWidget {
  const MainNavigation({super.key});

  @override
  State<MainNavigation> createState() => _MainNavigationState();
}

class _MainNavigationState extends State<MainNavigation> {
  int _selectedIndex = 0;
  final AnalyticsService _analyticsService = getIt<AnalyticsService>();

  final List<Widget> _screens = [
    const UnifiedFeedScreen(),
    const ExploreMainScreen(),
    const ReelsScreen(),
    const MainProfileScreen(),
  ];

  final List<String> _screenNames = ['Feed', 'Explore', 'Reels', 'Profile'];

  void _onItemTapped(int index) {
    if (index == 2) {
      _showCreateOptions();
      return;
    }
    setState(() {
      _selectedIndex = index > 2 ? index - 1 : index;
    });

    // Track screen view
    _analyticsService.logScreenView(
      screenName: _screenNames[_selectedIndex],
      screenClass: 'MainNavigation',
    );
  }

  void _showCreateOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => _buildCreateOptionsModal(),
    );
  }

  Widget _buildCreateOptionsModal() {
    return Container(
      height: MediaQuery.of(context).size.height * 0.4,
      decoration: const BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[600],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          const SizedBox(height: 20),

          // Title
          const Text(
            'Create',
            style: TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 30),

          // Creation options
          Expanded(
            child: GridView.count(
              crossAxisCount: 2,
              padding: const EdgeInsets.symmetric(horizontal: 20),
              mainAxisSpacing: 20,
              crossAxisSpacing: 20,
              children: [
                _buildCreateOption(
                  icon: FontAwesomeIcons.penToSquare,
                  title: 'Post',
                  subtitle: 'Share a photo or video',
                  onTap: () => _navigateToCamera(CreationMode.post),
                ),
                _buildCreateOption(
                  icon: FontAwesomeIcons.solidCircle,
                  title: 'Story',
                  subtitle: 'Share a moment',
                  onTap: () => _navigateToCamera(CreationMode.story),
                ),
                _buildCreateOption(
                  icon: FontAwesomeIcons.play,
                  title: 'Reel',
                  subtitle: 'Create a short video',
                  onTap: () => _navigateToCamera(CreationMode.reel),
                ),
                _buildCreateOption(
                  icon: FontAwesomeIcons.towerBroadcast,
                  title: 'Live',
                  subtitle: 'Go live now',
                  onTap: () => _navigateToCamera(CreationMode.live),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildCreateOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.grey[900],
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.grey[700]!),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: Colors.white, size: 32),
            const SizedBox(height: 12),
            Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(color: Colors.grey[400], fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToCamera(CreationMode mode) {
    Navigator.pop(context); // Close modal

    // Use Universal Navigation Service for content creation
    ContentCreationType contentType;
    switch (mode) {
      case CreationMode.post:
        contentType = ContentCreationType.post;
        break;
      case CreationMode.story:
        contentType = ContentCreationType.story;
        break;
      case CreationMode.reel:
        contentType = ContentCreationType.reel;
        break;
      case CreationMode.live:
        contentType = ContentCreationType.live;
        break;
    }

    // Universal navigation with permission checks and limits validation
    UniversalNavigationService.navigateToContentCreation(context, contentType);

    // Track analytics using the correct method
    _analyticsService.logEventSafely(
      eventName: 'content_creation_started',
      parameters: {
        'creation_type': mode.name,
        'source': 'main_navigation',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  @override
  void initState() {
    super.initState();
    // Track initial screen view
    _analyticsService.logScreenView(
      screenName: _screenNames[_selectedIndex],
      screenClass: 'MainNavigation',
    );
  }

  @override
  Widget build(BuildContext context) {
    final currentUser = FirebaseAuth.instance.currentUser;
    final isAuthenticated = currentUser != null;

    return Scaffold(
      body: IndexedStack(index: _selectedIndex, children: _screens),
      floatingActionButton: isAuthenticated && kDebugMode
          ? FloatingActionButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const DebugToolsScreen(),
                  ),
                );
              },
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              heroTag: 'debug_fab',
              child: const Icon(Icons.bug_report),
            )
          : null,
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _selectedIndex,
        onTap: _onItemTapped,
        type: BottomNavigationBarType.fixed,
        backgroundColor:
            Theme.of(context).bottomAppBarTheme.color ??
            Theme.of(context).primaryColor,
        selectedItemColor: Theme.of(context).colorScheme.secondary,
        unselectedItemColor: Theme.of(context).colorScheme.onSurface,
        showSelectedLabels: false,
        showUnselectedLabels: false,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(FontAwesomeIcons.house),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(FontAwesomeIcons.magnifyingGlass),
            label: 'Explore',
          ),
          BottomNavigationBarItem(
            icon: Icon(FontAwesomeIcons.plus),
            label: 'Create',
          ),
          BottomNavigationBarItem(
            icon: Icon(FontAwesomeIcons.play),
            label: 'Reels',
          ),
          BottomNavigationBarItem(
            icon: Icon(FontAwesomeIcons.circleUser),
            label: 'Profile',
          ),
        ],
      ),
    );
  }
}
