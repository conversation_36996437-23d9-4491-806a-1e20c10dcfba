import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/profile/providers/profile_provider.dart';
import 'package:billionaires_social/features/profile/widgets/profile_header.dart';
import 'package:billionaires_social/features/profile/widgets/profile_post_grid.dart';
import 'package:billionaires_social/features/profile/widgets/tagged_posts_tab.dart';
import 'package:billionaires_social/core/services/universal_user_role_service.dart';
import 'package:billionaires_social/features/profile/services/profile_service.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/core/app_theme.dart';

class UserProfileScreen extends ConsumerWidget {
  final String userId;

  const UserProfileScreen({super.key, required this.userId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Universal logic: determine if this is current user
    final isCurrentUser = UniversalUserRoleService.isCurrentUser(userId);

    debugPrint(
      '🔍 UserProfileScreen: userId=$userId, isCurrentUser=$isCurrentUser',
    );

    final profileState = ref.watch(profileProvider(userId));

    return Scaffold(
      appBar: AppBar(
        title: profileState.when(
          data: (profile) =>
              Text(profile.username, style: AppTheme.fontStyles.title),
          loading: () => const Text(''),
          error: (e, s) => const Text('Error'),
        ),
      ),
      body: profileState.when(
        data: (profile) {
          return FutureBuilder<List<Post>>(
            future: ProfileService().getUserPostsWithPinnedStatus(userId),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              }
              if (snapshot.hasError) {
                return Center(
                  child: Text('Error loading posts: ${snapshot.error}'),
                );
              }
              final posts = snapshot.data ?? [];

              return DefaultTabController(
                length: 3,
                child: NestedScrollView(
                  headerSliverBuilder: (context, innerBoxIsScrolled) {
                    return [
                      SliverToBoxAdapter(
                        child: ProfileHeader(
                          userId: profile.id,
                          isCurrentUser: isCurrentUser, // Universal logic
                        ),
                      ),
                      SliverPersistentHeader(
                        delegate: _SliverTabBarDelegate(
                          const TabBar(
                            indicatorColor: AppTheme.accentColor,
                            indicatorWeight: 1,
                            tabs: [
                              Tab(icon: Icon(Icons.grid_on)),
                              Tab(icon: Icon(Icons.bookmark_border)),
                              Tab(icon: Icon(Icons.person_pin_outlined)),
                            ],
                          ),
                        ),
                        pinned: true,
                      ),
                    ];
                  },
                  body: TabBarView(
                    children: [
                      ProfilePostGrid(
                        posts: posts,
                        isCurrentUser: isCurrentUser, // Universal logic
                        userProfile: profile,
                      ),
                      const Center(
                        child: Text('Saved Posts (Not Implemented)'),
                      ),
                      TaggedPostsTab(
                        userId: userId,
                        isCurrentUser: isCurrentUser,
                      ), // Universal logic
                    ],
                  ),
                ),
              );
            },
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(child: Text('Error: $error')),
      ),
    );
  }
}

class _SliverTabBarDelegate extends SliverPersistentHeaderDelegate {
  _SliverTabBarDelegate(this._tabBar);

  final TabBar _tabBar;

  @override
  double get minExtent => _tabBar.preferredSize.height;
  @override
  double get maxExtent => _tabBar.preferredSize.height;

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: _tabBar,
    );
  }

  @override
  bool shouldRebuild(_SliverTabBarDelegate oldDelegate) {
    return false;
  }
}
