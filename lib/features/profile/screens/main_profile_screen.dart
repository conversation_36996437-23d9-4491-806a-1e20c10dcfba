import 'package:billionaires_social/features/profile/providers/profile_provider.dart';
import 'package:billionaires_social/features/profile/providers/saved_content_provider.dart';
import 'package:billionaires_social/features/profile/models/profile_model.dart';
import 'package:billionaires_social/features/profile/services/profile_service.dart';
import 'package:billionaires_social/features/settings/screens/main_settings_screen.dart';
import 'package:billionaires_social/features/profile/screens/account_switcher_screen.dart';
import 'package:billionaires_social/features/creation/screens/post_creation_screen.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:billionaires_social/features/profile/widgets/profile_header.dart';
import 'package:billionaires_social/features/profile/widgets/profile_post_grid.dart';
import 'package:billionaires_social/features/profile/widgets/tagged_posts_tab.dart';
import 'package:billionaires_social/features/profile/widgets/profile_empty_states.dart';
import 'package:billionaires_social/features/profile/widgets/enhanced_loading_animations.dart';
import 'package:billionaires_social/features/profile/widgets/archived_posts_tab.dart';
import 'package:billionaires_social/features/admin/screens/admin_main_screen.dart';
import '../widgets/account_indicator.dart';

class MainProfileScreen extends ConsumerWidget {
  final String? userId;
  const MainProfileScreen({super.key, this.userId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isCurrentUser = userId == null;

    return Scaffold(
      appBar: AppBar(
        title: isCurrentUser
            ? Row(
                children: [
                  const AccountIndicator(
                    accountType: ProfileAccountType.personal,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'My Profile',
                    style: AppTheme.fontStyles.title.copyWith(
                      color: AppTheme.luxuryWhite,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              )
            : null,
        actions: [
          if (isCurrentUser)
            IconButton(
              icon: const Icon(Icons.add_box_outlined),
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(builder: (_) => const PostCreationScreen()),
                );
              },
            ),
          ref
              .watch(userProfileProvider)
              .maybeWhen(
                data: (profile) => profile.isAdmin
                    ? IconButton(
                        icon: const Icon(FontAwesomeIcons.userShield),
                        tooltip: 'Admin Panel',
                        onPressed: () {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (_) => const AdminMainScreen(),
                            ),
                          );
                        },
                      )
                    : const SizedBox.shrink(),
                orElse: () => const SizedBox.shrink(),
              ),
          IconButton(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (_) => const AccountSwitcherScreen(),
                ),
              );
            },
            icon: const FaIcon(FontAwesomeIcons.users),
          ),
          IconButton(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(builder: (_) => const MainSettingsScreen()),
              );
            },
            icon: const FaIcon(FontAwesomeIcons.gear),
          ),
        ],
      ),
      body: ref
          .watch(userProfileProvider)
          .when(
            data: (profile) {
              // Run migration to fix posts without status field (one-time fix)
              if (isCurrentUser) {
                WidgetsBinding.instance.addPostFrameCallback((_) async {
                  try {
                    final profileService = getIt<ProfileService>();
                    await profileService.fixPostsWithoutStatus(profile.id);
                  } catch (e) {
                    debugPrint('❌ Migration error: $e');
                  }
                });
              }

              final postsAsync = ref.watch(
                userPostsWithPinnedStatusStreamProvider(profile.id),
              );

              return postsAsync.when(
                data: (posts) {
                  return DefaultTabController(
                    length: 4,
                    child: NestedScrollView(
                      headerSliverBuilder: (context, innerBoxIsScrolled) {
                        return [
                          SliverToBoxAdapter(
                            child: ProfileHeader(
                              userId: profile.id,
                              isCurrentUser: isCurrentUser,
                            ),
                          ),
                          SliverPersistentHeader(
                            delegate: _SliverTabBarDelegate(
                              TabBar(
                                indicatorColor: Theme.of(
                                  context,
                                ).colorScheme.secondary,
                                indicatorWeight: 1,
                                tabs: const [
                                  Tab(icon: Icon(Icons.grid_on)),
                                  Tab(icon: Icon(Icons.bookmark_border)),
                                  Tab(icon: Icon(Icons.person_pin_outlined)),
                                  Tab(icon: Icon(Icons.archive)),
                                ],
                              ),
                            ),
                            pinned: true,
                          ),
                        ];
                      },
                      body: TabBarView(
                        children: [
                          ProfilePostGrid(
                            posts: posts,
                            isCurrentUser: isCurrentUser,
                            userProfile: profile,
                          ),
                          _buildSavedPostsTab(),
                          _buildTaggedPostsTab(isCurrentUser),
                          if (isCurrentUser)
                            _buildArchivedPostsTab(profile)
                          else
                            const Center(
                              child: Text('Archived posts are private'),
                            ),
                        ],
                      ),
                    ),
                  );
                },
                loading: () => EnhancedLoadingAnimations.postsGridSkeleton(),
                error: (error, stack) =>
                    Center(child: Text('Error loading posts: $error')),
              );
            },
            loading: () => EnhancedLoadingAnimations.profileHeaderSkeleton(),
            error: (error, stack) => Center(child: Text('Error: $error')),
          ),
      floatingActionButton: isCurrentUser
          ? FloatingActionButton(
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(builder: (_) => const PostCreationScreen()),
                );
              },
              backgroundColor: Theme.of(context).colorScheme.secondary,
              foregroundColor: Theme.of(context).colorScheme.onSecondary,
              child: const Icon(Icons.add),
            )
          : null,
    );
  }

  Widget _buildSavedPostsTab() {
    return Consumer(
      builder: (context, ref, child) {
        final savedPostsAsync = ref.watch(savedContentProvider);

        return savedPostsAsync.when(
          data: (posts) {
            if (posts.isEmpty) {
              return ProfileEmptyStates.savedPosts(
                isCurrentUser:
                    true, // Saved posts are only visible to current user
              );
            }

            return GridView.builder(
              padding: const EdgeInsets.all(2),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: 2,
                mainAxisSpacing: 2,
                childAspectRatio: 1,
              ),
              itemCount: posts.length,
              itemBuilder: (context, index) {
                final post = posts[index];
                return GestureDetector(
                  onTap: () {
                    // Navigate to post detail or open post viewer
                    // You can implement this based on your app's navigation
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(4),
                      child: Image.network(
                        post.mediaUrl,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: Colors.grey[300],
                            child: const Icon(
                              Icons.broken_image,
                              color: Colors.grey,
                              size: 40,
                            ),
                          );
                        },
                        loadingBuilder: (context, child, loadingProgress) {
                          if (loadingProgress == null) return child;
                          return Container(
                            color: Colors.grey[200],
                            child: const Center(
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                );
              },
            );
          },
          loading: () => EnhancedLoadingAnimations.postsGridSkeleton(),
          error: (error, stack) => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                Text(
                  'Error loading saved posts',
                  style: TextStyle(fontSize: 16, color: Colors.red[700]),
                ),
                const SizedBox(height: 8),
                Text(
                  error.toString(),
                  style: const TextStyle(color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTaggedPostsTab(bool isCurrentUser) {
    return Consumer(
      builder: (context, ref, child) {
        final profile = ref.watch(userProfileProvider).valueOrNull;
        if (profile == null) {
          return const Center(child: CircularProgressIndicator());
        }

        return TaggedPostsTab(userId: profile.id, isCurrentUser: isCurrentUser);
      },
    );
  }

  Widget _buildArchivedPostsTab(ProfileModel profile) {
    return ArchivedPostsTab(userProfile: profile);
  }
}

class _SliverTabBarDelegate extends SliverPersistentHeaderDelegate {
  _SliverTabBarDelegate(this._tabBar);

  final TabBar _tabBar;

  @override
  double get minExtent => _tabBar.preferredSize.height;
  @override
  double get maxExtent => _tabBar.preferredSize.height;

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: _tabBar,
    );
  }

  @override
  bool shouldRebuild(_SliverTabBarDelegate oldDelegate) {
    return false;
  }
}
