import 'package:billionaires_social/features/profile/models/close_friends_group_model.dart';
import 'package:billionaires_social/features/profile/providers/close_friends_groups_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/profile/models/profile_model.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/features/profile/services/profile_service.dart';

class CloseFriendsScreen extends ConsumerWidget {
  const CloseFriendsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final groupsAsync = ref.watch(closeFriendsGroupsNotifierProvider);
    return Scaffold(
      appBar: AppBar(title: const Text('Close Friends Groups')),
      body: groupsAsync.when(
        data: (groups) => groups.isEmpty
            ? const Center(child: Text('No groups yet. Tap + to add one.'))
            : ListView.builder(
                itemCount: groups.length,
                itemBuilder: (context, i) {
                  final group = groups[i];
                  return ListTile(
                    leading: Text(
                      group.emoji,
                      style: const TextStyle(fontSize: 28),
                    ),
                    title: Text(group.name),
                    subtitle: Text(
                      '${group.memberIds.length} members • Story: ${group.isStoryVisible ? "Visible" : "Hidden"}',
                    ),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () => _showGroupModal(context, ref, group),
                  );
                },
              ),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (e, s) => Center(child: Text('Error: $e')),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showCreateGroupModal(context, ref),
        tooltip: 'Add Group',
        child: const Icon(Icons.add),
      ),
    );
  }

  void _showCreateGroupModal(BuildContext context, WidgetRef ref) {
    final nameController = TextEditingController();
    String emoji = '⭐️';
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) {
        return Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom + 16,
            left: 16,
            right: 16,
            top: 24,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  GestureDetector(
                    onTap: () async {
                      // TODO: Emoji picker
                    },
                    child: Text(emoji, style: const TextStyle(fontSize: 32)),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextField(
                      controller: nameController,
                      decoration: const InputDecoration(
                        labelText: 'Group Name',
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () async {
                  if (nameController.text.trim().isEmpty) return;

                  final navigator = Navigator.of(context);
                  await ref
                      .read(closeFriendsGroupsNotifierProvider.notifier)
                      .createGroup(
                        name: nameController.text.trim(),
                        emoji: emoji,
                      );
                  navigator.pop();
                },
                child: const Text('Create Group'),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showGroupModal(
    BuildContext context,
    WidgetRef ref,
    CloseFriendsGroup group,
  ) {
    final nameController = TextEditingController(text: group.name);
    String emoji = group.emoji;
    bool isStoryVisible = group.isStoryVisible;
    final profileService = getIt<ProfileService>();
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) {
        return Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom + 16,
            left: 16,
            right: 16,
            top: 24,
          ),
          child: StatefulBuilder(
            builder: (context, setState) => FutureBuilder<List<ProfileModel>>(
              future: profileService.getFollowers('current_user'),
              builder: (context, snapshot) {
                final followers = snapshot.data ?? [];
                String search = '';
                List<ProfileModel> filtered = followers
                    .where(
                      (f) =>
                          f.name.toLowerCase().contains(search.toLowerCase()) ||
                          f.username.toLowerCase().contains(
                            search.toLowerCase(),
                          ),
                    )
                    .toList();
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        GestureDetector(
                          onTap: () async {
                            // TODO: Emoji picker
                          },
                          child: Text(
                            emoji,
                            style: const TextStyle(fontSize: 32),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextField(
                            controller: nameController,
                            decoration: const InputDecoration(
                              labelText: 'Group Name',
                            ),
                            onChanged: (_) {},
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    SwitchListTile(
                      title: const Text('Story Visibility'),
                      value: isStoryVisible,
                      onChanged: (val) => setState(() => isStoryVisible = val),
                    ),
                    const SizedBox(height: 8),
                    // Member management UI
                    TextField(
                      decoration: const InputDecoration(
                        labelText: 'Search followers',
                        prefixIcon: Icon(Icons.search),
                      ),
                      onChanged: (val) => setState(() => search = val),
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      height: 180,
                      child: snapshot.connectionState == ConnectionState.waiting
                          ? const Center(child: CircularProgressIndicator())
                          : ListView.builder(
                              itemCount: filtered.length,
                              itemBuilder: (context, i) {
                                final user = filtered[i];
                                final isMember = group.memberIds.contains(
                                  user.id,
                                );
                                return CheckboxListTile(
                                  value: isMember,
                                  title: Text(user.name),
                                  subtitle: Text('@${user.username}'),
                                  secondary: CircleAvatar(
                                    backgroundImage: NetworkImage(
                                      user.profilePictureUrl,
                                    ),
                                  ),
                                  onChanged: (checked) async {
                                    if (checked == true) {
                                      await ref
                                          .read(
                                            closeFriendsGroupsNotifierProvider
                                                .notifier,
                                          )
                                          .addMember(group.id, user.id);
                                    } else {
                                      await ref
                                          .read(
                                            closeFriendsGroupsNotifierProvider
                                                .notifier,
                                          )
                                          .removeMember(group.id, user.id);
                                    }
                                    setState(() {});
                                  },
                                );
                              },
                            ),
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: () async {
                        final navigator = Navigator.of(context);
                        final updated = group.copyWith(
                          name: nameController.text.trim(),
                          emoji: emoji,
                          isStoryVisible: isStoryVisible,
                        );
                        await ref
                            .read(closeFriendsGroupsNotifierProvider.notifier)
                            .updateGroup(updated);
                        navigator.pop();
                      },
                      child: const Text('Save Changes'),
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                      ),
                      onPressed: () async {
                        final navigator = Navigator.of(context);
                        await ref
                            .read(closeFriendsGroupsNotifierProvider.notifier)
                            .deleteGroup(group.id);
                        navigator.pop();
                      },
                      child: const Text(
                        'Delete Group',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        );
      },
    );
  }
}
