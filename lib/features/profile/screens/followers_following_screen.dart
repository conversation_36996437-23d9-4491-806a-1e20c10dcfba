import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/features/profile/models/follower_model.dart'
    as profile;
import 'package:billionaires_social/features/profile/providers/followers_provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart';
import 'package:billionaires_social/features/auth/providers/auth_provider.dart'
    as auth;
import 'package:billionaires_social/core/utils/dialog_utils.dart';

class FollowersFollowingScreen extends ConsumerStatefulWidget {
  final int initialTab;
  const FollowersFollowingScreen({super.key, this.initialTab = 0});

  @override
  ConsumerState<FollowersFollowingScreen> createState() =>
      _FollowersFollowingScreenState();
}

class _FollowersFollowingScreenState
    extends ConsumerState<FollowersFollowingScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isSelectionMode = false;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: 2,
      vsync: this,
      initialIndex: widget.initialTab,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final analyticsAsync = ref.watch(followersAnalyticsNotifierProvider);
    final authState = ref.watch(auth.authProvider);
    String? userId;
    authState.when(
      data: (user) => userId = user?.uid,
      loading: () {},
      error: (e, s) {},
    );

    if (userId == null) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    return Scaffold(
      backgroundColor: AppTheme.primaryColor,
      appBar: AppBar(
        backgroundColor: AppTheme.primaryColor,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(Icons.arrow_back, color: AppTheme.accentColor),
        ),
        title: Text(
          'Followers & Following',
          style: AppTheme.fontStyles.title.copyWith(
            color: AppTheme.accentColor,
          ),
        ),
        actions: [
          if (_isSelectionMode) ...[
            IconButton(
              onPressed: () {
                setState(() {
                  _isSelectionMode = false;
                });
                ref
                    .read(followersFilterNotifierProvider.notifier)
                    .clearFilters();
              },
              icon: const Icon(Icons.close, color: AppTheme.accentColor),
            ),
          ] else ...[
            IconButton(
              onPressed: () {
                setState(() {
                  _isSelectionMode = true;
                });
              },
              icon: FaIcon(
                FontAwesomeIcons.listCheck,
                color: AppTheme.accentColor,
              ),
            ),
            IconButton(
              onPressed: () {
                _showFilterModal();
              },
              icon: FaIcon(
                FontAwesomeIcons.filter,
                color: AppTheme.accentColor,
              ),
            ),
          ],
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: AppTheme.accentColor,
          labelColor: AppTheme.accentColor,
          unselectedLabelColor: AppTheme.secondaryAccentColor,
          tabs: const [
            Tab(text: 'Followers'),
            Tab(text: 'Following'),
          ],
        ),
      ),
      body: Column(
        children: [
          // Analytics Summary
          analyticsAsync.when(
            data: (analytics) => _buildAnalyticsSummary(analytics),
            loading: () => const SizedBox(
              height: 120,
              child: Center(child: CircularProgressIndicator()),
            ),
            error: (error, stack) => const SizedBox(
              height: 120,
              child: Center(child: Text('Error loading analytics')),
            ),
          ),

          // Search Bar
          _buildSearchBar(),

          // Selection Mode Actions
          if (_isSelectionMode) _buildSelectionActions(userId!),

          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildFollowersList(userId!),
                _buildFollowingList(userId!),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnalyticsSummary(profile.FollowersAnalytics analytics) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppTheme.accentColor.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Analytics Summary',
            style: AppTheme.fontStyles.subtitle.copyWith(
              color: AppTheme.accentColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildAnalyticsCard(
                  'Total Following',
                  analytics.totalFollowing.toString(),
                  FontAwesomeIcons.users,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildAnalyticsCard(
                  'Not Following Back',
                  analytics.notFollowingBack.toString(),
                  FontAwesomeIcons.userXmark,
                  color: Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildAnalyticsCard(
                  'Verified',
                  analytics.verifiedAccountsFollowed.toString(),
                  FontAwesomeIcons.check,
                  color: Colors.blue,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildAnalyticsCard(
                  'Billionaires',
                  analytics.billionairesFollowed.toString(),
                  FontAwesomeIcons.crown,
                  color: Colors.amber,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppTheme.accentColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                FaIcon(
                  FontAwesomeIcons.venusMars,
                  color: AppTheme.accentColor,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  'Gender Split: ${analytics.genderSplit.malePercentage.toStringAsFixed(0)}% Male / ${analytics.genderSplit.femalePercentage.toStringAsFixed(0)}% Female',
                  style: AppTheme.fontStyles.caption.copyWith(
                    color: AppTheme.accentColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnalyticsCard(
    String title,
    String value,
    IconData icon, {
    Color? color,
  }) {
    return GestureDetector(
      onTap: () {
        final authState = ref.read(auth.authProvider);
        String? userId;
        authState.when(
          data: (user) => userId = user?.uid,
          loading: () {},
          error: (e, s) {},
        );
        if (userId != null) {
          _showAnalyticsDetails(title, value, icon, color, userId!);
        }
      },
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppTheme.accentColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: AppTheme.accentColor.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            FaIcon(icon, color: color ?? AppTheme.accentColor, size: 16),
            const SizedBox(height: 4),
            Text(
              value,
              style: AppTheme.fontStyles.bodyBold.copyWith(
                color: AppTheme.accentColor,
              ),
            ),
            Flexible(
              child: Text(
                title,
                style: AppTheme.fontStyles.caption.copyWith(
                  color: AppTheme.secondaryAccentColor,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(height: 2),
            Flexible(
              child: Text(
                'Tap to view details',
                style: AppTheme.fontStyles.caption.copyWith(
                  color: AppTheme.accentColor.withValues(alpha: 0.6),
                  fontSize: 10,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showAnalyticsDetails(
    String title,
    String value,
    IconData icon,
    Color? color,
    String userId,
  ) {
    final analytics = ref.read(followersAnalyticsNotifierProvider).value;
    if (analytics == null) return;

    String subtitle = '';

    switch (title) {
      case 'Total Following':
        subtitle = 'All accounts you are following';
        break;
      case 'Not Following Back':
        subtitle = 'Accounts you follow who don\'t follow you back';
        break;
      case 'Verified':
        subtitle = 'Verified accounts you follow';
        break;
      case 'Billionaires':
        subtitle = 'Billionaire accounts you follow';
        break;
    }

    showAppDialog(
      context: context,
      title: Text('Analytics Details'),
      content: Column(
        children: [
          Row(
            children: [
              FaIcon(icon, color: color ?? AppTheme.accentColor, size: 24),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: AppTheme.fontStyles.title.copyWith(
                        color: AppTheme.accentColor,
                      ),
                    ),
                    Text(
                      subtitle,
                      style: AppTheme.fontStyles.body.copyWith(
                        color: AppTheme.secondaryAccentColor,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: AppTheme.accentColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  value,
                  style: AppTheme.fontStyles.bodyBold.copyWith(
                    color: AppTheme.accentColor,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Expanded(
            child: Consumer(
              builder: (context, ref, child) {
                final followingAsync = ref.watch(followingProvider(userId));

                return followingAsync.when(
                  data: (following) {
                    List<dynamic> filteredUsers = [];

                    switch (title) {
                      case 'Total Following':
                        filteredUsers = following;
                        break;
                      case 'Not Following Back':
                        filteredUsers = following
                            .where((user) => !user.isFollowingBack)
                            .toList();
                        break;
                      case 'Verified':
                        filteredUsers = following
                            .where((user) => user.isVerified)
                            .toList();
                        break;
                      case 'Billionaires':
                        filteredUsers = following
                            .where(
                              (user) =>
                                  user.accountType ==
                                  profile.AccountType.billionaire,
                            )
                            .toList();
                        break;
                    }

                    if (filteredUsers.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            FaIcon(
                              icon,
                              size: 64,
                              color: AppTheme.secondaryAccentColor.withValues(
                                alpha: 0.5,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'No users found',
                              style: AppTheme.fontStyles.subtitle.copyWith(
                                color: AppTheme.secondaryAccentColor,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Try refreshing the data',
                              style: AppTheme.fontStyles.body.copyWith(
                                color: AppTheme.secondaryAccentColor.withValues(
                                  alpha: 0.7,
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    }

                    return ListView.builder(
                      itemCount: filteredUsers.length,
                      itemBuilder: (context, index) {
                        final user = filteredUsers[index];
                        return _buildUserTileInModal(user);
                      },
                    );
                  },
                  loading: () =>
                      const Center(child: CircularProgressIndicator()),
                  error: (error, stack) => Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        FaIcon(
                          FontAwesomeIcons.triangleExclamation,
                          size: 64,
                          color: Colors.red.withValues(alpha: 0.5),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Error loading data',
                          style: AppTheme.fontStyles.subtitle.copyWith(
                            color: Colors.red,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Please try again',
                          style: AppTheme.fontStyles.body.copyWith(
                            color: AppTheme.secondaryAccentColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 20),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                // Apply filter to main list
                _applyAnalyticsFilter(title);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.accentColor,
                foregroundColor: Colors.white,
              ),
              child: Text(
                'Apply Filter to Main List',
                style: AppTheme.fontStyles.bodyBold.copyWith(
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserTileInModal(dynamic user) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.accentColor.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundImage: CachedNetworkImageProvider(user.profilePictureUrl),
            backgroundColor: AppTheme.luxuryGrey,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        user.name,
                        style: AppTheme.fontStyles.bodyBold.copyWith(
                          color: AppTheme.accentColor,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (user.isVerified)
                      FaIcon(
                        FontAwesomeIcons.check,
                        color: Colors.blue,
                        size: 14,
                      ),
                    if (user.accountType == profile.AccountType.billionaire)
                      FaIcon(
                        FontAwesomeIcons.crown,
                        color: Colors.amber,
                        size: 14,
                      ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  '@${user.username}',
                  style: AppTheme.fontStyles.caption.copyWith(
                    color: AppTheme.secondaryAccentColor,
                  ),
                ),
                if (!user.isFollowingBack) ...[
                  const SizedBox(height: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      'Not following back',
                      style: AppTheme.fontStyles.caption.copyWith(
                        color: Colors.orange,
                        fontSize: 10,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _applyAnalyticsFilter(String title) {
    // Apply the appropriate filter based on the analytics card tapped
    switch (title) {
      case 'Not Following Back':
        ref
            .read(followersFilterNotifierProvider.notifier)
            .setMutualStatus(profile.MutualStatus.notFollowingBack);
        break;
      case 'Verified':
        ref.read(followersFilterNotifierProvider.notifier).setVerified(true);
        break;
      case 'Billionaires':
        ref
            .read(followersFilterNotifierProvider.notifier)
            .setAccountType(profile.AccountType.billionaire);
        break;
      case 'Total Following':
        // No filter needed, show all
        break;
    }

    // Switch to Following tab to show the filtered results
    _tabController.animateTo(1);
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search users...',
          hintStyle: AppTheme.fontStyles.body.copyWith(
            color: AppTheme.secondaryAccentColor,
          ),
          prefixIcon: FaIcon(
            FontAwesomeIcons.magnifyingGlass,
            color: AppTheme.accentColor,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(
              color: AppTheme.accentColor.withValues(alpha: 0.3),
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(
              color: AppTheme.accentColor.withValues(alpha: 0.3),
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: AppTheme.accentColor),
          ),
        ),
        onChanged: (query) {
          ref
              .read(followersFilterNotifierProvider.notifier)
              .setSearchQuery(query.isEmpty ? null : query);
        },
      ),
    );
  }

  Widget _buildSelectionActions(String userId) {
    final selectedUsers = ref.read(followersServiceProvider).getSelectedUsers();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.accentColor.withValues(alpha: 0.1),
        border: Border(
          bottom: BorderSide(
            color: AppTheme.accentColor.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          Text(
            '${selectedUsers.length} selected',
            style: AppTheme.fontStyles.body.copyWith(
              color: AppTheme.accentColor,
            ),
          ),
          const Spacer(),
          TextButton(
            onPressed: () {
              ref.read(followersProvider(userId).notifier).selectAll(true);
            },
            child: Text(
              'Select All',
              style: AppTheme.fontStyles.body.copyWith(
                color: AppTheme.accentColor,
              ),
            ),
          ),
          const SizedBox(width: 8),
          ElevatedButton(
            onPressed: selectedUsers.isNotEmpty
                ? () => _showBulkActionsModal(userId)
                : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.accentColor,
              foregroundColor: Colors.white,
            ),
            child: Text(
              'Actions',
              style: AppTheme.fontStyles.body.copyWith(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFollowersList(String userId) {
    final followersAsync = ref.watch(followersProvider(userId));

    return followersAsync.when(
      data: (followers) {
        if (followers.isEmpty) {
          return Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                FaIcon(
                  FontAwesomeIcons.users,
                  size: 64,
                  color: AppTheme.secondaryAccentColor.withValues(alpha: 0.5),
                ),
                const SizedBox(height: 16),
                Text(
                  'No followers yet',
                  style: AppTheme.fontStyles.subtitle.copyWith(
                    color: AppTheme.secondaryAccentColor,
                  ),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          itemCount: followers.length,
          itemBuilder: (context, index) {
            final follower = followers[index];
            return _buildUserTile(follower, true, userId);
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(child: Text('Error: $error')),
    );
  }

  Widget _buildFollowingList(String userId) {
    final followingAsync = ref.watch(followingProvider(userId));

    return followingAsync.when(
      data: (following) {
        if (following.isEmpty) {
          return Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                FaIcon(
                  FontAwesomeIcons.userPlus,
                  size: 64,
                  color: AppTheme.secondaryAccentColor.withValues(alpha: 0.5),
                ),
                const SizedBox(height: 16),
                Text(
                  'Not following anyone yet',
                  style: AppTheme.fontStyles.subtitle.copyWith(
                    color: AppTheme.secondaryAccentColor,
                  ),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          itemCount: following.length,
          itemBuilder: (context, index) {
            final follow = following[index];
            return _buildUserTile(follow, false, userId);
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(child: Text('Error: $error')),
    );
  }

  Widget _buildUserTile(dynamic user, bool isFollower, String userId) {
    final isSelected = user.isSelected;

    return GestureDetector(
      onTap: () {
        if (_isSelectionMode) {
          if (isFollower) {
            ref
                .read(followersProvider(userId).notifier)
                .toggleSelection(user.id, !isSelected);
          } else {
            ref
                .read(followingProvider(userId).notifier)
                .toggleSelection(user.id, !isSelected);
          }
        }
      },
      onLongPress: () {
        if (!_isSelectionMode) {
          setState(() {
            _isSelectionMode = true;
          });
          if (isFollower) {
            ref
                .read(followersProvider(userId).notifier)
                .toggleSelection(user.id, true);
          } else {
            ref
                .read(followingProvider(userId).notifier)
                .toggleSelection(user.id, true);
          }
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: AppTheme.luxuryGrey.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
        ),
        child: Row(
          children: [
            if (_isSelectionMode) ...[
              Checkbox(
                value: isSelected,
                onChanged: (value) {
                  if (isFollower) {
                    ref
                        .read(followersProvider(userId).notifier)
                        .toggleSelection(user.id, value ?? false);
                  } else {
                    ref
                        .read(followingProvider(userId).notifier)
                        .toggleSelection(user.id, value ?? false);
                  }
                },
                activeColor: AppTheme.accentColor,
              ),
              const SizedBox(width: 8),
            ],
            CircleAvatar(
              radius: 25,
              backgroundImage: _isValidImageUrl(user.profilePictureUrl)
                  ? CachedNetworkImageProvider(user.profilePictureUrl)
                  : null,
              backgroundColor: AppTheme.luxuryGrey,
              child: !_isValidImageUrl(user.profilePictureUrl)
                  ? const Icon(Icons.person, size: 30, color: Colors.grey)
                  : null,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          user.name,
                          style: AppTheme.fontStyles.bodyBold.copyWith(
                            color: AppTheme.accentColor,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      if (user.isVerified)
                        FaIcon(
                          FontAwesomeIcons.check,
                          color: Colors.blue,
                          size: 16,
                        ),
                      if (user.accountType == profile.AccountType.celebrity)
                        FaIcon(
                          FontAwesomeIcons.star,
                          color: Colors.amber,
                          size: 16,
                        ),
                      if (user.accountType == profile.AccountType.business)
                        FaIcon(
                          FontAwesomeIcons.briefcase,
                          color: Colors.green,
                          size: 16,
                        ),
                      if (user.accountType == profile.AccountType.billionaire)
                        FaIcon(
                          FontAwesomeIcons.crown,
                          color: Colors.amber,
                          size: 16,
                        ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '@${user.username}',
                    style: AppTheme.fontStyles.caption.copyWith(
                      color: AppTheme.secondaryAccentColor,
                    ),
                  ),
                  if (user.bio != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      user.bio!,
                      style: AppTheme.fontStyles.caption.copyWith(
                        color: AppTheme.secondaryAccentColor,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Text(
                        'Followed ${DateFormat.yMMMd().format(user.followedAt)}',
                        style: AppTheme.fontStyles.caption.copyWith(
                          color: AppTheme.secondaryAccentColor,
                          fontSize: 10,
                        ),
                      ),
                      if (user.isMuted) ...[
                        const SizedBox(width: 8),
                        FaIcon(
                          FontAwesomeIcons.volumeXmark,
                          color: Colors.red,
                          size: 12,
                        ),
                      ],
                      if (!user.isFollowingBack) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.orange.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            'Not following back',
                            style: AppTheme.fontStyles.caption.copyWith(
                              color: Colors.orange,
                              fontSize: 10,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
            if (!_isSelectionMode) ...[
              IconButton(
                onPressed: () {
                  _showUserOptions(user, isFollower, userId);
                },
                icon: FaIcon(
                  FontAwesomeIcons.ellipsis,
                  color: AppTheme.secondaryAccentColor,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _showFilterModal() {
    showAppDialog(
      context: context,
      title: Text('Filters'),
      content: Expanded(
        child: ListView(
          children: [
            _buildFilterSection('Account Type', [
              _buildFilterChip(
                'Verified',
                profile.AccountType.verified,
                FontAwesomeIcons.check,
              ),
              _buildFilterChip(
                'Celebrities',
                profile.AccountType.celebrity,
                FontAwesomeIcons.star,
              ),
              _buildFilterChip(
                'Businesses',
                profile.AccountType.business,
                FontAwesomeIcons.briefcase,
              ),
              _buildFilterChip(
                'Billionaires',
                profile.AccountType.billionaire,
                FontAwesomeIcons.crown,
              ),
            ]),
            _buildFilterSection('Gender', [
              _buildGenderChip(
                'Male',
                profile.Gender.male,
                FontAwesomeIcons.mars,
              ),
              _buildGenderChip(
                'Female',
                profile.Gender.female,
                FontAwesomeIcons.venus,
              ),
            ]),
            _buildFilterSection('Mutual Status', [
              _buildMutualChip(
                'Mutual',
                profile.MutualStatus.mutual,
                FontAwesomeIcons.handshake,
              ),
              _buildMutualChip(
                'Not Following Back',
                profile.MutualStatus.notFollowingBack,
                FontAwesomeIcons.userXmark,
              ),
              _buildMutualChip(
                'Not Followed Back',
                profile.MutualStatus.notFollowedBack,
                FontAwesomeIcons.userMinus,
              ),
            ]),
            _buildFilterSection('Status', [
              _buildStatusChip('Verified Only', true, FontAwesomeIcons.check),
              _buildStatusChip(
                'Muted',
                true,
                FontAwesomeIcons.volumeXmark,
                isMuted: true,
              ),
            ]),
          ],
        ),
      ),
      actions: [
        OutlinedButton(
          onPressed: () {
            ref.read(followersFilterNotifierProvider.notifier).clearFilters();
            Navigator.pop(context);
          },
          style: OutlinedButton.styleFrom(
            foregroundColor: AppTheme.accentColor,
            side: BorderSide(color: AppTheme.accentColor),
          ),
          child: const Text('Clear All'),
        ),
        const SizedBox(width: 16),
        ElevatedButton(
          onPressed: () {
            Navigator.pop(context);
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.accentColor,
            foregroundColor: Colors.white,
          ),
          child: const Text('Apply'),
        ),
      ],
    );
  }

  Widget _buildFilterSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTheme.fontStyles.bodyBold.copyWith(
            color: AppTheme.accentColor,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(spacing: 8, runSpacing: 8, children: children),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildFilterChip(
    String label,
    profile.AccountType type,
    IconData icon,
  ) {
    final filter = ref.watch(followersFilterNotifierProvider);
    final isSelected = filter.accountType == type;

    return FilterChip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          FaIcon(icon, size: 12),
          const SizedBox(width: 4),
          Text(label),
        ],
      ),
      selected: isSelected,
      onSelected: (selected) {
        ref
            .read(followersFilterNotifierProvider.notifier)
            .setAccountType(selected ? type : null);
      },
      selectedColor: AppTheme.accentColor.withValues(alpha: 0.2),
      checkmarkColor: AppTheme.accentColor,
    );
  }

  Widget _buildGenderChip(String label, profile.Gender gender, IconData icon) {
    final filter = ref.watch(followersFilterNotifierProvider);
    final isSelected = filter.gender == gender;

    return FilterChip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          FaIcon(icon, size: 12),
          const SizedBox(width: 4),
          Text(label),
        ],
      ),
      selected: isSelected,
      onSelected: (selected) {
        ref
            .read(followersFilterNotifierProvider.notifier)
            .setGender(selected ? gender : null);
      },
      selectedColor: AppTheme.accentColor.withValues(alpha: 0.2),
      checkmarkColor: AppTheme.accentColor,
    );
  }

  Widget _buildMutualChip(
    String label,
    profile.MutualStatus status,
    IconData icon,
  ) {
    final filter = ref.watch(followersFilterNotifierProvider);
    final isSelected = filter.mutualStatus == status;

    return FilterChip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          FaIcon(icon, size: 12),
          const SizedBox(width: 4),
          Text(label),
        ],
      ),
      selected: isSelected,
      onSelected: (selected) {
        ref
            .read(followersFilterNotifierProvider.notifier)
            .setMutualStatus(selected ? status : null);
      },
      selectedColor: AppTheme.accentColor.withValues(alpha: 0.2),
      checkmarkColor: AppTheme.accentColor,
    );
  }

  Widget _buildStatusChip(
    String label,
    bool value,
    IconData icon, {
    bool isMuted = false,
  }) {
    final filter = ref.watch(followersFilterNotifierProvider);
    final isSelected = isMuted
        ? filter.isMuted == value
        : filter.isVerified == value;

    return FilterChip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          FaIcon(icon, size: 12),
          const SizedBox(width: 4),
          Text(label),
        ],
      ),
      selected: isSelected,
      onSelected: (selected) {
        if (isMuted) {
          ref
              .read(followersFilterNotifierProvider.notifier)
              .setMuted(selected ? value : null);
        } else {
          ref
              .read(followersFilterNotifierProvider.notifier)
              .setVerified(selected ? value : null);
        }
      },
      selectedColor: AppTheme.accentColor.withValues(alpha: 0.2),
      checkmarkColor: AppTheme.accentColor,
    );
  }

  void _showBulkActionsModal(String userId) {
    final selectedUsers = ref.read(followersServiceProvider).getSelectedUsers();

    showAppDialog(
      context: context,
      title: Text('Bulk Actions'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '${selectedUsers.length} selected',
            style: AppTheme.fontStyles.title.copyWith(
              color: AppTheme.accentColor,
            ),
          ),
          const SizedBox(height: 20),
          _buildBulkActionTile(
            icon: FontAwesomeIcons.userMinus,
            title: 'Unfollow Selected',
            subtitle: 'Stop following these accounts',
            isDestructive: true,
            onTap: () {
              Navigator.pop(context);
              _confirmBulkAction(profile.BulkAction.unfollow, userId);
            },
          ),
          _buildBulkActionTile(
            icon: FontAwesomeIcons.volumeXmark,
            title: 'Mute Notifications',
            subtitle: 'Stop receiving notifications from these accounts',
            onTap: () {
              Navigator.pop(context);
              _confirmBulkAction(profile.BulkAction.mute, userId);
            },
          ),
          _buildBulkActionTile(
            icon: FontAwesomeIcons.volumeHigh,
            title: 'Unmute Notifications',
            subtitle: 'Start receiving notifications from these accounts',
            onTap: () {
              Navigator.pop(context);
              _confirmBulkAction(profile.BulkAction.unmute, userId);
            },
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildBulkActionTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: FaIcon(
        icon,
        color: isDestructive ? Colors.red : AppTheme.accentColor,
      ),
      title: Text(
        title,
        style: AppTheme.fontStyles.bodyBold.copyWith(
          color: isDestructive ? Colors.red : AppTheme.accentColor,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: AppTheme.fontStyles.caption.copyWith(
          color: AppTheme.secondaryAccentColor,
        ),
      ),
      onTap: onTap,
    );
  }

  void _confirmBulkAction(profile.BulkAction action, String userId) {
    final selectedUsers = ref.read(followersServiceProvider).getSelectedUsers();
    String title, message;

    switch (action) {
      case profile.BulkAction.unfollow:
        title = 'Unfollow Selected Accounts';
        message =
            'Are you sure you want to unfollow ${selectedUsers.length} accounts?';
        break;
      case profile.BulkAction.mute:
        title = 'Mute Notifications';
        message =
            'Are you sure you want to mute notifications from ${selectedUsers.length} accounts?';
        break;
      case profile.BulkAction.unmute:
        title = 'Unmute Notifications';
        message =
            'Are you sure you want to unmute notifications from ${selectedUsers.length} accounts?';
        break;
      case profile.BulkAction.addToList:
        title = 'Add to List';
        message =
            'Are you sure you want to add ${selectedUsers.length} accounts to the list?';
        break;
    }

    showAppDialog(
      context: context,
      title: Text(title),
      content: Text(message),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(
            'Cancel',
            style: AppTheme.fontStyles.body.copyWith(
              color: AppTheme.secondaryAccentColor,
            ),
          ),
        ),
        TextButton(
          onPressed: () {
            Navigator.pop(context);
            ref
                .read(followingProvider(userId).notifier)
                .performBulkAction(action);
            setState(() {
              _isSelectionMode = false;
            });
          },
          child: Text(
            'Confirm',
            style: AppTheme.fontStyles.bodyBold.copyWith(
              color: action == profile.BulkAction.unfollow
                  ? Colors.red
                  : AppTheme.accentColor,
            ),
          ),
        ),
      ],
    );
  }

  void _showUserOptions(dynamic user, bool isFollower, String userId) {
    showAppDialog(
      context: context,
      title: Text('User Options'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildUserOptionTile(
            icon: FontAwesomeIcons.userMinus,
            title: 'Unfollow',
            subtitle: 'Stop following this account',
            isDestructive: true,
            onTap: () {
              Navigator.pop(context);
              _confirmBulkAction(profile.BulkAction.unfollow, userId);
            },
          ),
          _buildUserOptionTile(
            icon: user.isMuted
                ? FontAwesomeIcons.volumeHigh
                : FontAwesomeIcons.volumeXmark,
            title: user.isMuted ? 'Unmute' : 'Mute',
            subtitle: user.isMuted
                ? 'Start receiving notifications'
                : 'Stop receiving notifications',
            onTap: () {
              Navigator.pop(context);
              _confirmBulkAction(
                user.isMuted
                    ? profile.BulkAction.unmute
                    : profile.BulkAction.mute,
                userId,
              );
            },
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildUserOptionTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: FaIcon(
        icon,
        color: isDestructive ? Colors.red : AppTheme.accentColor,
      ),
      title: Text(
        title,
        style: AppTheme.fontStyles.bodyBold.copyWith(
          color: isDestructive ? Colors.red : AppTheme.accentColor,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: AppTheme.fontStyles.caption.copyWith(
          color: AppTheme.secondaryAccentColor,
        ),
      ),
      onTap: onTap,
    );
  }

  bool _isValidImageUrl(String? url) {
    if (url == null || url.isEmpty) return false;
    return url.startsWith('http://') || url.startsWith('https://');
  }
}
