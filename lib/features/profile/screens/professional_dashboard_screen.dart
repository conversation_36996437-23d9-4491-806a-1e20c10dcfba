import 'package:billionaires_social/core/app_theme.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/profile/providers/profile_analytics_provider.dart';
import 'package:billionaires_social/features/profile/models/profile_analytics_model.dart';

class ProfessionalDashboardScreen extends StatelessWidget {
  const ProfessionalDashboardScreen({super.key});

  String _formatNumber(int number) {
    if (number >= 1000000) {
      return '${(number / 1000000).toStringAsFixed(1)}M';
    } else if (number >= 1000) {
      return '${(number / 1000).toStringAsFixed(1)}K';
    } else {
      return number.toString();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Professional Dashboard'),
        backgroundColor: AppTheme.primaryColor,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildOverviewCard(),
            const SizedBox(height: 20),
            _ProfileAnalyticsSection(),
            const SizedBox(height: 20),
            _buildAnalyticsSection(),
            const SizedBox(height: 20),
            _buildEngagementSection(),
            const SizedBox(height: 20),
            _buildProfessionalTools(),
          ],
        ),
      ),
    );
  }

  Widget _buildOverviewCard() {
    return Consumer(
      builder: (context, ref, child) {
        final analyticsAsync = ref.watch(profileLatestAnalyticsProvider);
        return analyticsAsync.when(
          data: (analytics) {
            if (analytics == null) {
              return Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppTheme.accentColor,
                      AppTheme.accentColor.withValues(alpha: 0.8),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        FaIcon(
                          FontAwesomeIcons.chartLine,
                          color: Colors.white,
                          size: 24,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Profile Overview',
                          style: AppTheme.fontStyles.title.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    const Center(
                      child: Text(
                        'No analytics data available yet',
                        style: TextStyle(color: Colors.white70),
                      ),
                    ),
                  ],
                ),
              );
            }

            return Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppTheme.accentColor,
                    AppTheme.accentColor.withValues(alpha: 0.8),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      FaIcon(
                        FontAwesomeIcons.chartLine,
                        color: Colors.white,
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'Profile Overview',
                        style: AppTheme.fontStyles.title.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildOverviewStat(
                        'Followers',
                        _formatNumber(analytics.followerGrowth.totalFollowers),
                        FontAwesomeIcons.users,
                      ),
                      _buildOverviewStat(
                        'Engagement',
                        '${analytics.engagement.overallEngagementRate.toStringAsFixed(1)}%',
                        FontAwesomeIcons.heart,
                      ),
                      _buildOverviewStat(
                        'Profile Views',
                        _formatNumber(analytics.profileViews.totalViews),
                        FontAwesomeIcons.eye,
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
          loading: () => Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppTheme.accentColor,
                  AppTheme.accentColor.withValues(alpha: 0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Center(child: CircularProgressIndicator()),
          ),
          error: (error, stack) => Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppTheme.accentColor,
                  AppTheme.accentColor.withValues(alpha: 0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Center(
              child: Text(
                'Error loading overview: $error',
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildOverviewStat(String label, String value, IconData icon) {
    return Column(
      children: [
        FaIcon(icon, color: Colors.white, size: 20),
        const SizedBox(height: 8),
        Text(
          value,
          style: AppTheme.fontStyles.title.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: AppTheme.fontStyles.caption.copyWith(
            color: Colors.white.withValues(alpha: 0.8),
          ),
        ),
      ],
    );
  }

  Widget _buildAnalyticsSection() {
    return Consumer(
      builder: (context, ref, child) {
        final analyticsAsync = ref.watch(profileLatestAnalyticsProvider);
        return analyticsAsync.when(
          data: (analytics) {
            if (analytics == null) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Analytics',
                    style: AppTheme.fontStyles.title.copyWith(
                      color: AppTheme.accentColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  const Center(
                    child: Text(
                      'No detailed analytics available yet',
                      style: TextStyle(color: Colors.white70),
                    ),
                  ),
                ],
              );
            }

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Analytics',
                  style: AppTheme.fontStyles.title.copyWith(
                    color: AppTheme.accentColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildAnalyticsCard(
                        'Growth Rate',
                        '${analytics.followerGrowth.growthRate >= 0 ? '+' : ''}${analytics.followerGrowth.growthRate.toStringAsFixed(1)}%',
                        analytics.followerGrowth.growthRate >= 0
                            ? FontAwesomeIcons.arrowUp
                            : FontAwesomeIcons.arrowDown,
                        analytics.followerGrowth.growthRate >= 0
                            ? Colors.green
                            : Colors.red,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildAnalyticsCard(
                        'Profile Views',
                        _formatNumber(analytics.profileViews.totalViews),
                        FontAwesomeIcons.eye,
                        Colors.blue,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildAnalyticsCard(
                        'Engagement Rate',
                        '${analytics.engagement.overallEngagementRate.toStringAsFixed(1)}%',
                        FontAwesomeIcons.heart,
                        Colors.orange,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildAnalyticsCard(
                        'Top Posts',
                        analytics.contentPerformance.topPosts.length.toString(),
                        FontAwesomeIcons.fire,
                        Colors.red,
                      ),
                    ),
                  ],
                ),
              ],
            );
          },
          loading: () => Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Analytics',
                style: AppTheme.fontStyles.title.copyWith(
                  color: AppTheme.accentColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 12),
              const Center(child: CircularProgressIndicator()),
            ],
          ),
          error: (error, stack) => Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Analytics',
                style: AppTheme.fontStyles.title.copyWith(
                  color: AppTheme.accentColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 12),
              Center(
                child: Text(
                  'Error loading analytics: $error',
                  style: const TextStyle(color: Colors.red),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAnalyticsCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          FaIcon(icon, color: color, size: 20),
          const SizedBox(height: 8),
          Text(
            value,
            style: AppTheme.fontStyles.subtitle.copyWith(
              color: AppTheme.accentColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: AppTheme.fontStyles.caption.copyWith(
              color: AppTheme.secondaryAccentColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildEngagementSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Engagement Insights',
          style: AppTheme.fontStyles.title.copyWith(
            color: AppTheme.accentColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        _buildEngagementCard(
          'Best Time to Post',
          '7:00 PM - 9:00 PM',
          FontAwesomeIcons.clock,
          'Your audience is most active during these hours',
        ),
        const SizedBox(height: 8),
        _buildEngagementCard(
          'Top Content Type',
          'Lifestyle Posts',
          FontAwesomeIcons.images,
          'Your lifestyle content gets 23% more engagement',
        ),
        const SizedBox(height: 8),
        _buildEngagementCard(
          'Audience Demographics',
          '25-34 years (45%)',
          FontAwesomeIcons.users,
          'Primary audience age group',
        ),
      ],
    );
  }

  Widget _buildEngagementCard(
    String title,
    String value,
    IconData icon,
    String subtitle,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.accentColor.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppTheme.accentColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: FaIcon(icon, color: AppTheme.accentColor, size: 20),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTheme.fontStyles.bodyBold.copyWith(
                    color: AppTheme.accentColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: AppTheme.fontStyles.body.copyWith(
                    color: AppTheme.secondaryAccentColor,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  subtitle,
                  style: AppTheme.fontStyles.caption.copyWith(
                    color: AppTheme.secondaryAccentColor.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfessionalTools() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Professional Tools',
          style: AppTheme.fontStyles.title.copyWith(
            color: AppTheme.accentColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        _buildToolCard(
          'Content Calendar',
          'Schedule and plan your posts',
          FontAwesomeIcons.calendar,
          () {},
        ),
        const SizedBox(height: 8),
        _buildToolCard(
          'Audience Insights',
          'Detailed analytics about your followers',
          FontAwesomeIcons.chartBar,
          () {},
        ),
        const SizedBox(height: 8),
        _buildToolCard(
          'Brand Collaborations',
          'Manage partnership opportunities',
          FontAwesomeIcons.handshake,
          () {},
        ),
        const SizedBox(height: 8),
        _buildToolCard(
          'Content Performance',
          'Track post performance metrics',
          FontAwesomeIcons.trophy,
          () {},
        ),
      ],
    );
  }

  Widget _buildToolCard(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppTheme.accentColor.withValues(alpha: 0.2),
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppTheme.accentColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: FaIcon(icon, color: AppTheme.accentColor, size: 20),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTheme.fontStyles.bodyBold.copyWith(
                      color: AppTheme.accentColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: AppTheme.fontStyles.caption.copyWith(
                      color: AppTheme.secondaryAccentColor,
                    ),
                  ),
                ],
              ),
            ),
            FaIcon(
              FontAwesomeIcons.arrowRight,
              color: AppTheme.accentColor.withValues(alpha: 0.5),
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
}

class _ProfileAnalyticsSection extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsAsync = ref.watch(profileLatestAnalyticsProvider);
    return analyticsAsync.when(
      data: (analytics) {
        if (analytics == null) {
          return Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Text('No analytics data available.'),
            ),
          );
        }
        return Card(
          color: AppTheme.luxuryGrey.withValues(alpha: 0.08),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 0,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(
                      FontAwesomeIcons.chartPie,
                      color: AppTheme.accentColor,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Profile Analytics',
                      style: AppTheme.fontStyles.title.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Wrap(
                  spacing: 16,
                  runSpacing: 12,
                  children: [
                    _analyticsMetric(
                      'Profile Views',
                      analytics.profileViews.totalViews.toString(),
                      FontAwesomeIcons.eye,
                      Colors.blue,
                    ),
                    _analyticsMetric(
                      'Followers',
                      analytics.followerGrowth.totalFollowers.toString(),
                      FontAwesomeIcons.users,
                      Colors.green,
                    ),
                    _analyticsMetric(
                      'Growth Rate',
                      '${analytics.followerGrowth.growthRate.toStringAsFixed(1)}%',
                      FontAwesomeIcons.arrowTrendUp,
                      Colors.purple,
                    ),
                    _analyticsMetric(
                      'Engagement',
                      '${analytics.engagement.overallEngagementRate.toStringAsFixed(1)}%',
                      FontAwesomeIcons.heart,
                      Colors.red,
                    ),
                    _analyticsMetric(
                      'Audience',
                      analytics.audience.totalAudience.toString(),
                      FontAwesomeIcons.userGroup,
                      Colors.orange,
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                if (analytics.contentPerformance.topPosts.isNotEmpty)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Top Performing Post',
                        style: AppTheme.fontStyles.bodyBold,
                      ),
                      const SizedBox(height: 8),
                      _topPostTile(analytics.contentPerformance.topPosts.first),
                    ],
                  ),
              ],
            ),
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (e, s) => Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Text('Failed to load analytics: $e'),
        ),
      ),
    );
  }

  Widget _analyticsMetric(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Chip(
      avatar: FaIcon(icon, color: color, size: 16),
      label: Text(
        '$label: $value',
        style: TextStyle(color: color, fontWeight: FontWeight.bold),
      ),
      backgroundColor: color.withValues(alpha: 0.08),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
    );
  }

  Widget _topPostTile(TopPerformingPost post) {
    return ListTile(
      leading: const Icon(FontAwesomeIcons.medal, color: Colors.amber),
      title: Text('Post ID: ${post.postId}'),
      subtitle: Text('Engagement: ${post.engagementRate.toStringAsFixed(1)}%'),
      trailing: Text('${post.likes} likes'),
      tileColor: Colors.amber.withValues(alpha: 0.08),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
    );
  }
}
