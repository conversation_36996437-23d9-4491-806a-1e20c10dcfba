import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:billionaires_social/features/profile/providers/profile_verification_provider.dart';
import 'package:billionaires_social/features/profile/models/profile_verification_model.dart';
import 'package:billionaires_social/features/profile/screens/verification_flow_screen.dart';

import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/core/widgets/error_display_widget.dart';

/// Screen for user-initiated profile verification
class ProfileVerificationScreen extends ConsumerStatefulWidget {
  const ProfileVerificationScreen({super.key});

  @override
  ConsumerState<ProfileVerificationScreen> createState() =>
      _ProfileVerificationScreenState();
}

class _ProfileVerificationScreenState
    extends ConsumerState<ProfileVerificationScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.luxuryBlack,
      appBar: AppBar(
        backgroundColor: AppTheme.luxuryBlack,
        title: Text(
          'Profile Verification',
          style: AppTheme.fontStyles.title.copyWith(
            color: AppTheme.luxuryWhite,
          ),
        ),
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: AppTheme.accentColor,
          labelColor: AppTheme.accentColor,
          unselectedLabelColor: AppTheme.secondaryAccentColor,
          tabs: const [
            Tab(text: 'Available'),
            Tab(text: 'My Status'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [_buildAvailableVerificationsTab(), _buildMyStatusTab()],
      ),
    );
  }

  Widget _buildAvailableVerificationsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header info
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: AppTheme.accentColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppTheme.accentColor.withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    FaIcon(
                      FontAwesomeIcons.shieldHalved,
                      color: AppTheme.accentColor,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Verification Benefits',
                      style: AppTheme.fontStyles.bodyBold.copyWith(
                        color: AppTheme.accentColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  'Verified accounts get increased visibility, trust badges, and access to exclusive features.',
                  style: AppTheme.fontStyles.body.copyWith(
                    color: AppTheme.secondaryAccentColor.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Verification types
          Text(
            'Choose Verification Type',
            style: AppTheme.fontStyles.title.copyWith(
              color: AppTheme.luxuryWhite,
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 16),

          // Basic verifications
          VerificationTypeCard(
            type: VerificationType.email,
            title: 'Email Verification',
            description: 'Verify your email address for account security',
            icon: FontAwesomeIcons.envelope,
            color: Colors.blue,
            isRecommended: true,
            onTap: () => _startVerification(VerificationType.email),
          ),

          const SizedBox(height: 12),

          VerificationTypeCard(
            type: VerificationType.phone,
            title: 'Phone Verification',
            description:
                'Verify your phone number for two-factor authentication',
            icon: FontAwesomeIcons.phone,
            color: Colors.green,
            onTap: () => _startVerification(VerificationType.phone),
          ),

          const SizedBox(height: 24),

          // Premium verifications
          Text(
            'Premium Verifications',
            style: AppTheme.fontStyles.title.copyWith(
              color: AppTheme.luxuryWhite,
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 16),

          VerificationTypeCard(
            type: VerificationType.identity,
            title: 'Identity Verification',
            description: 'Verify your identity with government-issued ID',
            icon: FontAwesomeIcons.idCard,
            color: Colors.orange,
            isPremium: true,
            onTap: () => _startVerification(VerificationType.identity),
          ),

          const SizedBox(height: 12),

          VerificationTypeCard(
            type: VerificationType.business,
            title: 'Business Verification',
            description: 'Verify your business with official documents',
            icon: FontAwesomeIcons.building,
            color: Colors.purple,
            isPremium: true,
            onTap: () => _startVerification(VerificationType.business),
          ),

          const SizedBox(height: 12),

          VerificationTypeCard(
            type: VerificationType.billionaire,
            title: 'Billionaire Status',
            description:
                'Exclusive verification for ultra-high net worth individuals',
            icon: FontAwesomeIcons.crown,
            color: AppTheme.accentColor,
            isPremium: true,
            isExclusive: true,
            onTap: () => _startVerification(VerificationType.billionaire),
          ),

          const SizedBox(height: 12),

          VerificationTypeCard(
            type: VerificationType.celebrity,
            title: 'Celebrity Verification',
            description: 'Verification for public figures and celebrities',
            icon: FontAwesomeIcons.star,
            color: Colors.pink,
            isPremium: true,
            isExclusive: true,
            onTap: () => _startVerification(VerificationType.celebrity),
          ),
        ],
      ),
    );
  }

  Widget _buildMyStatusTab() {
    final verificationsAsync = ref.watch(profileVerificationsProvider);

    return verificationsAsync.when(
      data: (verifications) {
        if (verifications.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: AppTheme.accentColor.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: FaIcon(
                    FontAwesomeIcons.shieldHalved,
                    size: 48,
                    color: AppTheme.accentColor.withValues(alpha: 0.7),
                  ),
                ),
                const SizedBox(height: 24),
                Text(
                  'No Verifications Yet',
                  style: AppTheme.fontStyles.title.copyWith(
                    color: AppTheme.luxuryWhite,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  'Start with email verification to secure your account',
                  style: AppTheme.fontStyles.body.copyWith(
                    color: AppTheme.secondaryAccentColor.withValues(alpha: 0.8),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: () => _tabController.animateTo(0),
                  icon: const FaIcon(FontAwesomeIcons.plus, size: 16),
                  label: const Text('Start Verification'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.accentColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: verifications.length,
          itemBuilder: (context, index) {
            final verification = verifications[index];
            return _buildVerificationStatusCard(verification);
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => ErrorDisplayWidget(
        error: error,
        context: 'Loading verifications',
        onRetry: () {
          ref.invalidate(profileVerificationsProvider);
        },
      ),
    );
  }

  Widget _buildVerificationStatusCard(ProfileVerificationModel verification) {
    final statusColor = _getStatusColor(verification.status);
    final statusIcon = _getStatusIcon(verification.status);
    final typeInfo = _getTypeInfo(verification.type);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: statusColor.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: typeInfo['color'].withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: FaIcon(
                  typeInfo['icon'],
                  color: typeInfo['color'],
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      typeInfo['title'],
                      style: AppTheme.fontStyles.bodyBold.copyWith(
                        color: AppTheme.luxuryWhite,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        FaIcon(statusIcon, size: 12, color: statusColor),
                        const SizedBox(width: 4),
                        Text(
                          _getStatusText(verification.status),
                          style: AppTheme.fontStyles.caption.copyWith(
                            color: statusColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              if (verification.status == VerificationStatus.rejected)
                IconButton(
                  onPressed: () => _retryVerification(verification),
                  icon: const FaIcon(
                    FontAwesomeIcons.arrowRotateRight,
                    size: 16,
                    color: Colors.orange,
                  ),
                ),
            ],
          ),

          if (verification.verificationNotes != null) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: statusColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                verification.verificationNotes!,
                style: AppTheme.fontStyles.caption.copyWith(
                  color: AppTheme.secondaryAccentColor,
                ),
              ),
            ),
          ],

          const SizedBox(height: 8),

          Text(
            'Submitted: ${_formatDate(verification.createdAt)}',
            style: AppTheme.fontStyles.caption.copyWith(
              color: AppTheme.secondaryAccentColor.withValues(alpha: 0.7),
            ),
          ),

          if (verification.completedAt != null)
            Text(
              'Completed: ${_formatDate(verification.completedAt!)}',
              style: AppTheme.fontStyles.caption.copyWith(
                color: AppTheme.secondaryAccentColor.withValues(alpha: 0.7),
              ),
            ),
        ],
      ),
    );
  }

  void _startVerification(VerificationType type) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => VerificationFlowScreen(type: type),
      ),
    );
  }

  void _retryVerification(ProfileVerificationModel verification) {
    // TODO: Implement retry verification
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Retry ${_getTypeInfo(verification.type)['title']} verification',
        ),
        backgroundColor: Colors.orange,
      ),
    );
  }

  Color _getStatusColor(VerificationStatus status) {
    switch (status) {
      case VerificationStatus.pending:
      case VerificationStatus.submitted:
        return Colors.orange;
      case VerificationStatus.underReview:
        return Colors.blue;
      case VerificationStatus.approved:
        return Colors.green;
      case VerificationStatus.rejected:
        return Colors.red;
      case VerificationStatus.expired:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(VerificationStatus status) {
    switch (status) {
      case VerificationStatus.pending:
      case VerificationStatus.submitted:
        return FontAwesomeIcons.clock;
      case VerificationStatus.underReview:
        return FontAwesomeIcons.magnifyingGlass;
      case VerificationStatus.approved:
        return FontAwesomeIcons.circleCheck;
      case VerificationStatus.rejected:
        return FontAwesomeIcons.circleXmark;
      case VerificationStatus.expired:
        return FontAwesomeIcons.clockRotateLeft;
    }
  }

  String _getStatusText(VerificationStatus status) {
    switch (status) {
      case VerificationStatus.pending:
        return 'Pending';
      case VerificationStatus.submitted:
        return 'Submitted';
      case VerificationStatus.underReview:
        return 'Under Review';
      case VerificationStatus.approved:
        return 'Approved';
      case VerificationStatus.rejected:
        return 'Rejected';
      case VerificationStatus.expired:
        return 'Expired';
    }
  }

  Map<String, dynamic> _getTypeInfo(VerificationType type) {
    switch (type) {
      case VerificationType.email:
        return {
          'title': 'Email Verification',
          'icon': FontAwesomeIcons.envelope,
          'color': Colors.blue,
        };
      case VerificationType.phone:
        return {
          'title': 'Phone Verification',
          'icon': FontAwesomeIcons.phone,
          'color': Colors.green,
        };
      case VerificationType.identity:
        return {
          'title': 'Identity Verification',
          'icon': FontAwesomeIcons.idCard,
          'color': Colors.orange,
        };
      case VerificationType.business:
        return {
          'title': 'Business Verification',
          'icon': FontAwesomeIcons.building,
          'color': Colors.purple,
        };
      case VerificationType.billionaire:
        return {
          'title': 'Billionaire Status',
          'icon': FontAwesomeIcons.crown,
          'color': AppTheme.accentColor,
        };
      case VerificationType.celebrity:
        return {
          'title': 'Celebrity Verification',
          'icon': FontAwesomeIcons.star,
          'color': Colors.pink,
        };
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// Widget for displaying verification type options
class VerificationTypeCard extends StatelessWidget {
  final VerificationType type;
  final String title;
  final String description;
  final IconData icon;
  final Color color;
  final bool isRecommended;
  final bool isPremium;
  final bool isExclusive;
  final VoidCallback onTap;

  const VerificationTypeCard({
    super.key,
    required this.type,
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    required this.onTap,
    this.isRecommended = false,
    this.isPremium = false,
    this.isExclusive = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isRecommended
                ? AppTheme.accentColor.withValues(alpha: 0.5)
                : color.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: FaIcon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        title,
                        style: AppTheme.fontStyles.bodyBold.copyWith(
                          color: AppTheme.luxuryWhite,
                        ),
                      ),
                      if (isRecommended) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: AppTheme.accentColor,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            'RECOMMENDED',
                            style: AppTheme.fontStyles.caption.copyWith(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                      if (isPremium) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.orange,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            'PREMIUM',
                            style: AppTheme.fontStyles.caption.copyWith(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                      if (isExclusive) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.purple,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            'EXCLUSIVE',
                            style: AppTheme.fontStyles.caption.copyWith(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: AppTheme.fontStyles.caption.copyWith(
                      color: AppTheme.secondaryAccentColor.withValues(
                        alpha: 0.8,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            FaIcon(
              FontAwesomeIcons.chevronRight,
              color: AppTheme.secondaryAccentColor.withValues(alpha: 0.5),
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
}
