import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/features/feed/services/content_access_service.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:get_it/get_it.dart';

class ContentPreferencesScreen extends ConsumerStatefulWidget {
  const ContentPreferencesScreen({super.key});

  @override
  ConsumerState<ContentPreferencesScreen> createState() =>
      _ContentPreferencesScreenState();
}

class _ContentPreferencesScreenState
    extends ConsumerState<ContentPreferencesScreen> {
  final ContentAccessService _contentAccessService =
      GetIt.I<ContentAccessService>();

  bool _isLoading = true;
  bool _isSaving = false;

  // Preference values
  bool _showSuggestedContent = true;
  bool _showTrendingPosts = true;
  bool _showRegionalContent = true;
  double _discoveryContentPercentage = 30.0;
  bool _showVerifiedOnly = false;
  bool _showFollowedUsersOnly = false;
  bool _enableContentFiltering = true;

  // Content type preferences
  bool _showImages = true;
  bool _showVideos = true;
  bool _showTextPosts = true;
  bool _showLinks = true;

  final List<String> _blockedKeywords = [];
  final TextEditingController _keywordController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadPreferences();
  }

  @override
  void dispose() {
    _keywordController.dispose();
    super.dispose();
  }

  Future<void> _loadPreferences() async {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) return;

    try {
      final preferences = await _contentAccessService.getContentPreferences(
        currentUser.uid,
      );

      setState(() {
        _showSuggestedContent =
            preferences['showSuggestedContent'] as bool? ?? true;
        _showTrendingPosts = preferences['showTrendingPosts'] as bool? ?? true;
        _showRegionalContent =
            preferences['showRegionalContent'] as bool? ?? true;
        _discoveryContentPercentage =
            (preferences['discoveryContentPercentage'] as double? ?? 30.0)
                .clamp(0.0, 50.0);
        _showVerifiedOnly = preferences['showVerifiedOnly'] as bool? ?? false;
        _showFollowedUsersOnly =
            preferences['showFollowedUsersOnly'] as bool? ?? false;
        _enableContentFiltering =
            preferences['enableContentFiltering'] as bool? ?? true;

        final contentTypes =
            preferences['contentTypes'] as Map<String, dynamic>? ?? {};
        _showImages = contentTypes['images'] as bool? ?? true;
        _showVideos = contentTypes['videos'] as bool? ?? true;
        _showTextPosts = contentTypes['text'] as bool? ?? true;
        _showLinks = contentTypes['links'] as bool? ?? true;

        _blockedKeywords.clear();
        _blockedKeywords.addAll(
          List<String>.from(preferences['blockedKeywords'] ?? []),
        );

        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load preferences: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _savePreferences() async {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) return;

    setState(() {
      _isSaving = true;
    });

    try {
      final preferences = {
        'showSuggestedContent': _showSuggestedContent,
        'showTrendingPosts': _showTrendingPosts,
        'showRegionalContent': _showRegionalContent,
        'discoveryContentPercentage': _discoveryContentPercentage,
        'showVerifiedOnly': _showVerifiedOnly,
        'showFollowedUsersOnly': _showFollowedUsersOnly,
        'enableContentFiltering': _enableContentFiltering,
        'contentTypes': {
          'images': _showImages,
          'videos': _showVideos,
          'text': _showTextPosts,
          'links': _showLinks,
        },
        'blockedKeywords': _blockedKeywords,
        'preferredLanguages': ['en'], // Default for now
      };

      await _contentAccessService.updateContentPreferences(
        currentUser.uid,
        preferences,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Preferences saved successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save preferences: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Content Preferences', style: AppTheme.fontStyles.title),
        actions: [
          if (_isSaving)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            )
          else
            TextButton(onPressed: _savePreferences, child: const Text('Save')),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildDiscoverySection(),
                  const SizedBox(height: 24),
                  _buildContentTypesSection(),
                  const SizedBox(height: 24),
                  _buildFilteringSection(),
                  const SizedBox(height: 24),
                  _buildKeywordsSection(),
                ],
              ),
            ),
    );
  }

  Widget _buildDiscoverySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const FaIcon(FontAwesomeIcons.compass, size: 20),
                const SizedBox(width: 8),
                Text('Content Discovery', style: AppTheme.fontStyles.subtitle),
              ],
            ),
            const SizedBox(height: 16),

            SwitchListTile(
              title: const Text('Show suggested content'),
              subtitle: const Text('Content from users you don\'t follow'),
              value: _showSuggestedContent,
              onChanged: (value) {
                setState(() {
                  _showSuggestedContent = value;
                });
              },
            ),

            SwitchListTile(
              title: const Text('Show trending posts'),
              subtitle: const Text('Popular posts from the community'),
              value: _showTrendingPosts,
              onChanged: (value) {
                setState(() {
                  _showTrendingPosts = value;
                });
              },
            ),

            SwitchListTile(
              title: const Text('Show regional content'),
              subtitle: const Text('Posts from users in your region'),
              value: _showRegionalContent,
              onChanged: (value) {
                setState(() {
                  _showRegionalContent = value;
                });
              },
            ),

            const SizedBox(height: 16),
            Text(
              'Discovery content percentage: ${_discoveryContentPercentage.round()}%',
            ),
            Slider(
              value: _discoveryContentPercentage,
              min: 0,
              max: 50,
              divisions: 10,
              label: '${_discoveryContentPercentage.round()}%',
              onChanged: (value) {
                setState(() {
                  _discoveryContentPercentage = value;
                });
              },
            ),
            const Text(
              'Controls how much of your feed consists of discovery content',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContentTypesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const FaIcon(FontAwesomeIcons.layerGroup, size: 20),
                const SizedBox(width: 8),
                Text('Content Types', style: AppTheme.fontStyles.subtitle),
              ],
            ),
            const SizedBox(height: 16),

            SwitchListTile(
              title: const Text('Images'),
              subtitle: const Text('Show image posts'),
              value: _showImages,
              onChanged: (value) {
                setState(() {
                  _showImages = value;
                });
              },
            ),

            SwitchListTile(
              title: const Text('Videos'),
              subtitle: const Text('Show video posts'),
              value: _showVideos,
              onChanged: (value) {
                setState(() {
                  _showVideos = value;
                });
              },
            ),

            SwitchListTile(
              title: const Text('Text posts'),
              subtitle: const Text('Show text-only posts'),
              value: _showTextPosts,
              onChanged: (value) {
                setState(() {
                  _showTextPosts = value;
                });
              },
            ),

            SwitchListTile(
              title: const Text('Links'),
              subtitle: const Text('Show posts with external links'),
              value: _showLinks,
              onChanged: (value) {
                setState(() {
                  _showLinks = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilteringSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const FaIcon(FontAwesomeIcons.filter, size: 20),
                const SizedBox(width: 8),
                Text('Content Filtering', style: AppTheme.fontStyles.subtitle),
              ],
            ),
            const SizedBox(height: 16),

            SwitchListTile(
              title: const Text('Enable content filtering'),
              subtitle: const Text('Filter content based on your preferences'),
              value: _enableContentFiltering,
              onChanged: (value) {
                setState(() {
                  _enableContentFiltering = value;
                });
              },
            ),

            SwitchListTile(
              title: const Text('Verified accounts only'),
              subtitle: const Text('Only show content from verified users'),
              value: _showVerifiedOnly,
              onChanged: (value) {
                setState(() {
                  _showVerifiedOnly = value;
                });
              },
            ),

            SwitchListTile(
              title: const Text('Followed users only'),
              subtitle: const Text('Only show content from users you follow'),
              value: _showFollowedUsersOnly,
              onChanged: (value) {
                setState(() {
                  _showFollowedUsersOnly = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildKeywordsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const FaIcon(FontAwesomeIcons.ban, size: 20),
                const SizedBox(width: 8),
                Text('Blocked Keywords', style: AppTheme.fontStyles.subtitle),
              ],
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _keywordController,
                    decoration: const InputDecoration(
                      hintText: 'Add keyword to block',
                      border: OutlineInputBorder(),
                    ),
                    onSubmitted: _addKeyword,
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () => _addKeyword(_keywordController.text),
                  child: const Text('Add'),
                ),
              ],
            ),

            const SizedBox(height: 16),

            if (_blockedKeywords.isEmpty)
              const Text(
                'No blocked keywords. Add keywords to filter out content containing those terms.',
                style: TextStyle(color: Colors.grey),
              )
            else
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _blockedKeywords.map((keyword) {
                  return Chip(
                    label: Text(keyword),
                    deleteIcon: const Icon(Icons.close, size: 18),
                    onDeleted: () => _removeKeyword(keyword),
                  );
                }).toList(),
              ),
          ],
        ),
      ),
    );
  }

  void _addKeyword(String keyword) {
    final trimmedKeyword = keyword.trim().toLowerCase();
    if (trimmedKeyword.isNotEmpty &&
        !_blockedKeywords.contains(trimmedKeyword)) {
      setState(() {
        _blockedKeywords.add(trimmedKeyword);
        _keywordController.clear();
      });
    }
  }

  void _removeKeyword(String keyword) {
    setState(() {
      _blockedKeywords.remove(keyword);
    });
  }
}
