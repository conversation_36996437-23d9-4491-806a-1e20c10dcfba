import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:billionaires_social/features/profile/models/profile_verification_model.dart';
import 'package:billionaires_social/features/profile/widgets/verification_step_widgets.dart';
import 'package:billionaires_social/core/app_theme.dart';

/// Screen for handling specific verification flows
class VerificationFlowScreen extends ConsumerStatefulWidget {
  final VerificationType type;

  const VerificationFlowScreen({super.key, required this.type});

  @override
  ConsumerState<VerificationFlowScreen> createState() =>
      _VerificationFlowScreenState();
}

class _VerificationFlowScreenState
    extends ConsumerState<VerificationFlowScreen> {
  int _currentStep = 0;
  bool _isLoading = false;
  final Map<String, dynamic> _formData = {};

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.luxuryBlack,
      appBar: AppBar(
        backgroundColor: AppTheme.luxuryBlack,
        title: Text(
          '${_getTypeTitle(widget.type)} Verification',
          style: AppTheme.fontStyles.title.copyWith(
            color: AppTheme.luxuryWhite,
          ),
        ),
      ),
      body: _buildVerificationFlow(),
    );
  }

  Widget _buildVerificationFlow() {
    switch (widget.type) {
      case VerificationType.email:
        return _buildEmailVerification();
      case VerificationType.phone:
        return _buildPhoneVerification();
      case VerificationType.identity:
        return _buildIdentityVerification();
      case VerificationType.business:
        return _buildBusinessVerification();
      case VerificationType.billionaire:
        return _buildBillionairVerification();
      case VerificationType.celebrity:
        return _buildCelebrityVerification();
    }
  }

  Widget _buildEmailVerification() {
    return Stepper(
      currentStep: _currentStep,
      onStepTapped: (step) => setState(() => _currentStep = step),
      controlsBuilder: (context, details) => _buildStepControls(details),
      steps: [
        Step(
          title: Text(
            'Enter Email',
            style: AppTheme.fontStyles.bodyBold.copyWith(
              color: AppTheme.luxuryWhite,
            ),
          ),
          content: EmailInputStep(
            onEmailChanged: (email) => _formData['email'] = email,
          ),
          isActive: _currentStep >= 0,
        ),
        Step(
          title: Text(
            'Verify Code',
            style: AppTheme.fontStyles.bodyBold.copyWith(
              color: AppTheme.luxuryWhite,
            ),
          ),
          content: VerificationCodeStep(
            type: 'email',
            onCodeChanged: (code) => _formData['code'] = code,
          ),
          isActive: _currentStep >= 1,
        ),
        Step(
          title: Text(
            'Complete',
            style: AppTheme.fontStyles.bodyBold.copyWith(
              color: AppTheme.luxuryWhite,
            ),
          ),
          content: CompletionStep(type: widget.type),
          isActive: _currentStep >= 2,
        ),
      ],
    );
  }

  Widget _buildPhoneVerification() {
    return Stepper(
      currentStep: _currentStep,
      onStepTapped: (step) => setState(() => _currentStep = step),
      controlsBuilder: (context, details) => _buildStepControls(details),
      steps: [
        Step(
          title: Text(
            'Enter Phone Number',
            style: AppTheme.fontStyles.bodyBold.copyWith(
              color: AppTheme.luxuryWhite,
            ),
          ),
          content: PhoneInputStep(
            onPhoneChanged: (phone) => _formData['phone'] = phone,
          ),
          isActive: _currentStep >= 0,
        ),
        Step(
          title: Text(
            'Verify SMS Code',
            style: AppTheme.fontStyles.bodyBold.copyWith(
              color: AppTheme.luxuryWhite,
            ),
          ),
          content: VerificationCodeStep(
            type: 'SMS',
            onCodeChanged: (code) => _formData['code'] = code,
          ),
          isActive: _currentStep >= 1,
        ),
        Step(
          title: Text(
            'Complete',
            style: AppTheme.fontStyles.bodyBold.copyWith(
              color: AppTheme.luxuryWhite,
            ),
          ),
          content: CompletionStep(type: widget.type),
          isActive: _currentStep >= 2,
        ),
      ],
    );
  }

  Widget _buildIdentityVerification() {
    return Stepper(
      currentStep: _currentStep,
      onStepTapped: (step) => setState(() => _currentStep = step),
      controlsBuilder: (context, details) => _buildStepControls(details),
      steps: [
        Step(
          title: Text(
            'Personal Information',
            style: AppTheme.fontStyles.bodyBold.copyWith(
              color: AppTheme.luxuryWhite,
            ),
          ),
          content: PersonalInfoStep(
            onDataChanged: (data) => _formData.addAll(data),
          ),
          isActive: _currentStep >= 0,
        ),
        Step(
          title: Text(
            'Upload ID Document',
            style: AppTheme.fontStyles.bodyBold.copyWith(
              color: AppTheme.luxuryWhite,
            ),
          ),
          content: DocumentUploadStep(
            documentType: 'Government ID',
            onDocumentUploaded: (path) => _formData['idDocument'] = path,
          ),
          isActive: _currentStep >= 1,
        ),
        Step(
          title: Text(
            'Selfie Verification',
            style: AppTheme.fontStyles.bodyBold.copyWith(
              color: AppTheme.luxuryWhite,
            ),
          ),
          content: SelfieVerificationStep(
            onSelfieUploaded: (path) => _formData['selfie'] = path,
          ),
          isActive: _currentStep >= 2,
        ),
        Step(
          title: Text(
            'Review & Submit',
            style: AppTheme.fontStyles.bodyBold.copyWith(
              color: AppTheme.luxuryWhite,
            ),
          ),
          content: ReviewStep(formData: _formData),
          isActive: _currentStep >= 3,
        ),
      ],
    );
  }

  Widget _buildBusinessVerification() {
    return Stepper(
      currentStep: _currentStep,
      onStepTapped: (step) => setState(() => _currentStep = step),
      controlsBuilder: (context, details) => _buildStepControls(details),
      steps: [
        Step(
          title: Text(
            'Business Information',
            style: AppTheme.fontStyles.bodyBold.copyWith(
              color: AppTheme.luxuryWhite,
            ),
          ),
          content: BusinessInfoStep(
            onDataChanged: (data) => _formData.addAll(data),
          ),
          isActive: _currentStep >= 0,
        ),
        Step(
          title: Text(
            'Business Documents',
            style: AppTheme.fontStyles.bodyBold.copyWith(
              color: AppTheme.luxuryWhite,
            ),
          ),
          content: BusinessDocumentsStep(
            onDocumentsUploaded: (docs) => _formData['documents'] = docs,
          ),
          isActive: _currentStep >= 1,
        ),
        Step(
          title: Text(
            'Review & Submit',
            style: AppTheme.fontStyles.bodyBold.copyWith(
              color: AppTheme.luxuryWhite,
            ),
          ),
          content: ReviewStep(formData: _formData),
          isActive: _currentStep >= 2,
        ),
      ],
    );
  }

  Widget _buildBillionairVerification() {
    return _buildPremiumVerificationFlow(
      title: 'Billionaire Status Verification',
      description:
          'Exclusive verification for ultra-high net worth individuals',
      requirements: [
        'Net worth verification documents',
        'Financial statements from certified accountant',
        'Asset documentation',
        'Investment portfolio proof',
      ],
    );
  }

  Widget _buildCelebrityVerification() {
    return _buildPremiumVerificationFlow(
      title: 'Celebrity Verification',
      description: 'Verification for public figures and celebrities',
      requirements: [
        'Media coverage or press articles',
        'Social media verification on other platforms',
        'Professional representation contact',
        'Public recognition documentation',
      ],
    );
  }

  Widget _buildPremiumVerificationFlow({
    required String title,
    required String description,
    required List<String> requirements,
  }) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: AppTheme.accentColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppTheme.accentColor.withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    FaIcon(
                      FontAwesomeIcons.crown,
                      color: AppTheme.accentColor,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      title,
                      style: AppTheme.fontStyles.title.copyWith(
                        color: AppTheme.accentColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  description,
                  style: AppTheme.fontStyles.body.copyWith(
                    color: AppTheme.secondaryAccentColor,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          Text(
            'Requirements',
            style: AppTheme.fontStyles.title.copyWith(
              color: AppTheme.luxuryWhite,
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 16),

          ...requirements.map(
            (requirement) => Container(
              margin: const EdgeInsets.only(bottom: 12),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  FaIcon(
                    FontAwesomeIcons.circleCheck,
                    color: AppTheme.accentColor,
                    size: 16,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      requirement,
                      style: AppTheme.fontStyles.body.copyWith(
                        color: AppTheme.luxuryWhite,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 32),

          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const FaIcon(
                      FontAwesomeIcons.triangleExclamation,
                      color: Colors.orange,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Manual Review Process',
                      style: AppTheme.fontStyles.bodyBold.copyWith(
                        color: Colors.orange,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  'This verification requires manual review by our team. Please allow 5-10 business days for processing.',
                  style: AppTheme.fontStyles.caption.copyWith(
                    color: AppTheme.secondaryAccentColor,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 32),

          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _isLoading ? null : () => _submitPremiumVerification(),
              icon: _isLoading
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const FaIcon(FontAwesomeIcons.paperPlane, size: 16),
              label: Text(_isLoading ? 'Submitting...' : 'Submit Application'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.accentColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStepControls(ControlsDetails details) {
    return Row(
      children: [
        if (details.stepIndex > 0)
          TextButton(
            onPressed: details.onStepCancel,
            child: Text(
              'Back',
              style: TextStyle(color: AppTheme.secondaryAccentColor),
            ),
          ),
        const SizedBox(width: 8),
        ElevatedButton(
          onPressed: _isLoading ? null : details.onStepContinue,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.accentColor,
            foregroundColor: Colors.white,
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : Text(
                  details.stepIndex == _getMaxSteps() - 1
                      ? 'Submit'
                      : 'Continue',
                ),
        ),
      ],
    );
  }

  int _getMaxSteps() {
    switch (widget.type) {
      case VerificationType.email:
      case VerificationType.phone:
        return 3;
      case VerificationType.identity:
        return 4;
      case VerificationType.business:
        return 3;
      case VerificationType.billionaire:
      case VerificationType.celebrity:
        return 1; // Single step for premium verifications
    }
  }

  String _getTypeTitle(VerificationType type) {
    switch (type) {
      case VerificationType.email:
        return 'Email';
      case VerificationType.phone:
        return 'Phone';
      case VerificationType.identity:
        return 'Identity';
      case VerificationType.business:
        return 'Business';
      case VerificationType.billionaire:
        return 'Billionaire';
      case VerificationType.celebrity:
        return 'Celebrity';
    }
  }

  Future<void> _submitPremiumVerification() async {
    setState(() => _isLoading = true);

    try {
      // TODO: Implement actual submission logic
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '${_getTypeTitle(widget.type)} verification submitted successfully',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to submit verification: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
