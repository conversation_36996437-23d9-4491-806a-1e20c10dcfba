import 'package:billionaires_social/core/app_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:image_picker/image_picker.dart';
import 'package:billionaires_social/features/profile/models/profile_model.dart';
import 'package:billionaires_social/features/profile/services/profile_service.dart';
import 'package:billionaires_social/features/profile/providers/profile_provider.dart';
import 'package:billionaires_social/features/stories/providers/story_provider.dart';
import 'package:billionaires_social/features/feed/providers/feed_provider.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/core/services/cache_service.dart';
import 'dart:io';
import 'package:billionaires_social/features/profile/providers/profile_verification_provider.dart';
import 'package:billionaires_social/features/profile/models/profile_verification_model.dart';

class EditProfileScreen extends ConsumerStatefulWidget {
  const EditProfileScreen({super.key});

  @override
  ConsumerState<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends ConsumerState<EditProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _usernameController = TextEditingController();
  final _bioController = TextEditingController();
  final _websiteController = TextEditingController();
  final _locationController = TextEditingController();
  final _phoneController = TextEditingController();

  File? _selectedAvatarImage;
  File? _selectedBannerImage;
  bool _isLoading = false;
  bool _isPrivate = false;
  bool _allowMessages = true;
  bool _showActivityStatus = true;
  ProfileModel? _currentProfile;

  // Advanced privacy settings
  String _privacyLevel = 'public';
  bool _temporarilyHidden = false;
  bool _enableProfileViewHistory = false;

  @override
  void initState() {
    super.initState();
    _loadCurrentProfile();
  }

  Future<void> _loadCurrentProfile() async {
    final profileService = getIt<ProfileService>();
    final profile = await profileService.getProfile();

    setState(() {
      _currentProfile = profile;
      _nameController.text = profile.name;
      _usernameController.text = profile.username;
      _bioController.text = profile.bio;
      _websiteController.text = profile.website ?? '';
      _locationController.text = profile.location ?? '';
      _phoneController.text = profile.phone ?? '';
      _isPrivate = profile.isPrivate;
      _allowMessages = profile.allowMessages;
      _showActivityStatus = profile.showActivityStatus;
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    _usernameController.dispose();
    _bioController.dispose();
    _websiteController.dispose();
    _locationController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  Future<void> _pickImage({bool isBanner = false}) async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
        maxWidth: 1024,
        maxHeight: 1024,
      );

      if (pickedFile != null) {
        debugPrint('📸 Image picked: ${pickedFile.path}');

        // Verify file exists and is readable
        final file = File(pickedFile.path);
        if (await file.exists()) {
          final fileSize = await file.length();
          debugPrint('📸 File size: $fileSize bytes');

          setState(() {
            if (isBanner) {
              _selectedBannerImage = file;
              debugPrint('🖼️ Banner image selected');
            } else {
              _selectedAvatarImage = file;
              debugPrint('👤 Avatar image selected');
            }
          });
        } else {
          throw Exception('Selected file does not exist or is not accessible');
        }
      } else {
        debugPrint('📸 No image selected');
      }
    } catch (e) {
      debugPrint('❌ Error picking image: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error selecting image: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Edit Profile'),
        backgroundColor: AppTheme.primaryColor,
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveProfile,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : Text(
                    'Save',
                    style: AppTheme.fontStyles.bodyBold.copyWith(
                      color: AppTheme.accentColor,
                    ),
                  ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildBannerSection(),
              const SizedBox(height: 16),
              _buildVerificationStatusSection(context),
              const SizedBox(height: 24),
              _buildProfilePictureSection(),
              const SizedBox(height: 24),
              _buildBasicInfoSection(),
              const SizedBox(height: 24),
              _buildContactInfoSection(),
              const SizedBox(height: 24),
              _buildPrivacySection(),
              const SizedBox(height: 24),
              _buildAccountSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBannerSection() {
    return Center(
      child: Column(
        children: [
          Container(
            width: double.infinity,
            height: 120,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppTheme.accentColor.withValues(alpha: 0.3),
                width: 2,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: _selectedBannerImage != null
                  ? Image.file(_selectedBannerImage!, fit: BoxFit.cover)
                  : _currentProfile?.bannerImageUrl != null
                  ? CachedNetworkImage(
                      imageUrl: _currentProfile!.bannerImageUrl!,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
                        child: const Center(child: CircularProgressIndicator()),
                      ),
                    )
                  : Container(
                      color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          FaIcon(
                            FontAwesomeIcons.image,
                            size: 32,
                            color: AppTheme.accentColor.withValues(alpha: 0.5),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Add Banner Image',
                            style: AppTheme.fontStyles.caption.copyWith(
                              color: AppTheme.accentColor,
                            ),
                          ),
                        ],
                      ),
                    ),
            ),
          ),
          const SizedBox(height: 8),
          TextButton(
            onPressed: () => _pickImage(isBanner: true),
            child: Text(
              'Change Banner Image',
              style: AppTheme.fontStyles.body.copyWith(
                color: AppTheme.accentColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfilePictureSection() {
    return Center(
      child: Column(
        children: [
          Stack(
            children: [
              CircleAvatar(
                radius: 60,
                backgroundImage: _selectedAvatarImage != null
                    ? FileImage(_selectedAvatarImage!)
                    : _currentProfile?.profilePictureUrl != null
                    ? CachedNetworkImageProvider(
                        _currentProfile!.profilePictureUrl,
                      )
                    : null,
                backgroundColor: AppTheme.luxuryGrey,
                child:
                    _selectedAvatarImage == null &&
                        _currentProfile?.profilePictureUrl == null
                    ? FaIcon(
                        FontAwesomeIcons.user,
                        size: 48,
                        color: AppTheme.accentColor.withValues(alpha: 0.5),
                      )
                    : null,
              ),
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppTheme.accentColor,
                    shape: BoxShape.circle,
                    border: Border.all(color: AppTheme.primaryColor, width: 3),
                  ),
                  child: const FaIcon(
                    FontAwesomeIcons.camera,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          TextButton(
            onPressed: () => _pickImage(isBanner: false),
            child: Text(
              'Change Profile Photo',
              style: AppTheme.fontStyles.body.copyWith(
                color: AppTheme.accentColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVerificationStatusSection(BuildContext context) {
    return Consumer(
      builder: (context, ref, _) {
        final verificationsAsync = ref.watch(profileVerificationsProvider);
        return verificationsAsync.when(
          data: (verifications) {
            final types = [
              VerificationType.email,
              VerificationType.phone,
              VerificationType.identity,
              VerificationType.business,
            ];
            return Card(
              color: AppTheme.luxuryGrey.withValues(alpha: 0.08),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(
                          FontAwesomeIcons.shieldHalved,
                          color: AppTheme.accentColor,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Verification Status',
                          style: AppTheme.fontStyles.title.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    ...types.map((type) {
                      final v = verifications
                          .where((e) => e.type == type)
                          .toList();
                      final status = v.isNotEmpty
                          ? v.first.status
                          : VerificationStatus.pending;
                      final isVerified = status == VerificationStatus.approved;
                      final label = _verificationTypeLabel(type);
                      final icon = _verificationTypeIcon(type);
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4),
                        child: Row(
                          children: [
                            Icon(
                              icon,
                              color: isVerified
                                  ? Colors.green
                                  : AppTheme.accentColor,
                              size: 18,
                            ),
                            const SizedBox(width: 8),
                            Text(label, style: AppTheme.fontStyles.bodyBold),
                            const SizedBox(width: 8),
                            Chip(
                              label: Text(_verificationStatusLabel(status)),
                              backgroundColor: isVerified
                                  ? Colors.green.withValues(alpha: 0.15)
                                  : Colors.orange.withValues(alpha: 0.15),
                              labelStyle: TextStyle(
                                color: isVerified
                                    ? Colors.green
                                    : Colors.orange,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const Spacer(),
                            if (!isVerified)
                              TextButton(
                                onPressed: () {
                                  // TODO: Implement verification flow for this type
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                        'Start ${label.toLowerCase()} verification (not implemented)',
                                      ),
                                    ),
                                  );
                                },
                                child: const Text('Verify'),
                              ),
                          ],
                        ),
                      );
                    }),
                  ],
                ),
              ),
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (e, s) => Container(),
        );
      },
    );
  }

  String _verificationTypeLabel(VerificationType type) {
    switch (type) {
      case VerificationType.email:
        return 'Email';
      case VerificationType.phone:
        return 'Phone';
      case VerificationType.identity:
        return 'Identity';
      case VerificationType.business:
        return 'Business';
      case VerificationType.celebrity:
        return 'Celebrity';
      case VerificationType.billionaire:
        return 'Billionaire';
    }
  }

  IconData _verificationTypeIcon(VerificationType type) {
    switch (type) {
      case VerificationType.email:
        return FontAwesomeIcons.envelope;
      case VerificationType.phone:
        return FontAwesomeIcons.phone;
      case VerificationType.identity:
        return FontAwesomeIcons.idCard;
      case VerificationType.business:
        return FontAwesomeIcons.building;
      case VerificationType.celebrity:
        return FontAwesomeIcons.star;
      case VerificationType.billionaire:
        return FontAwesomeIcons.gem;
    }
  }

  String _verificationStatusLabel(VerificationStatus status) {
    switch (status) {
      case VerificationStatus.pending:
        return 'Pending';
      case VerificationStatus.submitted:
        return 'Submitted';
      case VerificationStatus.underReview:
        return 'Under Review';
      case VerificationStatus.approved:
        return 'Verified';
      case VerificationStatus.rejected:
        return 'Rejected';
      case VerificationStatus.expired:
        return 'Expired';
    }
  }

  Widget _buildSectionHeader(String title) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 24,
            decoration: BoxDecoration(
              color: AppTheme.accentColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 12),
          Text(
            title,
            style: AppTheme.fontStyles.title.copyWith(
              color: AppTheme.luxuryWhite,
              fontWeight: FontWeight.bold,
              fontSize: 20,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Basic Information'),
        const SizedBox(height: 20),
        _buildTextField(
          controller: _nameController,
          label: 'Full Name',
          icon: FontAwesomeIcons.user,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your name';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        _buildTextField(
          controller: _usernameController,
          label: 'Username',
          icon: FontAwesomeIcons.at,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter a username';
            }
            if (value.length < 3) {
              return 'Username must be at least 3 characters';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        _buildTextField(
          controller: _bioController,
          label: 'Bio',
          icon: FontAwesomeIcons.pen,
          maxLines: 3,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter a bio';
            }
            if (value.length > 150) {
              return 'Bio must be less than 150 characters';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildContactInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Contact Information'),
        const SizedBox(height: 20),
        _buildTextField(
          controller: _websiteController,
          label: 'Website',
          icon: FontAwesomeIcons.globe,
        ),
        const SizedBox(height: 16),
        _buildTextField(
          controller: _locationController,
          label: 'Location',
          icon: FontAwesomeIcons.locationDot,
        ),
        const SizedBox(height: 16),
        _buildTextField(
          controller: _phoneController,
          label: 'Phone Number',
          icon: FontAwesomeIcons.phone,
        ),
      ],
    );
  }

  Widget _buildPrivacySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Privacy Settings'),
        const SizedBox(height: 20),
        _buildSwitchTile(
          'Private Account',
          'Only approved followers can see your posts',
          FontAwesomeIcons.lock,
          _isPrivate,
          (value) {
            setState(() {
              _isPrivate = value;
            });
          },
        ),
        _buildSwitchTile(
          'Allow Messages',
          'Let others send you direct messages',
          FontAwesomeIcons.envelope,
          _allowMessages,
          (value) {
            setState(() {
              _allowMessages = value;
            });
          },
        ),
        _buildSwitchTile(
          'Show Activity Status',
          'Let others see when you\'re active',
          FontAwesomeIcons.circle,
          _showActivityStatus,
          (value) {
            setState(() {
              _showActivityStatus = value;
            });
          },
        ),
        const SizedBox(height: 16),
        Text(
          'Advanced Privacy',
          style: AppTheme.fontStyles.subtitle.copyWith(
            color: AppTheme.accentColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        _buildPrivacyLevelDropdown(),
        const SizedBox(height: 8),
        _buildSwitchTile(
          'Temporarily Hide Profile',
          'Hide your profile from everyone for a limited time',
          FontAwesomeIcons.eyeSlash,
          _temporarilyHidden,
          (value) {
            setState(() {
              _temporarilyHidden = value;
            });
          },
        ),
        _buildSwitchTile(
          'Enable Profile View History',
          'See who viewed your profile (if enabled for both parties)',
          FontAwesomeIcons.clockRotateLeft,
          _enableProfileViewHistory,
          (value) {
            setState(() {
              _enableProfileViewHistory = value;
            });
          },
        ),
        _buildListTile('Blocked Users', FontAwesomeIcons.userXmark, () {
          // TODO: Navigate to Blocked Users Screen
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Blocked Users screen (not implemented)'),
            ),
          );
        }),
      ],
    );
  }

  Widget _buildPrivacyLevelDropdown() {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.accentColor.withValues(alpha: 0.2)),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        child: DropdownButtonFormField<String>(
          value: _privacyLevel,
          decoration: const InputDecoration(
            labelText: 'Profile Privacy Level',
            border: InputBorder.none,
          ),
          items: const [
            DropdownMenuItem(value: 'public', child: Text('Public')),
            DropdownMenuItem(value: 'private', child: Text('Private')),
            DropdownMenuItem(value: 'friends', child: Text('Friends Only')),
            DropdownMenuItem(value: 'custom', child: Text('Custom List')),
          ],
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _privacyLevel = value;
              });
            }
          },
        ),
      ),
    );
  }

  Widget _buildAccountSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Account'),
        const SizedBox(height: 20),
        _buildListTile('Change Password', FontAwesomeIcons.key, () {}),
        _buildListTile(
          'Two-Factor Authentication',
          FontAwesomeIcons.shield,
          () {},
        ),
        _buildListTile('Account Privacy', FontAwesomeIcons.userShield, () {}),
        _buildListTile(
          'Deactivate Account',
          FontAwesomeIcons.userXmark,
          () {},
          isDestructive: true,
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        maxLines: maxLines,
        validator: validator,
        style: AppTheme.fontStyles.body.copyWith(
          color: AppTheme.luxuryWhite,
          fontWeight: FontWeight.w500,
        ),
        decoration: InputDecoration(
          labelText: label,
          labelStyle: AppTheme.fontStyles.body.copyWith(
            color: AppTheme.accentColor.withValues(alpha: 0.8),
            fontWeight: FontWeight.w600,
          ),
          prefixIcon: Container(
            margin: const EdgeInsets.all(12),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppTheme.accentColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: FaIcon(icon, color: AppTheme.accentColor, size: 18),
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(
              color: AppTheme.accentColor.withValues(alpha: 0.5),
              width: 1.5,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(
              color: AppTheme.accentColor.withValues(alpha: 0.5),
              width: 1.5,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(color: AppTheme.accentColor, width: 2),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: const BorderSide(color: Colors.red, width: 1.5),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          filled: true,
          fillColor: AppTheme.luxuryGrey.withValues(alpha: 0.3),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 16,
          ),
        ),
      ),
    );
  }

  Widget _buildSwitchTile(
    String title,
    String subtitle,
    IconData icon,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: AppTheme.luxuryGrey.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.accentColor.withValues(alpha: 0.4),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SwitchListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
        title: Text(
          title,
          style: AppTheme.fontStyles.bodyBold.copyWith(
            color: AppTheme.luxuryWhite,
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: AppTheme.fontStyles.caption.copyWith(
            color: AppTheme.secondaryAccentColor.withValues(alpha: 0.9),
            fontWeight: FontWeight.w400,
          ),
        ),
        secondary: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppTheme.accentColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: FaIcon(icon, color: AppTheme.accentColor, size: 20),
        ),
        value: value,
        onChanged: onChanged,
        activeColor: AppTheme.accentColor,
        activeTrackColor: AppTheme.accentColor.withValues(alpha: 0.3),
        inactiveThumbColor: AppTheme.secondaryAccentColor.withValues(
          alpha: 0.7,
        ),
        inactiveTrackColor: AppTheme.luxuryGrey.withValues(alpha: 0.5),
      ),
    );
  }

  Widget _buildListTile(
    String title,
    IconData icon,
    VoidCallback onTap, {
    bool isDestructive = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: AppTheme.luxuryGrey.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isDestructive
              ? Colors.red.withValues(alpha: 0.4)
              : AppTheme.accentColor.withValues(alpha: 0.4),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 20,
          vertical: 12,
        ),
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: isDestructive
                ? Colors.red.withValues(alpha: 0.1)
                : AppTheme.accentColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: FaIcon(
            icon,
            color: isDestructive ? Colors.red : AppTheme.accentColor,
            size: 20,
          ),
        ),
        title: Text(
          title,
          style: AppTheme.fontStyles.body.copyWith(
            color: isDestructive ? Colors.red : AppTheme.luxuryWhite,
            fontWeight: FontWeight.w600,
          ),
        ),
        trailing: Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: AppTheme.accentColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: FaIcon(
            FontAwesomeIcons.arrowRight,
            color: AppTheme.accentColor.withValues(alpha: 0.8),
            size: 14,
          ),
        ),
        onTap: onTap,
      ),
    );
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final profileService = getIt<ProfileService>();

      // Upload images if selected
      String? newAvatarUrl;
      String? newBannerUrl;

      if (_selectedAvatarImage != null) {
        debugPrint('📸 Uploading new avatar image...');
        newAvatarUrl = await profileService.uploadProfileImage(
          _selectedAvatarImage!.path,
          isBanner: false,
        );
        debugPrint('✅ New avatar URL: $newAvatarUrl');
      }

      if (_selectedBannerImage != null) {
        debugPrint('🖼️ Uploading new banner image...');
        newBannerUrl = await profileService.uploadProfileImage(
          _selectedBannerImage!.path,
          isBanner: true,
        );
        debugPrint('✅ New banner URL: $newBannerUrl');
      }

      // Create updated profile
      final updatedProfile = ProfileModel(
        id: _currentProfile?.id ?? '',
        username: _usernameController.text.trim(),
        name: _nameController.text.trim(),
        bio: _bioController.text.trim(),
        profilePictureUrl:
            newAvatarUrl ?? _currentProfile?.profilePictureUrl ?? '',
        bannerImageUrl: newBannerUrl ?? _currentProfile?.bannerImageUrl,
        website: _websiteController.text.trim().isEmpty
            ? null
            : _websiteController.text.trim(),
        location: _locationController.text.trim().isEmpty
            ? null
            : _locationController.text.trim(),
        phone: _phoneController.text.trim().isEmpty
            ? null
            : _phoneController.text.trim(),
        email: _currentProfile?.email,
        isPrivate: _isPrivate,
        allowMessages: _allowMessages,
        showActivityStatus: _showActivityStatus,
        isVerified: _currentProfile?.isVerified ?? false,
        isBillionaire: _currentProfile?.isBillionaire ?? false,
        userType: _currentProfile?.userType ?? 'regular',
        postCount: _currentProfile?.postCount ?? 0,
        followerCount: _currentProfile?.followerCount ?? 0,
        followingCount: _currentProfile?.followingCount ?? 0,
        createdAt: _currentProfile?.createdAt,
        updatedAt: DateTime.now(),
      );

      debugPrint('📝 Profile update data:');
      debugPrint('  - Old avatar URL: ${_currentProfile?.profilePictureUrl}');
      debugPrint('  - New avatar URL: ${updatedProfile.profilePictureUrl}');
      debugPrint('  - Username: ${updatedProfile.username}');
      debugPrint('  - Name: ${updatedProfile.name}');

      // Save to Firebase
      final success = await profileService.updateProfile(updatedProfile);
      debugPrint('🔥 Firebase update success: $success');

      if (success) {
        // Clear cache and refresh providers
        final cacheService = getIt<CacheService>();
        final currentUserId = profileService.getCurrentUserId();
        if (currentUserId != null) {
          await cacheService.removeData('user_profile_$currentUserId');
        }

        // Clear CachedNetworkImage cache for the old profile picture URL
        if (_currentProfile?.profilePictureUrl != null &&
            newAvatarUrl != null) {
          await CachedNetworkImage.evictFromCache(
            _currentProfile!.profilePictureUrl,
          );
        }

        // Clear all profile picture caches to ensure fresh images are loaded
        await _clearProfilePictureCaches();

        // Force clear all caches and refresh providers
        await _forceRefreshAllData(currentUserId!);

        // Invalidate Riverpod providers to force refresh
        ref.invalidate(userProfileProvider);
        ref.invalidate(profileProvider(currentUserId));
        ref.invalidate(storyReelsProvider);
        ref.read(feedProvider.notifier).refresh();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Profile updated successfully!'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context);
        }
      } else {
        throw Exception('Failed to update profile');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating profile: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _clearProfilePictureCaches() async {
    try {
      // Clear the old profile picture cache if it exists
      final profilePictureUrl = _currentProfile?.profilePictureUrl;
      if (profilePictureUrl != null && profilePictureUrl.isNotEmpty) {
        await CachedNetworkImage.evictFromCache(profilePictureUrl);
      }

      // Clear the banner image cache if it exists
      final bannerImageUrl = _currentProfile?.bannerImageUrl;
      if (bannerImageUrl != null && bannerImageUrl.isNotEmpty) {
        await CachedNetworkImage.evictFromCache(bannerImageUrl);
      }

      debugPrint('✅ Cleared profile picture caches');
    } catch (e) {
      debugPrint('❌ Error clearing profile picture caches: $e');
    }
  }

  Future<void> _forceRefreshAllData(String userId) async {
    try {
      debugPrint('🔄 Force refreshing all data for user: $userId');

      final cacheService = getIt<CacheService>();

      // Clear all user-related caches
      await cacheService.removeData('user_profile_$userId');

      // Clear CachedNetworkImage cache completely
      await CachedNetworkImage.evictFromCache('');

      debugPrint('✅ All caches cleared and data refreshed');
    } catch (e) {
      debugPrint('❌ Error force refreshing data: $e');
    }
  }
}
