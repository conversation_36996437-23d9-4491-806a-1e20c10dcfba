import 'package:billionaires_social/core/app_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:billionaires_social/core/utils/dialog_utils.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/core/services/cache_service.dart';
import 'package:billionaires_social/features/profile/models/account_model.dart';
import 'package:billionaires_social/features/profile/providers/account_management_provider.dart';
import 'package:billionaires_social/features/profile/providers/profile_provider.dart';
import 'package:billionaires_social/features/profile/screens/create_account_screen.dart';
import 'package:billionaires_social/features/profile/services/account_management_service.dart';
import 'package:billionaires_social/features/feed/providers/feed_provider.dart';
import 'package:billionaires_social/features/auth/providers/auth_provider.dart'
    as auth;

class AccountSwitcherScreen extends ConsumerStatefulWidget {
  const AccountSwitcherScreen({super.key});

  @override
  ConsumerState<AccountSwitcherScreen> createState() =>
      _AccountSwitcherScreenState();
}

class _AccountSwitcherScreenState extends ConsumerState<AccountSwitcherScreen> {
  @override
  Widget build(BuildContext context) {
    final accountsAsync = ref.watch(accountManagementNotifierProvider);
    final activeAccountAsync = ref.watch(activeAccountNotifierProvider);

    return Scaffold(
      backgroundColor: AppTheme.primaryColor,
      appBar: AppBar(
        title: Text(
          'Switch Account',
          style: AppTheme.fontStyles.title.copyWith(
            color: AppTheme.accentColor,
          ),
        ),
        backgroundColor: AppTheme.primaryColor,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _addNewAccount,
            icon: const FaIcon(
              FontAwesomeIcons.plus,
              color: AppTheme.accentColor,
            ),
          ),
        ],
      ),
      body: accountsAsync.when(
        data: (accounts) => _buildAccountsContent(accounts, activeAccountAsync),
        loading: () => const Center(
          child: CircularProgressIndicator(color: AppTheme.accentColor),
        ),
        error: (error, stack) => _buildErrorState(error),
      ),
    );
  }

  Widget _buildAccountsContent(
    List<AccountModel> accounts,
    AsyncValue<AccountModel?> activeAccountAsync,
  ) {
    return Column(
      children: [
        activeAccountAsync.when(
          data: (activeAccount) => activeAccount != null
              ? _buildCurrentAccount(activeAccount)
              : const SizedBox.shrink(),
          loading: () => const SizedBox.shrink(),
          error: (_, _) => const SizedBox.shrink(),
        ),
        const Divider(height: 32, color: AppTheme.luxuryGrey),
        _buildAccountsList(accounts),
        const Spacer(),
        _buildBottomActions(),
      ],
    );
  }

  Widget _buildErrorState(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const FaIcon(
            FontAwesomeIcons.triangleExclamation,
            color: Colors.red,
            size: 48,
          ),
          const SizedBox(height: 16),
          Text(
            'Failed to load accounts',
            style: AppTheme.fontStyles.bodyBold.copyWith(color: Colors.red),
          ),
          const SizedBox(height: 8),
          Text(
            error.toString(),
            style: AppTheme.fontStyles.caption.copyWith(
              color: AppTheme.secondaryAccentColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              ref.invalidate(accountManagementNotifierProvider);
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentAccount(AccountModel currentAccount) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.accentColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.accentColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 30,
            backgroundImage: currentAccount.profilePictureUrl.isNotEmpty
                ? CachedNetworkImageProvider(currentAccount.profilePictureUrl)
                : null,
            child: currentAccount.profilePictureUrl.isEmpty
                ? Text(
                    currentAccount.name.isNotEmpty
                        ? currentAccount.name[0].toUpperCase()
                        : '?',
                    style: AppTheme.fontStyles.bodyBold.copyWith(
                      color: AppTheme.accentColor,
                    ),
                  )
                : null,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  currentAccount.name,
                  style: AppTheme.fontStyles.bodyBold.copyWith(
                    color: AppTheme.accentColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '@${currentAccount.username}',
                  style: AppTheme.fontStyles.caption.copyWith(
                    color: AppTheme.secondaryAccentColor,
                  ),
                ),
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.accentColor.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'Current Account',
                    style: AppTheme.fontStyles.caption.copyWith(
                      color: AppTheme.accentColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
          _buildAccountTypeIcon(currentAccount.accountType),
        ],
      ),
    );
  }

  Widget _buildAccountsList(List<AccountModel> accounts) {
    final inactiveAccounts = accounts
        .where((account) => !account.isActive)
        .toList();

    if (inactiveAccounts.isEmpty) {
      return Expanded(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const FaIcon(
                FontAwesomeIcons.userPlus,
                color: AppTheme.secondaryAccentColor,
                size: 48,
              ),
              const SizedBox(height: 16),
              Text(
                'No other accounts',
                style: AppTheme.fontStyles.bodyBold.copyWith(
                  color: AppTheme.secondaryAccentColor,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Create additional accounts to switch between them',
                style: AppTheme.fontStyles.caption.copyWith(
                  color: AppTheme.secondaryAccentColor,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Expanded(
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: inactiveAccounts.length,
        itemBuilder: (context, index) {
          final account = inactiveAccounts[index];
          return _buildAccountTile(account);
        },
      ),
    );
  }

  Widget _buildAccountTile(AccountModel account) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.accentColor.withValues(alpha: 0.2)),
      ),
      child: ListTile(
        leading: CircleAvatar(
          radius: 25,
          backgroundImage: account.profilePictureUrl.isNotEmpty
              ? CachedNetworkImageProvider(account.profilePictureUrl)
              : null,
          child: account.profilePictureUrl.isEmpty
              ? Text(
                  account.name.isNotEmpty ? account.name[0].toUpperCase() : '?',
                  style: AppTheme.fontStyles.bodyBold.copyWith(
                    color: AppTheme.accentColor,
                  ),
                )
              : null,
        ),
        title: Text(
          account.name,
          style: AppTheme.fontStyles.bodyBold.copyWith(
            color: AppTheme.accentColor,
          ),
        ),
        subtitle: Text(
          '@${account.username}',
          style: AppTheme.fontStyles.caption.copyWith(
            color: AppTheme.secondaryAccentColor,
          ),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildAccountTypeIcon(account.accountType),
            const SizedBox(width: 8),
            PopupMenuButton<String>(
              icon: const FaIcon(FontAwesomeIcons.ellipsis),
              onSelected: (value) => _handleAccountAction(value, account),
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'switch',
                  child: Row(
                    children: [
                      FaIcon(FontAwesomeIcons.arrowRightArrowLeft),
                      SizedBox(width: 8),
                      Text('Switch to this account'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      FaIcon(FontAwesomeIcons.pen),
                      SizedBox(width: 8),
                      Text('Edit Account'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'remove',
                  child: Row(
                    children: [
                      FaIcon(FontAwesomeIcons.trash),
                      SizedBox(width: 8),
                      Text('Remove Account'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
        onTap: () => _switchToAccount(account),
      ),
    );
  }

  Widget _buildAccountTypeIcon(AccountType type) {
    switch (type) {
      case AccountType.personal:
        return const FaIcon(FontAwesomeIcons.user);
      case AccountType.business:
        return const FaIcon(FontAwesomeIcons.briefcase);
      case AccountType.celebrity:
        return const FaIcon(FontAwesomeIcons.star);
    }
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          ElevatedButton.icon(
            onPressed: _addNewAccount,
            icon: const FaIcon(FontAwesomeIcons.plus),
            label: const Text('Add Account'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.accentColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
          ElevatedButton.icon(
            onPressed: _logoutFromAllAccounts,
            icon: const FaIcon(FontAwesomeIcons.rightFromBracket),
            label: const Text('Logout All'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _switchToAccount(AccountModel account) async {
    try {
      await ref
          .read(accountManagementNotifierProvider.notifier)
          .switchToAccount(account.id);
      await ref
          .read(activeAccountNotifierProvider.notifier)
          .switchToAccount(account.id);

      // Clear all cached data and invalidate providers for account switch
      debugPrint(
        '🔄 Clearing cached data and refreshing providers for account switch...',
      );
      final cacheService = getIt<CacheService>();

      // Clear all cached data to ensure fresh data for switched account
      await cacheService.clearAll();

      // Invalidate all user-dependent providers to force refresh
      ref.invalidate(userProfileProvider);
      ref.invalidate(feedProvider);

      // Force refresh of auth provider to update current user context
      ref.invalidate(auth.authProvider);

      debugPrint(
        '✅ All providers invalidated and cache cleared for account switch',
      );

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Switched to ${account.name}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to switch account: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _handleAccountAction(String action, AccountModel account) {
    switch (action) {
      case 'switch':
        _switchToAccount(account);
        break;
      case 'edit':
        _editAccount(account);
        break;
      case 'remove':
        _removeAccount(account);
        break;
    }
  }

  void _addNewAccount() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 24),

            // Title
            Text(
              'Add Account',
              style: AppTheme.fontStyles.title.copyWith(
                color: Colors.black87,
                fontSize: 20,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Choose how you want to add an account',
              style: AppTheme.fontStyles.body.copyWith(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 32),

            // Login to existing account option
            _buildAccountOption(
              title: 'Log Into Existing Account',
              subtitle: 'Sign in with your username and password',
              icon: FontAwesomeIcons.rightToBracket,
              color: Colors.blue,
              onTap: () {
                Navigator.pop(context);
                _showLoginDialog();
              },
            ),
            const SizedBox(height: 16),

            // Create new account option
            _buildAccountOption(
              title: 'Create New Account',
              subtitle: 'Set up a brand new account from scratch',
              icon: FontAwesomeIcons.userPlus,
              color: AppTheme.accentColor,
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const CreateAccountScreen(),
                  ),
                );
              },
            ),
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountOption({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.grey[200]!),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: FaIcon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTheme.fontStyles.bodyBold.copyWith(
                      color: Colors.black87,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: AppTheme.fontStyles.body.copyWith(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            FaIcon(
              FontAwesomeIcons.chevronRight,
              color: Colors.grey[400],
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  void _showLoginDialog() {
    final emailController = TextEditingController();
    final passwordController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            FaIcon(
              FontAwesomeIcons.rightToBracket,
              color: Colors.blue,
              size: 20,
            ),
            const SizedBox(width: 12),
            const Text('Log Into Account'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Enter your existing account credentials',
              style: AppTheme.fontStyles.body.copyWith(color: Colors.grey[600]),
            ),
            const SizedBox(height: 20),

            // Email field
            TextField(
              controller: emailController,
              keyboardType: TextInputType.emailAddress,
              decoration: InputDecoration(
                labelText: 'Email',
                prefixIcon: const FaIcon(FontAwesomeIcons.envelope),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Colors.grey[50],
              ),
            ),
            const SizedBox(height: 16),

            // Password field
            TextField(
              controller: passwordController,
              obscureText: true,
              decoration: InputDecoration(
                labelText: 'Password',
                prefixIcon: const FaIcon(FontAwesomeIcons.lock),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Colors.grey[50],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () =>
                _performLogin(emailController.text, passwordController.text),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('Log In'),
          ),
        ],
      ),
    );
  }

  Future<void> _performLogin(String email, String password) async {
    if (email.trim().isEmpty || password.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Please enter both email and password'),
          backgroundColor: Colors.red[600],
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      );
      return;
    }

    // Validate email format
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email.trim())) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Please enter a valid email address'),
          backgroundColor: Colors.red[600],
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      );
      return;
    }

    try {
      // Use the real authentication system
      final result = await ref
          .read(auth.authProvider.notifier)
          .signIn(email: email.trim(), password: password);

      // Check if widget is still mounted
      if (!mounted) return;

      // Close the dialog
      Navigator.pop(context);

      // Get the logged-in user
      final user = result['user'] as User?;
      if (user == null) {
        throw Exception('Login failed - no user returned');
      }

      // Get user profile from Firestore to get account information
      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .get();

      if (!userDoc.exists) {
        throw Exception('User profile not found in Firestore');
      }

      final userData = userDoc.data()!;

      // Check if this user has accounts in the account management system
      final accountService = getIt<AccountManagementService>();
      final existingAccounts = await accountService.getUserAccounts();

      AccountModel? targetAccount;

      // Look for existing account with matching username or create new one
      final userUsername =
          userData['username'] ?? user.email?.split('@')[0] ?? 'user';

      for (final account in existingAccounts) {
        if (account.username == userUsername) {
          targetAccount = account;
          break;
        }
      }

      // If account doesn't exist in the management system, create it
      if (targetAccount == null) {
        debugPrint(
          '🔄 Creating new account entry for logged-in user: ${user.uid}',
        );

        // Create new account entry using the service
        targetAccount = await accountService.createAccount(
          name: userData['name'] ?? 'Unknown User',
          username: userUsername,
          accountType: AccountType.personal, // Default type
          profilePictureUrl: userData['profilePictureUrl'] ?? '',
          bio: userData['bio'] ?? '',
        );

        debugPrint('✅ Created new account entry: ${targetAccount.name}');
      }

      // Switch to the target account
      debugPrint('🔄 Switching to account: ${targetAccount.name}');
      await ref
          .read(accountManagementNotifierProvider.notifier)
          .switchToAccount(targetAccount.id);
      await ref
          .read(activeAccountNotifierProvider.notifier)
          .switchToAccount(targetAccount.id);

      // Clear all cached data and invalidate providers
      debugPrint('🔄 Clearing cached data and refreshing providers...');
      final cacheService = getIt<CacheService>();

      // Clear all cached data to ensure fresh data for new account
      await cacheService.clearAll();

      // Invalidate all user-dependent providers to force refresh
      ref.invalidate(userProfileProvider);
      ref.invalidate(feedProvider);

      // Force refresh of auth provider to update current user context
      ref.invalidate(auth.authProvider);

      // Also invalidate account management providers to refresh account list
      ref.invalidate(accountManagementNotifierProvider);
      ref.invalidate(activeAccountNotifierProvider);

      debugPrint('✅ All providers invalidated and cache cleared');

      // Check if widget is still mounted
      if (!mounted) return;

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const FaIcon(
                FontAwesomeIcons.circleCheck,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'Login Successful!',
                      style: AppTheme.fontStyles.bodyBold.copyWith(
                        color: Colors.white,
                        fontSize: 14,
                      ),
                    ),
                    Text(
                      'Switched to ${targetAccount.name}',
                      style: AppTheme.fontStyles.body.copyWith(
                        color: Colors.white.withValues(alpha: 0.9),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          backgroundColor: Colors.green[600],
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          duration: const Duration(seconds: 3),
        ),
      );

      // Check if widget is still mounted before navigation
      if (!mounted) return;

      // Navigate back to main app - the auth state will handle the navigation
      Navigator.of(context).popUntil((route) => route.isFirst);
    } catch (e) {
      // Check if widget is still mounted
      if (!mounted) return;

      // Close the dialog
      Navigator.pop(context);

      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const FaIcon(
                FontAwesomeIcons.triangleExclamation,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'Login Failed',
                      style: AppTheme.fontStyles.bodyBold.copyWith(
                        color: Colors.white,
                        fontSize: 14,
                      ),
                    ),
                    Text(
                      e.toString().replaceAll('Exception: ', ''),
                      style: AppTheme.fontStyles.body.copyWith(
                        color: Colors.white.withValues(alpha: 0.9),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          backgroundColor: Colors.red[600],
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          duration: const Duration(seconds: 5),
        ),
      );
    }
  }

  void _editAccount(AccountModel account) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Edit ${account.name} feature coming soon!'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _removeAccount(AccountModel account) {
    showAppDialog(
      context: context,
      title: Text('Remove Account'),
      content: Text(
        'Are you sure you want to remove ${account.name}? This action cannot be undone.',
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () async {
            Navigator.pop(context);
            try {
              await ref
                  .read(accountManagementNotifierProvider.notifier)
                  .deleteAccount(account.id);
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('${account.name} removed'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            } catch (e) {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Failed to remove account: ${e.toString()}'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            }
          },
          style: TextButton.styleFrom(foregroundColor: Colors.red),
          child: const Text('Remove'),
        ),
      ],
    );
  }

  void _logoutFromAllAccounts() {
    showAppDialog(
      context: context,
      title: Text('Logout All Accounts'),
      content: Text(
        'Are you sure you want to logout from all accounts? You will need to sign in again.',
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () {
            Navigator.pop(context);
            Navigator.pop(context);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Logged out from all accounts'),
                backgroundColor: Colors.red,
              ),
            );
          },
          style: TextButton.styleFrom(foregroundColor: Colors.red),
          child: const Text('Logout All'),
        ),
      ],
    );
  }
}
