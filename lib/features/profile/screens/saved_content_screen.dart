import 'package:billionaires_social/features/profile/providers/saved_content_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class SavedContentScreen extends ConsumerWidget {
  const SavedContentScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final savedContentAsync = ref.watch(savedContentProvider);

    return Scaffold(
      appBar: AppBar(title: const Text('Saved')),
      body: savedContentAsync.when(
        data: (posts) {
          if (posts.isEmpty) {
            return const Center(child: Text('You have no saved posts.'));
          }
          return GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: 2,
              mainAxisSpacing: 2,
            ),
            itemCount: posts.length,
            itemBuilder: (context, index) {
              final post = posts[index];
              return Image.network(post.mediaUrl, fit: BoxFit.cover);
            },
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (err, stack) => Center(child: Text('Error: $err')),
      ),
    );
  }
}
