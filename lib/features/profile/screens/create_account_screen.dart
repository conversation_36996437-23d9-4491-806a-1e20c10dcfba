import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/core/widgets/back_button_widget.dart';
import 'package:billionaires_social/core/widgets/validated_text_field.dart';
import 'package:billionaires_social/core/utils/input_validation.dart';
import 'package:billionaires_social/features/profile/models/account_model.dart';
import 'package:billionaires_social/features/profile/providers/account_management_provider.dart';

class CreateAccountScreen extends ConsumerStatefulWidget {
  const CreateAccountScreen({super.key});

  @override
  ConsumerState<CreateAccountScreen> createState() =>
      _CreateAccountScreenState();
}

class _CreateAccountScreenState extends ConsumerState<CreateAccountScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _usernameController = TextEditingController();
  final _bioController = TextEditingController();

  AccountType _selectedAccountType = AccountType.personal;
  bool _isLoading = false;
  bool _isCheckingUsername = false;
  String? _error;
  String? _usernameError;
  bool _isUsernameAvailable = false;
  List<String> _usernameSuggestions = [];

  @override
  void initState() {
    super.initState();
    _usernameController.addListener(_onUsernameChanged);
  }

  @override
  void dispose() {
    _usernameController.removeListener(_onUsernameChanged);
    _nameController.dispose();
    _usernameController.dispose();
    _bioController.dispose();
    super.dispose();
  }

  void _onUsernameChanged() {
    final username = _usernameController.text.trim();
    if (username.length >= 3) {
      _checkUsernameAvailability(username);
    } else {
      setState(() {
        _usernameError = null;
        _isUsernameAvailable = false;
      });
    }
  }

  Future<void> _checkUsernameAvailability(String username) async {
    setState(() {
      _isCheckingUsername = true;
      _usernameError = null;
    });

    try {
      // Add a small delay to avoid too many requests
      await Future.delayed(const Duration(milliseconds: 500));

      final accountService = ref.read(
        accountManagementNotifierProvider.notifier,
      );
      final isAvailable = await accountService.isUsernameAvailable(username);

      if (mounted) {
        setState(() {
          _isUsernameAvailable = isAvailable;
          _usernameError = isAvailable ? null : 'Username is already taken';
          _usernameSuggestions = isAvailable
              ? []
              : _generateUsernameSuggestions(username);
          _isCheckingUsername = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _usernameError = 'Error checking username availability';
          _isCheckingUsername = false;
        });
      }
    }
  }

  List<String> _generateUsernameSuggestions(String baseUsername) {
    final suggestions = <String>[];
    final random = DateTime.now().millisecondsSinceEpoch % 1000;

    // Add number suffixes
    suggestions.add('${baseUsername}_$random');
    suggestions.add('$baseUsername${random.toString().substring(0, 2)}');

    // Add common suffixes
    suggestions.add('${baseUsername}_official');
    suggestions.add('${baseUsername}_real');
    suggestions.add('the_$baseUsername');

    return suggestions.take(3).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        leading: const CustomBackButton(size: 36),
        iconTheme: const IconThemeData(color: Colors.black),
        title: Text(
          'Create New Account',
          style: AppTheme.fontStyles.title.copyWith(
            color: Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.grey[200]!),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: AppTheme.accentColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: FaIcon(
                        FontAwesomeIcons.userPlus,
                        color: AppTheme.accentColor,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Add New Account',
                            style: AppTheme.fontStyles.bodyBold.copyWith(
                              color: Colors.black87,
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Create additional accounts to manage different aspects of your life',
                            style: AppTheme.fontStyles.body.copyWith(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 32),

              // Account Type Selection
              _buildAccountTypeSection(),
              const SizedBox(height: 32),

              // Form Fields
              _buildFormFields(),
              const SizedBox(height: 32),

              // Error Message
              if (_error != null) ...[
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.red.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      const FaIcon(
                        FontAwesomeIcons.triangleExclamation,
                        color: Colors.red,
                        size: 16,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          _error!,
                          style: AppTheme.fontStyles.body.copyWith(
                            color: Colors.red,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),
              ],

              // Account Creation Tips
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppTheme.accentColor.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppTheme.accentColor.withValues(alpha: 0.2),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        FaIcon(
                          FontAwesomeIcons.lightbulb,
                          color: AppTheme.accentColor,
                          size: 20,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            'Pro Tips for Your Account',
                            style: AppTheme.fontStyles.bodyBold.copyWith(
                              color: Colors.black87,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    _buildTip(
                      'Choose a memorable username - you can\'t change it later',
                    ),
                    const SizedBox(height: 8),
                    _buildTip(
                      'Add a profile picture to help others recognize you',
                    ),
                    const SizedBox(height: 8),
                    _buildTip(
                      'Write a bio that reflects your personality or interests',
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // Create Button
              Container(
                width: double.infinity,
                height: 56,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  gradient: LinearGradient(
                    colors: [
                      AppTheme.accentColor,
                      AppTheme.accentColor.withValues(alpha: 0.8),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.accentColor.withValues(alpha: 0.3),
                      blurRadius: 12,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _createAccount,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    foregroundColor: Colors.black,
                    shadowColor: Colors.transparent,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    elevation: 0,
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            strokeWidth: 2.5,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.black,
                            ),
                          ),
                        )
                      : Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const FaIcon(
                              FontAwesomeIcons.plus,
                              size: 16,
                              color: Colors.black,
                            ),
                            const SizedBox(width: 12),
                            Text(
                              'Create Account',
                              style: AppTheme.fontStyles.bodyBold.copyWith(
                                fontSize: 16,
                                color: Colors.black,
                              ),
                            ),
                          ],
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAccountTypeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Choose Account Type',
          style: AppTheme.fontStyles.bodyBold.copyWith(
            color: Colors.black87,
            fontSize: 18,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Select the type that best describes your account purpose',
          style: AppTheme.fontStyles.body.copyWith(
            color: Colors.grey[600],
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 20),
        ...AccountType.values.map((type) => _buildAccountTypeOption(type)),
      ],
    );
  }

  Widget _buildAccountTypeOption(AccountType type) {
    final isSelected = _selectedAccountType == type;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => setState(() => _selectedAccountType = type),
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: isSelected
                ? AppTheme.accentColor.withValues(alpha: 0.05)
                : Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: isSelected ? AppTheme.accentColor : Colors.grey[300]!,
              width: isSelected ? 2 : 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  color: isSelected ? AppTheme.accentColor : Colors.grey[100],
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    _getAccountTypeIcon(type),
                    style: TextStyle(
                      fontSize: 24,
                      color: isSelected ? Colors.white : Colors.grey[600],
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 20),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getAccountTypeTitle(type),
                      style: AppTheme.fontStyles.bodyBold.copyWith(
                        color: Colors.black87,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 6),
                    Text(
                      _getAccountTypeDescription(type),
                      style: AppTheme.fontStyles.body.copyWith(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              if (isSelected)
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppTheme.accentColor,
                    shape: BoxShape.circle,
                  ),
                  child: const FaIcon(
                    FontAwesomeIcons.check,
                    color: Colors.white,
                    size: 12,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFormFields() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Account Details',
          style: AppTheme.fontStyles.bodyBold.copyWith(
            color: Colors.black87,
            fontSize: 18,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Fill in the information below to create your account',
          style: AppTheme.fontStyles.body.copyWith(
            color: Colors.grey[600],
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 20),
        ValidatedTextField(
          controller: _nameController,
          label: 'Display Name',
          hint: 'Enter your display name',
          validator: InputValidation.validateName,
          prefixIcon: const FaIcon(FontAwesomeIcons.user),
        ),
        const SizedBox(height: 20),
        _buildUsernameField(),
        const SizedBox(height: 20),
        ValidatedTextField(
          controller: _bioController,
          label: 'Bio (Optional)',
          hint: 'Tell people about yourself',
          maxLines: 3,
          maxLength: 150,
          prefixIcon: const FaIcon(FontAwesomeIcons.fileLines),
        ),
      ],
    );
  }

  Widget _buildUsernameField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: _usernameController,
          decoration: InputDecoration(
            labelText: 'Username',
            hintText: 'Enter a unique username',
            prefixIcon: const FaIcon(FontAwesomeIcons.at),
            suffixIcon: _isCheckingUsername
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: Padding(
                      padding: EdgeInsets.all(12),
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                  )
                : _usernameController.text.length >= 3
                ? Icon(
                    _isUsernameAvailable ? Icons.check_circle : Icons.error,
                    color: _isUsernameAvailable ? Colors.green : Colors.red,
                  )
                : null,
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
            filled: true,
            fillColor: Colors.grey[50],
            errorText: _usernameError,
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Username is required';
            }
            if (value.trim().length < 3) {
              return 'Username must be at least 3 characters';
            }
            if (_usernameError != null) {
              return _usernameError;
            }
            return null;
          },
        ),
        if (_usernameController.text.length >= 3 && _isUsernameAvailable)
          Padding(
            padding: const EdgeInsets.only(top: 8, left: 12),
            child: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.green, size: 16),
                const SizedBox(width: 8),
                Text(
                  'Username is available!',
                  style: TextStyle(
                    color: Colors.green[700],
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        if (_usernameSuggestions.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 12, left: 12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Try these suggestions:',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  children: _usernameSuggestions.map((suggestion) {
                    return GestureDetector(
                      onTap: () {
                        _usernameController.text = suggestion;
                        _checkUsernameAvailability(suggestion);
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: AppTheme.accentColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: AppTheme.accentColor.withValues(alpha: 0.3),
                          ),
                        ),
                        child: Text(
                          suggestion,
                          style: TextStyle(
                            color: AppTheme.accentColor,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildTip(String text) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          margin: const EdgeInsets.only(top: 6),
          width: 4,
          height: 4,
          decoration: BoxDecoration(
            color: AppTheme.accentColor,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            text,
            style: AppTheme.fontStyles.body.copyWith(
              color: Colors.grey[700],
              fontSize: 13,
            ),
          ),
        ),
      ],
    );
  }

  String _getAccountTypeIcon(AccountType type) {
    switch (type) {
      case AccountType.personal:
        return '👤';
      case AccountType.business:
        return '🏢';
      case AccountType.celebrity:
        return '⭐';
    }
  }

  String _getAccountTypeTitle(AccountType type) {
    switch (type) {
      case AccountType.personal:
        return 'Personal';
      case AccountType.business:
        return 'Business';
      case AccountType.celebrity:
        return 'Celebrity';
    }
  }

  String _getAccountTypeDescription(AccountType type) {
    switch (type) {
      case AccountType.personal:
        return 'Perfect for personal use and connecting with friends and family';
      case AccountType.business:
        return 'Ideal for businesses, brands, and professional organizations';
      case AccountType.celebrity:
        return 'Designed for public figures, creators, and verified celebrities';
    }
  }

  Future<void> _createAccount() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Additional validation for username availability
    if (_usernameController.text.length >= 3 && !_isUsernameAvailable) {
      setState(() {
        _error =
            'Please choose an available username before creating the account.';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      await ref
          .read(accountManagementNotifierProvider.notifier)
          .createAccount(
            name: _nameController.text.trim(),
            username: _usernameController.text.trim().toLowerCase(),
            accountType: _selectedAccountType,
            bio: _bioController.text.trim().isEmpty
                ? null
                : _bioController.text.trim(),
          );

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const FaIcon(
                  FontAwesomeIcons.circleCheck,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'Account Created Successfully!',
                        style: AppTheme.fontStyles.bodyBold.copyWith(
                          color: Colors.white,
                          fontSize: 14,
                        ),
                      ),
                      Text(
                        '${_nameController.text} is ready to use',
                        style: AppTheme.fontStyles.body.copyWith(
                          color: Colors.white.withValues(alpha: 0.9),
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.green[600],
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            margin: const EdgeInsets.all(16),
            duration: const Duration(seconds: 4),
          ),
        );
      }
    } catch (e) {
      String errorMessage = e.toString().replaceAll('Exception: ', '');

      // Provide more user-friendly error messages
      if (errorMessage.contains('Username is already taken')) {
        errorMessage =
            'This username is already taken. Please try one of the suggestions below or choose a different username.';
        // Generate new suggestions
        setState(() {
          _usernameSuggestions = _generateUsernameSuggestions(
            _usernameController.text.trim(),
          );
        });
      } else if (errorMessage.contains('network')) {
        errorMessage =
            'Network error. Please check your connection and try again.';
      } else if (errorMessage.contains('permission')) {
        errorMessage =
            'Permission denied. Please try again or contact support.';
      }

      setState(() {
        _error = errorMessage;
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
