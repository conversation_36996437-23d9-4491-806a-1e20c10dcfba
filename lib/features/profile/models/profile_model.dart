import 'package:freezed_annotation/freezed_annotation.dart';

part 'profile_model.freezed.dart';
part 'profile_model.g.dart';

@freezed
abstract class ProfileModel with _$ProfileModel {
  const factory ProfileModel({
    required String id,
    required String username,
    required String name,
    required String profilePictureUrl,
    required String bio,
    required int postCount,
    required int followerCount,
    required int followingCount,
    // Enhanced fields for complete profile
    String? bannerImageUrl,
    String? website,
    String? location,
    String? phone,
    String? email,
    @Default(false) bool isPrivate,
    @Default(true) bool allowMessages,
    @Default(true) bool showActivityStatus,
    @Default(false) bool isVerified,
    @Default(false) bool isBillionaire,
    @Default('regular') String userType,
    DateTime? createdAt,
    DateTime? updatedAt,
    // Business Account fields for sellers
    @Default(false) bool isBusinessAccount,
    String? businessName,
    String? businessLogoUrl,
    String? businessEmail,
    String? businessPhone,
    String? businessDescription,
    @Default(false) bool businessVerified,
    @Default(false) bool businessExclusive,
    String? businessCategory,
    String? businessWebsite,
    String? businessAddress,
    DateTime? businessCreatedAt,
    DateTime? businessVerifiedAt,
    @Default(false) bool isAdmin,
  }) = _ProfileModel;

  factory ProfileModel.fromJson(Map<String, dynamic> json) =>
      _$ProfileModelFromJson(json);
}
