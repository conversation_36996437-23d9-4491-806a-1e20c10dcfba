// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'profile_verification_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ProfileVerificationModel {

 String get id; String get userId; VerificationType get type; VerificationStatus get status; DateTime get createdAt; DateTime? get completedAt; DateTime? get expiresAt; String? get verificationCode; String? get documentUrl; String? get documentType; String? get verificationNotes; String? get verifiedBy; Map<String, dynamic>? get metadata;
/// Create a copy of ProfileVerificationModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProfileVerificationModelCopyWith<ProfileVerificationModel> get copyWith => _$ProfileVerificationModelCopyWithImpl<ProfileVerificationModel>(this as ProfileVerificationModel, _$identity);

  /// Serializes this ProfileVerificationModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProfileVerificationModel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.type, type) || other.type == type)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.completedAt, completedAt) || other.completedAt == completedAt)&&(identical(other.expiresAt, expiresAt) || other.expiresAt == expiresAt)&&(identical(other.verificationCode, verificationCode) || other.verificationCode == verificationCode)&&(identical(other.documentUrl, documentUrl) || other.documentUrl == documentUrl)&&(identical(other.documentType, documentType) || other.documentType == documentType)&&(identical(other.verificationNotes, verificationNotes) || other.verificationNotes == verificationNotes)&&(identical(other.verifiedBy, verifiedBy) || other.verifiedBy == verifiedBy)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,type,status,createdAt,completedAt,expiresAt,verificationCode,documentUrl,documentType,verificationNotes,verifiedBy,const DeepCollectionEquality().hash(metadata));

@override
String toString() {
  return 'ProfileVerificationModel(id: $id, userId: $userId, type: $type, status: $status, createdAt: $createdAt, completedAt: $completedAt, expiresAt: $expiresAt, verificationCode: $verificationCode, documentUrl: $documentUrl, documentType: $documentType, verificationNotes: $verificationNotes, verifiedBy: $verifiedBy, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $ProfileVerificationModelCopyWith<$Res>  {
  factory $ProfileVerificationModelCopyWith(ProfileVerificationModel value, $Res Function(ProfileVerificationModel) _then) = _$ProfileVerificationModelCopyWithImpl;
@useResult
$Res call({
 String id, String userId, VerificationType type, VerificationStatus status, DateTime createdAt, DateTime? completedAt, DateTime? expiresAt, String? verificationCode, String? documentUrl, String? documentType, String? verificationNotes, String? verifiedBy, Map<String, dynamic>? metadata
});




}
/// @nodoc
class _$ProfileVerificationModelCopyWithImpl<$Res>
    implements $ProfileVerificationModelCopyWith<$Res> {
  _$ProfileVerificationModelCopyWithImpl(this._self, this._then);

  final ProfileVerificationModel _self;
  final $Res Function(ProfileVerificationModel) _then;

/// Create a copy of ProfileVerificationModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? type = null,Object? status = null,Object? createdAt = null,Object? completedAt = freezed,Object? expiresAt = freezed,Object? verificationCode = freezed,Object? documentUrl = freezed,Object? documentType = freezed,Object? verificationNotes = freezed,Object? verifiedBy = freezed,Object? metadata = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as VerificationType,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as VerificationStatus,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,completedAt: freezed == completedAt ? _self.completedAt : completedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,expiresAt: freezed == expiresAt ? _self.expiresAt : expiresAt // ignore: cast_nullable_to_non_nullable
as DateTime?,verificationCode: freezed == verificationCode ? _self.verificationCode : verificationCode // ignore: cast_nullable_to_non_nullable
as String?,documentUrl: freezed == documentUrl ? _self.documentUrl : documentUrl // ignore: cast_nullable_to_non_nullable
as String?,documentType: freezed == documentType ? _self.documentType : documentType // ignore: cast_nullable_to_non_nullable
as String?,verificationNotes: freezed == verificationNotes ? _self.verificationNotes : verificationNotes // ignore: cast_nullable_to_non_nullable
as String?,verifiedBy: freezed == verifiedBy ? _self.verifiedBy : verifiedBy // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [ProfileVerificationModel].
extension ProfileVerificationModelPatterns on ProfileVerificationModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ProfileVerificationModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ProfileVerificationModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ProfileVerificationModel value)  $default,){
final _that = this;
switch (_that) {
case _ProfileVerificationModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ProfileVerificationModel value)?  $default,){
final _that = this;
switch (_that) {
case _ProfileVerificationModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  VerificationType type,  VerificationStatus status,  DateTime createdAt,  DateTime? completedAt,  DateTime? expiresAt,  String? verificationCode,  String? documentUrl,  String? documentType,  String? verificationNotes,  String? verifiedBy,  Map<String, dynamic>? metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ProfileVerificationModel() when $default != null:
return $default(_that.id,_that.userId,_that.type,_that.status,_that.createdAt,_that.completedAt,_that.expiresAt,_that.verificationCode,_that.documentUrl,_that.documentType,_that.verificationNotes,_that.verifiedBy,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  VerificationType type,  VerificationStatus status,  DateTime createdAt,  DateTime? completedAt,  DateTime? expiresAt,  String? verificationCode,  String? documentUrl,  String? documentType,  String? verificationNotes,  String? verifiedBy,  Map<String, dynamic>? metadata)  $default,) {final _that = this;
switch (_that) {
case _ProfileVerificationModel():
return $default(_that.id,_that.userId,_that.type,_that.status,_that.createdAt,_that.completedAt,_that.expiresAt,_that.verificationCode,_that.documentUrl,_that.documentType,_that.verificationNotes,_that.verifiedBy,_that.metadata);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  VerificationType type,  VerificationStatus status,  DateTime createdAt,  DateTime? completedAt,  DateTime? expiresAt,  String? verificationCode,  String? documentUrl,  String? documentType,  String? verificationNotes,  String? verifiedBy,  Map<String, dynamic>? metadata)?  $default,) {final _that = this;
switch (_that) {
case _ProfileVerificationModel() when $default != null:
return $default(_that.id,_that.userId,_that.type,_that.status,_that.createdAt,_that.completedAt,_that.expiresAt,_that.verificationCode,_that.documentUrl,_that.documentType,_that.verificationNotes,_that.verifiedBy,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ProfileVerificationModel implements ProfileVerificationModel {
  const _ProfileVerificationModel({required this.id, required this.userId, required this.type, required this.status, required this.createdAt, this.completedAt, this.expiresAt, this.verificationCode, this.documentUrl, this.documentType, this.verificationNotes, this.verifiedBy, final  Map<String, dynamic>? metadata}): _metadata = metadata;
  factory _ProfileVerificationModel.fromJson(Map<String, dynamic> json) => _$ProfileVerificationModelFromJson(json);

@override final  String id;
@override final  String userId;
@override final  VerificationType type;
@override final  VerificationStatus status;
@override final  DateTime createdAt;
@override final  DateTime? completedAt;
@override final  DateTime? expiresAt;
@override final  String? verificationCode;
@override final  String? documentUrl;
@override final  String? documentType;
@override final  String? verificationNotes;
@override final  String? verifiedBy;
 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of ProfileVerificationModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProfileVerificationModelCopyWith<_ProfileVerificationModel> get copyWith => __$ProfileVerificationModelCopyWithImpl<_ProfileVerificationModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ProfileVerificationModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProfileVerificationModel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.type, type) || other.type == type)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.completedAt, completedAt) || other.completedAt == completedAt)&&(identical(other.expiresAt, expiresAt) || other.expiresAt == expiresAt)&&(identical(other.verificationCode, verificationCode) || other.verificationCode == verificationCode)&&(identical(other.documentUrl, documentUrl) || other.documentUrl == documentUrl)&&(identical(other.documentType, documentType) || other.documentType == documentType)&&(identical(other.verificationNotes, verificationNotes) || other.verificationNotes == verificationNotes)&&(identical(other.verifiedBy, verifiedBy) || other.verifiedBy == verifiedBy)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,type,status,createdAt,completedAt,expiresAt,verificationCode,documentUrl,documentType,verificationNotes,verifiedBy,const DeepCollectionEquality().hash(_metadata));

@override
String toString() {
  return 'ProfileVerificationModel(id: $id, userId: $userId, type: $type, status: $status, createdAt: $createdAt, completedAt: $completedAt, expiresAt: $expiresAt, verificationCode: $verificationCode, documentUrl: $documentUrl, documentType: $documentType, verificationNotes: $verificationNotes, verifiedBy: $verifiedBy, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$ProfileVerificationModelCopyWith<$Res> implements $ProfileVerificationModelCopyWith<$Res> {
  factory _$ProfileVerificationModelCopyWith(_ProfileVerificationModel value, $Res Function(_ProfileVerificationModel) _then) = __$ProfileVerificationModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, VerificationType type, VerificationStatus status, DateTime createdAt, DateTime? completedAt, DateTime? expiresAt, String? verificationCode, String? documentUrl, String? documentType, String? verificationNotes, String? verifiedBy, Map<String, dynamic>? metadata
});




}
/// @nodoc
class __$ProfileVerificationModelCopyWithImpl<$Res>
    implements _$ProfileVerificationModelCopyWith<$Res> {
  __$ProfileVerificationModelCopyWithImpl(this._self, this._then);

  final _ProfileVerificationModel _self;
  final $Res Function(_ProfileVerificationModel) _then;

/// Create a copy of ProfileVerificationModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? type = null,Object? status = null,Object? createdAt = null,Object? completedAt = freezed,Object? expiresAt = freezed,Object? verificationCode = freezed,Object? documentUrl = freezed,Object? documentType = freezed,Object? verificationNotes = freezed,Object? verifiedBy = freezed,Object? metadata = freezed,}) {
  return _then(_ProfileVerificationModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as VerificationType,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as VerificationStatus,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,completedAt: freezed == completedAt ? _self.completedAt : completedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,expiresAt: freezed == expiresAt ? _self.expiresAt : expiresAt // ignore: cast_nullable_to_non_nullable
as DateTime?,verificationCode: freezed == verificationCode ? _self.verificationCode : verificationCode // ignore: cast_nullable_to_non_nullable
as String?,documentUrl: freezed == documentUrl ? _self.documentUrl : documentUrl // ignore: cast_nullable_to_non_nullable
as String?,documentType: freezed == documentType ? _self.documentType : documentType // ignore: cast_nullable_to_non_nullable
as String?,verificationNotes: freezed == verificationNotes ? _self.verificationNotes : verificationNotes // ignore: cast_nullable_to_non_nullable
as String?,verifiedBy: freezed == verifiedBy ? _self.verifiedBy : verifiedBy // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}


/// @nodoc
mixin _$EmailVerificationModel {

 String get email; String get verificationCode; DateTime get expiresAt; bool get isVerified; DateTime? get verifiedAt;
/// Create a copy of EmailVerificationModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$EmailVerificationModelCopyWith<EmailVerificationModel> get copyWith => _$EmailVerificationModelCopyWithImpl<EmailVerificationModel>(this as EmailVerificationModel, _$identity);

  /// Serializes this EmailVerificationModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EmailVerificationModel&&(identical(other.email, email) || other.email == email)&&(identical(other.verificationCode, verificationCode) || other.verificationCode == verificationCode)&&(identical(other.expiresAt, expiresAt) || other.expiresAt == expiresAt)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.verifiedAt, verifiedAt) || other.verifiedAt == verifiedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,email,verificationCode,expiresAt,isVerified,verifiedAt);

@override
String toString() {
  return 'EmailVerificationModel(email: $email, verificationCode: $verificationCode, expiresAt: $expiresAt, isVerified: $isVerified, verifiedAt: $verifiedAt)';
}


}

/// @nodoc
abstract mixin class $EmailVerificationModelCopyWith<$Res>  {
  factory $EmailVerificationModelCopyWith(EmailVerificationModel value, $Res Function(EmailVerificationModel) _then) = _$EmailVerificationModelCopyWithImpl;
@useResult
$Res call({
 String email, String verificationCode, DateTime expiresAt, bool isVerified, DateTime? verifiedAt
});




}
/// @nodoc
class _$EmailVerificationModelCopyWithImpl<$Res>
    implements $EmailVerificationModelCopyWith<$Res> {
  _$EmailVerificationModelCopyWithImpl(this._self, this._then);

  final EmailVerificationModel _self;
  final $Res Function(EmailVerificationModel) _then;

/// Create a copy of EmailVerificationModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? email = null,Object? verificationCode = null,Object? expiresAt = null,Object? isVerified = null,Object? verifiedAt = freezed,}) {
  return _then(_self.copyWith(
email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,verificationCode: null == verificationCode ? _self.verificationCode : verificationCode // ignore: cast_nullable_to_non_nullable
as String,expiresAt: null == expiresAt ? _self.expiresAt : expiresAt // ignore: cast_nullable_to_non_nullable
as DateTime,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,verifiedAt: freezed == verifiedAt ? _self.verifiedAt : verifiedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [EmailVerificationModel].
extension EmailVerificationModelPatterns on EmailVerificationModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _EmailVerificationModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _EmailVerificationModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _EmailVerificationModel value)  $default,){
final _that = this;
switch (_that) {
case _EmailVerificationModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _EmailVerificationModel value)?  $default,){
final _that = this;
switch (_that) {
case _EmailVerificationModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String email,  String verificationCode,  DateTime expiresAt,  bool isVerified,  DateTime? verifiedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _EmailVerificationModel() when $default != null:
return $default(_that.email,_that.verificationCode,_that.expiresAt,_that.isVerified,_that.verifiedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String email,  String verificationCode,  DateTime expiresAt,  bool isVerified,  DateTime? verifiedAt)  $default,) {final _that = this;
switch (_that) {
case _EmailVerificationModel():
return $default(_that.email,_that.verificationCode,_that.expiresAt,_that.isVerified,_that.verifiedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String email,  String verificationCode,  DateTime expiresAt,  bool isVerified,  DateTime? verifiedAt)?  $default,) {final _that = this;
switch (_that) {
case _EmailVerificationModel() when $default != null:
return $default(_that.email,_that.verificationCode,_that.expiresAt,_that.isVerified,_that.verifiedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _EmailVerificationModel implements EmailVerificationModel {
  const _EmailVerificationModel({required this.email, required this.verificationCode, required this.expiresAt, required this.isVerified, this.verifiedAt});
  factory _EmailVerificationModel.fromJson(Map<String, dynamic> json) => _$EmailVerificationModelFromJson(json);

@override final  String email;
@override final  String verificationCode;
@override final  DateTime expiresAt;
@override final  bool isVerified;
@override final  DateTime? verifiedAt;

/// Create a copy of EmailVerificationModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$EmailVerificationModelCopyWith<_EmailVerificationModel> get copyWith => __$EmailVerificationModelCopyWithImpl<_EmailVerificationModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$EmailVerificationModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _EmailVerificationModel&&(identical(other.email, email) || other.email == email)&&(identical(other.verificationCode, verificationCode) || other.verificationCode == verificationCode)&&(identical(other.expiresAt, expiresAt) || other.expiresAt == expiresAt)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.verifiedAt, verifiedAt) || other.verifiedAt == verifiedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,email,verificationCode,expiresAt,isVerified,verifiedAt);

@override
String toString() {
  return 'EmailVerificationModel(email: $email, verificationCode: $verificationCode, expiresAt: $expiresAt, isVerified: $isVerified, verifiedAt: $verifiedAt)';
}


}

/// @nodoc
abstract mixin class _$EmailVerificationModelCopyWith<$Res> implements $EmailVerificationModelCopyWith<$Res> {
  factory _$EmailVerificationModelCopyWith(_EmailVerificationModel value, $Res Function(_EmailVerificationModel) _then) = __$EmailVerificationModelCopyWithImpl;
@override @useResult
$Res call({
 String email, String verificationCode, DateTime expiresAt, bool isVerified, DateTime? verifiedAt
});




}
/// @nodoc
class __$EmailVerificationModelCopyWithImpl<$Res>
    implements _$EmailVerificationModelCopyWith<$Res> {
  __$EmailVerificationModelCopyWithImpl(this._self, this._then);

  final _EmailVerificationModel _self;
  final $Res Function(_EmailVerificationModel) _then;

/// Create a copy of EmailVerificationModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? email = null,Object? verificationCode = null,Object? expiresAt = null,Object? isVerified = null,Object? verifiedAt = freezed,}) {
  return _then(_EmailVerificationModel(
email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,verificationCode: null == verificationCode ? _self.verificationCode : verificationCode // ignore: cast_nullable_to_non_nullable
as String,expiresAt: null == expiresAt ? _self.expiresAt : expiresAt // ignore: cast_nullable_to_non_nullable
as DateTime,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,verifiedAt: freezed == verifiedAt ? _self.verifiedAt : verifiedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$PhoneVerificationModel {

 String get phone; String get verificationCode; DateTime get expiresAt; bool get isVerified; DateTime? get verifiedAt;
/// Create a copy of PhoneVerificationModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PhoneVerificationModelCopyWith<PhoneVerificationModel> get copyWith => _$PhoneVerificationModelCopyWithImpl<PhoneVerificationModel>(this as PhoneVerificationModel, _$identity);

  /// Serializes this PhoneVerificationModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PhoneVerificationModel&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.verificationCode, verificationCode) || other.verificationCode == verificationCode)&&(identical(other.expiresAt, expiresAt) || other.expiresAt == expiresAt)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.verifiedAt, verifiedAt) || other.verifiedAt == verifiedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,phone,verificationCode,expiresAt,isVerified,verifiedAt);

@override
String toString() {
  return 'PhoneVerificationModel(phone: $phone, verificationCode: $verificationCode, expiresAt: $expiresAt, isVerified: $isVerified, verifiedAt: $verifiedAt)';
}


}

/// @nodoc
abstract mixin class $PhoneVerificationModelCopyWith<$Res>  {
  factory $PhoneVerificationModelCopyWith(PhoneVerificationModel value, $Res Function(PhoneVerificationModel) _then) = _$PhoneVerificationModelCopyWithImpl;
@useResult
$Res call({
 String phone, String verificationCode, DateTime expiresAt, bool isVerified, DateTime? verifiedAt
});




}
/// @nodoc
class _$PhoneVerificationModelCopyWithImpl<$Res>
    implements $PhoneVerificationModelCopyWith<$Res> {
  _$PhoneVerificationModelCopyWithImpl(this._self, this._then);

  final PhoneVerificationModel _self;
  final $Res Function(PhoneVerificationModel) _then;

/// Create a copy of PhoneVerificationModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? phone = null,Object? verificationCode = null,Object? expiresAt = null,Object? isVerified = null,Object? verifiedAt = freezed,}) {
  return _then(_self.copyWith(
phone: null == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String,verificationCode: null == verificationCode ? _self.verificationCode : verificationCode // ignore: cast_nullable_to_non_nullable
as String,expiresAt: null == expiresAt ? _self.expiresAt : expiresAt // ignore: cast_nullable_to_non_nullable
as DateTime,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,verifiedAt: freezed == verifiedAt ? _self.verifiedAt : verifiedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [PhoneVerificationModel].
extension PhoneVerificationModelPatterns on PhoneVerificationModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PhoneVerificationModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PhoneVerificationModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PhoneVerificationModel value)  $default,){
final _that = this;
switch (_that) {
case _PhoneVerificationModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PhoneVerificationModel value)?  $default,){
final _that = this;
switch (_that) {
case _PhoneVerificationModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String phone,  String verificationCode,  DateTime expiresAt,  bool isVerified,  DateTime? verifiedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PhoneVerificationModel() when $default != null:
return $default(_that.phone,_that.verificationCode,_that.expiresAt,_that.isVerified,_that.verifiedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String phone,  String verificationCode,  DateTime expiresAt,  bool isVerified,  DateTime? verifiedAt)  $default,) {final _that = this;
switch (_that) {
case _PhoneVerificationModel():
return $default(_that.phone,_that.verificationCode,_that.expiresAt,_that.isVerified,_that.verifiedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String phone,  String verificationCode,  DateTime expiresAt,  bool isVerified,  DateTime? verifiedAt)?  $default,) {final _that = this;
switch (_that) {
case _PhoneVerificationModel() when $default != null:
return $default(_that.phone,_that.verificationCode,_that.expiresAt,_that.isVerified,_that.verifiedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PhoneVerificationModel implements PhoneVerificationModel {
  const _PhoneVerificationModel({required this.phone, required this.verificationCode, required this.expiresAt, required this.isVerified, this.verifiedAt});
  factory _PhoneVerificationModel.fromJson(Map<String, dynamic> json) => _$PhoneVerificationModelFromJson(json);

@override final  String phone;
@override final  String verificationCode;
@override final  DateTime expiresAt;
@override final  bool isVerified;
@override final  DateTime? verifiedAt;

/// Create a copy of PhoneVerificationModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PhoneVerificationModelCopyWith<_PhoneVerificationModel> get copyWith => __$PhoneVerificationModelCopyWithImpl<_PhoneVerificationModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PhoneVerificationModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PhoneVerificationModel&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.verificationCode, verificationCode) || other.verificationCode == verificationCode)&&(identical(other.expiresAt, expiresAt) || other.expiresAt == expiresAt)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.verifiedAt, verifiedAt) || other.verifiedAt == verifiedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,phone,verificationCode,expiresAt,isVerified,verifiedAt);

@override
String toString() {
  return 'PhoneVerificationModel(phone: $phone, verificationCode: $verificationCode, expiresAt: $expiresAt, isVerified: $isVerified, verifiedAt: $verifiedAt)';
}


}

/// @nodoc
abstract mixin class _$PhoneVerificationModelCopyWith<$Res> implements $PhoneVerificationModelCopyWith<$Res> {
  factory _$PhoneVerificationModelCopyWith(_PhoneVerificationModel value, $Res Function(_PhoneVerificationModel) _then) = __$PhoneVerificationModelCopyWithImpl;
@override @useResult
$Res call({
 String phone, String verificationCode, DateTime expiresAt, bool isVerified, DateTime? verifiedAt
});




}
/// @nodoc
class __$PhoneVerificationModelCopyWithImpl<$Res>
    implements _$PhoneVerificationModelCopyWith<$Res> {
  __$PhoneVerificationModelCopyWithImpl(this._self, this._then);

  final _PhoneVerificationModel _self;
  final $Res Function(_PhoneVerificationModel) _then;

/// Create a copy of PhoneVerificationModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? phone = null,Object? verificationCode = null,Object? expiresAt = null,Object? isVerified = null,Object? verifiedAt = freezed,}) {
  return _then(_PhoneVerificationModel(
phone: null == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String,verificationCode: null == verificationCode ? _self.verificationCode : verificationCode // ignore: cast_nullable_to_non_nullable
as String,expiresAt: null == expiresAt ? _self.expiresAt : expiresAt // ignore: cast_nullable_to_non_nullable
as DateTime,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,verifiedAt: freezed == verifiedAt ? _self.verifiedAt : verifiedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$IdentityVerificationModel {

 String get id; String get userId; String get documentType; String get documentUrl; String get fullName; DateTime get dateOfBirth; String get nationality; VerificationStatus get status; DateTime get submittedAt; DateTime? get reviewedAt; String? get reviewedBy; String? get rejectionReason; Map<String, dynamic>? get documentData;
/// Create a copy of IdentityVerificationModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$IdentityVerificationModelCopyWith<IdentityVerificationModel> get copyWith => _$IdentityVerificationModelCopyWithImpl<IdentityVerificationModel>(this as IdentityVerificationModel, _$identity);

  /// Serializes this IdentityVerificationModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is IdentityVerificationModel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.documentType, documentType) || other.documentType == documentType)&&(identical(other.documentUrl, documentUrl) || other.documentUrl == documentUrl)&&(identical(other.fullName, fullName) || other.fullName == fullName)&&(identical(other.dateOfBirth, dateOfBirth) || other.dateOfBirth == dateOfBirth)&&(identical(other.nationality, nationality) || other.nationality == nationality)&&(identical(other.status, status) || other.status == status)&&(identical(other.submittedAt, submittedAt) || other.submittedAt == submittedAt)&&(identical(other.reviewedAt, reviewedAt) || other.reviewedAt == reviewedAt)&&(identical(other.reviewedBy, reviewedBy) || other.reviewedBy == reviewedBy)&&(identical(other.rejectionReason, rejectionReason) || other.rejectionReason == rejectionReason)&&const DeepCollectionEquality().equals(other.documentData, documentData));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,documentType,documentUrl,fullName,dateOfBirth,nationality,status,submittedAt,reviewedAt,reviewedBy,rejectionReason,const DeepCollectionEquality().hash(documentData));

@override
String toString() {
  return 'IdentityVerificationModel(id: $id, userId: $userId, documentType: $documentType, documentUrl: $documentUrl, fullName: $fullName, dateOfBirth: $dateOfBirth, nationality: $nationality, status: $status, submittedAt: $submittedAt, reviewedAt: $reviewedAt, reviewedBy: $reviewedBy, rejectionReason: $rejectionReason, documentData: $documentData)';
}


}

/// @nodoc
abstract mixin class $IdentityVerificationModelCopyWith<$Res>  {
  factory $IdentityVerificationModelCopyWith(IdentityVerificationModel value, $Res Function(IdentityVerificationModel) _then) = _$IdentityVerificationModelCopyWithImpl;
@useResult
$Res call({
 String id, String userId, String documentType, String documentUrl, String fullName, DateTime dateOfBirth, String nationality, VerificationStatus status, DateTime submittedAt, DateTime? reviewedAt, String? reviewedBy, String? rejectionReason, Map<String, dynamic>? documentData
});




}
/// @nodoc
class _$IdentityVerificationModelCopyWithImpl<$Res>
    implements $IdentityVerificationModelCopyWith<$Res> {
  _$IdentityVerificationModelCopyWithImpl(this._self, this._then);

  final IdentityVerificationModel _self;
  final $Res Function(IdentityVerificationModel) _then;

/// Create a copy of IdentityVerificationModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? documentType = null,Object? documentUrl = null,Object? fullName = null,Object? dateOfBirth = null,Object? nationality = null,Object? status = null,Object? submittedAt = null,Object? reviewedAt = freezed,Object? reviewedBy = freezed,Object? rejectionReason = freezed,Object? documentData = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,documentType: null == documentType ? _self.documentType : documentType // ignore: cast_nullable_to_non_nullable
as String,documentUrl: null == documentUrl ? _self.documentUrl : documentUrl // ignore: cast_nullable_to_non_nullable
as String,fullName: null == fullName ? _self.fullName : fullName // ignore: cast_nullable_to_non_nullable
as String,dateOfBirth: null == dateOfBirth ? _self.dateOfBirth : dateOfBirth // ignore: cast_nullable_to_non_nullable
as DateTime,nationality: null == nationality ? _self.nationality : nationality // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as VerificationStatus,submittedAt: null == submittedAt ? _self.submittedAt : submittedAt // ignore: cast_nullable_to_non_nullable
as DateTime,reviewedAt: freezed == reviewedAt ? _self.reviewedAt : reviewedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,reviewedBy: freezed == reviewedBy ? _self.reviewedBy : reviewedBy // ignore: cast_nullable_to_non_nullable
as String?,rejectionReason: freezed == rejectionReason ? _self.rejectionReason : rejectionReason // ignore: cast_nullable_to_non_nullable
as String?,documentData: freezed == documentData ? _self.documentData : documentData // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [IdentityVerificationModel].
extension IdentityVerificationModelPatterns on IdentityVerificationModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _IdentityVerificationModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _IdentityVerificationModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _IdentityVerificationModel value)  $default,){
final _that = this;
switch (_that) {
case _IdentityVerificationModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _IdentityVerificationModel value)?  $default,){
final _that = this;
switch (_that) {
case _IdentityVerificationModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  String documentType,  String documentUrl,  String fullName,  DateTime dateOfBirth,  String nationality,  VerificationStatus status,  DateTime submittedAt,  DateTime? reviewedAt,  String? reviewedBy,  String? rejectionReason,  Map<String, dynamic>? documentData)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _IdentityVerificationModel() when $default != null:
return $default(_that.id,_that.userId,_that.documentType,_that.documentUrl,_that.fullName,_that.dateOfBirth,_that.nationality,_that.status,_that.submittedAt,_that.reviewedAt,_that.reviewedBy,_that.rejectionReason,_that.documentData);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  String documentType,  String documentUrl,  String fullName,  DateTime dateOfBirth,  String nationality,  VerificationStatus status,  DateTime submittedAt,  DateTime? reviewedAt,  String? reviewedBy,  String? rejectionReason,  Map<String, dynamic>? documentData)  $default,) {final _that = this;
switch (_that) {
case _IdentityVerificationModel():
return $default(_that.id,_that.userId,_that.documentType,_that.documentUrl,_that.fullName,_that.dateOfBirth,_that.nationality,_that.status,_that.submittedAt,_that.reviewedAt,_that.reviewedBy,_that.rejectionReason,_that.documentData);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  String documentType,  String documentUrl,  String fullName,  DateTime dateOfBirth,  String nationality,  VerificationStatus status,  DateTime submittedAt,  DateTime? reviewedAt,  String? reviewedBy,  String? rejectionReason,  Map<String, dynamic>? documentData)?  $default,) {final _that = this;
switch (_that) {
case _IdentityVerificationModel() when $default != null:
return $default(_that.id,_that.userId,_that.documentType,_that.documentUrl,_that.fullName,_that.dateOfBirth,_that.nationality,_that.status,_that.submittedAt,_that.reviewedAt,_that.reviewedBy,_that.rejectionReason,_that.documentData);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _IdentityVerificationModel implements IdentityVerificationModel {
  const _IdentityVerificationModel({required this.id, required this.userId, required this.documentType, required this.documentUrl, required this.fullName, required this.dateOfBirth, required this.nationality, required this.status, required this.submittedAt, this.reviewedAt, this.reviewedBy, this.rejectionReason, final  Map<String, dynamic>? documentData}): _documentData = documentData;
  factory _IdentityVerificationModel.fromJson(Map<String, dynamic> json) => _$IdentityVerificationModelFromJson(json);

@override final  String id;
@override final  String userId;
@override final  String documentType;
@override final  String documentUrl;
@override final  String fullName;
@override final  DateTime dateOfBirth;
@override final  String nationality;
@override final  VerificationStatus status;
@override final  DateTime submittedAt;
@override final  DateTime? reviewedAt;
@override final  String? reviewedBy;
@override final  String? rejectionReason;
 final  Map<String, dynamic>? _documentData;
@override Map<String, dynamic>? get documentData {
  final value = _documentData;
  if (value == null) return null;
  if (_documentData is EqualUnmodifiableMapView) return _documentData;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of IdentityVerificationModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$IdentityVerificationModelCopyWith<_IdentityVerificationModel> get copyWith => __$IdentityVerificationModelCopyWithImpl<_IdentityVerificationModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$IdentityVerificationModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _IdentityVerificationModel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.documentType, documentType) || other.documentType == documentType)&&(identical(other.documentUrl, documentUrl) || other.documentUrl == documentUrl)&&(identical(other.fullName, fullName) || other.fullName == fullName)&&(identical(other.dateOfBirth, dateOfBirth) || other.dateOfBirth == dateOfBirth)&&(identical(other.nationality, nationality) || other.nationality == nationality)&&(identical(other.status, status) || other.status == status)&&(identical(other.submittedAt, submittedAt) || other.submittedAt == submittedAt)&&(identical(other.reviewedAt, reviewedAt) || other.reviewedAt == reviewedAt)&&(identical(other.reviewedBy, reviewedBy) || other.reviewedBy == reviewedBy)&&(identical(other.rejectionReason, rejectionReason) || other.rejectionReason == rejectionReason)&&const DeepCollectionEquality().equals(other._documentData, _documentData));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,documentType,documentUrl,fullName,dateOfBirth,nationality,status,submittedAt,reviewedAt,reviewedBy,rejectionReason,const DeepCollectionEquality().hash(_documentData));

@override
String toString() {
  return 'IdentityVerificationModel(id: $id, userId: $userId, documentType: $documentType, documentUrl: $documentUrl, fullName: $fullName, dateOfBirth: $dateOfBirth, nationality: $nationality, status: $status, submittedAt: $submittedAt, reviewedAt: $reviewedAt, reviewedBy: $reviewedBy, rejectionReason: $rejectionReason, documentData: $documentData)';
}


}

/// @nodoc
abstract mixin class _$IdentityVerificationModelCopyWith<$Res> implements $IdentityVerificationModelCopyWith<$Res> {
  factory _$IdentityVerificationModelCopyWith(_IdentityVerificationModel value, $Res Function(_IdentityVerificationModel) _then) = __$IdentityVerificationModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, String documentType, String documentUrl, String fullName, DateTime dateOfBirth, String nationality, VerificationStatus status, DateTime submittedAt, DateTime? reviewedAt, String? reviewedBy, String? rejectionReason, Map<String, dynamic>? documentData
});




}
/// @nodoc
class __$IdentityVerificationModelCopyWithImpl<$Res>
    implements _$IdentityVerificationModelCopyWith<$Res> {
  __$IdentityVerificationModelCopyWithImpl(this._self, this._then);

  final _IdentityVerificationModel _self;
  final $Res Function(_IdentityVerificationModel) _then;

/// Create a copy of IdentityVerificationModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? documentType = null,Object? documentUrl = null,Object? fullName = null,Object? dateOfBirth = null,Object? nationality = null,Object? status = null,Object? submittedAt = null,Object? reviewedAt = freezed,Object? reviewedBy = freezed,Object? rejectionReason = freezed,Object? documentData = freezed,}) {
  return _then(_IdentityVerificationModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,documentType: null == documentType ? _self.documentType : documentType // ignore: cast_nullable_to_non_nullable
as String,documentUrl: null == documentUrl ? _self.documentUrl : documentUrl // ignore: cast_nullable_to_non_nullable
as String,fullName: null == fullName ? _self.fullName : fullName // ignore: cast_nullable_to_non_nullable
as String,dateOfBirth: null == dateOfBirth ? _self.dateOfBirth : dateOfBirth // ignore: cast_nullable_to_non_nullable
as DateTime,nationality: null == nationality ? _self.nationality : nationality // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as VerificationStatus,submittedAt: null == submittedAt ? _self.submittedAt : submittedAt // ignore: cast_nullable_to_non_nullable
as DateTime,reviewedAt: freezed == reviewedAt ? _self.reviewedAt : reviewedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,reviewedBy: freezed == reviewedBy ? _self.reviewedBy : reviewedBy // ignore: cast_nullable_to_non_nullable
as String?,rejectionReason: freezed == rejectionReason ? _self.rejectionReason : rejectionReason // ignore: cast_nullable_to_non_nullable
as String?,documentData: freezed == documentData ? _self._documentData : documentData // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}


/// @nodoc
mixin _$BusinessVerificationModel {

 String get id; String get userId; String get businessName; String get businessRegistrationNumber; String get businessAddress; String get businessCategory; String get documentUrl; String get contactEmail; String get contactPhone; VerificationStatus get status; DateTime get submittedAt; DateTime? get reviewedAt; String? get reviewedBy; String? get rejectionReason; String? get verifiedBusinessName; String? get verifiedBusinessLogo; Map<String, dynamic>? get businessData;
/// Create a copy of BusinessVerificationModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BusinessVerificationModelCopyWith<BusinessVerificationModel> get copyWith => _$BusinessVerificationModelCopyWithImpl<BusinessVerificationModel>(this as BusinessVerificationModel, _$identity);

  /// Serializes this BusinessVerificationModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BusinessVerificationModel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.businessName, businessName) || other.businessName == businessName)&&(identical(other.businessRegistrationNumber, businessRegistrationNumber) || other.businessRegistrationNumber == businessRegistrationNumber)&&(identical(other.businessAddress, businessAddress) || other.businessAddress == businessAddress)&&(identical(other.businessCategory, businessCategory) || other.businessCategory == businessCategory)&&(identical(other.documentUrl, documentUrl) || other.documentUrl == documentUrl)&&(identical(other.contactEmail, contactEmail) || other.contactEmail == contactEmail)&&(identical(other.contactPhone, contactPhone) || other.contactPhone == contactPhone)&&(identical(other.status, status) || other.status == status)&&(identical(other.submittedAt, submittedAt) || other.submittedAt == submittedAt)&&(identical(other.reviewedAt, reviewedAt) || other.reviewedAt == reviewedAt)&&(identical(other.reviewedBy, reviewedBy) || other.reviewedBy == reviewedBy)&&(identical(other.rejectionReason, rejectionReason) || other.rejectionReason == rejectionReason)&&(identical(other.verifiedBusinessName, verifiedBusinessName) || other.verifiedBusinessName == verifiedBusinessName)&&(identical(other.verifiedBusinessLogo, verifiedBusinessLogo) || other.verifiedBusinessLogo == verifiedBusinessLogo)&&const DeepCollectionEquality().equals(other.businessData, businessData));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,businessName,businessRegistrationNumber,businessAddress,businessCategory,documentUrl,contactEmail,contactPhone,status,submittedAt,reviewedAt,reviewedBy,rejectionReason,verifiedBusinessName,verifiedBusinessLogo,const DeepCollectionEquality().hash(businessData));

@override
String toString() {
  return 'BusinessVerificationModel(id: $id, userId: $userId, businessName: $businessName, businessRegistrationNumber: $businessRegistrationNumber, businessAddress: $businessAddress, businessCategory: $businessCategory, documentUrl: $documentUrl, contactEmail: $contactEmail, contactPhone: $contactPhone, status: $status, submittedAt: $submittedAt, reviewedAt: $reviewedAt, reviewedBy: $reviewedBy, rejectionReason: $rejectionReason, verifiedBusinessName: $verifiedBusinessName, verifiedBusinessLogo: $verifiedBusinessLogo, businessData: $businessData)';
}


}

/// @nodoc
abstract mixin class $BusinessVerificationModelCopyWith<$Res>  {
  factory $BusinessVerificationModelCopyWith(BusinessVerificationModel value, $Res Function(BusinessVerificationModel) _then) = _$BusinessVerificationModelCopyWithImpl;
@useResult
$Res call({
 String id, String userId, String businessName, String businessRegistrationNumber, String businessAddress, String businessCategory, String documentUrl, String contactEmail, String contactPhone, VerificationStatus status, DateTime submittedAt, DateTime? reviewedAt, String? reviewedBy, String? rejectionReason, String? verifiedBusinessName, String? verifiedBusinessLogo, Map<String, dynamic>? businessData
});




}
/// @nodoc
class _$BusinessVerificationModelCopyWithImpl<$Res>
    implements $BusinessVerificationModelCopyWith<$Res> {
  _$BusinessVerificationModelCopyWithImpl(this._self, this._then);

  final BusinessVerificationModel _self;
  final $Res Function(BusinessVerificationModel) _then;

/// Create a copy of BusinessVerificationModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? businessName = null,Object? businessRegistrationNumber = null,Object? businessAddress = null,Object? businessCategory = null,Object? documentUrl = null,Object? contactEmail = null,Object? contactPhone = null,Object? status = null,Object? submittedAt = null,Object? reviewedAt = freezed,Object? reviewedBy = freezed,Object? rejectionReason = freezed,Object? verifiedBusinessName = freezed,Object? verifiedBusinessLogo = freezed,Object? businessData = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,businessName: null == businessName ? _self.businessName : businessName // ignore: cast_nullable_to_non_nullable
as String,businessRegistrationNumber: null == businessRegistrationNumber ? _self.businessRegistrationNumber : businessRegistrationNumber // ignore: cast_nullable_to_non_nullable
as String,businessAddress: null == businessAddress ? _self.businessAddress : businessAddress // ignore: cast_nullable_to_non_nullable
as String,businessCategory: null == businessCategory ? _self.businessCategory : businessCategory // ignore: cast_nullable_to_non_nullable
as String,documentUrl: null == documentUrl ? _self.documentUrl : documentUrl // ignore: cast_nullable_to_non_nullable
as String,contactEmail: null == contactEmail ? _self.contactEmail : contactEmail // ignore: cast_nullable_to_non_nullable
as String,contactPhone: null == contactPhone ? _self.contactPhone : contactPhone // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as VerificationStatus,submittedAt: null == submittedAt ? _self.submittedAt : submittedAt // ignore: cast_nullable_to_non_nullable
as DateTime,reviewedAt: freezed == reviewedAt ? _self.reviewedAt : reviewedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,reviewedBy: freezed == reviewedBy ? _self.reviewedBy : reviewedBy // ignore: cast_nullable_to_non_nullable
as String?,rejectionReason: freezed == rejectionReason ? _self.rejectionReason : rejectionReason // ignore: cast_nullable_to_non_nullable
as String?,verifiedBusinessName: freezed == verifiedBusinessName ? _self.verifiedBusinessName : verifiedBusinessName // ignore: cast_nullable_to_non_nullable
as String?,verifiedBusinessLogo: freezed == verifiedBusinessLogo ? _self.verifiedBusinessLogo : verifiedBusinessLogo // ignore: cast_nullable_to_non_nullable
as String?,businessData: freezed == businessData ? _self.businessData : businessData // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [BusinessVerificationModel].
extension BusinessVerificationModelPatterns on BusinessVerificationModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _BusinessVerificationModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _BusinessVerificationModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _BusinessVerificationModel value)  $default,){
final _that = this;
switch (_that) {
case _BusinessVerificationModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _BusinessVerificationModel value)?  $default,){
final _that = this;
switch (_that) {
case _BusinessVerificationModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  String businessName,  String businessRegistrationNumber,  String businessAddress,  String businessCategory,  String documentUrl,  String contactEmail,  String contactPhone,  VerificationStatus status,  DateTime submittedAt,  DateTime? reviewedAt,  String? reviewedBy,  String? rejectionReason,  String? verifiedBusinessName,  String? verifiedBusinessLogo,  Map<String, dynamic>? businessData)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _BusinessVerificationModel() when $default != null:
return $default(_that.id,_that.userId,_that.businessName,_that.businessRegistrationNumber,_that.businessAddress,_that.businessCategory,_that.documentUrl,_that.contactEmail,_that.contactPhone,_that.status,_that.submittedAt,_that.reviewedAt,_that.reviewedBy,_that.rejectionReason,_that.verifiedBusinessName,_that.verifiedBusinessLogo,_that.businessData);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  String businessName,  String businessRegistrationNumber,  String businessAddress,  String businessCategory,  String documentUrl,  String contactEmail,  String contactPhone,  VerificationStatus status,  DateTime submittedAt,  DateTime? reviewedAt,  String? reviewedBy,  String? rejectionReason,  String? verifiedBusinessName,  String? verifiedBusinessLogo,  Map<String, dynamic>? businessData)  $default,) {final _that = this;
switch (_that) {
case _BusinessVerificationModel():
return $default(_that.id,_that.userId,_that.businessName,_that.businessRegistrationNumber,_that.businessAddress,_that.businessCategory,_that.documentUrl,_that.contactEmail,_that.contactPhone,_that.status,_that.submittedAt,_that.reviewedAt,_that.reviewedBy,_that.rejectionReason,_that.verifiedBusinessName,_that.verifiedBusinessLogo,_that.businessData);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  String businessName,  String businessRegistrationNumber,  String businessAddress,  String businessCategory,  String documentUrl,  String contactEmail,  String contactPhone,  VerificationStatus status,  DateTime submittedAt,  DateTime? reviewedAt,  String? reviewedBy,  String? rejectionReason,  String? verifiedBusinessName,  String? verifiedBusinessLogo,  Map<String, dynamic>? businessData)?  $default,) {final _that = this;
switch (_that) {
case _BusinessVerificationModel() when $default != null:
return $default(_that.id,_that.userId,_that.businessName,_that.businessRegistrationNumber,_that.businessAddress,_that.businessCategory,_that.documentUrl,_that.contactEmail,_that.contactPhone,_that.status,_that.submittedAt,_that.reviewedAt,_that.reviewedBy,_that.rejectionReason,_that.verifiedBusinessName,_that.verifiedBusinessLogo,_that.businessData);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _BusinessVerificationModel implements BusinessVerificationModel {
  const _BusinessVerificationModel({required this.id, required this.userId, required this.businessName, required this.businessRegistrationNumber, required this.businessAddress, required this.businessCategory, required this.documentUrl, required this.contactEmail, required this.contactPhone, required this.status, required this.submittedAt, this.reviewedAt, this.reviewedBy, this.rejectionReason, this.verifiedBusinessName, this.verifiedBusinessLogo, final  Map<String, dynamic>? businessData}): _businessData = businessData;
  factory _BusinessVerificationModel.fromJson(Map<String, dynamic> json) => _$BusinessVerificationModelFromJson(json);

@override final  String id;
@override final  String userId;
@override final  String businessName;
@override final  String businessRegistrationNumber;
@override final  String businessAddress;
@override final  String businessCategory;
@override final  String documentUrl;
@override final  String contactEmail;
@override final  String contactPhone;
@override final  VerificationStatus status;
@override final  DateTime submittedAt;
@override final  DateTime? reviewedAt;
@override final  String? reviewedBy;
@override final  String? rejectionReason;
@override final  String? verifiedBusinessName;
@override final  String? verifiedBusinessLogo;
 final  Map<String, dynamic>? _businessData;
@override Map<String, dynamic>? get businessData {
  final value = _businessData;
  if (value == null) return null;
  if (_businessData is EqualUnmodifiableMapView) return _businessData;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of BusinessVerificationModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BusinessVerificationModelCopyWith<_BusinessVerificationModel> get copyWith => __$BusinessVerificationModelCopyWithImpl<_BusinessVerificationModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BusinessVerificationModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BusinessVerificationModel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.businessName, businessName) || other.businessName == businessName)&&(identical(other.businessRegistrationNumber, businessRegistrationNumber) || other.businessRegistrationNumber == businessRegistrationNumber)&&(identical(other.businessAddress, businessAddress) || other.businessAddress == businessAddress)&&(identical(other.businessCategory, businessCategory) || other.businessCategory == businessCategory)&&(identical(other.documentUrl, documentUrl) || other.documentUrl == documentUrl)&&(identical(other.contactEmail, contactEmail) || other.contactEmail == contactEmail)&&(identical(other.contactPhone, contactPhone) || other.contactPhone == contactPhone)&&(identical(other.status, status) || other.status == status)&&(identical(other.submittedAt, submittedAt) || other.submittedAt == submittedAt)&&(identical(other.reviewedAt, reviewedAt) || other.reviewedAt == reviewedAt)&&(identical(other.reviewedBy, reviewedBy) || other.reviewedBy == reviewedBy)&&(identical(other.rejectionReason, rejectionReason) || other.rejectionReason == rejectionReason)&&(identical(other.verifiedBusinessName, verifiedBusinessName) || other.verifiedBusinessName == verifiedBusinessName)&&(identical(other.verifiedBusinessLogo, verifiedBusinessLogo) || other.verifiedBusinessLogo == verifiedBusinessLogo)&&const DeepCollectionEquality().equals(other._businessData, _businessData));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,businessName,businessRegistrationNumber,businessAddress,businessCategory,documentUrl,contactEmail,contactPhone,status,submittedAt,reviewedAt,reviewedBy,rejectionReason,verifiedBusinessName,verifiedBusinessLogo,const DeepCollectionEquality().hash(_businessData));

@override
String toString() {
  return 'BusinessVerificationModel(id: $id, userId: $userId, businessName: $businessName, businessRegistrationNumber: $businessRegistrationNumber, businessAddress: $businessAddress, businessCategory: $businessCategory, documentUrl: $documentUrl, contactEmail: $contactEmail, contactPhone: $contactPhone, status: $status, submittedAt: $submittedAt, reviewedAt: $reviewedAt, reviewedBy: $reviewedBy, rejectionReason: $rejectionReason, verifiedBusinessName: $verifiedBusinessName, verifiedBusinessLogo: $verifiedBusinessLogo, businessData: $businessData)';
}


}

/// @nodoc
abstract mixin class _$BusinessVerificationModelCopyWith<$Res> implements $BusinessVerificationModelCopyWith<$Res> {
  factory _$BusinessVerificationModelCopyWith(_BusinessVerificationModel value, $Res Function(_BusinessVerificationModel) _then) = __$BusinessVerificationModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, String businessName, String businessRegistrationNumber, String businessAddress, String businessCategory, String documentUrl, String contactEmail, String contactPhone, VerificationStatus status, DateTime submittedAt, DateTime? reviewedAt, String? reviewedBy, String? rejectionReason, String? verifiedBusinessName, String? verifiedBusinessLogo, Map<String, dynamic>? businessData
});




}
/// @nodoc
class __$BusinessVerificationModelCopyWithImpl<$Res>
    implements _$BusinessVerificationModelCopyWith<$Res> {
  __$BusinessVerificationModelCopyWithImpl(this._self, this._then);

  final _BusinessVerificationModel _self;
  final $Res Function(_BusinessVerificationModel) _then;

/// Create a copy of BusinessVerificationModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? businessName = null,Object? businessRegistrationNumber = null,Object? businessAddress = null,Object? businessCategory = null,Object? documentUrl = null,Object? contactEmail = null,Object? contactPhone = null,Object? status = null,Object? submittedAt = null,Object? reviewedAt = freezed,Object? reviewedBy = freezed,Object? rejectionReason = freezed,Object? verifiedBusinessName = freezed,Object? verifiedBusinessLogo = freezed,Object? businessData = freezed,}) {
  return _then(_BusinessVerificationModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,businessName: null == businessName ? _self.businessName : businessName // ignore: cast_nullable_to_non_nullable
as String,businessRegistrationNumber: null == businessRegistrationNumber ? _self.businessRegistrationNumber : businessRegistrationNumber // ignore: cast_nullable_to_non_nullable
as String,businessAddress: null == businessAddress ? _self.businessAddress : businessAddress // ignore: cast_nullable_to_non_nullable
as String,businessCategory: null == businessCategory ? _self.businessCategory : businessCategory // ignore: cast_nullable_to_non_nullable
as String,documentUrl: null == documentUrl ? _self.documentUrl : documentUrl // ignore: cast_nullable_to_non_nullable
as String,contactEmail: null == contactEmail ? _self.contactEmail : contactEmail // ignore: cast_nullable_to_non_nullable
as String,contactPhone: null == contactPhone ? _self.contactPhone : contactPhone // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as VerificationStatus,submittedAt: null == submittedAt ? _self.submittedAt : submittedAt // ignore: cast_nullable_to_non_nullable
as DateTime,reviewedAt: freezed == reviewedAt ? _self.reviewedAt : reviewedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,reviewedBy: freezed == reviewedBy ? _self.reviewedBy : reviewedBy // ignore: cast_nullable_to_non_nullable
as String?,rejectionReason: freezed == rejectionReason ? _self.rejectionReason : rejectionReason // ignore: cast_nullable_to_non_nullable
as String?,verifiedBusinessName: freezed == verifiedBusinessName ? _self.verifiedBusinessName : verifiedBusinessName // ignore: cast_nullable_to_non_nullable
as String?,verifiedBusinessLogo: freezed == verifiedBusinessLogo ? _self.verifiedBusinessLogo : verifiedBusinessLogo // ignore: cast_nullable_to_non_nullable
as String?,businessData: freezed == businessData ? _self._businessData : businessData // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}

// dart format on
