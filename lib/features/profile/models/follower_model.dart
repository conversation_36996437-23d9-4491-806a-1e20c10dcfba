import 'package:freezed_annotation/freezed_annotation.dart';

part 'follower_model.freezed.dart';
part 'follower_model.g.dart';

@freezed
abstract class FollowerModel with _$FollowerModel {
  const factory FollowerModel({
    required String id,
    required String username,
    required String name,
    required String profilePictureUrl,
    required String? bio,
    required bool isVerified,
    required AccountType accountType,
    required Gender gender,
    required bool isFollowingBack,
    required bool isMutual,
    required DateTime followedAt,
    required bool isMuted,
    required bool isSelected, // For bulk actions
    required List<String> tags, // For billionaire, celebrity tags
  }) = _FollowerModel;

  factory FollowerModel.fromJson(Map<String, dynamic> json) =>
      _$FollowerModelFromJson(json);
}

@freezed
abstract class FollowingModel with _$FollowingModel {
  const factory FollowingModel({
    required String id,
    required String username,
    required String name,
    required String profilePictureUrl,
    required String? bio,
    required bool isVerified,
    required AccountType accountType,
    required Gender gender,
    required bool isFollowingBack,
    required bool isMutual,
    required DateTime followedAt,
    required bool isMuted,
    required bool isSelected, // For bulk actions
    required List<String> tags, // For billionaire, celebrity tags
  }) = _FollowingModel;

  factory FollowingModel.fromJson(Map<String, dynamic> json) =>
      _$FollowingModelFromJson(json);
}

@freezed
abstract class FollowersAnalytics with _$FollowersAnalytics {
  const factory FollowersAnalytics({
    required int totalFollowers,
    required int totalFollowing,
    required int notFollowingBack,
    required int verifiedAccountsFollowed,
    required int billionairesFollowed,
    required int celebritiesFollowed,
    required int businessesFollowed,
    required GenderSplit genderSplit,
    required AccountTypeSplit accountTypeSplit,
    required MutualStatusSplit mutualStatusSplit,
  }) = _FollowersAnalytics;

  factory FollowersAnalytics.fromJson(Map<String, dynamic> json) =>
      _$FollowersAnalyticsFromJson(json);
}

@freezed
abstract class GenderSplit with _$GenderSplit {
  const factory GenderSplit({
    required int male,
    required int female,
    required int other,
    required double malePercentage,
    required double femalePercentage,
    required double otherPercentage,
  }) = _GenderSplit;

  factory GenderSplit.fromJson(Map<String, dynamic> json) =>
      _$GenderSplitFromJson(json);
}

@freezed
abstract class AccountTypeSplit with _$AccountTypeSplit {
  const factory AccountTypeSplit({
    required int verified,
    required int celebrities,
    required int businesses,
    required int billionaires,
    required int regular,
  }) = _AccountTypeSplit;

  factory AccountTypeSplit.fromJson(Map<String, dynamic> json) =>
      _$AccountTypeSplitFromJson(json);
}

@freezed
abstract class MutualStatusSplit with _$MutualStatusSplit {
  const factory MutualStatusSplit({
    required int mutual,
    required int notFollowingBack,
    required int notFollowedBack,
  }) = _MutualStatusSplit;

  factory MutualStatusSplit.fromJson(Map<String, dynamic> json) =>
      _$MutualStatusSplitFromJson(json);
}

@freezed
abstract class FollowersFilter with _$FollowersFilter {
  const factory FollowersFilter({
    required FilterType type,
    required Gender? gender,
    required AccountType? accountType,
    required MutualStatus? mutualStatus,
    required bool? isVerified,
    required bool? isMuted,
    required String? searchQuery,
  }) = _FollowersFilter;

  factory FollowersFilter.fromJson(Map<String, dynamic> json) =>
      _$FollowersFilterFromJson(json);
}

enum AccountType { regular, verified, celebrity, business, billionaire }

enum Gender { male, female, other }

enum MutualStatus { mutual, notFollowingBack, notFollowedBack }

enum FilterType { followers, following, all }

enum BulkAction { unfollow, mute, unmute, addToList }
