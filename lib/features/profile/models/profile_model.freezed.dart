// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'profile_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ProfileModel {

 String get id; String get username; String get name; String get profilePictureUrl; String get bio; int get postCount; int get followerCount; int get followingCount;// Enhanced fields for complete profile
 String? get bannerImageUrl; String? get website; String? get location; String? get phone; String? get email; bool get isPrivate; bool get allowMessages; bool get showActivityStatus; bool get isVerified; bool get isBillionaire; String get userType; DateTime? get createdAt; DateTime? get updatedAt;// Business Account fields for sellers
 bool get isBusinessAccount; String? get businessName; String? get businessLogoUrl; String? get businessEmail; String? get businessPhone; String? get businessDescription; bool get businessVerified; bool get businessExclusive; String? get businessCategory; String? get businessWebsite; String? get businessAddress; DateTime? get businessCreatedAt; DateTime? get businessVerifiedAt; bool get isAdmin;
/// Create a copy of ProfileModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProfileModelCopyWith<ProfileModel> get copyWith => _$ProfileModelCopyWithImpl<ProfileModel>(this as ProfileModel, _$identity);

  /// Serializes this ProfileModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProfileModel&&(identical(other.id, id) || other.id == id)&&(identical(other.username, username) || other.username == username)&&(identical(other.name, name) || other.name == name)&&(identical(other.profilePictureUrl, profilePictureUrl) || other.profilePictureUrl == profilePictureUrl)&&(identical(other.bio, bio) || other.bio == bio)&&(identical(other.postCount, postCount) || other.postCount == postCount)&&(identical(other.followerCount, followerCount) || other.followerCount == followerCount)&&(identical(other.followingCount, followingCount) || other.followingCount == followingCount)&&(identical(other.bannerImageUrl, bannerImageUrl) || other.bannerImageUrl == bannerImageUrl)&&(identical(other.website, website) || other.website == website)&&(identical(other.location, location) || other.location == location)&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.email, email) || other.email == email)&&(identical(other.isPrivate, isPrivate) || other.isPrivate == isPrivate)&&(identical(other.allowMessages, allowMessages) || other.allowMessages == allowMessages)&&(identical(other.showActivityStatus, showActivityStatus) || other.showActivityStatus == showActivityStatus)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.isBillionaire, isBillionaire) || other.isBillionaire == isBillionaire)&&(identical(other.userType, userType) || other.userType == userType)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isBusinessAccount, isBusinessAccount) || other.isBusinessAccount == isBusinessAccount)&&(identical(other.businessName, businessName) || other.businessName == businessName)&&(identical(other.businessLogoUrl, businessLogoUrl) || other.businessLogoUrl == businessLogoUrl)&&(identical(other.businessEmail, businessEmail) || other.businessEmail == businessEmail)&&(identical(other.businessPhone, businessPhone) || other.businessPhone == businessPhone)&&(identical(other.businessDescription, businessDescription) || other.businessDescription == businessDescription)&&(identical(other.businessVerified, businessVerified) || other.businessVerified == businessVerified)&&(identical(other.businessExclusive, businessExclusive) || other.businessExclusive == businessExclusive)&&(identical(other.businessCategory, businessCategory) || other.businessCategory == businessCategory)&&(identical(other.businessWebsite, businessWebsite) || other.businessWebsite == businessWebsite)&&(identical(other.businessAddress, businessAddress) || other.businessAddress == businessAddress)&&(identical(other.businessCreatedAt, businessCreatedAt) || other.businessCreatedAt == businessCreatedAt)&&(identical(other.businessVerifiedAt, businessVerifiedAt) || other.businessVerifiedAt == businessVerifiedAt)&&(identical(other.isAdmin, isAdmin) || other.isAdmin == isAdmin));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,username,name,profilePictureUrl,bio,postCount,followerCount,followingCount,bannerImageUrl,website,location,phone,email,isPrivate,allowMessages,showActivityStatus,isVerified,isBillionaire,userType,createdAt,updatedAt,isBusinessAccount,businessName,businessLogoUrl,businessEmail,businessPhone,businessDescription,businessVerified,businessExclusive,businessCategory,businessWebsite,businessAddress,businessCreatedAt,businessVerifiedAt,isAdmin]);

@override
String toString() {
  return 'ProfileModel(id: $id, username: $username, name: $name, profilePictureUrl: $profilePictureUrl, bio: $bio, postCount: $postCount, followerCount: $followerCount, followingCount: $followingCount, bannerImageUrl: $bannerImageUrl, website: $website, location: $location, phone: $phone, email: $email, isPrivate: $isPrivate, allowMessages: $allowMessages, showActivityStatus: $showActivityStatus, isVerified: $isVerified, isBillionaire: $isBillionaire, userType: $userType, createdAt: $createdAt, updatedAt: $updatedAt, isBusinessAccount: $isBusinessAccount, businessName: $businessName, businessLogoUrl: $businessLogoUrl, businessEmail: $businessEmail, businessPhone: $businessPhone, businessDescription: $businessDescription, businessVerified: $businessVerified, businessExclusive: $businessExclusive, businessCategory: $businessCategory, businessWebsite: $businessWebsite, businessAddress: $businessAddress, businessCreatedAt: $businessCreatedAt, businessVerifiedAt: $businessVerifiedAt, isAdmin: $isAdmin)';
}


}

/// @nodoc
abstract mixin class $ProfileModelCopyWith<$Res>  {
  factory $ProfileModelCopyWith(ProfileModel value, $Res Function(ProfileModel) _then) = _$ProfileModelCopyWithImpl;
@useResult
$Res call({
 String id, String username, String name, String profilePictureUrl, String bio, int postCount, int followerCount, int followingCount, String? bannerImageUrl, String? website, String? location, String? phone, String? email, bool isPrivate, bool allowMessages, bool showActivityStatus, bool isVerified, bool isBillionaire, String userType, DateTime? createdAt, DateTime? updatedAt, bool isBusinessAccount, String? businessName, String? businessLogoUrl, String? businessEmail, String? businessPhone, String? businessDescription, bool businessVerified, bool businessExclusive, String? businessCategory, String? businessWebsite, String? businessAddress, DateTime? businessCreatedAt, DateTime? businessVerifiedAt, bool isAdmin
});




}
/// @nodoc
class _$ProfileModelCopyWithImpl<$Res>
    implements $ProfileModelCopyWith<$Res> {
  _$ProfileModelCopyWithImpl(this._self, this._then);

  final ProfileModel _self;
  final $Res Function(ProfileModel) _then;

/// Create a copy of ProfileModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? username = null,Object? name = null,Object? profilePictureUrl = null,Object? bio = null,Object? postCount = null,Object? followerCount = null,Object? followingCount = null,Object? bannerImageUrl = freezed,Object? website = freezed,Object? location = freezed,Object? phone = freezed,Object? email = freezed,Object? isPrivate = null,Object? allowMessages = null,Object? showActivityStatus = null,Object? isVerified = null,Object? isBillionaire = null,Object? userType = null,Object? createdAt = freezed,Object? updatedAt = freezed,Object? isBusinessAccount = null,Object? businessName = freezed,Object? businessLogoUrl = freezed,Object? businessEmail = freezed,Object? businessPhone = freezed,Object? businessDescription = freezed,Object? businessVerified = null,Object? businessExclusive = null,Object? businessCategory = freezed,Object? businessWebsite = freezed,Object? businessAddress = freezed,Object? businessCreatedAt = freezed,Object? businessVerifiedAt = freezed,Object? isAdmin = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,profilePictureUrl: null == profilePictureUrl ? _self.profilePictureUrl : profilePictureUrl // ignore: cast_nullable_to_non_nullable
as String,bio: null == bio ? _self.bio : bio // ignore: cast_nullable_to_non_nullable
as String,postCount: null == postCount ? _self.postCount : postCount // ignore: cast_nullable_to_non_nullable
as int,followerCount: null == followerCount ? _self.followerCount : followerCount // ignore: cast_nullable_to_non_nullable
as int,followingCount: null == followingCount ? _self.followingCount : followingCount // ignore: cast_nullable_to_non_nullable
as int,bannerImageUrl: freezed == bannerImageUrl ? _self.bannerImageUrl : bannerImageUrl // ignore: cast_nullable_to_non_nullable
as String?,website: freezed == website ? _self.website : website // ignore: cast_nullable_to_non_nullable
as String?,location: freezed == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String?,phone: freezed == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String?,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,isPrivate: null == isPrivate ? _self.isPrivate : isPrivate // ignore: cast_nullable_to_non_nullable
as bool,allowMessages: null == allowMessages ? _self.allowMessages : allowMessages // ignore: cast_nullable_to_non_nullable
as bool,showActivityStatus: null == showActivityStatus ? _self.showActivityStatus : showActivityStatus // ignore: cast_nullable_to_non_nullable
as bool,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,isBillionaire: null == isBillionaire ? _self.isBillionaire : isBillionaire // ignore: cast_nullable_to_non_nullable
as bool,userType: null == userType ? _self.userType : userType // ignore: cast_nullable_to_non_nullable
as String,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isBusinessAccount: null == isBusinessAccount ? _self.isBusinessAccount : isBusinessAccount // ignore: cast_nullable_to_non_nullable
as bool,businessName: freezed == businessName ? _self.businessName : businessName // ignore: cast_nullable_to_non_nullable
as String?,businessLogoUrl: freezed == businessLogoUrl ? _self.businessLogoUrl : businessLogoUrl // ignore: cast_nullable_to_non_nullable
as String?,businessEmail: freezed == businessEmail ? _self.businessEmail : businessEmail // ignore: cast_nullable_to_non_nullable
as String?,businessPhone: freezed == businessPhone ? _self.businessPhone : businessPhone // ignore: cast_nullable_to_non_nullable
as String?,businessDescription: freezed == businessDescription ? _self.businessDescription : businessDescription // ignore: cast_nullable_to_non_nullable
as String?,businessVerified: null == businessVerified ? _self.businessVerified : businessVerified // ignore: cast_nullable_to_non_nullable
as bool,businessExclusive: null == businessExclusive ? _self.businessExclusive : businessExclusive // ignore: cast_nullable_to_non_nullable
as bool,businessCategory: freezed == businessCategory ? _self.businessCategory : businessCategory // ignore: cast_nullable_to_non_nullable
as String?,businessWebsite: freezed == businessWebsite ? _self.businessWebsite : businessWebsite // ignore: cast_nullable_to_non_nullable
as String?,businessAddress: freezed == businessAddress ? _self.businessAddress : businessAddress // ignore: cast_nullable_to_non_nullable
as String?,businessCreatedAt: freezed == businessCreatedAt ? _self.businessCreatedAt : businessCreatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,businessVerifiedAt: freezed == businessVerifiedAt ? _self.businessVerifiedAt : businessVerifiedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isAdmin: null == isAdmin ? _self.isAdmin : isAdmin // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [ProfileModel].
extension ProfileModelPatterns on ProfileModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ProfileModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ProfileModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ProfileModel value)  $default,){
final _that = this;
switch (_that) {
case _ProfileModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ProfileModel value)?  $default,){
final _that = this;
switch (_that) {
case _ProfileModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String username,  String name,  String profilePictureUrl,  String bio,  int postCount,  int followerCount,  int followingCount,  String? bannerImageUrl,  String? website,  String? location,  String? phone,  String? email,  bool isPrivate,  bool allowMessages,  bool showActivityStatus,  bool isVerified,  bool isBillionaire,  String userType,  DateTime? createdAt,  DateTime? updatedAt,  bool isBusinessAccount,  String? businessName,  String? businessLogoUrl,  String? businessEmail,  String? businessPhone,  String? businessDescription,  bool businessVerified,  bool businessExclusive,  String? businessCategory,  String? businessWebsite,  String? businessAddress,  DateTime? businessCreatedAt,  DateTime? businessVerifiedAt,  bool isAdmin)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ProfileModel() when $default != null:
return $default(_that.id,_that.username,_that.name,_that.profilePictureUrl,_that.bio,_that.postCount,_that.followerCount,_that.followingCount,_that.bannerImageUrl,_that.website,_that.location,_that.phone,_that.email,_that.isPrivate,_that.allowMessages,_that.showActivityStatus,_that.isVerified,_that.isBillionaire,_that.userType,_that.createdAt,_that.updatedAt,_that.isBusinessAccount,_that.businessName,_that.businessLogoUrl,_that.businessEmail,_that.businessPhone,_that.businessDescription,_that.businessVerified,_that.businessExclusive,_that.businessCategory,_that.businessWebsite,_that.businessAddress,_that.businessCreatedAt,_that.businessVerifiedAt,_that.isAdmin);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String username,  String name,  String profilePictureUrl,  String bio,  int postCount,  int followerCount,  int followingCount,  String? bannerImageUrl,  String? website,  String? location,  String? phone,  String? email,  bool isPrivate,  bool allowMessages,  bool showActivityStatus,  bool isVerified,  bool isBillionaire,  String userType,  DateTime? createdAt,  DateTime? updatedAt,  bool isBusinessAccount,  String? businessName,  String? businessLogoUrl,  String? businessEmail,  String? businessPhone,  String? businessDescription,  bool businessVerified,  bool businessExclusive,  String? businessCategory,  String? businessWebsite,  String? businessAddress,  DateTime? businessCreatedAt,  DateTime? businessVerifiedAt,  bool isAdmin)  $default,) {final _that = this;
switch (_that) {
case _ProfileModel():
return $default(_that.id,_that.username,_that.name,_that.profilePictureUrl,_that.bio,_that.postCount,_that.followerCount,_that.followingCount,_that.bannerImageUrl,_that.website,_that.location,_that.phone,_that.email,_that.isPrivate,_that.allowMessages,_that.showActivityStatus,_that.isVerified,_that.isBillionaire,_that.userType,_that.createdAt,_that.updatedAt,_that.isBusinessAccount,_that.businessName,_that.businessLogoUrl,_that.businessEmail,_that.businessPhone,_that.businessDescription,_that.businessVerified,_that.businessExclusive,_that.businessCategory,_that.businessWebsite,_that.businessAddress,_that.businessCreatedAt,_that.businessVerifiedAt,_that.isAdmin);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String username,  String name,  String profilePictureUrl,  String bio,  int postCount,  int followerCount,  int followingCount,  String? bannerImageUrl,  String? website,  String? location,  String? phone,  String? email,  bool isPrivate,  bool allowMessages,  bool showActivityStatus,  bool isVerified,  bool isBillionaire,  String userType,  DateTime? createdAt,  DateTime? updatedAt,  bool isBusinessAccount,  String? businessName,  String? businessLogoUrl,  String? businessEmail,  String? businessPhone,  String? businessDescription,  bool businessVerified,  bool businessExclusive,  String? businessCategory,  String? businessWebsite,  String? businessAddress,  DateTime? businessCreatedAt,  DateTime? businessVerifiedAt,  bool isAdmin)?  $default,) {final _that = this;
switch (_that) {
case _ProfileModel() when $default != null:
return $default(_that.id,_that.username,_that.name,_that.profilePictureUrl,_that.bio,_that.postCount,_that.followerCount,_that.followingCount,_that.bannerImageUrl,_that.website,_that.location,_that.phone,_that.email,_that.isPrivate,_that.allowMessages,_that.showActivityStatus,_that.isVerified,_that.isBillionaire,_that.userType,_that.createdAt,_that.updatedAt,_that.isBusinessAccount,_that.businessName,_that.businessLogoUrl,_that.businessEmail,_that.businessPhone,_that.businessDescription,_that.businessVerified,_that.businessExclusive,_that.businessCategory,_that.businessWebsite,_that.businessAddress,_that.businessCreatedAt,_that.businessVerifiedAt,_that.isAdmin);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ProfileModel implements ProfileModel {
  const _ProfileModel({required this.id, required this.username, required this.name, required this.profilePictureUrl, required this.bio, required this.postCount, required this.followerCount, required this.followingCount, this.bannerImageUrl, this.website, this.location, this.phone, this.email, this.isPrivate = false, this.allowMessages = true, this.showActivityStatus = true, this.isVerified = false, this.isBillionaire = false, this.userType = 'regular', this.createdAt, this.updatedAt, this.isBusinessAccount = false, this.businessName, this.businessLogoUrl, this.businessEmail, this.businessPhone, this.businessDescription, this.businessVerified = false, this.businessExclusive = false, this.businessCategory, this.businessWebsite, this.businessAddress, this.businessCreatedAt, this.businessVerifiedAt, this.isAdmin = false});
  factory _ProfileModel.fromJson(Map<String, dynamic> json) => _$ProfileModelFromJson(json);

@override final  String id;
@override final  String username;
@override final  String name;
@override final  String profilePictureUrl;
@override final  String bio;
@override final  int postCount;
@override final  int followerCount;
@override final  int followingCount;
// Enhanced fields for complete profile
@override final  String? bannerImageUrl;
@override final  String? website;
@override final  String? location;
@override final  String? phone;
@override final  String? email;
@override@JsonKey() final  bool isPrivate;
@override@JsonKey() final  bool allowMessages;
@override@JsonKey() final  bool showActivityStatus;
@override@JsonKey() final  bool isVerified;
@override@JsonKey() final  bool isBillionaire;
@override@JsonKey() final  String userType;
@override final  DateTime? createdAt;
@override final  DateTime? updatedAt;
// Business Account fields for sellers
@override@JsonKey() final  bool isBusinessAccount;
@override final  String? businessName;
@override final  String? businessLogoUrl;
@override final  String? businessEmail;
@override final  String? businessPhone;
@override final  String? businessDescription;
@override@JsonKey() final  bool businessVerified;
@override@JsonKey() final  bool businessExclusive;
@override final  String? businessCategory;
@override final  String? businessWebsite;
@override final  String? businessAddress;
@override final  DateTime? businessCreatedAt;
@override final  DateTime? businessVerifiedAt;
@override@JsonKey() final  bool isAdmin;

/// Create a copy of ProfileModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProfileModelCopyWith<_ProfileModel> get copyWith => __$ProfileModelCopyWithImpl<_ProfileModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ProfileModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProfileModel&&(identical(other.id, id) || other.id == id)&&(identical(other.username, username) || other.username == username)&&(identical(other.name, name) || other.name == name)&&(identical(other.profilePictureUrl, profilePictureUrl) || other.profilePictureUrl == profilePictureUrl)&&(identical(other.bio, bio) || other.bio == bio)&&(identical(other.postCount, postCount) || other.postCount == postCount)&&(identical(other.followerCount, followerCount) || other.followerCount == followerCount)&&(identical(other.followingCount, followingCount) || other.followingCount == followingCount)&&(identical(other.bannerImageUrl, bannerImageUrl) || other.bannerImageUrl == bannerImageUrl)&&(identical(other.website, website) || other.website == website)&&(identical(other.location, location) || other.location == location)&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.email, email) || other.email == email)&&(identical(other.isPrivate, isPrivate) || other.isPrivate == isPrivate)&&(identical(other.allowMessages, allowMessages) || other.allowMessages == allowMessages)&&(identical(other.showActivityStatus, showActivityStatus) || other.showActivityStatus == showActivityStatus)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.isBillionaire, isBillionaire) || other.isBillionaire == isBillionaire)&&(identical(other.userType, userType) || other.userType == userType)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isBusinessAccount, isBusinessAccount) || other.isBusinessAccount == isBusinessAccount)&&(identical(other.businessName, businessName) || other.businessName == businessName)&&(identical(other.businessLogoUrl, businessLogoUrl) || other.businessLogoUrl == businessLogoUrl)&&(identical(other.businessEmail, businessEmail) || other.businessEmail == businessEmail)&&(identical(other.businessPhone, businessPhone) || other.businessPhone == businessPhone)&&(identical(other.businessDescription, businessDescription) || other.businessDescription == businessDescription)&&(identical(other.businessVerified, businessVerified) || other.businessVerified == businessVerified)&&(identical(other.businessExclusive, businessExclusive) || other.businessExclusive == businessExclusive)&&(identical(other.businessCategory, businessCategory) || other.businessCategory == businessCategory)&&(identical(other.businessWebsite, businessWebsite) || other.businessWebsite == businessWebsite)&&(identical(other.businessAddress, businessAddress) || other.businessAddress == businessAddress)&&(identical(other.businessCreatedAt, businessCreatedAt) || other.businessCreatedAt == businessCreatedAt)&&(identical(other.businessVerifiedAt, businessVerifiedAt) || other.businessVerifiedAt == businessVerifiedAt)&&(identical(other.isAdmin, isAdmin) || other.isAdmin == isAdmin));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,username,name,profilePictureUrl,bio,postCount,followerCount,followingCount,bannerImageUrl,website,location,phone,email,isPrivate,allowMessages,showActivityStatus,isVerified,isBillionaire,userType,createdAt,updatedAt,isBusinessAccount,businessName,businessLogoUrl,businessEmail,businessPhone,businessDescription,businessVerified,businessExclusive,businessCategory,businessWebsite,businessAddress,businessCreatedAt,businessVerifiedAt,isAdmin]);

@override
String toString() {
  return 'ProfileModel(id: $id, username: $username, name: $name, profilePictureUrl: $profilePictureUrl, bio: $bio, postCount: $postCount, followerCount: $followerCount, followingCount: $followingCount, bannerImageUrl: $bannerImageUrl, website: $website, location: $location, phone: $phone, email: $email, isPrivate: $isPrivate, allowMessages: $allowMessages, showActivityStatus: $showActivityStatus, isVerified: $isVerified, isBillionaire: $isBillionaire, userType: $userType, createdAt: $createdAt, updatedAt: $updatedAt, isBusinessAccount: $isBusinessAccount, businessName: $businessName, businessLogoUrl: $businessLogoUrl, businessEmail: $businessEmail, businessPhone: $businessPhone, businessDescription: $businessDescription, businessVerified: $businessVerified, businessExclusive: $businessExclusive, businessCategory: $businessCategory, businessWebsite: $businessWebsite, businessAddress: $businessAddress, businessCreatedAt: $businessCreatedAt, businessVerifiedAt: $businessVerifiedAt, isAdmin: $isAdmin)';
}


}

/// @nodoc
abstract mixin class _$ProfileModelCopyWith<$Res> implements $ProfileModelCopyWith<$Res> {
  factory _$ProfileModelCopyWith(_ProfileModel value, $Res Function(_ProfileModel) _then) = __$ProfileModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String username, String name, String profilePictureUrl, String bio, int postCount, int followerCount, int followingCount, String? bannerImageUrl, String? website, String? location, String? phone, String? email, bool isPrivate, bool allowMessages, bool showActivityStatus, bool isVerified, bool isBillionaire, String userType, DateTime? createdAt, DateTime? updatedAt, bool isBusinessAccount, String? businessName, String? businessLogoUrl, String? businessEmail, String? businessPhone, String? businessDescription, bool businessVerified, bool businessExclusive, String? businessCategory, String? businessWebsite, String? businessAddress, DateTime? businessCreatedAt, DateTime? businessVerifiedAt, bool isAdmin
});




}
/// @nodoc
class __$ProfileModelCopyWithImpl<$Res>
    implements _$ProfileModelCopyWith<$Res> {
  __$ProfileModelCopyWithImpl(this._self, this._then);

  final _ProfileModel _self;
  final $Res Function(_ProfileModel) _then;

/// Create a copy of ProfileModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? username = null,Object? name = null,Object? profilePictureUrl = null,Object? bio = null,Object? postCount = null,Object? followerCount = null,Object? followingCount = null,Object? bannerImageUrl = freezed,Object? website = freezed,Object? location = freezed,Object? phone = freezed,Object? email = freezed,Object? isPrivate = null,Object? allowMessages = null,Object? showActivityStatus = null,Object? isVerified = null,Object? isBillionaire = null,Object? userType = null,Object? createdAt = freezed,Object? updatedAt = freezed,Object? isBusinessAccount = null,Object? businessName = freezed,Object? businessLogoUrl = freezed,Object? businessEmail = freezed,Object? businessPhone = freezed,Object? businessDescription = freezed,Object? businessVerified = null,Object? businessExclusive = null,Object? businessCategory = freezed,Object? businessWebsite = freezed,Object? businessAddress = freezed,Object? businessCreatedAt = freezed,Object? businessVerifiedAt = freezed,Object? isAdmin = null,}) {
  return _then(_ProfileModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,profilePictureUrl: null == profilePictureUrl ? _self.profilePictureUrl : profilePictureUrl // ignore: cast_nullable_to_non_nullable
as String,bio: null == bio ? _self.bio : bio // ignore: cast_nullable_to_non_nullable
as String,postCount: null == postCount ? _self.postCount : postCount // ignore: cast_nullable_to_non_nullable
as int,followerCount: null == followerCount ? _self.followerCount : followerCount // ignore: cast_nullable_to_non_nullable
as int,followingCount: null == followingCount ? _self.followingCount : followingCount // ignore: cast_nullable_to_non_nullable
as int,bannerImageUrl: freezed == bannerImageUrl ? _self.bannerImageUrl : bannerImageUrl // ignore: cast_nullable_to_non_nullable
as String?,website: freezed == website ? _self.website : website // ignore: cast_nullable_to_non_nullable
as String?,location: freezed == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String?,phone: freezed == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String?,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,isPrivate: null == isPrivate ? _self.isPrivate : isPrivate // ignore: cast_nullable_to_non_nullable
as bool,allowMessages: null == allowMessages ? _self.allowMessages : allowMessages // ignore: cast_nullable_to_non_nullable
as bool,showActivityStatus: null == showActivityStatus ? _self.showActivityStatus : showActivityStatus // ignore: cast_nullable_to_non_nullable
as bool,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,isBillionaire: null == isBillionaire ? _self.isBillionaire : isBillionaire // ignore: cast_nullable_to_non_nullable
as bool,userType: null == userType ? _self.userType : userType // ignore: cast_nullable_to_non_nullable
as String,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isBusinessAccount: null == isBusinessAccount ? _self.isBusinessAccount : isBusinessAccount // ignore: cast_nullable_to_non_nullable
as bool,businessName: freezed == businessName ? _self.businessName : businessName // ignore: cast_nullable_to_non_nullable
as String?,businessLogoUrl: freezed == businessLogoUrl ? _self.businessLogoUrl : businessLogoUrl // ignore: cast_nullable_to_non_nullable
as String?,businessEmail: freezed == businessEmail ? _self.businessEmail : businessEmail // ignore: cast_nullable_to_non_nullable
as String?,businessPhone: freezed == businessPhone ? _self.businessPhone : businessPhone // ignore: cast_nullable_to_non_nullable
as String?,businessDescription: freezed == businessDescription ? _self.businessDescription : businessDescription // ignore: cast_nullable_to_non_nullable
as String?,businessVerified: null == businessVerified ? _self.businessVerified : businessVerified // ignore: cast_nullable_to_non_nullable
as bool,businessExclusive: null == businessExclusive ? _self.businessExclusive : businessExclusive // ignore: cast_nullable_to_non_nullable
as bool,businessCategory: freezed == businessCategory ? _self.businessCategory : businessCategory // ignore: cast_nullable_to_non_nullable
as String?,businessWebsite: freezed == businessWebsite ? _self.businessWebsite : businessWebsite // ignore: cast_nullable_to_non_nullable
as String?,businessAddress: freezed == businessAddress ? _self.businessAddress : businessAddress // ignore: cast_nullable_to_non_nullable
as String?,businessCreatedAt: freezed == businessCreatedAt ? _self.businessCreatedAt : businessCreatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,businessVerifiedAt: freezed == businessVerifiedAt ? _self.businessVerifiedAt : businessVerifiedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isAdmin: null == isAdmin ? _self.isAdmin : isAdmin // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
