// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'profile_analytics_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ProfileAnalyticsModel {

 String get id; String get userId; DateTime get date; ProfileViewsData get profileViews; FollowerGrowthData get followerGrowth; EngagementData get engagement; ContentPerformanceData get contentPerformance; AudienceData get audience; Map<String, dynamic> get metadata;
/// Create a copy of ProfileAnalyticsModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProfileAnalyticsModelCopyWith<ProfileAnalyticsModel> get copyWith => _$ProfileAnalyticsModelCopyWithImpl<ProfileAnalyticsModel>(this as ProfileAnalyticsModel, _$identity);

  /// Serializes this ProfileAnalyticsModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProfileAnalyticsModel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.date, date) || other.date == date)&&(identical(other.profileViews, profileViews) || other.profileViews == profileViews)&&(identical(other.followerGrowth, followerGrowth) || other.followerGrowth == followerGrowth)&&(identical(other.engagement, engagement) || other.engagement == engagement)&&(identical(other.contentPerformance, contentPerformance) || other.contentPerformance == contentPerformance)&&(identical(other.audience, audience) || other.audience == audience)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,date,profileViews,followerGrowth,engagement,contentPerformance,audience,const DeepCollectionEquality().hash(metadata));

@override
String toString() {
  return 'ProfileAnalyticsModel(id: $id, userId: $userId, date: $date, profileViews: $profileViews, followerGrowth: $followerGrowth, engagement: $engagement, contentPerformance: $contentPerformance, audience: $audience, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $ProfileAnalyticsModelCopyWith<$Res>  {
  factory $ProfileAnalyticsModelCopyWith(ProfileAnalyticsModel value, $Res Function(ProfileAnalyticsModel) _then) = _$ProfileAnalyticsModelCopyWithImpl;
@useResult
$Res call({
 String id, String userId, DateTime date, ProfileViewsData profileViews, FollowerGrowthData followerGrowth, EngagementData engagement, ContentPerformanceData contentPerformance, AudienceData audience, Map<String, dynamic> metadata
});


$ProfileViewsDataCopyWith<$Res> get profileViews;$FollowerGrowthDataCopyWith<$Res> get followerGrowth;$EngagementDataCopyWith<$Res> get engagement;$ContentPerformanceDataCopyWith<$Res> get contentPerformance;$AudienceDataCopyWith<$Res> get audience;

}
/// @nodoc
class _$ProfileAnalyticsModelCopyWithImpl<$Res>
    implements $ProfileAnalyticsModelCopyWith<$Res> {
  _$ProfileAnalyticsModelCopyWithImpl(this._self, this._then);

  final ProfileAnalyticsModel _self;
  final $Res Function(ProfileAnalyticsModel) _then;

/// Create a copy of ProfileAnalyticsModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? date = null,Object? profileViews = null,Object? followerGrowth = null,Object? engagement = null,Object? contentPerformance = null,Object? audience = null,Object? metadata = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,date: null == date ? _self.date : date // ignore: cast_nullable_to_non_nullable
as DateTime,profileViews: null == profileViews ? _self.profileViews : profileViews // ignore: cast_nullable_to_non_nullable
as ProfileViewsData,followerGrowth: null == followerGrowth ? _self.followerGrowth : followerGrowth // ignore: cast_nullable_to_non_nullable
as FollowerGrowthData,engagement: null == engagement ? _self.engagement : engagement // ignore: cast_nullable_to_non_nullable
as EngagementData,contentPerformance: null == contentPerformance ? _self.contentPerformance : contentPerformance // ignore: cast_nullable_to_non_nullable
as ContentPerformanceData,audience: null == audience ? _self.audience : audience // ignore: cast_nullable_to_non_nullable
as AudienceData,metadata: null == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}
/// Create a copy of ProfileAnalyticsModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProfileViewsDataCopyWith<$Res> get profileViews {
  
  return $ProfileViewsDataCopyWith<$Res>(_self.profileViews, (value) {
    return _then(_self.copyWith(profileViews: value));
  });
}/// Create a copy of ProfileAnalyticsModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$FollowerGrowthDataCopyWith<$Res> get followerGrowth {
  
  return $FollowerGrowthDataCopyWith<$Res>(_self.followerGrowth, (value) {
    return _then(_self.copyWith(followerGrowth: value));
  });
}/// Create a copy of ProfileAnalyticsModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$EngagementDataCopyWith<$Res> get engagement {
  
  return $EngagementDataCopyWith<$Res>(_self.engagement, (value) {
    return _then(_self.copyWith(engagement: value));
  });
}/// Create a copy of ProfileAnalyticsModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ContentPerformanceDataCopyWith<$Res> get contentPerformance {
  
  return $ContentPerformanceDataCopyWith<$Res>(_self.contentPerformance, (value) {
    return _then(_self.copyWith(contentPerformance: value));
  });
}/// Create a copy of ProfileAnalyticsModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AudienceDataCopyWith<$Res> get audience {
  
  return $AudienceDataCopyWith<$Res>(_self.audience, (value) {
    return _then(_self.copyWith(audience: value));
  });
}
}


/// Adds pattern-matching-related methods to [ProfileAnalyticsModel].
extension ProfileAnalyticsModelPatterns on ProfileAnalyticsModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ProfileAnalyticsModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ProfileAnalyticsModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ProfileAnalyticsModel value)  $default,){
final _that = this;
switch (_that) {
case _ProfileAnalyticsModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ProfileAnalyticsModel value)?  $default,){
final _that = this;
switch (_that) {
case _ProfileAnalyticsModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  DateTime date,  ProfileViewsData profileViews,  FollowerGrowthData followerGrowth,  EngagementData engagement,  ContentPerformanceData contentPerformance,  AudienceData audience,  Map<String, dynamic> metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ProfileAnalyticsModel() when $default != null:
return $default(_that.id,_that.userId,_that.date,_that.profileViews,_that.followerGrowth,_that.engagement,_that.contentPerformance,_that.audience,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  DateTime date,  ProfileViewsData profileViews,  FollowerGrowthData followerGrowth,  EngagementData engagement,  ContentPerformanceData contentPerformance,  AudienceData audience,  Map<String, dynamic> metadata)  $default,) {final _that = this;
switch (_that) {
case _ProfileAnalyticsModel():
return $default(_that.id,_that.userId,_that.date,_that.profileViews,_that.followerGrowth,_that.engagement,_that.contentPerformance,_that.audience,_that.metadata);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  DateTime date,  ProfileViewsData profileViews,  FollowerGrowthData followerGrowth,  EngagementData engagement,  ContentPerformanceData contentPerformance,  AudienceData audience,  Map<String, dynamic> metadata)?  $default,) {final _that = this;
switch (_that) {
case _ProfileAnalyticsModel() when $default != null:
return $default(_that.id,_that.userId,_that.date,_that.profileViews,_that.followerGrowth,_that.engagement,_that.contentPerformance,_that.audience,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ProfileAnalyticsModel implements ProfileAnalyticsModel {
  const _ProfileAnalyticsModel({required this.id, required this.userId, required this.date, required this.profileViews, required this.followerGrowth, required this.engagement, required this.contentPerformance, required this.audience, required final  Map<String, dynamic> metadata}): _metadata = metadata;
  factory _ProfileAnalyticsModel.fromJson(Map<String, dynamic> json) => _$ProfileAnalyticsModelFromJson(json);

@override final  String id;
@override final  String userId;
@override final  DateTime date;
@override final  ProfileViewsData profileViews;
@override final  FollowerGrowthData followerGrowth;
@override final  EngagementData engagement;
@override final  ContentPerformanceData contentPerformance;
@override final  AudienceData audience;
 final  Map<String, dynamic> _metadata;
@override Map<String, dynamic> get metadata {
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_metadata);
}


/// Create a copy of ProfileAnalyticsModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProfileAnalyticsModelCopyWith<_ProfileAnalyticsModel> get copyWith => __$ProfileAnalyticsModelCopyWithImpl<_ProfileAnalyticsModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ProfileAnalyticsModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProfileAnalyticsModel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.date, date) || other.date == date)&&(identical(other.profileViews, profileViews) || other.profileViews == profileViews)&&(identical(other.followerGrowth, followerGrowth) || other.followerGrowth == followerGrowth)&&(identical(other.engagement, engagement) || other.engagement == engagement)&&(identical(other.contentPerformance, contentPerformance) || other.contentPerformance == contentPerformance)&&(identical(other.audience, audience) || other.audience == audience)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,date,profileViews,followerGrowth,engagement,contentPerformance,audience,const DeepCollectionEquality().hash(_metadata));

@override
String toString() {
  return 'ProfileAnalyticsModel(id: $id, userId: $userId, date: $date, profileViews: $profileViews, followerGrowth: $followerGrowth, engagement: $engagement, contentPerformance: $contentPerformance, audience: $audience, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$ProfileAnalyticsModelCopyWith<$Res> implements $ProfileAnalyticsModelCopyWith<$Res> {
  factory _$ProfileAnalyticsModelCopyWith(_ProfileAnalyticsModel value, $Res Function(_ProfileAnalyticsModel) _then) = __$ProfileAnalyticsModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, DateTime date, ProfileViewsData profileViews, FollowerGrowthData followerGrowth, EngagementData engagement, ContentPerformanceData contentPerformance, AudienceData audience, Map<String, dynamic> metadata
});


@override $ProfileViewsDataCopyWith<$Res> get profileViews;@override $FollowerGrowthDataCopyWith<$Res> get followerGrowth;@override $EngagementDataCopyWith<$Res> get engagement;@override $ContentPerformanceDataCopyWith<$Res> get contentPerformance;@override $AudienceDataCopyWith<$Res> get audience;

}
/// @nodoc
class __$ProfileAnalyticsModelCopyWithImpl<$Res>
    implements _$ProfileAnalyticsModelCopyWith<$Res> {
  __$ProfileAnalyticsModelCopyWithImpl(this._self, this._then);

  final _ProfileAnalyticsModel _self;
  final $Res Function(_ProfileAnalyticsModel) _then;

/// Create a copy of ProfileAnalyticsModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? date = null,Object? profileViews = null,Object? followerGrowth = null,Object? engagement = null,Object? contentPerformance = null,Object? audience = null,Object? metadata = null,}) {
  return _then(_ProfileAnalyticsModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,date: null == date ? _self.date : date // ignore: cast_nullable_to_non_nullable
as DateTime,profileViews: null == profileViews ? _self.profileViews : profileViews // ignore: cast_nullable_to_non_nullable
as ProfileViewsData,followerGrowth: null == followerGrowth ? _self.followerGrowth : followerGrowth // ignore: cast_nullable_to_non_nullable
as FollowerGrowthData,engagement: null == engagement ? _self.engagement : engagement // ignore: cast_nullable_to_non_nullable
as EngagementData,contentPerformance: null == contentPerformance ? _self.contentPerformance : contentPerformance // ignore: cast_nullable_to_non_nullable
as ContentPerformanceData,audience: null == audience ? _self.audience : audience // ignore: cast_nullable_to_non_nullable
as AudienceData,metadata: null == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}

/// Create a copy of ProfileAnalyticsModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProfileViewsDataCopyWith<$Res> get profileViews {
  
  return $ProfileViewsDataCopyWith<$Res>(_self.profileViews, (value) {
    return _then(_self.copyWith(profileViews: value));
  });
}/// Create a copy of ProfileAnalyticsModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$FollowerGrowthDataCopyWith<$Res> get followerGrowth {
  
  return $FollowerGrowthDataCopyWith<$Res>(_self.followerGrowth, (value) {
    return _then(_self.copyWith(followerGrowth: value));
  });
}/// Create a copy of ProfileAnalyticsModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$EngagementDataCopyWith<$Res> get engagement {
  
  return $EngagementDataCopyWith<$Res>(_self.engagement, (value) {
    return _then(_self.copyWith(engagement: value));
  });
}/// Create a copy of ProfileAnalyticsModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ContentPerformanceDataCopyWith<$Res> get contentPerformance {
  
  return $ContentPerformanceDataCopyWith<$Res>(_self.contentPerformance, (value) {
    return _then(_self.copyWith(contentPerformance: value));
  });
}/// Create a copy of ProfileAnalyticsModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AudienceDataCopyWith<$Res> get audience {
  
  return $AudienceDataCopyWith<$Res>(_self.audience, (value) {
    return _then(_self.copyWith(audience: value));
  });
}
}


/// @nodoc
mixin _$ProfileViewsData {

 int get totalViews; int get uniqueViews; int get profileVisits; int get profileShares; int get profileBookmarks; List<ProfileViewSource> get viewSources; List<ProfileViewTime> get viewTimes; Map<String, int> get viewsByCountry; Map<String, int> get viewsByDevice;
/// Create a copy of ProfileViewsData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProfileViewsDataCopyWith<ProfileViewsData> get copyWith => _$ProfileViewsDataCopyWithImpl<ProfileViewsData>(this as ProfileViewsData, _$identity);

  /// Serializes this ProfileViewsData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProfileViewsData&&(identical(other.totalViews, totalViews) || other.totalViews == totalViews)&&(identical(other.uniqueViews, uniqueViews) || other.uniqueViews == uniqueViews)&&(identical(other.profileVisits, profileVisits) || other.profileVisits == profileVisits)&&(identical(other.profileShares, profileShares) || other.profileShares == profileShares)&&(identical(other.profileBookmarks, profileBookmarks) || other.profileBookmarks == profileBookmarks)&&const DeepCollectionEquality().equals(other.viewSources, viewSources)&&const DeepCollectionEquality().equals(other.viewTimes, viewTimes)&&const DeepCollectionEquality().equals(other.viewsByCountry, viewsByCountry)&&const DeepCollectionEquality().equals(other.viewsByDevice, viewsByDevice));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalViews,uniqueViews,profileVisits,profileShares,profileBookmarks,const DeepCollectionEquality().hash(viewSources),const DeepCollectionEquality().hash(viewTimes),const DeepCollectionEquality().hash(viewsByCountry),const DeepCollectionEquality().hash(viewsByDevice));

@override
String toString() {
  return 'ProfileViewsData(totalViews: $totalViews, uniqueViews: $uniqueViews, profileVisits: $profileVisits, profileShares: $profileShares, profileBookmarks: $profileBookmarks, viewSources: $viewSources, viewTimes: $viewTimes, viewsByCountry: $viewsByCountry, viewsByDevice: $viewsByDevice)';
}


}

/// @nodoc
abstract mixin class $ProfileViewsDataCopyWith<$Res>  {
  factory $ProfileViewsDataCopyWith(ProfileViewsData value, $Res Function(ProfileViewsData) _then) = _$ProfileViewsDataCopyWithImpl;
@useResult
$Res call({
 int totalViews, int uniqueViews, int profileVisits, int profileShares, int profileBookmarks, List<ProfileViewSource> viewSources, List<ProfileViewTime> viewTimes, Map<String, int> viewsByCountry, Map<String, int> viewsByDevice
});




}
/// @nodoc
class _$ProfileViewsDataCopyWithImpl<$Res>
    implements $ProfileViewsDataCopyWith<$Res> {
  _$ProfileViewsDataCopyWithImpl(this._self, this._then);

  final ProfileViewsData _self;
  final $Res Function(ProfileViewsData) _then;

/// Create a copy of ProfileViewsData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? totalViews = null,Object? uniqueViews = null,Object? profileVisits = null,Object? profileShares = null,Object? profileBookmarks = null,Object? viewSources = null,Object? viewTimes = null,Object? viewsByCountry = null,Object? viewsByDevice = null,}) {
  return _then(_self.copyWith(
totalViews: null == totalViews ? _self.totalViews : totalViews // ignore: cast_nullable_to_non_nullable
as int,uniqueViews: null == uniqueViews ? _self.uniqueViews : uniqueViews // ignore: cast_nullable_to_non_nullable
as int,profileVisits: null == profileVisits ? _self.profileVisits : profileVisits // ignore: cast_nullable_to_non_nullable
as int,profileShares: null == profileShares ? _self.profileShares : profileShares // ignore: cast_nullable_to_non_nullable
as int,profileBookmarks: null == profileBookmarks ? _self.profileBookmarks : profileBookmarks // ignore: cast_nullable_to_non_nullable
as int,viewSources: null == viewSources ? _self.viewSources : viewSources // ignore: cast_nullable_to_non_nullable
as List<ProfileViewSource>,viewTimes: null == viewTimes ? _self.viewTimes : viewTimes // ignore: cast_nullable_to_non_nullable
as List<ProfileViewTime>,viewsByCountry: null == viewsByCountry ? _self.viewsByCountry : viewsByCountry // ignore: cast_nullable_to_non_nullable
as Map<String, int>,viewsByDevice: null == viewsByDevice ? _self.viewsByDevice : viewsByDevice // ignore: cast_nullable_to_non_nullable
as Map<String, int>,
  ));
}

}


/// Adds pattern-matching-related methods to [ProfileViewsData].
extension ProfileViewsDataPatterns on ProfileViewsData {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ProfileViewsData value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ProfileViewsData() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ProfileViewsData value)  $default,){
final _that = this;
switch (_that) {
case _ProfileViewsData():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ProfileViewsData value)?  $default,){
final _that = this;
switch (_that) {
case _ProfileViewsData() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int totalViews,  int uniqueViews,  int profileVisits,  int profileShares,  int profileBookmarks,  List<ProfileViewSource> viewSources,  List<ProfileViewTime> viewTimes,  Map<String, int> viewsByCountry,  Map<String, int> viewsByDevice)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ProfileViewsData() when $default != null:
return $default(_that.totalViews,_that.uniqueViews,_that.profileVisits,_that.profileShares,_that.profileBookmarks,_that.viewSources,_that.viewTimes,_that.viewsByCountry,_that.viewsByDevice);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int totalViews,  int uniqueViews,  int profileVisits,  int profileShares,  int profileBookmarks,  List<ProfileViewSource> viewSources,  List<ProfileViewTime> viewTimes,  Map<String, int> viewsByCountry,  Map<String, int> viewsByDevice)  $default,) {final _that = this;
switch (_that) {
case _ProfileViewsData():
return $default(_that.totalViews,_that.uniqueViews,_that.profileVisits,_that.profileShares,_that.profileBookmarks,_that.viewSources,_that.viewTimes,_that.viewsByCountry,_that.viewsByDevice);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int totalViews,  int uniqueViews,  int profileVisits,  int profileShares,  int profileBookmarks,  List<ProfileViewSource> viewSources,  List<ProfileViewTime> viewTimes,  Map<String, int> viewsByCountry,  Map<String, int> viewsByDevice)?  $default,) {final _that = this;
switch (_that) {
case _ProfileViewsData() when $default != null:
return $default(_that.totalViews,_that.uniqueViews,_that.profileVisits,_that.profileShares,_that.profileBookmarks,_that.viewSources,_that.viewTimes,_that.viewsByCountry,_that.viewsByDevice);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ProfileViewsData implements ProfileViewsData {
  const _ProfileViewsData({required this.totalViews, required this.uniqueViews, required this.profileVisits, required this.profileShares, required this.profileBookmarks, required final  List<ProfileViewSource> viewSources, required final  List<ProfileViewTime> viewTimes, required final  Map<String, int> viewsByCountry, required final  Map<String, int> viewsByDevice}): _viewSources = viewSources,_viewTimes = viewTimes,_viewsByCountry = viewsByCountry,_viewsByDevice = viewsByDevice;
  factory _ProfileViewsData.fromJson(Map<String, dynamic> json) => _$ProfileViewsDataFromJson(json);

@override final  int totalViews;
@override final  int uniqueViews;
@override final  int profileVisits;
@override final  int profileShares;
@override final  int profileBookmarks;
 final  List<ProfileViewSource> _viewSources;
@override List<ProfileViewSource> get viewSources {
  if (_viewSources is EqualUnmodifiableListView) return _viewSources;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_viewSources);
}

 final  List<ProfileViewTime> _viewTimes;
@override List<ProfileViewTime> get viewTimes {
  if (_viewTimes is EqualUnmodifiableListView) return _viewTimes;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_viewTimes);
}

 final  Map<String, int> _viewsByCountry;
@override Map<String, int> get viewsByCountry {
  if (_viewsByCountry is EqualUnmodifiableMapView) return _viewsByCountry;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_viewsByCountry);
}

 final  Map<String, int> _viewsByDevice;
@override Map<String, int> get viewsByDevice {
  if (_viewsByDevice is EqualUnmodifiableMapView) return _viewsByDevice;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_viewsByDevice);
}


/// Create a copy of ProfileViewsData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProfileViewsDataCopyWith<_ProfileViewsData> get copyWith => __$ProfileViewsDataCopyWithImpl<_ProfileViewsData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ProfileViewsDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProfileViewsData&&(identical(other.totalViews, totalViews) || other.totalViews == totalViews)&&(identical(other.uniqueViews, uniqueViews) || other.uniqueViews == uniqueViews)&&(identical(other.profileVisits, profileVisits) || other.profileVisits == profileVisits)&&(identical(other.profileShares, profileShares) || other.profileShares == profileShares)&&(identical(other.profileBookmarks, profileBookmarks) || other.profileBookmarks == profileBookmarks)&&const DeepCollectionEquality().equals(other._viewSources, _viewSources)&&const DeepCollectionEquality().equals(other._viewTimes, _viewTimes)&&const DeepCollectionEquality().equals(other._viewsByCountry, _viewsByCountry)&&const DeepCollectionEquality().equals(other._viewsByDevice, _viewsByDevice));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalViews,uniqueViews,profileVisits,profileShares,profileBookmarks,const DeepCollectionEquality().hash(_viewSources),const DeepCollectionEquality().hash(_viewTimes),const DeepCollectionEquality().hash(_viewsByCountry),const DeepCollectionEquality().hash(_viewsByDevice));

@override
String toString() {
  return 'ProfileViewsData(totalViews: $totalViews, uniqueViews: $uniqueViews, profileVisits: $profileVisits, profileShares: $profileShares, profileBookmarks: $profileBookmarks, viewSources: $viewSources, viewTimes: $viewTimes, viewsByCountry: $viewsByCountry, viewsByDevice: $viewsByDevice)';
}


}

/// @nodoc
abstract mixin class _$ProfileViewsDataCopyWith<$Res> implements $ProfileViewsDataCopyWith<$Res> {
  factory _$ProfileViewsDataCopyWith(_ProfileViewsData value, $Res Function(_ProfileViewsData) _then) = __$ProfileViewsDataCopyWithImpl;
@override @useResult
$Res call({
 int totalViews, int uniqueViews, int profileVisits, int profileShares, int profileBookmarks, List<ProfileViewSource> viewSources, List<ProfileViewTime> viewTimes, Map<String, int> viewsByCountry, Map<String, int> viewsByDevice
});




}
/// @nodoc
class __$ProfileViewsDataCopyWithImpl<$Res>
    implements _$ProfileViewsDataCopyWith<$Res> {
  __$ProfileViewsDataCopyWithImpl(this._self, this._then);

  final _ProfileViewsData _self;
  final $Res Function(_ProfileViewsData) _then;

/// Create a copy of ProfileViewsData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? totalViews = null,Object? uniqueViews = null,Object? profileVisits = null,Object? profileShares = null,Object? profileBookmarks = null,Object? viewSources = null,Object? viewTimes = null,Object? viewsByCountry = null,Object? viewsByDevice = null,}) {
  return _then(_ProfileViewsData(
totalViews: null == totalViews ? _self.totalViews : totalViews // ignore: cast_nullable_to_non_nullable
as int,uniqueViews: null == uniqueViews ? _self.uniqueViews : uniqueViews // ignore: cast_nullable_to_non_nullable
as int,profileVisits: null == profileVisits ? _self.profileVisits : profileVisits // ignore: cast_nullable_to_non_nullable
as int,profileShares: null == profileShares ? _self.profileShares : profileShares // ignore: cast_nullable_to_non_nullable
as int,profileBookmarks: null == profileBookmarks ? _self.profileBookmarks : profileBookmarks // ignore: cast_nullable_to_non_nullable
as int,viewSources: null == viewSources ? _self._viewSources : viewSources // ignore: cast_nullable_to_non_nullable
as List<ProfileViewSource>,viewTimes: null == viewTimes ? _self._viewTimes : viewTimes // ignore: cast_nullable_to_non_nullable
as List<ProfileViewTime>,viewsByCountry: null == viewsByCountry ? _self._viewsByCountry : viewsByCountry // ignore: cast_nullable_to_non_nullable
as Map<String, int>,viewsByDevice: null == viewsByDevice ? _self._viewsByDevice : viewsByDevice // ignore: cast_nullable_to_non_nullable
as Map<String, int>,
  ));
}


}


/// @nodoc
mixin _$ProfileViewSource {

 String get source; int get count; double get percentage;
/// Create a copy of ProfileViewSource
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProfileViewSourceCopyWith<ProfileViewSource> get copyWith => _$ProfileViewSourceCopyWithImpl<ProfileViewSource>(this as ProfileViewSource, _$identity);

  /// Serializes this ProfileViewSource to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProfileViewSource&&(identical(other.source, source) || other.source == source)&&(identical(other.count, count) || other.count == count)&&(identical(other.percentage, percentage) || other.percentage == percentage));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,source,count,percentage);

@override
String toString() {
  return 'ProfileViewSource(source: $source, count: $count, percentage: $percentage)';
}


}

/// @nodoc
abstract mixin class $ProfileViewSourceCopyWith<$Res>  {
  factory $ProfileViewSourceCopyWith(ProfileViewSource value, $Res Function(ProfileViewSource) _then) = _$ProfileViewSourceCopyWithImpl;
@useResult
$Res call({
 String source, int count, double percentage
});




}
/// @nodoc
class _$ProfileViewSourceCopyWithImpl<$Res>
    implements $ProfileViewSourceCopyWith<$Res> {
  _$ProfileViewSourceCopyWithImpl(this._self, this._then);

  final ProfileViewSource _self;
  final $Res Function(ProfileViewSource) _then;

/// Create a copy of ProfileViewSource
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? source = null,Object? count = null,Object? percentage = null,}) {
  return _then(_self.copyWith(
source: null == source ? _self.source : source // ignore: cast_nullable_to_non_nullable
as String,count: null == count ? _self.count : count // ignore: cast_nullable_to_non_nullable
as int,percentage: null == percentage ? _self.percentage : percentage // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [ProfileViewSource].
extension ProfileViewSourcePatterns on ProfileViewSource {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ProfileViewSource value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ProfileViewSource() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ProfileViewSource value)  $default,){
final _that = this;
switch (_that) {
case _ProfileViewSource():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ProfileViewSource value)?  $default,){
final _that = this;
switch (_that) {
case _ProfileViewSource() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String source,  int count,  double percentage)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ProfileViewSource() when $default != null:
return $default(_that.source,_that.count,_that.percentage);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String source,  int count,  double percentage)  $default,) {final _that = this;
switch (_that) {
case _ProfileViewSource():
return $default(_that.source,_that.count,_that.percentage);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String source,  int count,  double percentage)?  $default,) {final _that = this;
switch (_that) {
case _ProfileViewSource() when $default != null:
return $default(_that.source,_that.count,_that.percentage);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ProfileViewSource implements ProfileViewSource {
  const _ProfileViewSource({required this.source, required this.count, required this.percentage});
  factory _ProfileViewSource.fromJson(Map<String, dynamic> json) => _$ProfileViewSourceFromJson(json);

@override final  String source;
@override final  int count;
@override final  double percentage;

/// Create a copy of ProfileViewSource
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProfileViewSourceCopyWith<_ProfileViewSource> get copyWith => __$ProfileViewSourceCopyWithImpl<_ProfileViewSource>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ProfileViewSourceToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProfileViewSource&&(identical(other.source, source) || other.source == source)&&(identical(other.count, count) || other.count == count)&&(identical(other.percentage, percentage) || other.percentage == percentage));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,source,count,percentage);

@override
String toString() {
  return 'ProfileViewSource(source: $source, count: $count, percentage: $percentage)';
}


}

/// @nodoc
abstract mixin class _$ProfileViewSourceCopyWith<$Res> implements $ProfileViewSourceCopyWith<$Res> {
  factory _$ProfileViewSourceCopyWith(_ProfileViewSource value, $Res Function(_ProfileViewSource) _then) = __$ProfileViewSourceCopyWithImpl;
@override @useResult
$Res call({
 String source, int count, double percentage
});




}
/// @nodoc
class __$ProfileViewSourceCopyWithImpl<$Res>
    implements _$ProfileViewSourceCopyWith<$Res> {
  __$ProfileViewSourceCopyWithImpl(this._self, this._then);

  final _ProfileViewSource _self;
  final $Res Function(_ProfileViewSource) _then;

/// Create a copy of ProfileViewSource
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? source = null,Object? count = null,Object? percentage = null,}) {
  return _then(_ProfileViewSource(
source: null == source ? _self.source : source // ignore: cast_nullable_to_non_nullable
as String,count: null == count ? _self.count : count // ignore: cast_nullable_to_non_nullable
as int,percentage: null == percentage ? _self.percentage : percentage // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}


/// @nodoc
mixin _$ProfileViewTime {

 String get timeSlot; int get count;
/// Create a copy of ProfileViewTime
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProfileViewTimeCopyWith<ProfileViewTime> get copyWith => _$ProfileViewTimeCopyWithImpl<ProfileViewTime>(this as ProfileViewTime, _$identity);

  /// Serializes this ProfileViewTime to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProfileViewTime&&(identical(other.timeSlot, timeSlot) || other.timeSlot == timeSlot)&&(identical(other.count, count) || other.count == count));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,timeSlot,count);

@override
String toString() {
  return 'ProfileViewTime(timeSlot: $timeSlot, count: $count)';
}


}

/// @nodoc
abstract mixin class $ProfileViewTimeCopyWith<$Res>  {
  factory $ProfileViewTimeCopyWith(ProfileViewTime value, $Res Function(ProfileViewTime) _then) = _$ProfileViewTimeCopyWithImpl;
@useResult
$Res call({
 String timeSlot, int count
});




}
/// @nodoc
class _$ProfileViewTimeCopyWithImpl<$Res>
    implements $ProfileViewTimeCopyWith<$Res> {
  _$ProfileViewTimeCopyWithImpl(this._self, this._then);

  final ProfileViewTime _self;
  final $Res Function(ProfileViewTime) _then;

/// Create a copy of ProfileViewTime
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? timeSlot = null,Object? count = null,}) {
  return _then(_self.copyWith(
timeSlot: null == timeSlot ? _self.timeSlot : timeSlot // ignore: cast_nullable_to_non_nullable
as String,count: null == count ? _self.count : count // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [ProfileViewTime].
extension ProfileViewTimePatterns on ProfileViewTime {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ProfileViewTime value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ProfileViewTime() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ProfileViewTime value)  $default,){
final _that = this;
switch (_that) {
case _ProfileViewTime():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ProfileViewTime value)?  $default,){
final _that = this;
switch (_that) {
case _ProfileViewTime() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String timeSlot,  int count)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ProfileViewTime() when $default != null:
return $default(_that.timeSlot,_that.count);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String timeSlot,  int count)  $default,) {final _that = this;
switch (_that) {
case _ProfileViewTime():
return $default(_that.timeSlot,_that.count);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String timeSlot,  int count)?  $default,) {final _that = this;
switch (_that) {
case _ProfileViewTime() when $default != null:
return $default(_that.timeSlot,_that.count);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ProfileViewTime implements ProfileViewTime {
  const _ProfileViewTime({required this.timeSlot, required this.count});
  factory _ProfileViewTime.fromJson(Map<String, dynamic> json) => _$ProfileViewTimeFromJson(json);

@override final  String timeSlot;
@override final  int count;

/// Create a copy of ProfileViewTime
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProfileViewTimeCopyWith<_ProfileViewTime> get copyWith => __$ProfileViewTimeCopyWithImpl<_ProfileViewTime>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ProfileViewTimeToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProfileViewTime&&(identical(other.timeSlot, timeSlot) || other.timeSlot == timeSlot)&&(identical(other.count, count) || other.count == count));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,timeSlot,count);

@override
String toString() {
  return 'ProfileViewTime(timeSlot: $timeSlot, count: $count)';
}


}

/// @nodoc
abstract mixin class _$ProfileViewTimeCopyWith<$Res> implements $ProfileViewTimeCopyWith<$Res> {
  factory _$ProfileViewTimeCopyWith(_ProfileViewTime value, $Res Function(_ProfileViewTime) _then) = __$ProfileViewTimeCopyWithImpl;
@override @useResult
$Res call({
 String timeSlot, int count
});




}
/// @nodoc
class __$ProfileViewTimeCopyWithImpl<$Res>
    implements _$ProfileViewTimeCopyWith<$Res> {
  __$ProfileViewTimeCopyWithImpl(this._self, this._then);

  final _ProfileViewTime _self;
  final $Res Function(_ProfileViewTime) _then;

/// Create a copy of ProfileViewTime
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? timeSlot = null,Object? count = null,}) {
  return _then(_ProfileViewTime(
timeSlot: null == timeSlot ? _self.timeSlot : timeSlot // ignore: cast_nullable_to_non_nullable
as String,count: null == count ? _self.count : count // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}


/// @nodoc
mixin _$FollowerGrowthData {

 int get totalFollowers; int get newFollowers; int get lostFollowers; double get growthRate; List<FollowerGrowthPoint> get growthHistory; Map<String, int> get followersBySource; Map<String, int> get followersByCountry; Map<String, int> get followersByAge; Map<String, int> get followersByGender;
/// Create a copy of FollowerGrowthData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$FollowerGrowthDataCopyWith<FollowerGrowthData> get copyWith => _$FollowerGrowthDataCopyWithImpl<FollowerGrowthData>(this as FollowerGrowthData, _$identity);

  /// Serializes this FollowerGrowthData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is FollowerGrowthData&&(identical(other.totalFollowers, totalFollowers) || other.totalFollowers == totalFollowers)&&(identical(other.newFollowers, newFollowers) || other.newFollowers == newFollowers)&&(identical(other.lostFollowers, lostFollowers) || other.lostFollowers == lostFollowers)&&(identical(other.growthRate, growthRate) || other.growthRate == growthRate)&&const DeepCollectionEquality().equals(other.growthHistory, growthHistory)&&const DeepCollectionEquality().equals(other.followersBySource, followersBySource)&&const DeepCollectionEquality().equals(other.followersByCountry, followersByCountry)&&const DeepCollectionEquality().equals(other.followersByAge, followersByAge)&&const DeepCollectionEquality().equals(other.followersByGender, followersByGender));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalFollowers,newFollowers,lostFollowers,growthRate,const DeepCollectionEquality().hash(growthHistory),const DeepCollectionEquality().hash(followersBySource),const DeepCollectionEquality().hash(followersByCountry),const DeepCollectionEquality().hash(followersByAge),const DeepCollectionEquality().hash(followersByGender));

@override
String toString() {
  return 'FollowerGrowthData(totalFollowers: $totalFollowers, newFollowers: $newFollowers, lostFollowers: $lostFollowers, growthRate: $growthRate, growthHistory: $growthHistory, followersBySource: $followersBySource, followersByCountry: $followersByCountry, followersByAge: $followersByAge, followersByGender: $followersByGender)';
}


}

/// @nodoc
abstract mixin class $FollowerGrowthDataCopyWith<$Res>  {
  factory $FollowerGrowthDataCopyWith(FollowerGrowthData value, $Res Function(FollowerGrowthData) _then) = _$FollowerGrowthDataCopyWithImpl;
@useResult
$Res call({
 int totalFollowers, int newFollowers, int lostFollowers, double growthRate, List<FollowerGrowthPoint> growthHistory, Map<String, int> followersBySource, Map<String, int> followersByCountry, Map<String, int> followersByAge, Map<String, int> followersByGender
});




}
/// @nodoc
class _$FollowerGrowthDataCopyWithImpl<$Res>
    implements $FollowerGrowthDataCopyWith<$Res> {
  _$FollowerGrowthDataCopyWithImpl(this._self, this._then);

  final FollowerGrowthData _self;
  final $Res Function(FollowerGrowthData) _then;

/// Create a copy of FollowerGrowthData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? totalFollowers = null,Object? newFollowers = null,Object? lostFollowers = null,Object? growthRate = null,Object? growthHistory = null,Object? followersBySource = null,Object? followersByCountry = null,Object? followersByAge = null,Object? followersByGender = null,}) {
  return _then(_self.copyWith(
totalFollowers: null == totalFollowers ? _self.totalFollowers : totalFollowers // ignore: cast_nullable_to_non_nullable
as int,newFollowers: null == newFollowers ? _self.newFollowers : newFollowers // ignore: cast_nullable_to_non_nullable
as int,lostFollowers: null == lostFollowers ? _self.lostFollowers : lostFollowers // ignore: cast_nullable_to_non_nullable
as int,growthRate: null == growthRate ? _self.growthRate : growthRate // ignore: cast_nullable_to_non_nullable
as double,growthHistory: null == growthHistory ? _self.growthHistory : growthHistory // ignore: cast_nullable_to_non_nullable
as List<FollowerGrowthPoint>,followersBySource: null == followersBySource ? _self.followersBySource : followersBySource // ignore: cast_nullable_to_non_nullable
as Map<String, int>,followersByCountry: null == followersByCountry ? _self.followersByCountry : followersByCountry // ignore: cast_nullable_to_non_nullable
as Map<String, int>,followersByAge: null == followersByAge ? _self.followersByAge : followersByAge // ignore: cast_nullable_to_non_nullable
as Map<String, int>,followersByGender: null == followersByGender ? _self.followersByGender : followersByGender // ignore: cast_nullable_to_non_nullable
as Map<String, int>,
  ));
}

}


/// Adds pattern-matching-related methods to [FollowerGrowthData].
extension FollowerGrowthDataPatterns on FollowerGrowthData {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _FollowerGrowthData value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _FollowerGrowthData() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _FollowerGrowthData value)  $default,){
final _that = this;
switch (_that) {
case _FollowerGrowthData():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _FollowerGrowthData value)?  $default,){
final _that = this;
switch (_that) {
case _FollowerGrowthData() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int totalFollowers,  int newFollowers,  int lostFollowers,  double growthRate,  List<FollowerGrowthPoint> growthHistory,  Map<String, int> followersBySource,  Map<String, int> followersByCountry,  Map<String, int> followersByAge,  Map<String, int> followersByGender)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _FollowerGrowthData() when $default != null:
return $default(_that.totalFollowers,_that.newFollowers,_that.lostFollowers,_that.growthRate,_that.growthHistory,_that.followersBySource,_that.followersByCountry,_that.followersByAge,_that.followersByGender);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int totalFollowers,  int newFollowers,  int lostFollowers,  double growthRate,  List<FollowerGrowthPoint> growthHistory,  Map<String, int> followersBySource,  Map<String, int> followersByCountry,  Map<String, int> followersByAge,  Map<String, int> followersByGender)  $default,) {final _that = this;
switch (_that) {
case _FollowerGrowthData():
return $default(_that.totalFollowers,_that.newFollowers,_that.lostFollowers,_that.growthRate,_that.growthHistory,_that.followersBySource,_that.followersByCountry,_that.followersByAge,_that.followersByGender);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int totalFollowers,  int newFollowers,  int lostFollowers,  double growthRate,  List<FollowerGrowthPoint> growthHistory,  Map<String, int> followersBySource,  Map<String, int> followersByCountry,  Map<String, int> followersByAge,  Map<String, int> followersByGender)?  $default,) {final _that = this;
switch (_that) {
case _FollowerGrowthData() when $default != null:
return $default(_that.totalFollowers,_that.newFollowers,_that.lostFollowers,_that.growthRate,_that.growthHistory,_that.followersBySource,_that.followersByCountry,_that.followersByAge,_that.followersByGender);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _FollowerGrowthData implements FollowerGrowthData {
  const _FollowerGrowthData({required this.totalFollowers, required this.newFollowers, required this.lostFollowers, required this.growthRate, required final  List<FollowerGrowthPoint> growthHistory, required final  Map<String, int> followersBySource, required final  Map<String, int> followersByCountry, required final  Map<String, int> followersByAge, required final  Map<String, int> followersByGender}): _growthHistory = growthHistory,_followersBySource = followersBySource,_followersByCountry = followersByCountry,_followersByAge = followersByAge,_followersByGender = followersByGender;
  factory _FollowerGrowthData.fromJson(Map<String, dynamic> json) => _$FollowerGrowthDataFromJson(json);

@override final  int totalFollowers;
@override final  int newFollowers;
@override final  int lostFollowers;
@override final  double growthRate;
 final  List<FollowerGrowthPoint> _growthHistory;
@override List<FollowerGrowthPoint> get growthHistory {
  if (_growthHistory is EqualUnmodifiableListView) return _growthHistory;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_growthHistory);
}

 final  Map<String, int> _followersBySource;
@override Map<String, int> get followersBySource {
  if (_followersBySource is EqualUnmodifiableMapView) return _followersBySource;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_followersBySource);
}

 final  Map<String, int> _followersByCountry;
@override Map<String, int> get followersByCountry {
  if (_followersByCountry is EqualUnmodifiableMapView) return _followersByCountry;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_followersByCountry);
}

 final  Map<String, int> _followersByAge;
@override Map<String, int> get followersByAge {
  if (_followersByAge is EqualUnmodifiableMapView) return _followersByAge;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_followersByAge);
}

 final  Map<String, int> _followersByGender;
@override Map<String, int> get followersByGender {
  if (_followersByGender is EqualUnmodifiableMapView) return _followersByGender;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_followersByGender);
}


/// Create a copy of FollowerGrowthData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$FollowerGrowthDataCopyWith<_FollowerGrowthData> get copyWith => __$FollowerGrowthDataCopyWithImpl<_FollowerGrowthData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$FollowerGrowthDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _FollowerGrowthData&&(identical(other.totalFollowers, totalFollowers) || other.totalFollowers == totalFollowers)&&(identical(other.newFollowers, newFollowers) || other.newFollowers == newFollowers)&&(identical(other.lostFollowers, lostFollowers) || other.lostFollowers == lostFollowers)&&(identical(other.growthRate, growthRate) || other.growthRate == growthRate)&&const DeepCollectionEquality().equals(other._growthHistory, _growthHistory)&&const DeepCollectionEquality().equals(other._followersBySource, _followersBySource)&&const DeepCollectionEquality().equals(other._followersByCountry, _followersByCountry)&&const DeepCollectionEquality().equals(other._followersByAge, _followersByAge)&&const DeepCollectionEquality().equals(other._followersByGender, _followersByGender));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalFollowers,newFollowers,lostFollowers,growthRate,const DeepCollectionEquality().hash(_growthHistory),const DeepCollectionEquality().hash(_followersBySource),const DeepCollectionEquality().hash(_followersByCountry),const DeepCollectionEquality().hash(_followersByAge),const DeepCollectionEquality().hash(_followersByGender));

@override
String toString() {
  return 'FollowerGrowthData(totalFollowers: $totalFollowers, newFollowers: $newFollowers, lostFollowers: $lostFollowers, growthRate: $growthRate, growthHistory: $growthHistory, followersBySource: $followersBySource, followersByCountry: $followersByCountry, followersByAge: $followersByAge, followersByGender: $followersByGender)';
}


}

/// @nodoc
abstract mixin class _$FollowerGrowthDataCopyWith<$Res> implements $FollowerGrowthDataCopyWith<$Res> {
  factory _$FollowerGrowthDataCopyWith(_FollowerGrowthData value, $Res Function(_FollowerGrowthData) _then) = __$FollowerGrowthDataCopyWithImpl;
@override @useResult
$Res call({
 int totalFollowers, int newFollowers, int lostFollowers, double growthRate, List<FollowerGrowthPoint> growthHistory, Map<String, int> followersBySource, Map<String, int> followersByCountry, Map<String, int> followersByAge, Map<String, int> followersByGender
});




}
/// @nodoc
class __$FollowerGrowthDataCopyWithImpl<$Res>
    implements _$FollowerGrowthDataCopyWith<$Res> {
  __$FollowerGrowthDataCopyWithImpl(this._self, this._then);

  final _FollowerGrowthData _self;
  final $Res Function(_FollowerGrowthData) _then;

/// Create a copy of FollowerGrowthData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? totalFollowers = null,Object? newFollowers = null,Object? lostFollowers = null,Object? growthRate = null,Object? growthHistory = null,Object? followersBySource = null,Object? followersByCountry = null,Object? followersByAge = null,Object? followersByGender = null,}) {
  return _then(_FollowerGrowthData(
totalFollowers: null == totalFollowers ? _self.totalFollowers : totalFollowers // ignore: cast_nullable_to_non_nullable
as int,newFollowers: null == newFollowers ? _self.newFollowers : newFollowers // ignore: cast_nullable_to_non_nullable
as int,lostFollowers: null == lostFollowers ? _self.lostFollowers : lostFollowers // ignore: cast_nullable_to_non_nullable
as int,growthRate: null == growthRate ? _self.growthRate : growthRate // ignore: cast_nullable_to_non_nullable
as double,growthHistory: null == growthHistory ? _self._growthHistory : growthHistory // ignore: cast_nullable_to_non_nullable
as List<FollowerGrowthPoint>,followersBySource: null == followersBySource ? _self._followersBySource : followersBySource // ignore: cast_nullable_to_non_nullable
as Map<String, int>,followersByCountry: null == followersByCountry ? _self._followersByCountry : followersByCountry // ignore: cast_nullable_to_non_nullable
as Map<String, int>,followersByAge: null == followersByAge ? _self._followersByAge : followersByAge // ignore: cast_nullable_to_non_nullable
as Map<String, int>,followersByGender: null == followersByGender ? _self._followersByGender : followersByGender // ignore: cast_nullable_to_non_nullable
as Map<String, int>,
  ));
}


}


/// @nodoc
mixin _$FollowerGrowthPoint {

 DateTime get date; int get followers; int get newFollowers; int get lostFollowers;
/// Create a copy of FollowerGrowthPoint
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$FollowerGrowthPointCopyWith<FollowerGrowthPoint> get copyWith => _$FollowerGrowthPointCopyWithImpl<FollowerGrowthPoint>(this as FollowerGrowthPoint, _$identity);

  /// Serializes this FollowerGrowthPoint to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is FollowerGrowthPoint&&(identical(other.date, date) || other.date == date)&&(identical(other.followers, followers) || other.followers == followers)&&(identical(other.newFollowers, newFollowers) || other.newFollowers == newFollowers)&&(identical(other.lostFollowers, lostFollowers) || other.lostFollowers == lostFollowers));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,date,followers,newFollowers,lostFollowers);

@override
String toString() {
  return 'FollowerGrowthPoint(date: $date, followers: $followers, newFollowers: $newFollowers, lostFollowers: $lostFollowers)';
}


}

/// @nodoc
abstract mixin class $FollowerGrowthPointCopyWith<$Res>  {
  factory $FollowerGrowthPointCopyWith(FollowerGrowthPoint value, $Res Function(FollowerGrowthPoint) _then) = _$FollowerGrowthPointCopyWithImpl;
@useResult
$Res call({
 DateTime date, int followers, int newFollowers, int lostFollowers
});




}
/// @nodoc
class _$FollowerGrowthPointCopyWithImpl<$Res>
    implements $FollowerGrowthPointCopyWith<$Res> {
  _$FollowerGrowthPointCopyWithImpl(this._self, this._then);

  final FollowerGrowthPoint _self;
  final $Res Function(FollowerGrowthPoint) _then;

/// Create a copy of FollowerGrowthPoint
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? date = null,Object? followers = null,Object? newFollowers = null,Object? lostFollowers = null,}) {
  return _then(_self.copyWith(
date: null == date ? _self.date : date // ignore: cast_nullable_to_non_nullable
as DateTime,followers: null == followers ? _self.followers : followers // ignore: cast_nullable_to_non_nullable
as int,newFollowers: null == newFollowers ? _self.newFollowers : newFollowers // ignore: cast_nullable_to_non_nullable
as int,lostFollowers: null == lostFollowers ? _self.lostFollowers : lostFollowers // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [FollowerGrowthPoint].
extension FollowerGrowthPointPatterns on FollowerGrowthPoint {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _FollowerGrowthPoint value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _FollowerGrowthPoint() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _FollowerGrowthPoint value)  $default,){
final _that = this;
switch (_that) {
case _FollowerGrowthPoint():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _FollowerGrowthPoint value)?  $default,){
final _that = this;
switch (_that) {
case _FollowerGrowthPoint() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( DateTime date,  int followers,  int newFollowers,  int lostFollowers)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _FollowerGrowthPoint() when $default != null:
return $default(_that.date,_that.followers,_that.newFollowers,_that.lostFollowers);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( DateTime date,  int followers,  int newFollowers,  int lostFollowers)  $default,) {final _that = this;
switch (_that) {
case _FollowerGrowthPoint():
return $default(_that.date,_that.followers,_that.newFollowers,_that.lostFollowers);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( DateTime date,  int followers,  int newFollowers,  int lostFollowers)?  $default,) {final _that = this;
switch (_that) {
case _FollowerGrowthPoint() when $default != null:
return $default(_that.date,_that.followers,_that.newFollowers,_that.lostFollowers);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _FollowerGrowthPoint implements FollowerGrowthPoint {
  const _FollowerGrowthPoint({required this.date, required this.followers, required this.newFollowers, required this.lostFollowers});
  factory _FollowerGrowthPoint.fromJson(Map<String, dynamic> json) => _$FollowerGrowthPointFromJson(json);

@override final  DateTime date;
@override final  int followers;
@override final  int newFollowers;
@override final  int lostFollowers;

/// Create a copy of FollowerGrowthPoint
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$FollowerGrowthPointCopyWith<_FollowerGrowthPoint> get copyWith => __$FollowerGrowthPointCopyWithImpl<_FollowerGrowthPoint>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$FollowerGrowthPointToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _FollowerGrowthPoint&&(identical(other.date, date) || other.date == date)&&(identical(other.followers, followers) || other.followers == followers)&&(identical(other.newFollowers, newFollowers) || other.newFollowers == newFollowers)&&(identical(other.lostFollowers, lostFollowers) || other.lostFollowers == lostFollowers));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,date,followers,newFollowers,lostFollowers);

@override
String toString() {
  return 'FollowerGrowthPoint(date: $date, followers: $followers, newFollowers: $newFollowers, lostFollowers: $lostFollowers)';
}


}

/// @nodoc
abstract mixin class _$FollowerGrowthPointCopyWith<$Res> implements $FollowerGrowthPointCopyWith<$Res> {
  factory _$FollowerGrowthPointCopyWith(_FollowerGrowthPoint value, $Res Function(_FollowerGrowthPoint) _then) = __$FollowerGrowthPointCopyWithImpl;
@override @useResult
$Res call({
 DateTime date, int followers, int newFollowers, int lostFollowers
});




}
/// @nodoc
class __$FollowerGrowthPointCopyWithImpl<$Res>
    implements _$FollowerGrowthPointCopyWith<$Res> {
  __$FollowerGrowthPointCopyWithImpl(this._self, this._then);

  final _FollowerGrowthPoint _self;
  final $Res Function(_FollowerGrowthPoint) _then;

/// Create a copy of FollowerGrowthPoint
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? date = null,Object? followers = null,Object? newFollowers = null,Object? lostFollowers = null,}) {
  return _then(_FollowerGrowthPoint(
date: null == date ? _self.date : date // ignore: cast_nullable_to_non_nullable
as DateTime,followers: null == followers ? _self.followers : followers // ignore: cast_nullable_to_non_nullable
as int,newFollowers: null == newFollowers ? _self.newFollowers : newFollowers // ignore: cast_nullable_to_non_nullable
as int,lostFollowers: null == lostFollowers ? _self.lostFollowers : lostFollowers // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}


/// @nodoc
mixin _$EngagementData {

 double get overallEngagementRate; int get totalEngagements; int get likes; int get comments; int get shares; int get saves; List<EngagementTrend> get engagementTrends; Map<String, double> get engagementByContentType; Map<String, double> get engagementByTimeOfDay; Map<String, double> get engagementByDayOfWeek;
/// Create a copy of EngagementData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$EngagementDataCopyWith<EngagementData> get copyWith => _$EngagementDataCopyWithImpl<EngagementData>(this as EngagementData, _$identity);

  /// Serializes this EngagementData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EngagementData&&(identical(other.overallEngagementRate, overallEngagementRate) || other.overallEngagementRate == overallEngagementRate)&&(identical(other.totalEngagements, totalEngagements) || other.totalEngagements == totalEngagements)&&(identical(other.likes, likes) || other.likes == likes)&&(identical(other.comments, comments) || other.comments == comments)&&(identical(other.shares, shares) || other.shares == shares)&&(identical(other.saves, saves) || other.saves == saves)&&const DeepCollectionEquality().equals(other.engagementTrends, engagementTrends)&&const DeepCollectionEquality().equals(other.engagementByContentType, engagementByContentType)&&const DeepCollectionEquality().equals(other.engagementByTimeOfDay, engagementByTimeOfDay)&&const DeepCollectionEquality().equals(other.engagementByDayOfWeek, engagementByDayOfWeek));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,overallEngagementRate,totalEngagements,likes,comments,shares,saves,const DeepCollectionEquality().hash(engagementTrends),const DeepCollectionEquality().hash(engagementByContentType),const DeepCollectionEquality().hash(engagementByTimeOfDay),const DeepCollectionEquality().hash(engagementByDayOfWeek));

@override
String toString() {
  return 'EngagementData(overallEngagementRate: $overallEngagementRate, totalEngagements: $totalEngagements, likes: $likes, comments: $comments, shares: $shares, saves: $saves, engagementTrends: $engagementTrends, engagementByContentType: $engagementByContentType, engagementByTimeOfDay: $engagementByTimeOfDay, engagementByDayOfWeek: $engagementByDayOfWeek)';
}


}

/// @nodoc
abstract mixin class $EngagementDataCopyWith<$Res>  {
  factory $EngagementDataCopyWith(EngagementData value, $Res Function(EngagementData) _then) = _$EngagementDataCopyWithImpl;
@useResult
$Res call({
 double overallEngagementRate, int totalEngagements, int likes, int comments, int shares, int saves, List<EngagementTrend> engagementTrends, Map<String, double> engagementByContentType, Map<String, double> engagementByTimeOfDay, Map<String, double> engagementByDayOfWeek
});




}
/// @nodoc
class _$EngagementDataCopyWithImpl<$Res>
    implements $EngagementDataCopyWith<$Res> {
  _$EngagementDataCopyWithImpl(this._self, this._then);

  final EngagementData _self;
  final $Res Function(EngagementData) _then;

/// Create a copy of EngagementData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? overallEngagementRate = null,Object? totalEngagements = null,Object? likes = null,Object? comments = null,Object? shares = null,Object? saves = null,Object? engagementTrends = null,Object? engagementByContentType = null,Object? engagementByTimeOfDay = null,Object? engagementByDayOfWeek = null,}) {
  return _then(_self.copyWith(
overallEngagementRate: null == overallEngagementRate ? _self.overallEngagementRate : overallEngagementRate // ignore: cast_nullable_to_non_nullable
as double,totalEngagements: null == totalEngagements ? _self.totalEngagements : totalEngagements // ignore: cast_nullable_to_non_nullable
as int,likes: null == likes ? _self.likes : likes // ignore: cast_nullable_to_non_nullable
as int,comments: null == comments ? _self.comments : comments // ignore: cast_nullable_to_non_nullable
as int,shares: null == shares ? _self.shares : shares // ignore: cast_nullable_to_non_nullable
as int,saves: null == saves ? _self.saves : saves // ignore: cast_nullable_to_non_nullable
as int,engagementTrends: null == engagementTrends ? _self.engagementTrends : engagementTrends // ignore: cast_nullable_to_non_nullable
as List<EngagementTrend>,engagementByContentType: null == engagementByContentType ? _self.engagementByContentType : engagementByContentType // ignore: cast_nullable_to_non_nullable
as Map<String, double>,engagementByTimeOfDay: null == engagementByTimeOfDay ? _self.engagementByTimeOfDay : engagementByTimeOfDay // ignore: cast_nullable_to_non_nullable
as Map<String, double>,engagementByDayOfWeek: null == engagementByDayOfWeek ? _self.engagementByDayOfWeek : engagementByDayOfWeek // ignore: cast_nullable_to_non_nullable
as Map<String, double>,
  ));
}

}


/// Adds pattern-matching-related methods to [EngagementData].
extension EngagementDataPatterns on EngagementData {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _EngagementData value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _EngagementData() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _EngagementData value)  $default,){
final _that = this;
switch (_that) {
case _EngagementData():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _EngagementData value)?  $default,){
final _that = this;
switch (_that) {
case _EngagementData() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double overallEngagementRate,  int totalEngagements,  int likes,  int comments,  int shares,  int saves,  List<EngagementTrend> engagementTrends,  Map<String, double> engagementByContentType,  Map<String, double> engagementByTimeOfDay,  Map<String, double> engagementByDayOfWeek)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _EngagementData() when $default != null:
return $default(_that.overallEngagementRate,_that.totalEngagements,_that.likes,_that.comments,_that.shares,_that.saves,_that.engagementTrends,_that.engagementByContentType,_that.engagementByTimeOfDay,_that.engagementByDayOfWeek);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double overallEngagementRate,  int totalEngagements,  int likes,  int comments,  int shares,  int saves,  List<EngagementTrend> engagementTrends,  Map<String, double> engagementByContentType,  Map<String, double> engagementByTimeOfDay,  Map<String, double> engagementByDayOfWeek)  $default,) {final _that = this;
switch (_that) {
case _EngagementData():
return $default(_that.overallEngagementRate,_that.totalEngagements,_that.likes,_that.comments,_that.shares,_that.saves,_that.engagementTrends,_that.engagementByContentType,_that.engagementByTimeOfDay,_that.engagementByDayOfWeek);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double overallEngagementRate,  int totalEngagements,  int likes,  int comments,  int shares,  int saves,  List<EngagementTrend> engagementTrends,  Map<String, double> engagementByContentType,  Map<String, double> engagementByTimeOfDay,  Map<String, double> engagementByDayOfWeek)?  $default,) {final _that = this;
switch (_that) {
case _EngagementData() when $default != null:
return $default(_that.overallEngagementRate,_that.totalEngagements,_that.likes,_that.comments,_that.shares,_that.saves,_that.engagementTrends,_that.engagementByContentType,_that.engagementByTimeOfDay,_that.engagementByDayOfWeek);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _EngagementData implements EngagementData {
  const _EngagementData({required this.overallEngagementRate, required this.totalEngagements, required this.likes, required this.comments, required this.shares, required this.saves, required final  List<EngagementTrend> engagementTrends, required final  Map<String, double> engagementByContentType, required final  Map<String, double> engagementByTimeOfDay, required final  Map<String, double> engagementByDayOfWeek}): _engagementTrends = engagementTrends,_engagementByContentType = engagementByContentType,_engagementByTimeOfDay = engagementByTimeOfDay,_engagementByDayOfWeek = engagementByDayOfWeek;
  factory _EngagementData.fromJson(Map<String, dynamic> json) => _$EngagementDataFromJson(json);

@override final  double overallEngagementRate;
@override final  int totalEngagements;
@override final  int likes;
@override final  int comments;
@override final  int shares;
@override final  int saves;
 final  List<EngagementTrend> _engagementTrends;
@override List<EngagementTrend> get engagementTrends {
  if (_engagementTrends is EqualUnmodifiableListView) return _engagementTrends;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_engagementTrends);
}

 final  Map<String, double> _engagementByContentType;
@override Map<String, double> get engagementByContentType {
  if (_engagementByContentType is EqualUnmodifiableMapView) return _engagementByContentType;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_engagementByContentType);
}

 final  Map<String, double> _engagementByTimeOfDay;
@override Map<String, double> get engagementByTimeOfDay {
  if (_engagementByTimeOfDay is EqualUnmodifiableMapView) return _engagementByTimeOfDay;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_engagementByTimeOfDay);
}

 final  Map<String, double> _engagementByDayOfWeek;
@override Map<String, double> get engagementByDayOfWeek {
  if (_engagementByDayOfWeek is EqualUnmodifiableMapView) return _engagementByDayOfWeek;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_engagementByDayOfWeek);
}


/// Create a copy of EngagementData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$EngagementDataCopyWith<_EngagementData> get copyWith => __$EngagementDataCopyWithImpl<_EngagementData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$EngagementDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _EngagementData&&(identical(other.overallEngagementRate, overallEngagementRate) || other.overallEngagementRate == overallEngagementRate)&&(identical(other.totalEngagements, totalEngagements) || other.totalEngagements == totalEngagements)&&(identical(other.likes, likes) || other.likes == likes)&&(identical(other.comments, comments) || other.comments == comments)&&(identical(other.shares, shares) || other.shares == shares)&&(identical(other.saves, saves) || other.saves == saves)&&const DeepCollectionEquality().equals(other._engagementTrends, _engagementTrends)&&const DeepCollectionEquality().equals(other._engagementByContentType, _engagementByContentType)&&const DeepCollectionEquality().equals(other._engagementByTimeOfDay, _engagementByTimeOfDay)&&const DeepCollectionEquality().equals(other._engagementByDayOfWeek, _engagementByDayOfWeek));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,overallEngagementRate,totalEngagements,likes,comments,shares,saves,const DeepCollectionEquality().hash(_engagementTrends),const DeepCollectionEquality().hash(_engagementByContentType),const DeepCollectionEquality().hash(_engagementByTimeOfDay),const DeepCollectionEquality().hash(_engagementByDayOfWeek));

@override
String toString() {
  return 'EngagementData(overallEngagementRate: $overallEngagementRate, totalEngagements: $totalEngagements, likes: $likes, comments: $comments, shares: $shares, saves: $saves, engagementTrends: $engagementTrends, engagementByContentType: $engagementByContentType, engagementByTimeOfDay: $engagementByTimeOfDay, engagementByDayOfWeek: $engagementByDayOfWeek)';
}


}

/// @nodoc
abstract mixin class _$EngagementDataCopyWith<$Res> implements $EngagementDataCopyWith<$Res> {
  factory _$EngagementDataCopyWith(_EngagementData value, $Res Function(_EngagementData) _then) = __$EngagementDataCopyWithImpl;
@override @useResult
$Res call({
 double overallEngagementRate, int totalEngagements, int likes, int comments, int shares, int saves, List<EngagementTrend> engagementTrends, Map<String, double> engagementByContentType, Map<String, double> engagementByTimeOfDay, Map<String, double> engagementByDayOfWeek
});




}
/// @nodoc
class __$EngagementDataCopyWithImpl<$Res>
    implements _$EngagementDataCopyWith<$Res> {
  __$EngagementDataCopyWithImpl(this._self, this._then);

  final _EngagementData _self;
  final $Res Function(_EngagementData) _then;

/// Create a copy of EngagementData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? overallEngagementRate = null,Object? totalEngagements = null,Object? likes = null,Object? comments = null,Object? shares = null,Object? saves = null,Object? engagementTrends = null,Object? engagementByContentType = null,Object? engagementByTimeOfDay = null,Object? engagementByDayOfWeek = null,}) {
  return _then(_EngagementData(
overallEngagementRate: null == overallEngagementRate ? _self.overallEngagementRate : overallEngagementRate // ignore: cast_nullable_to_non_nullable
as double,totalEngagements: null == totalEngagements ? _self.totalEngagements : totalEngagements // ignore: cast_nullable_to_non_nullable
as int,likes: null == likes ? _self.likes : likes // ignore: cast_nullable_to_non_nullable
as int,comments: null == comments ? _self.comments : comments // ignore: cast_nullable_to_non_nullable
as int,shares: null == shares ? _self.shares : shares // ignore: cast_nullable_to_non_nullable
as int,saves: null == saves ? _self.saves : saves // ignore: cast_nullable_to_non_nullable
as int,engagementTrends: null == engagementTrends ? _self._engagementTrends : engagementTrends // ignore: cast_nullable_to_non_nullable
as List<EngagementTrend>,engagementByContentType: null == engagementByContentType ? _self._engagementByContentType : engagementByContentType // ignore: cast_nullable_to_non_nullable
as Map<String, double>,engagementByTimeOfDay: null == engagementByTimeOfDay ? _self._engagementByTimeOfDay : engagementByTimeOfDay // ignore: cast_nullable_to_non_nullable
as Map<String, double>,engagementByDayOfWeek: null == engagementByDayOfWeek ? _self._engagementByDayOfWeek : engagementByDayOfWeek // ignore: cast_nullable_to_non_nullable
as Map<String, double>,
  ));
}


}


/// @nodoc
mixin _$EngagementTrend {

 DateTime get date; double get engagementRate; int get totalEngagements;
/// Create a copy of EngagementTrend
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$EngagementTrendCopyWith<EngagementTrend> get copyWith => _$EngagementTrendCopyWithImpl<EngagementTrend>(this as EngagementTrend, _$identity);

  /// Serializes this EngagementTrend to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EngagementTrend&&(identical(other.date, date) || other.date == date)&&(identical(other.engagementRate, engagementRate) || other.engagementRate == engagementRate)&&(identical(other.totalEngagements, totalEngagements) || other.totalEngagements == totalEngagements));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,date,engagementRate,totalEngagements);

@override
String toString() {
  return 'EngagementTrend(date: $date, engagementRate: $engagementRate, totalEngagements: $totalEngagements)';
}


}

/// @nodoc
abstract mixin class $EngagementTrendCopyWith<$Res>  {
  factory $EngagementTrendCopyWith(EngagementTrend value, $Res Function(EngagementTrend) _then) = _$EngagementTrendCopyWithImpl;
@useResult
$Res call({
 DateTime date, double engagementRate, int totalEngagements
});




}
/// @nodoc
class _$EngagementTrendCopyWithImpl<$Res>
    implements $EngagementTrendCopyWith<$Res> {
  _$EngagementTrendCopyWithImpl(this._self, this._then);

  final EngagementTrend _self;
  final $Res Function(EngagementTrend) _then;

/// Create a copy of EngagementTrend
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? date = null,Object? engagementRate = null,Object? totalEngagements = null,}) {
  return _then(_self.copyWith(
date: null == date ? _self.date : date // ignore: cast_nullable_to_non_nullable
as DateTime,engagementRate: null == engagementRate ? _self.engagementRate : engagementRate // ignore: cast_nullable_to_non_nullable
as double,totalEngagements: null == totalEngagements ? _self.totalEngagements : totalEngagements // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [EngagementTrend].
extension EngagementTrendPatterns on EngagementTrend {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _EngagementTrend value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _EngagementTrend() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _EngagementTrend value)  $default,){
final _that = this;
switch (_that) {
case _EngagementTrend():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _EngagementTrend value)?  $default,){
final _that = this;
switch (_that) {
case _EngagementTrend() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( DateTime date,  double engagementRate,  int totalEngagements)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _EngagementTrend() when $default != null:
return $default(_that.date,_that.engagementRate,_that.totalEngagements);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( DateTime date,  double engagementRate,  int totalEngagements)  $default,) {final _that = this;
switch (_that) {
case _EngagementTrend():
return $default(_that.date,_that.engagementRate,_that.totalEngagements);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( DateTime date,  double engagementRate,  int totalEngagements)?  $default,) {final _that = this;
switch (_that) {
case _EngagementTrend() when $default != null:
return $default(_that.date,_that.engagementRate,_that.totalEngagements);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _EngagementTrend implements EngagementTrend {
  const _EngagementTrend({required this.date, required this.engagementRate, required this.totalEngagements});
  factory _EngagementTrend.fromJson(Map<String, dynamic> json) => _$EngagementTrendFromJson(json);

@override final  DateTime date;
@override final  double engagementRate;
@override final  int totalEngagements;

/// Create a copy of EngagementTrend
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$EngagementTrendCopyWith<_EngagementTrend> get copyWith => __$EngagementTrendCopyWithImpl<_EngagementTrend>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$EngagementTrendToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _EngagementTrend&&(identical(other.date, date) || other.date == date)&&(identical(other.engagementRate, engagementRate) || other.engagementRate == engagementRate)&&(identical(other.totalEngagements, totalEngagements) || other.totalEngagements == totalEngagements));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,date,engagementRate,totalEngagements);

@override
String toString() {
  return 'EngagementTrend(date: $date, engagementRate: $engagementRate, totalEngagements: $totalEngagements)';
}


}

/// @nodoc
abstract mixin class _$EngagementTrendCopyWith<$Res> implements $EngagementTrendCopyWith<$Res> {
  factory _$EngagementTrendCopyWith(_EngagementTrend value, $Res Function(_EngagementTrend) _then) = __$EngagementTrendCopyWithImpl;
@override @useResult
$Res call({
 DateTime date, double engagementRate, int totalEngagements
});




}
/// @nodoc
class __$EngagementTrendCopyWithImpl<$Res>
    implements _$EngagementTrendCopyWith<$Res> {
  __$EngagementTrendCopyWithImpl(this._self, this._then);

  final _EngagementTrend _self;
  final $Res Function(_EngagementTrend) _then;

/// Create a copy of EngagementTrend
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? date = null,Object? engagementRate = null,Object? totalEngagements = null,}) {
  return _then(_EngagementTrend(
date: null == date ? _self.date : date // ignore: cast_nullable_to_non_nullable
as DateTime,engagementRate: null == engagementRate ? _self.engagementRate : engagementRate // ignore: cast_nullable_to_non_nullable
as double,totalEngagements: null == totalEngagements ? _self.totalEngagements : totalEngagements // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}


/// @nodoc
mixin _$ContentPerformanceData {

 int get totalPosts; double get averageLikes; double get averageComments; double get averageShares; double get averageSaves; List<TopPerformingPost> get topPosts; Map<String, int> get postsByType; Map<String, double> get performanceByType;
/// Create a copy of ContentPerformanceData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ContentPerformanceDataCopyWith<ContentPerformanceData> get copyWith => _$ContentPerformanceDataCopyWithImpl<ContentPerformanceData>(this as ContentPerformanceData, _$identity);

  /// Serializes this ContentPerformanceData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ContentPerformanceData&&(identical(other.totalPosts, totalPosts) || other.totalPosts == totalPosts)&&(identical(other.averageLikes, averageLikes) || other.averageLikes == averageLikes)&&(identical(other.averageComments, averageComments) || other.averageComments == averageComments)&&(identical(other.averageShares, averageShares) || other.averageShares == averageShares)&&(identical(other.averageSaves, averageSaves) || other.averageSaves == averageSaves)&&const DeepCollectionEquality().equals(other.topPosts, topPosts)&&const DeepCollectionEquality().equals(other.postsByType, postsByType)&&const DeepCollectionEquality().equals(other.performanceByType, performanceByType));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalPosts,averageLikes,averageComments,averageShares,averageSaves,const DeepCollectionEquality().hash(topPosts),const DeepCollectionEquality().hash(postsByType),const DeepCollectionEquality().hash(performanceByType));

@override
String toString() {
  return 'ContentPerformanceData(totalPosts: $totalPosts, averageLikes: $averageLikes, averageComments: $averageComments, averageShares: $averageShares, averageSaves: $averageSaves, topPosts: $topPosts, postsByType: $postsByType, performanceByType: $performanceByType)';
}


}

/// @nodoc
abstract mixin class $ContentPerformanceDataCopyWith<$Res>  {
  factory $ContentPerformanceDataCopyWith(ContentPerformanceData value, $Res Function(ContentPerformanceData) _then) = _$ContentPerformanceDataCopyWithImpl;
@useResult
$Res call({
 int totalPosts, double averageLikes, double averageComments, double averageShares, double averageSaves, List<TopPerformingPost> topPosts, Map<String, int> postsByType, Map<String, double> performanceByType
});




}
/// @nodoc
class _$ContentPerformanceDataCopyWithImpl<$Res>
    implements $ContentPerformanceDataCopyWith<$Res> {
  _$ContentPerformanceDataCopyWithImpl(this._self, this._then);

  final ContentPerformanceData _self;
  final $Res Function(ContentPerformanceData) _then;

/// Create a copy of ContentPerformanceData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? totalPosts = null,Object? averageLikes = null,Object? averageComments = null,Object? averageShares = null,Object? averageSaves = null,Object? topPosts = null,Object? postsByType = null,Object? performanceByType = null,}) {
  return _then(_self.copyWith(
totalPosts: null == totalPosts ? _self.totalPosts : totalPosts // ignore: cast_nullable_to_non_nullable
as int,averageLikes: null == averageLikes ? _self.averageLikes : averageLikes // ignore: cast_nullable_to_non_nullable
as double,averageComments: null == averageComments ? _self.averageComments : averageComments // ignore: cast_nullable_to_non_nullable
as double,averageShares: null == averageShares ? _self.averageShares : averageShares // ignore: cast_nullable_to_non_nullable
as double,averageSaves: null == averageSaves ? _self.averageSaves : averageSaves // ignore: cast_nullable_to_non_nullable
as double,topPosts: null == topPosts ? _self.topPosts : topPosts // ignore: cast_nullable_to_non_nullable
as List<TopPerformingPost>,postsByType: null == postsByType ? _self.postsByType : postsByType // ignore: cast_nullable_to_non_nullable
as Map<String, int>,performanceByType: null == performanceByType ? _self.performanceByType : performanceByType // ignore: cast_nullable_to_non_nullable
as Map<String, double>,
  ));
}

}


/// Adds pattern-matching-related methods to [ContentPerformanceData].
extension ContentPerformanceDataPatterns on ContentPerformanceData {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ContentPerformanceData value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ContentPerformanceData() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ContentPerformanceData value)  $default,){
final _that = this;
switch (_that) {
case _ContentPerformanceData():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ContentPerformanceData value)?  $default,){
final _that = this;
switch (_that) {
case _ContentPerformanceData() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int totalPosts,  double averageLikes,  double averageComments,  double averageShares,  double averageSaves,  List<TopPerformingPost> topPosts,  Map<String, int> postsByType,  Map<String, double> performanceByType)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ContentPerformanceData() when $default != null:
return $default(_that.totalPosts,_that.averageLikes,_that.averageComments,_that.averageShares,_that.averageSaves,_that.topPosts,_that.postsByType,_that.performanceByType);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int totalPosts,  double averageLikes,  double averageComments,  double averageShares,  double averageSaves,  List<TopPerformingPost> topPosts,  Map<String, int> postsByType,  Map<String, double> performanceByType)  $default,) {final _that = this;
switch (_that) {
case _ContentPerformanceData():
return $default(_that.totalPosts,_that.averageLikes,_that.averageComments,_that.averageShares,_that.averageSaves,_that.topPosts,_that.postsByType,_that.performanceByType);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int totalPosts,  double averageLikes,  double averageComments,  double averageShares,  double averageSaves,  List<TopPerformingPost> topPosts,  Map<String, int> postsByType,  Map<String, double> performanceByType)?  $default,) {final _that = this;
switch (_that) {
case _ContentPerformanceData() when $default != null:
return $default(_that.totalPosts,_that.averageLikes,_that.averageComments,_that.averageShares,_that.averageSaves,_that.topPosts,_that.postsByType,_that.performanceByType);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ContentPerformanceData implements ContentPerformanceData {
  const _ContentPerformanceData({required this.totalPosts, required this.averageLikes, required this.averageComments, required this.averageShares, required this.averageSaves, required final  List<TopPerformingPost> topPosts, required final  Map<String, int> postsByType, required final  Map<String, double> performanceByType}): _topPosts = topPosts,_postsByType = postsByType,_performanceByType = performanceByType;
  factory _ContentPerformanceData.fromJson(Map<String, dynamic> json) => _$ContentPerformanceDataFromJson(json);

@override final  int totalPosts;
@override final  double averageLikes;
@override final  double averageComments;
@override final  double averageShares;
@override final  double averageSaves;
 final  List<TopPerformingPost> _topPosts;
@override List<TopPerformingPost> get topPosts {
  if (_topPosts is EqualUnmodifiableListView) return _topPosts;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_topPosts);
}

 final  Map<String, int> _postsByType;
@override Map<String, int> get postsByType {
  if (_postsByType is EqualUnmodifiableMapView) return _postsByType;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_postsByType);
}

 final  Map<String, double> _performanceByType;
@override Map<String, double> get performanceByType {
  if (_performanceByType is EqualUnmodifiableMapView) return _performanceByType;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_performanceByType);
}


/// Create a copy of ContentPerformanceData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ContentPerformanceDataCopyWith<_ContentPerformanceData> get copyWith => __$ContentPerformanceDataCopyWithImpl<_ContentPerformanceData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ContentPerformanceDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ContentPerformanceData&&(identical(other.totalPosts, totalPosts) || other.totalPosts == totalPosts)&&(identical(other.averageLikes, averageLikes) || other.averageLikes == averageLikes)&&(identical(other.averageComments, averageComments) || other.averageComments == averageComments)&&(identical(other.averageShares, averageShares) || other.averageShares == averageShares)&&(identical(other.averageSaves, averageSaves) || other.averageSaves == averageSaves)&&const DeepCollectionEquality().equals(other._topPosts, _topPosts)&&const DeepCollectionEquality().equals(other._postsByType, _postsByType)&&const DeepCollectionEquality().equals(other._performanceByType, _performanceByType));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalPosts,averageLikes,averageComments,averageShares,averageSaves,const DeepCollectionEquality().hash(_topPosts),const DeepCollectionEquality().hash(_postsByType),const DeepCollectionEquality().hash(_performanceByType));

@override
String toString() {
  return 'ContentPerformanceData(totalPosts: $totalPosts, averageLikes: $averageLikes, averageComments: $averageComments, averageShares: $averageShares, averageSaves: $averageSaves, topPosts: $topPosts, postsByType: $postsByType, performanceByType: $performanceByType)';
}


}

/// @nodoc
abstract mixin class _$ContentPerformanceDataCopyWith<$Res> implements $ContentPerformanceDataCopyWith<$Res> {
  factory _$ContentPerformanceDataCopyWith(_ContentPerformanceData value, $Res Function(_ContentPerformanceData) _then) = __$ContentPerformanceDataCopyWithImpl;
@override @useResult
$Res call({
 int totalPosts, double averageLikes, double averageComments, double averageShares, double averageSaves, List<TopPerformingPost> topPosts, Map<String, int> postsByType, Map<String, double> performanceByType
});




}
/// @nodoc
class __$ContentPerformanceDataCopyWithImpl<$Res>
    implements _$ContentPerformanceDataCopyWith<$Res> {
  __$ContentPerformanceDataCopyWithImpl(this._self, this._then);

  final _ContentPerformanceData _self;
  final $Res Function(_ContentPerformanceData) _then;

/// Create a copy of ContentPerformanceData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? totalPosts = null,Object? averageLikes = null,Object? averageComments = null,Object? averageShares = null,Object? averageSaves = null,Object? topPosts = null,Object? postsByType = null,Object? performanceByType = null,}) {
  return _then(_ContentPerformanceData(
totalPosts: null == totalPosts ? _self.totalPosts : totalPosts // ignore: cast_nullable_to_non_nullable
as int,averageLikes: null == averageLikes ? _self.averageLikes : averageLikes // ignore: cast_nullable_to_non_nullable
as double,averageComments: null == averageComments ? _self.averageComments : averageComments // ignore: cast_nullable_to_non_nullable
as double,averageShares: null == averageShares ? _self.averageShares : averageShares // ignore: cast_nullable_to_non_nullable
as double,averageSaves: null == averageSaves ? _self.averageSaves : averageSaves // ignore: cast_nullable_to_non_nullable
as double,topPosts: null == topPosts ? _self._topPosts : topPosts // ignore: cast_nullable_to_non_nullable
as List<TopPerformingPost>,postsByType: null == postsByType ? _self._postsByType : postsByType // ignore: cast_nullable_to_non_nullable
as Map<String, int>,performanceByType: null == performanceByType ? _self._performanceByType : performanceByType // ignore: cast_nullable_to_non_nullable
as Map<String, double>,
  ));
}


}


/// @nodoc
mixin _$TopPerformingPost {

 String get postId; String get postType; int get likes; int get comments; int get shares; int get saves; double get engagementRate; DateTime get postedAt;
/// Create a copy of TopPerformingPost
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TopPerformingPostCopyWith<TopPerformingPost> get copyWith => _$TopPerformingPostCopyWithImpl<TopPerformingPost>(this as TopPerformingPost, _$identity);

  /// Serializes this TopPerformingPost to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TopPerformingPost&&(identical(other.postId, postId) || other.postId == postId)&&(identical(other.postType, postType) || other.postType == postType)&&(identical(other.likes, likes) || other.likes == likes)&&(identical(other.comments, comments) || other.comments == comments)&&(identical(other.shares, shares) || other.shares == shares)&&(identical(other.saves, saves) || other.saves == saves)&&(identical(other.engagementRate, engagementRate) || other.engagementRate == engagementRate)&&(identical(other.postedAt, postedAt) || other.postedAt == postedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,postId,postType,likes,comments,shares,saves,engagementRate,postedAt);

@override
String toString() {
  return 'TopPerformingPost(postId: $postId, postType: $postType, likes: $likes, comments: $comments, shares: $shares, saves: $saves, engagementRate: $engagementRate, postedAt: $postedAt)';
}


}

/// @nodoc
abstract mixin class $TopPerformingPostCopyWith<$Res>  {
  factory $TopPerformingPostCopyWith(TopPerformingPost value, $Res Function(TopPerformingPost) _then) = _$TopPerformingPostCopyWithImpl;
@useResult
$Res call({
 String postId, String postType, int likes, int comments, int shares, int saves, double engagementRate, DateTime postedAt
});




}
/// @nodoc
class _$TopPerformingPostCopyWithImpl<$Res>
    implements $TopPerformingPostCopyWith<$Res> {
  _$TopPerformingPostCopyWithImpl(this._self, this._then);

  final TopPerformingPost _self;
  final $Res Function(TopPerformingPost) _then;

/// Create a copy of TopPerformingPost
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? postId = null,Object? postType = null,Object? likes = null,Object? comments = null,Object? shares = null,Object? saves = null,Object? engagementRate = null,Object? postedAt = null,}) {
  return _then(_self.copyWith(
postId: null == postId ? _self.postId : postId // ignore: cast_nullable_to_non_nullable
as String,postType: null == postType ? _self.postType : postType // ignore: cast_nullable_to_non_nullable
as String,likes: null == likes ? _self.likes : likes // ignore: cast_nullable_to_non_nullable
as int,comments: null == comments ? _self.comments : comments // ignore: cast_nullable_to_non_nullable
as int,shares: null == shares ? _self.shares : shares // ignore: cast_nullable_to_non_nullable
as int,saves: null == saves ? _self.saves : saves // ignore: cast_nullable_to_non_nullable
as int,engagementRate: null == engagementRate ? _self.engagementRate : engagementRate // ignore: cast_nullable_to_non_nullable
as double,postedAt: null == postedAt ? _self.postedAt : postedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// Adds pattern-matching-related methods to [TopPerformingPost].
extension TopPerformingPostPatterns on TopPerformingPost {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _TopPerformingPost value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _TopPerformingPost() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _TopPerformingPost value)  $default,){
final _that = this;
switch (_that) {
case _TopPerformingPost():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _TopPerformingPost value)?  $default,){
final _that = this;
switch (_that) {
case _TopPerformingPost() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String postId,  String postType,  int likes,  int comments,  int shares,  int saves,  double engagementRate,  DateTime postedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _TopPerformingPost() when $default != null:
return $default(_that.postId,_that.postType,_that.likes,_that.comments,_that.shares,_that.saves,_that.engagementRate,_that.postedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String postId,  String postType,  int likes,  int comments,  int shares,  int saves,  double engagementRate,  DateTime postedAt)  $default,) {final _that = this;
switch (_that) {
case _TopPerformingPost():
return $default(_that.postId,_that.postType,_that.likes,_that.comments,_that.shares,_that.saves,_that.engagementRate,_that.postedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String postId,  String postType,  int likes,  int comments,  int shares,  int saves,  double engagementRate,  DateTime postedAt)?  $default,) {final _that = this;
switch (_that) {
case _TopPerformingPost() when $default != null:
return $default(_that.postId,_that.postType,_that.likes,_that.comments,_that.shares,_that.saves,_that.engagementRate,_that.postedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _TopPerformingPost implements TopPerformingPost {
  const _TopPerformingPost({required this.postId, required this.postType, required this.likes, required this.comments, required this.shares, required this.saves, required this.engagementRate, required this.postedAt});
  factory _TopPerformingPost.fromJson(Map<String, dynamic> json) => _$TopPerformingPostFromJson(json);

@override final  String postId;
@override final  String postType;
@override final  int likes;
@override final  int comments;
@override final  int shares;
@override final  int saves;
@override final  double engagementRate;
@override final  DateTime postedAt;

/// Create a copy of TopPerformingPost
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TopPerformingPostCopyWith<_TopPerformingPost> get copyWith => __$TopPerformingPostCopyWithImpl<_TopPerformingPost>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TopPerformingPostToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TopPerformingPost&&(identical(other.postId, postId) || other.postId == postId)&&(identical(other.postType, postType) || other.postType == postType)&&(identical(other.likes, likes) || other.likes == likes)&&(identical(other.comments, comments) || other.comments == comments)&&(identical(other.shares, shares) || other.shares == shares)&&(identical(other.saves, saves) || other.saves == saves)&&(identical(other.engagementRate, engagementRate) || other.engagementRate == engagementRate)&&(identical(other.postedAt, postedAt) || other.postedAt == postedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,postId,postType,likes,comments,shares,saves,engagementRate,postedAt);

@override
String toString() {
  return 'TopPerformingPost(postId: $postId, postType: $postType, likes: $likes, comments: $comments, shares: $shares, saves: $saves, engagementRate: $engagementRate, postedAt: $postedAt)';
}


}

/// @nodoc
abstract mixin class _$TopPerformingPostCopyWith<$Res> implements $TopPerformingPostCopyWith<$Res> {
  factory _$TopPerformingPostCopyWith(_TopPerformingPost value, $Res Function(_TopPerformingPost) _then) = __$TopPerformingPostCopyWithImpl;
@override @useResult
$Res call({
 String postId, String postType, int likes, int comments, int shares, int saves, double engagementRate, DateTime postedAt
});




}
/// @nodoc
class __$TopPerformingPostCopyWithImpl<$Res>
    implements _$TopPerformingPostCopyWith<$Res> {
  __$TopPerformingPostCopyWithImpl(this._self, this._then);

  final _TopPerformingPost _self;
  final $Res Function(_TopPerformingPost) _then;

/// Create a copy of TopPerformingPost
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? postId = null,Object? postType = null,Object? likes = null,Object? comments = null,Object? shares = null,Object? saves = null,Object? engagementRate = null,Object? postedAt = null,}) {
  return _then(_TopPerformingPost(
postId: null == postId ? _self.postId : postId // ignore: cast_nullable_to_non_nullable
as String,postType: null == postType ? _self.postType : postType // ignore: cast_nullable_to_non_nullable
as String,likes: null == likes ? _self.likes : likes // ignore: cast_nullable_to_non_nullable
as int,comments: null == comments ? _self.comments : comments // ignore: cast_nullable_to_non_nullable
as int,shares: null == shares ? _self.shares : shares // ignore: cast_nullable_to_non_nullable
as int,saves: null == saves ? _self.saves : saves // ignore: cast_nullable_to_non_nullable
as int,engagementRate: null == engagementRate ? _self.engagementRate : engagementRate // ignore: cast_nullable_to_non_nullable
as double,postedAt: null == postedAt ? _self.postedAt : postedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}


/// @nodoc
mixin _$AudienceData {

 int get totalAudience; Map<String, int> get audienceByCountry; Map<String, int> get audienceByAge; Map<String, int> get audienceByGender; Map<String, int> get audienceByInterest; List<String> get topCountries; List<String> get topInterests; double get averageAge; String get dominantGender;
/// Create a copy of AudienceData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AudienceDataCopyWith<AudienceData> get copyWith => _$AudienceDataCopyWithImpl<AudienceData>(this as AudienceData, _$identity);

  /// Serializes this AudienceData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AudienceData&&(identical(other.totalAudience, totalAudience) || other.totalAudience == totalAudience)&&const DeepCollectionEquality().equals(other.audienceByCountry, audienceByCountry)&&const DeepCollectionEquality().equals(other.audienceByAge, audienceByAge)&&const DeepCollectionEquality().equals(other.audienceByGender, audienceByGender)&&const DeepCollectionEquality().equals(other.audienceByInterest, audienceByInterest)&&const DeepCollectionEquality().equals(other.topCountries, topCountries)&&const DeepCollectionEquality().equals(other.topInterests, topInterests)&&(identical(other.averageAge, averageAge) || other.averageAge == averageAge)&&(identical(other.dominantGender, dominantGender) || other.dominantGender == dominantGender));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalAudience,const DeepCollectionEquality().hash(audienceByCountry),const DeepCollectionEquality().hash(audienceByAge),const DeepCollectionEquality().hash(audienceByGender),const DeepCollectionEquality().hash(audienceByInterest),const DeepCollectionEquality().hash(topCountries),const DeepCollectionEquality().hash(topInterests),averageAge,dominantGender);

@override
String toString() {
  return 'AudienceData(totalAudience: $totalAudience, audienceByCountry: $audienceByCountry, audienceByAge: $audienceByAge, audienceByGender: $audienceByGender, audienceByInterest: $audienceByInterest, topCountries: $topCountries, topInterests: $topInterests, averageAge: $averageAge, dominantGender: $dominantGender)';
}


}

/// @nodoc
abstract mixin class $AudienceDataCopyWith<$Res>  {
  factory $AudienceDataCopyWith(AudienceData value, $Res Function(AudienceData) _then) = _$AudienceDataCopyWithImpl;
@useResult
$Res call({
 int totalAudience, Map<String, int> audienceByCountry, Map<String, int> audienceByAge, Map<String, int> audienceByGender, Map<String, int> audienceByInterest, List<String> topCountries, List<String> topInterests, double averageAge, String dominantGender
});




}
/// @nodoc
class _$AudienceDataCopyWithImpl<$Res>
    implements $AudienceDataCopyWith<$Res> {
  _$AudienceDataCopyWithImpl(this._self, this._then);

  final AudienceData _self;
  final $Res Function(AudienceData) _then;

/// Create a copy of AudienceData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? totalAudience = null,Object? audienceByCountry = null,Object? audienceByAge = null,Object? audienceByGender = null,Object? audienceByInterest = null,Object? topCountries = null,Object? topInterests = null,Object? averageAge = null,Object? dominantGender = null,}) {
  return _then(_self.copyWith(
totalAudience: null == totalAudience ? _self.totalAudience : totalAudience // ignore: cast_nullable_to_non_nullable
as int,audienceByCountry: null == audienceByCountry ? _self.audienceByCountry : audienceByCountry // ignore: cast_nullable_to_non_nullable
as Map<String, int>,audienceByAge: null == audienceByAge ? _self.audienceByAge : audienceByAge // ignore: cast_nullable_to_non_nullable
as Map<String, int>,audienceByGender: null == audienceByGender ? _self.audienceByGender : audienceByGender // ignore: cast_nullable_to_non_nullable
as Map<String, int>,audienceByInterest: null == audienceByInterest ? _self.audienceByInterest : audienceByInterest // ignore: cast_nullable_to_non_nullable
as Map<String, int>,topCountries: null == topCountries ? _self.topCountries : topCountries // ignore: cast_nullable_to_non_nullable
as List<String>,topInterests: null == topInterests ? _self.topInterests : topInterests // ignore: cast_nullable_to_non_nullable
as List<String>,averageAge: null == averageAge ? _self.averageAge : averageAge // ignore: cast_nullable_to_non_nullable
as double,dominantGender: null == dominantGender ? _self.dominantGender : dominantGender // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [AudienceData].
extension AudienceDataPatterns on AudienceData {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _AudienceData value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _AudienceData() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _AudienceData value)  $default,){
final _that = this;
switch (_that) {
case _AudienceData():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _AudienceData value)?  $default,){
final _that = this;
switch (_that) {
case _AudienceData() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int totalAudience,  Map<String, int> audienceByCountry,  Map<String, int> audienceByAge,  Map<String, int> audienceByGender,  Map<String, int> audienceByInterest,  List<String> topCountries,  List<String> topInterests,  double averageAge,  String dominantGender)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _AudienceData() when $default != null:
return $default(_that.totalAudience,_that.audienceByCountry,_that.audienceByAge,_that.audienceByGender,_that.audienceByInterest,_that.topCountries,_that.topInterests,_that.averageAge,_that.dominantGender);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int totalAudience,  Map<String, int> audienceByCountry,  Map<String, int> audienceByAge,  Map<String, int> audienceByGender,  Map<String, int> audienceByInterest,  List<String> topCountries,  List<String> topInterests,  double averageAge,  String dominantGender)  $default,) {final _that = this;
switch (_that) {
case _AudienceData():
return $default(_that.totalAudience,_that.audienceByCountry,_that.audienceByAge,_that.audienceByGender,_that.audienceByInterest,_that.topCountries,_that.topInterests,_that.averageAge,_that.dominantGender);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int totalAudience,  Map<String, int> audienceByCountry,  Map<String, int> audienceByAge,  Map<String, int> audienceByGender,  Map<String, int> audienceByInterest,  List<String> topCountries,  List<String> topInterests,  double averageAge,  String dominantGender)?  $default,) {final _that = this;
switch (_that) {
case _AudienceData() when $default != null:
return $default(_that.totalAudience,_that.audienceByCountry,_that.audienceByAge,_that.audienceByGender,_that.audienceByInterest,_that.topCountries,_that.topInterests,_that.averageAge,_that.dominantGender);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _AudienceData implements AudienceData {
  const _AudienceData({required this.totalAudience, required final  Map<String, int> audienceByCountry, required final  Map<String, int> audienceByAge, required final  Map<String, int> audienceByGender, required final  Map<String, int> audienceByInterest, required final  List<String> topCountries, required final  List<String> topInterests, required this.averageAge, required this.dominantGender}): _audienceByCountry = audienceByCountry,_audienceByAge = audienceByAge,_audienceByGender = audienceByGender,_audienceByInterest = audienceByInterest,_topCountries = topCountries,_topInterests = topInterests;
  factory _AudienceData.fromJson(Map<String, dynamic> json) => _$AudienceDataFromJson(json);

@override final  int totalAudience;
 final  Map<String, int> _audienceByCountry;
@override Map<String, int> get audienceByCountry {
  if (_audienceByCountry is EqualUnmodifiableMapView) return _audienceByCountry;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_audienceByCountry);
}

 final  Map<String, int> _audienceByAge;
@override Map<String, int> get audienceByAge {
  if (_audienceByAge is EqualUnmodifiableMapView) return _audienceByAge;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_audienceByAge);
}

 final  Map<String, int> _audienceByGender;
@override Map<String, int> get audienceByGender {
  if (_audienceByGender is EqualUnmodifiableMapView) return _audienceByGender;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_audienceByGender);
}

 final  Map<String, int> _audienceByInterest;
@override Map<String, int> get audienceByInterest {
  if (_audienceByInterest is EqualUnmodifiableMapView) return _audienceByInterest;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_audienceByInterest);
}

 final  List<String> _topCountries;
@override List<String> get topCountries {
  if (_topCountries is EqualUnmodifiableListView) return _topCountries;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_topCountries);
}

 final  List<String> _topInterests;
@override List<String> get topInterests {
  if (_topInterests is EqualUnmodifiableListView) return _topInterests;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_topInterests);
}

@override final  double averageAge;
@override final  String dominantGender;

/// Create a copy of AudienceData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AudienceDataCopyWith<_AudienceData> get copyWith => __$AudienceDataCopyWithImpl<_AudienceData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AudienceDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AudienceData&&(identical(other.totalAudience, totalAudience) || other.totalAudience == totalAudience)&&const DeepCollectionEquality().equals(other._audienceByCountry, _audienceByCountry)&&const DeepCollectionEquality().equals(other._audienceByAge, _audienceByAge)&&const DeepCollectionEquality().equals(other._audienceByGender, _audienceByGender)&&const DeepCollectionEquality().equals(other._audienceByInterest, _audienceByInterest)&&const DeepCollectionEquality().equals(other._topCountries, _topCountries)&&const DeepCollectionEquality().equals(other._topInterests, _topInterests)&&(identical(other.averageAge, averageAge) || other.averageAge == averageAge)&&(identical(other.dominantGender, dominantGender) || other.dominantGender == dominantGender));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalAudience,const DeepCollectionEquality().hash(_audienceByCountry),const DeepCollectionEquality().hash(_audienceByAge),const DeepCollectionEquality().hash(_audienceByGender),const DeepCollectionEquality().hash(_audienceByInterest),const DeepCollectionEquality().hash(_topCountries),const DeepCollectionEquality().hash(_topInterests),averageAge,dominantGender);

@override
String toString() {
  return 'AudienceData(totalAudience: $totalAudience, audienceByCountry: $audienceByCountry, audienceByAge: $audienceByAge, audienceByGender: $audienceByGender, audienceByInterest: $audienceByInterest, topCountries: $topCountries, topInterests: $topInterests, averageAge: $averageAge, dominantGender: $dominantGender)';
}


}

/// @nodoc
abstract mixin class _$AudienceDataCopyWith<$Res> implements $AudienceDataCopyWith<$Res> {
  factory _$AudienceDataCopyWith(_AudienceData value, $Res Function(_AudienceData) _then) = __$AudienceDataCopyWithImpl;
@override @useResult
$Res call({
 int totalAudience, Map<String, int> audienceByCountry, Map<String, int> audienceByAge, Map<String, int> audienceByGender, Map<String, int> audienceByInterest, List<String> topCountries, List<String> topInterests, double averageAge, String dominantGender
});




}
/// @nodoc
class __$AudienceDataCopyWithImpl<$Res>
    implements _$AudienceDataCopyWith<$Res> {
  __$AudienceDataCopyWithImpl(this._self, this._then);

  final _AudienceData _self;
  final $Res Function(_AudienceData) _then;

/// Create a copy of AudienceData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? totalAudience = null,Object? audienceByCountry = null,Object? audienceByAge = null,Object? audienceByGender = null,Object? audienceByInterest = null,Object? topCountries = null,Object? topInterests = null,Object? averageAge = null,Object? dominantGender = null,}) {
  return _then(_AudienceData(
totalAudience: null == totalAudience ? _self.totalAudience : totalAudience // ignore: cast_nullable_to_non_nullable
as int,audienceByCountry: null == audienceByCountry ? _self._audienceByCountry : audienceByCountry // ignore: cast_nullable_to_non_nullable
as Map<String, int>,audienceByAge: null == audienceByAge ? _self._audienceByAge : audienceByAge // ignore: cast_nullable_to_non_nullable
as Map<String, int>,audienceByGender: null == audienceByGender ? _self._audienceByGender : audienceByGender // ignore: cast_nullable_to_non_nullable
as Map<String, int>,audienceByInterest: null == audienceByInterest ? _self._audienceByInterest : audienceByInterest // ignore: cast_nullable_to_non_nullable
as Map<String, int>,topCountries: null == topCountries ? _self._topCountries : topCountries // ignore: cast_nullable_to_non_nullable
as List<String>,topInterests: null == topInterests ? _self._topInterests : topInterests // ignore: cast_nullable_to_non_nullable
as List<String>,averageAge: null == averageAge ? _self.averageAge : averageAge // ignore: cast_nullable_to_non_nullable
as double,dominantGender: null == dominantGender ? _self.dominantGender : dominantGender // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$ProfileAnalyticsSummary {

 String get userId; DateTime get periodStart; DateTime get periodEnd; int get totalProfileViews; int get totalFollowers; double get engagementRate; int get totalPosts; double get followerGrowthRate; List<String> get topPerformingContent; Map<String, dynamic> get insights;
/// Create a copy of ProfileAnalyticsSummary
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProfileAnalyticsSummaryCopyWith<ProfileAnalyticsSummary> get copyWith => _$ProfileAnalyticsSummaryCopyWithImpl<ProfileAnalyticsSummary>(this as ProfileAnalyticsSummary, _$identity);

  /// Serializes this ProfileAnalyticsSummary to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProfileAnalyticsSummary&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.periodStart, periodStart) || other.periodStart == periodStart)&&(identical(other.periodEnd, periodEnd) || other.periodEnd == periodEnd)&&(identical(other.totalProfileViews, totalProfileViews) || other.totalProfileViews == totalProfileViews)&&(identical(other.totalFollowers, totalFollowers) || other.totalFollowers == totalFollowers)&&(identical(other.engagementRate, engagementRate) || other.engagementRate == engagementRate)&&(identical(other.totalPosts, totalPosts) || other.totalPosts == totalPosts)&&(identical(other.followerGrowthRate, followerGrowthRate) || other.followerGrowthRate == followerGrowthRate)&&const DeepCollectionEquality().equals(other.topPerformingContent, topPerformingContent)&&const DeepCollectionEquality().equals(other.insights, insights));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,userId,periodStart,periodEnd,totalProfileViews,totalFollowers,engagementRate,totalPosts,followerGrowthRate,const DeepCollectionEquality().hash(topPerformingContent),const DeepCollectionEquality().hash(insights));

@override
String toString() {
  return 'ProfileAnalyticsSummary(userId: $userId, periodStart: $periodStart, periodEnd: $periodEnd, totalProfileViews: $totalProfileViews, totalFollowers: $totalFollowers, engagementRate: $engagementRate, totalPosts: $totalPosts, followerGrowthRate: $followerGrowthRate, topPerformingContent: $topPerformingContent, insights: $insights)';
}


}

/// @nodoc
abstract mixin class $ProfileAnalyticsSummaryCopyWith<$Res>  {
  factory $ProfileAnalyticsSummaryCopyWith(ProfileAnalyticsSummary value, $Res Function(ProfileAnalyticsSummary) _then) = _$ProfileAnalyticsSummaryCopyWithImpl;
@useResult
$Res call({
 String userId, DateTime periodStart, DateTime periodEnd, int totalProfileViews, int totalFollowers, double engagementRate, int totalPosts, double followerGrowthRate, List<String> topPerformingContent, Map<String, dynamic> insights
});




}
/// @nodoc
class _$ProfileAnalyticsSummaryCopyWithImpl<$Res>
    implements $ProfileAnalyticsSummaryCopyWith<$Res> {
  _$ProfileAnalyticsSummaryCopyWithImpl(this._self, this._then);

  final ProfileAnalyticsSummary _self;
  final $Res Function(ProfileAnalyticsSummary) _then;

/// Create a copy of ProfileAnalyticsSummary
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? userId = null,Object? periodStart = null,Object? periodEnd = null,Object? totalProfileViews = null,Object? totalFollowers = null,Object? engagementRate = null,Object? totalPosts = null,Object? followerGrowthRate = null,Object? topPerformingContent = null,Object? insights = null,}) {
  return _then(_self.copyWith(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,periodStart: null == periodStart ? _self.periodStart : periodStart // ignore: cast_nullable_to_non_nullable
as DateTime,periodEnd: null == periodEnd ? _self.periodEnd : periodEnd // ignore: cast_nullable_to_non_nullable
as DateTime,totalProfileViews: null == totalProfileViews ? _self.totalProfileViews : totalProfileViews // ignore: cast_nullable_to_non_nullable
as int,totalFollowers: null == totalFollowers ? _self.totalFollowers : totalFollowers // ignore: cast_nullable_to_non_nullable
as int,engagementRate: null == engagementRate ? _self.engagementRate : engagementRate // ignore: cast_nullable_to_non_nullable
as double,totalPosts: null == totalPosts ? _self.totalPosts : totalPosts // ignore: cast_nullable_to_non_nullable
as int,followerGrowthRate: null == followerGrowthRate ? _self.followerGrowthRate : followerGrowthRate // ignore: cast_nullable_to_non_nullable
as double,topPerformingContent: null == topPerformingContent ? _self.topPerformingContent : topPerformingContent // ignore: cast_nullable_to_non_nullable
as List<String>,insights: null == insights ? _self.insights : insights // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}

}


/// Adds pattern-matching-related methods to [ProfileAnalyticsSummary].
extension ProfileAnalyticsSummaryPatterns on ProfileAnalyticsSummary {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ProfileAnalyticsSummary value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ProfileAnalyticsSummary() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ProfileAnalyticsSummary value)  $default,){
final _that = this;
switch (_that) {
case _ProfileAnalyticsSummary():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ProfileAnalyticsSummary value)?  $default,){
final _that = this;
switch (_that) {
case _ProfileAnalyticsSummary() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String userId,  DateTime periodStart,  DateTime periodEnd,  int totalProfileViews,  int totalFollowers,  double engagementRate,  int totalPosts,  double followerGrowthRate,  List<String> topPerformingContent,  Map<String, dynamic> insights)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ProfileAnalyticsSummary() when $default != null:
return $default(_that.userId,_that.periodStart,_that.periodEnd,_that.totalProfileViews,_that.totalFollowers,_that.engagementRate,_that.totalPosts,_that.followerGrowthRate,_that.topPerformingContent,_that.insights);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String userId,  DateTime periodStart,  DateTime periodEnd,  int totalProfileViews,  int totalFollowers,  double engagementRate,  int totalPosts,  double followerGrowthRate,  List<String> topPerformingContent,  Map<String, dynamic> insights)  $default,) {final _that = this;
switch (_that) {
case _ProfileAnalyticsSummary():
return $default(_that.userId,_that.periodStart,_that.periodEnd,_that.totalProfileViews,_that.totalFollowers,_that.engagementRate,_that.totalPosts,_that.followerGrowthRate,_that.topPerformingContent,_that.insights);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String userId,  DateTime periodStart,  DateTime periodEnd,  int totalProfileViews,  int totalFollowers,  double engagementRate,  int totalPosts,  double followerGrowthRate,  List<String> topPerformingContent,  Map<String, dynamic> insights)?  $default,) {final _that = this;
switch (_that) {
case _ProfileAnalyticsSummary() when $default != null:
return $default(_that.userId,_that.periodStart,_that.periodEnd,_that.totalProfileViews,_that.totalFollowers,_that.engagementRate,_that.totalPosts,_that.followerGrowthRate,_that.topPerformingContent,_that.insights);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ProfileAnalyticsSummary implements ProfileAnalyticsSummary {
  const _ProfileAnalyticsSummary({required this.userId, required this.periodStart, required this.periodEnd, required this.totalProfileViews, required this.totalFollowers, required this.engagementRate, required this.totalPosts, required this.followerGrowthRate, required final  List<String> topPerformingContent, required final  Map<String, dynamic> insights}): _topPerformingContent = topPerformingContent,_insights = insights;
  factory _ProfileAnalyticsSummary.fromJson(Map<String, dynamic> json) => _$ProfileAnalyticsSummaryFromJson(json);

@override final  String userId;
@override final  DateTime periodStart;
@override final  DateTime periodEnd;
@override final  int totalProfileViews;
@override final  int totalFollowers;
@override final  double engagementRate;
@override final  int totalPosts;
@override final  double followerGrowthRate;
 final  List<String> _topPerformingContent;
@override List<String> get topPerformingContent {
  if (_topPerformingContent is EqualUnmodifiableListView) return _topPerformingContent;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_topPerformingContent);
}

 final  Map<String, dynamic> _insights;
@override Map<String, dynamic> get insights {
  if (_insights is EqualUnmodifiableMapView) return _insights;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_insights);
}


/// Create a copy of ProfileAnalyticsSummary
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProfileAnalyticsSummaryCopyWith<_ProfileAnalyticsSummary> get copyWith => __$ProfileAnalyticsSummaryCopyWithImpl<_ProfileAnalyticsSummary>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ProfileAnalyticsSummaryToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProfileAnalyticsSummary&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.periodStart, periodStart) || other.periodStart == periodStart)&&(identical(other.periodEnd, periodEnd) || other.periodEnd == periodEnd)&&(identical(other.totalProfileViews, totalProfileViews) || other.totalProfileViews == totalProfileViews)&&(identical(other.totalFollowers, totalFollowers) || other.totalFollowers == totalFollowers)&&(identical(other.engagementRate, engagementRate) || other.engagementRate == engagementRate)&&(identical(other.totalPosts, totalPosts) || other.totalPosts == totalPosts)&&(identical(other.followerGrowthRate, followerGrowthRate) || other.followerGrowthRate == followerGrowthRate)&&const DeepCollectionEquality().equals(other._topPerformingContent, _topPerformingContent)&&const DeepCollectionEquality().equals(other._insights, _insights));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,userId,periodStart,periodEnd,totalProfileViews,totalFollowers,engagementRate,totalPosts,followerGrowthRate,const DeepCollectionEquality().hash(_topPerformingContent),const DeepCollectionEquality().hash(_insights));

@override
String toString() {
  return 'ProfileAnalyticsSummary(userId: $userId, periodStart: $periodStart, periodEnd: $periodEnd, totalProfileViews: $totalProfileViews, totalFollowers: $totalFollowers, engagementRate: $engagementRate, totalPosts: $totalPosts, followerGrowthRate: $followerGrowthRate, topPerformingContent: $topPerformingContent, insights: $insights)';
}


}

/// @nodoc
abstract mixin class _$ProfileAnalyticsSummaryCopyWith<$Res> implements $ProfileAnalyticsSummaryCopyWith<$Res> {
  factory _$ProfileAnalyticsSummaryCopyWith(_ProfileAnalyticsSummary value, $Res Function(_ProfileAnalyticsSummary) _then) = __$ProfileAnalyticsSummaryCopyWithImpl;
@override @useResult
$Res call({
 String userId, DateTime periodStart, DateTime periodEnd, int totalProfileViews, int totalFollowers, double engagementRate, int totalPosts, double followerGrowthRate, List<String> topPerformingContent, Map<String, dynamic> insights
});




}
/// @nodoc
class __$ProfileAnalyticsSummaryCopyWithImpl<$Res>
    implements _$ProfileAnalyticsSummaryCopyWith<$Res> {
  __$ProfileAnalyticsSummaryCopyWithImpl(this._self, this._then);

  final _ProfileAnalyticsSummary _self;
  final $Res Function(_ProfileAnalyticsSummary) _then;

/// Create a copy of ProfileAnalyticsSummary
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? userId = null,Object? periodStart = null,Object? periodEnd = null,Object? totalProfileViews = null,Object? totalFollowers = null,Object? engagementRate = null,Object? totalPosts = null,Object? followerGrowthRate = null,Object? topPerformingContent = null,Object? insights = null,}) {
  return _then(_ProfileAnalyticsSummary(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,periodStart: null == periodStart ? _self.periodStart : periodStart // ignore: cast_nullable_to_non_nullable
as DateTime,periodEnd: null == periodEnd ? _self.periodEnd : periodEnd // ignore: cast_nullable_to_non_nullable
as DateTime,totalProfileViews: null == totalProfileViews ? _self.totalProfileViews : totalProfileViews // ignore: cast_nullable_to_non_nullable
as int,totalFollowers: null == totalFollowers ? _self.totalFollowers : totalFollowers // ignore: cast_nullable_to_non_nullable
as int,engagementRate: null == engagementRate ? _self.engagementRate : engagementRate // ignore: cast_nullable_to_non_nullable
as double,totalPosts: null == totalPosts ? _self.totalPosts : totalPosts // ignore: cast_nullable_to_non_nullable
as int,followerGrowthRate: null == followerGrowthRate ? _self.followerGrowthRate : followerGrowthRate // ignore: cast_nullable_to_non_nullable
as double,topPerformingContent: null == topPerformingContent ? _self._topPerformingContent : topPerformingContent // ignore: cast_nullable_to_non_nullable
as List<String>,insights: null == insights ? _self._insights : insights // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}


}

// dart format on
