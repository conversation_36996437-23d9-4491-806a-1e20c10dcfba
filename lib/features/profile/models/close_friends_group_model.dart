import 'package:freezed_annotation/freezed_annotation.dart';

part 'close_friends_group_model.freezed.dart';
part 'close_friends_group_model.g.dart';

@freezed
abstract class CloseFriendsGroup with _$CloseFriendsGroup {
  const factory CloseFriendsGroup({
    required String id,
    required String name,
    required String emoji,
    required List<String> memberIds,
    required bool isStoryVisible,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) = _CloseFriendsGroup;

  factory CloseFriendsGroup.fromJson(Map<String, dynamic> json) =>
      _$CloseFriendsGroupFromJson(json);
}
