// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'close_friends_group_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CloseFriendsGroup {

 String get id; String get name; String get emoji; List<String> get memberIds; bool get isStoryVisible; DateTime get createdAt; DateTime get updatedAt;
/// Create a copy of CloseFriendsGroup
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CloseFriendsGroupCopyWith<CloseFriendsGroup> get copyWith => _$CloseFriendsGroupCopyWithImpl<CloseFriendsGroup>(this as CloseFriendsGroup, _$identity);

  /// Serializes this CloseFriendsGroup to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CloseFriendsGroup&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.emoji, emoji) || other.emoji == emoji)&&const DeepCollectionEquality().equals(other.memberIds, memberIds)&&(identical(other.isStoryVisible, isStoryVisible) || other.isStoryVisible == isStoryVisible)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,emoji,const DeepCollectionEquality().hash(memberIds),isStoryVisible,createdAt,updatedAt);

@override
String toString() {
  return 'CloseFriendsGroup(id: $id, name: $name, emoji: $emoji, memberIds: $memberIds, isStoryVisible: $isStoryVisible, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $CloseFriendsGroupCopyWith<$Res>  {
  factory $CloseFriendsGroupCopyWith(CloseFriendsGroup value, $Res Function(CloseFriendsGroup) _then) = _$CloseFriendsGroupCopyWithImpl;
@useResult
$Res call({
 String id, String name, String emoji, List<String> memberIds, bool isStoryVisible, DateTime createdAt, DateTime updatedAt
});




}
/// @nodoc
class _$CloseFriendsGroupCopyWithImpl<$Res>
    implements $CloseFriendsGroupCopyWith<$Res> {
  _$CloseFriendsGroupCopyWithImpl(this._self, this._then);

  final CloseFriendsGroup _self;
  final $Res Function(CloseFriendsGroup) _then;

/// Create a copy of CloseFriendsGroup
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? emoji = null,Object? memberIds = null,Object? isStoryVisible = null,Object? createdAt = null,Object? updatedAt = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,emoji: null == emoji ? _self.emoji : emoji // ignore: cast_nullable_to_non_nullable
as String,memberIds: null == memberIds ? _self.memberIds : memberIds // ignore: cast_nullable_to_non_nullable
as List<String>,isStoryVisible: null == isStoryVisible ? _self.isStoryVisible : isStoryVisible // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// Adds pattern-matching-related methods to [CloseFriendsGroup].
extension CloseFriendsGroupPatterns on CloseFriendsGroup {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CloseFriendsGroup value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CloseFriendsGroup() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CloseFriendsGroup value)  $default,){
final _that = this;
switch (_that) {
case _CloseFriendsGroup():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CloseFriendsGroup value)?  $default,){
final _that = this;
switch (_that) {
case _CloseFriendsGroup() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  String emoji,  List<String> memberIds,  bool isStoryVisible,  DateTime createdAt,  DateTime updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CloseFriendsGroup() when $default != null:
return $default(_that.id,_that.name,_that.emoji,_that.memberIds,_that.isStoryVisible,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  String emoji,  List<String> memberIds,  bool isStoryVisible,  DateTime createdAt,  DateTime updatedAt)  $default,) {final _that = this;
switch (_that) {
case _CloseFriendsGroup():
return $default(_that.id,_that.name,_that.emoji,_that.memberIds,_that.isStoryVisible,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  String emoji,  List<String> memberIds,  bool isStoryVisible,  DateTime createdAt,  DateTime updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _CloseFriendsGroup() when $default != null:
return $default(_that.id,_that.name,_that.emoji,_that.memberIds,_that.isStoryVisible,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _CloseFriendsGroup implements CloseFriendsGroup {
  const _CloseFriendsGroup({required this.id, required this.name, required this.emoji, required final  List<String> memberIds, required this.isStoryVisible, required this.createdAt, required this.updatedAt}): _memberIds = memberIds;
  factory _CloseFriendsGroup.fromJson(Map<String, dynamic> json) => _$CloseFriendsGroupFromJson(json);

@override final  String id;
@override final  String name;
@override final  String emoji;
 final  List<String> _memberIds;
@override List<String> get memberIds {
  if (_memberIds is EqualUnmodifiableListView) return _memberIds;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_memberIds);
}

@override final  bool isStoryVisible;
@override final  DateTime createdAt;
@override final  DateTime updatedAt;

/// Create a copy of CloseFriendsGroup
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CloseFriendsGroupCopyWith<_CloseFriendsGroup> get copyWith => __$CloseFriendsGroupCopyWithImpl<_CloseFriendsGroup>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CloseFriendsGroupToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CloseFriendsGroup&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.emoji, emoji) || other.emoji == emoji)&&const DeepCollectionEquality().equals(other._memberIds, _memberIds)&&(identical(other.isStoryVisible, isStoryVisible) || other.isStoryVisible == isStoryVisible)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,emoji,const DeepCollectionEquality().hash(_memberIds),isStoryVisible,createdAt,updatedAt);

@override
String toString() {
  return 'CloseFriendsGroup(id: $id, name: $name, emoji: $emoji, memberIds: $memberIds, isStoryVisible: $isStoryVisible, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$CloseFriendsGroupCopyWith<$Res> implements $CloseFriendsGroupCopyWith<$Res> {
  factory _$CloseFriendsGroupCopyWith(_CloseFriendsGroup value, $Res Function(_CloseFriendsGroup) _then) = __$CloseFriendsGroupCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, String emoji, List<String> memberIds, bool isStoryVisible, DateTime createdAt, DateTime updatedAt
});




}
/// @nodoc
class __$CloseFriendsGroupCopyWithImpl<$Res>
    implements _$CloseFriendsGroupCopyWith<$Res> {
  __$CloseFriendsGroupCopyWithImpl(this._self, this._then);

  final _CloseFriendsGroup _self;
  final $Res Function(_CloseFriendsGroup) _then;

/// Create a copy of CloseFriendsGroup
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? emoji = null,Object? memberIds = null,Object? isStoryVisible = null,Object? createdAt = null,Object? updatedAt = null,}) {
  return _then(_CloseFriendsGroup(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,emoji: null == emoji ? _self.emoji : emoji // ignore: cast_nullable_to_non_nullable
as String,memberIds: null == memberIds ? _self._memberIds : memberIds // ignore: cast_nullable_to_non_nullable
as List<String>,isStoryVisible: null == isStoryVisible ? _self.isStoryVisible : isStoryVisible // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}

// dart format on
