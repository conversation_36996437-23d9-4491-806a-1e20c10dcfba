import 'package:freezed_annotation/freezed_annotation.dart';

part 'profile_verification_model.freezed.dart';
part 'profile_verification_model.g.dart';

@freezed
abstract class ProfileVerificationModel with _$ProfileVerificationModel {
  const factory ProfileVerificationModel({
    required String id,
    required String userId,
    required VerificationType type,
    required VerificationStatus status,
    required DateTime createdAt,
    DateTime? completedAt,
    DateTime? expiresAt,
    String? verificationCode,
    String? documentUrl,
    String? documentType,
    String? verificationNotes,
    String? verifiedBy,
    Map<String, dynamic>? metadata,
  }) = _ProfileVerificationModel;

  factory ProfileVerificationModel.fromJson(Map<String, dynamic> json) =>
      _$ProfileVerificationModelFromJson(json);
}

@freezed
abstract class EmailVerificationModel with _$EmailVerificationModel {
  const factory EmailVerificationModel({
    required String email,
    required String verificationCode,
    required DateTime expiresAt,
    required bool isVerified,
    DateTime? verifiedAt,
  }) = _EmailVerificationModel;

  factory EmailVerificationModel.fromJson(Map<String, dynamic> json) =>
      _$EmailVerificationModelFromJson(json);
}

@freezed
abstract class PhoneVerificationModel with _$PhoneVerificationModel {
  const factory PhoneVerificationModel({
    required String phone,
    required String verificationCode,
    required DateTime expiresAt,
    required bool isVerified,
    DateTime? verifiedAt,
  }) = _PhoneVerificationModel;

  factory PhoneVerificationModel.fromJson(Map<String, dynamic> json) =>
      _$PhoneVerificationModelFromJson(json);
}

@freezed
abstract class IdentityVerificationModel with _$IdentityVerificationModel {
  const factory IdentityVerificationModel({
    required String id,
    required String userId,
    required String documentType,
    required String documentUrl,
    required String fullName,
    required DateTime dateOfBirth,
    required String nationality,
    required VerificationStatus status,
    required DateTime submittedAt,
    DateTime? reviewedAt,
    String? reviewedBy,
    String? rejectionReason,
    Map<String, dynamic>? documentData,
  }) = _IdentityVerificationModel;

  factory IdentityVerificationModel.fromJson(Map<String, dynamic> json) =>
      _$IdentityVerificationModelFromJson(json);
}

@freezed
abstract class BusinessVerificationModel with _$BusinessVerificationModel {
  const factory BusinessVerificationModel({
    required String id,
    required String userId,
    required String businessName,
    required String businessRegistrationNumber,
    required String businessAddress,
    required String businessCategory,
    required String documentUrl,
    required String contactEmail,
    required String contactPhone,
    required VerificationStatus status,
    required DateTime submittedAt,
    DateTime? reviewedAt,
    String? reviewedBy,
    String? rejectionReason,
    String? verifiedBusinessName,
    String? verifiedBusinessLogo,
    Map<String, dynamic>? businessData,
  }) = _BusinessVerificationModel;

  factory BusinessVerificationModel.fromJson(Map<String, dynamic> json) =>
      _$BusinessVerificationModelFromJson(json);
}

enum VerificationType {
  email,
  phone,
  identity,
  business,
  celebrity,
  billionaire,
}

enum VerificationStatus {
  pending,
  submitted,
  underReview,
  approved,
  rejected,
  expired,
}

enum DocumentType {
  passport,
  nationalId,
  driversLicense,
  businessLicense,
  taxCertificate,
  bankStatement,
  utilityBill,
  other,
}
