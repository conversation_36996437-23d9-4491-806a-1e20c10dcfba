import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_profile_model.freezed.dart';
part 'user_profile_model.g.dart';

@freezed
abstract class UserProfile with _$UserProfile {
  const factory UserProfile({
    required String id,
    required String email,
    required String name,
    String? profileImageUrl,
    String? bio,
    @Default([]) List<String> interests,
    @Default(0) int followerCount,
    @Default(0) int followingCount,
    @Default(false) bool isVerified,
    @Default(false) bool isBillionaire,
  }) = _UserProfile;

  factory UserProfile.fromFirestore(
    DocumentSnapshot<Map<String, dynamic>> snapshot,
    SnapshotOptions? options,
  ) {
    final data = snapshot.data()!;

    // Map Firestore fields to model fields
    final mappedData = <String, dynamic>{
      'id': snapshot.id,
      'email': data['email'] ?? '',
      'name': data['name'] ?? '',
      'profileImageUrl':
          data['profilePictureUrl'] ?? '', // Map Firestore field to model field
      'bio': data['bio'] ?? '',
      'followerCount': data['followerCount'] ?? 0,
      'followingCount': data['followingCount'] ?? 0,
      'isVerified': data['isVerified'] ?? false,
      'isBillionaire': data['isBillionaire'] ?? false,
      'interests': <String>[], // Default empty list
    };

    return UserProfile.fromJson(mappedData);
  }

  factory UserProfile.fromJson(Map<String, dynamic> json) =>
      _$UserProfileFromJson(json);
}
