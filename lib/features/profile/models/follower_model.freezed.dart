// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'follower_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$FollowerModel {

 String get id; String get username; String get name; String get profilePictureUrl; String? get bio; bool get isVerified; AccountType get accountType; Gender get gender; bool get isFollowingBack; bool get isMutual; DateTime get followedAt; bool get isMuted; bool get isSelected;// For bulk actions
 List<String> get tags;
/// Create a copy of FollowerModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$FollowerModelCopyWith<FollowerModel> get copyWith => _$FollowerModelCopyWithImpl<FollowerModel>(this as FollowerModel, _$identity);

  /// Serializes this FollowerModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is FollowerModel&&(identical(other.id, id) || other.id == id)&&(identical(other.username, username) || other.username == username)&&(identical(other.name, name) || other.name == name)&&(identical(other.profilePictureUrl, profilePictureUrl) || other.profilePictureUrl == profilePictureUrl)&&(identical(other.bio, bio) || other.bio == bio)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.accountType, accountType) || other.accountType == accountType)&&(identical(other.gender, gender) || other.gender == gender)&&(identical(other.isFollowingBack, isFollowingBack) || other.isFollowingBack == isFollowingBack)&&(identical(other.isMutual, isMutual) || other.isMutual == isMutual)&&(identical(other.followedAt, followedAt) || other.followedAt == followedAt)&&(identical(other.isMuted, isMuted) || other.isMuted == isMuted)&&(identical(other.isSelected, isSelected) || other.isSelected == isSelected)&&const DeepCollectionEquality().equals(other.tags, tags));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,username,name,profilePictureUrl,bio,isVerified,accountType,gender,isFollowingBack,isMutual,followedAt,isMuted,isSelected,const DeepCollectionEquality().hash(tags));

@override
String toString() {
  return 'FollowerModel(id: $id, username: $username, name: $name, profilePictureUrl: $profilePictureUrl, bio: $bio, isVerified: $isVerified, accountType: $accountType, gender: $gender, isFollowingBack: $isFollowingBack, isMutual: $isMutual, followedAt: $followedAt, isMuted: $isMuted, isSelected: $isSelected, tags: $tags)';
}


}

/// @nodoc
abstract mixin class $FollowerModelCopyWith<$Res>  {
  factory $FollowerModelCopyWith(FollowerModel value, $Res Function(FollowerModel) _then) = _$FollowerModelCopyWithImpl;
@useResult
$Res call({
 String id, String username, String name, String profilePictureUrl, String? bio, bool isVerified, AccountType accountType, Gender gender, bool isFollowingBack, bool isMutual, DateTime followedAt, bool isMuted, bool isSelected, List<String> tags
});




}
/// @nodoc
class _$FollowerModelCopyWithImpl<$Res>
    implements $FollowerModelCopyWith<$Res> {
  _$FollowerModelCopyWithImpl(this._self, this._then);

  final FollowerModel _self;
  final $Res Function(FollowerModel) _then;

/// Create a copy of FollowerModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? username = null,Object? name = null,Object? profilePictureUrl = null,Object? bio = freezed,Object? isVerified = null,Object? accountType = null,Object? gender = null,Object? isFollowingBack = null,Object? isMutual = null,Object? followedAt = null,Object? isMuted = null,Object? isSelected = null,Object? tags = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,profilePictureUrl: null == profilePictureUrl ? _self.profilePictureUrl : profilePictureUrl // ignore: cast_nullable_to_non_nullable
as String,bio: freezed == bio ? _self.bio : bio // ignore: cast_nullable_to_non_nullable
as String?,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,accountType: null == accountType ? _self.accountType : accountType // ignore: cast_nullable_to_non_nullable
as AccountType,gender: null == gender ? _self.gender : gender // ignore: cast_nullable_to_non_nullable
as Gender,isFollowingBack: null == isFollowingBack ? _self.isFollowingBack : isFollowingBack // ignore: cast_nullable_to_non_nullable
as bool,isMutual: null == isMutual ? _self.isMutual : isMutual // ignore: cast_nullable_to_non_nullable
as bool,followedAt: null == followedAt ? _self.followedAt : followedAt // ignore: cast_nullable_to_non_nullable
as DateTime,isMuted: null == isMuted ? _self.isMuted : isMuted // ignore: cast_nullable_to_non_nullable
as bool,isSelected: null == isSelected ? _self.isSelected : isSelected // ignore: cast_nullable_to_non_nullable
as bool,tags: null == tags ? _self.tags : tags // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}

}


/// Adds pattern-matching-related methods to [FollowerModel].
extension FollowerModelPatterns on FollowerModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _FollowerModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _FollowerModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _FollowerModel value)  $default,){
final _that = this;
switch (_that) {
case _FollowerModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _FollowerModel value)?  $default,){
final _that = this;
switch (_that) {
case _FollowerModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String username,  String name,  String profilePictureUrl,  String? bio,  bool isVerified,  AccountType accountType,  Gender gender,  bool isFollowingBack,  bool isMutual,  DateTime followedAt,  bool isMuted,  bool isSelected,  List<String> tags)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _FollowerModel() when $default != null:
return $default(_that.id,_that.username,_that.name,_that.profilePictureUrl,_that.bio,_that.isVerified,_that.accountType,_that.gender,_that.isFollowingBack,_that.isMutual,_that.followedAt,_that.isMuted,_that.isSelected,_that.tags);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String username,  String name,  String profilePictureUrl,  String? bio,  bool isVerified,  AccountType accountType,  Gender gender,  bool isFollowingBack,  bool isMutual,  DateTime followedAt,  bool isMuted,  bool isSelected,  List<String> tags)  $default,) {final _that = this;
switch (_that) {
case _FollowerModel():
return $default(_that.id,_that.username,_that.name,_that.profilePictureUrl,_that.bio,_that.isVerified,_that.accountType,_that.gender,_that.isFollowingBack,_that.isMutual,_that.followedAt,_that.isMuted,_that.isSelected,_that.tags);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String username,  String name,  String profilePictureUrl,  String? bio,  bool isVerified,  AccountType accountType,  Gender gender,  bool isFollowingBack,  bool isMutual,  DateTime followedAt,  bool isMuted,  bool isSelected,  List<String> tags)?  $default,) {final _that = this;
switch (_that) {
case _FollowerModel() when $default != null:
return $default(_that.id,_that.username,_that.name,_that.profilePictureUrl,_that.bio,_that.isVerified,_that.accountType,_that.gender,_that.isFollowingBack,_that.isMutual,_that.followedAt,_that.isMuted,_that.isSelected,_that.tags);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _FollowerModel implements FollowerModel {
  const _FollowerModel({required this.id, required this.username, required this.name, required this.profilePictureUrl, required this.bio, required this.isVerified, required this.accountType, required this.gender, required this.isFollowingBack, required this.isMutual, required this.followedAt, required this.isMuted, required this.isSelected, required final  List<String> tags}): _tags = tags;
  factory _FollowerModel.fromJson(Map<String, dynamic> json) => _$FollowerModelFromJson(json);

@override final  String id;
@override final  String username;
@override final  String name;
@override final  String profilePictureUrl;
@override final  String? bio;
@override final  bool isVerified;
@override final  AccountType accountType;
@override final  Gender gender;
@override final  bool isFollowingBack;
@override final  bool isMutual;
@override final  DateTime followedAt;
@override final  bool isMuted;
@override final  bool isSelected;
// For bulk actions
 final  List<String> _tags;
// For bulk actions
@override List<String> get tags {
  if (_tags is EqualUnmodifiableListView) return _tags;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_tags);
}


/// Create a copy of FollowerModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$FollowerModelCopyWith<_FollowerModel> get copyWith => __$FollowerModelCopyWithImpl<_FollowerModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$FollowerModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _FollowerModel&&(identical(other.id, id) || other.id == id)&&(identical(other.username, username) || other.username == username)&&(identical(other.name, name) || other.name == name)&&(identical(other.profilePictureUrl, profilePictureUrl) || other.profilePictureUrl == profilePictureUrl)&&(identical(other.bio, bio) || other.bio == bio)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.accountType, accountType) || other.accountType == accountType)&&(identical(other.gender, gender) || other.gender == gender)&&(identical(other.isFollowingBack, isFollowingBack) || other.isFollowingBack == isFollowingBack)&&(identical(other.isMutual, isMutual) || other.isMutual == isMutual)&&(identical(other.followedAt, followedAt) || other.followedAt == followedAt)&&(identical(other.isMuted, isMuted) || other.isMuted == isMuted)&&(identical(other.isSelected, isSelected) || other.isSelected == isSelected)&&const DeepCollectionEquality().equals(other._tags, _tags));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,username,name,profilePictureUrl,bio,isVerified,accountType,gender,isFollowingBack,isMutual,followedAt,isMuted,isSelected,const DeepCollectionEquality().hash(_tags));

@override
String toString() {
  return 'FollowerModel(id: $id, username: $username, name: $name, profilePictureUrl: $profilePictureUrl, bio: $bio, isVerified: $isVerified, accountType: $accountType, gender: $gender, isFollowingBack: $isFollowingBack, isMutual: $isMutual, followedAt: $followedAt, isMuted: $isMuted, isSelected: $isSelected, tags: $tags)';
}


}

/// @nodoc
abstract mixin class _$FollowerModelCopyWith<$Res> implements $FollowerModelCopyWith<$Res> {
  factory _$FollowerModelCopyWith(_FollowerModel value, $Res Function(_FollowerModel) _then) = __$FollowerModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String username, String name, String profilePictureUrl, String? bio, bool isVerified, AccountType accountType, Gender gender, bool isFollowingBack, bool isMutual, DateTime followedAt, bool isMuted, bool isSelected, List<String> tags
});




}
/// @nodoc
class __$FollowerModelCopyWithImpl<$Res>
    implements _$FollowerModelCopyWith<$Res> {
  __$FollowerModelCopyWithImpl(this._self, this._then);

  final _FollowerModel _self;
  final $Res Function(_FollowerModel) _then;

/// Create a copy of FollowerModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? username = null,Object? name = null,Object? profilePictureUrl = null,Object? bio = freezed,Object? isVerified = null,Object? accountType = null,Object? gender = null,Object? isFollowingBack = null,Object? isMutual = null,Object? followedAt = null,Object? isMuted = null,Object? isSelected = null,Object? tags = null,}) {
  return _then(_FollowerModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,profilePictureUrl: null == profilePictureUrl ? _self.profilePictureUrl : profilePictureUrl // ignore: cast_nullable_to_non_nullable
as String,bio: freezed == bio ? _self.bio : bio // ignore: cast_nullable_to_non_nullable
as String?,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,accountType: null == accountType ? _self.accountType : accountType // ignore: cast_nullable_to_non_nullable
as AccountType,gender: null == gender ? _self.gender : gender // ignore: cast_nullable_to_non_nullable
as Gender,isFollowingBack: null == isFollowingBack ? _self.isFollowingBack : isFollowingBack // ignore: cast_nullable_to_non_nullable
as bool,isMutual: null == isMutual ? _self.isMutual : isMutual // ignore: cast_nullable_to_non_nullable
as bool,followedAt: null == followedAt ? _self.followedAt : followedAt // ignore: cast_nullable_to_non_nullable
as DateTime,isMuted: null == isMuted ? _self.isMuted : isMuted // ignore: cast_nullable_to_non_nullable
as bool,isSelected: null == isSelected ? _self.isSelected : isSelected // ignore: cast_nullable_to_non_nullable
as bool,tags: null == tags ? _self._tags : tags // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}


}


/// @nodoc
mixin _$FollowingModel {

 String get id; String get username; String get name; String get profilePictureUrl; String? get bio; bool get isVerified; AccountType get accountType; Gender get gender; bool get isFollowingBack; bool get isMutual; DateTime get followedAt; bool get isMuted; bool get isSelected;// For bulk actions
 List<String> get tags;
/// Create a copy of FollowingModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$FollowingModelCopyWith<FollowingModel> get copyWith => _$FollowingModelCopyWithImpl<FollowingModel>(this as FollowingModel, _$identity);

  /// Serializes this FollowingModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is FollowingModel&&(identical(other.id, id) || other.id == id)&&(identical(other.username, username) || other.username == username)&&(identical(other.name, name) || other.name == name)&&(identical(other.profilePictureUrl, profilePictureUrl) || other.profilePictureUrl == profilePictureUrl)&&(identical(other.bio, bio) || other.bio == bio)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.accountType, accountType) || other.accountType == accountType)&&(identical(other.gender, gender) || other.gender == gender)&&(identical(other.isFollowingBack, isFollowingBack) || other.isFollowingBack == isFollowingBack)&&(identical(other.isMutual, isMutual) || other.isMutual == isMutual)&&(identical(other.followedAt, followedAt) || other.followedAt == followedAt)&&(identical(other.isMuted, isMuted) || other.isMuted == isMuted)&&(identical(other.isSelected, isSelected) || other.isSelected == isSelected)&&const DeepCollectionEquality().equals(other.tags, tags));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,username,name,profilePictureUrl,bio,isVerified,accountType,gender,isFollowingBack,isMutual,followedAt,isMuted,isSelected,const DeepCollectionEquality().hash(tags));

@override
String toString() {
  return 'FollowingModel(id: $id, username: $username, name: $name, profilePictureUrl: $profilePictureUrl, bio: $bio, isVerified: $isVerified, accountType: $accountType, gender: $gender, isFollowingBack: $isFollowingBack, isMutual: $isMutual, followedAt: $followedAt, isMuted: $isMuted, isSelected: $isSelected, tags: $tags)';
}


}

/// @nodoc
abstract mixin class $FollowingModelCopyWith<$Res>  {
  factory $FollowingModelCopyWith(FollowingModel value, $Res Function(FollowingModel) _then) = _$FollowingModelCopyWithImpl;
@useResult
$Res call({
 String id, String username, String name, String profilePictureUrl, String? bio, bool isVerified, AccountType accountType, Gender gender, bool isFollowingBack, bool isMutual, DateTime followedAt, bool isMuted, bool isSelected, List<String> tags
});




}
/// @nodoc
class _$FollowingModelCopyWithImpl<$Res>
    implements $FollowingModelCopyWith<$Res> {
  _$FollowingModelCopyWithImpl(this._self, this._then);

  final FollowingModel _self;
  final $Res Function(FollowingModel) _then;

/// Create a copy of FollowingModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? username = null,Object? name = null,Object? profilePictureUrl = null,Object? bio = freezed,Object? isVerified = null,Object? accountType = null,Object? gender = null,Object? isFollowingBack = null,Object? isMutual = null,Object? followedAt = null,Object? isMuted = null,Object? isSelected = null,Object? tags = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,profilePictureUrl: null == profilePictureUrl ? _self.profilePictureUrl : profilePictureUrl // ignore: cast_nullable_to_non_nullable
as String,bio: freezed == bio ? _self.bio : bio // ignore: cast_nullable_to_non_nullable
as String?,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,accountType: null == accountType ? _self.accountType : accountType // ignore: cast_nullable_to_non_nullable
as AccountType,gender: null == gender ? _self.gender : gender // ignore: cast_nullable_to_non_nullable
as Gender,isFollowingBack: null == isFollowingBack ? _self.isFollowingBack : isFollowingBack // ignore: cast_nullable_to_non_nullable
as bool,isMutual: null == isMutual ? _self.isMutual : isMutual // ignore: cast_nullable_to_non_nullable
as bool,followedAt: null == followedAt ? _self.followedAt : followedAt // ignore: cast_nullable_to_non_nullable
as DateTime,isMuted: null == isMuted ? _self.isMuted : isMuted // ignore: cast_nullable_to_non_nullable
as bool,isSelected: null == isSelected ? _self.isSelected : isSelected // ignore: cast_nullable_to_non_nullable
as bool,tags: null == tags ? _self.tags : tags // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}

}


/// Adds pattern-matching-related methods to [FollowingModel].
extension FollowingModelPatterns on FollowingModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _FollowingModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _FollowingModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _FollowingModel value)  $default,){
final _that = this;
switch (_that) {
case _FollowingModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _FollowingModel value)?  $default,){
final _that = this;
switch (_that) {
case _FollowingModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String username,  String name,  String profilePictureUrl,  String? bio,  bool isVerified,  AccountType accountType,  Gender gender,  bool isFollowingBack,  bool isMutual,  DateTime followedAt,  bool isMuted,  bool isSelected,  List<String> tags)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _FollowingModel() when $default != null:
return $default(_that.id,_that.username,_that.name,_that.profilePictureUrl,_that.bio,_that.isVerified,_that.accountType,_that.gender,_that.isFollowingBack,_that.isMutual,_that.followedAt,_that.isMuted,_that.isSelected,_that.tags);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String username,  String name,  String profilePictureUrl,  String? bio,  bool isVerified,  AccountType accountType,  Gender gender,  bool isFollowingBack,  bool isMutual,  DateTime followedAt,  bool isMuted,  bool isSelected,  List<String> tags)  $default,) {final _that = this;
switch (_that) {
case _FollowingModel():
return $default(_that.id,_that.username,_that.name,_that.profilePictureUrl,_that.bio,_that.isVerified,_that.accountType,_that.gender,_that.isFollowingBack,_that.isMutual,_that.followedAt,_that.isMuted,_that.isSelected,_that.tags);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String username,  String name,  String profilePictureUrl,  String? bio,  bool isVerified,  AccountType accountType,  Gender gender,  bool isFollowingBack,  bool isMutual,  DateTime followedAt,  bool isMuted,  bool isSelected,  List<String> tags)?  $default,) {final _that = this;
switch (_that) {
case _FollowingModel() when $default != null:
return $default(_that.id,_that.username,_that.name,_that.profilePictureUrl,_that.bio,_that.isVerified,_that.accountType,_that.gender,_that.isFollowingBack,_that.isMutual,_that.followedAt,_that.isMuted,_that.isSelected,_that.tags);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _FollowingModel implements FollowingModel {
  const _FollowingModel({required this.id, required this.username, required this.name, required this.profilePictureUrl, required this.bio, required this.isVerified, required this.accountType, required this.gender, required this.isFollowingBack, required this.isMutual, required this.followedAt, required this.isMuted, required this.isSelected, required final  List<String> tags}): _tags = tags;
  factory _FollowingModel.fromJson(Map<String, dynamic> json) => _$FollowingModelFromJson(json);

@override final  String id;
@override final  String username;
@override final  String name;
@override final  String profilePictureUrl;
@override final  String? bio;
@override final  bool isVerified;
@override final  AccountType accountType;
@override final  Gender gender;
@override final  bool isFollowingBack;
@override final  bool isMutual;
@override final  DateTime followedAt;
@override final  bool isMuted;
@override final  bool isSelected;
// For bulk actions
 final  List<String> _tags;
// For bulk actions
@override List<String> get tags {
  if (_tags is EqualUnmodifiableListView) return _tags;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_tags);
}


/// Create a copy of FollowingModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$FollowingModelCopyWith<_FollowingModel> get copyWith => __$FollowingModelCopyWithImpl<_FollowingModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$FollowingModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _FollowingModel&&(identical(other.id, id) || other.id == id)&&(identical(other.username, username) || other.username == username)&&(identical(other.name, name) || other.name == name)&&(identical(other.profilePictureUrl, profilePictureUrl) || other.profilePictureUrl == profilePictureUrl)&&(identical(other.bio, bio) || other.bio == bio)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.accountType, accountType) || other.accountType == accountType)&&(identical(other.gender, gender) || other.gender == gender)&&(identical(other.isFollowingBack, isFollowingBack) || other.isFollowingBack == isFollowingBack)&&(identical(other.isMutual, isMutual) || other.isMutual == isMutual)&&(identical(other.followedAt, followedAt) || other.followedAt == followedAt)&&(identical(other.isMuted, isMuted) || other.isMuted == isMuted)&&(identical(other.isSelected, isSelected) || other.isSelected == isSelected)&&const DeepCollectionEquality().equals(other._tags, _tags));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,username,name,profilePictureUrl,bio,isVerified,accountType,gender,isFollowingBack,isMutual,followedAt,isMuted,isSelected,const DeepCollectionEquality().hash(_tags));

@override
String toString() {
  return 'FollowingModel(id: $id, username: $username, name: $name, profilePictureUrl: $profilePictureUrl, bio: $bio, isVerified: $isVerified, accountType: $accountType, gender: $gender, isFollowingBack: $isFollowingBack, isMutual: $isMutual, followedAt: $followedAt, isMuted: $isMuted, isSelected: $isSelected, tags: $tags)';
}


}

/// @nodoc
abstract mixin class _$FollowingModelCopyWith<$Res> implements $FollowingModelCopyWith<$Res> {
  factory _$FollowingModelCopyWith(_FollowingModel value, $Res Function(_FollowingModel) _then) = __$FollowingModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String username, String name, String profilePictureUrl, String? bio, bool isVerified, AccountType accountType, Gender gender, bool isFollowingBack, bool isMutual, DateTime followedAt, bool isMuted, bool isSelected, List<String> tags
});




}
/// @nodoc
class __$FollowingModelCopyWithImpl<$Res>
    implements _$FollowingModelCopyWith<$Res> {
  __$FollowingModelCopyWithImpl(this._self, this._then);

  final _FollowingModel _self;
  final $Res Function(_FollowingModel) _then;

/// Create a copy of FollowingModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? username = null,Object? name = null,Object? profilePictureUrl = null,Object? bio = freezed,Object? isVerified = null,Object? accountType = null,Object? gender = null,Object? isFollowingBack = null,Object? isMutual = null,Object? followedAt = null,Object? isMuted = null,Object? isSelected = null,Object? tags = null,}) {
  return _then(_FollowingModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,profilePictureUrl: null == profilePictureUrl ? _self.profilePictureUrl : profilePictureUrl // ignore: cast_nullable_to_non_nullable
as String,bio: freezed == bio ? _self.bio : bio // ignore: cast_nullable_to_non_nullable
as String?,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,accountType: null == accountType ? _self.accountType : accountType // ignore: cast_nullable_to_non_nullable
as AccountType,gender: null == gender ? _self.gender : gender // ignore: cast_nullable_to_non_nullable
as Gender,isFollowingBack: null == isFollowingBack ? _self.isFollowingBack : isFollowingBack // ignore: cast_nullable_to_non_nullable
as bool,isMutual: null == isMutual ? _self.isMutual : isMutual // ignore: cast_nullable_to_non_nullable
as bool,followedAt: null == followedAt ? _self.followedAt : followedAt // ignore: cast_nullable_to_non_nullable
as DateTime,isMuted: null == isMuted ? _self.isMuted : isMuted // ignore: cast_nullable_to_non_nullable
as bool,isSelected: null == isSelected ? _self.isSelected : isSelected // ignore: cast_nullable_to_non_nullable
as bool,tags: null == tags ? _self._tags : tags // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}


}


/// @nodoc
mixin _$FollowersAnalytics {

 int get totalFollowers; int get totalFollowing; int get notFollowingBack; int get verifiedAccountsFollowed; int get billionairesFollowed; int get celebritiesFollowed; int get businessesFollowed; GenderSplit get genderSplit; AccountTypeSplit get accountTypeSplit; MutualStatusSplit get mutualStatusSplit;
/// Create a copy of FollowersAnalytics
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$FollowersAnalyticsCopyWith<FollowersAnalytics> get copyWith => _$FollowersAnalyticsCopyWithImpl<FollowersAnalytics>(this as FollowersAnalytics, _$identity);

  /// Serializes this FollowersAnalytics to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is FollowersAnalytics&&(identical(other.totalFollowers, totalFollowers) || other.totalFollowers == totalFollowers)&&(identical(other.totalFollowing, totalFollowing) || other.totalFollowing == totalFollowing)&&(identical(other.notFollowingBack, notFollowingBack) || other.notFollowingBack == notFollowingBack)&&(identical(other.verifiedAccountsFollowed, verifiedAccountsFollowed) || other.verifiedAccountsFollowed == verifiedAccountsFollowed)&&(identical(other.billionairesFollowed, billionairesFollowed) || other.billionairesFollowed == billionairesFollowed)&&(identical(other.celebritiesFollowed, celebritiesFollowed) || other.celebritiesFollowed == celebritiesFollowed)&&(identical(other.businessesFollowed, businessesFollowed) || other.businessesFollowed == businessesFollowed)&&(identical(other.genderSplit, genderSplit) || other.genderSplit == genderSplit)&&(identical(other.accountTypeSplit, accountTypeSplit) || other.accountTypeSplit == accountTypeSplit)&&(identical(other.mutualStatusSplit, mutualStatusSplit) || other.mutualStatusSplit == mutualStatusSplit));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalFollowers,totalFollowing,notFollowingBack,verifiedAccountsFollowed,billionairesFollowed,celebritiesFollowed,businessesFollowed,genderSplit,accountTypeSplit,mutualStatusSplit);

@override
String toString() {
  return 'FollowersAnalytics(totalFollowers: $totalFollowers, totalFollowing: $totalFollowing, notFollowingBack: $notFollowingBack, verifiedAccountsFollowed: $verifiedAccountsFollowed, billionairesFollowed: $billionairesFollowed, celebritiesFollowed: $celebritiesFollowed, businessesFollowed: $businessesFollowed, genderSplit: $genderSplit, accountTypeSplit: $accountTypeSplit, mutualStatusSplit: $mutualStatusSplit)';
}


}

/// @nodoc
abstract mixin class $FollowersAnalyticsCopyWith<$Res>  {
  factory $FollowersAnalyticsCopyWith(FollowersAnalytics value, $Res Function(FollowersAnalytics) _then) = _$FollowersAnalyticsCopyWithImpl;
@useResult
$Res call({
 int totalFollowers, int totalFollowing, int notFollowingBack, int verifiedAccountsFollowed, int billionairesFollowed, int celebritiesFollowed, int businessesFollowed, GenderSplit genderSplit, AccountTypeSplit accountTypeSplit, MutualStatusSplit mutualStatusSplit
});


$GenderSplitCopyWith<$Res> get genderSplit;$AccountTypeSplitCopyWith<$Res> get accountTypeSplit;$MutualStatusSplitCopyWith<$Res> get mutualStatusSplit;

}
/// @nodoc
class _$FollowersAnalyticsCopyWithImpl<$Res>
    implements $FollowersAnalyticsCopyWith<$Res> {
  _$FollowersAnalyticsCopyWithImpl(this._self, this._then);

  final FollowersAnalytics _self;
  final $Res Function(FollowersAnalytics) _then;

/// Create a copy of FollowersAnalytics
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? totalFollowers = null,Object? totalFollowing = null,Object? notFollowingBack = null,Object? verifiedAccountsFollowed = null,Object? billionairesFollowed = null,Object? celebritiesFollowed = null,Object? businessesFollowed = null,Object? genderSplit = null,Object? accountTypeSplit = null,Object? mutualStatusSplit = null,}) {
  return _then(_self.copyWith(
totalFollowers: null == totalFollowers ? _self.totalFollowers : totalFollowers // ignore: cast_nullable_to_non_nullable
as int,totalFollowing: null == totalFollowing ? _self.totalFollowing : totalFollowing // ignore: cast_nullable_to_non_nullable
as int,notFollowingBack: null == notFollowingBack ? _self.notFollowingBack : notFollowingBack // ignore: cast_nullable_to_non_nullable
as int,verifiedAccountsFollowed: null == verifiedAccountsFollowed ? _self.verifiedAccountsFollowed : verifiedAccountsFollowed // ignore: cast_nullable_to_non_nullable
as int,billionairesFollowed: null == billionairesFollowed ? _self.billionairesFollowed : billionairesFollowed // ignore: cast_nullable_to_non_nullable
as int,celebritiesFollowed: null == celebritiesFollowed ? _self.celebritiesFollowed : celebritiesFollowed // ignore: cast_nullable_to_non_nullable
as int,businessesFollowed: null == businessesFollowed ? _self.businessesFollowed : businessesFollowed // ignore: cast_nullable_to_non_nullable
as int,genderSplit: null == genderSplit ? _self.genderSplit : genderSplit // ignore: cast_nullable_to_non_nullable
as GenderSplit,accountTypeSplit: null == accountTypeSplit ? _self.accountTypeSplit : accountTypeSplit // ignore: cast_nullable_to_non_nullable
as AccountTypeSplit,mutualStatusSplit: null == mutualStatusSplit ? _self.mutualStatusSplit : mutualStatusSplit // ignore: cast_nullable_to_non_nullable
as MutualStatusSplit,
  ));
}
/// Create a copy of FollowersAnalytics
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$GenderSplitCopyWith<$Res> get genderSplit {
  
  return $GenderSplitCopyWith<$Res>(_self.genderSplit, (value) {
    return _then(_self.copyWith(genderSplit: value));
  });
}/// Create a copy of FollowersAnalytics
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountTypeSplitCopyWith<$Res> get accountTypeSplit {
  
  return $AccountTypeSplitCopyWith<$Res>(_self.accountTypeSplit, (value) {
    return _then(_self.copyWith(accountTypeSplit: value));
  });
}/// Create a copy of FollowersAnalytics
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MutualStatusSplitCopyWith<$Res> get mutualStatusSplit {
  
  return $MutualStatusSplitCopyWith<$Res>(_self.mutualStatusSplit, (value) {
    return _then(_self.copyWith(mutualStatusSplit: value));
  });
}
}


/// Adds pattern-matching-related methods to [FollowersAnalytics].
extension FollowersAnalyticsPatterns on FollowersAnalytics {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _FollowersAnalytics value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _FollowersAnalytics() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _FollowersAnalytics value)  $default,){
final _that = this;
switch (_that) {
case _FollowersAnalytics():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _FollowersAnalytics value)?  $default,){
final _that = this;
switch (_that) {
case _FollowersAnalytics() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int totalFollowers,  int totalFollowing,  int notFollowingBack,  int verifiedAccountsFollowed,  int billionairesFollowed,  int celebritiesFollowed,  int businessesFollowed,  GenderSplit genderSplit,  AccountTypeSplit accountTypeSplit,  MutualStatusSplit mutualStatusSplit)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _FollowersAnalytics() when $default != null:
return $default(_that.totalFollowers,_that.totalFollowing,_that.notFollowingBack,_that.verifiedAccountsFollowed,_that.billionairesFollowed,_that.celebritiesFollowed,_that.businessesFollowed,_that.genderSplit,_that.accountTypeSplit,_that.mutualStatusSplit);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int totalFollowers,  int totalFollowing,  int notFollowingBack,  int verifiedAccountsFollowed,  int billionairesFollowed,  int celebritiesFollowed,  int businessesFollowed,  GenderSplit genderSplit,  AccountTypeSplit accountTypeSplit,  MutualStatusSplit mutualStatusSplit)  $default,) {final _that = this;
switch (_that) {
case _FollowersAnalytics():
return $default(_that.totalFollowers,_that.totalFollowing,_that.notFollowingBack,_that.verifiedAccountsFollowed,_that.billionairesFollowed,_that.celebritiesFollowed,_that.businessesFollowed,_that.genderSplit,_that.accountTypeSplit,_that.mutualStatusSplit);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int totalFollowers,  int totalFollowing,  int notFollowingBack,  int verifiedAccountsFollowed,  int billionairesFollowed,  int celebritiesFollowed,  int businessesFollowed,  GenderSplit genderSplit,  AccountTypeSplit accountTypeSplit,  MutualStatusSplit mutualStatusSplit)?  $default,) {final _that = this;
switch (_that) {
case _FollowersAnalytics() when $default != null:
return $default(_that.totalFollowers,_that.totalFollowing,_that.notFollowingBack,_that.verifiedAccountsFollowed,_that.billionairesFollowed,_that.celebritiesFollowed,_that.businessesFollowed,_that.genderSplit,_that.accountTypeSplit,_that.mutualStatusSplit);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _FollowersAnalytics implements FollowersAnalytics {
  const _FollowersAnalytics({required this.totalFollowers, required this.totalFollowing, required this.notFollowingBack, required this.verifiedAccountsFollowed, required this.billionairesFollowed, required this.celebritiesFollowed, required this.businessesFollowed, required this.genderSplit, required this.accountTypeSplit, required this.mutualStatusSplit});
  factory _FollowersAnalytics.fromJson(Map<String, dynamic> json) => _$FollowersAnalyticsFromJson(json);

@override final  int totalFollowers;
@override final  int totalFollowing;
@override final  int notFollowingBack;
@override final  int verifiedAccountsFollowed;
@override final  int billionairesFollowed;
@override final  int celebritiesFollowed;
@override final  int businessesFollowed;
@override final  GenderSplit genderSplit;
@override final  AccountTypeSplit accountTypeSplit;
@override final  MutualStatusSplit mutualStatusSplit;

/// Create a copy of FollowersAnalytics
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$FollowersAnalyticsCopyWith<_FollowersAnalytics> get copyWith => __$FollowersAnalyticsCopyWithImpl<_FollowersAnalytics>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$FollowersAnalyticsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _FollowersAnalytics&&(identical(other.totalFollowers, totalFollowers) || other.totalFollowers == totalFollowers)&&(identical(other.totalFollowing, totalFollowing) || other.totalFollowing == totalFollowing)&&(identical(other.notFollowingBack, notFollowingBack) || other.notFollowingBack == notFollowingBack)&&(identical(other.verifiedAccountsFollowed, verifiedAccountsFollowed) || other.verifiedAccountsFollowed == verifiedAccountsFollowed)&&(identical(other.billionairesFollowed, billionairesFollowed) || other.billionairesFollowed == billionairesFollowed)&&(identical(other.celebritiesFollowed, celebritiesFollowed) || other.celebritiesFollowed == celebritiesFollowed)&&(identical(other.businessesFollowed, businessesFollowed) || other.businessesFollowed == businessesFollowed)&&(identical(other.genderSplit, genderSplit) || other.genderSplit == genderSplit)&&(identical(other.accountTypeSplit, accountTypeSplit) || other.accountTypeSplit == accountTypeSplit)&&(identical(other.mutualStatusSplit, mutualStatusSplit) || other.mutualStatusSplit == mutualStatusSplit));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalFollowers,totalFollowing,notFollowingBack,verifiedAccountsFollowed,billionairesFollowed,celebritiesFollowed,businessesFollowed,genderSplit,accountTypeSplit,mutualStatusSplit);

@override
String toString() {
  return 'FollowersAnalytics(totalFollowers: $totalFollowers, totalFollowing: $totalFollowing, notFollowingBack: $notFollowingBack, verifiedAccountsFollowed: $verifiedAccountsFollowed, billionairesFollowed: $billionairesFollowed, celebritiesFollowed: $celebritiesFollowed, businessesFollowed: $businessesFollowed, genderSplit: $genderSplit, accountTypeSplit: $accountTypeSplit, mutualStatusSplit: $mutualStatusSplit)';
}


}

/// @nodoc
abstract mixin class _$FollowersAnalyticsCopyWith<$Res> implements $FollowersAnalyticsCopyWith<$Res> {
  factory _$FollowersAnalyticsCopyWith(_FollowersAnalytics value, $Res Function(_FollowersAnalytics) _then) = __$FollowersAnalyticsCopyWithImpl;
@override @useResult
$Res call({
 int totalFollowers, int totalFollowing, int notFollowingBack, int verifiedAccountsFollowed, int billionairesFollowed, int celebritiesFollowed, int businessesFollowed, GenderSplit genderSplit, AccountTypeSplit accountTypeSplit, MutualStatusSplit mutualStatusSplit
});


@override $GenderSplitCopyWith<$Res> get genderSplit;@override $AccountTypeSplitCopyWith<$Res> get accountTypeSplit;@override $MutualStatusSplitCopyWith<$Res> get mutualStatusSplit;

}
/// @nodoc
class __$FollowersAnalyticsCopyWithImpl<$Res>
    implements _$FollowersAnalyticsCopyWith<$Res> {
  __$FollowersAnalyticsCopyWithImpl(this._self, this._then);

  final _FollowersAnalytics _self;
  final $Res Function(_FollowersAnalytics) _then;

/// Create a copy of FollowersAnalytics
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? totalFollowers = null,Object? totalFollowing = null,Object? notFollowingBack = null,Object? verifiedAccountsFollowed = null,Object? billionairesFollowed = null,Object? celebritiesFollowed = null,Object? businessesFollowed = null,Object? genderSplit = null,Object? accountTypeSplit = null,Object? mutualStatusSplit = null,}) {
  return _then(_FollowersAnalytics(
totalFollowers: null == totalFollowers ? _self.totalFollowers : totalFollowers // ignore: cast_nullable_to_non_nullable
as int,totalFollowing: null == totalFollowing ? _self.totalFollowing : totalFollowing // ignore: cast_nullable_to_non_nullable
as int,notFollowingBack: null == notFollowingBack ? _self.notFollowingBack : notFollowingBack // ignore: cast_nullable_to_non_nullable
as int,verifiedAccountsFollowed: null == verifiedAccountsFollowed ? _self.verifiedAccountsFollowed : verifiedAccountsFollowed // ignore: cast_nullable_to_non_nullable
as int,billionairesFollowed: null == billionairesFollowed ? _self.billionairesFollowed : billionairesFollowed // ignore: cast_nullable_to_non_nullable
as int,celebritiesFollowed: null == celebritiesFollowed ? _self.celebritiesFollowed : celebritiesFollowed // ignore: cast_nullable_to_non_nullable
as int,businessesFollowed: null == businessesFollowed ? _self.businessesFollowed : businessesFollowed // ignore: cast_nullable_to_non_nullable
as int,genderSplit: null == genderSplit ? _self.genderSplit : genderSplit // ignore: cast_nullable_to_non_nullable
as GenderSplit,accountTypeSplit: null == accountTypeSplit ? _self.accountTypeSplit : accountTypeSplit // ignore: cast_nullable_to_non_nullable
as AccountTypeSplit,mutualStatusSplit: null == mutualStatusSplit ? _self.mutualStatusSplit : mutualStatusSplit // ignore: cast_nullable_to_non_nullable
as MutualStatusSplit,
  ));
}

/// Create a copy of FollowersAnalytics
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$GenderSplitCopyWith<$Res> get genderSplit {
  
  return $GenderSplitCopyWith<$Res>(_self.genderSplit, (value) {
    return _then(_self.copyWith(genderSplit: value));
  });
}/// Create a copy of FollowersAnalytics
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountTypeSplitCopyWith<$Res> get accountTypeSplit {
  
  return $AccountTypeSplitCopyWith<$Res>(_self.accountTypeSplit, (value) {
    return _then(_self.copyWith(accountTypeSplit: value));
  });
}/// Create a copy of FollowersAnalytics
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MutualStatusSplitCopyWith<$Res> get mutualStatusSplit {
  
  return $MutualStatusSplitCopyWith<$Res>(_self.mutualStatusSplit, (value) {
    return _then(_self.copyWith(mutualStatusSplit: value));
  });
}
}


/// @nodoc
mixin _$GenderSplit {

 int get male; int get female; int get other; double get malePercentage; double get femalePercentage; double get otherPercentage;
/// Create a copy of GenderSplit
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$GenderSplitCopyWith<GenderSplit> get copyWith => _$GenderSplitCopyWithImpl<GenderSplit>(this as GenderSplit, _$identity);

  /// Serializes this GenderSplit to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is GenderSplit&&(identical(other.male, male) || other.male == male)&&(identical(other.female, female) || other.female == female)&&(identical(other.other, this.other) || other.other == this.other)&&(identical(other.malePercentage, malePercentage) || other.malePercentage == malePercentage)&&(identical(other.femalePercentage, femalePercentage) || other.femalePercentage == femalePercentage)&&(identical(other.otherPercentage, otherPercentage) || other.otherPercentage == otherPercentage));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,male,female,other,malePercentage,femalePercentage,otherPercentage);

@override
String toString() {
  return 'GenderSplit(male: $male, female: $female, other: $other, malePercentage: $malePercentage, femalePercentage: $femalePercentage, otherPercentage: $otherPercentage)';
}


}

/// @nodoc
abstract mixin class $GenderSplitCopyWith<$Res>  {
  factory $GenderSplitCopyWith(GenderSplit value, $Res Function(GenderSplit) _then) = _$GenderSplitCopyWithImpl;
@useResult
$Res call({
 int male, int female, int other, double malePercentage, double femalePercentage, double otherPercentage
});




}
/// @nodoc
class _$GenderSplitCopyWithImpl<$Res>
    implements $GenderSplitCopyWith<$Res> {
  _$GenderSplitCopyWithImpl(this._self, this._then);

  final GenderSplit _self;
  final $Res Function(GenderSplit) _then;

/// Create a copy of GenderSplit
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? male = null,Object? female = null,Object? other = null,Object? malePercentage = null,Object? femalePercentage = null,Object? otherPercentage = null,}) {
  return _then(_self.copyWith(
male: null == male ? _self.male : male // ignore: cast_nullable_to_non_nullable
as int,female: null == female ? _self.female : female // ignore: cast_nullable_to_non_nullable
as int,other: null == other ? _self.other : other // ignore: cast_nullable_to_non_nullable
as int,malePercentage: null == malePercentage ? _self.malePercentage : malePercentage // ignore: cast_nullable_to_non_nullable
as double,femalePercentage: null == femalePercentage ? _self.femalePercentage : femalePercentage // ignore: cast_nullable_to_non_nullable
as double,otherPercentage: null == otherPercentage ? _self.otherPercentage : otherPercentage // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [GenderSplit].
extension GenderSplitPatterns on GenderSplit {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _GenderSplit value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _GenderSplit() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _GenderSplit value)  $default,){
final _that = this;
switch (_that) {
case _GenderSplit():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _GenderSplit value)?  $default,){
final _that = this;
switch (_that) {
case _GenderSplit() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int male,  int female,  int other,  double malePercentage,  double femalePercentage,  double otherPercentage)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _GenderSplit() when $default != null:
return $default(_that.male,_that.female,_that.other,_that.malePercentage,_that.femalePercentage,_that.otherPercentage);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int male,  int female,  int other,  double malePercentage,  double femalePercentage,  double otherPercentage)  $default,) {final _that = this;
switch (_that) {
case _GenderSplit():
return $default(_that.male,_that.female,_that.other,_that.malePercentage,_that.femalePercentage,_that.otherPercentage);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int male,  int female,  int other,  double malePercentage,  double femalePercentage,  double otherPercentage)?  $default,) {final _that = this;
switch (_that) {
case _GenderSplit() when $default != null:
return $default(_that.male,_that.female,_that.other,_that.malePercentage,_that.femalePercentage,_that.otherPercentage);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _GenderSplit implements GenderSplit {
  const _GenderSplit({required this.male, required this.female, required this.other, required this.malePercentage, required this.femalePercentage, required this.otherPercentage});
  factory _GenderSplit.fromJson(Map<String, dynamic> json) => _$GenderSplitFromJson(json);

@override final  int male;
@override final  int female;
@override final  int other;
@override final  double malePercentage;
@override final  double femalePercentage;
@override final  double otherPercentage;

/// Create a copy of GenderSplit
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$GenderSplitCopyWith<_GenderSplit> get copyWith => __$GenderSplitCopyWithImpl<_GenderSplit>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$GenderSplitToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GenderSplit&&(identical(other.male, male) || other.male == male)&&(identical(other.female, female) || other.female == female)&&(identical(other.other, this.other) || other.other == this.other)&&(identical(other.malePercentage, malePercentage) || other.malePercentage == malePercentage)&&(identical(other.femalePercentage, femalePercentage) || other.femalePercentage == femalePercentage)&&(identical(other.otherPercentage, otherPercentage) || other.otherPercentage == otherPercentage));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,male,female,other,malePercentage,femalePercentage,otherPercentage);

@override
String toString() {
  return 'GenderSplit(male: $male, female: $female, other: $other, malePercentage: $malePercentage, femalePercentage: $femalePercentage, otherPercentage: $otherPercentage)';
}


}

/// @nodoc
abstract mixin class _$GenderSplitCopyWith<$Res> implements $GenderSplitCopyWith<$Res> {
  factory _$GenderSplitCopyWith(_GenderSplit value, $Res Function(_GenderSplit) _then) = __$GenderSplitCopyWithImpl;
@override @useResult
$Res call({
 int male, int female, int other, double malePercentage, double femalePercentage, double otherPercentage
});




}
/// @nodoc
class __$GenderSplitCopyWithImpl<$Res>
    implements _$GenderSplitCopyWith<$Res> {
  __$GenderSplitCopyWithImpl(this._self, this._then);

  final _GenderSplit _self;
  final $Res Function(_GenderSplit) _then;

/// Create a copy of GenderSplit
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? male = null,Object? female = null,Object? other = null,Object? malePercentage = null,Object? femalePercentage = null,Object? otherPercentage = null,}) {
  return _then(_GenderSplit(
male: null == male ? _self.male : male // ignore: cast_nullable_to_non_nullable
as int,female: null == female ? _self.female : female // ignore: cast_nullable_to_non_nullable
as int,other: null == other ? _self.other : other // ignore: cast_nullable_to_non_nullable
as int,malePercentage: null == malePercentage ? _self.malePercentage : malePercentage // ignore: cast_nullable_to_non_nullable
as double,femalePercentage: null == femalePercentage ? _self.femalePercentage : femalePercentage // ignore: cast_nullable_to_non_nullable
as double,otherPercentage: null == otherPercentage ? _self.otherPercentage : otherPercentage // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}


/// @nodoc
mixin _$AccountTypeSplit {

 int get verified; int get celebrities; int get businesses; int get billionaires; int get regular;
/// Create a copy of AccountTypeSplit
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AccountTypeSplitCopyWith<AccountTypeSplit> get copyWith => _$AccountTypeSplitCopyWithImpl<AccountTypeSplit>(this as AccountTypeSplit, _$identity);

  /// Serializes this AccountTypeSplit to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountTypeSplit&&(identical(other.verified, verified) || other.verified == verified)&&(identical(other.celebrities, celebrities) || other.celebrities == celebrities)&&(identical(other.businesses, businesses) || other.businesses == businesses)&&(identical(other.billionaires, billionaires) || other.billionaires == billionaires)&&(identical(other.regular, regular) || other.regular == regular));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,verified,celebrities,businesses,billionaires,regular);

@override
String toString() {
  return 'AccountTypeSplit(verified: $verified, celebrities: $celebrities, businesses: $businesses, billionaires: $billionaires, regular: $regular)';
}


}

/// @nodoc
abstract mixin class $AccountTypeSplitCopyWith<$Res>  {
  factory $AccountTypeSplitCopyWith(AccountTypeSplit value, $Res Function(AccountTypeSplit) _then) = _$AccountTypeSplitCopyWithImpl;
@useResult
$Res call({
 int verified, int celebrities, int businesses, int billionaires, int regular
});




}
/// @nodoc
class _$AccountTypeSplitCopyWithImpl<$Res>
    implements $AccountTypeSplitCopyWith<$Res> {
  _$AccountTypeSplitCopyWithImpl(this._self, this._then);

  final AccountTypeSplit _self;
  final $Res Function(AccountTypeSplit) _then;

/// Create a copy of AccountTypeSplit
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? verified = null,Object? celebrities = null,Object? businesses = null,Object? billionaires = null,Object? regular = null,}) {
  return _then(_self.copyWith(
verified: null == verified ? _self.verified : verified // ignore: cast_nullable_to_non_nullable
as int,celebrities: null == celebrities ? _self.celebrities : celebrities // ignore: cast_nullable_to_non_nullable
as int,businesses: null == businesses ? _self.businesses : businesses // ignore: cast_nullable_to_non_nullable
as int,billionaires: null == billionaires ? _self.billionaires : billionaires // ignore: cast_nullable_to_non_nullable
as int,regular: null == regular ? _self.regular : regular // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [AccountTypeSplit].
extension AccountTypeSplitPatterns on AccountTypeSplit {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _AccountTypeSplit value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _AccountTypeSplit() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _AccountTypeSplit value)  $default,){
final _that = this;
switch (_that) {
case _AccountTypeSplit():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _AccountTypeSplit value)?  $default,){
final _that = this;
switch (_that) {
case _AccountTypeSplit() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int verified,  int celebrities,  int businesses,  int billionaires,  int regular)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _AccountTypeSplit() when $default != null:
return $default(_that.verified,_that.celebrities,_that.businesses,_that.billionaires,_that.regular);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int verified,  int celebrities,  int businesses,  int billionaires,  int regular)  $default,) {final _that = this;
switch (_that) {
case _AccountTypeSplit():
return $default(_that.verified,_that.celebrities,_that.businesses,_that.billionaires,_that.regular);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int verified,  int celebrities,  int businesses,  int billionaires,  int regular)?  $default,) {final _that = this;
switch (_that) {
case _AccountTypeSplit() when $default != null:
return $default(_that.verified,_that.celebrities,_that.businesses,_that.billionaires,_that.regular);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _AccountTypeSplit implements AccountTypeSplit {
  const _AccountTypeSplit({required this.verified, required this.celebrities, required this.businesses, required this.billionaires, required this.regular});
  factory _AccountTypeSplit.fromJson(Map<String, dynamic> json) => _$AccountTypeSplitFromJson(json);

@override final  int verified;
@override final  int celebrities;
@override final  int businesses;
@override final  int billionaires;
@override final  int regular;

/// Create a copy of AccountTypeSplit
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AccountTypeSplitCopyWith<_AccountTypeSplit> get copyWith => __$AccountTypeSplitCopyWithImpl<_AccountTypeSplit>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AccountTypeSplitToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AccountTypeSplit&&(identical(other.verified, verified) || other.verified == verified)&&(identical(other.celebrities, celebrities) || other.celebrities == celebrities)&&(identical(other.businesses, businesses) || other.businesses == businesses)&&(identical(other.billionaires, billionaires) || other.billionaires == billionaires)&&(identical(other.regular, regular) || other.regular == regular));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,verified,celebrities,businesses,billionaires,regular);

@override
String toString() {
  return 'AccountTypeSplit(verified: $verified, celebrities: $celebrities, businesses: $businesses, billionaires: $billionaires, regular: $regular)';
}


}

/// @nodoc
abstract mixin class _$AccountTypeSplitCopyWith<$Res> implements $AccountTypeSplitCopyWith<$Res> {
  factory _$AccountTypeSplitCopyWith(_AccountTypeSplit value, $Res Function(_AccountTypeSplit) _then) = __$AccountTypeSplitCopyWithImpl;
@override @useResult
$Res call({
 int verified, int celebrities, int businesses, int billionaires, int regular
});




}
/// @nodoc
class __$AccountTypeSplitCopyWithImpl<$Res>
    implements _$AccountTypeSplitCopyWith<$Res> {
  __$AccountTypeSplitCopyWithImpl(this._self, this._then);

  final _AccountTypeSplit _self;
  final $Res Function(_AccountTypeSplit) _then;

/// Create a copy of AccountTypeSplit
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? verified = null,Object? celebrities = null,Object? businesses = null,Object? billionaires = null,Object? regular = null,}) {
  return _then(_AccountTypeSplit(
verified: null == verified ? _self.verified : verified // ignore: cast_nullable_to_non_nullable
as int,celebrities: null == celebrities ? _self.celebrities : celebrities // ignore: cast_nullable_to_non_nullable
as int,businesses: null == businesses ? _self.businesses : businesses // ignore: cast_nullable_to_non_nullable
as int,billionaires: null == billionaires ? _self.billionaires : billionaires // ignore: cast_nullable_to_non_nullable
as int,regular: null == regular ? _self.regular : regular // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}


/// @nodoc
mixin _$MutualStatusSplit {

 int get mutual; int get notFollowingBack; int get notFollowedBack;
/// Create a copy of MutualStatusSplit
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MutualStatusSplitCopyWith<MutualStatusSplit> get copyWith => _$MutualStatusSplitCopyWithImpl<MutualStatusSplit>(this as MutualStatusSplit, _$identity);

  /// Serializes this MutualStatusSplit to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MutualStatusSplit&&(identical(other.mutual, mutual) || other.mutual == mutual)&&(identical(other.notFollowingBack, notFollowingBack) || other.notFollowingBack == notFollowingBack)&&(identical(other.notFollowedBack, notFollowedBack) || other.notFollowedBack == notFollowedBack));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,mutual,notFollowingBack,notFollowedBack);

@override
String toString() {
  return 'MutualStatusSplit(mutual: $mutual, notFollowingBack: $notFollowingBack, notFollowedBack: $notFollowedBack)';
}


}

/// @nodoc
abstract mixin class $MutualStatusSplitCopyWith<$Res>  {
  factory $MutualStatusSplitCopyWith(MutualStatusSplit value, $Res Function(MutualStatusSplit) _then) = _$MutualStatusSplitCopyWithImpl;
@useResult
$Res call({
 int mutual, int notFollowingBack, int notFollowedBack
});




}
/// @nodoc
class _$MutualStatusSplitCopyWithImpl<$Res>
    implements $MutualStatusSplitCopyWith<$Res> {
  _$MutualStatusSplitCopyWithImpl(this._self, this._then);

  final MutualStatusSplit _self;
  final $Res Function(MutualStatusSplit) _then;

/// Create a copy of MutualStatusSplit
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? mutual = null,Object? notFollowingBack = null,Object? notFollowedBack = null,}) {
  return _then(_self.copyWith(
mutual: null == mutual ? _self.mutual : mutual // ignore: cast_nullable_to_non_nullable
as int,notFollowingBack: null == notFollowingBack ? _self.notFollowingBack : notFollowingBack // ignore: cast_nullable_to_non_nullable
as int,notFollowedBack: null == notFollowedBack ? _self.notFollowedBack : notFollowedBack // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [MutualStatusSplit].
extension MutualStatusSplitPatterns on MutualStatusSplit {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _MutualStatusSplit value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _MutualStatusSplit() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _MutualStatusSplit value)  $default,){
final _that = this;
switch (_that) {
case _MutualStatusSplit():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _MutualStatusSplit value)?  $default,){
final _that = this;
switch (_that) {
case _MutualStatusSplit() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int mutual,  int notFollowingBack,  int notFollowedBack)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _MutualStatusSplit() when $default != null:
return $default(_that.mutual,_that.notFollowingBack,_that.notFollowedBack);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int mutual,  int notFollowingBack,  int notFollowedBack)  $default,) {final _that = this;
switch (_that) {
case _MutualStatusSplit():
return $default(_that.mutual,_that.notFollowingBack,_that.notFollowedBack);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int mutual,  int notFollowingBack,  int notFollowedBack)?  $default,) {final _that = this;
switch (_that) {
case _MutualStatusSplit() when $default != null:
return $default(_that.mutual,_that.notFollowingBack,_that.notFollowedBack);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _MutualStatusSplit implements MutualStatusSplit {
  const _MutualStatusSplit({required this.mutual, required this.notFollowingBack, required this.notFollowedBack});
  factory _MutualStatusSplit.fromJson(Map<String, dynamic> json) => _$MutualStatusSplitFromJson(json);

@override final  int mutual;
@override final  int notFollowingBack;
@override final  int notFollowedBack;

/// Create a copy of MutualStatusSplit
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MutualStatusSplitCopyWith<_MutualStatusSplit> get copyWith => __$MutualStatusSplitCopyWithImpl<_MutualStatusSplit>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$MutualStatusSplitToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _MutualStatusSplit&&(identical(other.mutual, mutual) || other.mutual == mutual)&&(identical(other.notFollowingBack, notFollowingBack) || other.notFollowingBack == notFollowingBack)&&(identical(other.notFollowedBack, notFollowedBack) || other.notFollowedBack == notFollowedBack));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,mutual,notFollowingBack,notFollowedBack);

@override
String toString() {
  return 'MutualStatusSplit(mutual: $mutual, notFollowingBack: $notFollowingBack, notFollowedBack: $notFollowedBack)';
}


}

/// @nodoc
abstract mixin class _$MutualStatusSplitCopyWith<$Res> implements $MutualStatusSplitCopyWith<$Res> {
  factory _$MutualStatusSplitCopyWith(_MutualStatusSplit value, $Res Function(_MutualStatusSplit) _then) = __$MutualStatusSplitCopyWithImpl;
@override @useResult
$Res call({
 int mutual, int notFollowingBack, int notFollowedBack
});




}
/// @nodoc
class __$MutualStatusSplitCopyWithImpl<$Res>
    implements _$MutualStatusSplitCopyWith<$Res> {
  __$MutualStatusSplitCopyWithImpl(this._self, this._then);

  final _MutualStatusSplit _self;
  final $Res Function(_MutualStatusSplit) _then;

/// Create a copy of MutualStatusSplit
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? mutual = null,Object? notFollowingBack = null,Object? notFollowedBack = null,}) {
  return _then(_MutualStatusSplit(
mutual: null == mutual ? _self.mutual : mutual // ignore: cast_nullable_to_non_nullable
as int,notFollowingBack: null == notFollowingBack ? _self.notFollowingBack : notFollowingBack // ignore: cast_nullable_to_non_nullable
as int,notFollowedBack: null == notFollowedBack ? _self.notFollowedBack : notFollowedBack // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}


/// @nodoc
mixin _$FollowersFilter {

 FilterType get type; Gender? get gender; AccountType? get accountType; MutualStatus? get mutualStatus; bool? get isVerified; bool? get isMuted; String? get searchQuery;
/// Create a copy of FollowersFilter
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$FollowersFilterCopyWith<FollowersFilter> get copyWith => _$FollowersFilterCopyWithImpl<FollowersFilter>(this as FollowersFilter, _$identity);

  /// Serializes this FollowersFilter to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is FollowersFilter&&(identical(other.type, type) || other.type == type)&&(identical(other.gender, gender) || other.gender == gender)&&(identical(other.accountType, accountType) || other.accountType == accountType)&&(identical(other.mutualStatus, mutualStatus) || other.mutualStatus == mutualStatus)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.isMuted, isMuted) || other.isMuted == isMuted)&&(identical(other.searchQuery, searchQuery) || other.searchQuery == searchQuery));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,type,gender,accountType,mutualStatus,isVerified,isMuted,searchQuery);

@override
String toString() {
  return 'FollowersFilter(type: $type, gender: $gender, accountType: $accountType, mutualStatus: $mutualStatus, isVerified: $isVerified, isMuted: $isMuted, searchQuery: $searchQuery)';
}


}

/// @nodoc
abstract mixin class $FollowersFilterCopyWith<$Res>  {
  factory $FollowersFilterCopyWith(FollowersFilter value, $Res Function(FollowersFilter) _then) = _$FollowersFilterCopyWithImpl;
@useResult
$Res call({
 FilterType type, Gender? gender, AccountType? accountType, MutualStatus? mutualStatus, bool? isVerified, bool? isMuted, String? searchQuery
});




}
/// @nodoc
class _$FollowersFilterCopyWithImpl<$Res>
    implements $FollowersFilterCopyWith<$Res> {
  _$FollowersFilterCopyWithImpl(this._self, this._then);

  final FollowersFilter _self;
  final $Res Function(FollowersFilter) _then;

/// Create a copy of FollowersFilter
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? type = null,Object? gender = freezed,Object? accountType = freezed,Object? mutualStatus = freezed,Object? isVerified = freezed,Object? isMuted = freezed,Object? searchQuery = freezed,}) {
  return _then(_self.copyWith(
type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as FilterType,gender: freezed == gender ? _self.gender : gender // ignore: cast_nullable_to_non_nullable
as Gender?,accountType: freezed == accountType ? _self.accountType : accountType // ignore: cast_nullable_to_non_nullable
as AccountType?,mutualStatus: freezed == mutualStatus ? _self.mutualStatus : mutualStatus // ignore: cast_nullable_to_non_nullable
as MutualStatus?,isVerified: freezed == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool?,isMuted: freezed == isMuted ? _self.isMuted : isMuted // ignore: cast_nullable_to_non_nullable
as bool?,searchQuery: freezed == searchQuery ? _self.searchQuery : searchQuery // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [FollowersFilter].
extension FollowersFilterPatterns on FollowersFilter {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _FollowersFilter value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _FollowersFilter() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _FollowersFilter value)  $default,){
final _that = this;
switch (_that) {
case _FollowersFilter():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _FollowersFilter value)?  $default,){
final _that = this;
switch (_that) {
case _FollowersFilter() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( FilterType type,  Gender? gender,  AccountType? accountType,  MutualStatus? mutualStatus,  bool? isVerified,  bool? isMuted,  String? searchQuery)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _FollowersFilter() when $default != null:
return $default(_that.type,_that.gender,_that.accountType,_that.mutualStatus,_that.isVerified,_that.isMuted,_that.searchQuery);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( FilterType type,  Gender? gender,  AccountType? accountType,  MutualStatus? mutualStatus,  bool? isVerified,  bool? isMuted,  String? searchQuery)  $default,) {final _that = this;
switch (_that) {
case _FollowersFilter():
return $default(_that.type,_that.gender,_that.accountType,_that.mutualStatus,_that.isVerified,_that.isMuted,_that.searchQuery);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( FilterType type,  Gender? gender,  AccountType? accountType,  MutualStatus? mutualStatus,  bool? isVerified,  bool? isMuted,  String? searchQuery)?  $default,) {final _that = this;
switch (_that) {
case _FollowersFilter() when $default != null:
return $default(_that.type,_that.gender,_that.accountType,_that.mutualStatus,_that.isVerified,_that.isMuted,_that.searchQuery);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _FollowersFilter implements FollowersFilter {
  const _FollowersFilter({required this.type, required this.gender, required this.accountType, required this.mutualStatus, required this.isVerified, required this.isMuted, required this.searchQuery});
  factory _FollowersFilter.fromJson(Map<String, dynamic> json) => _$FollowersFilterFromJson(json);

@override final  FilterType type;
@override final  Gender? gender;
@override final  AccountType? accountType;
@override final  MutualStatus? mutualStatus;
@override final  bool? isVerified;
@override final  bool? isMuted;
@override final  String? searchQuery;

/// Create a copy of FollowersFilter
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$FollowersFilterCopyWith<_FollowersFilter> get copyWith => __$FollowersFilterCopyWithImpl<_FollowersFilter>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$FollowersFilterToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _FollowersFilter&&(identical(other.type, type) || other.type == type)&&(identical(other.gender, gender) || other.gender == gender)&&(identical(other.accountType, accountType) || other.accountType == accountType)&&(identical(other.mutualStatus, mutualStatus) || other.mutualStatus == mutualStatus)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.isMuted, isMuted) || other.isMuted == isMuted)&&(identical(other.searchQuery, searchQuery) || other.searchQuery == searchQuery));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,type,gender,accountType,mutualStatus,isVerified,isMuted,searchQuery);

@override
String toString() {
  return 'FollowersFilter(type: $type, gender: $gender, accountType: $accountType, mutualStatus: $mutualStatus, isVerified: $isVerified, isMuted: $isMuted, searchQuery: $searchQuery)';
}


}

/// @nodoc
abstract mixin class _$FollowersFilterCopyWith<$Res> implements $FollowersFilterCopyWith<$Res> {
  factory _$FollowersFilterCopyWith(_FollowersFilter value, $Res Function(_FollowersFilter) _then) = __$FollowersFilterCopyWithImpl;
@override @useResult
$Res call({
 FilterType type, Gender? gender, AccountType? accountType, MutualStatus? mutualStatus, bool? isVerified, bool? isMuted, String? searchQuery
});




}
/// @nodoc
class __$FollowersFilterCopyWithImpl<$Res>
    implements _$FollowersFilterCopyWith<$Res> {
  __$FollowersFilterCopyWithImpl(this._self, this._then);

  final _FollowersFilter _self;
  final $Res Function(_FollowersFilter) _then;

/// Create a copy of FollowersFilter
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? type = null,Object? gender = freezed,Object? accountType = freezed,Object? mutualStatus = freezed,Object? isVerified = freezed,Object? isMuted = freezed,Object? searchQuery = freezed,}) {
  return _then(_FollowersFilter(
type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as FilterType,gender: freezed == gender ? _self.gender : gender // ignore: cast_nullable_to_non_nullable
as Gender?,accountType: freezed == accountType ? _self.accountType : accountType // ignore: cast_nullable_to_non_nullable
as AccountType?,mutualStatus: freezed == mutualStatus ? _self.mutualStatus : mutualStatus // ignore: cast_nullable_to_non_nullable
as MutualStatus?,isVerified: freezed == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool?,isMuted: freezed == isMuted ? _self.isMuted : isMuted // ignore: cast_nullable_to_non_nullable
as bool?,searchQuery: freezed == searchQuery ? _self.searchQuery : searchQuery // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
