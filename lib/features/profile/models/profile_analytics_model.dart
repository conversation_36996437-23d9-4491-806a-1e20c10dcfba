import 'package:freezed_annotation/freezed_annotation.dart';

part 'profile_analytics_model.freezed.dart';
part 'profile_analytics_model.g.dart';

@freezed
abstract class ProfileAnalyticsModel with _$ProfileAnalyticsModel {
  const factory ProfileAnalyticsModel({
    required String id,
    required String userId,
    required DateTime date,
    required ProfileViewsData profileViews,
    required FollowerGrowthData followerGrowth,
    required EngagementData engagement,
    required ContentPerformanceData contentPerformance,
    required AudienceData audience,
    required Map<String, dynamic> metadata,
  }) = _ProfileAnalyticsModel;

  factory ProfileAnalyticsModel.fromJson(Map<String, dynamic> json) =>
      _$ProfileAnalyticsModelFromJson(json);
}

@freezed
abstract class ProfileViewsData with _$ProfileViewsData {
  const factory ProfileViewsData({
    required int totalViews,
    required int uniqueViews,
    required int profileVisits,
    required int profileShares,
    required int profileBookmarks,
    required List<ProfileViewSource> viewSources,
    required List<ProfileViewTime> viewTimes,
    required Map<String, int> viewsByCountry,
    required Map<String, int> viewsByDevice,
  }) = _ProfileViewsData;

  factory ProfileViewsData.fromJson(Map<String, dynamic> json) =>
      _$ProfileViewsDataFromJson(json);
}

@freezed
abstract class ProfileViewSource with _$ProfileViewSource {
  const factory ProfileViewSource({
    required String source,
    required int count,
    required double percentage,
  }) = _ProfileViewSource;

  factory ProfileViewSource.fromJson(Map<String, dynamic> json) =>
      _$ProfileViewSourceFromJson(json);
}

@freezed
abstract class ProfileViewTime with _$ProfileViewTime {
  const factory ProfileViewTime({
    required String timeSlot,
    required int count,
  }) = _ProfileViewTime;

  factory ProfileViewTime.fromJson(Map<String, dynamic> json) =>
      _$ProfileViewTimeFromJson(json);
}

@freezed
abstract class FollowerGrowthData with _$FollowerGrowthData {
  const factory FollowerGrowthData({
    required int totalFollowers,
    required int newFollowers,
    required int lostFollowers,
    required double growthRate,
    required List<FollowerGrowthPoint> growthHistory,
    required Map<String, int> followersBySource,
    required Map<String, int> followersByCountry,
    required Map<String, int> followersByAge,
    required Map<String, int> followersByGender,
  }) = _FollowerGrowthData;

  factory FollowerGrowthData.fromJson(Map<String, dynamic> json) =>
      _$FollowerGrowthDataFromJson(json);
}

@freezed
abstract class FollowerGrowthPoint with _$FollowerGrowthPoint {
  const factory FollowerGrowthPoint({
    required DateTime date,
    required int followers,
    required int newFollowers,
    required int lostFollowers,
  }) = _FollowerGrowthPoint;

  factory FollowerGrowthPoint.fromJson(Map<String, dynamic> json) =>
      _$FollowerGrowthPointFromJson(json);
}

@freezed
abstract class EngagementData with _$EngagementData {
  const factory EngagementData({
    required double overallEngagementRate,
    required int totalEngagements,
    required int likes,
    required int comments,
    required int shares,
    required int saves,
    required List<EngagementTrend> engagementTrends,
    required Map<String, double> engagementByContentType,
    required Map<String, double> engagementByTimeOfDay,
    required Map<String, double> engagementByDayOfWeek,
  }) = _EngagementData;

  factory EngagementData.fromJson(Map<String, dynamic> json) =>
      _$EngagementDataFromJson(json);
}

@freezed
abstract class EngagementTrend with _$EngagementTrend {
  const factory EngagementTrend({
    required DateTime date,
    required double engagementRate,
    required int totalEngagements,
  }) = _EngagementTrend;

  factory EngagementTrend.fromJson(Map<String, dynamic> json) =>
      _$EngagementTrendFromJson(json);
}

@freezed
abstract class ContentPerformanceData with _$ContentPerformanceData {
  const factory ContentPerformanceData({
    required int totalPosts,
    required double averageLikes,
    required double averageComments,
    required double averageShares,
    required double averageSaves,
    required List<TopPerformingPost> topPosts,
    required Map<String, int> postsByType,
    required Map<String, double> performanceByType,
  }) = _ContentPerformanceData;

  factory ContentPerformanceData.fromJson(Map<String, dynamic> json) =>
      _$ContentPerformanceDataFromJson(json);
}

@freezed
abstract class TopPerformingPost with _$TopPerformingPost {
  const factory TopPerformingPost({
    required String postId,
    required String postType,
    required int likes,
    required int comments,
    required int shares,
    required int saves,
    required double engagementRate,
    required DateTime postedAt,
  }) = _TopPerformingPost;

  factory TopPerformingPost.fromJson(Map<String, dynamic> json) =>
      _$TopPerformingPostFromJson(json);
}

@freezed
abstract class AudienceData with _$AudienceData {
  const factory AudienceData({
    required int totalAudience,
    required Map<String, int> audienceByCountry,
    required Map<String, int> audienceByAge,
    required Map<String, int> audienceByGender,
    required Map<String, int> audienceByInterest,
    required List<String> topCountries,
    required List<String> topInterests,
    required double averageAge,
    required String dominantGender,
  }) = _AudienceData;

  factory AudienceData.fromJson(Map<String, dynamic> json) =>
      _$AudienceDataFromJson(json);
}

@freezed
abstract class ProfileAnalyticsSummary with _$ProfileAnalyticsSummary {
  const factory ProfileAnalyticsSummary({
    required String userId,
    required DateTime periodStart,
    required DateTime periodEnd,
    required int totalProfileViews,
    required int totalFollowers,
    required double engagementRate,
    required int totalPosts,
    required double followerGrowthRate,
    required List<String> topPerformingContent,
    required Map<String, dynamic> insights,
  }) = _ProfileAnalyticsSummary;

  factory ProfileAnalyticsSummary.fromJson(Map<String, dynamic> json) =>
      _$ProfileAnalyticsSummaryFromJson(json);
}
