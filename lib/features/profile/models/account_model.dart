import 'package:cloud_firestore/cloud_firestore.dart';

enum AccountType { personal, business, celebrity }

class AccountModel {
  final String id;
  final String name;
  final String username;
  final String profilePictureUrl;
  final String bio;
  final AccountType accountType;
  final bool isActive;
  final DateTime createdAt;
  final int followersCount;
  final int followingCount;
  final int postsCount;
  final bool isVerified;
  final bool isBillionaire;
  final DateTime? lastActiveAt;
  final Map<String, dynamic>? settings;

  const AccountModel({
    required this.id,
    required this.name,
    required this.username,
    required this.profilePictureUrl,
    required this.bio,
    required this.accountType,
    required this.isActive,
    required this.createdAt,
    required this.followersCount,
    required this.followingCount,
    required this.postsCount,
    required this.isVerified,
    required this.isBillionaire,
    this.lastActiveAt,
    this.settings,
  });

  factory AccountModel.fromJson(Map<String, dynamic> json) {
    return AccountModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      username: json['username'] ?? '',
      profilePictureUrl: json['profilePictureUrl'] ?? '',
      bio: json['bio'] ?? '',
      accountType: _parseAccountType(json['accountType']),
      isActive: json['isActive'] ?? false,
      createdAt: _parseTimestamp(json['createdAt']),
      followersCount: json['followersCount'] ?? 0,
      followingCount: json['followingCount'] ?? 0,
      postsCount: json['postsCount'] ?? 0,
      isVerified: json['isVerified'] ?? false,
      isBillionaire: json['isBillionaire'] ?? false,
      lastActiveAt: _parseTimestamp(json['lastActiveAt']),
      settings: json['settings'] as Map<String, dynamic>?,
    );
  }

  factory AccountModel.fromFirestore(String id, Map<String, dynamic> data) {
    return AccountModel(
      id: id,
      name: data['name'] ?? '',
      username: data['username'] ?? '',
      profilePictureUrl: data['profilePictureUrl'] ?? '',
      bio: data['bio'] ?? '',
      accountType: _parseAccountType(data['accountType']),
      isActive: data['isActive'] ?? false,
      createdAt: _parseTimestamp(data['createdAt']),
      followersCount: data['followersCount'] ?? 0,
      followingCount: data['followingCount'] ?? 0,
      postsCount: data['postsCount'] ?? 0,
      isVerified: data['isVerified'] ?? false,
      isBillionaire: data['isBillionaire'] ?? false,
      lastActiveAt: _parseTimestamp(data['lastActiveAt']),
      settings: data['settings'] as Map<String, dynamic>?,
    );
  }

  static AccountType _parseAccountType(dynamic value) {
    if (value == null) return AccountType.personal;

    switch (value.toString().toLowerCase()) {
      case 'business':
        return AccountType.business;
      case 'celebrity':
        return AccountType.celebrity;
      default:
        return AccountType.personal;
    }
  }

  static DateTime _parseTimestamp(dynamic value) {
    if (value == null) return DateTime.now();

    if (value is Timestamp) {
      return value.toDate();
    } else if (value is String) {
      return DateTime.tryParse(value) ?? DateTime.now();
    } else if (value is int) {
      return DateTime.fromMillisecondsSinceEpoch(value);
    }

    return DateTime.now();
  }
}

extension AccountModelExtensions on AccountModel {
  /// Get display name for the account type
  String get accountTypeDisplayName {
    switch (accountType) {
      case AccountType.personal:
        return 'Personal';
      case AccountType.business:
        return 'Business';
      case AccountType.celebrity:
        return 'Celebrity';
    }
  }

  /// Get icon for the account type
  String get accountTypeIcon {
    switch (accountType) {
      case AccountType.personal:
        return '👤';
      case AccountType.business:
        return '🏢';
      case AccountType.celebrity:
        return '⭐';
    }
  }

  /// Get color for the account type
  String get accountTypeColor {
    switch (accountType) {
      case AccountType.personal:
        return '#4A90E2'; // Blue
      case AccountType.business:
        return '#7ED321'; // Green
      case AccountType.celebrity:
        return '#F5A623'; // Gold
    }
  }

  /// Check if account has premium features
  bool get hasPremiumFeatures {
    return accountType == AccountType.celebrity || isBillionaire;
  }

  /// Get formatted follower count
  String get formattedFollowersCount {
    if (followersCount >= 1000000) {
      return '${(followersCount / 1000000).toStringAsFixed(1)}M';
    } else if (followersCount >= 1000) {
      return '${(followersCount / 1000).toStringAsFixed(1)}K';
    }
    return followersCount.toString();
  }

  /// Get formatted following count
  String get formattedFollowingCount {
    if (followingCount >= 1000000) {
      return '${(followingCount / 1000000).toStringAsFixed(1)}M';
    } else if (followingCount >= 1000) {
      return '${(followingCount / 1000).toStringAsFixed(1)}K';
    }
    return followingCount.toString();
  }

  /// Get formatted posts count
  String get formattedPostsCount {
    if (postsCount >= 1000000) {
      return '${(postsCount / 1000000).toStringAsFixed(1)}M';
    } else if (postsCount >= 1000) {
      return '${(postsCount / 1000).toStringAsFixed(1)}K';
    }
    return postsCount.toString();
  }

  /// Convert to Firestore data
  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'username': username,
      'profilePictureUrl': profilePictureUrl,
      'bio': bio,
      'accountType': accountType.toString().split('.').last,
      'isActive': isActive,
      'createdAt': Timestamp.fromDate(createdAt),
      'followersCount': followersCount,
      'followingCount': followingCount,
      'postsCount': postsCount,
      'isVerified': isVerified,
      'isBillionaire': isBillionaire,
      'lastActiveAt': lastActiveAt != null
          ? Timestamp.fromDate(lastActiveAt!)
          : null,
      'settings': settings,
      'updatedAt': FieldValue.serverTimestamp(),
    };
  }

  /// Create a copy with updated values
  AccountModel copyWith({
    String? id,
    String? name,
    String? username,
    String? profilePictureUrl,
    String? bio,
    AccountType? accountType,
    bool? isActive,
    DateTime? createdAt,
    int? followersCount,
    int? followingCount,
    int? postsCount,
    bool? isVerified,
    bool? isBillionaire,
    DateTime? lastActiveAt,
    Map<String, dynamic>? settings,
  }) {
    return AccountModel(
      id: id ?? this.id,
      name: name ?? this.name,
      username: username ?? this.username,
      profilePictureUrl: profilePictureUrl ?? this.profilePictureUrl,
      bio: bio ?? this.bio,
      accountType: accountType ?? this.accountType,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      followersCount: followersCount ?? this.followersCount,
      followingCount: followingCount ?? this.followingCount,
      postsCount: postsCount ?? this.postsCount,
      isVerified: isVerified ?? this.isVerified,
      isBillionaire: isBillionaire ?? this.isBillionaire,
      lastActiveAt: lastActiveAt ?? this.lastActiveAt,
      settings: settings ?? this.settings,
    );
  }
}

/// Account creation request model
class CreateAccountRequest {
  final String name;
  final String username;
  final AccountType accountType;
  final String? profilePictureUrl;
  final String? bio;

  const CreateAccountRequest({
    required this.name,
    required this.username,
    required this.accountType,
    this.profilePictureUrl,
    this.bio,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'username': username,
      'accountType': accountType.toString().split('.').last,
      'profilePictureUrl': profilePictureUrl,
      'bio': bio,
    };
  }
}

/// Account update request model
class UpdateAccountRequest {
  final String? name;
  final String? username;
  final String? profilePictureUrl;
  final String? bio;
  final Map<String, dynamic>? settings;

  const UpdateAccountRequest({
    this.name,
    this.username,
    this.profilePictureUrl,
    this.bio,
    this.settings,
  });

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (name != null) json['name'] = name;
    if (username != null) json['username'] = username;
    if (profilePictureUrl != null) {
      json['profilePictureUrl'] = profilePictureUrl;
    }
    if (bio != null) json['bio'] = bio;
    if (settings != null) json['settings'] = settings;
    return json;
  }
}
