// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'close_friends_group_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_CloseFriendsGroup _$CloseFriendsGroupFromJson(Map<String, dynamic> json) =>
    _CloseFriendsGroup(
      id: json['id'] as String,
      name: json['name'] as String,
      emoji: json['emoji'] as String,
      memberIds: (json['memberIds'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      isStoryVisible: json['isStoryVisible'] as bool,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$CloseFriendsGroupToJson(_CloseFriendsGroup instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'emoji': instance.emoji,
      'memberIds': instance.memberIds,
      'isStoryVisible': instance.isStoryVisible,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };
