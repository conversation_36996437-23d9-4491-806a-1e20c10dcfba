// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'profile_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ProfileModel _$ProfileModelFromJson(Map<String, dynamic> json) =>
    _ProfileModel(
      id: json['id'] as String,
      username: json['username'] as String,
      name: json['name'] as String,
      profilePictureUrl: json['profilePictureUrl'] as String,
      bio: json['bio'] as String,
      postCount: (json['postCount'] as num).toInt(),
      followerCount: (json['followerCount'] as num).toInt(),
      followingCount: (json['followingCount'] as num).toInt(),
      bannerImageUrl: json['bannerImageUrl'] as String?,
      website: json['website'] as String?,
      location: json['location'] as String?,
      phone: json['phone'] as String?,
      email: json['email'] as String?,
      isPrivate: json['isPrivate'] as bool? ?? false,
      allowMessages: json['allowMessages'] as bool? ?? true,
      showActivityStatus: json['showActivityStatus'] as bool? ?? true,
      isVerified: json['isVerified'] as bool? ?? false,
      isBillionaire: json['isBillionaire'] as bool? ?? false,
      userType: json['userType'] as String? ?? 'regular',
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      isBusinessAccount: json['isBusinessAccount'] as bool? ?? false,
      businessName: json['businessName'] as String?,
      businessLogoUrl: json['businessLogoUrl'] as String?,
      businessEmail: json['businessEmail'] as String?,
      businessPhone: json['businessPhone'] as String?,
      businessDescription: json['businessDescription'] as String?,
      businessVerified: json['businessVerified'] as bool? ?? false,
      businessExclusive: json['businessExclusive'] as bool? ?? false,
      businessCategory: json['businessCategory'] as String?,
      businessWebsite: json['businessWebsite'] as String?,
      businessAddress: json['businessAddress'] as String?,
      businessCreatedAt: json['businessCreatedAt'] == null
          ? null
          : DateTime.parse(json['businessCreatedAt'] as String),
      businessVerifiedAt: json['businessVerifiedAt'] == null
          ? null
          : DateTime.parse(json['businessVerifiedAt'] as String),
      isAdmin: json['isAdmin'] as bool? ?? false,
    );

Map<String, dynamic> _$ProfileModelToJson(_ProfileModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'username': instance.username,
      'name': instance.name,
      'profilePictureUrl': instance.profilePictureUrl,
      'bio': instance.bio,
      'postCount': instance.postCount,
      'followerCount': instance.followerCount,
      'followingCount': instance.followingCount,
      'bannerImageUrl': instance.bannerImageUrl,
      'website': instance.website,
      'location': instance.location,
      'phone': instance.phone,
      'email': instance.email,
      'isPrivate': instance.isPrivate,
      'allowMessages': instance.allowMessages,
      'showActivityStatus': instance.showActivityStatus,
      'isVerified': instance.isVerified,
      'isBillionaire': instance.isBillionaire,
      'userType': instance.userType,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'isBusinessAccount': instance.isBusinessAccount,
      'businessName': instance.businessName,
      'businessLogoUrl': instance.businessLogoUrl,
      'businessEmail': instance.businessEmail,
      'businessPhone': instance.businessPhone,
      'businessDescription': instance.businessDescription,
      'businessVerified': instance.businessVerified,
      'businessExclusive': instance.businessExclusive,
      'businessCategory': instance.businessCategory,
      'businessWebsite': instance.businessWebsite,
      'businessAddress': instance.businessAddress,
      'businessCreatedAt': instance.businessCreatedAt?.toIso8601String(),
      'businessVerifiedAt': instance.businessVerifiedAt?.toIso8601String(),
      'isAdmin': instance.isAdmin,
    };
