import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/features/notifications/services/notification_service.dart';

class CloseFriendsService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final NotificationService _notificationService = getIt<NotificationService>();

  /// Add a user to close friends list
  Future<void> addToCloseFriends(String userId) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    try {
      // Add to current user's close friends collection
      await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .collection('closeFriends')
          .doc(userId)
          .set({
        'userId': userId,
        'addedAt': FieldValue.serverTimestamp(),
      });

      // Send notification to the user being added
      await _notificationService.createCloseFriendNotification(
        userId,
        currentUser.uid,
        'added you to their close friends',
      );

      debugPrint('✅ Added user $userId to close friends');
    } catch (e) {
      debugPrint('❌ Error adding to close friends: $e');
      throw Exception('Failed to add to close friends');
    }
  }

  /// Remove a user from close friends list
  Future<void> removeFromCloseFriends(String userId) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    try {
      // Remove from current user's close friends collection
      await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .collection('closeFriends')
          .doc(userId)
          .delete();

      debugPrint('✅ Removed user $userId from close friends');
    } catch (e) {
      debugPrint('❌ Error removing from close friends: $e');
      throw Exception('Failed to remove from close friends');
    }
  }

  /// Check if a user is in close friends list
  Future<bool> isCloseFriend(String userId) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return false;

    try {
      final doc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .collection('closeFriends')
          .doc(userId)
          .get();

      return doc.exists;
    } catch (e) {
      debugPrint('❌ Error checking close friend status: $e');
      return false;
    }
  }

  /// Get all close friends
  Future<List<String>> getCloseFriends() async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return [];

    try {
      final snapshot = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .collection('closeFriends')
          .get();

      return snapshot.docs.map((doc) => doc.id).toList();
    } catch (e) {
      debugPrint('❌ Error getting close friends: $e');
      return [];
    }
  }

  /// Get close friends with their profile data
  Future<List<Map<String, dynamic>>> getCloseFriendsWithProfiles() async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return [];

    try {
      final closeFriendIds = await getCloseFriends();
      if (closeFriendIds.isEmpty) return [];

      final List<Map<String, dynamic>> closeFriendsProfiles = [];

      // Fetch profiles in batches (Firestore limit is 10 for 'in' queries)
      for (int i = 0; i < closeFriendIds.length; i += 10) {
        final batch = closeFriendIds.skip(i).take(10).toList();
        final usersSnapshot = await _firestore
            .collection('users')
            .where(FieldPath.documentId, whereIn: batch)
            .get();

        final batchProfiles = usersSnapshot.docs.map((doc) {
          final data = doc.data();
          return {
            'id': doc.id,
            'name': data['name'] ?? '',
            'username': data['username'] ?? '',
            'profilePictureUrl': data['profilePictureUrl'] ?? '',
            'isVerified': data['isVerified'] ?? false,
            'isBillionaire': data['isBillionaire'] ?? false,
            'userType': data['userType'] ?? 'regular',
          };
        }).toList();

        closeFriendsProfiles.addAll(batchProfiles);
      }

      return closeFriendsProfiles;
    } catch (e) {
      debugPrint('❌ Error getting close friends with profiles: $e');
      return [];
    }
  }

  /// Get close friends count
  Future<int> getCloseFriendsCount() async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return 0;

    try {
      final snapshot = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .collection('closeFriends')
          .count()
          .get();

      return snapshot.count ?? 0;
    } catch (e) {
      debugPrint('❌ Error getting close friends count: $e');
      return 0;
    }
  }

  /// Stream of close friends (for real-time updates)
  Stream<List<String>> closeFriendsStream() {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return Stream.value([]);

    return _firestore
        .collection('users')
        .doc(currentUser.uid)
        .collection('closeFriends')
        .snapshots()
        .map((snapshot) => snapshot.docs.map((doc) => doc.id).toList());
  }

  /// Toggle close friend status
  Future<bool> toggleCloseFriend(String userId) async {
    final isCurrentlyCloseFriend = await isCloseFriend(userId);
    
    if (isCurrentlyCloseFriend) {
      await removeFromCloseFriends(userId);
      return false;
    } else {
      await addToCloseFriends(userId);
      return true;
    }
  }
}
