import 'package:billionaires_social/features/profile/models/profile_model.dart';
import 'package:billionaires_social/core/services/firebase_service.dart';
import 'package:billionaires_social/core/services/cache_service.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'dart:io';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/features/profile/models/profile_verification_model.dart';
import 'package:billionaires_social/features/profile/models/profile_analytics_model.dart';

/// Utility class for post validation and filtering
class PostValidationUtils {
  /// Determines if a post is valid based on content and metadata
  static bool isValidPost(Post post) {
    // Allow posts with empty captions or placeholder captions
    // Users should be able to post without captions if they want
    final caption = post.caption.trim();

    // Only filter out specific test captions that are clearly for debugging
    if (caption == "This is a test post from profile screen" ||
        caption.startsWith("Test post from debug tools")) {
      debugPrint(
        '❌ Invalid post ${post.id}: Test/placeholder caption: "$caption"',
      );
      return false;
    }

    // Use createdAt if present, otherwise fallback to localCreatedAt if available
    DateTime? timestamp = post.timestamp;
    if ((timestamp.millisecondsSinceEpoch == 0) &&
        post.localCreatedAt != null) {
      timestamp = post.localCreatedAt;
      debugPrint('ℹ️ Using localCreatedAt for post ${post.id}: $timestamp');
    }

    // Additional check: ensure post has a reasonable timestamp (not too far in future)
    final now = DateTime.now();
    final maxFutureTime = now.add(
      const Duration(hours: 1),
    ); // Allow 1 hour in future for timezone issues
    if (timestamp?.isAfter(maxFutureTime) == true) {
      debugPrint(
        '❌ Invalid post ${post.id}: Future or missing timestamp: $timestamp',
      );
      return false;
    }

    // Post is valid
    debugPrint(
      '✅ Valid post ${post.id}: "${caption.substring(0, math.min(30, caption.length))}..."',
    );
    return true;
  }

  /// Filters a list of posts to only include valid ones
  static List<Post> filterValidPosts(List<Post> posts) {
    final validPosts = posts.where(isValidPost).toList();
    final filteredCount = posts.length - validPosts.length;

    if (filteredCount > 0) {
      debugPrint(
        '🔍 Filtered out $filteredCount invalid posts, keeping ${validPosts.length} valid posts',
      );
    }

    return validPosts;
  }

  /// Checks if a post is valid based on raw Firestore data
  static bool isValidPostData(Map<String, dynamic> data) {
    // Check if post has a valid creation date
    final createdAt = data['createdAt'] as Timestamp?;
    final localCreatedAt = data['localCreatedAt'] as Timestamp?;
    if (createdAt == null && localCreatedAt == null) {
      return false;
    }

    // Allow posts with empty captions or placeholder captions
    // Users should be able to post without captions if they want
    final caption = (data['caption'] as String?)?.trim() ?? '';

    // Only filter out specific test captions that are clearly for debugging
    if (caption == "This is a test post from profile screen" ||
        caption.startsWith("Test post from debug tools")) {
      return false;
    }

    // Additional check: ensure timestamp is not too far in the future
    DateTime? timestamp = createdAt?.toDate();
    if (timestamp == null && localCreatedAt != null) {
      timestamp = localCreatedAt.toDate();
    }
    final now = DateTime.now();
    final maxFutureTime = now.add(const Duration(hours: 1));
    if (timestamp == null || timestamp.isAfter(maxFutureTime)) {
      return false;
    }

    return true;
  }
}

class ProfileService {
  final FirebaseService _firebaseService = getIt<FirebaseService>();
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Get current user ID
  String? getCurrentUserId() {
    return _auth.currentUser?.uid;
  }

  // Get current user profile
  Future<ProfileModel?> getCurrentUserProfile() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('No user is currently signed in');
      }

      final profile = await _firebaseService.getUserProfile(user.uid);
      if (profile != null) {
        return profile;
      }

      // If profile doesn't exist, create a default one
      return _createDefaultProfile(user);
    } catch (e) {
      throw Exception('Failed to fetch user profile: ${e.toString()}');
    }
  }

  // Get profile by user ID
  Future<ProfileModel?> getUserProfile(String userId) async {
    try {
      return await _firebaseService.getUserProfile(userId);
    } catch (e) {
      throw Exception('Failed to fetch profile: ${e.toString()}');
    }
  }

  // Update profile
  Future<void> updateProfileData(Map<String, dynamic> updates) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('No user is currently signed in');
      }

      await _firebaseService.updateUserProfile(user.uid, updates);
    } catch (e) {
      throw Exception('Failed to update profile: ${e.toString()}');
    }
  }

  // Upload profile image
  Future<String> uploadProfileImageFile(File imageFile) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('No user is currently signed in');
      }

      final imageUrl = await _firebaseService.uploadProfileImage(
        user.uid,
        imageFile,
      );

      // Update profile with new image URL
      await _firebaseService.updateUserProfile(user.uid, {
        'profilePictureUrl': imageUrl,
      });

      return imageUrl;
    } catch (e) {
      throw Exception('Failed to upload profile image: ${e.toString()}');
    }
  }

  // Update close friends list
  Future<void> updateCloseFriendsList(List<String> closeFriendIds) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('No user is currently signed in');
      }

      await _firebaseService.updateUserProfile(user.uid, {
        'closeFriendIds': closeFriendIds,
      });
    } catch (e) {
      throw Exception('Failed to update close friends: ${e.toString()}');
    }
  }

  // Get user posts
  Future<List<Post>> getUserPosts(
    String userId, {
    bool includeArchived = false,
  }) async {
    try {
      // Use proper Firestore ordering to ensure consistent sorting
      // Temporarily removing status filter until composite index is created
      // Will filter in memory instead to avoid index requirement
      Query query = FirebaseFirestore.instance
          .collection('posts')
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true);

      final snapshot = await query.get();
      final posts = snapshot.docs
          .map((doc) {
            final data = doc.data() as Map<String, dynamic>?;
            if (data == null) return null;

            return Post(
              id: doc.id,
              userId: data['userId'] ?? '',
              username: data['username'] ?? '',
              userAvatarUrl: data['userAvatarUrl'] ?? '',
              userRole: data['userRole'],
              isVerified: data['isVerified'] ?? false,
              mediaType: MediaType.values.firstWhere(
                (e) => e.name == (data['mediaType'] ?? 'image'),
                orElse: () => MediaType.image,
              ),
              mediaUrl: data['mediaUrl'] ?? '',
              caption: data['caption'] ?? '',
              location: data['location'],
              locationId: data['locationId'],
              likeCount: data['likeCount'] ?? 0,
              commentCount: data['commentCount'] ?? 0,
              timestamp:
                  (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
              isLiked: data['isLiked'] ?? false,
              isBookmarked: data['isBookmarked'] ?? false,
              isReposted: data['isReposted'] ?? false,
              coAuthorIds: data['coAuthorIds'] != null
                  ? List<String>.from(data['coAuthorIds']!)
                  : null,
              coAuthorUsernames: data['coAuthorUsernames'] != null
                  ? List<String>.from(data['coAuthorUsernames']!)
                  : null,
              coAuthorAvatars: data['coAuthorAvatars'] != null
                  ? List<String>.from(data['coAuthorAvatars']!)
                  : null,
              mentionedUsers: data['mentionedUsers'] != null
                  ? List<String>.from(data['mentionedUsers']!)
                  : null,
              hashtags: data['hashtags'] != null
                  ? List<String>.from(data['hashtags']!)
                  : null,
              mediaTags: data['mediaTags'] != null
                  ? (data['mediaTags'] as List)
                        .map((tag) => MediaTag.fromJson(tag))
                        .toList()
                  : null,
              likedBy: data['likedBy'] != null
                  ? List<String>.from(data['likedBy']!)
                  : null,
              bookmarkedBy: data['bookmarkedBy'] != null
                  ? List<String>.from(data['bookmarkedBy']!)
                  : null,
              repostedBy: data['repostedBy'] != null
                  ? List<String>.from(data['repostedBy']!)
                  : null,
              isPublic: data['isPublic'] ?? true,
              visibility: data['visibility'] ?? 'public',
              isArchived: data['isArchived'] ?? false,
              isReported: data['isReported'] ?? false,
              viewCount: data['viewCount'],
              shareCount: data['shareCount'],
              lastEditedAt: data['lastEditedAt'] != null
                  ? (data['lastEditedAt'] as Timestamp).toDate()
                  : null,
              editedBy: data['editedBy'],
            );
          })
          .whereType<Post>()
          .toList();

      // Apply smart filtering to only include valid posts
      final validPosts = PostValidationUtils.filterValidPosts(posts);

      // Filter by status in memory (until composite index is created)
      final activePosts = validPosts
          .where((post) => post.status == 'active')
          .toList();

      // Filter out archived posts in memory if not requested
      final filteredPosts = includeArchived
          ? activePosts
          : activePosts.where((post) => !post.isArchived).toList();

      // Posts are already sorted by Firestore query, but ensure consistency
      // by sorting again in memory as a fallback
      filteredPosts.sort((a, b) => b.timestamp.compareTo(a.timestamp));

      debugPrint('📊 Profile posts fetched: ${filteredPosts.length} posts');
      debugPrint(
        '📅 First post timestamp: ${filteredPosts.isNotEmpty ? filteredPosts.first.timestamp : 'No posts'}',
      );
      debugPrint(
        '📅 Last post timestamp: ${filteredPosts.isNotEmpty ? filteredPosts.last.timestamp : 'No posts'}',
      );

      return filteredPosts;
    } catch (e) {
      debugPrint('❌ Error fetching user posts: $e');
      throw Exception('Failed to fetch user posts: ${e.toString()}');
    }
  }

  // Get followers list
  Future<List<ProfileModel>> getFollowers(String userId) async {
    try {
      // This would typically fetch from Firestore
      // For now, return empty list - implement based on your followers structure
      return [];
    } catch (e) {
      throw Exception('Failed to fetch followers: ${e.toString()}');
    }
  }

  // Get following list
  Future<List<ProfileModel>> getFollowing(String userId) async {
    try {
      // This would typically fetch from Firestore
      // For now, return empty list - implement based on your following structure
      return [];
    } catch (e) {
      throw Exception('Failed to fetch following: ${e.toString()}');
    }
  }

  // Create default profile for new users
  ProfileModel _createDefaultProfile(User user) {
    return ProfileModel(
      id: user.uid,
      username: user.email?.split('@')[0] ?? 'user',
      name: user.displayName ?? 'User',
      bio: '',
      profilePictureUrl: '',
      postCount: 0,
      followerCount: 0,
      followingCount: 0,
      isVerified: false,
      isBillionaire: false,
      isAdmin: false,
      isBusinessAccount: false,
      businessName: '',
      businessCategory: '',
      website: '',
      createdAt: DateTime.now(),
    );
  }

  // Get profile by user ID (compatibility method)
  Future<ProfileModel> getProfile({String? userId}) async {
    try {
      if (userId != null) {
        final profile = await getUserProfile(userId);
        if (profile != null) {
          return profile;
        }
      }

      final currentProfile = await getCurrentUserProfile();
      if (currentProfile != null) {
        return currentProfile;
      }

      throw Exception('Profile not found');
    } catch (e) {
      throw Exception('Failed to fetch profile: ${e.toString()}');
    }
  }

  // Update profile (compatibility method)
  Future<bool> updateProfile(ProfileModel profile) async {
    try {
      await updateProfileData({
        'name': profile.name,
        'username': profile.username,
        'bio': profile.bio,
        'profilePictureUrl': profile.profilePictureUrl,
        'bannerImageUrl': profile.bannerImageUrl,
        'website': profile.website,
        'location': profile.location,
        'phone': profile.phone,
        'isPrivate': profile.isPrivate,
        'allowMessages': profile.allowMessages,
        'showActivityStatus': profile.showActivityStatus,
        'businessName': profile.businessName,
        'businessCategory': profile.businessCategory,
        'updatedAt': DateTime.now(),
      });
      return true;
    } catch (e) {
      return false;
    }
  }

  // Upload profile image (compatibility method)
  Future<String?> uploadProfileImage(
    String imagePath, {
    bool isBanner = false,
  }) async {
    try {
      debugPrint('📤 Starting profile image upload...');
      debugPrint('  - Image path: $imagePath');
      debugPrint('  - Is banner: $isBanner');

      final file = File(imagePath);
      final result = await uploadProfileImageFile(file);

      debugPrint('✅ Profile image upload completed: $result');
      return result;
    } catch (e) {
      debugPrint('❌ Profile image upload failed: $e');
      return null;
    }
  }

  // Get close friends (compatibility method)
  Future<Set<String>> getCloseFriends() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return <String>{};
      }

      // For now, return empty set - implement based on your close friends structure
      // This would typically fetch from a separate collection or subcollection
      return <String>{};
    } catch (e) {
      return <String>{};
    }
  }

  // Update close friends (compatibility method)
  Future<void> updateCloseFriends(Set<String> closeFriendIds) async {
    await updateCloseFriendsList(closeFriendIds.toList());
  }

  // Get profile posts (compatibility method)
  Future<List<Post>> getProfilePosts(String userId) async {
    return getUserPosts(userId);
  }

  // Get pinned posts for a user
  Future<List<String>> getPinnedPosts(String userId) async {
    try {
      final doc = await FirebaseFirestore.instance
          .collection('users')
          .doc(userId)
          .get();

      if (doc.exists) {
        final data = doc.data();
        final pinnedPosts = data?['pinnedPosts'] as List<dynamic>?;
        return pinnedPosts?.cast<String>() ?? [];
      }
      return [];
    } catch (e) {
      debugPrint('Error fetching pinned posts: $e');
      return [];
    }
  }

  // Add a post to pinned posts
  Future<bool> pinPost(String userId, String postId) async {
    try {
      final pinnedPosts = await getPinnedPosts(userId);

      // Check if already pinned
      if (pinnedPosts.contains(postId)) {
        return true; // Already pinned
      }

      // Check if we can add more (max 3)
      if (pinnedPosts.length >= 3) {
        return false; // Cannot pin more than 3 posts
      }

      // Add to pinned posts
      pinnedPosts.add(postId);

      await FirebaseFirestore.instance.collection('users').doc(userId).update({
        'pinnedPosts': pinnedPosts,
      });

      return true;
    } catch (e) {
      debugPrint('Error pinning post: $e');
      return false;
    }
  }

  // Remove a post from pinned posts
  Future<bool> unpinPost(String userId, String postId) async {
    try {
      final pinnedPosts = await getPinnedPosts(userId);

      if (!pinnedPosts.contains(postId)) {
        return true; // Not pinned
      }

      pinnedPosts.remove(postId);

      await FirebaseFirestore.instance.collection('users').doc(userId).update({
        'pinnedPosts': pinnedPosts,
      });

      return true;
    } catch (e) {
      debugPrint('Error unpinning post: $e');
      return false;
    }
  }

  // Get posts with pinned status
  Future<List<Post>> getUserPostsWithPinnedStatus(String userId) async {
    try {
      final posts = await getUserPosts(userId);
      final pinnedPostIds = await getPinnedPosts(userId);

      debugPrint('📌 Pinned post IDs: $pinnedPostIds');

      // Apply smart filtering to only include valid posts
      final validPosts = PostValidationUtils.filterValidPosts(posts);

      // Create new posts with pinned status
      final updatedPosts = validPosts.map((post) {
        final isPinned = pinnedPostIds.contains(post.id);
        if (isPinned) {
          debugPrint('📌 Post ${post.id} is pinned');
        }
        return post.copyWith(isPinned: isPinned);
      }).toList();

      // Sort: pinned posts first, then by timestamp (newest first)
      updatedPosts.sort((a, b) {
        // First, sort by pinned status
        if (a.isPinned && !b.isPinned) return -1;
        if (!a.isPinned && b.isPinned) return 1;

        // Then, sort by timestamp (newest first)
        return b.timestamp.compareTo(a.timestamp);
      });

      debugPrint('📊 Posts with pinned status: ${updatedPosts.length} total');
      debugPrint(
        '📌 Pinned posts: ${updatedPosts.where((p) => p.isPinned).length}',
      );
      debugPrint(
        '📅 First post timestamp: ${updatedPosts.isNotEmpty ? updatedPosts.first.timestamp : 'No posts'}',
      );
      debugPrint(
        '📅 Last post timestamp: ${updatedPosts.isNotEmpty ? updatedPosts.last.timestamp : 'No posts'}',
      );

      return updatedPosts;
    } catch (e) {
      debugPrint('❌ Error fetching posts with pinned status: $e');
      return await getUserPosts(userId);
    }
  }

  // Get user posts as a stream for real-time updates
  Stream<List<Post>> getUserPostsStream(String userId) {
    return FirebaseFirestore.instance
        .collection('posts')
        .where('userId', isEqualTo: userId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map((doc) {
                final data = doc.data() as Map<String, dynamic>?;
                if (data == null) return null;

                return Post(
                  id: doc.id,
                  userId: data['userId'] ?? '',
                  username: data['username'] ?? '',
                  userAvatarUrl: data['userAvatarUrl'] ?? '',
                  userRole: data['userRole'],
                  isVerified: data['isVerified'] ?? false,
                  mediaType: MediaType.values.firstWhere(
                    (e) => e.name == (data['mediaType'] ?? 'image'),
                    orElse: () => MediaType.image,
                  ),
                  mediaUrl: data['mediaUrl'] ?? '',
                  caption: data['caption'] ?? '',
                  location: data['location'],
                  locationId: data['locationId'],
                  likeCount: data['likeCount'] ?? 0,
                  commentCount: data['commentCount'] ?? 0,
                  timestamp:
                      (data['createdAt'] as Timestamp?)?.toDate() ??
                      DateTime.now(),
                  isLiked: data['isLiked'] ?? false,
                  isBookmarked: data['isBookmarked'] ?? false,
                  isReposted: data['isReposted'] ?? false,
                  coAuthorIds: data['coAuthorIds'] != null
                      ? List<String>.from(data['coAuthorIds']!)
                      : null,
                  coAuthorUsernames: data['coAuthorUsernames'] != null
                      ? List<String>.from(data['coAuthorUsernames']!)
                      : null,
                  coAuthorAvatars: data['coAuthorAvatars'] != null
                      ? List<String>.from(data['coAuthorAvatars']!)
                      : null,
                  mentionedUsers: data['mentionedUsers'] != null
                      ? List<String>.from(data['mentionedUsers']!)
                      : null,
                  hashtags: data['hashtags'] != null
                      ? List<String>.from(data['hashtags']!)
                      : null,
                  mediaTags: data['mediaTags'] != null
                      ? (data['mediaTags'] as List)
                            .map((tag) => MediaTag.fromJson(tag))
                            .toList()
                      : null,
                  likedBy: data['likedBy'] != null
                      ? List<String>.from(data['likedBy']!)
                      : null,
                  bookmarkedBy: data['bookmarkedBy'] != null
                      ? List<String>.from(data['bookmarkedBy']!)
                      : null,
                  repostedBy: data['repostedBy'] != null
                      ? List<String>.from(data['repostedBy']!)
                      : null,
                  isPublic: data['isPublic'] ?? true,
                  visibility: data['visibility'] ?? 'public',
                  isArchived: data['isArchived'] ?? false,
                  isReported: data['isReported'] ?? false,
                  viewCount: data['viewCount'],
                  shareCount: data['shareCount'],
                  lastEditedAt: data['lastEditedAt'] != null
                      ? (data['lastEditedAt'] as Timestamp).toDate()
                      : null,
                  editedBy: data['editedBy'],
                );
              })
              .whereType<Post>()
              .where((post) {
                // Filter in memory to only include active posts (or posts without status for backward compatibility)
                // We need to check the raw data since Post model doesn't have status field
                try {
                  final doc = snapshot.docs
                      .cast<QueryDocumentSnapshot<Map<String, dynamic>>>()
                      .firstWhere((doc) => doc.id == post.id);

                  final data = doc.data();
                  final status = data['status'];
                  final isActiveStatus = status == 'active' || status == null;

                  // Apply smart filtering to only show valid posts
                  final isValidPost = PostValidationUtils.isValidPost(post);

                  return isActiveStatus && isValidPost;
                } catch (e) {
                  // If document not found, exclude the post
                  debugPrint('Document not found for post ${post.id}: $e');
                  return false;
                }
              })
              .toList(),
        );
  }

  // Get user posts with pinned status as a stream for real-time updates
  Stream<List<Post>> getUserPostsWithPinnedStatusStream(String userId) {
    return getUserPostsStream(userId).asyncMap((posts) async {
      final pinnedPostIds = await getPinnedPosts(userId);

      debugPrint('📌 Pinned post IDs: $pinnedPostIds');

      // Create new posts with pinned status
      final updatedPosts = posts.map((post) {
        final isPinned = pinnedPostIds.contains(post.id);
        if (isPinned) {
          debugPrint('📌 Post ${post.id} is pinned');
        }
        return post.copyWith(isPinned: isPinned);
      }).toList();

      // Sort: pinned posts first, then by timestamp (newest first)
      updatedPosts.sort((a, b) {
        // First, sort by pinned status
        if (a.isPinned && !b.isPinned) return -1;
        if (!a.isPinned && b.isPinned) return 1;

        // Then, sort by timestamp (newest first)
        return b.timestamp.compareTo(a.timestamp);
      });

      debugPrint('📊 Posts with pinned status: ${updatedPosts.length} total');
      debugPrint(
        '📌 Pinned posts: ${updatedPosts.where((p) => p.isPinned).length}',
      );

      return updatedPosts;
    });
  }

  /// --- PROFILE VERIFICATION ---

  Future<void> createVerification(
    String userId,
    ProfileVerificationModel verification,
  ) async {
    final ref = FirebaseFirestore.instance
        .collection('users')
        .doc(userId)
        .collection('verifications')
        .doc(verification.id);
    await ref.set(verification.toJson());
  }

  Future<List<ProfileVerificationModel>> getVerifications(String userId) async {
    final ref = FirebaseFirestore.instance
        .collection('users')
        .doc(userId)
        .collection('verifications');
    final snapshot = await ref.get();
    return snapshot.docs
        .map((doc) => ProfileVerificationModel.fromJson(doc.data()))
        .toList();
  }

  Future<void> updateVerification(
    String userId,
    ProfileVerificationModel verification,
  ) async {
    final ref = FirebaseFirestore.instance
        .collection('users')
        .doc(userId)
        .collection('verifications')
        .doc(verification.id);
    await ref.update(verification.toJson());
  }

  Future<void> deleteVerification(String userId, String verificationId) async {
    final ref = FirebaseFirestore.instance
        .collection('users')
        .doc(userId)
        .collection('verifications')
        .doc(verificationId);
    await ref.delete();
  }

  /// --- PROFILE ANALYTICS ---

  Future<ProfileAnalyticsModel?> getLatestAnalytics(String userId) async {
    final ref = FirebaseFirestore.instance
        .collection('users')
        .doc(userId)
        .collection('analytics')
        .orderBy('date', descending: true)
        .limit(1);
    final snapshot = await ref.get();
    if (snapshot.docs.isEmpty) return null;
    return ProfileAnalyticsModel.fromJson(snapshot.docs.first.data());
  }

  Future<void> addAnalyticsEntry(
    String userId,
    ProfileAnalyticsModel analytics,
  ) async {
    final ref = FirebaseFirestore.instance
        .collection('users')
        .doc(userId)
        .collection('analytics')
        .doc(analytics.id);
    await ref.set(analytics.toJson());
  }

  Future<List<ProfileAnalyticsModel>> getAnalyticsHistory(
    String userId, {
    int limit = 30,
  }) async {
    final ref = FirebaseFirestore.instance
        .collection('users')
        .doc(userId)
        .collection('analytics')
        .orderBy('date', descending: true)
        .limit(limit);
    final snapshot = await ref.get();
    return snapshot.docs
        .map((doc) => ProfileAnalyticsModel.fromJson(doc.data()))
        .toList();
  }

  /// Migration method to fix posts without status field
  Future<void> fixPostsWithoutStatus(String userId) async {
    try {
      // Check if user is authenticated
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null || currentUser.uid != userId) {
        debugPrint('❌ Migration skipped: User not authenticated or not owner');
        return;
      }

      debugPrint(
        '🔧 Starting migration: Adding status field to posts without it',
      );

      // Get all posts for the user (without status filter)
      final snapshot = await FirebaseFirestore.instance
          .collection('posts')
          .where('userId', isEqualTo: userId)
          .get();

      debugPrint('📊 Found ${snapshot.docs.length} total posts for user');

      // Debug: Show detailed information about each post
      for (int i = 0; i < snapshot.docs.length; i++) {
        final doc = snapshot.docs[i];
        final data = doc.data();
        final createdAt = data['createdAt'] as Timestamp?;
        final caption = data['caption'] as String?;
        final status = data['status'];
        final mediaUrl = data['mediaUrl'] as String?;
        debugPrint('📝 Post ${i + 1}/${snapshot.docs.length} - ID: ${doc.id}:');
        debugPrint('   - Created: ${createdAt?.toDate()}');
        debugPrint(
          '   - Caption: ${caption?.substring(0, math.min(50, caption.length)) ?? 'No caption'}...',
        );
        debugPrint('   - Status: $status');
        debugPrint('   - UserId: ${data['userId']}');
        debugPrint(
          '   - MediaUrl: ${mediaUrl?.substring(0, math.min(60, mediaUrl.length)) ?? 'No media'}...',
        );
      }

      int updatedCount = 0;
      final batch = FirebaseFirestore.instance.batch();

      for (final doc in snapshot.docs) {
        final data = doc.data();

        // Check if post doesn't have status field or has null/empty status
        if (!data.containsKey('status') ||
            data['status'] == null ||
            data['status'] == '') {
          // Add status field to posts that don't have it
          batch.update(doc.reference, {
            'status': 'active',
            'isDeleted': false,
            'updatedAt': FieldValue.serverTimestamp(),
          });
          updatedCount++;
          debugPrint('🔧 Updating post ${doc.id} to add status field');
        }
      }

      if (updatedCount > 0) {
        try {
          await batch.commit();
          debugPrint(
            '✅ Migration complete: Updated $updatedCount posts with status field',
          );
        } catch (e) {
          debugPrint('❌ Migration batch commit failed: $e');
          // Try individual updates if batch fails
          debugPrint('🔄 Attempting individual post updates...');
          int individualUpdates = 0;

          for (final doc in snapshot.docs) {
            final data = doc.data();
            if (!data.containsKey('status') ||
                data['status'] == null ||
                data['status'] == '') {
              try {
                await doc.reference.update({
                  'status': 'active',
                  'isDeleted': false,
                  'updatedAt': FieldValue.serverTimestamp(),
                });
                individualUpdates++;
              } catch (individualError) {
                debugPrint(
                  '❌ Failed to update post ${doc.id}: $individualError',
                );
              }
            }
          }

          debugPrint(
            '✅ Individual updates complete: $individualUpdates posts updated',
          );
        }
      } else {
        debugPrint('✅ Migration complete: No posts needed status field update');
      }

      // Always recalculate the user's post count to apply smart filtering
      debugPrint('🔄 Recalculating post count with smart filtering...');
      await _recalculatePostCount(userId);
    } catch (e) {
      debugPrint('❌ Error during post status migration: $e');
      // Don't rethrow - allow the app to continue functioning
    }
  }

  /// Public method to recalculate post count (for manual triggering)
  Future<void> recalculatePostCount(String userId) async {
    await _recalculatePostCount(userId);
  }

  /// Recalculate follower and following counts from actual relationships
  Future<void> recalculateFollowerCounts(String userId) async {
    try {
      debugPrint(
        '🔢 Recalculating follower/following counts for user: $userId',
      );

      // Count actual followers
      final followersSnapshot = await FirebaseFirestore.instance
          .collection('follows')
          .where('followingId', isEqualTo: userId)
          .where('isActive', isEqualTo: true)
          .get();

      // Count actual following
      final followingSnapshot = await FirebaseFirestore.instance
          .collection('follows')
          .where('followerId', isEqualTo: userId)
          .where('isActive', isEqualTo: true)
          .get();

      final actualFollowerCount = followersSnapshot.docs.length;
      final actualFollowingCount = followingSnapshot.docs.length;

      debugPrint(
        '📊 Actual counts - Followers: $actualFollowerCount, Following: $actualFollowingCount',
      );

      // Update user document with accurate counts
      await FirebaseFirestore.instance.collection('users').doc(userId).update({
        'followerCount': actualFollowerCount,
        'followingCount': actualFollowingCount,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Clear profile cache to force UI refresh with new counts
      try {
        final cacheService = getIt<CacheService>();
        await cacheService.removeData('user_profile_$userId');
        debugPrint('🗑️ Cleared profile cache for user: $userId');
      } catch (e) {
        debugPrint('⚠️ Could not clear profile cache: $e');
      }

      debugPrint('✅ Follower/following counts updated successfully');
    } catch (e) {
      debugPrint('❌ Error recalculating follower/following counts: $e');
      rethrow;
    }
  }

  /// Recalculate and update user's post count
  Future<void> _recalculatePostCount(String userId) async {
    try {
      // Check if user is authenticated and is the owner
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null || currentUser.uid != userId) {
        debugPrint(
          '❌ Post count recalculation skipped: User not authenticated or not owner',
        );
        return;
      }

      debugPrint('🔢 Recalculating post count for user: $userId');

      // Count active posts (avoiding composite index requirement)
      final snapshot = await FirebaseFirestore.instance
          .collection('posts')
          .where('userId', isEqualTo: userId)
          .get();

      // Filter in memory to count only active and valid posts
      debugPrint('🔍 Analyzing ${snapshot.docs.length} posts for counting:');

      final validPosts = <DocumentSnapshot>[];

      for (final doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>?;
        if (data == null) {
          debugPrint('   ❌ Post ${doc.id}: No data');
          continue;
        }

        // Check status
        final status = data['status'];
        final isActiveStatus = status == 'active' || status == null;

        // Check if post data is valid using raw Firestore data
        final isValidData = PostValidationUtils.isValidPostData(data);

        final isValid = isActiveStatus && isValidData;
        final createdAt = data['createdAt'] as Timestamp?;
        final caption = data['caption'] as String?;

        debugPrint('   ${isValid ? '✅' : '❌'} Post ${doc.id}:');
        debugPrint(
          '      - Status: $status (${isActiveStatus ? 'ACTIVE' : 'INACTIVE'})',
        );
        debugPrint('      - Created: ${createdAt?.toDate()}');
        debugPrint(
          '      - Caption: ${caption != null ? caption.substring(0, math.min(30, caption.length)) : 'No caption'}...',
        );
        debugPrint(
          '      - Valid: ${isValidData ? 'YES' : 'NO'} (${isValid ? 'COUNTED' : 'EXCLUDED'})',
        );

        if (isValid) {
          validPosts.add(doc);
        }
      }

      final actualPostCount = validPosts.length;
      debugPrint(
        '📊 Final count: $actualPostCount valid posts out of ${snapshot.docs.length} total',
      );

      // Update user's post count
      try {
        await FirebaseFirestore.instance.collection('users').doc(userId).update(
          {
            'postCount': actualPostCount,
            'updatedAt': FieldValue.serverTimestamp(),
          },
        );

        debugPrint('✅ Post count updated to: $actualPostCount');
      } catch (updateError) {
        debugPrint('❌ Error updating user post count: $updateError');
        // Don't rethrow - the count calculation was successful even if update failed
        debugPrint(
          'ℹ️ Post count calculation completed but database update failed',
        );
      }

      // Clear profile cache to force UI refresh with new count
      try {
        final cacheService = getIt<CacheService>();
        await cacheService.removeData('user_profile_$userId');
        debugPrint('🗑️ Cleared profile cache for user: $userId');
      } catch (e) {
        debugPrint('⚠️ Could not clear profile cache: $e');
      }
    } catch (e) {
      debugPrint('❌ Error recalculating post count: $e');
      // Don't rethrow - allow the app to continue functioning
    }
  }
}
