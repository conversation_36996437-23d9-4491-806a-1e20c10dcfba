import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/core/services/analytics_service.dart';

/// Service for managing tagged posts functionality
class TaggedPostsService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final AnalyticsService _analyticsService = getIt<AnalyticsService>();

  /// Get posts where the user is tagged
  Future<List<Post>> getTaggedPosts(String userId) async {
    try {
      debugPrint('🏷️ Getting tagged posts for user: $userId');

      // Query posts where user is in taggedUserIds or mentionedUsers
      final taggedQuery = await _firestore
          .collection('posts')
          .where('taggedUserIds', arrayContains: userId)
          .where('status', isEqualTo: 'active')
          .orderBy('createdAt', descending: true)
          .limit(50)
          .get();

      final mentionedQuery = await _firestore
          .collection('posts')
          .where('mentionedUsers', arrayContains: userId)
          .where('status', isEqualTo: 'active')
          .orderBy('createdAt', descending: true)
          .limit(50)
          .get();

      // Combine and deduplicate results
      final allDocs = <QueryDocumentSnapshot<Map<String, dynamic>>>[];
      final seenIds = <String>{};

      for (final doc in [...taggedQuery.docs, ...mentionedQuery.docs]) {
        if (!seenIds.contains(doc.id)) {
          allDocs.add(doc);
          seenIds.add(doc.id);
        }
      }

      // Sort by timestamp
      allDocs.sort((a, b) {
        final aTime =
            (a.data()['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now();
        final bTime =
            (b.data()['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now();
        return bTime.compareTo(aTime);
      });

      final posts = <Post>[];
      final currentUser = _auth.currentUser;

      for (final doc in allDocs) {
        final data = doc.data();

        // Check if current user has bookmarked this post
        final isBookmarked = currentUser != null
            ? await _checkUserBookmark(doc.id, currentUser.uid)
            : false;

        // Check if current user has liked this post
        final isLiked = currentUser != null
            ? await _checkUserLike(doc.id, currentUser.uid)
            : false;

        final post = Post(
          id: doc.id,
          userId: data['userId'] ?? '',
          username: data['username'] ?? '',
          userAvatarUrl: data['userAvatarUrl'] ?? '',
          userRole: data['userRole'],
          isVerified: data['isVerified'],
          mediaType: MediaType.values.firstWhere(
            (e) => e.name == (data['mediaType'] ?? 'image'),
            orElse: () => MediaType.image,
          ),
          mediaUrl: data['mediaUrl'] ?? '',
          caption: data['caption'] ?? '',
          location: data['location'],
          locationId: data['locationId'],
          likeCount: data['likeCount'] ?? 0,
          commentCount: data['commentCount'] ?? 0,
          timestamp:
              (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
          isLiked: isLiked,
          isBookmarked: isBookmarked,
          isReposted: data['isReposted'] ?? false,
          coAuthorIds: data['coAuthorIds']?.cast<String>(),
          coAuthorUsernames: data['coAuthorUsernames']?.cast<String>(),
          coAuthorAvatars: data['coAuthorAvatars']?.cast<String>(),
          mentionedUsers: data['mentionedUsers']?.cast<String>(),
          hashtags: data['hashtags']?.cast<String>(),
          mediaTags: data['mediaTags']
              ?.map<MediaTag>((tag) => MediaTag.fromJson(tag))
              .toList(),
          taggedUserIds: data['taggedUserIds']?.cast<String>(),
          visibility: data['visibility'] ?? 'public',
          isArchived: data['isArchived'] ?? false,
          isReported: data['isReported'] ?? false,
          isPinned: data['isPinned'] ?? false,
          isDeleted: data['isDeleted'] ?? false,
          isFlagged: data['isFlagged'] ?? false,
          status: data['status'] ?? 'active',
          viewCount: data['viewCount'],
          shareCount: data['shareCount'],
          repostCount: data['repostCount'],
          saveCount: data['saveCount'],
          trending: data['trending'] ?? false,
          trendingScore: data['trendingScore'],
        );

        posts.add(post);
      }

      debugPrint('🏷️ Found ${posts.length} tagged posts for user: $userId');

      // Log analytics
      await _analyticsService.logEventSafely(
        eventName: 'tagged_posts_viewed',
        parameters: {
          'user_id': userId,
          'post_count': posts.length,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      return posts;
    } catch (e) {
      debugPrint('❌ Error getting tagged posts: $e');
      throw Exception('Failed to get tagged posts: ${e.toString()}');
    }
  }

  /// Stream of tagged posts for real-time updates
  Stream<List<Post>> getTaggedPostsStream(String userId) {
    try {
      debugPrint('🏷️ Setting up tagged posts stream for user: $userId');

      // Create a stream that combines both tagged and mentioned posts
      return _firestore
          .collection('posts')
          .where('taggedUserIds', arrayContains: userId)
          .where('status', isEqualTo: 'active')
          .orderBy('createdAt', descending: true)
          .limit(50)
          .snapshots()
          .asyncMap((snapshot) async {
            final posts = <Post>[];
            final currentUser = _auth.currentUser;

            for (final doc in snapshot.docs) {
              final data = doc.data();

              // Check user interactions
              final isBookmarked = currentUser != null
                  ? await _checkUserBookmark(doc.id, currentUser.uid)
                  : false;

              final isLiked = currentUser != null
                  ? await _checkUserLike(doc.id, currentUser.uid)
                  : false;

              final post = Post(
                id: doc.id,
                userId: data['userId'] ?? '',
                username: data['username'] ?? '',
                userAvatarUrl: data['userAvatarUrl'] ?? '',
                userRole: data['userRole'],
                isVerified: data['isVerified'],
                mediaType: MediaType.values.firstWhere(
                  (e) => e.name == (data['mediaType'] ?? 'image'),
                  orElse: () => MediaType.image,
                ),
                mediaUrl: data['mediaUrl'] ?? '',
                caption: data['caption'] ?? '',
                location: data['location'],
                locationId: data['locationId'],
                likeCount: data['likeCount'] ?? 0,
                commentCount: data['commentCount'] ?? 0,
                timestamp:
                    (data['createdAt'] as Timestamp?)?.toDate() ??
                    DateTime.now(),
                isLiked: isLiked,
                isBookmarked: isBookmarked,
                isReposted: data['isReposted'] ?? false,
                coAuthorIds: data['coAuthorIds']?.cast<String>(),
                coAuthorUsernames: data['coAuthorUsernames']?.cast<String>(),
                coAuthorAvatars: data['coAuthorAvatars']?.cast<String>(),
                mentionedUsers: data['mentionedUsers']?.cast<String>(),
                hashtags: data['hashtags']?.cast<String>(),
                mediaTags: data['mediaTags']
                    ?.map<MediaTag>((tag) => MediaTag.fromJson(tag))
                    .toList(),
                taggedUserIds: data['taggedUserIds']?.cast<String>(),
                visibility: data['visibility'] ?? 'public',
                isArchived: data['isArchived'] ?? false,
                isReported: data['isReported'] ?? false,
                isPinned: data['isPinned'] ?? false,
                isDeleted: data['isDeleted'] ?? false,
                isFlagged: data['isFlagged'] ?? false,
                status: data['status'] ?? 'active',
                viewCount: data['viewCount'],
                shareCount: data['shareCount'],
                repostCount: data['repostCount'],
                saveCount: data['saveCount'],
                trending: data['trending'] ?? false,
                trendingScore: data['trendingScore'],
              );

              posts.add(post);
            }

            return posts;
          });
    } catch (e) {
      debugPrint('❌ Error setting up tagged posts stream: $e');
      throw Exception('Failed to setup tagged posts stream: ${e.toString()}');
    }
  }

  /// Check if user has bookmarked a post
  Future<bool> _checkUserBookmark(String postId, String userId) async {
    try {
      final doc = await _firestore
          .collection('users')
          .doc(userId)
          .collection('bookmarks')
          .doc(postId)
          .get();
      return doc.exists;
    } catch (e) {
      debugPrint('❌ Error checking bookmark: $e');
      return false;
    }
  }

  /// Check if user has liked a post
  Future<bool> _checkUserLike(String postId, String userId) async {
    try {
      final doc = await _firestore
          .collection('posts')
          .doc(postId)
          .collection('likes')
          .doc(userId)
          .get();
      return doc.exists;
    } catch (e) {
      debugPrint('❌ Error checking like: $e');
      return false;
    }
  }

  /// Remove user from tagged posts (privacy feature)
  Future<void> removeUserFromTaggedPost(String postId, String userId) async {
    try {
      debugPrint('🏷️ Removing user $userId from tagged post $postId');

      await _firestore.collection('posts').doc(postId).update({
        'taggedUserIds': FieldValue.arrayRemove([userId]),
      });

      // Log analytics
      await _analyticsService.logEventSafely(
        eventName: 'user_removed_from_tag',
        parameters: {
          'post_id': postId,
          'user_id': userId,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      debugPrint('✅ User removed from tagged post successfully');
    } catch (e) {
      debugPrint('❌ Error removing user from tagged post: $e');
      throw Exception(
        'Failed to remove user from tagged post: ${e.toString()}',
      );
    }
  }

  /// Get tagged posts count for a user
  Future<int> getTaggedPostsCount(String userId) async {
    try {
      final taggedQuery = await _firestore
          .collection('posts')
          .where('taggedUserIds', arrayContains: userId)
          .where('status', isEqualTo: 'active')
          .count()
          .get();

      final mentionedQuery = await _firestore
          .collection('posts')
          .where('mentionedUsers', arrayContains: userId)
          .where('status', isEqualTo: 'active')
          .count()
          .get();

      return (taggedQuery.count ?? 0) + (mentionedQuery.count ?? 0);
    } catch (e) {
      debugPrint('❌ Error getting tagged posts count: $e');
      return 0;
    }
  }
}
