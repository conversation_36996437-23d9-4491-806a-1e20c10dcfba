import 'package:billionaires_social/features/profile/models/close_friends_group_model.dart';
import 'package:uuid/uuid.dart';

class CloseFriendsGroupService {
  final List<CloseFriendsGroup> _groups = [];
  final _uuid = const Uuid();

  Future<List<CloseFriendsGroup>> getGroups() async {
    await Future.delayed(const Duration(milliseconds: 200));
    return List<CloseFriendsGroup>.from(_groups);
  }

  Future<CloseFriendsGroup> createGroup({
    required String name,
    required String emoji,
    List<String>? memberIds,
    bool isStoryVisible = true,
  }) async {
    final group = CloseFriendsGroup(
      id: _uuid.v4(),
      name: name,
      emoji: emoji,
      memberIds: memberIds ?? [],
      isStoryVisible: isStoryVisible,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
    _groups.add(group);
    return group;
  }

  Future<CloseFriendsGroup> updateGroup(CloseFriendsGroup updated) async {
    await Future.delayed(const Duration(milliseconds: 100));
    final idx = _groups.indexWhere((g) => g.id == updated.id);
    if (idx != -1) {
      _groups[idx] = updated.copyWith(updatedAt: DateTime.now());
      return _groups[idx];
    }
    throw Exception('Group not found');
  }

  Future<void> deleteGroup(String groupId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    _groups.removeWhere((g) => g.id == groupId);
  }

  Future<void> addMember(String groupId, String userId) async {
    final idx = _groups.indexWhere((g) => g.id == groupId);
    if (idx != -1 && !_groups[idx].memberIds.contains(userId)) {
      _groups[idx] = _groups[idx].copyWith(
        memberIds: List<String>.from(_groups[idx].memberIds)..add(userId),
        updatedAt: DateTime.now(),
      );
    }
  }

  Future<void> removeMember(String groupId, String userId) async {
    final idx = _groups.indexWhere((g) => g.id == groupId);
    if (idx != -1 && _groups[idx].memberIds.contains(userId)) {
      _groups[idx] = _groups[idx].copyWith(
        memberIds: List<String>.from(_groups[idx].memberIds)..remove(userId),
        updatedAt: DateTime.now(),
      );
    }
  }

  Future<void> setStoryVisibility(String groupId, bool visible) async {
    final idx = _groups.indexWhere((g) => g.id == groupId);
    if (idx != -1) {
      _groups[idx] = _groups[idx].copyWith(
        isStoryVisible: visible,
        updatedAt: DateTime.now(),
      );
    }
  }
}
