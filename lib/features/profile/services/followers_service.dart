import 'package:billionaires_social/core/services/cache_service.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/features/profile/models/follower_model.dart';
import 'package:billionaires_social/features/profile/models/profile_model.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';

class FollowersService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Singleton pattern
  static final FollowersService _instance = FollowersService._internal();
  factory FollowersService() => _instance;
  FollowersService._internal();

  // Sample data for demonstration
  final List<FollowerModel> _followers = [
    FollowerModel(
      id: '1',
      username: 'alex_thompson',
      name: '<PERSON>',
      profilePictureUrl: 'https://example.com/avatar1.jpg',
      bio: 'Luxury lifestyle enthusiast',
      isVerified: true,
      accountType: AccountType.verified,
      gender: Gender.male,
      isFollowingBack: true,
      isMutual: true,
      followedAt: DateTime.now().subtract(const Duration(days: 30)),
      isMuted: false,
      isSelected: false,
      tags: ['luxury', 'lifestyle'],
    ),
    FollowerModel(
      id: '2',
      username: 'sarah_wilson',
      name: 'Sarah Wilson',
      profilePictureUrl: 'https://example.com/avatar2.jpg',
      bio: 'Fashion & Beauty Influencer',
      isVerified: true,
      accountType: AccountType.celebrity,
      gender: Gender.female,
      isFollowingBack: false,
      isMutual: false,
      followedAt: DateTime.now().subtract(const Duration(days: 15)),
      isMuted: false,
      isSelected: false,
      tags: ['fashion', 'beauty', 'influencer'],
    ),
    FollowerModel(
      id: '3',
      username: 'mike_billionaire',
      name: 'Mike Johnson',
      profilePictureUrl: 'https://example.com/avatar3.jpg',
      bio: 'Tech Entrepreneur & Investor',
      isVerified: true,
      accountType: AccountType.billionaire,
      gender: Gender.male,
      isFollowingBack: true,
      isMutual: true,
      followedAt: DateTime.now().subtract(const Duration(days: 7)),
      isMuted: false,
      isSelected: false,
      tags: ['billionaire', 'tech', 'entrepreneur'],
    ),
    FollowerModel(
      id: '4',
      username: 'luxury_brands',
      name: 'Luxury Brands Co.',
      profilePictureUrl: 'https://example.com/avatar4.jpg',
      bio: 'Premium luxury brand collection',
      isVerified: true,
      accountType: AccountType.business,
      gender: Gender.other,
      isFollowingBack: false,
      isMutual: false,
      followedAt: DateTime.now().subtract(const Duration(days: 45)),
      isMuted: true,
      isSelected: false,
      tags: ['business', 'luxury', 'brands'],
    ),
    FollowerModel(
      id: '5',
      username: 'jessica_celeb',
      name: 'Jessica Parker',
      profilePictureUrl: 'https://example.com/avatar5.jpg',
      bio: 'Hollywood Actress',
      isVerified: true,
      accountType: AccountType.celebrity,
      gender: Gender.female,
      isFollowingBack: true,
      isMutual: true,
      followedAt: DateTime.now().subtract(const Duration(days: 3)),
      isMuted: false,
      isSelected: false,
      tags: ['celebrity', 'actress', 'hollywood'],
    ),
  ];

  final List<FollowingModel> _following = [
    FollowingModel(
      id: '1',
      username: 'alex_thompson',
      name: 'Alex Thompson',
      profilePictureUrl: 'https://example.com/avatar1.jpg',
      bio: 'Luxury lifestyle enthusiast',
      isVerified: true,
      accountType: AccountType.verified,
      gender: Gender.male,
      isFollowingBack: true,
      isMutual: true,
      followedAt: DateTime.now().subtract(const Duration(days: 30)),
      isMuted: false,
      isSelected: false,
      tags: ['luxury', 'lifestyle'],
    ),
    FollowingModel(
      id: '2',
      username: 'sarah_wilson',
      name: 'Sarah Wilson',
      profilePictureUrl: 'https://example.com/avatar2.jpg',
      bio: 'Fashion & Beauty Influencer',
      isVerified: true,
      accountType: AccountType.celebrity,
      gender: Gender.female,
      isFollowingBack: false,
      isMutual: false,
      followedAt: DateTime.now().subtract(const Duration(days: 15)),
      isMuted: false,
      isSelected: false,
      tags: ['fashion', 'beauty', 'influencer'],
    ),
    FollowingModel(
      id: '3',
      username: 'mike_billionaire',
      name: 'Mike Johnson',
      profilePictureUrl: 'https://example.com/avatar3.jpg',
      bio: 'Tech Entrepreneur & Investor',
      isVerified: true,
      accountType: AccountType.billionaire,
      gender: Gender.male,
      isFollowingBack: true,
      isMutual: true,
      followedAt: DateTime.now().subtract(const Duration(days: 7)),
      isMuted: false,
      isSelected: false,
      tags: ['billionaire', 'tech', 'entrepreneur'],
    ),
    FollowingModel(
      id: '4',
      username: 'luxury_brands',
      name: 'Luxury Brands Co.',
      profilePictureUrl: 'https://example.com/avatar4.jpg',
      bio: 'Premium luxury brand collection',
      isVerified: true,
      accountType: AccountType.business,
      gender: Gender.other,
      isFollowingBack: false,
      isMutual: false,
      followedAt: DateTime.now().subtract(const Duration(days: 45)),
      isMuted: true,
      isSelected: false,
      tags: ['business', 'luxury', 'brands'],
    ),
    FollowingModel(
      id: '5',
      username: 'jessica_celeb',
      name: 'Jessica Parker',
      profilePictureUrl: 'https://example.com/avatar5.jpg',
      bio: 'Hollywood Actress',
      isVerified: true,
      accountType: AccountType.celebrity,
      gender: Gender.female,
      isFollowingBack: true,
      isMutual: true,
      followedAt: DateTime.now().subtract(const Duration(days: 3)),
      isMuted: false,
      isSelected: false,
      tags: ['celebrity', 'actress', 'hollywood'],
    ),
    FollowingModel(
      id: '6',
      username: 'david_regular',
      name: 'David Smith',
      profilePictureUrl: 'https://example.com/avatar6.jpg',
      bio: 'Regular user',
      isVerified: false,
      accountType: AccountType.regular,
      gender: Gender.male,
      isFollowingBack: false,
      isMutual: false,
      followedAt: DateTime.now().subtract(const Duration(days: 60)),
      isMuted: false,
      isSelected: false,
      tags: [],
    ),
  ];

  // Get followers for a specific user
  Future<List<FollowerModel>> getFollowers(String userId) async {
    try {
      final followsSnapshot = await _firestore
          .collection('follows')
          .where('followingId', isEqualTo: userId)
          .where('isActive', isEqualTo: true)
          .orderBy('followedAt', descending: true)
          .get();

      final List<FollowerModel> followers = [];

      for (final followDoc in followsSnapshot.docs) {
        final followerId = followDoc.data()['followerId'] as String;
        final userProfile = await getUserProfile(followerId);

        if (userProfile != null) {
          // Check if the current user is following back
          final isFollowingBack = await _checkIfFollowing(userId, followerId);

          followers.add(
            FollowerModel(
              id: userProfile.id,
              username: userProfile.username,
              name: userProfile.name,
              profilePictureUrl: userProfile.profilePictureUrl,
              bio: userProfile.bio,
              isVerified: userProfile.isVerified,
              accountType: _mapAccountType(userProfile),
              gender: Gender.other, // Would need to be added to user profile
              isFollowingBack: isFollowingBack,
              isMutual: isFollowingBack,
              followedAt: (followDoc.data()['followedAt'] as Timestamp)
                  .toDate(),
              isMuted:
                  false, // Would need to be stored in a separate collection
              isSelected: false,
              tags: [],
            ),
          );
        }
      }

      return followers;
    } catch (e) {
      throw Exception('Failed to get followers: ${e.toString()}');
    }
  }

  // Get followers for a specific user with filtering
  Future<List<FollowerModel>> getFollowersWithFilter(
    String userId,
    FollowersFilter filter,
  ) async {
    final followers = await getFollowers(userId);
    return _applyFilter(followers, filter);
  }

  // Get users that a specific user is following
  Future<List<FollowerModel>> getFollowing(String userId) async {
    try {
      final followsSnapshot = await _firestore
          .collection('follows')
          .where('followerId', isEqualTo: userId)
          .where('isActive', isEqualTo: true)
          // Temporarily removed orderBy until Firestore indexes are built
          // .orderBy('followedAt', descending: true)
          .get();

      final List<FollowerModel> following = [];

      for (final followDoc in followsSnapshot.docs) {
        final followingId = followDoc.data()['followingId'] as String;
        final userProfile = await getUserProfile(followingId);

        if (userProfile != null) {
          // Check if the followed user is following back
          final isFollowingBack = await _checkIfFollowing(followingId, userId);

          following.add(
            FollowerModel(
              id: userProfile.id,
              username: userProfile.username,
              name: userProfile.name,
              profilePictureUrl: userProfile.profilePictureUrl,
              bio: userProfile.bio,
              isVerified: userProfile.isVerified,
              accountType: _mapAccountType(userProfile),
              gender: Gender.other, // Would need to be added to user profile
              isFollowingBack: isFollowingBack,
              isMutual: isFollowingBack,
              followedAt: (followDoc.data()['followedAt'] as Timestamp)
                  .toDate(),
              isMuted:
                  false, // Would need to be stored in a separate collection
              isSelected: false,
              tags: [],
            ),
          );
        }
      }

      // Sort by followedAt descending (client-side until indexes are built)
      following.sort((a, b) => b.followedAt.compareTo(a.followedAt));

      return following;
    } catch (e) {
      throw Exception('Failed to get following: ${e.toString()}');
    }
  }

  // Get users that a specific user is following with filtering
  Future<List<FollowingModel>> getFollowingWithFilter(
    String userId,
    FollowersFilter filter,
  ) async {
    // First get the following list as FollowerModel
    final followingAsFollowers = await getFollowing(userId);

    // Convert to FollowingModel (assuming they have similar structure)
    final following = followingAsFollowers
        .map(
          (follower) => FollowingModel(
            id: follower.id,
            username: follower.username,
            name: follower.name,
            profilePictureUrl: follower.profilePictureUrl,
            bio: follower.bio,
            isVerified: follower.isVerified,
            followedAt: follower.followedAt,
            isMutual: follower.isFollowingBack,
            isFollowingBack: follower.isFollowingBack,
            accountType: follower.accountType,
            gender: follower.gender,
            isMuted: follower.isMuted,
            isSelected: follower.isSelected,
            tags: follower.tags,
          ),
        )
        .toList();

    return _applyFollowingFilter(following, filter);
  }

  // Follow a user
  Future<void> followUser(String userIdToFollow) async {
    final currentUserId = _auth.currentUser?.uid;
    if (currentUserId == null) {
      throw Exception('User not logged in');
    }

    if (currentUserId == userIdToFollow) {
      throw Exception('You cannot follow yourself');
    }

    try {
      // Check if already following
      final existingFollow = await _firestore
          .collection('follows')
          .where('followerId', isEqualTo: currentUserId)
          .where('followingId', isEqualTo: userIdToFollow)
          .get();

      if (existingFollow.docs.isNotEmpty) {
        throw Exception('You are already following this user');
      }

      // Use transaction to ensure all operations succeed or fail together
      await _firestore.runTransaction((transaction) async {
        // Create follow relationship
        final followRef = _firestore.collection('follows').doc();
        transaction.set(followRef, {
          'followerId': currentUserId,
          'followingId': userIdToFollow,
          'followedAt': FieldValue.serverTimestamp(),
          'isActive': true,
        });

        // Update follower count for the user being followed
        final userToFollowRef = _firestore
            .collection('users')
            .doc(userIdToFollow);
        transaction.update(userToFollowRef, {
          'followerCount': FieldValue.increment(1),
          'updatedAt': FieldValue.serverTimestamp(),
        });

        // Update following count for the current user
        final currentUserRef = _firestore
            .collection('users')
            .doc(currentUserId);
        transaction.update(currentUserRef, {
          'followingCount': FieldValue.increment(1),
          'updatedAt': FieldValue.serverTimestamp(),
        });
      });

      // Clear profile caches to ensure fresh counts are displayed
      await _clearProfileCaches([currentUserId, userIdToFollow]);

      // Create notification for the user being followed (outside transaction)
      await _firestore.collection('notifications').add({
        'userId': userIdToFollow,
        'type': 'follow',
        'senderId': currentUserId,
        'title': 'New Follower',
        'body': 'Someone started following you',
        'timestamp': FieldValue.serverTimestamp(),
        'isRead': false,
        'data': {'followerId': currentUserId},
      });

      // Log analytics (outside transaction)
      await _logFollowAnalytics(currentUserId, userIdToFollow);
    } catch (e) {
      throw Exception('Failed to follow user: ${e.toString()}');
    }
  }

  // Unfollow a user
  Future<void> unfollowUser(String userIdToUnfollow) async {
    final currentUserId = _auth.currentUser?.uid;
    if (currentUserId == null) {
      throw Exception('User not logged in');
    }

    if (currentUserId == userIdToUnfollow) {
      throw Exception('You cannot unfollow yourself');
    }

    try {
      // Find the follow relationship
      final followQuery = await _firestore
          .collection('follows')
          .where('followerId', isEqualTo: currentUserId)
          .where('followingId', isEqualTo: userIdToUnfollow)
          .get();

      if (followQuery.docs.isEmpty) {
        throw Exception('You are not following this user');
      }

      // Use transaction to ensure all operations succeed or fail together
      await _firestore.runTransaction((transaction) async {
        // Delete all follow relationships (in case there are duplicates)
        for (final doc in followQuery.docs) {
          transaction.delete(doc.reference);
        }

        // Update follower count for the user being unfollowed
        final userToUnfollowRef = _firestore
            .collection('users')
            .doc(userIdToUnfollow);
        transaction.update(userToUnfollowRef, {
          'followerCount': FieldValue.increment(-1),
          'updatedAt': FieldValue.serverTimestamp(),
        });

        // Update following count for the current user
        final currentUserRef = _firestore
            .collection('users')
            .doc(currentUserId);
        transaction.update(currentUserRef, {
          'followingCount': FieldValue.increment(-1),
          'updatedAt': FieldValue.serverTimestamp(),
        });
      });

      // Clear profile caches to ensure fresh counts are displayed
      await _clearProfileCaches([currentUserId, userIdToUnfollow]);

      // Log analytics (outside transaction)
      await _logUnfollowAnalytics(currentUserId, userIdToUnfollow);
    } catch (e) {
      throw Exception('Failed to unfollow user: ${e.toString()}');
    }
  }

  // Public method to check if current user is following another user
  Future<bool> isFollowing(String userIdToCheck) async {
    final currentUserId = _auth.currentUser?.uid;
    if (currentUserId == null) return false;

    return _checkIfFollowing(currentUserId, userIdToCheck);
  }

  // Helper method to check if user A is following user B
  Future<bool> _checkIfFollowing(String followerId, String followingId) async {
    try {
      final followQuery = await _firestore
          .collection('follows')
          .where('followerId', isEqualTo: followerId)
          .where('followingId', isEqualTo: followingId)
          .get();

      return followQuery.docs.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  // Helper method to map user profile to account type
  AccountType _mapAccountType(ProfileModel profile) {
    if (profile.isBillionaire) return AccountType.billionaire;
    if (profile.isBusinessAccount) return AccountType.business;
    if (profile.isVerified && profile.followerCount > 10000) {
      return AccountType.celebrity;
    }
    return AccountType.regular;
  }

  // Helper method to get user profile
  Future<ProfileModel?> getUserProfile(String userId) async {
    try {
      final doc = await _firestore.collection('users').doc(userId).get();
      if (doc.exists) {
        final data = doc.data()!;
        return ProfileModel.fromJson({
          'id': doc.id,
          'username': data['username'] ?? '',
          'name': data['name'] ?? '',
          'profilePictureUrl': data['profilePictureUrl'] ?? '',
          'bio': data['bio'] ?? '',
          'postCount': data['postCount'] ?? 0,
          'followerCount': data['followerCount'] ?? 0,
          'followingCount': data['followingCount'] ?? 0,
          'isVerified': data['isVerified'] ?? false,
          'isBillionaire': data['isBillionaire'] ?? false,
          'isAdmin': data['isAdmin'] ?? false,
          'isBusinessAccount': data['isBusinessAccount'] ?? false,
          'businessName': data['businessName'] ?? '',
          'businessCategory': data['businessCategory'] ?? '',
          'website': data['website'] ?? '',
          'email': data['email'] ?? '',
          'createdAt': data['createdAt'] != null
              ? (data['createdAt'] as Timestamp).toDate().toIso8601String()
              : DateTime.now().toIso8601String(),
        });
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  // Helper method to log follow analytics
  Future<void> _logFollowAnalytics(
    String followerId,
    String followingId,
  ) async {
    try {
      await _firestore.collection('analytics').add({
        'type': 'follow',
        'followerId': followerId,
        'followingId': followingId,
        'timestamp': FieldValue.serverTimestamp(),
        'platform': 'mobile',
      });
    } catch (e) {
      // Analytics logging failure shouldn't break the main functionality
    }
  }

  // Helper method to log unfollow analytics
  Future<void> _logUnfollowAnalytics(
    String followerId,
    String followingId,
  ) async {
    try {
      await _firestore.collection('analytics').add({
        'type': 'unfollow',
        'followerId': followerId,
        'followingId': followingId,
        'timestamp': FieldValue.serverTimestamp(),
        'platform': 'mobile',
      });
    } catch (e) {
      // Analytics logging failure shouldn't break the main functionality
    }
  }

  // Get analytics with caching
  Future<FollowersAnalytics> getAnalytics() async {
    try {
      final currentUserId = _auth.currentUser?.uid;
      if (currentUserId == null) {
        return _getEmptyAnalytics();
      }

      // Get real data from Firestore in parallel for better performance
      final results = await Future.wait([
        getFollowers(currentUserId),
        getFollowing(currentUserId),
      ]);

      final followers = results[0];
      final following = results[1];

      // Calculate gender split for followers
      final maleFollowers = followers
          .where((f) => f.gender == Gender.male)
          .length;
      final femaleFollowers = followers
          .where((f) => f.gender == Gender.female)
          .length;
      final otherFollowers = followers
          .where((f) => f.gender == Gender.other)
          .length;

      final genderSplit = GenderSplit(
        male: maleFollowers,
        female: femaleFollowers,
        other: otherFollowers,
        malePercentage: followers.isNotEmpty
            ? (maleFollowers / followers.length) * 100
            : 0,
        femalePercentage: followers.isNotEmpty
            ? (femaleFollowers / followers.length) * 100
            : 0,
        otherPercentage: followers.isNotEmpty
            ? (otherFollowers / followers.length) * 100
            : 0,
      );

      // Calculate account type split for following
      final verifiedFollowing = following.where((f) => f.isVerified).length;
      final celebritiesFollowing = following
          .where((f) => f.accountType == AccountType.celebrity)
          .length;
      final businessesFollowing = following
          .where((f) => f.accountType == AccountType.business)
          .length;
      final billionairesFollowing = following
          .where((f) => f.accountType == AccountType.billionaire)
          .length;
      final regularFollowing = following
          .where((f) => f.accountType == AccountType.regular)
          .length;

      final accountTypeSplit = AccountTypeSplit(
        verified: verifiedFollowing,
        celebrities: celebritiesFollowing,
        businesses: businessesFollowing,
        billionaires: billionairesFollowing,
        regular: regularFollowing,
      );

      // Calculate mutual status split
      final mutualFollowing = following.where((f) => f.isMutual).length;
      final notFollowingBack = following
          .where((f) => !f.isFollowingBack)
          .length;
      final notFollowedBack = followers.where((f) => !f.isFollowingBack).length;

      final mutualStatusSplit = MutualStatusSplit(
        mutual: mutualFollowing,
        notFollowingBack: notFollowingBack,
        notFollowedBack: notFollowedBack,
      );

      final analytics = FollowersAnalytics(
        totalFollowers: followers.length,
        totalFollowing: following.length,
        notFollowingBack: notFollowingBack,
        verifiedAccountsFollowed: verifiedFollowing,
        billionairesFollowed: billionairesFollowing,
        celebritiesFollowed: celebritiesFollowing,
        businessesFollowed: businessesFollowing,
        genderSplit: genderSplit,
        accountTypeSplit: accountTypeSplit,
        mutualStatusSplit: mutualStatusSplit,
      );

      return analytics;
    } catch (e) {
      debugPrint('Error getting analytics: $e');
      return _getEmptyAnalytics();
    }
  }

  // Helper method to return empty analytics
  FollowersAnalytics _getEmptyAnalytics() {
    return FollowersAnalytics(
      totalFollowers: 0,
      totalFollowing: 0,
      notFollowingBack: 0,
      verifiedAccountsFollowed: 0,
      billionairesFollowed: 0,
      celebritiesFollowed: 0,
      businessesFollowed: 0,
      genderSplit: GenderSplit(
        male: 0,
        female: 0,
        other: 0,
        malePercentage: 0,
        femalePercentage: 0,
        otherPercentage: 0,
      ),
      accountTypeSplit: AccountTypeSplit(
        verified: 0,
        celebrities: 0,
        businesses: 0,
        billionaires: 0,
        regular: 0,
      ),
      mutualStatusSplit: MutualStatusSplit(
        mutual: 0,
        notFollowingBack: 0,
        notFollowedBack: 0,
      ),
    );
  }

  // Bulk actions
  Future<bool> performBulkAction({
    required List<String> userIds,
    required BulkAction action,
  }) async {
    await Future.delayed(const Duration(milliseconds: 800));

    switch (action) {
      case BulkAction.unfollow:
        // Remove from following list
        _following.removeWhere((f) => userIds.contains(f.id));
        break;
      case BulkAction.mute:
        // Mute notifications for selected users
        for (final follower in _followers) {
          if (userIds.contains(follower.id)) {
            final index = _followers.indexWhere((f) => f.id == follower.id);
            if (index != -1) {
              _followers[index] = follower.copyWith(isMuted: true);
            }
          }
        }
        for (final following in _following) {
          if (userIds.contains(following.id)) {
            final index = _following.indexWhere((f) => f.id == following.id);
            if (index != -1) {
              _following[index] = following.copyWith(isMuted: true);
            }
          }
        }
        break;
      case BulkAction.unmute:
        // Unmute notifications for selected users
        for (final follower in _followers) {
          if (userIds.contains(follower.id)) {
            final index = _followers.indexWhere((f) => f.id == follower.id);
            if (index != -1) {
              _followers[index] = follower.copyWith(isMuted: false);
            }
          }
        }
        for (final following in _following) {
          if (userIds.contains(following.id)) {
            final index = _following.indexWhere((f) => f.id == following.id);
            if (index != -1) {
              _following[index] = following.copyWith(isMuted: false);
            }
          }
        }
        break;
      case BulkAction.addToList:
        // TODO: Implement custom lists feature
        break;
    }

    return true;
  }

  // Toggle selection for bulk actions
  Future<bool> toggleSelection(String userId, bool isSelected) async {
    await Future.delayed(const Duration(milliseconds: 100));

    // Update in followers list
    final followerIndex = _followers.indexWhere((f) => f.id == userId);
    if (followerIndex != -1) {
      _followers[followerIndex] = _followers[followerIndex].copyWith(
        isSelected: isSelected,
      );
    }

    // Update in following list
    final followingIndex = _following.indexWhere((f) => f.id == userId);
    if (followingIndex != -1) {
      _following[followingIndex] = _following[followingIndex].copyWith(
        isSelected: isSelected,
      );
    }

    return true;
  }

  // Select all
  Future<bool> selectAll(bool isSelected) async {
    await Future.delayed(const Duration(milliseconds: 200));

    for (int i = 0; i < _followers.length; i++) {
      _followers[i] = _followers[i].copyWith(isSelected: isSelected);
    }

    for (int i = 0; i < _following.length; i++) {
      _following[i] = _following[i].copyWith(isSelected: isSelected);
    }

    return true;
  }

  // Get selected users
  List<String> getSelectedUsers() {
    final selectedFollowers = _followers
        .where((f) => f.isSelected)
        .map((f) => f.id)
        .toList();
    final selectedFollowing = _following
        .where((f) => f.isSelected)
        .map((f) => f.id)
        .toList();
    return [...selectedFollowers, ...selectedFollowing];
  }

  // Helper method to apply filters to followers
  List<FollowerModel> _applyFilter(
    List<FollowerModel> followers,
    FollowersFilter filter,
  ) {
    return followers.where((follower) {
      // Gender filter
      if (filter.gender != null && follower.gender != filter.gender) {
        return false;
      }

      // Account type filter
      if (filter.accountType != null &&
          follower.accountType != filter.accountType) {
        return false;
      }

      // Verification filter
      if (filter.isVerified != null &&
          follower.isVerified != filter.isVerified) {
        return false;
      }

      // Muted filter
      if (filter.isMuted != null && follower.isMuted != filter.isMuted) {
        return false;
      }

      // Search query filter
      if (filter.searchQuery != null && filter.searchQuery!.isNotEmpty) {
        final query = filter.searchQuery!.toLowerCase();
        final matchesName = follower.name.toLowerCase().contains(query);
        final matchesUsername = follower.username.toLowerCase().contains(query);
        final matchesBio = follower.bio?.toLowerCase().contains(query) ?? false;

        if (!matchesName && !matchesUsername && !matchesBio) {
          return false;
        }
      }

      return true;
    }).toList();
  }

  // Helper method to apply filters to following
  List<FollowingModel> _applyFollowingFilter(
    List<FollowingModel> following,
    FollowersFilter filter,
  ) {
    return following.where((follow) {
      // Gender filter
      if (filter.gender != null && follow.gender != filter.gender) {
        return false;
      }

      // Account type filter
      if (filter.accountType != null &&
          follow.accountType != filter.accountType) {
        return false;
      }

      // Verification filter
      if (filter.isVerified != null && follow.isVerified != filter.isVerified) {
        return false;
      }

      // Muted filter
      if (filter.isMuted != null && follow.isMuted != filter.isMuted) {
        return false;
      }

      // Mutual status filter
      if (filter.mutualStatus != null) {
        switch (filter.mutualStatus!) {
          case MutualStatus.mutual:
            if (!follow.isMutual) return false;
            break;
          case MutualStatus.notFollowingBack:
            if (follow.isFollowingBack) return false;
            break;
          case MutualStatus.notFollowedBack:
            if (follow.isFollowingBack) return false;
            break;
        }
      }

      // Search query filter
      if (filter.searchQuery != null && filter.searchQuery!.isNotEmpty) {
        final query = filter.searchQuery!.toLowerCase();
        final matchesName = follow.name.toLowerCase().contains(query);
        final matchesUsername = follow.username.toLowerCase().contains(query);
        final matchesBio = follow.bio?.toLowerCase().contains(query) ?? false;

        if (!matchesName && !matchesUsername && !matchesBio) {
          return false;
        }
      }

      return true;
    }).toList();
  }

  /// Clear profile caches for the given user IDs to ensure fresh counts are displayed
  Future<void> _clearProfileCaches(List<String> userIds) async {
    try {
      final cacheService = getIt<CacheService>();
      for (final userId in userIds) {
        await cacheService.removeData('user_profile_$userId');
      }
    } catch (e) {
      // Silently fail cache clearing - not critical for functionality
    }
  }

  /// Recalculate follower and following counts from actual relationships
  Future<void> recalculateFollowerCounts(String userId) async {
    try {
      // Count actual followers
      final followersSnapshot = await _firestore
          .collection('follows')
          .where('followingId', isEqualTo: userId)
          .where('isActive', isEqualTo: true)
          .get();

      // Count actual following
      final followingSnapshot = await _firestore
          .collection('follows')
          .where('followerId', isEqualTo: userId)
          .where('isActive', isEqualTo: true)
          .get();

      // Update user document with accurate counts
      await _firestore.collection('users').doc(userId).update({
        'followerCount': followersSnapshot.docs.length,
        'followingCount': followingSnapshot.docs.length,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Clear cache to force fresh data
      await _clearProfileCaches([userId]);
    } catch (e) {
      throw Exception('Failed to recalculate follower counts: ${e.toString()}');
    }
  }
}
