import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/core/services/analytics_service.dart';
import 'package:billionaires_social/core/services/error_handling_service.dart';

/// Service for managing user-level blocking and reporting functionality
class UserBlockingService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final AnalyticsService _analyticsService = getIt<AnalyticsService>();
  final ErrorHandlingService _errorHandlingService =
      getIt<ErrorHandlingService>();

  /// Block a user
  Future<void> blockUser(String userIdToBlock) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    try {
      debugPrint('🚫 Blocking user: $userIdToBlock by ${currentUser.uid}');

      // Create block relationship
      await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .collection('blocked_users')
          .doc(userIdToBlock)
          .set({
            'blockedUserId': userIdToBlock,
            'blockedAt': FieldValue.serverTimestamp(),
            'reason': 'user_initiated',
          });

      // Update current user's privacy settings to include blocked user
      await _firestore.collection('users').doc(currentUser.uid).update({
        'blockedAccountIds': FieldValue.arrayUnion([userIdToBlock]),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Remove any existing follow relationships
      await _removeFollowRelationships(currentUser.uid, userIdToBlock);

      // Remove from close friends if exists
      await _removeFromCloseFriends(currentUser.uid, userIdToBlock);

      // Log analytics
      await _analyticsService.logEventSafely(
        eventName: 'user_blocked',
        parameters: {
          'blocked_user_id': userIdToBlock,
          'blocker_user_id': currentUser.uid,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      debugPrint('✅ User blocked successfully: $userIdToBlock');
    } catch (e) {
      debugPrint('❌ Error blocking user: $e');
      _errorHandlingService.handleError(
        e,
        context: 'UserBlockingService.blockUser',
      );
      throw Exception('Failed to block user: ${e.toString()}');
    }
  }

  /// Unblock a user
  Future<void> unblockUser(String userIdToUnblock) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    try {
      debugPrint('✅ Unblocking user: $userIdToUnblock by ${currentUser.uid}');

      // Remove block relationship
      await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .collection('blocked_users')
          .doc(userIdToUnblock)
          .delete();

      // Update current user's privacy settings to remove blocked user
      await _firestore.collection('users').doc(currentUser.uid).update({
        'blockedAccountIds': FieldValue.arrayRemove([userIdToUnblock]),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Log analytics
      await _analyticsService.logEventSafely(
        eventName: 'user_unblocked',
        parameters: {
          'unblocked_user_id': userIdToUnblock,
          'unblocker_user_id': currentUser.uid,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      debugPrint('✅ User unblocked successfully: $userIdToUnblock');
    } catch (e) {
      debugPrint('❌ Error unblocking user: $e');
      _errorHandlingService.handleError(
        e,
        context: 'UserBlockingService.unblockUser',
      );
      throw Exception('Failed to unblock user: ${e.toString()}');
    }
  }

  /// Check if a user is blocked by current user
  Future<bool> isUserBlocked(String userId) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return false;

    try {
      final doc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .collection('blocked_users')
          .doc(userId)
          .get();

      return doc.exists;
    } catch (e) {
      debugPrint('❌ Error checking if user is blocked: $e');
      return false;
    }
  }

  /// Check if current user is blocked by another user
  Future<bool> isBlockedByUser(String userId) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return false;

    try {
      final doc = await _firestore
          .collection('users')
          .doc(userId)
          .collection('blocked_users')
          .doc(currentUser.uid)
          .get();

      return doc.exists;
    } catch (e) {
      debugPrint('❌ Error checking if blocked by user: $e');
      return false;
    }
  }

  /// Get list of blocked users
  Future<List<String>> getBlockedUsers() async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return [];

    try {
      final snapshot = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .collection('blocked_users')
          .get();

      return snapshot.docs.map((doc) => doc.id).toList();
    } catch (e) {
      debugPrint('❌ Error getting blocked users: $e');
      return [];
    }
  }

  /// Report a user
  Future<void> reportUser({
    required String userIdToReport,
    required String reason,
    String? additionalDetails,
  }) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    try {
      debugPrint('📝 Reporting user: $userIdToReport by ${currentUser.uid}');

      // Create user report
      await _firestore.collection('user_reports').add({
        'reportedUserId': userIdToReport,
        'reportedBy': currentUser.uid,
        'reason': reason,
        'additionalDetails': additionalDetails ?? '',
        'status': 'pending', // pending, reviewed, resolved, dismissed
        'createdAt': FieldValue.serverTimestamp(),
        'reviewedBy': null,
        'reviewedAt': null,
        'action': null, // warn, suspend, ban, none
      });

      // Update reported user's report count
      await _firestore.collection('users').doc(userIdToReport).update({
        'reportCount': FieldValue.increment(1),
        'lastReportedAt': FieldValue.serverTimestamp(),
      });

      // Log analytics
      await _analyticsService.logEventSafely(
        eventName: 'user_reported',
        parameters: {
          'reported_user_id': userIdToReport,
          'reporter_user_id': currentUser.uid,
          'reason': reason,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      debugPrint('✅ User reported successfully: $userIdToReport');
    } catch (e) {
      debugPrint('❌ Error reporting user: $e');
      _errorHandlingService.handleError(
        e,
        context: 'UserBlockingService.reportUser',
      );
      throw Exception('Failed to report user: ${e.toString()}');
    }
  }

  /// Remove follow relationships when blocking
  Future<void> _removeFollowRelationships(
    String blockerId,
    String blockedId,
  ) async {
    try {
      // Remove blocker following blocked user
      await _firestore
          .collection('users')
          .doc(blockerId)
          .collection('following')
          .doc(blockedId)
          .delete();

      // Remove blocked user following blocker
      await _firestore
          .collection('users')
          .doc(blockedId)
          .collection('following')
          .doc(blockerId)
          .delete();

      // Remove from followers collections
      await _firestore
          .collection('users')
          .doc(blockerId)
          .collection('followers')
          .doc(blockedId)
          .delete();

      await _firestore
          .collection('users')
          .doc(blockedId)
          .collection('followers')
          .doc(blockerId)
          .delete();

      // Update follower/following counts
      await _updateFollowCounts(blockerId);
      await _updateFollowCounts(blockedId);
    } catch (e) {
      debugPrint('❌ Error removing follow relationships: $e');
    }
  }

  /// Remove from close friends when blocking
  Future<void> _removeFromCloseFriends(
    String blockerId,
    String blockedId,
  ) async {
    try {
      // Remove from blocker's close friends
      await _firestore
          .collection('users')
          .doc(blockerId)
          .collection('close_friends')
          .doc(blockedId)
          .delete();

      // Remove from blocked user's close friends
      await _firestore
          .collection('users')
          .doc(blockedId)
          .collection('close_friends')
          .doc(blockerId)
          .delete();
    } catch (e) {
      debugPrint('❌ Error removing from close friends: $e');
    }
  }

  /// Update follow counts after removing relationships
  Future<void> _updateFollowCounts(String userId) async {
    try {
      final followingSnapshot = await _firestore
          .collection('users')
          .doc(userId)
          .collection('following')
          .get();

      final followersSnapshot = await _firestore
          .collection('users')
          .doc(userId)
          .collection('followers')
          .get();

      await _firestore.collection('users').doc(userId).update({
        'followingCount': followingSnapshot.docs.length,
        'followerCount': followersSnapshot.docs.length,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('❌ Error updating follow counts: $e');
    }
  }

  /// Stream of blocked users for real-time updates
  Stream<List<String>> getBlockedUsersStream() {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      return Stream.value([]);
    }

    return _firestore
        .collection('users')
        .doc(currentUser.uid)
        .collection('blocked_users')
        .snapshots()
        .map((snapshot) => snapshot.docs.map((doc) => doc.id).toList());
  }

  /// Check if users can interact (not blocked by each other)
  Future<bool> canUsersInteract(String userId1, String userId2) async {
    try {
      // Check if user1 blocked user2
      final user1BlockedUser2 = await _firestore
          .collection('users')
          .doc(userId1)
          .collection('blocked_users')
          .doc(userId2)
          .get();

      // Check if user2 blocked user1
      final user2BlockedUser1 = await _firestore
          .collection('users')
          .doc(userId2)
          .collection('blocked_users')
          .doc(userId1)
          .get();

      return !user1BlockedUser2.exists && !user2BlockedUser1.exists;
    } catch (e) {
      debugPrint('❌ Error checking user interaction: $e');
      return false;
    }
  }
}
