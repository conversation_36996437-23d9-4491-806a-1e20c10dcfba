import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:billionaires_social/features/profile/models/account_model.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AccountManagementService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  static const String _activeAccountKey = 'active_account_id';

  /// Get all accounts for the current user
  Future<List<AccountModel>> getUserAccounts() async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return [];

    try {
      final snapshot = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .collection('accounts')
          .orderBy('createdAt', descending: false)
          .get();

      final accounts = snapshot.docs.map((doc) {
        final data = doc.data();
        return AccountModel.fromFirestore(doc.id, data);
      }).toList();

      // If no accounts exist, create a default personal account
      if (accounts.isEmpty) {
        final defaultAccount = await _createDefaultAccount();
        accounts.add(defaultAccount);
      }

      return accounts;
    } catch (e) {
      debugPrint('❌ Error getting user accounts: $e');
      return [];
    }
  }

  /// Create a new account
  Future<AccountModel> createAccount({
    required String name,
    required String username,
    required AccountType accountType,
    String? profilePictureUrl,
    String? bio,
  }) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    try {
      // Check if username is available
      final isAvailable = await _isUsernameAvailable(username);
      if (!isAvailable) {
        throw Exception('Username is already taken');
      }

      // Create account document
      final accountRef = _firestore
          .collection('users')
          .doc(currentUser.uid)
          .collection('accounts')
          .doc();

      final accountData = {
        'name': name,
        'username': username,
        'accountType': accountType.toString().split('.').last,
        'profilePictureUrl': profilePictureUrl ?? '',
        'bio': bio ?? '',
        'isActive': false, // New accounts start inactive
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
        'followersCount': 0,
        'followingCount': 0,
        'postsCount': 0,
        'isVerified': false,
        'isBillionaire': accountType == AccountType.celebrity,
      };

      await accountRef.set(accountData);

      // Create the account model
      final account = AccountModel(
        id: accountRef.id,
        name: name,
        username: username,
        profilePictureUrl: profilePictureUrl ?? '',
        bio: bio ?? '',
        accountType: accountType,
        isActive: false,
        createdAt: DateTime.now(),
        followersCount: 0,
        followingCount: 0,
        postsCount: 0,
        isVerified: false,
        isBillionaire: accountType == AccountType.celebrity,
      );

      debugPrint('✅ Created new account: ${account.username}');
      return account;
    } catch (e) {
      debugPrint('❌ Error creating account: $e');
      throw Exception('Failed to create account: $e');
    }
  }

  /// Switch to a different account
  Future<void> switchToAccount(String accountId) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    try {
      // Get all user accounts
      final accounts = await getUserAccounts();
      final targetAccount = accounts.firstWhere(
        (account) => account.id == accountId,
        orElse: () => throw Exception('Account not found'),
      );

      // Update active status in Firestore
      final batch = _firestore.batch();

      // Set all accounts to inactive
      for (final account in accounts) {
        final accountRef = _firestore
            .collection('users')
            .doc(currentUser.uid)
            .collection('accounts')
            .doc(account.id);

        batch.update(accountRef, {
          'isActive': account.id == accountId,
          'lastActiveAt': account.id == accountId
              ? FieldValue.serverTimestamp()
              : null,
        });
      }

      await batch.commit();

      // Store active account locally
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_activeAccountKey, accountId);

      // Send notification about account switch (if notification service supports it)
      try {
        // await _notificationService.createSystemNotification(
        //   currentUser.uid,
        //   'Account switched to ${targetAccount.name}',
        //   'account_switch',
        // );
      } catch (e) {
        debugPrint('Failed to send account switch notification: $e');
      }

      debugPrint('✅ Switched to account: ${targetAccount.username}');
    } catch (e) {
      debugPrint('❌ Error switching account: $e');
      throw Exception('Failed to switch account: $e');
    }
  }

  /// Get the currently active account
  Future<AccountModel?> getActiveAccount() async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return null;

    try {
      final accounts = await getUserAccounts();
      final activeAccount = accounts
          .where((account) => account.isActive)
          .firstOrNull;

      if (activeAccount != null) {
        return activeAccount;
      }

      // If no active account, make the first one active
      if (accounts.isNotEmpty) {
        await switchToAccount(accounts.first.id);
        return accounts.first.copyWith(isActive: true);
      }

      return null;
    } catch (e) {
      debugPrint('❌ Error getting active account: $e');
      return null;
    }
  }

  /// Update account information
  Future<void> updateAccount(AccountModel account) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    try {
      await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .collection('accounts')
          .doc(account.id)
          .update({
            'name': account.name,
            'username': account.username,
            'bio': account.bio,
            'profilePictureUrl': account.profilePictureUrl,
            'updatedAt': FieldValue.serverTimestamp(),
          });

      debugPrint('✅ Updated account: ${account.username}');
    } catch (e) {
      debugPrint('❌ Error updating account: $e');
      throw Exception('Failed to update account: $e');
    }
  }

  /// Delete an account
  Future<void> deleteAccount(String accountId) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    try {
      final accounts = await getUserAccounts();

      // Don't allow deleting the last account
      if (accounts.length <= 1) {
        throw Exception('Cannot delete your only account');
      }

      // If deleting the active account, switch to another one first
      final accountToDelete = accounts.firstWhere((acc) => acc.id == accountId);
      if (accountToDelete.isActive) {
        final otherAccount = accounts.firstWhere((acc) => acc.id != accountId);
        await switchToAccount(otherAccount.id);
      }

      // Delete the account
      await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .collection('accounts')
          .doc(accountId)
          .delete();

      debugPrint('✅ Deleted account: $accountId');
    } catch (e) {
      debugPrint('❌ Error deleting account: $e');
      throw Exception('Failed to delete account: $e');
    }
  }

  /// Check if username is available
  Future<bool> _isUsernameAvailable(String username) async {
    try {
      final snapshot = await _firestore
          .collectionGroup('accounts')
          .where('username', isEqualTo: username.toLowerCase())
          .limit(1)
          .get();

      return snapshot.docs.isEmpty;
    } catch (e) {
      debugPrint('❌ Error checking username availability: $e');
      return false;
    }
  }

  /// Create default personal account for new users
  Future<AccountModel> _createDefaultAccount() async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    // Generate a default username based on email or user ID
    final email = currentUser.email ?? '';
    final defaultUsername = email.isNotEmpty
        ? email.split('@').first.toLowerCase()
        : 'user_${currentUser.uid.substring(0, 8)}';

    return await createAccount(
      name: currentUser.displayName ?? 'User',
      username: defaultUsername,
      accountType: AccountType.personal,
      profilePictureUrl: currentUser.photoURL,
    );
  }

  /// Stream of user accounts for real-time updates
  Stream<List<AccountModel>> accountsStream() {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return Stream.value([]);

    return _firestore
        .collection('users')
        .doc(currentUser.uid)
        .collection('accounts')
        .orderBy('createdAt', descending: false)
        .snapshots()
        .map((snapshot) {
          return snapshot.docs.map((doc) {
            final data = doc.data();
            return AccountModel.fromFirestore(doc.id, data);
          }).toList();
        });
  }
}
