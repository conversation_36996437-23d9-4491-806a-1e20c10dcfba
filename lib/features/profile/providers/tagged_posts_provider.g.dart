// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tagged_posts_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$taggedPostsHash() => r'5c5d301188ac20dccb8decadd65ff4c57f752432';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Provider for tagged posts with caching
///
/// Copied from [taggedPosts].
@ProviderFor(taggedPosts)
const taggedPostsProvider = TaggedPostsFamily();

/// Provider for tagged posts with caching
///
/// Copied from [taggedPosts].
class TaggedPostsFamily extends Family<AsyncValue<List<Post>>> {
  /// Provider for tagged posts with caching
  ///
  /// Copied from [taggedPosts].
  const TaggedPostsFamily();

  /// Provider for tagged posts with caching
  ///
  /// Copied from [taggedPosts].
  TaggedPostsProvider call(String userId) {
    return TaggedPostsProvider(userId);
  }

  @override
  TaggedPostsProvider getProviderOverride(
    covariant TaggedPostsProvider provider,
  ) {
    return call(provider.userId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'taggedPostsProvider';
}

/// Provider for tagged posts with caching
///
/// Copied from [taggedPosts].
class TaggedPostsProvider extends AutoDisposeFutureProvider<List<Post>> {
  /// Provider for tagged posts with caching
  ///
  /// Copied from [taggedPosts].
  TaggedPostsProvider(String userId)
    : this._internal(
        (ref) => taggedPosts(ref as TaggedPostsRef, userId),
        from: taggedPostsProvider,
        name: r'taggedPostsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$taggedPostsHash,
        dependencies: TaggedPostsFamily._dependencies,
        allTransitiveDependencies: TaggedPostsFamily._allTransitiveDependencies,
        userId: userId,
      );

  TaggedPostsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.userId,
  }) : super.internal();

  final String userId;

  @override
  Override overrideWith(
    FutureOr<List<Post>> Function(TaggedPostsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: TaggedPostsProvider._internal(
        (ref) => create(ref as TaggedPostsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        userId: userId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<Post>> createElement() {
    return _TaggedPostsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is TaggedPostsProvider && other.userId == userId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, userId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin TaggedPostsRef on AutoDisposeFutureProviderRef<List<Post>> {
  /// The parameter `userId` of this provider.
  String get userId;
}

class _TaggedPostsProviderElement
    extends AutoDisposeFutureProviderElement<List<Post>>
    with TaggedPostsRef {
  _TaggedPostsProviderElement(super.provider);

  @override
  String get userId => (origin as TaggedPostsProvider).userId;
}

String _$taggedPostsStreamHash() => r'427c473a8e55eb200b7d684b3a8663fb729576e2';

/// Stream provider for real-time tagged posts updates
///
/// Copied from [taggedPostsStream].
@ProviderFor(taggedPostsStream)
const taggedPostsStreamProvider = TaggedPostsStreamFamily();

/// Stream provider for real-time tagged posts updates
///
/// Copied from [taggedPostsStream].
class TaggedPostsStreamFamily extends Family<AsyncValue<List<Post>>> {
  /// Stream provider for real-time tagged posts updates
  ///
  /// Copied from [taggedPostsStream].
  const TaggedPostsStreamFamily();

  /// Stream provider for real-time tagged posts updates
  ///
  /// Copied from [taggedPostsStream].
  TaggedPostsStreamProvider call(String userId) {
    return TaggedPostsStreamProvider(userId);
  }

  @override
  TaggedPostsStreamProvider getProviderOverride(
    covariant TaggedPostsStreamProvider provider,
  ) {
    return call(provider.userId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'taggedPostsStreamProvider';
}

/// Stream provider for real-time tagged posts updates
///
/// Copied from [taggedPostsStream].
class TaggedPostsStreamProvider extends AutoDisposeStreamProvider<List<Post>> {
  /// Stream provider for real-time tagged posts updates
  ///
  /// Copied from [taggedPostsStream].
  TaggedPostsStreamProvider(String userId)
    : this._internal(
        (ref) => taggedPostsStream(ref as TaggedPostsStreamRef, userId),
        from: taggedPostsStreamProvider,
        name: r'taggedPostsStreamProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$taggedPostsStreamHash,
        dependencies: TaggedPostsStreamFamily._dependencies,
        allTransitiveDependencies:
            TaggedPostsStreamFamily._allTransitiveDependencies,
        userId: userId,
      );

  TaggedPostsStreamProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.userId,
  }) : super.internal();

  final String userId;

  @override
  Override overrideWith(
    Stream<List<Post>> Function(TaggedPostsStreamRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: TaggedPostsStreamProvider._internal(
        (ref) => create(ref as TaggedPostsStreamRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        userId: userId,
      ),
    );
  }

  @override
  AutoDisposeStreamProviderElement<List<Post>> createElement() {
    return _TaggedPostsStreamProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is TaggedPostsStreamProvider && other.userId == userId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, userId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin TaggedPostsStreamRef on AutoDisposeStreamProviderRef<List<Post>> {
  /// The parameter `userId` of this provider.
  String get userId;
}

class _TaggedPostsStreamProviderElement
    extends AutoDisposeStreamProviderElement<List<Post>>
    with TaggedPostsStreamRef {
  _TaggedPostsStreamProviderElement(super.provider);

  @override
  String get userId => (origin as TaggedPostsStreamProvider).userId;
}

String _$taggedPostsCountHash() => r'b37e800aaae107147948e6ab4610c0cf10faeb25';

/// Provider for tagged posts count
///
/// Copied from [taggedPostsCount].
@ProviderFor(taggedPostsCount)
const taggedPostsCountProvider = TaggedPostsCountFamily();

/// Provider for tagged posts count
///
/// Copied from [taggedPostsCount].
class TaggedPostsCountFamily extends Family<AsyncValue<int>> {
  /// Provider for tagged posts count
  ///
  /// Copied from [taggedPostsCount].
  const TaggedPostsCountFamily();

  /// Provider for tagged posts count
  ///
  /// Copied from [taggedPostsCount].
  TaggedPostsCountProvider call(String userId) {
    return TaggedPostsCountProvider(userId);
  }

  @override
  TaggedPostsCountProvider getProviderOverride(
    covariant TaggedPostsCountProvider provider,
  ) {
    return call(provider.userId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'taggedPostsCountProvider';
}

/// Provider for tagged posts count
///
/// Copied from [taggedPostsCount].
class TaggedPostsCountProvider extends AutoDisposeFutureProvider<int> {
  /// Provider for tagged posts count
  ///
  /// Copied from [taggedPostsCount].
  TaggedPostsCountProvider(String userId)
    : this._internal(
        (ref) => taggedPostsCount(ref as TaggedPostsCountRef, userId),
        from: taggedPostsCountProvider,
        name: r'taggedPostsCountProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$taggedPostsCountHash,
        dependencies: TaggedPostsCountFamily._dependencies,
        allTransitiveDependencies:
            TaggedPostsCountFamily._allTransitiveDependencies,
        userId: userId,
      );

  TaggedPostsCountProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.userId,
  }) : super.internal();

  final String userId;

  @override
  Override overrideWith(
    FutureOr<int> Function(TaggedPostsCountRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: TaggedPostsCountProvider._internal(
        (ref) => create(ref as TaggedPostsCountRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        userId: userId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<int> createElement() {
    return _TaggedPostsCountProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is TaggedPostsCountProvider && other.userId == userId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, userId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin TaggedPostsCountRef on AutoDisposeFutureProviderRef<int> {
  /// The parameter `userId` of this provider.
  String get userId;
}

class _TaggedPostsCountProviderElement
    extends AutoDisposeFutureProviderElement<int>
    with TaggedPostsCountRef {
  _TaggedPostsCountProviderElement(super.provider);

  @override
  String get userId => (origin as TaggedPostsCountProvider).userId;
}

String _$taggedPostsNotifierHash() =>
    r'861655f9872bc9818b37e5ae1d1c35f353adf981';

abstract class _$TaggedPostsNotifier
    extends BuildlessAutoDisposeAsyncNotifier<List<Post>> {
  late final String userId;

  FutureOr<List<Post>> build(String userId);
}

/// Notifier for managing tagged posts state
///
/// Copied from [TaggedPostsNotifier].
@ProviderFor(TaggedPostsNotifier)
const taggedPostsNotifierProvider = TaggedPostsNotifierFamily();

/// Notifier for managing tagged posts state
///
/// Copied from [TaggedPostsNotifier].
class TaggedPostsNotifierFamily extends Family<AsyncValue<List<Post>>> {
  /// Notifier for managing tagged posts state
  ///
  /// Copied from [TaggedPostsNotifier].
  const TaggedPostsNotifierFamily();

  /// Notifier for managing tagged posts state
  ///
  /// Copied from [TaggedPostsNotifier].
  TaggedPostsNotifierProvider call(String userId) {
    return TaggedPostsNotifierProvider(userId);
  }

  @override
  TaggedPostsNotifierProvider getProviderOverride(
    covariant TaggedPostsNotifierProvider provider,
  ) {
    return call(provider.userId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'taggedPostsNotifierProvider';
}

/// Notifier for managing tagged posts state
///
/// Copied from [TaggedPostsNotifier].
class TaggedPostsNotifierProvider
    extends
        AutoDisposeAsyncNotifierProviderImpl<TaggedPostsNotifier, List<Post>> {
  /// Notifier for managing tagged posts state
  ///
  /// Copied from [TaggedPostsNotifier].
  TaggedPostsNotifierProvider(String userId)
    : this._internal(
        () => TaggedPostsNotifier()..userId = userId,
        from: taggedPostsNotifierProvider,
        name: r'taggedPostsNotifierProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$taggedPostsNotifierHash,
        dependencies: TaggedPostsNotifierFamily._dependencies,
        allTransitiveDependencies:
            TaggedPostsNotifierFamily._allTransitiveDependencies,
        userId: userId,
      );

  TaggedPostsNotifierProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.userId,
  }) : super.internal();

  final String userId;

  @override
  FutureOr<List<Post>> runNotifierBuild(
    covariant TaggedPostsNotifier notifier,
  ) {
    return notifier.build(userId);
  }

  @override
  Override overrideWith(TaggedPostsNotifier Function() create) {
    return ProviderOverride(
      origin: this,
      override: TaggedPostsNotifierProvider._internal(
        () => create()..userId = userId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        userId: userId,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<TaggedPostsNotifier, List<Post>>
  createElement() {
    return _TaggedPostsNotifierProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is TaggedPostsNotifierProvider && other.userId == userId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, userId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin TaggedPostsNotifierRef
    on AutoDisposeAsyncNotifierProviderRef<List<Post>> {
  /// The parameter `userId` of this provider.
  String get userId;
}

class _TaggedPostsNotifierProviderElement
    extends
        AutoDisposeAsyncNotifierProviderElement<TaggedPostsNotifier, List<Post>>
    with TaggedPostsNotifierRef {
  _TaggedPostsNotifierProviderElement(super.provider);

  @override
  String get userId => (origin as TaggedPostsNotifierProvider).userId;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
