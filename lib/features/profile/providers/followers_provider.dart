import 'package:billionaires_social/features/profile/models/follower_model.dart';
import 'package:billionaires_social/features/profile/services/followers_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

part 'followers_provider.g.dart';

@riverpod
FollowersService followersService(Ref ref) {
  return FollowersService();
}

@riverpod
class Followers extends _$Followers {
  @override
  Future<List<FollowerModel>> build(String userId) async {
    return ref.watch(followersServiceProvider).getFollowers(userId);
  }

  Future<void> refresh(String userId) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(
      () => ref.read(followersServiceProvider).getFollowers(userId),
    );
  }

  Future<void> toggleSelection(String userId, bool isSelected) async {
    await ref
        .read(followersServiceProvider)
        .toggleSelection(userId, isSelected);
    await refresh(this.userId);
  }

  Future<void> selectAll(bool isSelected) async {
    await ref.read(followersServiceProvider).selectAll(isSelected);
    await refresh(userId);
  }
}

@riverpod
class Following extends _$Following {
  @override
  Future<List<FollowerModel>> build(String userId) async {
    return ref.watch(followersServiceProvider).getFollowing(userId);
  }

  Future<void> refresh(String userId) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(
      () => ref.read(followersServiceProvider).getFollowing(userId),
    );
  }

  Future<void> toggleSelection(String userId, bool isSelected) async {
    await ref
        .read(followersServiceProvider)
        .toggleSelection(userId, isSelected);
    await refresh(this.userId);
  }

  Future<void> performBulkAction(BulkAction action) async {
    final selectedUserIds = ref
        .read(followersServiceProvider)
        .getSelectedUsers();
    await ref
        .read(followersServiceProvider)
        .performBulkAction(userIds: selectedUserIds, action: action);
    await refresh(userId);
  }
}

@riverpod
class FollowersAnalyticsNotifier extends _$FollowersAnalyticsNotifier {
  @override
  Future<FollowersAnalytics> build() async {
    return ref.read(followersServiceProvider).getAnalytics();
  }

  Future<void> refreshAnalytics() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(
      () => ref.read(followersServiceProvider).getAnalytics(),
    );
  }
}

@riverpod
class FollowersFilterNotifier extends _$FollowersFilterNotifier {
  @override
  FollowersFilter build() {
    return const FollowersFilter(
      type: FilterType.all,
      gender: null,
      accountType: null,
      mutualStatus: null,
      isVerified: null,
      isMuted: null,
      searchQuery: null,
    );
  }

  void updateFilter(FollowersFilter filter) {
    state = filter;
  }

  void clearFilters() {
    state = const FollowersFilter(
      type: FilterType.all,
      gender: null,
      accountType: null,
      mutualStatus: null,
      isVerified: null,
      isMuted: null,
      searchQuery: null,
    );
  }

  void setGender(Gender? gender) {
    state = state.copyWith(gender: gender);
  }

  void setAccountType(AccountType? accountType) {
    state = state.copyWith(accountType: accountType);
  }

  void setMutualStatus(MutualStatus? mutualStatus) {
    state = state.copyWith(mutualStatus: mutualStatus);
  }

  void setVerified(bool? isVerified) {
    state = state.copyWith(isVerified: isVerified);
  }

  void setMuted(bool? isMuted) {
    state = state.copyWith(isMuted: isMuted);
  }

  void setSearchQuery(String? searchQuery) {
    state = state.copyWith(searchQuery: searchQuery);
  }

  void setFilterType(FilterType type) {
    state = state.copyWith(type: type);
  }
}
