// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'saved_content_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$savedContentHash() => r'dc28dd2b0db2b2096ba0efcfac00a5b00e575921';

/// See also [savedContent].
@ProviderFor(savedContent)
final savedContentProvider = AutoDisposeFutureProvider<List<Post>>.internal(
  savedContent,
  name: r'savedContentProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$savedContentHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SavedContentRef = AutoDisposeFutureProviderRef<List<Post>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
