import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/features/feed/services/feed_service.dart';
import 'package:billionaires_social/core/services/firebase_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:riverpod/riverpod.dart';

part 'saved_content_provider.g.dart';

@riverpod
Future<List<Post>> savedContent(Ref ref) async {
  final feedService = getIt<FeedService>();
  final firebaseService = getIt<FirebaseService>();

  final currentUser = firebaseService.currentUser;
  if (currentUser == null) return [];

  return feedService.getSavedPosts();
}
