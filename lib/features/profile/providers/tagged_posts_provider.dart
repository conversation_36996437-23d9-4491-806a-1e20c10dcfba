import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/features/profile/services/tagged_posts_service.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/core/services/cache_service.dart';
import 'package:flutter/foundation.dart';

part 'tagged_posts_provider.g.dart';

/// Provider for tagged posts with caching
@riverpod
Future<List<Post>> taggedPosts(Ref ref, String userId) async {
  final taggedPostsService = TaggedPostsService();
  final cacheService = getIt<CacheService>();

  debugPrint(
    '🏷️ TaggedPosts provider: Fetching tagged posts for user: $userId',
  );

  // Check cache first
  try {
    final cachedData = await cacheService.getData('tagged_posts_$userId');
    if (cachedData != null) {
      debugPrint('🏷️ TaggedPosts provider: Found cached data');
      final cachedPosts = (cachedData as List)
          .map((json) => Post.fromJson(json as Map<String, dynamic>))
          .toList();

      // Return cached data immediately, then refresh in background
      _refreshTaggedPostsInBackground(userId, taggedPostsService, cacheService);
      return cachedPosts;
    }
  } catch (e) {
    debugPrint(
      '🏷️ TaggedPosts provider: Cache error, fetching fresh data: $e',
    );
    // If cached data is invalid, remove it and continue with fresh fetch
    await cacheService.removeData('tagged_posts_$userId');
  }

  debugPrint('🏷️ TaggedPosts provider: Fetching fresh data from Firestore');

  // Fetch fresh data
  final posts = await taggedPostsService.getTaggedPosts(userId);

  // Cache the fresh data
  final postsJson = posts.map((post) => post.toJson()).toList();
  await cacheService.setData(
    'tagged_posts_$userId',
    postsJson,
    expiry: const Duration(minutes: 5),
  );

  debugPrint(
    '🏷️ TaggedPosts provider: Returning ${posts.length} tagged posts',
  );
  return posts;
}

/// Stream provider for real-time tagged posts updates
@riverpod
Stream<List<Post>> taggedPostsStream(Ref ref, String userId) {
  final taggedPostsService = TaggedPostsService();
  debugPrint(
    '🏷️ TaggedPostsStream provider: Setting up stream for user: $userId',
  );

  return taggedPostsService.getTaggedPostsStream(userId);
}

/// Provider for tagged posts count
@riverpod
Future<int> taggedPostsCount(Ref ref, String userId) async {
  final taggedPostsService = TaggedPostsService();
  final cacheService = getIt<CacheService>();

  debugPrint('🏷️ TaggedPostsCount provider: Getting count for user: $userId');

  // Check cache first
  try {
    final cachedCount = await cacheService.getData(
      'tagged_posts_count_$userId',
    );
    if (cachedCount != null) {
      debugPrint(
        '🏷️ TaggedPostsCount provider: Found cached count: $cachedCount',
      );

      // Return cached count immediately, then refresh in background
      _refreshTaggedPostsCountInBackground(
        userId,
        taggedPostsService,
        cacheService,
      );
      return cachedCount as int;
    }
  } catch (e) {
    debugPrint('🏷️ TaggedPostsCount provider: Cache error: $e');
    await cacheService.removeData('tagged_posts_count_$userId');
  }

  debugPrint('🏷️ TaggedPostsCount provider: Fetching fresh count');

  // Fetch fresh count
  final count = await taggedPostsService.getTaggedPostsCount(userId);

  // Cache the count
  await cacheService.setData(
    'tagged_posts_count_$userId',
    count,
    expiry: const Duration(minutes: 5),
  );

  debugPrint('🏷️ TaggedPostsCount provider: Returning count: $count');
  return count;
}

/// Notifier for managing tagged posts state
@riverpod
class TaggedPostsNotifier extends _$TaggedPostsNotifier {
  @override
  Future<List<Post>> build(String userId) async {
    return ref.watch(taggedPostsProvider(userId).future);
  }

  /// Refresh tagged posts
  Future<void> refresh() async {
    final cacheService = getIt<CacheService>();
    final userId = state.value?.first.userId ?? '';

    if (userId.isNotEmpty) {
      // Clear cache
      await cacheService.removeData('tagged_posts_$userId');
      await cacheService.removeData('tagged_posts_count_$userId');
    }

    // Invalidate and rebuild
    ref.invalidateSelf();
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => build(userId));
  }

  /// Remove user from a tagged post
  Future<void> removeFromTaggedPost(String postId) async {
    final taggedPostsService = TaggedPostsService();
    final currentUserId = state.value?.first.userId ?? '';

    if (currentUserId.isEmpty) return;

    try {
      await taggedPostsService.removeUserFromTaggedPost(postId, currentUserId);

      // Update local state by removing the post
      final currentPosts = state.value ?? [];
      final updatedPosts = currentPosts
          .where((post) => post.id != postId)
          .toList();
      state = AsyncValue.data(updatedPosts);

      // Clear cache to ensure fresh data on next load
      final cacheService = getIt<CacheService>();
      await cacheService.removeData('tagged_posts_$currentUserId');
      await cacheService.removeData('tagged_posts_count_$currentUserId');
    } catch (e) {
      debugPrint('❌ Error removing from tagged post: $e');
      // Optionally show error to user
    }
  }

  /// Add post to local state (for real-time updates)
  void addPost(Post post) {
    final currentPosts = state.value ?? [];
    final updatedPosts = [post, ...currentPosts];
    state = AsyncValue.data(updatedPosts);
  }

  /// Update post in local state
  void updatePost(Post updatedPost) {
    final currentPosts = state.value ?? [];
    final updatedPosts = currentPosts.map((post) {
      return post.id == updatedPost.id ? updatedPost : post;
    }).toList();
    state = AsyncValue.data(updatedPosts);
  }

  /// Remove post from local state
  void removePost(String postId) {
    final currentPosts = state.value ?? [];
    final updatedPosts = currentPosts
        .where((post) => post.id != postId)
        .toList();
    state = AsyncValue.data(updatedPosts);
  }
}

/// Background refresh function for tagged posts
Future<void> _refreshTaggedPostsInBackground(
  String userId,
  TaggedPostsService taggedPostsService,
  CacheService cacheService,
) async {
  try {
    debugPrint('🏷️ Refreshing tagged posts in background for user: $userId');

    final posts = await taggedPostsService.getTaggedPosts(userId);
    final postsJson = posts.map((post) => post.toJson()).toList();
    await cacheService.setData(
      'tagged_posts_$userId',
      postsJson,
      expiry: const Duration(minutes: 5),
    );

    debugPrint('🏷️ Background refresh completed for tagged posts');
  } catch (e) {
    debugPrint('❌ Background refresh failed for tagged posts: $e');
  }
}

/// Background refresh function for tagged posts count
Future<void> _refreshTaggedPostsCountInBackground(
  String userId,
  TaggedPostsService taggedPostsService,
  CacheService cacheService,
) async {
  try {
    debugPrint(
      '🏷️ Refreshing tagged posts count in background for user: $userId',
    );

    final count = await taggedPostsService.getTaggedPostsCount(userId);
    await cacheService.setData(
      'tagged_posts_count_$userId',
      count,
      expiry: const Duration(minutes: 5),
    );

    debugPrint('🏷️ Background refresh completed for tagged posts count');
  } catch (e) {
    debugPrint('❌ Background refresh failed for tagged posts count: $e');
  }
}
