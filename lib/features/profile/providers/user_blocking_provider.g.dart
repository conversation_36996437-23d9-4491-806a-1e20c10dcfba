// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_blocking_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$userBlockingServiceHash() =>
    r'1691efc6b37240dc1bb73f319e41c10674a7642d';

/// Provider for user blocking service
///
/// Copied from [userBlockingService].
@ProviderFor(userBlockingService)
final userBlockingServiceProvider =
    AutoDisposeProvider<UserBlockingService>.internal(
      userBlockingService,
      name: r'userBlockingServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$userBlockingServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef UserBlockingServiceRef = AutoDisposeProviderRef<UserBlockingService>;
String _$isUserBlockedHash() => r'77561313f4b64625e3531588d84e82661e48ba45';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Provider to check if a user is blocked
///
/// Copied from [isUserBlocked].
@ProviderFor(isUserBlocked)
const isUserBlockedProvider = IsUserBlockedFamily();

/// Provider to check if a user is blocked
///
/// Copied from [isUserBlocked].
class IsUserBlockedFamily extends Family<AsyncValue<bool>> {
  /// Provider to check if a user is blocked
  ///
  /// Copied from [isUserBlocked].
  const IsUserBlockedFamily();

  /// Provider to check if a user is blocked
  ///
  /// Copied from [isUserBlocked].
  IsUserBlockedProvider call(String userId) {
    return IsUserBlockedProvider(userId);
  }

  @override
  IsUserBlockedProvider getProviderOverride(
    covariant IsUserBlockedProvider provider,
  ) {
    return call(provider.userId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'isUserBlockedProvider';
}

/// Provider to check if a user is blocked
///
/// Copied from [isUserBlocked].
class IsUserBlockedProvider extends AutoDisposeFutureProvider<bool> {
  /// Provider to check if a user is blocked
  ///
  /// Copied from [isUserBlocked].
  IsUserBlockedProvider(String userId)
    : this._internal(
        (ref) => isUserBlocked(ref as IsUserBlockedRef, userId),
        from: isUserBlockedProvider,
        name: r'isUserBlockedProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$isUserBlockedHash,
        dependencies: IsUserBlockedFamily._dependencies,
        allTransitiveDependencies:
            IsUserBlockedFamily._allTransitiveDependencies,
        userId: userId,
      );

  IsUserBlockedProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.userId,
  }) : super.internal();

  final String userId;

  @override
  Override overrideWith(
    FutureOr<bool> Function(IsUserBlockedRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: IsUserBlockedProvider._internal(
        (ref) => create(ref as IsUserBlockedRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        userId: userId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<bool> createElement() {
    return _IsUserBlockedProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is IsUserBlockedProvider && other.userId == userId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, userId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin IsUserBlockedRef on AutoDisposeFutureProviderRef<bool> {
  /// The parameter `userId` of this provider.
  String get userId;
}

class _IsUserBlockedProviderElement
    extends AutoDisposeFutureProviderElement<bool>
    with IsUserBlockedRef {
  _IsUserBlockedProviderElement(super.provider);

  @override
  String get userId => (origin as IsUserBlockedProvider).userId;
}

String _$isBlockedByUserHash() => r'adff5a4d5855ff475919f5f1f1c9f2d5c3322c9b';

/// Provider to check if current user is blocked by another user
///
/// Copied from [isBlockedByUser].
@ProviderFor(isBlockedByUser)
const isBlockedByUserProvider = IsBlockedByUserFamily();

/// Provider to check if current user is blocked by another user
///
/// Copied from [isBlockedByUser].
class IsBlockedByUserFamily extends Family<AsyncValue<bool>> {
  /// Provider to check if current user is blocked by another user
  ///
  /// Copied from [isBlockedByUser].
  const IsBlockedByUserFamily();

  /// Provider to check if current user is blocked by another user
  ///
  /// Copied from [isBlockedByUser].
  IsBlockedByUserProvider call(String userId) {
    return IsBlockedByUserProvider(userId);
  }

  @override
  IsBlockedByUserProvider getProviderOverride(
    covariant IsBlockedByUserProvider provider,
  ) {
    return call(provider.userId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'isBlockedByUserProvider';
}

/// Provider to check if current user is blocked by another user
///
/// Copied from [isBlockedByUser].
class IsBlockedByUserProvider extends AutoDisposeFutureProvider<bool> {
  /// Provider to check if current user is blocked by another user
  ///
  /// Copied from [isBlockedByUser].
  IsBlockedByUserProvider(String userId)
    : this._internal(
        (ref) => isBlockedByUser(ref as IsBlockedByUserRef, userId),
        from: isBlockedByUserProvider,
        name: r'isBlockedByUserProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$isBlockedByUserHash,
        dependencies: IsBlockedByUserFamily._dependencies,
        allTransitiveDependencies:
            IsBlockedByUserFamily._allTransitiveDependencies,
        userId: userId,
      );

  IsBlockedByUserProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.userId,
  }) : super.internal();

  final String userId;

  @override
  Override overrideWith(
    FutureOr<bool> Function(IsBlockedByUserRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: IsBlockedByUserProvider._internal(
        (ref) => create(ref as IsBlockedByUserRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        userId: userId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<bool> createElement() {
    return _IsBlockedByUserProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is IsBlockedByUserProvider && other.userId == userId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, userId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin IsBlockedByUserRef on AutoDisposeFutureProviderRef<bool> {
  /// The parameter `userId` of this provider.
  String get userId;
}

class _IsBlockedByUserProviderElement
    extends AutoDisposeFutureProviderElement<bool>
    with IsBlockedByUserRef {
  _IsBlockedByUserProviderElement(super.provider);

  @override
  String get userId => (origin as IsBlockedByUserProvider).userId;
}

String _$blockedUsersHash() => r'567819bf29dfe434a7dbfa78f51783d84e95f40b';

/// Provider for blocked users list
///
/// Copied from [blockedUsers].
@ProviderFor(blockedUsers)
final blockedUsersProvider = AutoDisposeFutureProvider<List<String>>.internal(
  blockedUsers,
  name: r'blockedUsersProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$blockedUsersHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef BlockedUsersRef = AutoDisposeFutureProviderRef<List<String>>;
String _$blockedUsersStreamHash() =>
    r'c72937bb2d9e23d5dbb8af340456b140e77b4dfd';

/// Stream provider for blocked users (real-time updates)
///
/// Copied from [blockedUsersStream].
@ProviderFor(blockedUsersStream)
final blockedUsersStreamProvider =
    AutoDisposeStreamProvider<List<String>>.internal(
      blockedUsersStream,
      name: r'blockedUsersStreamProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$blockedUsersStreamHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef BlockedUsersStreamRef = AutoDisposeStreamProviderRef<List<String>>;
String _$canUsersInteractHash() => r'49dfc926eb6c8c373d59a9ad033bc6a556582d0c';

/// Provider to check if two users can interact
///
/// Copied from [canUsersInteract].
@ProviderFor(canUsersInteract)
const canUsersInteractProvider = CanUsersInteractFamily();

/// Provider to check if two users can interact
///
/// Copied from [canUsersInteract].
class CanUsersInteractFamily extends Family<AsyncValue<bool>> {
  /// Provider to check if two users can interact
  ///
  /// Copied from [canUsersInteract].
  const CanUsersInteractFamily();

  /// Provider to check if two users can interact
  ///
  /// Copied from [canUsersInteract].
  CanUsersInteractProvider call(String userId1, String userId2) {
    return CanUsersInteractProvider(userId1, userId2);
  }

  @override
  CanUsersInteractProvider getProviderOverride(
    covariant CanUsersInteractProvider provider,
  ) {
    return call(provider.userId1, provider.userId2);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'canUsersInteractProvider';
}

/// Provider to check if two users can interact
///
/// Copied from [canUsersInteract].
class CanUsersInteractProvider extends AutoDisposeFutureProvider<bool> {
  /// Provider to check if two users can interact
  ///
  /// Copied from [canUsersInteract].
  CanUsersInteractProvider(String userId1, String userId2)
    : this._internal(
        (ref) => canUsersInteract(ref as CanUsersInteractRef, userId1, userId2),
        from: canUsersInteractProvider,
        name: r'canUsersInteractProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$canUsersInteractHash,
        dependencies: CanUsersInteractFamily._dependencies,
        allTransitiveDependencies:
            CanUsersInteractFamily._allTransitiveDependencies,
        userId1: userId1,
        userId2: userId2,
      );

  CanUsersInteractProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.userId1,
    required this.userId2,
  }) : super.internal();

  final String userId1;
  final String userId2;

  @override
  Override overrideWith(
    FutureOr<bool> Function(CanUsersInteractRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CanUsersInteractProvider._internal(
        (ref) => create(ref as CanUsersInteractRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        userId1: userId1,
        userId2: userId2,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<bool> createElement() {
    return _CanUsersInteractProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CanUsersInteractProvider &&
        other.userId1 == userId1 &&
        other.userId2 == userId2;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, userId1.hashCode);
    hash = _SystemHash.combine(hash, userId2.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CanUsersInteractRef on AutoDisposeFutureProviderRef<bool> {
  /// The parameter `userId1` of this provider.
  String get userId1;

  /// The parameter `userId2` of this provider.
  String get userId2;
}

class _CanUsersInteractProviderElement
    extends AutoDisposeFutureProviderElement<bool>
    with CanUsersInteractRef {
  _CanUsersInteractProviderElement(super.provider);

  @override
  String get userId1 => (origin as CanUsersInteractProvider).userId1;
  @override
  String get userId2 => (origin as CanUsersInteractProvider).userId2;
}

String _$userReportReasonsHash() => r'd2202fddeb98ddd6f486e4f2b8bc722996768795';

/// Provider for report reasons
///
/// Copied from [userReportReasons].
@ProviderFor(userReportReasons)
final userReportReasonsProvider =
    AutoDisposeProvider<List<UserReportReason>>.internal(
      userReportReasons,
      name: r'userReportReasonsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$userReportReasonsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef UserReportReasonsRef = AutoDisposeProviderRef<List<UserReportReason>>;
String _$userBlockingNotifierHash() =>
    r'354f0bebb6eaa7e609a03fc5ac9803e231baa79a';

/// Notifier for managing blocking actions
///
/// Copied from [UserBlockingNotifier].
@ProviderFor(UserBlockingNotifier)
final userBlockingNotifierProvider =
    AutoDisposeAsyncNotifierProvider<
      UserBlockingNotifier,
      List<String>
    >.internal(
      UserBlockingNotifier.new,
      name: r'userBlockingNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$userBlockingNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$UserBlockingNotifier = AutoDisposeAsyncNotifier<List<String>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
