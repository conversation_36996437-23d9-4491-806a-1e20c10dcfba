import 'dart:async';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/features/profile/models/profile_model.dart';
import 'package:billionaires_social/features/profile/services/profile_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'close_friends_provider.g.dart';

class CloseFriendsState {
  final List<ProfileModel> followers;
  final Set<String> closeFriendIds;

  CloseFriendsState({required this.followers, required this.closeFriendIds});
}

@riverpod
class CloseFriendsNotifier extends _$CloseFriendsNotifier {
  @override
  Future<CloseFriendsState> build() async {
    final profileService = getIt<ProfileService>();
    final currentUser = await profileService.getCurrentUserProfile();
    final userId = currentUser?.id ?? '';
    final followers = await profileService.getFollowers(userId);
    final closeFriendIds = await profileService.getCloseFriends();
    return CloseFriendsState(
      followers: followers,
      closeFriendIds: closeFriendIds,
    );
  }

  void toggleCloseFriend(String userId) {
    if (state.value == null) return;

    final currentIds = state.value!.closeFriendIds;
    final newIds = Set<String>.from(currentIds);

    if (newIds.contains(userId)) {
      newIds.remove(userId);
    } else {
      newIds.add(userId);
    }

    _updateState(newIds);
  }

  Future<void> _updateState(Set<String> newIds) async {
    final profileService = getIt<ProfileService>();
    final followers = state.value!.followers;

    // Optimistic update
    state = AsyncData(
      CloseFriendsState(followers: followers, closeFriendIds: newIds),
    );

    try {
      await profileService.updateCloseFriends(newIds);
    } catch (e) {
      // Revert on error
      state = AsyncData(
        CloseFriendsState(
          followers: followers,
          closeFriendIds: state.value!.closeFriendIds,
        ),
      );
    }
  }
}
