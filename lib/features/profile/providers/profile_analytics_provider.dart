import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/profile/models/profile_analytics_model.dart';
import 'package:billionaires_social/features/profile/services/profile_service.dart';
import 'package:billionaires_social/core/service_locator.dart';

final profileLatestAnalyticsProvider = FutureProvider<ProfileAnalyticsModel?>((
  ref,
) async {
  final profileService = getIt<ProfileService>();
  final userId = profileService.getCurrentUserId();
  if (userId == null) return null;
  return await profileService.getLatestAnalytics(userId);
});

final profileAnalyticsHistoryProvider =
    FutureProvider<List<ProfileAnalyticsModel>>((ref) async {
      final profileService = getIt<ProfileService>();
      final userId = profileService.getCurrentUserId();
      if (userId == null) return [];
      return await profileService.getAnalyticsHistory(userId);
    });
