// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'close_friends_groups_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$closeFriendsGroupsNotifierHash() =>
    r'43c7fa3af210af6d9f38922d3d096bc88b3ab1a7';

/// See also [CloseFriendsGroupsNotifier].
@ProviderFor(CloseFriendsGroupsNotifier)
final closeFriendsGroupsNotifierProvider =
    AutoDisposeAsyncNotifierProvider<
      CloseFriendsGroupsNotifier,
      List<CloseFriendsGroup>
    >.internal(
      CloseFriendsGroupsNotifier.new,
      name: r'closeFriendsGroupsNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$closeFriendsGroupsNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$CloseFriendsGroupsNotifier =
    AutoDisposeAsyncNotifier<List<CloseFriendsGroup>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
