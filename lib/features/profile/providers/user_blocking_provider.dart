import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:billionaires_social/features/profile/services/user_blocking_service.dart';
import 'package:flutter/foundation.dart';

part 'user_blocking_provider.g.dart';

/// Provider for user blocking service
@riverpod
UserBlockingService userBlockingService(Ref ref) {
  return UserBlockingService();
}

/// Provider to check if a user is blocked
@riverpod
Future<bool> isUserBlocked(Ref ref, String userId) async {
  final blockingService = ref.watch(userBlockingServiceProvider);
  debugPrint('🚫 Checking if user is blocked: $userId');
  
  return await blockingService.isUserBlocked(userId);
}

/// Provider to check if current user is blocked by another user
@riverpod
Future<bool> isBlockedByUser(Ref ref, String userId) async {
  final blockingService = ref.watch(userBlockingServiceProvider);
  debugPrint('🚫 Checking if blocked by user: $userId');
  
  return await blockingService.isBlockedByUser(userId);
}

/// Provider for blocked users list
@riverpod
Future<List<String>> blockedUsers(Ref ref) async {
  final blockingService = ref.watch(userBlockingServiceProvider);
  debugPrint('🚫 Getting blocked users list');
  
  return await blockingService.getBlockedUsers();
}

/// Stream provider for blocked users (real-time updates)
@riverpod
Stream<List<String>> blockedUsersStream(Ref ref) {
  final blockingService = ref.watch(userBlockingServiceProvider);
  debugPrint('🚫 Setting up blocked users stream');
  
  return blockingService.getBlockedUsersStream();
}

/// Provider to check if two users can interact
@riverpod
Future<bool> canUsersInteract(Ref ref, String userId1, String userId2) async {
  final blockingService = ref.watch(userBlockingServiceProvider);
  debugPrint('🚫 Checking if users can interact: $userId1 and $userId2');
  
  return await blockingService.canUsersInteract(userId1, userId2);
}

/// Notifier for managing blocking actions
@riverpod
class UserBlockingNotifier extends _$UserBlockingNotifier {
  @override
  Future<List<String>> build() async {
    return ref.watch(blockedUsersProvider.future);
  }

  /// Block a user
  Future<void> blockUser(String userId) async {
    final blockingService = ref.read(userBlockingServiceProvider);
    
    try {
      await blockingService.blockUser(userId);
      
      // Update local state
      final currentBlocked = state.value ?? [];
      state = AsyncValue.data([...currentBlocked, userId]);
      
      // Invalidate related providers
      ref.invalidate(isUserBlockedProvider(userId));
      ref.invalidate(canUsersInteractProvider);
      
    } catch (e) {
      debugPrint('❌ Error in UserBlockingNotifier.blockUser: $e');
      rethrow;
    }
  }

  /// Unblock a user
  Future<void> unblockUser(String userId) async {
    final blockingService = ref.read(userBlockingServiceProvider);
    
    try {
      await blockingService.unblockUser(userId);
      
      // Update local state
      final currentBlocked = state.value ?? [];
      state = AsyncValue.data(currentBlocked.where((id) => id != userId).toList());
      
      // Invalidate related providers
      ref.invalidate(isUserBlockedProvider(userId));
      ref.invalidate(canUsersInteractProvider);
      
    } catch (e) {
      debugPrint('❌ Error in UserBlockingNotifier.unblockUser: $e');
      rethrow;
    }
  }

  /// Report a user
  Future<void> reportUser({
    required String userId,
    required String reason,
    String? additionalDetails,
  }) async {
    final blockingService = ref.read(userBlockingServiceProvider);
    
    try {
      await blockingService.reportUser(
        userIdToReport: userId,
        reason: reason,
        additionalDetails: additionalDetails,
      );
      
    } catch (e) {
      debugPrint('❌ Error in UserBlockingNotifier.reportUser: $e');
      rethrow;
    }
  }

  /// Refresh blocked users list
  Future<void> refresh() async {
    ref.invalidateSelf();
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => build());
  }
}

/// Report reasons enum for consistency
enum UserReportReason {
  harassment('Harassment or bullying'),
  spam('Spam or fake account'),
  inappropriateContent('Inappropriate content'),
  impersonation('Impersonation'),
  hateSpeech('Hate speech'),
  violence('Violence or threats'),
  selfHarm('Self-harm or suicide'),
  intellectualProperty('Intellectual property violation'),
  other('Other');

  const UserReportReason(this.displayName);
  final String displayName;
}

/// Provider for report reasons
@riverpod
List<UserReportReason> userReportReasons(Ref ref) {
  return UserReportReason.values;
}
