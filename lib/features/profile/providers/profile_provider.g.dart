// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'profile_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$profileHash() => r'54c0295f35602822c0874d5f39a0b175f1f4b416';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [profile].
@ProviderFor(profile)
const profileProvider = ProfileFamily();

/// See also [profile].
class ProfileFamily extends Family<AsyncValue<ProfileModel>> {
  /// See also [profile].
  const ProfileFamily();

  /// See also [profile].
  ProfileProvider call(String userId) {
    return ProfileProvider(userId);
  }

  @override
  ProfileProvider getProviderOverride(covariant ProfileProvider provider) {
    return call(provider.userId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'profileProvider';
}

/// See also [profile].
class ProfileProvider extends AutoDisposeFutureProvider<ProfileModel> {
  /// See also [profile].
  ProfileProvider(String userId)
    : this._internal(
        (ref) => profile(ref as ProfileRef, userId),
        from: profileProvider,
        name: r'profileProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$profileHash,
        dependencies: ProfileFamily._dependencies,
        allTransitiveDependencies: ProfileFamily._allTransitiveDependencies,
        userId: userId,
      );

  ProfileProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.userId,
  }) : super.internal();

  final String userId;

  @override
  Override overrideWith(
    FutureOr<ProfileModel> Function(ProfileRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ProfileProvider._internal(
        (ref) => create(ref as ProfileRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        userId: userId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<ProfileModel> createElement() {
    return _ProfileProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ProfileProvider && other.userId == userId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, userId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ProfileRef on AutoDisposeFutureProviderRef<ProfileModel> {
  /// The parameter `userId` of this provider.
  String get userId;
}

class _ProfileProviderElement
    extends AutoDisposeFutureProviderElement<ProfileModel>
    with ProfileRef {
  _ProfileProviderElement(super.provider);

  @override
  String get userId => (origin as ProfileProvider).userId;
}

String _$userPostsStreamHash() => r'cf0248411bf408d4fb807c06a2cdfb4207eada2e';

/// See also [userPostsStream].
@ProviderFor(userPostsStream)
const userPostsStreamProvider = UserPostsStreamFamily();

/// See also [userPostsStream].
class UserPostsStreamFamily extends Family<AsyncValue<List<Post>>> {
  /// See also [userPostsStream].
  const UserPostsStreamFamily();

  /// See also [userPostsStream].
  UserPostsStreamProvider call(String userId) {
    return UserPostsStreamProvider(userId);
  }

  @override
  UserPostsStreamProvider getProviderOverride(
    covariant UserPostsStreamProvider provider,
  ) {
    return call(provider.userId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'userPostsStreamProvider';
}

/// See also [userPostsStream].
class UserPostsStreamProvider extends AutoDisposeStreamProvider<List<Post>> {
  /// See also [userPostsStream].
  UserPostsStreamProvider(String userId)
    : this._internal(
        (ref) => userPostsStream(ref as UserPostsStreamRef, userId),
        from: userPostsStreamProvider,
        name: r'userPostsStreamProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$userPostsStreamHash,
        dependencies: UserPostsStreamFamily._dependencies,
        allTransitiveDependencies:
            UserPostsStreamFamily._allTransitiveDependencies,
        userId: userId,
      );

  UserPostsStreamProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.userId,
  }) : super.internal();

  final String userId;

  @override
  Override overrideWith(
    Stream<List<Post>> Function(UserPostsStreamRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: UserPostsStreamProvider._internal(
        (ref) => create(ref as UserPostsStreamRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        userId: userId,
      ),
    );
  }

  @override
  AutoDisposeStreamProviderElement<List<Post>> createElement() {
    return _UserPostsStreamProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is UserPostsStreamProvider && other.userId == userId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, userId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin UserPostsStreamRef on AutoDisposeStreamProviderRef<List<Post>> {
  /// The parameter `userId` of this provider.
  String get userId;
}

class _UserPostsStreamProviderElement
    extends AutoDisposeStreamProviderElement<List<Post>>
    with UserPostsStreamRef {
  _UserPostsStreamProviderElement(super.provider);

  @override
  String get userId => (origin as UserPostsStreamProvider).userId;
}

String _$userPostsWithPinnedStatusStreamHash() =>
    r'996fe29c2bc61b8c725481d7619e538ed26112eb';

/// See also [userPostsWithPinnedStatusStream].
@ProviderFor(userPostsWithPinnedStatusStream)
const userPostsWithPinnedStatusStreamProvider =
    UserPostsWithPinnedStatusStreamFamily();

/// See also [userPostsWithPinnedStatusStream].
class UserPostsWithPinnedStatusStreamFamily
    extends Family<AsyncValue<List<Post>>> {
  /// See also [userPostsWithPinnedStatusStream].
  const UserPostsWithPinnedStatusStreamFamily();

  /// See also [userPostsWithPinnedStatusStream].
  UserPostsWithPinnedStatusStreamProvider call(String userId) {
    return UserPostsWithPinnedStatusStreamProvider(userId);
  }

  @override
  UserPostsWithPinnedStatusStreamProvider getProviderOverride(
    covariant UserPostsWithPinnedStatusStreamProvider provider,
  ) {
    return call(provider.userId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'userPostsWithPinnedStatusStreamProvider';
}

/// See also [userPostsWithPinnedStatusStream].
class UserPostsWithPinnedStatusStreamProvider
    extends AutoDisposeStreamProvider<List<Post>> {
  /// See also [userPostsWithPinnedStatusStream].
  UserPostsWithPinnedStatusStreamProvider(String userId)
    : this._internal(
        (ref) => userPostsWithPinnedStatusStream(
          ref as UserPostsWithPinnedStatusStreamRef,
          userId,
        ),
        from: userPostsWithPinnedStatusStreamProvider,
        name: r'userPostsWithPinnedStatusStreamProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$userPostsWithPinnedStatusStreamHash,
        dependencies: UserPostsWithPinnedStatusStreamFamily._dependencies,
        allTransitiveDependencies:
            UserPostsWithPinnedStatusStreamFamily._allTransitiveDependencies,
        userId: userId,
      );

  UserPostsWithPinnedStatusStreamProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.userId,
  }) : super.internal();

  final String userId;

  @override
  Override overrideWith(
    Stream<List<Post>> Function(UserPostsWithPinnedStatusStreamRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: UserPostsWithPinnedStatusStreamProvider._internal(
        (ref) => create(ref as UserPostsWithPinnedStatusStreamRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        userId: userId,
      ),
    );
  }

  @override
  AutoDisposeStreamProviderElement<List<Post>> createElement() {
    return _UserPostsWithPinnedStatusStreamProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is UserPostsWithPinnedStatusStreamProvider &&
        other.userId == userId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, userId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin UserPostsWithPinnedStatusStreamRef
    on AutoDisposeStreamProviderRef<List<Post>> {
  /// The parameter `userId` of this provider.
  String get userId;
}

class _UserPostsWithPinnedStatusStreamProviderElement
    extends AutoDisposeStreamProviderElement<List<Post>>
    with UserPostsWithPinnedStatusStreamRef {
  _UserPostsWithPinnedStatusStreamProviderElement(super.provider);

  @override
  String get userId =>
      (origin as UserPostsWithPinnedStatusStreamProvider).userId;
}

String _$followerCountHash() => r'27df9f7c06ff85527f9c09531f1549adbb5a14aa';

/// Real-time follower count stream
///
/// Copied from [followerCount].
@ProviderFor(followerCount)
const followerCountProvider = FollowerCountFamily();

/// Real-time follower count stream
///
/// Copied from [followerCount].
class FollowerCountFamily extends Family<AsyncValue<int>> {
  /// Real-time follower count stream
  ///
  /// Copied from [followerCount].
  const FollowerCountFamily();

  /// Real-time follower count stream
  ///
  /// Copied from [followerCount].
  FollowerCountProvider call(String userId) {
    return FollowerCountProvider(userId);
  }

  @override
  FollowerCountProvider getProviderOverride(
    covariant FollowerCountProvider provider,
  ) {
    return call(provider.userId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'followerCountProvider';
}

/// Real-time follower count stream
///
/// Copied from [followerCount].
class FollowerCountProvider extends AutoDisposeStreamProvider<int> {
  /// Real-time follower count stream
  ///
  /// Copied from [followerCount].
  FollowerCountProvider(String userId)
    : this._internal(
        (ref) => followerCount(ref as FollowerCountRef, userId),
        from: followerCountProvider,
        name: r'followerCountProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$followerCountHash,
        dependencies: FollowerCountFamily._dependencies,
        allTransitiveDependencies:
            FollowerCountFamily._allTransitiveDependencies,
        userId: userId,
      );

  FollowerCountProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.userId,
  }) : super.internal();

  final String userId;

  @override
  Override overrideWith(
    Stream<int> Function(FollowerCountRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FollowerCountProvider._internal(
        (ref) => create(ref as FollowerCountRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        userId: userId,
      ),
    );
  }

  @override
  AutoDisposeStreamProviderElement<int> createElement() {
    return _FollowerCountProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FollowerCountProvider && other.userId == userId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, userId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FollowerCountRef on AutoDisposeStreamProviderRef<int> {
  /// The parameter `userId` of this provider.
  String get userId;
}

class _FollowerCountProviderElement
    extends AutoDisposeStreamProviderElement<int>
    with FollowerCountRef {
  _FollowerCountProviderElement(super.provider);

  @override
  String get userId => (origin as FollowerCountProvider).userId;
}

String _$followingCountHash() => r'9cb42fa1aace90aaf35d958049baae5fc6e8fa41';

/// Real-time following count stream
///
/// Copied from [followingCount].
@ProviderFor(followingCount)
const followingCountProvider = FollowingCountFamily();

/// Real-time following count stream
///
/// Copied from [followingCount].
class FollowingCountFamily extends Family<AsyncValue<int>> {
  /// Real-time following count stream
  ///
  /// Copied from [followingCount].
  const FollowingCountFamily();

  /// Real-time following count stream
  ///
  /// Copied from [followingCount].
  FollowingCountProvider call(String userId) {
    return FollowingCountProvider(userId);
  }

  @override
  FollowingCountProvider getProviderOverride(
    covariant FollowingCountProvider provider,
  ) {
    return call(provider.userId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'followingCountProvider';
}

/// Real-time following count stream
///
/// Copied from [followingCount].
class FollowingCountProvider extends AutoDisposeStreamProvider<int> {
  /// Real-time following count stream
  ///
  /// Copied from [followingCount].
  FollowingCountProvider(String userId)
    : this._internal(
        (ref) => followingCount(ref as FollowingCountRef, userId),
        from: followingCountProvider,
        name: r'followingCountProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$followingCountHash,
        dependencies: FollowingCountFamily._dependencies,
        allTransitiveDependencies:
            FollowingCountFamily._allTransitiveDependencies,
        userId: userId,
      );

  FollowingCountProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.userId,
  }) : super.internal();

  final String userId;

  @override
  Override overrideWith(
    Stream<int> Function(FollowingCountRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FollowingCountProvider._internal(
        (ref) => create(ref as FollowingCountRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        userId: userId,
      ),
    );
  }

  @override
  AutoDisposeStreamProviderElement<int> createElement() {
    return _FollowingCountProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FollowingCountProvider && other.userId == userId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, userId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FollowingCountRef on AutoDisposeStreamProviderRef<int> {
  /// The parameter `userId` of this provider.
  String get userId;
}

class _FollowingCountProviderElement
    extends AutoDisposeStreamProviderElement<int>
    with FollowingCountRef {
  _FollowingCountProviderElement(super.provider);

  @override
  String get userId => (origin as FollowingCountProvider).userId;
}

String _$userProfileHash() => r'f5dfd9d6ef1aa0798db98cb79088c110e6d6285d';

/// See also [UserProfile].
@ProviderFor(UserProfile)
final userProfileProvider =
    AsyncNotifierProvider<UserProfile, ProfileModel>.internal(
      UserProfile.new,
      name: r'userProfileProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$userProfileHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$UserProfile = AsyncNotifier<ProfileModel>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
