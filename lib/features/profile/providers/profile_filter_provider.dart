import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import 'package:billionaires_social/features/feed/providers/feed_filter_provider.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';

enum PostTimeFilter { all, thisMonth, thisYear, custom }

class ProfileFilterState {
  final PostTimeFilter timeFilter;
  final ContentTypeFilter contentTypeFilter;
  final DateTime? customStartDate;
  final DateTime? customEndDate;
  final bool isLoading;

  const ProfileFilterState({
    this.timeFilter = PostTimeFilter.all,
    this.contentTypeFilter = ContentTypeFilter.all,
    this.customStartDate,
    this.customEndDate,
    this.isLoading = false,
  });

  ProfileFilterState copyWith({
    PostTimeFilter? timeFilter,
    ContentTypeFilter? contentTypeFilter,
    DateTime? customStartDate,
    DateTime? customEndDate,
    bool? isLoading,
    bool clearCustomDates = false,
  }) {
    return ProfileFilterState(
      timeFilter: timeFilter ?? this.timeFilter,
      contentTypeFilter: contentTypeFilter ?? this.contentTypeFilter,
      customStartDate: clearCustomDates
          ? null
          : (customStartDate ?? this.customStartDate),
      customEndDate: clearCustomDates
          ? null
          : (customEndDate ?? this.customEndDate),
      isLoading: isLoading ?? this.isLoading,
    );
  }

  bool get hasActiveFilter => timeFilter != PostTimeFilter.all;

  bool get isValidCustomRange {
    if (timeFilter != PostTimeFilter.custom) return true;
    if (customStartDate == null || customEndDate == null) return false;
    return !customStartDate!.isAfter(customEndDate!);
  }

  String get displayText {
    switch (timeFilter) {
      case PostTimeFilter.all:
        return 'All Posts';
      case PostTimeFilter.thisMonth:
        return 'This Month';
      case PostTimeFilter.thisYear:
        return 'This Year';
      case PostTimeFilter.custom:
        if (customStartDate != null && customEndDate != null) {
          return '${customStartDate!.day}/${customStartDate!.month} - ${customEndDate!.day}/${customEndDate!.month}';
        }
        return 'Custom Range';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProfileFilterState &&
        other.timeFilter == timeFilter &&
        other.customStartDate == customStartDate &&
        other.customEndDate == customEndDate &&
        other.isLoading == isLoading;
  }

  @override
  int get hashCode =>
      Object.hash(timeFilter, customStartDate, customEndDate, isLoading);

  @override
  String toString() {
    return 'ProfileFilterState(timeFilter: $timeFilter, customStartDate: $customStartDate, customEndDate: $customEndDate, isLoading: $isLoading)';
  }
}

class ProfileFilterNotifier extends StateNotifier<ProfileFilterState> {
  ProfileFilterNotifier() : super(const ProfileFilterState());

  void setTimeFilter(PostTimeFilter filter) {
    if (filter == PostTimeFilter.custom) {
      // Keep existing custom dates if switching to custom
      state = state.copyWith(timeFilter: filter);
    } else {
      // Clear custom dates when switching away from custom
      state = state.copyWith(timeFilter: filter, clearCustomDates: true);
    }
  }

  void setContentTypeFilter(ContentTypeFilter filter) {
    state = state.copyWith(contentTypeFilter: filter);
  }

  void setCustomDateRange(DateTime? startDate, DateTime? endDate) {
    state = state.copyWith(customStartDate: startDate, customEndDate: endDate);
  }

  void setLoading(bool loading) {
    state = state.copyWith(isLoading: loading);
  }

  void resetFilter() {
    state = const ProfileFilterState();
  }

  void applyCustomFilter(DateTime startDate, DateTime endDate) {
    if (startDate.isAfter(endDate)) {
      debugPrint('Invalid date range: start date is after end date');
      return;
    }

    state = state.copyWith(
      timeFilter: PostTimeFilter.custom,
      customStartDate: startDate,
      customEndDate: endDate,
    );
  }
}

// Provider for profile filter state
final profileFilterProvider =
    StateNotifierProvider<ProfileFilterNotifier, ProfileFilterState>((ref) {
      return ProfileFilterNotifier();
    });

// Provider for filtered posts
final filteredPostsProvider = Provider.family<List<dynamic>, List<dynamic>>((
  ref,
  posts,
) {
  final filterState = ref.watch(profileFilterProvider);

  debugPrint(
    '🔍 filteredPostsProvider called with ${posts.length} posts, time filter: ${filterState.timeFilter}, content filter: ${filterState.contentTypeFilter}',
  );

  // Apply both time and content type filtering
  final now = DateTime.now();
  debugPrint(
    '🔍 Filtering posts for time: ${filterState.timeFilter}, content: ${filterState.contentTypeFilter}',
  );

  final filtered = posts.where((post) {
    // First apply content type filtering
    if (filterState.contentTypeFilter != ContentTypeFilter.all) {
      final mediaType = _getPostMediaType(post);
      if (mediaType != null) {
        switch (filterState.contentTypeFilter) {
          case ContentTypeFilter.media:
            if (mediaType != MediaType.image && mediaType != MediaType.video) {
              return false;
            }
            break;
          case ContentTypeFilter.text:
            if (mediaType != MediaType.text) {
              // Also check for posts with empty mediaUrl but non-empty caption
              final mediaUrl = _getPostMediaUrl(post);
              final caption = _getPostCaption(post);
              if (!(mediaUrl?.isEmpty == true && caption?.isNotEmpty == true)) {
                return false;
              }
            }
            break;
          case ContentTypeFilter.all:
            break;
        }
      }
    }

    // Then apply time filtering
    if (filterState.timeFilter == PostTimeFilter.all) {
      return true;
    }
    // Assuming post has a timestamp field
    final postTimestamp = _getPostTimestamp(post);
    if (postTimestamp == null) {
      debugPrint('🔍 Post has null timestamp, including in results');
      return true;
    }

    debugPrint('🔍 Post timestamp: $postTimestamp');

    switch (filterState.timeFilter) {
      case PostTimeFilter.all:
        return true;
      case PostTimeFilter.thisMonth:
        return postTimestamp.year == now.year &&
            postTimestamp.month == now.month;
      case PostTimeFilter.thisYear:
        return postTimestamp.year == now.year;
      case PostTimeFilter.custom:
        if (filterState.customStartDate != null &&
            filterState.customEndDate != null) {
          // Use start of day for start date and end of day for end date
          final startOfDay = DateTime(
            filterState.customStartDate!.year,
            filterState.customStartDate!.month,
            filterState.customStartDate!.day,
          );
          final endOfDay = DateTime(
            filterState.customEndDate!.year,
            filterState.customEndDate!.month,
            filterState.customEndDate!.day,
            23,
            59,
            59,
          );

          return postTimestamp.isAfter(
                startOfDay.subtract(const Duration(milliseconds: 1)),
              ) &&
              postTimestamp.isBefore(
                endOfDay.add(const Duration(milliseconds: 1)),
              );
        }
        return true;
    }
  }).toList();

  debugPrint('🔍 Filtered ${posts.length} posts to ${filtered.length} posts');
  return filtered;
});

// Helper functions to extract post properties
MediaType? _getPostMediaType(dynamic post) {
  try {
    if (post is Map<String, dynamic>) {
      final mediaTypeValue = post['mediaType'];
      if (mediaTypeValue is String) {
        switch (mediaTypeValue) {
          case 'image':
            return MediaType.image;
          case 'video':
            return MediaType.video;
          case 'text':
            return MediaType.text;
          default:
            return null;
        }
      }
      return null;
    } else {
      // For Post model objects, directly access the mediaType property
      return (post as dynamic).mediaType as MediaType?;
    }
  } catch (e) {
    debugPrint('Error extracting mediaType from post: $e');
    return null;
  }
}

String? _getPostMediaUrl(dynamic post) {
  try {
    if (post is Map<String, dynamic>) {
      return post['mediaUrl'] as String?;
    } else {
      // For Post model objects, directly access the mediaUrl property
      return (post as dynamic).mediaUrl as String?;
    }
  } catch (e) {
    debugPrint('Error extracting mediaUrl from post: $e');
    return null;
  }
}

String? _getPostCaption(dynamic post) {
  try {
    if (post is Map<String, dynamic>) {
      return post['caption'] as String?;
    } else {
      // For Post model objects, directly access the caption property
      return (post as dynamic).caption as String?;
    }
  } catch (e) {
    debugPrint('Error extracting caption from post: $e');
    return null;
  }
}

// Helper function to extract timestamp from post object
DateTime? _getPostTimestamp(dynamic post) {
  try {
    // Handle different post types
    if (post is Map<String, dynamic>) {
      final timestampValue = post['timestamp'];
      if (timestampValue is DateTime) {
        return timestampValue;
      } else if (timestampValue is String) {
        return DateTime.parse(timestampValue);
      }
      return null;
    } else {
      // For Post model objects, directly access the timestamp property
      return (post as dynamic).timestamp as DateTime?;
    }
  } catch (e) {
    debugPrint('Error extracting timestamp from post: $e');
    debugPrint('Post type: ${post.runtimeType}');
    debugPrint('Post data: $post');
    return null;
  }
}

// Provider for filter statistics
final filterStatsProvider = Provider.family<FilterStats, List<dynamic>>((
  ref,
  posts,
) {
  final filterState = ref.watch(profileFilterProvider);
  final filteredPosts = ref.watch(filteredPostsProvider(posts));

  return FilterStats(
    totalPosts: posts.length,
    filteredPosts: filteredPosts.length,
    hasActiveFilter: filterState.hasActiveFilter,
    filterType: filterState.timeFilter,
  );
});

class FilterStats {
  final int totalPosts;
  final int filteredPosts;
  final bool hasActiveFilter;
  final PostTimeFilter filterType;

  const FilterStats({
    required this.totalPosts,
    required this.filteredPosts,
    required this.hasActiveFilter,
    required this.filterType,
  });

  double get filterPercentage {
    if (totalPosts == 0) return 0.0;
    return (filteredPosts / totalPosts) * 100;
  }

  @override
  String toString() {
    return 'FilterStats(total: $totalPosts, filtered: $filteredPosts, active: $hasActiveFilter, type: $filterType)';
  }
}
