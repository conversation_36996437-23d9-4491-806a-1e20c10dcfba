import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:billionaires_social/features/profile/models/account_model.dart';
import 'package:billionaires_social/features/profile/services/account_management_service.dart';
import 'package:billionaires_social/core/service_locator.dart';

// Service provider
final accountManagementServiceProvider = Provider<AccountManagementService>((
  ref,
) {
  return getIt<AccountManagementService>();
});

// Accounts list provider
final accountsProvider = StreamProvider<List<AccountModel>>((ref) {
  final service = ref.watch(accountManagementServiceProvider);
  return service.accountsStream();
});

// Active account provider
final activeAccountProvider = FutureProvider<AccountModel?>((ref) async {
  final service = ref.watch(accountManagementServiceProvider);
  return await service.getActiveAccount();
});

// Account management notifier
class AccountManagementNotifier
    extends StateNotifier<AsyncValue<List<AccountModel>>> {
  final AccountManagementService _service;

  AccountManagementNotifier(this._service) : super(const AsyncValue.loading()) {
    _loadAccounts();
  }

  Future<void> _loadAccounts() async {
    try {
      final accounts = await _service.getUserAccounts();
      state = AsyncValue.data(accounts);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Create a new account
  Future<void> createAccount({
    required String name,
    required String username,
    required AccountType accountType,
    String? profilePictureUrl,
    String? bio,
  }) async {
    try {
      await _service.createAccount(
        name: name,
        username: username,
        accountType: accountType,
        profilePictureUrl: profilePictureUrl,
        bio: bio,
      );
      await _loadAccounts(); // Refresh the list
    } catch (error) {
      rethrow;
    }
  }

  /// Switch to a different account
  Future<void> switchToAccount(String accountId) async {
    try {
      await _service.switchToAccount(accountId);
      await _loadAccounts(); // Refresh the list
    } catch (error) {
      rethrow;
    }
  }

  /// Update account information
  Future<void> updateAccount(AccountModel account) async {
    try {
      await _service.updateAccount(account);
      await _loadAccounts(); // Refresh the list
    } catch (error) {
      rethrow;
    }
  }

  /// Delete an account
  Future<void> deleteAccount(String accountId) async {
    try {
      await _service.deleteAccount(accountId);
      await _loadAccounts(); // Refresh the list
    } catch (error) {
      rethrow;
    }
  }

  /// Refresh accounts list
  Future<void> refresh() async {
    await _loadAccounts();
  }

  /// Check if username is available
  Future<bool> isUsernameAvailable(String username) async {
    try {
      // Use the private method from the service
      final snapshot = await FirebaseFirestore.instance
          .collectionGroup('accounts')
          .where('username', isEqualTo: username.toLowerCase())
          .limit(1)
          .get();

      return snapshot.docs.isEmpty;
    } catch (e) {
      debugPrint('❌ Error checking username availability: $e');
      return false;
    }
  }
}

// Account management notifier provider
final accountManagementNotifierProvider =
    StateNotifierProvider<
      AccountManagementNotifier,
      AsyncValue<List<AccountModel>>
    >((ref) {
      final service = ref.watch(accountManagementServiceProvider);
      return AccountManagementNotifier(service);
    });

// Active account notifier for real-time updates
class ActiveAccountNotifier extends StateNotifier<AsyncValue<AccountModel?>> {
  final AccountManagementService _service;

  ActiveAccountNotifier(this._service) : super(const AsyncValue.loading()) {
    _loadActiveAccount();
  }

  Future<void> _loadActiveAccount() async {
    try {
      final account = await _service.getActiveAccount();
      state = AsyncValue.data(account);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Switch to a different account and update active account
  Future<void> switchToAccount(String accountId) async {
    try {
      await _service.switchToAccount(accountId);
      await _loadActiveAccount(); // Refresh active account
    } catch (error) {
      rethrow;
    }
  }

  /// Refresh active account
  Future<void> refresh() async {
    await _loadActiveAccount();
  }
}

// Active account notifier provider
final activeAccountNotifierProvider =
    StateNotifierProvider<ActiveAccountNotifier, AsyncValue<AccountModel?>>((
      ref,
    ) {
      final service = ref.watch(accountManagementServiceProvider);
      return ActiveAccountNotifier(service);
    });

// Helper providers for specific account data
final currentAccountProvider = Provider<AccountModel?>((ref) {
  final activeAccountAsync = ref.watch(activeAccountNotifierProvider);
  return activeAccountAsync.maybeWhen(
    data: (account) => account,
    orElse: () => null,
  );
});

// Provider for checking if user has multiple accounts
final hasMultipleAccountsProvider = Provider<bool>((ref) {
  final accountsAsync = ref.watch(accountManagementNotifierProvider);
  return accountsAsync.maybeWhen(
    data: (accounts) => accounts.length > 1,
    orElse: () => false,
  );
});

// Provider for getting account by ID
final accountByIdProvider = Provider.family<AccountModel?, String>((
  ref,
  accountId,
) {
  final accountsAsync = ref.watch(accountManagementNotifierProvider);
  return accountsAsync.maybeWhen(
    data: (accounts) =>
        accounts.where((account) => account.id == accountId).firstOrNull,
    orElse: () => null,
  );
});

// Provider for getting accounts by type
final accountsByTypeProvider = Provider.family<List<AccountModel>, AccountType>(
  (ref, type) {
    final accountsAsync = ref.watch(accountManagementNotifierProvider);
    return accountsAsync.maybeWhen(
      data: (accounts) =>
          accounts.where((account) => account.accountType == type).toList(),
      orElse: () => [],
    );
  },
);

// Provider for account statistics
final accountStatsProvider = Provider<Map<String, int>>((ref) {
  final accountsAsync = ref.watch(accountManagementNotifierProvider);
  return accountsAsync.maybeWhen(
    data: (accounts) {
      final stats = <String, int>{
        'total': accounts.length,
        'personal': accounts
            .where((a) => a.accountType == AccountType.personal)
            .length,
        'business': accounts
            .where((a) => a.accountType == AccountType.business)
            .length,
        'celebrity': accounts
            .where((a) => a.accountType == AccountType.celebrity)
            .length,
        'verified': accounts.where((a) => a.isVerified).length,
        'billionaire': accounts.where((a) => a.isBillionaire).length,
      };
      return stats;
    },
    orElse: () => {
      'total': 0,
      'personal': 0,
      'business': 0,
      'celebrity': 0,
      'verified': 0,
      'billionaire': 0,
    },
  );
});

// Username availability checker
final usernameAvailabilityProvider = FutureProvider.family<bool, String>((
  ref,
  username,
) async {
  if (username.isEmpty) return false;

  // This would need to be implemented in the service
  // For now, return true as placeholder
  return true;
});

// Account creation state provider
enum AccountCreationState { idle, creating, success, error }

class AccountCreationNotifier extends StateNotifier<AccountCreationState> {
  final AccountManagementService _service;
  String? _errorMessage;

  AccountCreationNotifier(this._service) : super(AccountCreationState.idle);

  String? get errorMessage => _errorMessage;

  Future<void> createAccount({
    required String name,
    required String username,
    required AccountType accountType,
    String? profilePictureUrl,
    String? bio,
  }) async {
    state = AccountCreationState.creating;
    _errorMessage = null;

    try {
      await _service.createAccount(
        name: name,
        username: username,
        accountType: accountType,
        profilePictureUrl: profilePictureUrl,
        bio: bio,
      );
      state = AccountCreationState.success;
    } catch (error) {
      _errorMessage = error.toString();
      state = AccountCreationState.error;
    }
  }

  void reset() {
    state = AccountCreationState.idle;
    _errorMessage = null;
  }
}

final accountCreationNotifierProvider =
    StateNotifierProvider<AccountCreationNotifier, AccountCreationState>((ref) {
      final service = ref.watch(accountManagementServiceProvider);
      return AccountCreationNotifier(service);
    });
