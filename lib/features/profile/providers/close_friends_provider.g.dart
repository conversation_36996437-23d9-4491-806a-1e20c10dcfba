// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'close_friends_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$closeFriendsNotifierHash() =>
    r'0f9b39bb55602304c2817105742bc2b4c9737a42';

/// See also [CloseFriendsNotifier].
@ProviderFor(CloseFriendsNotifier)
final closeFriendsNotifierProvider =
    AutoDisposeAsyncNotifierProvider<
      CloseFriendsNotifier,
      CloseFriendsState
    >.internal(
      CloseFriendsNotifier.new,
      name: r'closeFriendsNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$closeFriendsNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$CloseFriendsNotifier = AutoDisposeAsyncNotifier<CloseFriendsState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
