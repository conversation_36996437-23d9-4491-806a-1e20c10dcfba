import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/auth/providers/auth_provider.dart';
import 'package:billionaires_social/features/profile/models/user_profile_model.dart';
import 'package:flutter/foundation.dart';

part 'user_profile_provider.g.dart';

@riverpod
Stream<UserProfile?> userProfile(Ref ref) {
  final authState = ref.watch(authProvider);
  final firestore = FirebaseFirestore.instance;

  return authState.when(
    data: (user) {
      if (user != null) {
        debugPrint('🔐 Auth state: User authenticated with UID: ${user.uid}');
        return firestore.collection('users').doc(user.uid).snapshots().map((
          snapshot,
        ) {
          if (snapshot.exists) {
            debugPrint('📄 User profile document exists for UID: ${user.uid}');
            try {
              final profile = UserProfile.fromFirestore(snapshot, null);
              debugPrint(
                '✅ User profile loaded successfully: ${profile.name} (${profile.email})',
              );
              return profile;
            } catch (e) {
              debugPrint('❌ Error loading user profile: $e');
              return null;
            }
          } else {
            debugPrint(
              '❌ User profile document does not exist for UID: ${user.uid}',
            );
            return null;
          }
        });
      } else {
        debugPrint('🔐 Auth state: No user authenticated');
        return Stream.value(null);
      }
    },
    loading: () {
      debugPrint('⏳ Auth state: Loading...');
      return Stream.value(null);
    },
    error: (error, stack) {
      debugPrint('❌ Auth state error: $error');
      return Stream.value(null);
    },
  );
}
