import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/profile/models/profile_verification_model.dart';
import 'package:billionaires_social/features/profile/services/profile_service.dart';
import 'package:billionaires_social/core/service_locator.dart';

final profileVerificationsProvider =
    FutureProvider<List<ProfileVerificationModel>>((ref) async {
      final profileService = getIt<ProfileService>();
      final userId = profileService.getCurrentUserId();
      if (userId == null) return [];
      return await profileService.getVerifications(userId);
    });

final profileVerificationByTypeProvider =
    FutureProvider.family<List<ProfileVerificationModel>, VerificationType>((
      ref,
      type,
    ) async {
      final profileService = getIt<ProfileService>();
      final userId = profileService.getCurrentUserId();
      if (userId == null) return [];
      final all = await profileService.getVerifications(userId);
      return all.where((v) => v.type == type).toList();
    });

final profileVerificationStatusProvider =
    FutureProvider.family<VerificationStatus?, VerificationType>((
      ref,
      type,
    ) async {
      final profileService = getIt<ProfileService>();
      final userId = profileService.getCurrentUserId();
      if (userId == null) return null;
      final all = await profileService.getVerifications(userId);
      final v = all.where((v) => v.type == type).toList();
      if (v.isEmpty) return null;
      return v.first.status;
    });
