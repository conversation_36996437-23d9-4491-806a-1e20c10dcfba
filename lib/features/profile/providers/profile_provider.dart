import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/features/profile/models/profile_model.dart';
import 'package:billionaires_social/features/profile/services/profile_service.dart';
import 'package:billionaires_social/core/services/cache_service.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

part 'profile_provider.g.dart';

@riverpod
Future<ProfileModel> profile(Ref ref, String userId) async {
  debugPrint('🔄 Profile provider: Fetching profile for user: $userId');

  final profileService = getIt<ProfileService>();
  final cacheService = getIt<CacheService>();

  // Check cache first
  final cachedProfile = await cacheService.getCachedUserProfile(userId);
  if (cachedProfile != null) {
    try {
      debugPrint('🔄 Profile provider: Using cached profile for user: $userId');
      final profile = ProfileModel.fromJson(cachedProfile);
      debugPrint(
        '🔄 Profile provider: Cached profile picture URL: ${profile.profilePictureUrl}',
      );

      // Return cached data immediately
      // Note: Background refresh removed to prevent excessive API calls
      // Cache will expire in 5 minutes and fresh data will be fetched then
      return profile;
    } catch (e) {
      debugPrint(
        '❌ Profile provider: Invalid cached data, removing cache for user: $userId',
      );
      // If cached data is invalid, remove it and continue with fresh fetch
      await cacheService.removeData('user_profile_$userId');
    }
  }

  debugPrint(
    '🔄 Profile provider: Fetching fresh profile from Firestore for user: $userId',
  );
  // Fetch fresh data from Firestore
  final profile = await profileService.getUserProfile(userId);
  if (profile == null) {
    throw Exception('Profile not found for user: $userId');
  }

  debugPrint(
    '🔄 Profile provider: Fresh profile picture URL: ${profile.profilePictureUrl}',
  );
  // Cache the fresh data
  await cacheService.cacheUserProfile(userId, profile.toJson());

  return profile;
}

// Background refresh functions removed to prevent excessive API calls
// Cache TTL is now 5 minutes, so fresh data will be fetched when cache expires

@Riverpod(keepAlive: true)
class UserProfile extends _$UserProfile {
  @override
  Future<ProfileModel> build() async {
    debugPrint('🔄 UserProfile provider: Building current user profile');

    // Fetches the current user's profile with caching
    final profileService = getIt<ProfileService>();
    final cacheService = getIt<CacheService>();

    // Get current user ID
    final currentUser = profileService.getCurrentUserId();
    if (currentUser == null) {
      throw Exception('No user is currently signed in');
    }

    debugPrint('🔄 UserProfile provider: Current user ID: $currentUser');

    // Check cache first
    final cachedProfile = await cacheService.getCachedUserProfile(currentUser);
    if (cachedProfile != null) {
      try {
        debugPrint(
          '🔄 UserProfile provider: Using cached profile for current user',
        );
        final profile = ProfileModel.fromJson(cachedProfile);
        debugPrint(
          '🔄 UserProfile provider: Cached profile picture URL: ${profile.profilePictureUrl}',
        );

        // Return cached data immediately
        // Note: Background refresh removed to prevent excessive API calls
        // Cache will expire in 5 minutes and fresh data will be fetched then
        return profile;
      } catch (e) {
        debugPrint(
          '❌ UserProfile provider: Invalid cached data, removing cache',
        );
        // If cached data is invalid, remove it and continue with fresh fetch
        await cacheService.removeData('user_profile_$currentUser');
      }
    }

    debugPrint(
      '🔄 UserProfile provider: Fetching fresh profile from Firestore for current user',
    );
    // Fetch fresh data from Firestore
    final profile = await profileService.getCurrentUserProfile();
    if (profile == null) {
      throw Exception('Current user profile not found');
    }

    debugPrint(
      '🔄 UserProfile provider: Fresh profile picture URL: ${profile.profilePictureUrl}',
    );
    // Cache the fresh data
    await cacheService.cacheUserProfile(currentUser, profile.toJson());

    return profile;
  }

  Future<void> refresh() async {
    final profileService = getIt<ProfileService>();
    final cacheService = getIt<CacheService>();

    final currentUser = profileService.getCurrentUserId();
    if (currentUser == null) return;

    // Clear cache and refetch
    await cacheService.removeData('user_profile_$currentUser');
    ref.invalidateSelf();
  }

  /// Refresh profile counts after follow/unfollow actions
  Future<void> refreshCounts(String userId) async {
    final cacheService = getIt<CacheService>();

    // Clear cache for the specific user
    await cacheService.removeData('user_profile_$userId');

    // If this is the current user's profile, refresh it
    final profileService = getIt<ProfileService>();
    final currentUser = profileService.getCurrentUserId();
    if (currentUser == userId) {
      ref.invalidateSelf();
    } else {
      // Invalidate the specific user's profile
      ref.invalidate(profileProvider(userId));
    }
  }
}

// Background refresh function removed to prevent excessive API calls

@riverpod
Stream<List<Post>> userPostsStream(Ref ref, String userId) {
  final profileService = getIt<ProfileService>();
  return profileService.getUserPostsStream(userId);
}

@riverpod
Stream<List<Post>> userPostsWithPinnedStatusStream(Ref ref, String userId) {
  final profileService = getIt<ProfileService>();
  return profileService.getUserPostsWithPinnedStatusStream(userId);
}

/// Real-time follower count stream
@riverpod
Stream<int> followerCount(Ref ref, String userId) {
  return FirebaseFirestore.instance
      .collection('follows')
      .where('followingId', isEqualTo: userId)
      .where('isActive', isEqualTo: true)
      .snapshots()
      .map((snapshot) => snapshot.docs.length);
}

/// Real-time following count stream
@riverpod
Stream<int> followingCount(Ref ref, String userId) {
  return FirebaseFirestore.instance
      .collection('follows')
      .where('followerId', isEqualTo: userId)
      .where('isActive', isEqualTo: true)
      .snapshots()
      .map((snapshot) => snapshot.docs.length);
}
