import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/features/profile/models/close_friends_group_model.dart';
import 'package:billionaires_social/features/profile/services/close_friends_group_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'close_friends_groups_provider.g.dart';

@riverpod
class CloseFriendsGroupsNotifier extends _$CloseFriendsGroupsNotifier {
  late final CloseFriendsGroupService _service;

  @override
  Future<List<CloseFriendsGroup>> build() async {
    _service = getIt<CloseFriendsGroupService>();
    return _service.getGroups();
  }

  Future<void> refresh() async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(() => _service.getGroups());
  }

  Future<void> createGroup({
    required String name,
    required String emoji,
  }) async {
    final group = await _service.createGroup(name: name, emoji: emoji);
    state = AsyncValue.data([...?state.value, group]);
  }

  Future<void> updateGroup(CloseFriendsGroup group) async {
    final updated = await _service.updateGroup(group);
    state = AsyncValue.data([
      for (final g in state.value ?? [])
        if (g.id == updated.id) updated else g,
    ]);
  }

  Future<void> deleteGroup(String groupId) async {
    await _service.deleteGroup(groupId);
    state = AsyncValue.data([
      for (final g in state.value ?? [])
        if (g.id != groupId) g,
    ]);
  }

  Future<void> addMember(String groupId, String userId) async {
    await _service.addMember(groupId, userId);
    await refresh();
  }

  Future<void> removeMember(String groupId, String userId) async {
    await _service.removeMember(groupId, userId);
    await refresh();
  }

  Future<void> setStoryVisibility(String groupId, bool visible) async {
    await _service.setStoryVisibility(groupId, visible);
    await refresh();
  }
}
