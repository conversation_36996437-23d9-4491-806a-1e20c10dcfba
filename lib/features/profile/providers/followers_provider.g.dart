// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'followers_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$followersServiceHash() => r'd39b2c70ab76787890eac36eb10f335770b215e2';

/// See also [followersService].
@ProviderFor(followersService)
final followersServiceProvider = AutoDisposeProvider<FollowersService>.internal(
  followersService,
  name: r'followersServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$followersServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FollowersServiceRef = AutoDisposeProviderRef<FollowersService>;
String _$followersHash() => r'5ad7425c22279e5dfc75eef5de67db4ece51e041';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$Followers
    extends BuildlessAutoDisposeAsyncNotifier<List<FollowerModel>> {
  late final String userId;

  FutureOr<List<FollowerModel>> build(String userId);
}

/// See also [Followers].
@ProviderFor(Followers)
const followersProvider = FollowersFamily();

/// See also [Followers].
class FollowersFamily extends Family<AsyncValue<List<FollowerModel>>> {
  /// See also [Followers].
  const FollowersFamily();

  /// See also [Followers].
  FollowersProvider call(String userId) {
    return FollowersProvider(userId);
  }

  @override
  FollowersProvider getProviderOverride(covariant FollowersProvider provider) {
    return call(provider.userId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'followersProvider';
}

/// See also [Followers].
class FollowersProvider
    extends
        AutoDisposeAsyncNotifierProviderImpl<Followers, List<FollowerModel>> {
  /// See also [Followers].
  FollowersProvider(String userId)
    : this._internal(
        () => Followers()..userId = userId,
        from: followersProvider,
        name: r'followersProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$followersHash,
        dependencies: FollowersFamily._dependencies,
        allTransitiveDependencies: FollowersFamily._allTransitiveDependencies,
        userId: userId,
      );

  FollowersProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.userId,
  }) : super.internal();

  final String userId;

  @override
  FutureOr<List<FollowerModel>> runNotifierBuild(covariant Followers notifier) {
    return notifier.build(userId);
  }

  @override
  Override overrideWith(Followers Function() create) {
    return ProviderOverride(
      origin: this,
      override: FollowersProvider._internal(
        () => create()..userId = userId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        userId: userId,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<Followers, List<FollowerModel>>
  createElement() {
    return _FollowersProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FollowersProvider && other.userId == userId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, userId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FollowersRef on AutoDisposeAsyncNotifierProviderRef<List<FollowerModel>> {
  /// The parameter `userId` of this provider.
  String get userId;
}

class _FollowersProviderElement
    extends
        AutoDisposeAsyncNotifierProviderElement<Followers, List<FollowerModel>>
    with FollowersRef {
  _FollowersProviderElement(super.provider);

  @override
  String get userId => (origin as FollowersProvider).userId;
}

String _$followingHash() => r'475a62304b2c76ae7fd4aec7e742f44ae032c00f';

abstract class _$Following
    extends BuildlessAutoDisposeAsyncNotifier<List<FollowerModel>> {
  late final String userId;

  FutureOr<List<FollowerModel>> build(String userId);
}

/// See also [Following].
@ProviderFor(Following)
const followingProvider = FollowingFamily();

/// See also [Following].
class FollowingFamily extends Family<AsyncValue<List<FollowerModel>>> {
  /// See also [Following].
  const FollowingFamily();

  /// See also [Following].
  FollowingProvider call(String userId) {
    return FollowingProvider(userId);
  }

  @override
  FollowingProvider getProviderOverride(covariant FollowingProvider provider) {
    return call(provider.userId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'followingProvider';
}

/// See also [Following].
class FollowingProvider
    extends
        AutoDisposeAsyncNotifierProviderImpl<Following, List<FollowerModel>> {
  /// See also [Following].
  FollowingProvider(String userId)
    : this._internal(
        () => Following()..userId = userId,
        from: followingProvider,
        name: r'followingProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$followingHash,
        dependencies: FollowingFamily._dependencies,
        allTransitiveDependencies: FollowingFamily._allTransitiveDependencies,
        userId: userId,
      );

  FollowingProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.userId,
  }) : super.internal();

  final String userId;

  @override
  FutureOr<List<FollowerModel>> runNotifierBuild(covariant Following notifier) {
    return notifier.build(userId);
  }

  @override
  Override overrideWith(Following Function() create) {
    return ProviderOverride(
      origin: this,
      override: FollowingProvider._internal(
        () => create()..userId = userId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        userId: userId,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<Following, List<FollowerModel>>
  createElement() {
    return _FollowingProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FollowingProvider && other.userId == userId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, userId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FollowingRef on AutoDisposeAsyncNotifierProviderRef<List<FollowerModel>> {
  /// The parameter `userId` of this provider.
  String get userId;
}

class _FollowingProviderElement
    extends
        AutoDisposeAsyncNotifierProviderElement<Following, List<FollowerModel>>
    with FollowingRef {
  _FollowingProviderElement(super.provider);

  @override
  String get userId => (origin as FollowingProvider).userId;
}

String _$followersAnalyticsNotifierHash() =>
    r'4819cc3307ced45d81d2e69c02a9466017fab6a7';

/// See also [FollowersAnalyticsNotifier].
@ProviderFor(FollowersAnalyticsNotifier)
final followersAnalyticsNotifierProvider =
    AutoDisposeAsyncNotifierProvider<
      FollowersAnalyticsNotifier,
      FollowersAnalytics
    >.internal(
      FollowersAnalyticsNotifier.new,
      name: r'followersAnalyticsNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$followersAnalyticsNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$FollowersAnalyticsNotifier =
    AutoDisposeAsyncNotifier<FollowersAnalytics>;
String _$followersFilterNotifierHash() =>
    r'3770f34683e302076819702c33c39441fc98cec7';

/// See also [FollowersFilterNotifier].
@ProviderFor(FollowersFilterNotifier)
final followersFilterNotifierProvider =
    AutoDisposeNotifierProvider<
      FollowersFilterNotifier,
      FollowersFilter
    >.internal(
      FollowersFilterNotifier.new,
      name: r'followersFilterNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$followersFilterNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$FollowersFilterNotifier = AutoDisposeNotifier<FollowersFilter>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
