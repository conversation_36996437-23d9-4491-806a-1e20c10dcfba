import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/profile/providers/profile_filter_provider.dart';

class EnhancedFilterButton extends ConsumerWidget {
  final VoidCallback onTap;
  final List<dynamic> posts;

  const EnhancedFilterButton({
    super.key,
    required this.onTap,
    required this.posts,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final filterState = ref.watch(profileFilterProvider);
    final filterStats = ref.watch(filterStatsProvider(posts));
    
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: filterState.hasActiveFilter
              ? Theme.of(context).primaryColor.withValues(alpha: 0.1)
              : Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: filterState.hasActiveFilter
                ? Theme.of(context).primaryColor
                : Colors.grey[300]!,
            width: filterState.hasActiveFilter ? 2 : 1,
          ),
          boxShadow: filterState.hasActiveFilter
              ? [
                  BoxShadow(
                    color: Theme.of(context).primaryColor.withValues(alpha: 0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 200),
              child: Icon(
                filterState.hasActiveFilter ? Icons.filter_alt : Icons.filter_list,
                key: ValueKey(filterState.hasActiveFilter),
                size: 18,
                color: filterState.hasActiveFilter
                    ? Theme.of(context).primaryColor
                    : Colors.grey[600],
              ),
            ),
            const SizedBox(width: 8),
            Flexible(
              child: Text(
                filterState.displayText,
                style: TextStyle(
                  fontWeight: filterState.hasActiveFilter ? FontWeight.w600 : FontWeight.w500,
                  color: filterState.hasActiveFilter
                      ? Theme.of(context).primaryColor
                      : Colors.grey[700],
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (filterState.hasActiveFilter) ...[
              const SizedBox(width: 8),
              AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${filterStats.filteredPosts}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
            const SizedBox(width: 4),
            Icon(
              Icons.keyboard_arrow_down,
              size: 16,
              color: filterState.hasActiveFilter
                  ? Theme.of(context).primaryColor
                  : Colors.grey[600],
            ),
          ],
        ),
      ),
    );
  }
}

class FilterStatsWidget extends ConsumerWidget {
  final List<dynamic> posts;

  const FilterStatsWidget({
    super.key,
    required this.posts,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final filterStats = ref.watch(filterStatsProvider(posts));
    
    if (!filterStats.hasActiveFilter) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.info_outline,
            size: 16,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(width: 8),
          Text(
            'Showing ${filterStats.filteredPosts} of ${filterStats.totalPosts} posts',
            style: TextStyle(
              fontSize: 12,
              color: Theme.of(context).primaryColor,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (filterStats.filteredPosts != filterStats.totalPosts) ...[
            const SizedBox(width: 8),
            Text(
              '(${filterStats.filterPercentage.toStringAsFixed(0)}%)',
              style: TextStyle(
                fontSize: 11,
                color: Theme.of(context).primaryColor.withValues(alpha: 0.7),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

class QuickFilterChips extends ConsumerWidget {
  const QuickFilterChips({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final filterState = ref.watch(profileFilterProvider);
    final filterNotifier = ref.read(profileFilterProvider.notifier);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Wrap(
        spacing: 8,
        children: [
          _buildFilterChip(
            context,
            'All',
            PostTimeFilter.all,
            filterState.timeFilter,
            () => filterNotifier.setTimeFilter(PostTimeFilter.all),
          ),
          _buildFilterChip(
            context,
            'This Month',
            PostTimeFilter.thisMonth,
            filterState.timeFilter,
            () => filterNotifier.setTimeFilter(PostTimeFilter.thisMonth),
          ),
          _buildFilterChip(
            context,
            'This Year',
            PostTimeFilter.thisYear,
            filterState.timeFilter,
            () => filterNotifier.setTimeFilter(PostTimeFilter.thisYear),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(
    BuildContext context,
    String label,
    PostTimeFilter filter,
    PostTimeFilter currentFilter,
    VoidCallback onTap,
  ) {
    final isSelected = currentFilter == filter;
    
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).primaryColor
              : Colors.grey[100],
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected
                ? Theme.of(context).primaryColor
                : Colors.grey[300]!,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: isSelected ? Colors.white : Colors.grey[700],
          ),
        ),
      ),
    );
  }
}
