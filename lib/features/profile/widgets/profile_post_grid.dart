import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/features/feed/screens/post_detail_screen.dart';
import 'package:billionaires_social/features/feed/screens/edit_post_screen.dart';
import 'package:billionaires_social/features/feed/widgets/post_actions_sheet.dart';
import 'package:billionaires_social/features/feed/services/feed_service.dart';
import 'package:billionaires_social/features/profile/models/profile_model.dart';
import 'package:billionaires_social/features/profile/services/profile_service.dart';
import 'package:billionaires_social/core/service_locator.dart';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_auth/firebase_auth.dart';

import 'package:billionaires_social/core/utils/dialog_utils.dart';
import 'package:billionaires_social/features/profile/providers/profile_provider.dart';
import 'package:billionaires_social/features/feed/providers/feed_provider.dart';
import 'package:billionaires_social/features/feed/providers/paginated_feed_provider.dart';
import 'package:billionaires_social/features/profile/providers/profile_filter_provider.dart';
import 'package:billionaires_social/features/profile/widgets/profile_empty_states.dart';

enum ProfileLayoutType { grid, list }

class ProfilePostGrid extends ConsumerStatefulWidget {
  final List<Post> posts;
  final bool isCurrentUser;
  final ProfileModel? userProfile;

  const ProfilePostGrid({
    super.key,
    required this.posts,
    this.isCurrentUser = false,
    this.userProfile,
  });

  @override
  ConsumerState<ProfilePostGrid> createState() => _ProfilePostGridState();
}

class _ProfilePostGridState extends ConsumerState<ProfilePostGrid>
    with TickerProviderStateMixin {
  final FeedService _feedService = getIt<FeedService>();
  final ProfileService _profileService = getIt<ProfileService>();

  ProfileLayoutType _layoutType = ProfileLayoutType.list;

  late AnimationController _toggleAnimationController;

  @override
  void initState() {
    super.initState();
    _loadLayoutPreference();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _toggleAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _toggleAnimationController.dispose();
    super.dispose();
  }

  Future<void> _loadLayoutPreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final layoutType = prefs.getString('profile_layout_type') ?? 'list';
      setState(() {
        _layoutType = layoutType == 'grid'
            ? ProfileLayoutType.grid
            : ProfileLayoutType.list;
      });
    } catch (e) {
      // If there's an error reading the preference, default to list view
      setState(() {
        _layoutType = ProfileLayoutType.list;
      });
      // Clear the corrupted preference
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('profile_layout_type');
    }
  }

  Future<void> _saveLayoutPreference() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('profile_layout_type', _layoutType.name);
  }

  // Filter posts based on time filter using provider
  List<Post> _getFilteredPosts() {
    debugPrint('🔍 _getFilteredPosts called with ${widget.posts.length} posts');
    final filterState = ref.watch(profileFilterProvider);
    debugPrint('🔍 Current filter: ${filterState.timeFilter}');

    final filteredPosts = ref
        .watch(filteredPostsProvider(widget.posts))
        .cast<Post>();

    debugPrint('🔍 Filtered to ${filteredPosts.length} posts');

    // Separate pinned and regular posts
    final pinnedPosts = filteredPosts.where((post) => post.isPinned).toList();
    final regularPosts = filteredPosts.where((post) => !post.isPinned).toList();

    // Sort: pinned posts first, then by timestamp (newest first)
    pinnedPosts.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    regularPosts.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    return [...pinnedPosts, ...regularPosts];
  }

  // Show time filter dialog
  void _showTimeFilterDialog() {
    debugPrint('🔍 Filter dialog called');
    final currentFilterState = ref.read(profileFilterProvider);
    debugPrint('🔍 Current filter state: ${currentFilterState.timeFilter}');
    debugPrint('🔍 Total posts: ${widget.posts.length}');

    // Enhanced dialog with custom date range
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) {
          PostTimeFilter selectedFilter = currentFilterState.timeFilter;
          DateTime? customStart = currentFilterState.customStartDate;
          DateTime? customEnd = currentFilterState.customEndDate;

          return AlertDialog(
            title: const Text('Filter Posts'),
            content: SizedBox(
              width: double.maxFinite,
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ListTile(
                      title: const Text('All Posts'),
                      leading: Radio<PostTimeFilter>(
                        value: PostTimeFilter.all,
                        groupValue: selectedFilter,
                        onChanged: (value) {
                          if (value != null) {
                            setDialogState(() {
                              selectedFilter = value;
                            });
                          }
                        },
                      ),
                    ),
                    ListTile(
                      title: const Text('This Month'),
                      leading: Radio<PostTimeFilter>(
                        value: PostTimeFilter.thisMonth,
                        groupValue: selectedFilter,
                        onChanged: (value) {
                          if (value != null) {
                            setDialogState(() {
                              selectedFilter = value;
                            });
                          }
                        },
                      ),
                    ),
                    ListTile(
                      title: const Text('This Year'),
                      leading: Radio<PostTimeFilter>(
                        value: PostTimeFilter.thisYear,
                        groupValue: selectedFilter,
                        onChanged: (value) {
                          if (value != null) {
                            setDialogState(() {
                              selectedFilter = value;
                            });
                          }
                        },
                      ),
                    ),
                    ListTile(
                      title: const Text('Custom Date Range'),
                      leading: Radio<PostTimeFilter>(
                        value: PostTimeFilter.custom,
                        groupValue: selectedFilter,
                        onChanged: (value) {
                          if (value != null) {
                            setDialogState(() {
                              selectedFilter = value;
                            });
                          }
                        },
                      ),
                    ),

                    // Custom date range pickers
                    if (selectedFilter == PostTimeFilter.custom) ...[
                      const SizedBox(height: 16),
                      const Divider(),
                      const SizedBox(height: 16),

                      // Start Date Picker
                      _buildDatePickerField(
                        context,
                        'From Date:',
                        customStart,
                        (date) => setDialogState(() => customStart = date),
                      ),

                      const SizedBox(height: 16),

                      // End Date Picker
                      _buildDatePickerField(
                        context,
                        'To Date:',
                        customEnd,
                        (date) => setDialogState(() => customEnd = date),
                        firstDate: customStart,
                      ),

                      // Validation message
                      if (customStart != null &&
                          customEnd != null &&
                          customStart!.isAfter(customEnd!)) ...[
                        const SizedBox(height: 8),
                        const Text(
                          'Start date must be before end date',
                          style: TextStyle(color: Colors.red, fontSize: 12),
                        ),
                      ],
                    ],
                  ],
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  // Validate custom date range
                  if (selectedFilter == PostTimeFilter.custom) {
                    if (customStart == null || customEnd == null) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text(
                            'Please select both start and end dates',
                          ),
                        ),
                      );
                      return;
                    }
                    if (customStart!.isAfter(customEnd!)) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Start date must be before end date'),
                        ),
                      );
                      return;
                    }
                  }

                  // Apply filter
                  debugPrint('🔍 Applying filter: $selectedFilter');
                  final filterNotifier = ref.read(
                    profileFilterProvider.notifier,
                  );
                  if (selectedFilter == PostTimeFilter.custom &&
                      customStart != null &&
                      customEnd != null) {
                    debugPrint(
                      '🔍 Applying custom filter: $customStart to $customEnd',
                    );
                    filterNotifier.applyCustomFilter(customStart!, customEnd!);
                  } else {
                    debugPrint('🔍 Applying time filter: $selectedFilter');
                    filterNotifier.setTimeFilter(selectedFilter);
                  }

                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Filter applied: $selectedFilter')),
                  );
                },
                child: const Text('Apply Filter'),
              ),
            ],
          );
        },
      ),
    );

    /* Original complex bottom sheet - temporarily disabled
    showAppModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) {
          PostTimeFilter tempFilter = currentFilterState.timeFilter;
          DateTime? tempStart = currentFilterState.customStartDate;
          DateTime? tempEnd = currentFilterState.customEndDate;
          String? errorText;
          bool isCustom = tempFilter == PostTimeFilter.custom;
          bool validRange =
              !isCustom ||
              (tempStart != null &&
                  tempEnd != null &&
                  !tempStart.isAfter(tempEnd));
          return Container(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom + 24,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Handle bar
                Container(
                  margin: const EdgeInsets.only(top: 12),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),

                // Title
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: Text(
                    'Filter Posts',
                    style: AppTheme.fontStyles.title.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.luxuryWhite,
                    ),
                  ),
                ),

                // Content
                Flexible(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildFilterOptionModal(
                          PostTimeFilter.all,
                          'All Posts',
                          tempFilter,
                          (f) => setModalState(() {
                            tempFilter = f;
                            errorText = null;
                          }),
                        ),
                        _buildFilterOptionModal(
                          PostTimeFilter.thisMonth,
                          'This Month',
                          tempFilter,
                          (f) => setModalState(() {
                            tempFilter = f;
                            errorText = null;
                          }),
                        ),
                        _buildFilterOptionModal(
                          PostTimeFilter.thisYear,
                          'This Year',
                          tempFilter,
                          (f) => setModalState(() {
                            tempFilter = f;
                            errorText = null;
                          }),
                        ),
                        _buildFilterOptionModal(
                          PostTimeFilter.custom,
                          'Custom Range',
                          tempFilter,
                          (f) => setModalState(() {
                            tempFilter = f;
                            errorText = null;
                          }),
                        ),
                        if (tempFilter == PostTimeFilter.custom) ...[
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: _buildDatePicker(
                                  'Start Date',
                                  tempStart,
                                  (date) => setModalState(() {
                                    tempStart = date;
                                    errorText = null;
                                  }),
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: _buildDatePicker(
                                  'End Date',
                                  tempEnd,
                                  (date) => setModalState(() {
                                    tempEnd = date;
                                    errorText = null;
                                  }),
                                ),
                              ),
                            ],
                          ),
                          if (tempStart != null &&
                              tempEnd != null &&
                              tempStart!.isAfter(tempEnd!))
                            Padding(
                              padding: const EdgeInsets.only(top: 8.0),
                              child: Text(
                                'Start date must be before end date.',
                                style: TextStyle(
                                  color: Colors.red[700],
                                  fontSize: 13,
                                ),
                              ),
                            ),
                        ],
                        const SizedBox(height: 20),
                        if (errorText != null)
                          Padding(
                            padding: const EdgeInsets.only(bottom: 8.0),
                            child: Text(
                              errorText!,
                              style: TextStyle(
                                color: Colors.red[700],
                                fontSize: 13,
                              ),
                            ),
                          ),
                        // Apply Button
                        const SizedBox(height: 20),
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: validRange
                                ? () {
                                    // Update filter state using provider
                                    final filterNotifier = ref.read(
                                      profileFilterProvider.notifier,
                                    );
                                    if (tempFilter == PostTimeFilter.custom &&
                                        tempStart != null &&
                                        tempEnd != null) {
                                      filterNotifier.applyCustomFilter(
                                        tempStart!,
                                        tempEnd!,
                                      );
                                    } else {
                                      filterNotifier.setTimeFilter(tempFilter);
                                    }
                                    Navigator.pop(context);
                                  }
                                : null,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Theme.of(context).primaryColor,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: const Text('Apply Filter'),
                          ),
                        ),
                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
    */
  }

  // Helper method to build date picker field
  Widget _buildDatePickerField(
    BuildContext context,
    String label,
    DateTime? selectedDate,
    Function(DateTime?) onDateSelected, {
    DateTime? firstDate,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: const TextStyle(fontWeight: FontWeight.w500)),
        const SizedBox(height: 8),
        InkWell(
          onTap: () async {
            final date = await showDatePicker(
              context: context,
              initialDate: selectedDate ?? DateTime.now(),
              firstDate: firstDate ?? DateTime(2020),
              lastDate: DateTime.now(),
            );
            if (date != null) {
              onDateSelected(date);
            }
          },
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                const Icon(Icons.calendar_today, size: 16, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  selectedDate != null
                      ? '${selectedDate.day}/${selectedDate.month}/${selectedDate.year}'
                      : 'Select date',
                  style: TextStyle(
                    color: selectedDate != null
                        ? Colors.black
                        : Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // ignore: unused_element
  Widget _buildFilterOptionModal(
    PostTimeFilter filter,
    String title,
    PostTimeFilter selected,
    void Function(PostTimeFilter) onTap,
  ) {
    final isSelected = selected == filter;
    return GestureDetector(
      onTap: () => onTap(filter),
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).primaryColor.withValues(alpha: 0.1)
              : Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected
                ? Theme.of(context).primaryColor
                : Colors.grey[200]!,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              isSelected
                  ? Icons.radio_button_checked
                  : Icons.radio_button_unchecked,
              color: isSelected
                  ? Theme.of(context).primaryColor
                  : Colors.grey[600],
            ),
            const SizedBox(width: 12),
            Text(
              title,
              style: TextStyle(
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                color: isSelected
                    ? Theme.of(context).primaryColor
                    : Colors.black,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // ignore: unused_element
  Widget _buildDatePicker(
    String label,
    DateTime? selectedDate,
    Function(DateTime?) onDateSelected,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: const TextStyle(fontWeight: FontWeight.w500)),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: () async {
            final date = await showDatePicker(
              context: context,
              initialDate: selectedDate ?? DateTime.now(),
              firstDate: DateTime(2020),
              lastDate: DateTime.now(),
            );
            onDateSelected(date);
          },
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 8),
                Text(
                  selectedDate != null
                      ? '${selectedDate.day}/${selectedDate.month}/${selectedDate.year}'
                      : 'Select date',
                  style: TextStyle(
                    color: selectedDate != null
                        ? Colors.black
                        : Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _pinPost(String postId) async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) return;

      final isCurrentlyPinned = widget.posts.any(
        (post) => post.id == postId && post.isPinned,
      );

      bool success;
      if (isCurrentlyPinned) {
        success = await _profileService.unpinPost(currentUser.uid, postId);
      } else {
        success = await _profileService.pinPost(currentUser.uid, postId);
      }

      if (mounted) {
        if (success) {
          // Reload posts to reflect the change
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                isCurrentlyPinned
                    ? 'Post unpinned from profile'
                    : 'Post pinned to profile',
              ),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('You can only pin up to 3 posts')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Failed to pin post: $e')));
      }
    }
  }

  void _showPostActions(Post post) {
    showAppDialog(
      context: context,
      title: Text('Post Actions'),
      content: PostActionsSheet(
        post: post,
        isOwner: widget.isCurrentUser,
        onEdit: () {
          Navigator.pop(context);
          Navigator.of(context).push(
            MaterialPageRoute(builder: (context) => EditPostScreen(post: post)),
          );
        },
        onDelete: () => _deletePost(post),
        onArchive: () => _archivePost(post),
        onPrivacyChange: (privacy) => _changePostPrivacy(post, privacy),
        onPin: () => _pinPost(post.id),
      ),
    );
  }

  Future<void> _archivePost(Post post) async {
    try {
      await _feedService.updatePost(post.id, {'isArchived': true});
      if (mounted) {
        Navigator.pop(context);

        // Also remove archived post from all feed providers
        try {
          // Remove from main feed provider
          ref.read(feedProvider.notifier).removePost(post.id);
          await ref.read(feedProvider.notifier).refresh();

          // Remove from paginated feed provider
          ref.read(paginatedFeedProvider.notifier).removePost(post.id);
          await ref.read(paginatedFeedProvider.notifier).refresh();

          debugPrint(
            '✅ POST ARCHIVE: All feed providers refreshed and post removed',
          );
        } catch (e) {
          debugPrint('⚠️ POST ARCHIVE: Error refreshing feed providers: $e');
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Post archived successfully')),
          );
          setState(() {});
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Failed to archive post: $e')));
      }
    }
  }

  Future<void> _changePostPrivacy(Post post, String privacy) async {
    try {
      await _feedService.updatePost(post.id, {'visibility': privacy});
      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Post privacy changed to $privacy')),
        );
        setState(() {});
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to change post privacy: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Watch the provider to trigger rebuilds when filter changes
    final filterState = ref.watch(profileFilterProvider);
    debugPrint('🔍 Build called with filter: ${filterState.timeFilter}');

    final filteredPosts = _getFilteredPosts();

    return Column(
      children: [
        // Enhanced Header with Filter and Layout Controls
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border(
              bottom: BorderSide(color: Colors.grey[200]!, width: 0.5),
            ),
          ),
          child: Row(
            children: [
              // Enhanced Filter Button - Only show for current user
              if (widget.isCurrentUser) ...[
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      debugPrint('🔍 Filter button tapped!');
                      _showTimeFilterDialog();
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.blue.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.blue),
                      ),
                      child: const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.filter_list, size: 18, color: Colors.blue),
                          SizedBox(width: 8),
                          Text(
                            'Filter Posts',
                            style: TextStyle(color: Colors.blue),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
              ],

              // Enhanced Layout Toggle
              Container(
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey[200]!),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildLayoutToggleButton(
                      ProfileLayoutType.list,
                      Icons.view_list,
                      'List',
                    ),
                    _buildLayoutToggleButton(
                      ProfileLayoutType.grid,
                      Icons.grid_view,
                      'Grid',
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // Posts Display
        Expanded(
          child: filteredPosts.isEmpty
              ? _buildEmptyState()
              : _layoutType == ProfileLayoutType.grid
              ? _buildGridView(filteredPosts)
              : _buildListView(filteredPosts),
        ),
      ],
    );
  }

  Widget _buildLayoutToggleButton(
    ProfileLayoutType type,
    IconData icon,
    String label,
  ) {
    final isSelected = _layoutType == type;

    return GestureDetector(
      onTap: () {
        setState(() {
          _layoutType = type;
        });
        _saveLayoutPreference();

        // Animate the toggle
        if (isSelected) {
          _toggleAnimationController.reverse();
        } else {
          _toggleAnimationController.forward();
        }
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        curve: Curves.easeInOut,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).primaryColor
              : Colors.transparent,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 18,
              color: isSelected ? Colors.white : Colors.grey[600],
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w500,
                color: isSelected ? Colors.white : Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    final hasActiveFilter = ref.read(profileFilterProvider).hasActiveFilter;

    if (hasActiveFilter) {
      return ProfileEmptyStates.searchResults(query: 'filtered posts');
    }

    return ProfileEmptyStates.posts(
      isCurrentUser: widget.isCurrentUser,
      onCreatePost: widget.isCurrentUser
          ? () {
              // TODO: Navigate to post creation screen
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Post creation coming soon')),
              );
            }
          : null,
    );
  }

  Widget _buildGridView(List<Post> posts) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const AlwaysScrollableScrollPhysics(),
      padding: const EdgeInsets.all(8),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 2,
        mainAxisSpacing: 2,
        childAspectRatio: 1.0,
      ),
      itemCount: posts.length,
      itemBuilder: (context, index) {
        final post = posts[index];
        final isPinned = post.isPinned;

        return GestureDetector(
          onTap: () => _navigateToPostDetail(post),
          onLongPress: widget.isCurrentUser
              ? () => _showPostActions(post)
              : null,
          child: Stack(
            children: [
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: isPinned
                      ? Border.all(color: Colors.blue, width: 2)
                      : null,
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(isPinned ? 6 : 8),
                  child: _buildPostThumbnail(post),
                ),
              ),
              if (isPinned)
                Positioned(
                  top: 4,
                  right: 4,
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: const BoxDecoration(
                      color: Colors.blue,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.push_pin,
                      color: Colors.white,
                      size: 12,
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildListView(List<Post> posts) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const AlwaysScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      itemCount: posts.length,
      itemBuilder: (context, index) {
        final post = posts[index];
        final isPinned = post.isPinned;

        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: isPinned
                ? Border.all(color: Colors.blue, width: 2)
                : Border.all(color: Colors.grey[200]!),
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Post Header
              Padding(
                padding: const EdgeInsets.all(12),
                child: Row(
                  children: [
                    CircleAvatar(
                      radius: 20,
                      backgroundImage: _isValidImageUrl(post.userAvatarUrl)
                          ? CachedNetworkImageProvider(post.userAvatarUrl)
                          : null,
                      child: !_isValidImageUrl(post.userAvatarUrl)
                          ? Text(
                              post.username.isNotEmpty
                                  ? post.username[0].toUpperCase()
                                  : 'U',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            )
                          : null,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Flexible(
                                child: Text(
                                  post.username,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              if (isPinned) ...[
                                const SizedBox(width: 8),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 6,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.blue,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: const Text(
                                    'PINNED',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                          Text(
                            _formatTimestamp(post.timestamp),
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (widget.isCurrentUser)
                      PopupMenuButton<String>(
                        icon: const Icon(Icons.more_vert),
                        onSelected: (value) => _handlePostAction(value, post),
                        itemBuilder: (context) => [
                          const PopupMenuItem(
                            value: 'edit',
                            child: Row(
                              children: [
                                Icon(Icons.edit, size: 18),
                                SizedBox(width: 8),
                                Text('Edit'),
                              ],
                            ),
                          ),
                          PopupMenuItem(
                            value: isPinned ? 'unpin' : 'pin',
                            child: Row(
                              children: [
                                Icon(
                                  isPinned
                                      ? Icons.push_pin
                                      : Icons.push_pin_outlined,
                                  size: 18,
                                ),
                                const SizedBox(width: 8),
                                Text(isPinned ? 'Unpin' : 'Pin'),
                              ],
                            ),
                          ),
                          const PopupMenuItem(
                            value: 'delete',
                            child: Row(
                              children: [
                                Icon(Icons.delete, size: 18, color: Colors.red),
                                SizedBox(width: 8),
                                Text(
                                  'Delete',
                                  style: TextStyle(color: Colors.red),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ),

              // Post Content
              GestureDetector(
                onTap: () => _navigateToPostDetail(post),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (post.caption.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        child: Text(
                          post.caption,
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                    const SizedBox(height: 8),
                    _buildPostMedia(post),
                  ],
                ),
              ),

              // Post Actions
              Padding(
                padding: const EdgeInsets.all(12),
                child: Row(
                  children: [
                    _buildActionButton(
                      icon: post.isLiked
                          ? Icons.favorite
                          : Icons.favorite_border,
                      label: '${post.likeCount}',
                      isActive: post.isLiked,
                      onTap: () => _toggleLike(post),
                    ),
                    const SizedBox(width: 16),
                    _buildActionButton(
                      icon: Icons.comment_outlined,
                      label: '${post.commentCount}',
                      onTap: () => _navigateToPostDetail(post),
                    ),
                    const SizedBox(width: 16),
                    _buildActionButton(
                      icon: Icons.share_outlined,
                      label: 'Share',
                      onTap: () => _sharePost(post),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    bool isActive = false,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Row(
        children: [
          Icon(icon, size: 20, color: isActive ? Colors.red : Colors.grey[600]),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: isActive ? Colors.red : Colors.grey[600],
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  Widget _buildPostThumbnail(Post post) {
    return CachedNetworkImage(
      imageUrl: post.mediaUrl,
      fit: BoxFit.cover,
      width: double.infinity,
      height: double.infinity,
      placeholder: (context, url) => Container(
        color: Colors.grey[200],
        child: const Center(child: CircularProgressIndicator()),
      ),
      errorWidget: (context, url, error) =>
          Container(color: Colors.grey[200], child: const Icon(Icons.error)),
    );
  }

  Widget _buildPostMedia(Post post) {
    return ClipRRect(
      borderRadius: const BorderRadius.only(
        bottomLeft: Radius.circular(12),
        bottomRight: Radius.circular(12),
      ),
      child: CachedNetworkImage(
        imageUrl: post.mediaUrl,
        width: double.infinity,
        height: MediaQuery.of(context).size.width * 0.8,
        fit: BoxFit.cover,
        placeholder: (context, url) => Container(
          height: MediaQuery.of(context).size.width * 0.8,
          color: Colors.grey[200],
          child: const Center(child: CircularProgressIndicator()),
        ),
        errorWidget: (context, url, error) => Container(
          height: MediaQuery.of(context).size.width * 0.8,
          color: Colors.grey[200],
          child: const Center(child: Icon(Icons.error, size: 48)),
        ),
      ),
    );
  }

  void _handlePostAction(String action, Post post) {
    switch (action) {
      case 'edit':
        _editPost(post);
        break;
      case 'pin':
      case 'unpin':
        _pinPost(post.id);
        break;
      case 'delete':
        _deletePost(post);
        break;
    }
  }

  void _editPost(Post post) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => EditPostScreen(post: post)),
    );
  }

  Future<void> _toggleLike(Post post) async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) return;

      final feedService = getIt<FeedService>();
      await feedService.toggleLike(post.id);

      // Show feedback
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(post.isLiked ? 'Post unliked' : 'Post liked'),
            duration: const Duration(seconds: 1),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to like post: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  Future<void> _sharePost(Post post) async {
    try {
      // TODO: Implement share functionality
      // final postUrl = 'https://billionaires-social.com/post/${post.id}';
      // final shareText = 'Check out this post by ${post.username}: $postUrl';
      // await SharePlus.instance.share(shareText);

      // Track share analytics
      final feedService = getIt<FeedService>();
      await feedService.trackShare(post.id);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to share post: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  Future<void> _deletePost(Post post) async {
    final confirmed = await showAppDialog<bool>(
      context: context,
      title: Text('Delete Post'),
      content: Text(
        'Are you sure you want to delete this post? This action cannot be undone.',
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context, false),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () => Navigator.pop(context, true),
          style: TextButton.styleFrom(foregroundColor: Colors.red),
          child: const Text('Delete'),
        ),
      ],
    );

    if (confirmed == true) {
      try {
        final feedService = getIt<FeedService>();
        await feedService.deletePost(post.id);

        if (mounted) {
          // Refresh the posts list and profile providers
          if (widget.userProfile != null) {
            ref.invalidate(
              userPostsWithPinnedStatusStreamProvider(widget.userProfile!.id),
            );
            ref.invalidate(profileProvider(widget.userProfile!.id));
            ref.invalidate(userProfileProvider);

            // Also refresh the user profile provider to update post count
            try {
              await ref.read(userProfileProvider.notifier).refresh();
              debugPrint('✅ POST DELETION: Profile providers refreshed');
            } catch (e) {
              debugPrint(
                '⚠️ POST DELETION: Error refreshing profile providers: $e',
              );
            }
          }

          // CRITICAL FIX: Also refresh all feed providers to remove deleted post from feed
          try {
            // Remove from main feed provider
            ref.read(feedProvider.notifier).removePost(post.id);
            await ref.read(feedProvider.notifier).refresh();

            // Remove from paginated feed provider
            ref.read(paginatedFeedProvider.notifier).removePost(post.id);
            await ref.read(paginatedFeedProvider.notifier).refresh();

            debugPrint(
              '✅ POST DELETION: All feed providers refreshed and post removed',
            );
          } catch (e) {
            debugPrint('⚠️ POST DELETION: Error refreshing feed providers: $e');
          }

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Post deleted successfully'),
                backgroundColor: Colors.green,
              ),
            );
          }
          // Force rebuild of the widget
          setState(() {});
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to delete post: $e'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      }
    }
  }

  void _navigateToPostDetail(Post post) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PostDetailScreen(postId: post.id),
      ),
    );
  }

  bool _isValidImageUrl(String? url) {
    if (url == null || url.isEmpty) return false;
    return url.startsWith('http://') || url.startsWith('https://');
  }
}
