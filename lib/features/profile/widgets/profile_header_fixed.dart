import 'package:billionaires_social/features/profile/screens/edit_profile_screen.dart';
import 'package:billionaires_social/features/stories/widgets/story_aware_profile_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/profile/providers/profile_provider.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:billionaires_social/features/stories/screens/story_creation_screen.dart';
import 'package:billionaires_social/core/main_navigation.dart';

class ProfileHeaderFixed extends ConsumerStatefulWidget {
  final String userId;
  final bool isCurrentUser;

  const ProfileHeaderFixed({
    super.key,
    required this.userId,
    this.isCurrentUser = false,
  });

  @override
  ConsumerState<ProfileHeaderFixed> createState() => _ProfileHeaderFixedState();
}

class _ProfileHeaderFixedState extends ConsumerState<ProfileHeaderFixed> {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  bool _isFollowing = false;
  final bool _isLoadingFollow = false;

  @override
  void initState() {
    super.initState();
    if (!widget.isCurrentUser) {
      _checkFollowStatus();
    }
  }

  Future<void> _checkFollowStatus() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return;

      final followDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .collection('following')
          .doc(widget.userId)
          .get();

      setState(() {
        _isFollowing = followDoc.exists;
      });
    } catch (e) {
      debugPrint('Error checking follow status: $e');
    }
  }

  Widget _buildCurrentUserStorySection(dynamic profile) {
    return Column(
      children: [
        Stack(
          children: [
            StoryAwareProfileImage(
              userId: widget.userId,
              profileImageUrl: profile.profilePictureUrl.isNotEmpty
                  ? profile.profilePictureUrl
                  : null,
              size: 80,
              showStoryIndicator: true,
              onTap: () {
                // Navigate to story creation when no stories exist
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const StoryCreationScreen(
                      initialMode: CreationMode.story,
                    ),
                  ),
                );
              },
            ),
            // Add story button overlay for current user
            Positioned(
              bottom: 0,
              right: 0,
              child: GestureDetector(
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const StoryCreationScreen(
                        initialMode: CreationMode.story,
                      ),
                    ),
                  );
                },
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Theme.of(context).scaffoldBackgroundColor,
                      width: 2,
                    ),
                  ),
                  child: Icon(
                    Icons.add,
                    size: 16,
                    color: Theme.of(context).colorScheme.onPrimary,
                  ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          'Add Story',
          style: Theme.of(context).textTheme.labelSmall?.copyWith(
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final profileAsync = ref.watch(profileProvider(widget.userId));

    return profileAsync.when(
      data: (profile) => Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            widget.isCurrentUser
                ? _buildCurrentUserStorySection(profile)
                : StoryAwareProfileImage(
                    userId: widget.userId,
                    profileImageUrl: profile.profilePictureUrl.isNotEmpty
                        ? profile.profilePictureUrl
                        : null,
                    size: 80,
                    showStoryIndicator: true,
                  ),
            const SizedBox(height: 12),
            // Profile name and verification badges
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  profile.name,
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                const SizedBox(width: 8),
                // Add verification badges here
              ],
            ),
            const SizedBox(height: 8),
            // Bio
            if (profile.bio.isNotEmpty)
              Text(
                profile.bio,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            const SizedBox(height: 16),
            // Stats row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildStatColumn('Posts', profile.postCount.toString()),
                // Real-time follower count
                Consumer(
                  builder: (context, ref, child) {
                    final followerCountAsync = ref.watch(
                      followerCountProvider(widget.userId),
                    );
                    return followerCountAsync.when(
                      data: (followerCount) => _buildStatColumn(
                        'Followers',
                        _formatCount(followerCount),
                      ),
                      loading: () => _buildStatColumn(
                        'Followers',
                        _formatCount(
                          profile.followerCount,
                        ), // Fallback to cached count
                      ),
                      error: (error, _) => _buildStatColumn(
                        'Followers',
                        _formatCount(
                          profile.followerCount,
                        ), // Fallback to cached count
                      ),
                    );
                  },
                ),
                // Real-time following count
                Consumer(
                  builder: (context, ref, child) {
                    final followingCountAsync = ref.watch(
                      followingCountProvider(widget.userId),
                    );
                    return followingCountAsync.when(
                      data: (followingCount) => _buildStatColumn(
                        'Following',
                        _formatCount(followingCount),
                      ),
                      loading: () => _buildStatColumn(
                        'Following',
                        _formatCount(
                          profile.followingCount,
                        ), // Fallback to cached count
                      ),
                      error: (error, _) => _buildStatColumn(
                        'Following',
                        _formatCount(
                          profile.followingCount,
                        ), // Fallback to cached count
                      ),
                    );
                  },
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Action buttons
            widget.isCurrentUser
                ? _buildCurrentUserActions()
                : _buildOtherUserActions(),
          ],
        ),
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(child: Text('Error: $error')),
    );
  }

  Widget _buildStatColumn(String label, String count) {
    return Column(
      children: [
        Text(
          count,
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        Text(label, style: Theme.of(context).textTheme.bodySmall),
      ],
    );
  }

  Widget _buildCurrentUserActions() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const EditProfileScreen(),
                ),
              );
            },
            child: const Text('Edit Profile'),
          ),
        ),
        const SizedBox(width: 8),
        ElevatedButton(
          onPressed: () {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) =>
                    const StoryCreationScreen(initialMode: CreationMode.story),
              ),
            );
          },
          child: const Icon(Icons.add),
        ),
      ],
    );
  }

  Widget _buildOtherUserActions() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoadingFollow
                ? null
                : () {
                    // Handle follow/unfollow
                  },
            child: Text(_isFollowing ? 'Unfollow' : 'Follow'),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: ElevatedButton(
            onPressed: () {
              // Handle message
            },
            child: const Text('Message'),
          ),
        ),
      ],
    );
  }

  String _formatCount(int count) {
    if (count >= 1000000) {
      return '${(count / 1000000).toStringAsFixed(1)}M';
    } else if (count >= 1000) {
      return '${(count / 1000).toStringAsFixed(1)}K';
    }
    return count.toString();
  }
}
