import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:billionaires_social/features/profile/models/profile_verification_model.dart';
import 'package:billionaires_social/core/app_theme.dart';

/// Email input step for email verification
class EmailInputStep extends StatefulWidget {
  final Function(String) onEmailChanged;

  const EmailInputStep({super.key, required this.onEmailChanged});

  @override
  State<EmailInputStep> createState() => _EmailInputStepState();
}

class _EmailInputStepState extends State<EmailInputStep> {
  final TextEditingController _emailController = TextEditingController();

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Enter your email address to receive a verification code',
          style: AppTheme.fontStyles.body.copyWith(
            color: AppTheme.secondaryAccentColor,
          ),
        ),
        const Si<PERSON><PERSON><PERSON>(height: 16),
        Text<PERSON>ield(
          controller: _emailController,
          keyboardType: TextInputType.emailAddress,
          style: AppTheme.fontStyles.body.copyWith(color: AppTheme.luxuryWhite),
          decoration: InputDecoration(
            labelText: 'Email Address',
            labelStyle: AppTheme.fontStyles.body.copyWith(
              color: AppTheme.secondaryAccentColor,
            ),
            prefixIcon: FaIcon(
              FontAwesomeIcons.envelope,
              color: AppTheme.secondaryAccentColor,
              size: 16,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: AppTheme.luxuryGrey),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: AppTheme.accentColor),
            ),
            filled: true,
            fillColor: AppTheme.luxuryGrey.withValues(alpha: 0.1),
          ),
          onChanged: widget.onEmailChanged,
        ),
      ],
    );
  }
}

/// Phone input step for phone verification
class PhoneInputStep extends StatefulWidget {
  final Function(String) onPhoneChanged;

  const PhoneInputStep({super.key, required this.onPhoneChanged});

  @override
  State<PhoneInputStep> createState() => _PhoneInputStepState();
}

class _PhoneInputStepState extends State<PhoneInputStep> {
  final TextEditingController _phoneController = TextEditingController();

  @override
  void dispose() {
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Enter your phone number to receive a verification SMS',
          style: AppTheme.fontStyles.body.copyWith(
            color: AppTheme.secondaryAccentColor,
          ),
        ),
        const SizedBox(height: 16),
        TextField(
          controller: _phoneController,
          keyboardType: TextInputType.phone,
          style: AppTheme.fontStyles.body.copyWith(color: AppTheme.luxuryWhite),
          decoration: InputDecoration(
            labelText: 'Phone Number',
            labelStyle: AppTheme.fontStyles.body.copyWith(
              color: AppTheme.secondaryAccentColor,
            ),
            prefixIcon: FaIcon(
              FontAwesomeIcons.phone,
              color: AppTheme.secondaryAccentColor,
              size: 16,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: AppTheme.luxuryGrey),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: AppTheme.accentColor),
            ),
            filled: true,
            fillColor: AppTheme.luxuryGrey.withValues(alpha: 0.1),
          ),
          onChanged: widget.onPhoneChanged,
        ),
      ],
    );
  }
}

/// Verification code input step
class VerificationCodeStep extends StatefulWidget {
  final String type;
  final Function(String) onCodeChanged;

  const VerificationCodeStep({
    super.key,
    required this.type,
    required this.onCodeChanged,
  });

  @override
  State<VerificationCodeStep> createState() => _VerificationCodeStepState();
}

class _VerificationCodeStepState extends State<VerificationCodeStep> {
  final TextEditingController _codeController = TextEditingController();

  @override
  void dispose() {
    _codeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Enter the verification code sent to your ${widget.type.toLowerCase()}',
          style: AppTheme.fontStyles.body.copyWith(
            color: AppTheme.secondaryAccentColor,
          ),
        ),
        const SizedBox(height: 16),
        TextField(
          controller: _codeController,
          keyboardType: TextInputType.number,
          maxLength: 6,
          style: AppTheme.fontStyles.body.copyWith(
            color: AppTheme.luxuryWhite,
            fontSize: 18,
            letterSpacing: 2,
          ),
          textAlign: TextAlign.center,
          decoration: InputDecoration(
            labelText: 'Verification Code',
            labelStyle: AppTheme.fontStyles.body.copyWith(
              color: AppTheme.secondaryAccentColor,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: AppTheme.luxuryGrey),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: AppTheme.accentColor),
            ),
            filled: true,
            fillColor: AppTheme.luxuryGrey.withValues(alpha: 0.1),
            counterText: '',
          ),
          onChanged: widget.onCodeChanged,
        ),
        const SizedBox(height: 16),
        Center(
          child: TextButton(
            onPressed: () {
              // TODO: Implement resend code functionality
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Resend functionality coming soon'),
                ),
              );
            },
            child: Text(
              'Resend Code',
              style: TextStyle(color: AppTheme.accentColor),
            ),
          ),
        ),
      ],
    );
  }
}

/// Personal information step for identity verification
class PersonalInfoStep extends StatefulWidget {
  final Function(Map<String, String>) onDataChanged;

  const PersonalInfoStep({super.key, required this.onDataChanged});

  @override
  State<PersonalInfoStep> createState() => _PersonalInfoStepState();
}

class _PersonalInfoStepState extends State<PersonalInfoStep> {
  final TextEditingController _firstNameController = TextEditingController();
  final TextEditingController _lastNameController = TextEditingController();
  final TextEditingController _dobController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _dobController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  void _updateData() {
    widget.onDataChanged({
      'firstName': _firstNameController.text,
      'lastName': _lastNameController.text,
      'dateOfBirth': _dobController.text,
      'address': _addressController.text,
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Please provide your personal information as it appears on your ID',
          style: AppTheme.fontStyles.body.copyWith(
            color: AppTheme.secondaryAccentColor,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _firstNameController,
                style: AppTheme.fontStyles.body.copyWith(
                  color: AppTheme.luxuryWhite,
                ),
                decoration: InputDecoration(
                  labelText: 'First Name',
                  labelStyle: AppTheme.fontStyles.body.copyWith(
                    color: AppTheme.secondaryAccentColor,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  filled: true,
                  fillColor: AppTheme.luxuryGrey.withValues(alpha: 0.1),
                ),
                onChanged: (_) => _updateData(),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: TextField(
                controller: _lastNameController,
                style: AppTheme.fontStyles.body.copyWith(
                  color: AppTheme.luxuryWhite,
                ),
                decoration: InputDecoration(
                  labelText: 'Last Name',
                  labelStyle: AppTheme.fontStyles.body.copyWith(
                    color: AppTheme.secondaryAccentColor,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  filled: true,
                  fillColor: AppTheme.luxuryGrey.withValues(alpha: 0.1),
                ),
                onChanged: (_) => _updateData(),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        TextField(
          controller: _dobController,
          style: AppTheme.fontStyles.body.copyWith(color: AppTheme.luxuryWhite),
          decoration: InputDecoration(
            labelText: 'Date of Birth (MM/DD/YYYY)',
            labelStyle: AppTheme.fontStyles.body.copyWith(
              color: AppTheme.secondaryAccentColor,
            ),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            filled: true,
            fillColor: AppTheme.luxuryGrey.withValues(alpha: 0.1),
          ),
          onChanged: (_) => _updateData(),
        ),
        const SizedBox(height: 16),
        TextField(
          controller: _addressController,
          maxLines: 3,
          style: AppTheme.fontStyles.body.copyWith(color: AppTheme.luxuryWhite),
          decoration: InputDecoration(
            labelText: 'Address',
            labelStyle: AppTheme.fontStyles.body.copyWith(
              color: AppTheme.secondaryAccentColor,
            ),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            filled: true,
            fillColor: AppTheme.luxuryGrey.withValues(alpha: 0.1),
          ),
          onChanged: (_) => _updateData(),
        ),
      ],
    );
  }
}

/// Document upload step
class DocumentUploadStep extends StatefulWidget {
  final String documentType;
  final Function(String) onDocumentUploaded;

  const DocumentUploadStep({
    super.key,
    required this.documentType,
    required this.onDocumentUploaded,
  });

  @override
  State<DocumentUploadStep> createState() => _DocumentUploadStepState();
}

class _DocumentUploadStepState extends State<DocumentUploadStep> {
  String? _uploadedDocument;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Upload a clear photo of your ${widget.documentType}',
          style: AppTheme.fontStyles.body.copyWith(
            color: AppTheme.secondaryAccentColor,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          width: double.infinity,
          height: 200,
          decoration: BoxDecoration(
            color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppTheme.luxuryGrey,
              style: BorderStyle.solid,
            ),
          ),
          child: _uploadedDocument == null
              ? Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    FaIcon(
                      FontAwesomeIcons.cloudArrowUp,
                      size: 48,
                      color: AppTheme.secondaryAccentColor.withValues(
                        alpha: 0.7,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Tap to upload ${widget.documentType}',
                      style: AppTheme.fontStyles.body.copyWith(
                        color: AppTheme.secondaryAccentColor,
                      ),
                    ),
                  ],
                )
              : Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    FaIcon(
                      FontAwesomeIcons.circleCheck,
                      size: 48,
                      color: Colors.green,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      '${widget.documentType} uploaded',
                      style: AppTheme.fontStyles.body.copyWith(
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () => _uploadDocument(),
            icon: FaIcon(
              _uploadedDocument == null
                  ? FontAwesomeIcons.camera
                  : FontAwesomeIcons.arrowRotateRight,
              size: 16,
            ),
            label: Text(
              _uploadedDocument == null ? 'Take Photo' : 'Retake Photo',
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.accentColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  void _uploadDocument() {
    // TODO: Implement actual document upload
    setState(() {
      _uploadedDocument =
          'document_${DateTime.now().millisecondsSinceEpoch}.jpg';
    });
    widget.onDocumentUploaded(_uploadedDocument!);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${widget.documentType} uploaded successfully'),
        backgroundColor: Colors.green,
      ),
    );
  }
}

/// Selfie verification step
class SelfieVerificationStep extends StatefulWidget {
  final Function(String) onSelfieUploaded;

  const SelfieVerificationStep({super.key, required this.onSelfieUploaded});

  @override
  State<SelfieVerificationStep> createState() => _SelfieVerificationStepState();
}

class _SelfieVerificationStepState extends State<SelfieVerificationStep> {
  String? _uploadedSelfie;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Take a selfie to verify your identity matches your ID',
          style: AppTheme.fontStyles.body.copyWith(
            color: AppTheme.secondaryAccentColor,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          width: double.infinity,
          height: 200,
          decoration: BoxDecoration(
            color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppTheme.luxuryGrey,
              style: BorderStyle.solid,
            ),
          ),
          child: _uploadedSelfie == null
              ? Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    FaIcon(
                      FontAwesomeIcons.userLarge,
                      size: 48,
                      color: AppTheme.secondaryAccentColor.withValues(
                        alpha: 0.7,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Tap to take selfie',
                      style: AppTheme.fontStyles.body.copyWith(
                        color: AppTheme.secondaryAccentColor,
                      ),
                    ),
                  ],
                )
              : Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    FaIcon(
                      FontAwesomeIcons.circleCheck,
                      size: 48,
                      color: Colors.green,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Selfie captured',
                      style: AppTheme.fontStyles.body.copyWith(
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () => _takeSelfie(),
            icon: FaIcon(
              _uploadedSelfie == null
                  ? FontAwesomeIcons.camera
                  : FontAwesomeIcons.arrowRotateRight,
              size: 16,
            ),
            label: Text(
              _uploadedSelfie == null ? 'Take Selfie' : 'Retake Selfie',
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.accentColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  void _takeSelfie() {
    // TODO: Implement actual selfie capture
    setState(() {
      _uploadedSelfie = 'selfie_${DateTime.now().millisecondsSinceEpoch}.jpg';
    });
    widget.onSelfieUploaded(_uploadedSelfie!);

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Selfie captured successfully'),
        backgroundColor: Colors.green,
      ),
    );
  }
}

/// Business information step
class BusinessInfoStep extends StatefulWidget {
  final Function(Map<String, String>) onDataChanged;

  const BusinessInfoStep({super.key, required this.onDataChanged});

  @override
  State<BusinessInfoStep> createState() => _BusinessInfoStepState();
}

class _BusinessInfoStepState extends State<BusinessInfoStep> {
  final TextEditingController _businessNameController = TextEditingController();
  final TextEditingController _registrationController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _websiteController = TextEditingController();

  @override
  void dispose() {
    _businessNameController.dispose();
    _registrationController.dispose();
    _addressController.dispose();
    _websiteController.dispose();
    super.dispose();
  }

  void _updateData() {
    widget.onDataChanged({
      'businessName': _businessNameController.text,
      'registrationNumber': _registrationController.text,
      'businessAddress': _addressController.text,
      'website': _websiteController.text,
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Please provide your business information',
          style: AppTheme.fontStyles.body.copyWith(
            color: AppTheme.secondaryAccentColor,
          ),
        ),
        const SizedBox(height: 16),
        TextField(
          controller: _businessNameController,
          style: AppTheme.fontStyles.body.copyWith(color: AppTheme.luxuryWhite),
          decoration: InputDecoration(
            labelText: 'Business Name',
            labelStyle: AppTheme.fontStyles.body.copyWith(
              color: AppTheme.secondaryAccentColor,
            ),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            filled: true,
            fillColor: AppTheme.luxuryGrey.withValues(alpha: 0.1),
          ),
          onChanged: (_) => _updateData(),
        ),
        const SizedBox(height: 16),
        TextField(
          controller: _registrationController,
          style: AppTheme.fontStyles.body.copyWith(color: AppTheme.luxuryWhite),
          decoration: InputDecoration(
            labelText: 'Registration Number',
            labelStyle: AppTheme.fontStyles.body.copyWith(
              color: AppTheme.secondaryAccentColor,
            ),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            filled: true,
            fillColor: AppTheme.luxuryGrey.withValues(alpha: 0.1),
          ),
          onChanged: (_) => _updateData(),
        ),
        const SizedBox(height: 16),
        TextField(
          controller: _addressController,
          maxLines: 3,
          style: AppTheme.fontStyles.body.copyWith(color: AppTheme.luxuryWhite),
          decoration: InputDecoration(
            labelText: 'Business Address',
            labelStyle: AppTheme.fontStyles.body.copyWith(
              color: AppTheme.secondaryAccentColor,
            ),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            filled: true,
            fillColor: AppTheme.luxuryGrey.withValues(alpha: 0.1),
          ),
          onChanged: (_) => _updateData(),
        ),
        const SizedBox(height: 16),
        TextField(
          controller: _websiteController,
          style: AppTheme.fontStyles.body.copyWith(color: AppTheme.luxuryWhite),
          decoration: InputDecoration(
            labelText: 'Website (Optional)',
            labelStyle: AppTheme.fontStyles.body.copyWith(
              color: AppTheme.secondaryAccentColor,
            ),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            filled: true,
            fillColor: AppTheme.luxuryGrey.withValues(alpha: 0.1),
          ),
          onChanged: (_) => _updateData(),
        ),
      ],
    );
  }
}

/// Business documents upload step
class BusinessDocumentsStep extends StatefulWidget {
  final Function(List<String>) onDocumentsUploaded;

  const BusinessDocumentsStep({super.key, required this.onDocumentsUploaded});

  @override
  State<BusinessDocumentsStep> createState() => _BusinessDocumentsStepState();
}

class _BusinessDocumentsStepState extends State<BusinessDocumentsStep> {
  final List<String> _uploadedDocuments = [];

  final List<Map<String, dynamic>> _requiredDocuments = [
    {
      'name': 'Business Registration Certificate',
      'description': 'Official business registration document',
      'required': true,
    },
    {
      'name': 'Tax Registration',
      'description': 'Tax identification document',
      'required': true,
    },
    {
      'name': 'Business License',
      'description': 'Operating license or permit',
      'required': false,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Upload the required business documents',
          style: AppTheme.fontStyles.body.copyWith(
            color: AppTheme.secondaryAccentColor,
          ),
        ),
        const SizedBox(height: 16),
        ..._requiredDocuments.asMap().entries.map((entry) {
          final index = entry.key;
          final doc = entry.value;
          final isUploaded = _uploadedDocuments.length > index;

          return Container(
            margin: const EdgeInsets.only(bottom: 16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isUploaded
                    ? Colors.green.withValues(alpha: 0.5)
                    : AppTheme.luxuryGrey,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                doc['name'],
                                style: AppTheme.fontStyles.bodyBold.copyWith(
                                  color: AppTheme.luxuryWhite,
                                ),
                              ),
                              if (doc['required']) ...[
                                const SizedBox(width: 4),
                                const Text(
                                  '*',
                                  style: TextStyle(
                                    color: Colors.red,
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ],
                          ),
                          const SizedBox(height: 4),
                          Text(
                            doc['description'],
                            style: AppTheme.fontStyles.caption.copyWith(
                              color: AppTheme.secondaryAccentColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (isUploaded)
                      const FaIcon(
                        FontAwesomeIcons.circleCheck,
                        color: Colors.green,
                        size: 20,
                      ),
                  ],
                ),
                const SizedBox(height: 12),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () => _uploadDocument(index, doc['name']),
                    icon: FaIcon(
                      isUploaded
                          ? FontAwesomeIcons.arrowRotateRight
                          : FontAwesomeIcons.cloudArrowUp,
                      size: 16,
                    ),
                    label: Text(isUploaded ? 'Replace' : 'Upload'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: isUploaded
                          ? Colors.orange
                          : AppTheme.accentColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                    ),
                  ),
                ),
              ],
            ),
          );
        }),
      ],
    );
  }

  void _uploadDocument(int index, String documentName) {
    // TODO: Implement actual document upload
    setState(() {
      if (_uploadedDocuments.length <= index) {
        _uploadedDocuments.add(
          '${documentName}_${DateTime.now().millisecondsSinceEpoch}.pdf',
        );
      } else {
        _uploadedDocuments[index] =
            '${documentName}_${DateTime.now().millisecondsSinceEpoch}.pdf';
      }
    });

    widget.onDocumentsUploaded(_uploadedDocuments);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$documentName uploaded successfully'),
        backgroundColor: Colors.green,
      ),
    );
  }
}

/// Review step for verification submission
class ReviewStep extends StatelessWidget {
  final Map<String, dynamic> formData;

  const ReviewStep({super.key, required this.formData});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Please review your information before submitting',
          style: AppTheme.fontStyles.body.copyWith(
            color: AppTheme.secondaryAccentColor,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Submitted Information',
                style: AppTheme.fontStyles.bodyBold.copyWith(
                  color: AppTheme.luxuryWhite,
                ),
              ),
              const SizedBox(height: 12),
              ...formData.entries.map(
                (entry) => Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        width: 120,
                        child: Text(
                          '${entry.key}:',
                          style: AppTheme.fontStyles.caption.copyWith(
                            color: AppTheme.secondaryAccentColor,
                          ),
                        ),
                      ),
                      Expanded(
                        child: Text(
                          entry.value.toString(),
                          style: AppTheme.fontStyles.caption.copyWith(
                            color: AppTheme.luxuryWhite,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.orange.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
          ),
          child: Row(
            children: [
              const FaIcon(
                FontAwesomeIcons.triangleExclamation,
                color: Colors.orange,
                size: 16,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Your verification will be reviewed by our team. You will receive an email notification once the review is complete.',
                  style: AppTheme.fontStyles.caption.copyWith(
                    color: Colors.orange,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

/// Completion step
class CompletionStep extends StatelessWidget {
  final VerificationType type;

  const CompletionStep({super.key, required this.type});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.green.withValues(alpha: 0.1),
            shape: BoxShape.circle,
          ),
          child: const FaIcon(
            FontAwesomeIcons.circleCheck,
            size: 48,
            color: Colors.green,
          ),
        ),
        const SizedBox(height: 24),
        Text(
          'Verification Complete!',
          style: AppTheme.fontStyles.title.copyWith(
            color: Colors.green,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Text(
          'Your ${_getTypeTitle(type)} verification has been submitted successfully.',
          style: AppTheme.fontStyles.body.copyWith(
            color: AppTheme.secondaryAccentColor,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  String _getTypeTitle(VerificationType type) {
    switch (type) {
      case VerificationType.email:
        return 'email';
      case VerificationType.phone:
        return 'phone';
      case VerificationType.identity:
        return 'identity';
      case VerificationType.business:
        return 'business';
      case VerificationType.billionaire:
        return 'billionaire';
      case VerificationType.celebrity:
        return 'celebrity';
    }
  }
}
