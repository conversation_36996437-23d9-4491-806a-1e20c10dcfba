import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/features/explore/screens/explore_main_screen.dart';

/// Comprehensive empty states for profile tabs
class ProfileEmptyStates {
  /// Empty state for posts tab
  static Widget posts({
    required bool isCurrentUser,
    VoidCallback? onCreatePost,
  }) {
    return _BaseEmptyState(
      icon: FontAwesomeIcons.camera,
      title: isCurrentUser ? 'Share Your First Post' : 'No Posts Yet',
      subtitle: isCurrentUser
          ? 'Capture and share moments from your luxury lifestyle'
          : 'This user hasn\'t shared any posts yet',
      actionButton: isCurrentUser && onCreatePost != null
          ? _EmptyStateButton(
              text: 'Create Post',
              icon: FontAwesomeIcons.plus,
              onPressed: onCreatePost,
            )
          : null,
      tips: isCurrentUser
          ? [
              'Share photos and videos of your experiences',
              'Use hashtags to reach more people',
              'Tag locations to show where you\'ve been',
            ]
          : null,
    );
  }

  /// Empty state for saved posts tab
  static Widget savedPosts({required bool isCurrentUser}) {
    return _BaseEmptyState(
      icon: FontAwesomeIcons.bookmark,
      title: isCurrentUser ? 'No Saved Posts Yet' : 'Private Collection',
      subtitle: isCurrentUser
          ? 'Save posts you want to revisit later'
          : 'Saved posts are private and only visible to the user',
      tips: isCurrentUser
          ? [
              'Tap the bookmark icon on any post to save it',
              'Organize your saved content by collections',
              'Access saved posts anytime from your profile',
            ]
          : null,
    );
  }

  /// Empty state for tagged posts tab
  static Widget taggedPosts({required bool isCurrentUser}) {
    return _BaseEmptyState(
      icon: FontAwesomeIcons.userTag,
      title: isCurrentUser ? 'No Tagged Posts Yet' : 'No Tagged Posts',
      subtitle: isCurrentUser
          ? 'When people tag you in their posts, they\'ll appear here'
          : 'Posts where this user is tagged will appear here',
      tips: isCurrentUser
          ? [
              'You can control who can tag you in privacy settings',
              'Remove yourself from tagged posts by long-pressing',
              'Tagged posts help showcase your social connections',
            ]
          : null,
    );
  }

  /// Empty state for archived posts tab
  static Widget archivedPosts() {
    return _BaseEmptyState(
      icon: FontAwesomeIcons.boxArchive,
      title: 'No Archived Posts',
      subtitle:
          'Posts you archive will be hidden from your profile but saved here',
      tips: [
        'Archive posts to hide them without deleting',
        'Only you can see your archived posts',
        'Restore archived posts anytime to your profile',
      ],
    );
  }

  /// Empty state for followers
  static Widget followers({required bool isCurrentUser}) {
    return _BaseEmptyState(
      icon: FontAwesomeIcons.users,
      title: isCurrentUser ? 'No Followers Yet' : 'No Followers',
      subtitle: isCurrentUser
          ? 'Start connecting with other billionaires and entrepreneurs'
          : 'This user doesn\'t have any followers yet',
      tips: isCurrentUser
          ? [
              'Share interesting content to attract followers',
              'Engage with other users\' posts and stories',
              'Use relevant hashtags in your posts',
            ]
          : null,
    );
  }

  /// Empty state for following
  static Widget following({
    required bool isCurrentUser,
    BuildContext? context,
  }) {
    return _BaseEmptyState(
      icon: FontAwesomeIcons.userPlus,
      title: isCurrentUser ? 'Not Following Anyone' : 'Not Following Anyone',
      subtitle: isCurrentUser
          ? 'Discover and follow other members of the community'
          : 'This user isn\'t following anyone yet',
      actionButton: isCurrentUser && context != null
          ? _EmptyStateButton(
              text: 'Discover People',
              icon: FontAwesomeIcons.compass,
              onPressed: () {
                // Navigate to discover/explore screen
                // For now, show a helpful message
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: const Text('Opening discover page...'),
                    backgroundColor: AppTheme.accentColor,
                    action: SnackBarAction(
                      label: 'Explore',
                      textColor: Colors.white,
                      onPressed: () {
                        // Navigate to explore screen for user discovery
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const ExploreMainScreen(),
                          ),
                        );
                      },
                    ),
                  ),
                );
              },
            )
          : null,
      tips: isCurrentUser
          ? [
              'Follow users whose content interests you',
              'Check out the explore page for suggestions',
              'Connect with people in your industry',
            ]
          : null,
    );
  }

  /// Empty state for stories
  static Widget stories({
    required bool isCurrentUser,
    VoidCallback? onCreateStory,
  }) {
    return _BaseEmptyState(
      icon: FontAwesomeIcons.plus,
      title: isCurrentUser ? 'Share Your Story' : 'No Stories',
      subtitle: isCurrentUser
          ? 'Share moments that disappear after 24 hours'
          : 'This user hasn\'t shared any stories recently',
      actionButton: isCurrentUser && onCreateStory != null
          ? _EmptyStateButton(
              text: 'Create Story',
              icon: FontAwesomeIcons.camera,
              onPressed: onCreateStory,
            )
          : null,
      tips: isCurrentUser
          ? [
              'Stories disappear after 24 hours',
              'Share behind-the-scenes moments',
              'Use story highlights to save important stories',
            ]
          : null,
    );
  }

  /// Empty state for search results
  static Widget searchResults({required String query}) {
    return _BaseEmptyState(
      icon: FontAwesomeIcons.magnifyingGlass,
      title: 'No Results Found',
      subtitle: 'No results found for "$query"',
      tips: [
        'Try different keywords or phrases',
        'Check your spelling',
        'Use more general terms',
      ],
    );
  }

  /// Empty state for notifications
  static Widget notifications() {
    return _BaseEmptyState(
      icon: FontAwesomeIcons.bell,
      title: 'No Notifications',
      subtitle: 'You\'re all caught up! New notifications will appear here',
      tips: [
        'Get notified when someone likes your posts',
        'Receive updates on comments and mentions',
        'Stay updated on follower activity',
      ],
    );
  }
}

/// Base empty state widget with consistent styling
class _BaseEmptyState extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final _EmptyStateButton? actionButton;
  final List<String>? tips;
  const _BaseEmptyState({
    required this.icon,
    required this.title,
    required this.subtitle,
    this.actionButton,
    this.tips,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Icon
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: AppTheme.accentColor.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: FaIcon(
                icon,
                size: 48,
                color: AppTheme.accentColor.withValues(alpha: 0.7),
              ),
            ),

            const SizedBox(height: 24),

            // Title
            Text(
              title,
              style: AppTheme.fontStyles.title.copyWith(
                color: AppTheme.luxuryWhite,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 12),

            // Subtitle
            Text(
              subtitle,
              style: AppTheme.fontStyles.body.copyWith(
                color: AppTheme.secondaryAccentColor.withValues(alpha: 0.8),
              ),
              textAlign: TextAlign.center,
            ),

            // Action Button
            if (actionButton != null) ...[
              const SizedBox(height: 24),
              actionButton!,
            ],

            // Tips
            if (tips != null && tips!.isNotEmpty) ...[
              const SizedBox(height: 32),
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppTheme.accentColor.withValues(alpha: 0.3),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        FaIcon(
                          FontAwesomeIcons.lightbulb,
                          size: 16,
                          color: AppTheme.accentColor,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Tips',
                          style: AppTheme.fontStyles.bodyBold.copyWith(
                            color: AppTheme.accentColor,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    ...tips!.map(
                      (tip) => Padding(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              margin: const EdgeInsets.only(top: 6),
                              width: 4,
                              height: 4,
                              decoration: BoxDecoration(
                                color: AppTheme.accentColor.withValues(
                                  alpha: 0.7,
                                ),
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                tip,
                                style: AppTheme.fontStyles.caption.copyWith(
                                  color: AppTheme.secondaryAccentColor
                                      .withValues(alpha: 0.9),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Action button for empty states
class _EmptyStateButton extends StatelessWidget {
  final String text;
  final IconData icon;
  final VoidCallback onPressed;

  const _EmptyStateButton({
    required this.text,
    required this.icon,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: FaIcon(icon, size: 16),
      label: Text(text),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppTheme.accentColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        elevation: 2,
      ),
    );
  }
}
