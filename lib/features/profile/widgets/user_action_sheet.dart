import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:billionaires_social/features/profile/providers/user_blocking_provider.dart';
import 'package:billionaires_social/features/profile/models/profile_model.dart';
import 'package:billionaires_social/features/profile/widgets/enhanced_close_friends_manager.dart';
import 'package:billionaires_social/features/profile/screens/profile_verification_screen.dart';
import 'package:billionaires_social/core/app_theme.dart';

/// Action sheet for user profile interactions (block, report, message, etc.)
class UserActionSheet extends ConsumerStatefulWidget {
  final ProfileModel user;
  final bool isCurrentUser;

  const UserActionSheet({
    super.key,
    required this.user,
    required this.isCurrentUser,
  });

  @override
  ConsumerState<UserActionSheet> createState() => _UserActionSheetState();
}

class _UserActionSheetState extends ConsumerState<UserActionSheet> {
  @override
  Widget build(BuildContext context) {
    if (widget.isCurrentUser) {
      return _buildCurrentUserActions();
    }

    return FutureBuilder<bool>(
      future: ref.read(isUserBlockedProvider(widget.user.id).future),
      builder: (context, snapshot) {
        final isBlocked = snapshot.data ?? false;

        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: AppTheme.luxuryBlack,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: AppTheme.luxuryGrey,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(height: 20),

              // User info header
              Row(
                children: [
                  CircleAvatar(
                    radius: 24,
                    backgroundImage: NetworkImage(
                      widget.user.profilePictureUrl,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              widget.user.name,
                              style: AppTheme.fontStyles.bodyBold.copyWith(
                                color: AppTheme.luxuryWhite,
                              ),
                            ),
                            if (widget.user.isVerified) ...[
                              const SizedBox(width: 4),
                              FaIcon(
                                FontAwesomeIcons.circleCheck,
                                size: 16,
                                color: AppTheme.accentColor,
                              ),
                            ],
                          ],
                        ),
                        Text(
                          '@${widget.user.username}',
                          style: AppTheme.fontStyles.caption.copyWith(
                            color: AppTheme.secondaryAccentColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Action buttons
              if (!isBlocked) ...[
                _buildActionTile(
                  icon: FontAwesomeIcons.message,
                  title: 'Send Message',
                  subtitle: 'Start a conversation',
                  color: AppTheme.accentColor,
                  onTap: () => _sendMessage(),
                ),
                _buildActionTile(
                  icon: FontAwesomeIcons.userPlus,
                  title: 'Add to Close Friends',
                  subtitle: 'Share exclusive content',
                  color: Colors.green,
                  onTap: () => _addToCloseFriends(),
                ),
                _buildActionTile(
                  icon: FontAwesomeIcons.share,
                  title: 'Share Profile',
                  subtitle: 'Share with others',
                  color: Colors.blue,
                  onTap: () => _shareProfile(),
                ),
                const Divider(color: Colors.grey),
                _buildActionTile(
                  icon: FontAwesomeIcons.flag,
                  title: 'Report User',
                  subtitle: 'Report inappropriate behavior',
                  color: Colors.orange,
                  onTap: () => _showReportDialog(),
                ),
                _buildActionTile(
                  icon: FontAwesomeIcons.ban,
                  title: 'Block User',
                  subtitle: 'Block this user',
                  color: Colors.red,
                  onTap: () => _showBlockConfirmation(),
                ),
              ] else ...[
                _buildActionTile(
                  icon: FontAwesomeIcons.userCheck,
                  title: 'Unblock User',
                  subtitle: 'Allow interactions again',
                  color: Colors.green,
                  onTap: () => _showUnblockConfirmation(),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildCurrentUserActions() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.luxuryBlack,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppTheme.luxuryGrey,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 20),

          Text(
            'Profile Actions',
            style: AppTheme.fontStyles.title.copyWith(
              color: AppTheme.luxuryWhite,
            ),
          ),
          const SizedBox(height: 20),

          _buildActionTile(
            icon: FontAwesomeIcons.penToSquare,
            title: 'Edit Profile',
            subtitle: 'Update your profile information',
            color: AppTheme.accentColor,
            onTap: () => _editProfile(),
          ),
          _buildActionTile(
            icon: FontAwesomeIcons.shieldHalved,
            title: 'Verification',
            subtitle: 'Verify your profile for trust and credibility',
            color: Colors.green,
            onTap: () => _openVerification(),
          ),
          _buildActionTile(
            icon: FontAwesomeIcons.boxArchive,
            title: 'Archived Posts',
            subtitle: 'View and manage your archived posts',
            color: Colors.orange,
            onTap: () => _openArchivedPosts(),
          ),
          _buildActionTile(
            icon: FontAwesomeIcons.gear,
            title: 'Settings',
            subtitle: 'Privacy and account settings',
            color: Colors.grey,
            onTap: () => _openSettings(),
          ),
          _buildActionTile(
            icon: FontAwesomeIcons.share,
            title: 'Share Profile',
            subtitle: 'Share your profile with others',
            color: Colors.blue,
            onTap: () => _shareProfile(),
          ),
        ],
      ),
    );
  }

  Widget _buildActionTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: FaIcon(icon, size: 20, color: color),
      ),
      title: Text(
        title,
        style: AppTheme.fontStyles.body.copyWith(
          color: AppTheme.luxuryWhite,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: AppTheme.fontStyles.caption.copyWith(color: Colors.grey[300]),
      ),
      onTap: () {
        Navigator.pop(context);
        onTap();
      },
    );
  }

  void _sendMessage() {
    Navigator.pop(context); // Close the action sheet first

    // Navigate to messaging with the user
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Opening chat with ${widget.user.name}...',
          style: TextStyle(
            color: AppTheme.luxuryBlack,
            fontWeight: FontWeight.w500,
          ),
        ),
        backgroundColor: AppTheme.accentColor,
        action: SnackBarAction(
          label: 'Open',
          textColor: AppTheme.luxuryBlack,
          onPressed: () {
            // TODO: Implement actual chat navigation when ChatScreen is ready
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'Chat functionality will be implemented soon',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                backgroundColor: AppTheme.luxuryBlack,
              ),
            );
          },
        ),
      ),
    );
  }

  void _addToCloseFriends() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.black.withValues(alpha: 0.5),
      elevation: 10,
      enableDrag: true,
      isDismissible: true,
      builder: (context) =>
          const EnhancedCloseFriendsManager(showAsBottomSheet: true),
    );
  }

  void _shareProfile() {
    Navigator.pop(context); // Close the action sheet first

    final profileUrl =
        'https://billionaires.social/profile/${widget.user.username}';
    final shareText =
        'Check out ${widget.user.name}\'s profile on Billionaires Social: $profileUrl';

    // Show share options
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      elevation: 10,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: AppTheme.luxuryBlack,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.5),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppTheme.luxuryGrey,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  Text(
                    'Share Profile',
                    style: AppTheme.fontStyles.title.copyWith(
                      color: AppTheme.luxuryWhite,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 20),

                  // Copy link option
                  ListTile(
                    leading: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppTheme.accentColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: FaIcon(
                        FontAwesomeIcons.link,
                        color: AppTheme.accentColor,
                        size: 20,
                      ),
                    ),
                    title: Text(
                      'Copy Link',
                      style: AppTheme.fontStyles.bodyBold.copyWith(
                        color: AppTheme.luxuryWhite,
                      ),
                    ),
                    subtitle: Text(
                      'Copy profile link to clipboard',
                      style: AppTheme.fontStyles.caption.copyWith(
                        color: AppTheme.secondaryAccentColor,
                      ),
                    ),
                    onTap: () {
                      // Copy the full share text to clipboard
                      Navigator.pop(context);
                      // TODO: Implement actual clipboard copy with Clipboard.setData(ClipboardData(text: shareText))
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            'Profile link copied to clipboard',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          backgroundColor: Colors.green,
                          action: SnackBarAction(
                            label: 'Preview',
                            textColor: Colors.white,
                            onPressed: () {
                              // Show what was copied
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    'Copied: $shareText',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  backgroundColor: AppTheme.luxuryBlack,
                                  duration: const Duration(seconds: 3),
                                ),
                              );
                            },
                          ),
                        ),
                      );
                    },
                  ),

                  // Share via text option
                  ListTile(
                    leading: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.blue.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const FaIcon(
                        FontAwesomeIcons.shareNodes,
                        color: Colors.blue,
                        size: 20,
                      ),
                    ),
                    title: Text(
                      'Share via...',
                      style: AppTheme.fontStyles.bodyBold.copyWith(
                        color: AppTheme.luxuryWhite,
                      ),
                    ),
                    subtitle: Text(
                      'Share using other apps',
                      style: AppTheme.fontStyles.caption.copyWith(
                        color: AppTheme.secondaryAccentColor,
                      ),
                    ),
                    onTap: () {
                      Navigator.pop(context);
                      // TODO: Implement system share when share_plus is available
                      // Example: Share.share(shareText, subject: 'Check out this profile');
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            'System sharing will be implemented soon',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          backgroundColor: AppTheme.accentColor,
                          action: SnackBarAction(
                            label: 'Preview',
                            textColor: Colors.white,
                            onPressed: () {
                              // Show what would be shared
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    'Would share: $shareText',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  backgroundColor: AppTheme.luxuryBlack,
                                  duration: const Duration(seconds: 3),
                                ),
                              );
                            },
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showReportDialog() {
    showDialog(
      context: context,
      builder: (context) => ReportUserDialog(user: widget.user),
    );
  }

  void _showBlockConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.luxuryBlack,
        title: Text(
          'Block ${widget.user.name}?',
          style: AppTheme.fontStyles.title.copyWith(
            color: AppTheme.luxuryWhite,
          ),
        ),
        content: Text(
          'They won\'t be able to find your profile, posts, or story on Billionaires Social. They won\'t be notified that you blocked them.',
          style: AppTheme.fontStyles.body.copyWith(
            color: AppTheme.secondaryAccentColor,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: TextStyle(color: AppTheme.secondaryAccentColor),
            ),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await _blockUser();
            },
            child: const Text('Block', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showUnblockConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.luxuryBlack,
        title: Text(
          'Unblock ${widget.user.name}?',
          style: AppTheme.fontStyles.title.copyWith(
            color: AppTheme.luxuryWhite,
          ),
        ),
        content: Text(
          'They will be able to find your profile, posts, and story again. They won\'t be notified that you unblocked them.',
          style: AppTheme.fontStyles.body.copyWith(
            color: AppTheme.secondaryAccentColor,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: TextStyle(color: AppTheme.secondaryAccentColor),
            ),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await _unblockUser();
            },
            child: Text(
              'Unblock',
              style: TextStyle(color: AppTheme.accentColor),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _blockUser() async {
    try {
      await ref
          .read(userBlockingNotifierProvider.notifier)
          .blockUser(widget.user.id);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Blocked ${widget.user.name}',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to block user: $e',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _unblockUser() async {
    try {
      await ref
          .read(userBlockingNotifierProvider.notifier)
          .unblockUser(widget.user.id);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Unblocked ${widget.user.name}',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to unblock user: $e',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _editProfile() {
    Navigator.pop(context); // Close the action sheet first

    // Navigate to edit profile screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Opening profile editor...'),
        backgroundColor: AppTheme.accentColor,
        action: SnackBarAction(
          label: 'Edit',
          textColor: Colors.white,
          onPressed: () {
            // TODO: Navigate to actual edit profile screen when available
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Profile editor will be implemented soon'),
              ),
            );
          },
        ),
      ),
    );
  }

  void _openVerification() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ProfileVerificationScreen(),
      ),
    );
  }

  void _openArchivedPosts() {
    Navigator.pop(context); // Close the action sheet first

    // Navigate to archived posts tab
    try {
      DefaultTabController.of(context).animateTo(3); // Archive tab is index 3
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Switched to Archived Posts',
            style: TextStyle(
              color: AppTheme.luxuryBlack,
              fontWeight: FontWeight.w500,
            ),
          ),
          backgroundColor: Colors.orange,
          duration: const Duration(seconds: 2),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Please navigate to your profile to view archived posts',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.w500),
          ),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  void _openSettings() {
    Navigator.pop(context); // Close the action sheet first

    // Navigate to settings screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Opening settings...'),
        backgroundColor: Colors.grey,
        action: SnackBarAction(
          label: 'Settings',
          textColor: Colors.white,
          onPressed: () {
            // TODO: Navigate to actual settings screen when available
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Settings screen will be implemented soon'),
              ),
            );
          },
        ),
      ),
    );
  }
}

/// Dialog for reporting users
class ReportUserDialog extends ConsumerStatefulWidget {
  final ProfileModel user;

  const ReportUserDialog({super.key, required this.user});

  @override
  ConsumerState<ReportUserDialog> createState() => _ReportUserDialogState();
}

class _ReportUserDialogState extends ConsumerState<ReportUserDialog> {
  UserReportReason? selectedReason;
  final TextEditingController _detailsController = TextEditingController();
  bool _isSubmitting = false;

  @override
  void dispose() {
    _detailsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final reasons = ref.watch(userReportReasonsProvider);

    return AlertDialog(
      backgroundColor: AppTheme.luxuryBlack,
      title: Text(
        'Report ${widget.user.name}',
        style: AppTheme.fontStyles.title.copyWith(color: AppTheme.luxuryWhite),
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Why are you reporting this user?',
              style: AppTheme.fontStyles.body.copyWith(
                color: AppTheme.secondaryAccentColor,
              ),
            ),
            const SizedBox(height: 16),

            // Reason selection
            ...reasons.map(
              (reason) => RadioListTile<UserReportReason>(
                title: Text(
                  reason.displayName,
                  style: AppTheme.fontStyles.body.copyWith(
                    color: AppTheme.luxuryWhite,
                  ),
                ),
                value: reason,
                groupValue: selectedReason,
                activeColor: AppTheme.accentColor,
                onChanged: (value) {
                  setState(() {
                    selectedReason = value;
                  });
                },
              ),
            ),

            const SizedBox(height: 16),

            // Additional details
            TextField(
              controller: _detailsController,
              style: AppTheme.fontStyles.body.copyWith(
                color: AppTheme.luxuryWhite,
              ),
              decoration: InputDecoration(
                hintText: 'Additional details (optional)',
                hintStyle: AppTheme.fontStyles.body.copyWith(
                  color: Colors.grey[400],
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: AppTheme.luxuryGrey),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: AppTheme.accentColor),
                ),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isSubmitting ? null : () => Navigator.pop(context),
          child: Text(
            'Cancel',
            style: TextStyle(color: AppTheme.secondaryAccentColor),
          ),
        ),
        TextButton(
          onPressed: _isSubmitting || selectedReason == null
              ? null
              : _submitReport,
          child: _isSubmitting
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Report', style: TextStyle(color: Colors.red)),
        ),
      ],
    );
  }

  Future<void> _submitReport() async {
    if (selectedReason == null) return;

    setState(() {
      _isSubmitting = true;
    });

    try {
      await ref
          .read(userBlockingNotifierProvider.notifier)
          .reportUser(
            userId: widget.user.id,
            reason: selectedReason!.name,
            additionalDetails: _detailsController.text.trim(),
          );

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Reported ${widget.user.name}',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to report user: $e',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }
}
