import 'package:billionaires_social/features/profile/screens/followers_following_screen.dart';
import 'package:billionaires_social/features/stories/widgets/story_aware_profile_image.dart';
import 'package:billionaires_social/features/stories/widgets/profile_story_image.dart';

import 'package:billionaires_social/features/profile/widgets/enhanced_loading_animations.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:billionaires_social/features/profile/providers/profile_provider.dart';
import 'package:firebase_auth/firebase_auth.dart';

import 'package:billionaires_social/features/profile/providers/profile_verification_provider.dart';
import 'package:billionaires_social/features/profile/models/profile_verification_model.dart';
import 'package:billionaires_social/features/stories/screens/story_creation_screen.dart';
import 'package:billionaires_social/core/main_navigation.dart';

import 'package:billionaires_social/core/services/universal_ui_service.dart';

class ProfileHeader extends ConsumerStatefulWidget {
  final String userId;
  final bool? isCurrentUser; // Universal: auto-detect if not provided

  const ProfileHeader({
    super.key,
    required this.userId,
    this.isCurrentUser, // Universal: will auto-detect if null
  });

  @override
  ConsumerState<ProfileHeader> createState() => _ProfileHeaderState();
}

class _ProfileHeaderState extends ConsumerState<ProfileHeader> {
  // Universal logic: determine if this is current user
  bool get _isCurrentUser {
    if (widget.isCurrentUser != null) {
      return widget.isCurrentUser!;
    }
    // Auto-detect if not provided
    final currentUserId = FirebaseAuth.instance.currentUser?.uid;
    return currentUserId == widget.userId;
  }

  Widget _buildCurrentUserStorySection(dynamic profile) {
    return Column(
      children: [
        Stack(
          children: [
            ProfileStoryImage(
              userId: widget.userId,
              profileImageUrl: profile.profilePictureUrl.isNotEmpty
                  ? profile.profilePictureUrl
                  : null,
              size: 80,
              showStoryIndicator: true,
              showSettings: true, // Enable settings for profile context
              onTap: () {
                // Navigate to story creation when no stories exist
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const StoryCreationScreen(
                      initialMode: CreationMode.story,
                    ),
                  ),
                );
              },
            ),
            // Add story button overlay for current user
            Positioned(
              bottom: 0,
              right: 0,
              child: GestureDetector(
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const StoryCreationScreen(
                        initialMode: CreationMode.story,
                      ),
                    ),
                  );
                },
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Theme.of(context).scaffoldBackgroundColor,
                      width: 2,
                    ),
                  ),
                  child: Icon(
                    Icons.add,
                    size: 16,
                    color: Theme.of(context).colorScheme.onPrimary,
                  ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          'Add Story',
          style: TextStyle(
            color: const Color(0xFF000000), // Black
            fontSize: 13,
            fontWeight: FontWeight.w600,
            fontFamily: 'SF Pro Display',
            letterSpacing: 0.2,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final profileAsync = ref.watch(profileProvider(widget.userId));

    return profileAsync.when(
      data: (profile) => Container(
        color: Colors.transparent,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              _isCurrentUser
                  ? _buildCurrentUserStorySection(profile)
                  : StoryAwareProfileImage(
                      userId: widget.userId,
                      profileImageUrl: profile.profilePictureUrl.isNotEmpty
                          ? profile.profilePictureUrl
                          : null,
                      size: 80,
                      showStoryIndicator: true,
                    ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    profile.name,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF000000), // Black
                      fontSize: 22,
                      fontFamily: 'SF Pro Display',
                      letterSpacing: 0.5,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Consumer(
                    builder: (context, ref, _) {
                      final verificationsAsync = ref.watch(
                        profileVerificationsProvider,
                      );
                      return verificationsAsync.when(
                        data: (verifications) {
                          final approvedTypes = [
                            VerificationType.email,
                            VerificationType.phone,
                            VerificationType.identity,
                            VerificationType.business,
                          ];
                          final icons = <Widget>[];
                          for (final type in approvedTypes) {
                            final v = verifications
                                .where(
                                  (e) =>
                                      e.type == type &&
                                      e.status == VerificationStatus.approved,
                                )
                                .toList();
                            if (v.isNotEmpty) {
                              icons.add(_verificationBadgeIcon(type));
                            }
                          }
                          return Row(children: icons);
                        },
                        loading: () => const SizedBox.shrink(),
                        error: (e, s) => const SizedBox.shrink(),
                      );
                    },
                  ),
                ],
              ),
              const SizedBox(height: 4),
              // Username display
              Text(
                '@${profile.username}',
                style: TextStyle(
                  color: const Color(0xFF000000), // Black
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                  fontFamily: 'SF Pro Display',
                  letterSpacing: 0.3,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                profile.bio,
                style: TextStyle(
                  color: const Color(0xFF000000), // Black
                  fontSize: 15,
                  fontWeight: FontWeight.w400,
                  fontFamily: 'SF Pro Display',
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatColumn(context, 'Posts', profile.postCount, null),
                  // Real-time follower count
                  Consumer(
                    builder: (context, ref, child) {
                      final followerCountAsync = ref.watch(
                        followerCountProvider(widget.userId),
                      );
                      return followerCountAsync.when(
                        data: (followerCount) => _buildStatColumn(
                          context,
                          'Followers',
                          followerCount,
                          () {
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (_) => const FollowersFollowingScreen(
                                  initialTab: 0,
                                ),
                              ),
                            );
                          },
                        ),
                        loading: () => _buildStatColumn(
                          context,
                          'Followers',
                          profile.followerCount, // Fallback to cached count
                          () {
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (_) => const FollowersFollowingScreen(
                                  initialTab: 0,
                                ),
                              ),
                            );
                          },
                        ),
                        error: (error, _) => _buildStatColumn(
                          context,
                          'Followers',
                          profile.followerCount, // Fallback to cached count
                          () {
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (_) => const FollowersFollowingScreen(
                                  initialTab: 0,
                                ),
                              ),
                            );
                          },
                        ),
                      );
                    },
                  ),
                  // Real-time following count
                  Consumer(
                    builder: (context, ref, child) {
                      final followingCountAsync = ref.watch(
                        followingCountProvider(widget.userId),
                      );
                      return followingCountAsync.when(
                        data: (followingCount) => _buildStatColumn(
                          context,
                          'Following',
                          followingCount,
                          () {
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (_) => const FollowersFollowingScreen(
                                  initialTab: 1,
                                ),
                              ),
                            );
                          },
                        ),
                        loading: () => _buildStatColumn(
                          context,
                          'Following',
                          profile.followingCount, // Fallback to cached count
                          () {
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (_) => const FollowersFollowingScreen(
                                  initialTab: 1,
                                ),
                              ),
                            );
                          },
                        ),
                        error: (error, _) => _buildStatColumn(
                          context,
                          'Following',
                          profile.followingCount, // Fallback to cached count
                          () {
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (_) => const FollowersFollowingScreen(
                                  initialTab: 1,
                                ),
                              ),
                            );
                          },
                        ),
                      );
                    },
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // Buttons
              _buildActionButtons(context),
            ],
          ),
        ),
      ),
      loading: () => EnhancedLoadingAnimations.profileHeaderSkeleton(),
      error: (err, stack) =>
          const Center(child: Text('Failed to load profile')),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        final profileAsync = ref.watch(profileProvider(widget.userId));

        return profileAsync.when(
          data: (profile) => FutureBuilder<List<Widget>>(
            future: UniversalUIService.getProfileActionButtons(
              context: context,
              targetUserId: widget.userId,
              targetUsername: profile.username,
            ),
            builder: (context, snapshot) {
              if (snapshot.hasData && snapshot.data!.isNotEmpty) {
                return Row(children: snapshot.data!);
              }

              // Fallback to loading state
              return const Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: null,
                      child: Text('Loading...'),
                    ),
                  ),
                ],
              );
            },
          ),
          loading: () => const Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: null,
                  child: Text('Loading...'),
                ),
              ),
            ],
          ),
          error: (error, stack) => const Row(
            children: [
              Expanded(
                child: ElevatedButton(onPressed: null, child: Text('Error')),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatColumn(
    BuildContext context,
    String label,
    int count,
    VoidCallback? onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: onTap != null
              ? AppTheme.accentColor.withValues(alpha: 0.1)
              : Colors.transparent,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              NumberFormat.compact().format(count),
              style: TextStyle(
                color: const Color(0xFF000000), // Black
                fontWeight: FontWeight.bold,
                fontSize: 20,
                fontFamily: 'SF Pro Display',
              ),
            ),
            const SizedBox(height: 2),
            Text(
              label,
              style: TextStyle(
                color: const Color(0xFF000000), // Black
                fontWeight: FontWeight.w500,
                fontSize: 13,
                fontFamily: 'SF Pro Display',
                letterSpacing: 0.2,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Icon _verificationBadgeIcon(VerificationType type) {
    switch (type) {
      case VerificationType.email:
        return const Icon(Icons.verified, color: Colors.blue, size: 18);
      case VerificationType.phone:
        return const Icon(Icons.phone_iphone, color: Colors.green, size: 18);
      case VerificationType.identity:
        return const Icon(Icons.badge, color: Colors.purple, size: 18);
      case VerificationType.business:
        return const Icon(Icons.business, color: Colors.orange, size: 18);
      case VerificationType.celebrity:
        return const Icon(Icons.star, color: Colors.amber, size: 18);
      case VerificationType.billionaire:
        return const Icon(Icons.diamond, color: Colors.teal, size: 18);
    }
  }
}
