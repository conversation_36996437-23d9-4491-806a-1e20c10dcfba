import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class AccountIndicator extends StatelessWidget {
  final ProfileAccountType accountType;
  final VoidCallback? onTap;

  const AccountIndicator({super.key, required this.accountType, this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: _getAccountTypeColor().withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: _getAccountTypeColor().withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            FaIcon(
              _getAccountTypeIcon(),
              color: _getAccountTypeColor(),
              size: 12,
            ),
            const SizedBox(width: 4),
            Text(
              _getAccountTypeLabel(),
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                color: _getAccountTypeColor(),
                fontWeight: FontWeight.bold,
              ),
            ),
            if (onTap != null) ...[
              const SizedBox(width: 4),
              FaIcon(
                FontAwesomeIcons.chevronDown,
                color: _getAccountTypeColor(),
                size: 10,
              ),
            ],
          ],
        ),
      ),
    );
  }

  IconData _getAccountTypeIcon() {
    switch (accountType) {
      case ProfileAccountType.personal:
        return FontAwesomeIcons.user;
      case ProfileAccountType.business:
        return FontAwesomeIcons.briefcase;
      case ProfileAccountType.celebrity:
        return FontAwesomeIcons.star;
    }
  }

  Color _getAccountTypeColor() {
    switch (accountType) {
      case ProfileAccountType.personal:
        return Colors.blue;
      case ProfileAccountType.business:
        return Colors.green;
      case ProfileAccountType.celebrity:
        return Colors.amber;
    }
  }

  String _getAccountTypeLabel() {
    switch (accountType) {
      case ProfileAccountType.personal:
        return 'Personal';
      case ProfileAccountType.business:
        return 'Business';
      case ProfileAccountType.celebrity:
        return 'Celebrity';
    }
  }
}

enum ProfileAccountType { personal, business, celebrity }
