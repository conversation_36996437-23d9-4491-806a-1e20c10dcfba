import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:billionaires_social/features/profile/providers/close_friends_provider.dart';
import 'package:billionaires_social/features/profile/providers/close_friends_groups_provider.dart';
import 'package:billionaires_social/features/profile/models/close_friends_group_model.dart';
import 'package:billionaires_social/features/profile/models/profile_model.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/core/widgets/error_display_widget.dart';
import 'package:cached_network_image/cached_network_image.dart';

/// Enhanced close friends management widget with intuitive controls
class EnhancedCloseFriendsManager extends ConsumerStatefulWidget {
  final bool showAsBottomSheet;

  const EnhancedCloseFriendsManager({
    super.key,
    this.showAsBottomSheet = false,
  });

  @override
  ConsumerState<EnhancedCloseFriendsManager> createState() =>
      _EnhancedCloseFriendsManagerState();
}

class _EnhancedCloseFriendsManagerState
    extends ConsumerState<EnhancedCloseFriendsManager>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text.toLowerCase();
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.showAsBottomSheet) {
      return _buildBottomSheetContent();
    }

    return Scaffold(
      backgroundColor: AppTheme.luxuryBlack,
      appBar: AppBar(
        backgroundColor: AppTheme.luxuryBlack,
        title: Text(
          'Close Friends',
          style: AppTheme.fontStyles.title.copyWith(
            color: AppTheme.luxuryWhite,
          ),
        ),
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: AppTheme.accentColor,
          labelColor: AppTheme.accentColor,
          unselectedLabelColor: AppTheme.secondaryAccentColor,
          tabs: const [
            Tab(text: 'Friends'),
            Tab(text: 'Groups'),
          ],
        ),
      ),
      body: _buildContent(),
    );
  }

  Widget _buildBottomSheetContent() {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: BoxDecoration(
        color: AppTheme.luxuryBlack,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppTheme.luxuryGrey,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Text(
                  'Close Friends',
                  style: AppTheme.fontStyles.title.copyWith(
                    color: AppTheme.luxuryWhite,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const FaIcon(
                    FontAwesomeIcons.xmark,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),

          // Tab bar
          TabBar(
            controller: _tabController,
            indicatorColor: AppTheme.accentColor,
            labelColor: AppTheme.accentColor,
            unselectedLabelColor: AppTheme.secondaryAccentColor,
            tabs: const [
              Tab(text: 'Friends'),
              Tab(text: 'Groups'),
            ],
          ),

          // Content
          Expanded(child: _buildContent()),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return TabBarView(
      controller: _tabController,
      children: [_buildFriendsTab(), _buildGroupsTab()],
    );
  }

  Widget _buildFriendsTab() {
    final closeFriendsAsync = ref.watch(closeFriendsNotifierProvider);

    return closeFriendsAsync.when(
      data: (state) {
        final filteredFollowers = state.followers.where((follower) {
          return follower.name.toLowerCase().contains(_searchQuery) ||
              follower.username.toLowerCase().contains(_searchQuery);
        }).toList();

        return Column(
          children: [
            // Search bar
            Padding(
              padding: const EdgeInsets.all(16),
              child: TextField(
                controller: _searchController,
                style: AppTheme.fontStyles.body.copyWith(
                  color: AppTheme.luxuryWhite,
                ),
                decoration: InputDecoration(
                  hintText: 'Search followers...',
                  hintStyle: AppTheme.fontStyles.body.copyWith(
                    color: AppTheme.secondaryAccentColor.withValues(alpha: 0.7),
                  ),
                  prefixIcon: FaIcon(
                    FontAwesomeIcons.magnifyingGlass,
                    color: AppTheme.secondaryAccentColor,
                    size: 16,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: AppTheme.luxuryGrey),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: AppTheme.accentColor),
                  ),
                  filled: true,
                  fillColor: AppTheme.luxuryGrey.withValues(alpha: 0.1),
                ),
              ),
            ),

            // Close friends count
            if (state.closeFriendIds.isNotEmpty)
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppTheme.accentColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: AppTheme.accentColor.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    FaIcon(
                      FontAwesomeIcons.userGroup,
                      color: AppTheme.accentColor,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '${state.closeFriendIds.length} close friends',
                      style: AppTheme.fontStyles.bodyBold.copyWith(
                        color: AppTheme.accentColor,
                      ),
                    ),
                  ],
                ),
              ),

            // Friends list
            Expanded(
              child: filteredFollowers.isEmpty
                  ? _buildEmptyFriendsState()
                  : ListView.builder(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      itemCount: filteredFollowers.length,
                      itemBuilder: (context, index) {
                        final follower = filteredFollowers[index];
                        final isCloseFriend = state.closeFriendIds.contains(
                          follower.id,
                        );

                        return _buildFriendTile(follower, isCloseFriend);
                      },
                    ),
            ),
          ],
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => ErrorDisplayWidget(
        error: error,
        context: 'Loading close friends',
        onRetry: () {
          ref.invalidate(closeFriendsNotifierProvider);
        },
      ),
    );
  }

  Widget _buildFriendTile(ProfileModel follower, bool isCloseFriend) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: isCloseFriend
            ? Border.all(color: AppTheme.accentColor.withValues(alpha: 0.5))
            : null,
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: Stack(
          children: [
            CircleAvatar(
              radius: 24,
              backgroundImage: CachedNetworkImageProvider(
                follower.profilePictureUrl,
              ),
            ),
            if (isCloseFriend)
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: AppTheme.accentColor,
                    shape: BoxShape.circle,
                    border: Border.all(color: AppTheme.luxuryBlack, width: 2),
                  ),
                  child: const FaIcon(
                    FontAwesomeIcons.star,
                    size: 10,
                    color: Colors.white,
                  ),
                ),
              ),
          ],
        ),
        title: Row(
          children: [
            Text(
              follower.name,
              style: AppTheme.fontStyles.bodyBold.copyWith(
                color: AppTheme.luxuryWhite,
              ),
            ),
            if (follower.isVerified) ...[
              const SizedBox(width: 4),
              FaIcon(
                FontAwesomeIcons.circleCheck,
                size: 14,
                color: AppTheme.accentColor,
              ),
            ],
          ],
        ),
        subtitle: Text(
          '@${follower.username}',
          style: AppTheme.fontStyles.caption.copyWith(
            color: AppTheme.secondaryAccentColor,
          ),
        ),
        trailing: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          child: Switch(
            value: isCloseFriend,
            onChanged: (value) {
              ref
                  .read(closeFriendsNotifierProvider.notifier)
                  .toggleCloseFriend(follower.id);

              // Show feedback
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    value
                        ? 'Added ${follower.name} to close friends'
                        : 'Removed ${follower.name} from close friends',
                  ),
                  backgroundColor: value ? Colors.green : Colors.orange,
                  duration: const Duration(seconds: 2),
                ),
              );
            },
            activeColor: AppTheme.accentColor,
            activeTrackColor: AppTheme.accentColor.withValues(alpha: 0.3),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyFriendsState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: AppTheme.accentColor.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: FaIcon(
              FontAwesomeIcons.userGroup,
              size: 48,
              color: AppTheme.accentColor.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 24),
          Text(
            _searchQuery.isEmpty ? 'No Followers Yet' : 'No Results Found',
            style: AppTheme.fontStyles.title.copyWith(
              color: AppTheme.luxuryWhite,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              _searchQuery.isEmpty
                  ? 'When people follow you, you can add them to your close friends here'
                  : 'Try searching with a different name or username',
              style: AppTheme.fontStyles.body.copyWith(
                color: AppTheme.secondaryAccentColor.withValues(alpha: 0.8),
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGroupsTab() {
    final groupsAsync = ref.watch(closeFriendsGroupsNotifierProvider);

    return groupsAsync.when(
      data: (groups) => Column(
        children: [
          // Create group button
          Padding(
            padding: const EdgeInsets.all(16),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _showCreateGroupDialog(),
                icon: const FaIcon(FontAwesomeIcons.plus, size: 16),
                label: const Text('Create New Group'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.accentColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ),

          // Groups list
          Expanded(
            child: groups.isEmpty
                ? _buildEmptyGroupsState()
                : ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: groups.length,
                    itemBuilder: (context, index) {
                      final group = groups[index];
                      return _buildGroupTile(group);
                    },
                  ),
          ),
        ],
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => ErrorDisplayWidget(
        error: error,
        context: 'Loading close friends groups',
        onRetry: () {
          ref.invalidate(closeFriendsGroupsNotifierProvider);
        },
      ),
    );
  }

  Widget _buildGroupTile(CloseFriendsGroup group) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.accentColor.withValues(alpha: 0.2)),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: AppTheme.accentColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Center(
            child: Text(group.emoji, style: const TextStyle(fontSize: 24)),
          ),
        ),
        title: Text(
          group.name,
          style: AppTheme.fontStyles.bodyBold.copyWith(
            color: AppTheme.luxuryWhite,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              '${group.memberIds.length} members',
              style: AppTheme.fontStyles.caption.copyWith(
                color: AppTheme.secondaryAccentColor,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                FaIcon(
                  group.isStoryVisible
                      ? FontAwesomeIcons.eye
                      : FontAwesomeIcons.eyeSlash,
                  size: 12,
                  color: group.isStoryVisible ? Colors.green : Colors.orange,
                ),
                const SizedBox(width: 4),
                Text(
                  group.isStoryVisible ? 'Stories visible' : 'Stories hidden',
                  style: AppTheme.fontStyles.caption.copyWith(
                    color: group.isStoryVisible ? Colors.green : Colors.orange,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          icon: FaIcon(
            FontAwesomeIcons.ellipsisVertical,
            color: AppTheme.secondaryAccentColor,
            size: 16,
          ),
          color: AppTheme.luxuryGrey,
          onSelected: (value) => _handleGroupAction(value, group),
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  const FaIcon(FontAwesomeIcons.penToSquare, size: 16),
                  const SizedBox(width: 8),
                  Text(
                    'Edit Group',
                    style: TextStyle(color: AppTheme.luxuryWhite),
                  ),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'members',
              child: Row(
                children: [
                  const FaIcon(FontAwesomeIcons.users, size: 16),
                  const SizedBox(width: 8),
                  Text(
                    'Manage Members',
                    style: TextStyle(color: AppTheme.luxuryWhite),
                  ),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  const FaIcon(
                    FontAwesomeIcons.trash,
                    size: 16,
                    color: Colors.red,
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'Delete Group',
                    style: TextStyle(color: Colors.red),
                  ),
                ],
              ),
            ),
          ],
        ),
        onTap: () => _showGroupDetails(group),
      ),
    );
  }

  Widget _buildEmptyGroupsState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: AppTheme.accentColor.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: FaIcon(
              FontAwesomeIcons.layerGroup,
              size: 48,
              color: AppTheme.accentColor.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'No Groups Yet',
            style: AppTheme.fontStyles.title.copyWith(
              color: AppTheme.luxuryWhite,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              'Create groups to organize your close friends and control who sees your stories',
              style: AppTheme.fontStyles.body.copyWith(
                color: AppTheme.secondaryAccentColor.withValues(alpha: 0.8),
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  void _showCreateGroupDialog() {
    showDialog(context: context, builder: (context) => _CreateGroupDialog());
  }

  void _showGroupDetails(CloseFriendsGroup group) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _GroupDetailsSheet(group: group),
    );
  }

  void _showEditGroupDialog(CloseFriendsGroup group) {
    showDialog(
      context: context,
      builder: (context) => _EditGroupDialog(group: group),
    );
  }

  void _handleGroupAction(String action, CloseFriendsGroup group) {
    switch (action) {
      case 'edit':
        _showEditGroupDialog(group);
        break;
      case 'members':
        _showGroupDetails(group);
        break;
      case 'delete':
        _showDeleteGroupConfirmation(group);
        break;
    }
  }

  void _showDeleteGroupConfirmation(CloseFriendsGroup group) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.luxuryBlack,
        title: Text(
          'Delete Group?',
          style: AppTheme.fontStyles.title.copyWith(
            color: AppTheme.luxuryWhite,
          ),
        ),
        content: Text(
          'Are you sure you want to delete "${group.name}"? This action cannot be undone.',
          style: AppTheme.fontStyles.body.copyWith(
            color: AppTheme.secondaryAccentColor,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: TextStyle(color: AppTheme.secondaryAccentColor),
            ),
          ),
          TextButton(
            onPressed: () async {
              final navigator = Navigator.of(context);
              final messenger = ScaffoldMessenger.of(context);

              navigator.pop();
              await ref
                  .read(closeFriendsGroupsNotifierProvider.notifier)
                  .deleteGroup(group.id);

              if (mounted) {
                messenger.showSnackBar(
                  SnackBar(
                    content: Text('Deleted group "${group.name}"'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}

/// Dialog for creating a new close friends group
class _CreateGroupDialog extends ConsumerStatefulWidget {
  @override
  ConsumerState<_CreateGroupDialog> createState() => _CreateGroupDialogState();
}

class _CreateGroupDialogState extends ConsumerState<_CreateGroupDialog> {
  final TextEditingController _nameController = TextEditingController();
  String _selectedEmoji = '⭐️';
  bool _isStoryVisible = true;
  bool _isLoading = false;

  final List<String> _availableEmojis = [
    '⭐️',
    '❤️',
    '🔥',
    '💎',
    '👑',
    '🎯',
    '🚀',
    '💫',
    '🌟',
    '✨',
    '🎉',
    '🎊',
    '🏆',
    '🥇',
    '💰',
    '💸',
    '🎭',
    '🎨',
    '🎪',
    '🎬',
    '🏖️',
    '🏝️',
    '✈️',
    '🛥️',
    '🏎️',
    '🏰',
    '🗽',
    '🎡',
    '🎢',
    '🎠',
  ];

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: AppTheme.luxuryBlack,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      title: Text(
        'Create New Group',
        style: AppTheme.fontStyles.title.copyWith(
          color: AppTheme.luxuryWhite,
          fontWeight: FontWeight.bold,
        ),
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Group name input
            TextField(
              controller: _nameController,
              style: AppTheme.fontStyles.body.copyWith(
                color: AppTheme.luxuryWhite,
              ),
              decoration: InputDecoration(
                labelText: 'Group Name',
                labelStyle: AppTheme.fontStyles.body.copyWith(
                  color: AppTheme.secondaryAccentColor,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: AppTheme.luxuryGrey),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: AppTheme.accentColor),
                ),
                filled: true,
                fillColor: AppTheme.luxuryGrey.withValues(alpha: 0.1),
              ),
            ),

            const SizedBox(height: 20),

            // Emoji selection
            Text(
              'Choose Emoji',
              style: AppTheme.fontStyles.bodyBold.copyWith(
                color: AppTheme.luxuryWhite,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              height: 120,
              decoration: BoxDecoration(
                color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppTheme.luxuryGrey),
              ),
              child: GridView.builder(
                padding: const EdgeInsets.all(8),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 6,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                ),
                itemCount: _availableEmojis.length,
                itemBuilder: (context, index) {
                  final emoji = _availableEmojis[index];
                  final isSelected = emoji == _selectedEmoji;

                  return GestureDetector(
                    onTap: () => setState(() => _selectedEmoji = emoji),
                    child: Container(
                      decoration: BoxDecoration(
                        color: isSelected
                            ? AppTheme.accentColor.withValues(alpha: 0.2)
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(8),
                        border: isSelected
                            ? Border.all(color: AppTheme.accentColor)
                            : null,
                      ),
                      child: Center(
                        child: Text(
                          emoji,
                          style: const TextStyle(fontSize: 24),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),

            const SizedBox(height: 20),

            // Story visibility toggle
            Row(
              children: [
                Switch(
                  value: _isStoryVisible,
                  onChanged: (value) => setState(() => _isStoryVisible = value),
                  activeColor: AppTheme.accentColor,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Story Visibility',
                        style: AppTheme.fontStyles.bodyBold.copyWith(
                          color: AppTheme.luxuryWhite,
                        ),
                      ),
                      Text(
                        _isStoryVisible
                            ? 'Group members can see your stories'
                            : 'Stories hidden from this group',
                        style: AppTheme.fontStyles.caption.copyWith(
                          color: AppTheme.secondaryAccentColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.pop(context),
          child: Text(
            'Cancel',
            style: TextStyle(color: AppTheme.secondaryAccentColor),
          ),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _createGroup,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.accentColor,
            foregroundColor: Colors.white,
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text('Create'),
        ),
      ],
    );
  }

  Future<void> _createGroup() async {
    if (_nameController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a group name'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      await ref
          .read(closeFriendsGroupsNotifierProvider.notifier)
          .createGroup(
            name: _nameController.text.trim(),
            emoji: _selectedEmoji,
          );

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Created group "${_nameController.text.trim()}"'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to create group: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}

/// Bottom sheet for viewing and managing group details
class _GroupDetailsSheet extends ConsumerStatefulWidget {
  final CloseFriendsGroup group;

  const _GroupDetailsSheet({required this.group});

  @override
  ConsumerState<_GroupDetailsSheet> createState() => _GroupDetailsSheetState();
}

class _GroupDetailsSheetState extends ConsumerState<_GroupDetailsSheet> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text.toLowerCase();
      });
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: BoxDecoration(
        color: AppTheme.luxuryBlack,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppTheme.luxuryGrey,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppTheme.accentColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    widget.group.emoji,
                    style: const TextStyle(fontSize: 24),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.group.name,
                        style: AppTheme.fontStyles.title.copyWith(
                          color: AppTheme.luxuryWhite,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${widget.group.memberIds.length} members',
                        style: AppTheme.fontStyles.caption.copyWith(
                          color: AppTheme.secondaryAccentColor,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const FaIcon(
                    FontAwesomeIcons.xmark,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),

          // Story visibility status
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 20),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: widget.group.isStoryVisible
                  ? Colors.green.withValues(alpha: 0.1)
                  : Colors.orange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: widget.group.isStoryVisible
                    ? Colors.green.withValues(alpha: 0.3)
                    : Colors.orange.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                FaIcon(
                  widget.group.isStoryVisible
                      ? FontAwesomeIcons.eye
                      : FontAwesomeIcons.eyeSlash,
                  size: 16,
                  color: widget.group.isStoryVisible
                      ? Colors.green
                      : Colors.orange,
                ),
                const SizedBox(width: 8),
                Text(
                  widget.group.isStoryVisible
                      ? 'Stories visible to this group'
                      : 'Stories hidden from this group',
                  style: AppTheme.fontStyles.caption.copyWith(
                    color: widget.group.isStoryVisible
                        ? Colors.green
                        : Colors.orange,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // Search bar
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: TextField(
              controller: _searchController,
              style: AppTheme.fontStyles.body.copyWith(
                color: AppTheme.luxuryWhite,
              ),
              decoration: InputDecoration(
                hintText: 'Search members...',
                hintStyle: AppTheme.fontStyles.body.copyWith(
                  color: AppTheme.secondaryAccentColor.withValues(alpha: 0.7),
                ),
                prefixIcon: FaIcon(
                  FontAwesomeIcons.magnifyingGlass,
                  color: AppTheme.secondaryAccentColor,
                  size: 16,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: AppTheme.luxuryGrey),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: AppTheme.accentColor),
                ),
                filled: true,
                fillColor: AppTheme.luxuryGrey.withValues(alpha: 0.1),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Members list
          Expanded(child: _buildMembersList()),

          // Action buttons
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      // TODO: Implement add members functionality
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text(
                            'Add members functionality coming soon',
                          ),
                        ),
                      );
                    },
                    icon: const FaIcon(FontAwesomeIcons.userPlus, size: 16),
                    label: const Text('Add Members'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.accentColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () {
                      // TODO: Implement edit group functionality
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Edit group functionality coming soon'),
                        ),
                      );
                    },
                    icon: const FaIcon(FontAwesomeIcons.penToSquare, size: 16),
                    label: const Text('Edit Group'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: AppTheme.accentColor,
                      side: BorderSide(color: AppTheme.accentColor),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMembersList() {
    if (widget.group.memberIds.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: AppTheme.accentColor.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: FaIcon(
                FontAwesomeIcons.userGroup,
                size: 48,
                color: AppTheme.accentColor.withValues(alpha: 0.7),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'No Members Yet',
              style: AppTheme.fontStyles.title.copyWith(
                color: AppTheme.luxuryWhite,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'Add friends to this group to share stories with them',
              style: AppTheme.fontStyles.body.copyWith(
                color: AppTheme.secondaryAccentColor.withValues(alpha: 0.8),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    // Filter members based on search query
    final filteredMemberIds = _searchQuery.isEmpty
        ? widget.group.memberIds
        : widget.group.memberIds.where((memberId) {
            // TODO: In a real implementation, you would fetch user data and filter by name/username
            // For now, we'll just show all members when searching since we don't have user data
            return true;
          }).toList();

    if (filteredMemberIds.isEmpty && _searchQuery.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: AppTheme.secondaryAccentColor.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: FaIcon(
                FontAwesomeIcons.magnifyingGlass,
                size: 48,
                color: AppTheme.secondaryAccentColor.withValues(alpha: 0.7),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'No Results Found',
              style: AppTheme.fontStyles.title.copyWith(
                color: AppTheme.luxuryWhite,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'No members found matching "$_searchQuery"',
              style: AppTheme.fontStyles.body.copyWith(
                color: AppTheme.secondaryAccentColor.withValues(alpha: 0.8),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      itemCount: filteredMemberIds.length,
      itemBuilder: (context, index) {
        final memberId = filteredMemberIds[index];
        return _buildMemberTile(memberId);
      },
    );
  }

  Widget _buildMemberTile(String memberId) {
    // TODO: Fetch actual user data for memberId
    // For now, show placeholder
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundColor: AppTheme.accentColor.withValues(alpha: 0.3),
            child: Text(
              memberId.substring(0, 1).toUpperCase(),
              style: AppTheme.fontStyles.bodyBold.copyWith(
                color: AppTheme.luxuryWhite,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'User $memberId', // TODO: Replace with actual user name
                  style: AppTheme.fontStyles.bodyBold.copyWith(
                    color: AppTheme.luxuryWhite,
                  ),
                ),
                Text(
                  '@user_${memberId.substring(0, 8)}', // TODO: Replace with actual username
                  style: AppTheme.fontStyles.caption.copyWith(
                    color: AppTheme.secondaryAccentColor,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => _removeMember(memberId),
            icon: const FaIcon(
              FontAwesomeIcons.xmark,
              size: 16,
              color: Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _removeMember(String memberId) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.luxuryBlack,
        title: Text(
          'Remove Member?',
          style: AppTheme.fontStyles.title.copyWith(
            color: AppTheme.luxuryWhite,
          ),
        ),
        content: Text(
          'Are you sure you want to remove this member from the group?',
          style: AppTheme.fontStyles.body.copyWith(
            color: AppTheme.secondaryAccentColor,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(
              'Cancel',
              style: TextStyle(color: AppTheme.secondaryAccentColor),
            ),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Remove', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await ref
            .read(closeFriendsGroupsNotifierProvider.notifier)
            .removeMember(widget.group.id, memberId);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Member removed from group'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to remove member: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}

/// Dialog for editing an existing close friends group
class _EditGroupDialog extends ConsumerStatefulWidget {
  final CloseFriendsGroup group;

  const _EditGroupDialog({required this.group});

  @override
  ConsumerState<_EditGroupDialog> createState() => _EditGroupDialogState();
}

class _EditGroupDialogState extends ConsumerState<_EditGroupDialog> {
  late TextEditingController _nameController;
  late String _selectedEmoji;
  late bool _isStoryVisible;
  bool _isLoading = false;

  final List<String> _availableEmojis = [
    '⭐️',
    '❤️',
    '🔥',
    '💎',
    '👑',
    '🎯',
    '🚀',
    '💫',
    '🌟',
    '✨',
    '🎉',
    '🎊',
    '🏆',
    '🥇',
    '💰',
    '💸',
    '🎭',
    '🎨',
    '🎪',
    '🎬',
    '🏖️',
    '🏝️',
    '✈️',
    '🛥️',
    '🏎️',
    '🏰',
    '🗽',
    '🎡',
    '🎢',
    '🎠',
  ];

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.group.name);
    _selectedEmoji = widget.group.emoji;
    _isStoryVisible = widget.group.isStoryVisible;
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: AppTheme.luxuryBlack,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      title: Text(
        'Edit Group',
        style: AppTheme.fontStyles.title.copyWith(
          color: AppTheme.luxuryWhite,
          fontWeight: FontWeight.bold,
        ),
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Group name input
            TextField(
              controller: _nameController,
              style: AppTheme.fontStyles.body.copyWith(
                color: AppTheme.luxuryWhite,
              ),
              decoration: InputDecoration(
                labelText: 'Group Name',
                labelStyle: AppTheme.fontStyles.body.copyWith(
                  color: AppTheme.secondaryAccentColor,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: AppTheme.luxuryGrey),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: AppTheme.accentColor),
                ),
                filled: true,
                fillColor: AppTheme.luxuryGrey.withValues(alpha: 0.1),
              ),
            ),

            const SizedBox(height: 20),

            // Emoji selection
            Text(
              'Choose Emoji',
              style: AppTheme.fontStyles.bodyBold.copyWith(
                color: AppTheme.luxuryWhite,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              height: 120,
              decoration: BoxDecoration(
                color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppTheme.luxuryGrey),
              ),
              child: GridView.builder(
                padding: const EdgeInsets.all(8),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 6,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                ),
                itemCount: _availableEmojis.length,
                itemBuilder: (context, index) {
                  final emoji = _availableEmojis[index];
                  final isSelected = emoji == _selectedEmoji;

                  return GestureDetector(
                    onTap: () => setState(() => _selectedEmoji = emoji),
                    child: Container(
                      decoration: BoxDecoration(
                        color: isSelected
                            ? AppTheme.accentColor.withValues(alpha: 0.2)
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(8),
                        border: isSelected
                            ? Border.all(color: AppTheme.accentColor)
                            : null,
                      ),
                      child: Center(
                        child: Text(
                          emoji,
                          style: const TextStyle(fontSize: 24),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),

            const SizedBox(height: 20),

            // Story visibility toggle
            Row(
              children: [
                Switch(
                  value: _isStoryVisible,
                  onChanged: (value) => setState(() => _isStoryVisible = value),
                  activeColor: AppTheme.accentColor,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Story Visibility',
                        style: AppTheme.fontStyles.bodyBold.copyWith(
                          color: AppTheme.luxuryWhite,
                        ),
                      ),
                      Text(
                        _isStoryVisible
                            ? 'Group members can see your stories'
                            : 'Stories hidden from this group',
                        style: AppTheme.fontStyles.caption.copyWith(
                          color: AppTheme.secondaryAccentColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.pop(context),
          child: Text(
            'Cancel',
            style: TextStyle(color: AppTheme.secondaryAccentColor),
          ),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _updateGroup,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.accentColor,
            foregroundColor: Colors.white,
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text('Update'),
        ),
      ],
    );
  }

  Future<void> _updateGroup() async {
    if (_nameController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a group name'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final updatedGroup = widget.group.copyWith(
        name: _nameController.text.trim(),
        emoji: _selectedEmoji,
        isStoryVisible: _isStoryVisible,
        updatedAt: DateTime.now(),
      );

      await ref
          .read(closeFriendsGroupsNotifierProvider.notifier)
          .updateGroup(updatedGroup);

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Updated group "${_nameController.text.trim()}"'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update group: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
