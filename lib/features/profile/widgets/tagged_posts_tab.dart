import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:billionaires_social/features/profile/providers/tagged_posts_provider.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/core/widgets/error_display_widget.dart';
import 'package:billionaires_social/features/feed/screens/post_detail_screen.dart';
import 'package:cached_network_image/cached_network_image.dart';

class TaggedPostsTab extends ConsumerStatefulWidget {
  final String userId;
  final bool isCurrentUser;

  const TaggedPostsTab({
    super.key,
    required this.userId,
    required this.isCurrentUser,
  });

  @override
  ConsumerState<TaggedPostsTab> createState() => _TaggedPostsTabState();
}

class _TaggedPostsTabState extends ConsumerState<TaggedPostsTab>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);

    final taggedPostsAsync = ref.watch(taggedPostsProvider(widget.userId));

    return taggedPostsAsync.when(
      data: (posts) {
        if (posts.isEmpty) {
          return _buildEmptyState();
        }

        return RefreshIndicator(
          onRefresh: () async {
            ref.invalidate(taggedPostsProvider(widget.userId));
          },
          child: CustomScrollView(
            slivers: [
              if (widget.isCurrentUser) _buildInfoHeader(),
              SliverPadding(
                padding: const EdgeInsets.all(8.0),
                sliver: SliverGrid(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 3,
                    crossAxisSpacing: 2,
                    mainAxisSpacing: 2,
                    childAspectRatio: 1.0,
                  ),
                  delegate: SliverChildBuilderDelegate((context, index) {
                    final post = posts[index];
                    return _buildPostThumbnail(post);
                  }, childCount: posts.length),
                ),
              ),
            ],
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => ErrorDisplayWidget(
        error: error,
        context: 'Loading tagged posts',
        onRetry: () {
          ref.invalidate(taggedPostsProvider(widget.userId));
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: AppTheme.accentColor.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: FaIcon(
              FontAwesomeIcons.userTag,
              size: 48,
              color: AppTheme.accentColor.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 24),
          Text(
            widget.isCurrentUser ? 'No Tagged Posts Yet' : 'No Tagged Posts',
            style: AppTheme.fontStyles.title.copyWith(
              color: AppTheme.luxuryWhite,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              widget.isCurrentUser
                  ? 'When people tag you in their posts, they\'ll appear here'
                  : 'Posts where this user is tagged will appear here',
              style: AppTheme.fontStyles.body.copyWith(
                color: AppTheme.secondaryAccentColor.withValues(alpha: 0.8),
              ),
              textAlign: TextAlign.center,
            ),
          ),
          if (widget.isCurrentUser) ...[
            const SizedBox(height: 24),
            Container(
              padding: const EdgeInsets.all(16),
              margin: const EdgeInsets.symmetric(horizontal: 32),
              decoration: BoxDecoration(
                color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppTheme.accentColor.withValues(alpha: 0.3),
                ),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      FaIcon(
                        FontAwesomeIcons.lightbulb,
                        size: 16,
                        color: AppTheme.accentColor,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Privacy Tip',
                        style: AppTheme.fontStyles.bodyBold.copyWith(
                          color: AppTheme.accentColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'You can control who can tag you in posts through your privacy settings.',
                    style: AppTheme.fontStyles.caption.copyWith(
                      color: AppTheme.secondaryAccentColor.withValues(
                        alpha: 0.9,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoHeader() {
    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.all(16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppTheme.accentColor.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppTheme.accentColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: FaIcon(
                FontAwesomeIcons.userTag,
                size: 20,
                color: AppTheme.accentColor,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Tagged Posts',
                    style: AppTheme.fontStyles.bodyBold.copyWith(
                      color: AppTheme.luxuryWhite,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Posts where you\'re tagged or mentioned',
                    style: AppTheme.fontStyles.caption.copyWith(
                      color: AppTheme.secondaryAccentColor.withValues(
                        alpha: 0.8,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            if (widget.isCurrentUser)
              IconButton(
                onPressed: () => _showTaggedPostsInfo(),
                icon: FaIcon(
                  FontAwesomeIcons.circleInfo,
                  size: 16,
                  color: AppTheme.accentColor.withValues(alpha: 0.7),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPostThumbnail(Post post) {
    return GestureDetector(
      onTap: () => _navigateToPostDetail(post),
      onLongPress: widget.isCurrentUser ? () => _showPostOptions(post) : null,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: AppTheme.accentColor.withValues(alpha: 0.2),
            width: 0.5,
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(7.5),
          child: Stack(
            fit: StackFit.expand,
            children: [
              if (post.mediaType == MediaType.image)
                CachedNetworkImage(
                  imageUrl: post.mediaUrl,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Container(
                    color: AppTheme.luxuryGrey.withValues(alpha: 0.3),
                    child: const Center(
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                  ),
                  errorWidget: (context, url, error) => Container(
                    color: AppTheme.luxuryGrey.withValues(alpha: 0.3),
                    child: const Center(
                      child: Icon(Icons.error, color: Colors.red),
                    ),
                  ),
                )
              else if (post.mediaType == MediaType.video)
                Stack(
                  fit: StackFit.expand,
                  children: [
                    CachedNetworkImage(
                      imageUrl: post.mediaUrl,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        color: AppTheme.luxuryGrey.withValues(alpha: 0.3),
                        child: const Center(
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                      ),
                      errorWidget: (context, url, error) => Container(
                        color: AppTheme.luxuryGrey.withValues(alpha: 0.3),
                        child: const Center(
                          child: Icon(Icons.error, color: Colors.red),
                        ),
                      ),
                    ),
                    const Center(
                      child: Icon(
                        Icons.play_circle_filled,
                        color: Colors.white,
                        size: 32,
                      ),
                    ),
                  ],
                )
              else
                Container(
                  color: AppTheme.luxuryGrey.withValues(alpha: 0.3),
                  child: Center(
                    child: Padding(
                      padding: const EdgeInsets.all(8),
                      child: Text(
                        post.caption,
                        style: AppTheme.fontStyles.caption.copyWith(
                          color: AppTheme.luxuryWhite,
                        ),
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),

              // Tag indicator
              Positioned(
                top: 4,
                right: 4,
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const FaIcon(
                    FontAwesomeIcons.userTag,
                    size: 12,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToPostDetail(Post post) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PostDetailScreen(postId: post.id),
      ),
    );
  }

  void _showPostOptions(Post post) {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppTheme.luxuryBlack,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppTheme.luxuryGrey,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            ListTile(
              leading: const FaIcon(
                FontAwesomeIcons.userMinus,
                color: Colors.red,
              ),
              title: const Text(
                'Remove Tag',
                style: TextStyle(color: Colors.red),
              ),
              subtitle: const Text(
                'Remove yourself from this post',
                style: TextStyle(color: Colors.grey),
              ),
              onTap: () {
                Navigator.pop(context);
                _removeFromTaggedPost(post);
              },
            ),
            ListTile(
              leading: FaIcon(
                FontAwesomeIcons.flag,
                color: AppTheme.secondaryAccentColor,
              ),
              title: Text(
                'Report Post',
                style: TextStyle(color: AppTheme.secondaryAccentColor),
              ),
              subtitle: const Text(
                'Report inappropriate content',
                style: TextStyle(color: Colors.grey),
              ),
              onTap: () {
                Navigator.pop(context);
                // TODO: Implement report functionality
              },
            ),
          ],
        ),
      ),
    );
  }

  void _removeFromTaggedPost(Post post) {
    ref
        .read(taggedPostsNotifierProvider(widget.userId).notifier)
        .removeFromTaggedPost(post.id);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Removed from tagged post'),
        backgroundColor: AppTheme.accentColor,
      ),
    );
  }

  void _showTaggedPostsInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.luxuryBlack,
        title: Text(
          'Tagged Posts',
          style: AppTheme.fontStyles.title.copyWith(
            color: AppTheme.luxuryWhite,
          ),
        ),
        content: Text(
          'This section shows posts where you\'ve been tagged or mentioned by other users. You can remove yourself from any tagged post by long-pressing on it.',
          style: AppTheme.fontStyles.body.copyWith(
            color: AppTheme.secondaryAccentColor,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Got it',
              style: TextStyle(color: AppTheme.accentColor),
            ),
          ),
        ],
      ),
    );
  }
}
