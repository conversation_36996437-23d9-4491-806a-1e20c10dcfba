import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:billionaires_social/features/profile/models/profile_model.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/features/profile/providers/profile_provider.dart';

import 'package:billionaires_social/features/profile/widgets/enhanced_loading_animations.dart';
import 'package:billionaires_social/core/app_theme.dart';

/// Enhanced archived posts tab with management features
class ArchivedPostsTab extends ConsumerStatefulWidget {
  final ProfileModel userProfile;

  const ArchivedPostsTab({super.key, required this.userProfile});

  @override
  ConsumerState<ArchivedPostsTab> createState() => _ArchivedPostsTabState();
}

class _ArchivedPostsTabState extends ConsumerState<ArchivedPostsTab> {
  bool _isSelectionMode = false;
  final Set<String> _selectedPostIds = {};
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();
  ArchivedPostsFilter _currentFilter = ArchivedPostsFilter.all;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final archivedPostsAsync = ref.watch(
      userPostsStreamProvider(widget.userProfile.id),
    );

    return archivedPostsAsync.when(
      data: (posts) => _buildArchivedPostsContent(posts),
      loading: () => EnhancedLoadingAnimations.archivedPostsSkeleton(),
      error: (error, stack) => _buildErrorState(error),
    );
  }

  Widget _buildArchivedPostsContent(List<Post> allPosts) {
    final archivedPosts = allPosts.where((post) => post.isArchived).toList();
    final filteredPosts = _filterPosts(archivedPosts);

    if (archivedPosts.isEmpty) {
      return _buildEnhancedEmptyState();
    }

    return Column(
      children: [
        // Search and filter bar
        _buildSearchAndFilterBar(archivedPosts.length),

        // Selection mode header
        if (_isSelectionMode) _buildSelectionHeader(),

        // Posts grid or list
        Expanded(
          child: filteredPosts.isEmpty && _searchQuery.isNotEmpty
              ? _buildNoSearchResultsState()
              : _buildPostsGrid(filteredPosts),
        ),

        // Bulk action bar
        if (_isSelectionMode && _selectedPostIds.isNotEmpty)
          _buildBulkActionBar(),
      ],
    );
  }

  Widget _buildSearchAndFilterBar(int totalCount) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.luxuryBlack,
        border: Border(
          bottom: BorderSide(color: AppTheme.luxuryGrey.withValues(alpha: 0.3)),
        ),
      ),
      child: Column(
        children: [
          // Search bar
          TextField(
            controller: _searchController,
            style: AppTheme.fontStyles.body.copyWith(
              color: AppTheme.luxuryWhite,
            ),
            decoration: InputDecoration(
              hintText: 'Search archived posts...',
              hintStyle: AppTheme.fontStyles.body.copyWith(
                color: Colors.black54,
              ),
              prefixIcon: FaIcon(
                FontAwesomeIcons.magnifyingGlass,
                color: AppTheme.secondaryAccentColor,
                size: 16,
              ),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      onPressed: () {
                        _searchController.clear();
                        setState(() => _searchQuery = '');
                      },
                      icon: FaIcon(
                        FontAwesomeIcons.xmark,
                        color: AppTheme.secondaryAccentColor,
                        size: 16,
                      ),
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppTheme.luxuryGrey),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppTheme.accentColor),
              ),
              filled: true,
              fillColor: AppTheme.luxuryGrey.withValues(alpha: 0.1),
            ),
            onChanged: (value) =>
                setState(() => _searchQuery = value.toLowerCase()),
          ),

          const SizedBox(height: 12),

          // Filter and action buttons
          Row(
            children: [
              // Filter dropdown
              Expanded(child: _buildFilterDropdown()),

              const SizedBox(width: 12),

              // Selection mode toggle
              TextButton.icon(
                onPressed: _toggleSelectionMode,
                icon: FaIcon(
                  _isSelectionMode
                      ? FontAwesomeIcons.xmark
                      : FontAwesomeIcons.squareCheck,
                  size: 16,
                  color: _isSelectionMode ? Colors.red : AppTheme.accentColor,
                ),
                label: Text(
                  _isSelectionMode ? 'Cancel' : 'Select',
                  style: AppTheme.fontStyles.bodyBold.copyWith(
                    color: _isSelectionMode ? Colors.red : AppTheme.accentColor,
                  ),
                ),
                style: TextButton.styleFrom(
                  backgroundColor:
                      (_isSelectionMode ? Colors.red : AppTheme.accentColor)
                          .withValues(alpha: 0.1),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),

              const SizedBox(width: 8),

              // Archive count
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: AppTheme.luxuryGrey.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '$totalCount archived',
                  style: AppTheme.fontStyles.caption.copyWith(
                    color: Colors.black87,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterDropdown() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppTheme.luxuryGrey),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<ArchivedPostsFilter>(
          value: _currentFilter,
          dropdownColor: AppTheme.luxuryBlack,
          icon: FaIcon(
            FontAwesomeIcons.chevronDown,
            color: AppTheme.secondaryAccentColor,
            size: 12,
          ),
          items: ArchivedPostsFilter.values.map((filter) {
            return DropdownMenuItem(
              value: filter,
              child: Row(
                children: [
                  FaIcon(
                    _getFilterIcon(filter),
                    color: AppTheme.accentColor,
                    size: 14,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _getFilterLabel(filter),
                    style: AppTheme.fontStyles.body.copyWith(
                      color: AppTheme.luxuryWhite,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
          onChanged: (filter) {
            if (filter != null) {
              setState(() => _currentFilter = filter);
            }
          },
        ),
      ),
    );
  }

  Widget _buildSelectionHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: AppTheme.accentColor.withValues(alpha: 0.1),
        border: Border(
          bottom: BorderSide(
            color: AppTheme.accentColor.withValues(alpha: 0.3),
          ),
        ),
      ),
      child: Row(
        children: [
          FaIcon(
            FontAwesomeIcons.squareCheck,
            color: AppTheme.accentColor,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            '${_selectedPostIds.length} selected',
            style: AppTheme.fontStyles.bodyBold.copyWith(
              color: AppTheme.accentColor,
            ),
          ),
          const Spacer(),
          TextButton(
            onPressed: _selectAll,
            child: Text(
              'Select All',
              style: AppTheme.fontStyles.body.copyWith(
                color: AppTheme.accentColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPostsGrid(List<Post> posts) {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 2,
        mainAxisSpacing: 2,
        childAspectRatio: 1,
      ),
      itemCount: posts.length,
      itemBuilder: (context, index) {
        final post = posts[index];
        return _buildArchivedPostTile(post);
      },
    );
  }

  Widget _buildArchivedPostTile(Post post) {
    final isSelected = _selectedPostIds.contains(post.id);

    return GestureDetector(
      onTap: () => _handlePostTap(post),
      onLongPress: () => _handlePostLongPress(post),
      child: Stack(
        children: [
          // Post image/content
          Container(
            decoration: BoxDecoration(
              color: AppTheme.luxuryGrey.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(8),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Stack(
                fit: StackFit.expand,
                children: [
                  // Post content (placeholder for now)
                  Container(
                    color: AppTheme.luxuryGrey.withValues(alpha: 0.5),
                    child: Center(
                      child: FaIcon(
                        FontAwesomeIcons.image,
                        color: Colors.black54,
                        size: 24,
                      ),
                    ),
                  ),

                  // Archive overlay
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topRight,
                        end: Alignment.center,
                        colors: [
                          Colors.black.withValues(alpha: 0.7),
                          Colors.transparent,
                        ],
                      ),
                    ),
                  ),

                  // Archive icon
                  Positioned(
                    top: 4,
                    right: 4,
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.7),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: FaIcon(
                        FontAwesomeIcons.boxArchive,
                        color: AppTheme.accentColor,
                        size: 12,
                      ),
                    ),
                  ),

                  // Selection overlay
                  if (_isSelectionMode)
                    Container(
                      decoration: BoxDecoration(
                        color: isSelected
                            ? AppTheme.accentColor.withValues(alpha: 0.3)
                            : Colors.black.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Center(
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: isSelected
                                ? AppTheme.accentColor
                                : Colors.transparent,
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: isSelected
                                  ? AppTheme.accentColor
                                  : AppTheme.luxuryWhite,
                              width: 2,
                            ),
                          ),
                          child: isSelected
                              ? FaIcon(
                                  FontAwesomeIcons.check,
                                  color: Colors.white,
                                  size: 16,
                                )
                              : null,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBulkActionBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.luxuryBlack,
        border: Border(
          top: BorderSide(color: AppTheme.luxuryGrey.withValues(alpha: 0.3)),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _restoreSelectedPosts,
              icon: const FaIcon(FontAwesomeIcons.arrowRotateLeft, size: 16),
              label: Text('Restore (${_selectedPostIds.length})'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _deleteSelectedPosts,
              icon: const FaIcon(FontAwesomeIcons.trash, size: 16),
              label: Text('Delete (${_selectedPostIds.length})'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedEmptyState() {
    return Center(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Animated archive icon
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: AppTheme.accentColor.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: FaIcon(
                FontAwesomeIcons.boxArchive,
                size: 48,
                color: AppTheme.accentColor,
              ),
            ),

            const SizedBox(height: 24),

            Text(
              'No Archived Posts',
              style: AppTheme.fontStyles.title.copyWith(
                color: Colors.black87,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 12),

            Text(
              'Archive posts to hide them from your profile while keeping them safe',
              style: AppTheme.fontStyles.body.copyWith(
                color: Colors.black87,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 32),

            // How archiving works
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppTheme.luxuryGrey.withValues(alpha: 0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      FaIcon(
                        FontAwesomeIcons.circleInfo,
                        color: AppTheme.accentColor,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'How Archiving Works',
                        style: AppTheme.fontStyles.bodyBold.copyWith(
                          color: Colors.black87,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  ...[
                    'Archive posts to hide them from your profile',
                    'Only you can see your archived posts',
                    'Restore archived posts anytime to your profile',
                    'Permanently delete posts you no longer need',
                  ].map(
                    (tip) => Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          FaIcon(
                            FontAwesomeIcons.circleCheck,
                            color: AppTheme.accentColor,
                            size: 12,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              tip,
                              style: AppTheme.fontStyles.caption.copyWith(
                                color: Colors.black87,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Action button - Navigate to posts tab to see existing posts
            ElevatedButton.icon(
              onPressed: () {
                // Navigate back to posts tab to see existing posts
                DefaultTabController.of(context).animateTo(0);
              },
              icon: const FaIcon(FontAwesomeIcons.images, size: 16),
              label: const Text('View Your Posts'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.accentColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoSearchResultsState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: AppTheme.secondaryAccentColor.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: FaIcon(
              FontAwesomeIcons.magnifyingGlass,
              size: 48,
              color: Colors.black54,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'No Results Found',
            style: AppTheme.fontStyles.title.copyWith(
              color: Colors.black87,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'No archived posts found matching "$_searchQuery"',
            style: AppTheme.fontStyles.body.copyWith(
              color: Colors.black87,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          TextButton(
            onPressed: () {
              _searchController.clear();
              setState(() => _searchQuery = '');
            },
            child: Text(
              'Clear Search',
              style: AppTheme.fontStyles.bodyBold.copyWith(
                color: AppTheme.accentColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.red.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: const FaIcon(
              FontAwesomeIcons.triangleExclamation,
              size: 48,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'Error Loading Archived Posts',
            style: AppTheme.fontStyles.title.copyWith(
              color: Colors.red,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            error.toString(),
            style: AppTheme.fontStyles.body.copyWith(
              color: Colors.black87,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              ref.invalidate(userPostsStreamProvider(widget.userProfile.id));
            },
            icon: const FaIcon(FontAwesomeIcons.arrowRotateRight, size: 16),
            label: const Text('Retry'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.accentColor,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  List<Post> _filterPosts(List<Post> posts) {
    var filtered = posts;

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((post) {
        return post.caption.toLowerCase().contains(_searchQuery);
      }).toList();
    }

    // Apply category filter
    switch (_currentFilter) {
      case ArchivedPostsFilter.all:
        break;
      case ArchivedPostsFilter.recent:
        filtered.sort((a, b) => b.timestamp.compareTo(a.timestamp));
        filtered = filtered.take(20).toList();
        break;
      case ArchivedPostsFilter.older:
        final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
        filtered = filtered
            .where((post) => post.timestamp.isBefore(thirtyDaysAgo))
            .toList();
        break;
      case ArchivedPostsFilter.images:
        filtered = filtered
            .where((post) => post.mediaType == MediaType.image)
            .toList();
        break;
      case ArchivedPostsFilter.videos:
        filtered = filtered
            .where((post) => post.mediaType == MediaType.video)
            .toList();
        break;
    }

    return filtered;
  }

  void _handlePostTap(Post post) {
    if (_isSelectionMode) {
      _togglePostSelection(post.id);
    } else {
      // TODO: Open post detail view
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Opening archived post: ${post.id}'),
          backgroundColor: AppTheme.accentColor,
        ),
      );
    }
  }

  void _handlePostLongPress(Post post) {
    if (!_isSelectionMode) {
      setState(() {
        _isSelectionMode = true;
        _selectedPostIds.add(post.id);
      });
    }
  }

  void _togglePostSelection(String postId) {
    setState(() {
      if (_selectedPostIds.contains(postId)) {
        _selectedPostIds.remove(postId);
      } else {
        _selectedPostIds.add(postId);
      }
    });
  }

  void _toggleSelectionMode() {
    setState(() {
      _isSelectionMode = !_isSelectionMode;
      if (!_isSelectionMode) {
        _selectedPostIds.clear();
      }
    });
  }

  void _selectAll() {
    final archivedPostsAsync = ref.read(
      userPostsStreamProvider(widget.userProfile.id),
    );
    archivedPostsAsync.whenData((posts) {
      final archivedPosts = posts.where((post) => post.isArchived).toList();
      final filteredPosts = _filterPosts(archivedPosts);

      setState(() {
        _selectedPostIds.clear();
        _selectedPostIds.addAll(filteredPosts.map((post) => post.id));
      });
    });
  }

  void _restoreSelectedPosts() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.luxuryBlack,
        title: Text(
          'Restore Posts',
          style: AppTheme.fontStyles.title.copyWith(
            color: AppTheme.luxuryWhite,
          ),
        ),
        content: Text(
          'Are you sure you want to restore ${_selectedPostIds.length} post(s) to your profile? They will be visible to your followers again.',
          style: AppTheme.fontStyles.body.copyWith(
            color: AppTheme.secondaryAccentColor,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: AppTheme.fontStyles.body.copyWith(
                color: AppTheme.secondaryAccentColor,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _performRestoreAction();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('Restore'),
          ),
        ],
      ),
    );
  }

  void _deleteSelectedPosts() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.luxuryBlack,
        title: Text(
          'Delete Posts Permanently',
          style: AppTheme.fontStyles.title.copyWith(color: Colors.red),
        ),
        content: Text(
          'Are you sure you want to permanently delete ${_selectedPostIds.length} post(s)? This action cannot be undone.',
          style: AppTheme.fontStyles.body.copyWith(
            color: AppTheme.secondaryAccentColor,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: AppTheme.fontStyles.body.copyWith(
                color: AppTheme.secondaryAccentColor,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _performDeleteAction();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _performRestoreAction() async {
    try {
      // TODO: Implement actual restore functionality
      // This would call a service to unarchive the posts

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            '${_selectedPostIds.length} post(s) restored successfully',
          ),
          backgroundColor: Colors.green,
        ),
      );

      setState(() {
        _isSelectionMode = false;
        _selectedPostIds.clear();
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to restore posts: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _performDeleteAction() async {
    try {
      // TODO: Implement actual delete functionality
      // This would call a service to permanently delete the posts

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            '${_selectedPostIds.length} post(s) deleted permanently',
          ),
          backgroundColor: Colors.red,
        ),
      );

      setState(() {
        _isSelectionMode = false;
        _selectedPostIds.clear();
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to delete posts: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  IconData _getFilterIcon(ArchivedPostsFilter filter) {
    switch (filter) {
      case ArchivedPostsFilter.all:
        return FontAwesomeIcons.list;
      case ArchivedPostsFilter.recent:
        return FontAwesomeIcons.clock;
      case ArchivedPostsFilter.older:
        return FontAwesomeIcons.calendar;
      case ArchivedPostsFilter.images:
        return FontAwesomeIcons.image;
      case ArchivedPostsFilter.videos:
        return FontAwesomeIcons.video;
    }
  }

  String _getFilterLabel(ArchivedPostsFilter filter) {
    switch (filter) {
      case ArchivedPostsFilter.all:
        return 'All Posts';
      case ArchivedPostsFilter.recent:
        return 'Recent';
      case ArchivedPostsFilter.older:
        return 'Older';
      case ArchivedPostsFilter.images:
        return 'Images';
      case ArchivedPostsFilter.videos:
        return 'Videos';
    }
  }
}

/// Filter options for archived posts
enum ArchivedPostsFilter { all, recent, older, images, videos }
