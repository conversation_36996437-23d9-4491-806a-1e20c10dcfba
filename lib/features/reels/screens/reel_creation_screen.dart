import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:video_player/video_player.dart';
import 'package:image_picker/image_picker.dart';
import 'package:billionaires_social/features/feed/services/feed_service.dart';
import 'package:billionaires_social/features/feed/providers/feed_provider.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/core/services/firebase_service.dart';
import 'package:billionaires_social/features/stories/widgets/music_selector.dart';
import 'package:billionaires_social/features/stories/screens/story_creation_screen.dart';
import 'package:billionaires_social/core/main_navigation.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class ReelCreationScreen extends ConsumerStatefulWidget {
  final File? mediaFile;

  const ReelCreationScreen({super.key, this.mediaFile});

  @override
  ConsumerState<ReelCreationScreen> createState() => _ReelCreationScreenState();
}

class _ReelCreationScreenState extends ConsumerState<ReelCreationScreen> {
  final TextEditingController _captionController = TextEditingController();
  final TextEditingController _locationController = TextEditingController();
  VideoPlayerController? _videoController;
  bool _isLoading = false;
  bool _isVideoInitialized = false;
  String? _selectedMusicPath;
  String? _selectedMusicTitle;
  File? _currentMediaFile; // Track the current media file

  bool _turnOffCommenting = false;
  bool _hideLikeCount = false;
  bool _turnOffSharing = false;
  final List<String> _taggedUsers = [];
  String _privacySetting = 'public'; // public, followers, close_friends

  @override
  void initState() {
    super.initState();
    _currentMediaFile = widget.mediaFile;
    _initializeVideo();
  }

  @override
  void dispose() {
    _videoController?.dispose();
    _captionController.dispose();
    super.dispose();
  }

  Future<void> _initializeVideo() async {
    if (_currentMediaFile == null) {
      debugPrint('No media file provided to reel creation screen');
      return;
    }

    try {
      // Dispose previous controller if exists
      _videoController?.dispose();

      // Check if file exists before trying to initialize
      if (!await _currentMediaFile!.exists()) {
        throw Exception(
          'Video file does not exist: ${_currentMediaFile!.path}',
        );
      }

      _videoController = VideoPlayerController.file(_currentMediaFile!);
      await _videoController!.initialize();
      setState(() {
        _isVideoInitialized = true;
      });
      debugPrint('Video initialized successfully');
    } catch (e) {
      debugPrint('Error loading video: $e');
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error loading video: $e')));
      }
    }
  }

  Future<void> _createReel() async {
    if (_currentMediaFile == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select a video first'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (_captionController.text.trim().isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Please add a caption')));
      return;
    }

    setState(() => _isLoading = true);

    try {
      final firebaseService = ref.read(firebaseServiceProvider);
      final feedService = ref.read(feedServiceProvider);
      final currentUser = firebaseService.currentUser;

      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Verify the video file still exists
      if (!await _currentMediaFile!.exists()) {
        throw Exception(
          'Video file is no longer available. Please select a new video.',
        );
      }

      // Upload video to Firebase Storage
      final fileExtension = _currentMediaFile!.path.split('.').last;
      final fileName =
          'reels/${DateTime.now().millisecondsSinceEpoch}_${currentUser.uid}.$fileExtension';

      debugPrint('Uploading reel video: ${_currentMediaFile!.path}');
      final videoUrl = await firebaseService.uploadImage(
        _currentMediaFile!.path,
        fileName,
      );

      if (videoUrl.isEmpty) {
        throw Exception('Video upload failed - no URL returned');
      }

      debugPrint('Reel video uploaded successfully: $videoUrl');

      // Create reel post
      final createdPost = await feedService.createPost(
        caption: _captionController.text.trim(),
        mediaUrl: videoUrl,
        mediaType: MediaType.video,
        location: _locationController.text.trim().isEmpty
            ? null
            : _locationController.text.trim(),
        // Note: Additional metadata like musicPath, privacy, etc.
        // would need to be added to the backend API first
      );

      // Add the new reel to the feed provider for immediate display
      ref.read(feedProvider.notifier).addNewPost(createdPost);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Reel created successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error creating reel: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _openMusicSelector() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) {
        return SizedBox(
          height: MediaQuery.of(context).size.height * 0.7,
          child: MusicSelector(
            selectedMusicPath: _selectedMusicPath,
            onMusicSelected: (musicPath) {
              setState(() {
                _selectedMusicPath = musicPath;
                // Extract title and artist from the path or use a lookup
                _selectedMusicTitle =
                    musicPath?.split('/').last.split('.').first ?? '';
                // Note: Artist metadata would be extracted in a real implementation
              });
            },
          ),
        );
      },
    );
  }

  void _openPrivacySettings() {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Privacy Settings',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              _buildPrivacyOption(
                'public',
                'Public',
                'Anyone can see this reel',
              ),
              _buildPrivacyOption(
                'followers',
                'Followers',
                'Only your followers can see this reel',
              ),
              _buildPrivacyOption(
                'close_friends',
                'Close Friends',
                'Only close friends can see this reel',
              ),
              const SizedBox(height: 16),
              const Divider(),
              CheckboxListTile(
                title: const Text('Turn off commenting'),
                value: _turnOffCommenting,
                onChanged: (value) {
                  setState(() {
                    _turnOffCommenting = value ?? false;
                  });
                  Navigator.pop(context);
                },
              ),
              CheckboxListTile(
                title: const Text('Hide like and share count'),
                value: _hideLikeCount,
                onChanged: (value) {
                  setState(() {
                    _hideLikeCount = value ?? false;
                  });
                  Navigator.pop(context);
                },
              ),
              CheckboxListTile(
                title: const Text('Turn off sharing'),
                value: _turnOffSharing,
                onChanged: (value) {
                  setState(() {
                    _turnOffSharing = value ?? false;
                  });
                  Navigator.pop(context);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPrivacyOption(String value, String title, String subtitle) {
    return ListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      leading: Radio<String>(
        value: value,
        groupValue: _privacySetting,
        onChanged: (String? newValue) {
          setState(() {
            _privacySetting = newValue ?? 'public';
          });
          Navigator.pop(context);
        },
      ),
    );
  }

  void _addTag() {
    showDialog(
      context: context,
      builder: (context) {
        final controller = TextEditingController();
        return AlertDialog(
          title: const Text('Tag People'),
          content: TextField(
            controller: controller,
            decoration: const InputDecoration(
              hintText: 'Enter username',
              prefixText: '@',
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                if (controller.text.trim().isNotEmpty) {
                  setState(() {
                    _taggedUsers.add(controller.text.trim());
                  });
                }
                Navigator.pop(context);
              },
              child: const Text('Add'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: _isLoading ? null : () => Navigator.pop(context),
        ),
        title: const Text('Create Reel', style: TextStyle(color: Colors.white)),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _createReel,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Share', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
      body: Column(
        children: [
          // Video preview
          Expanded(
            child: Center(
              child: _currentMediaFile == null
                  ? Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.video_library,
                          size: 64,
                          color: Colors.grey,
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'No video selected',
                          style: TextStyle(color: Colors.grey),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () async {
                            // Capture context before async operation
                            final scaffoldMessenger = ScaffoldMessenger.of(
                              context,
                            );

                            // Navigate to camera for video capture
                            final result = await Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (context) => const StoryCreationScreen(
                                  initialMode: CreationMode.reel,
                                ),
                                fullscreenDialog: true,
                              ),
                            );

                            if (result != null && result is File && mounted) {
                              // Handle the captured video result
                              setState(() {
                                _currentMediaFile = result;
                              });

                              // Initialize the video player with the new file
                              await _initializeVideo();

                              if (mounted) {
                                scaffoldMessenger.showSnackBar(
                                  const SnackBar(
                                    content: Text(
                                      'Video captured! Ready to create reel.',
                                    ),
                                    backgroundColor: Colors.green,
                                  ),
                                );
                              }
                            }
                          },
                          child: const Text('Capture Video'),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () async {
                            // Capture context before async operation
                            final scaffoldMessenger = ScaffoldMessenger.of(
                              context,
                            );

                            try {
                              final picker = ImagePicker();
                              final pickedFile = await picker.pickVideo(
                                source: ImageSource.gallery,
                                maxDuration: const Duration(minutes: 1),
                              );

                              if (pickedFile != null && mounted) {
                                final file = File(pickedFile.path);
                                setState(() {
                                  _currentMediaFile = file;
                                });

                                // Initialize the video player with the selected file
                                await _initializeVideo();

                                if (mounted) {
                                  scaffoldMessenger.showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                        'Video selected! Ready to create reel.',
                                      ),
                                      backgroundColor: Colors.green,
                                    ),
                                  );
                                }
                              }
                            } catch (e) {
                              if (mounted) {
                                scaffoldMessenger.showSnackBar(
                                  SnackBar(
                                    content: Text('Error selecting video: $e'),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                              }
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.grey[800],
                          ),
                          child: const Text('Choose from Gallery'),
                        ),
                      ],
                    )
                  : _isVideoInitialized && _videoController != null
                  ? Stack(
                      children: [
                        AspectRatio(
                          aspectRatio: _videoController!.value.aspectRatio,
                          child: VideoPlayer(_videoController!),
                        ),
                        // Music overlay if selected
                        if (_selectedMusicPath != null)
                          Positioned(
                            bottom: 16,
                            left: 16,
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 8,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.black54,
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  const Icon(
                                    Icons.music_note,
                                    color: Colors.white,
                                    size: 16,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    _selectedMusicTitle ?? 'Music',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                      ],
                    )
                  : const CircularProgressIndicator(),
            ),
          ),

          // Enhanced controls section
          Container(
            color: Colors.black,
            child: Column(
              children: [
                // Music and settings row
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  child: Row(
                    children: [
                      // Music button
                      Expanded(
                        child: GestureDetector(
                          onTap: _openMusicSelector,
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            decoration: BoxDecoration(
                              color: Colors.grey[800],
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const FaIcon(
                                  FontAwesomeIcons.music,
                                  color: Colors.white,
                                  size: 16,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  _selectedMusicPath != null
                                      ? 'Change Music'
                                      : 'Add Music',
                                  style: const TextStyle(color: Colors.white),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      // Tag people button
                      Expanded(
                        child: GestureDetector(
                          onTap: _addTag,
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            decoration: BoxDecoration(
                              color: Colors.grey[800],
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const FaIcon(
                                  FontAwesomeIcons.userTag,
                                  color: Colors.white,
                                  size: 16,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Tag (${_taggedUsers.length})',
                                  style: const TextStyle(color: Colors.white),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      // Privacy settings button
                      GestureDetector(
                        onTap: _openPrivacySettings,
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey[800],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const FaIcon(
                            FontAwesomeIcons.gear,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Location input
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: TextField(
                    controller: _locationController,
                    style: const TextStyle(color: Colors.white),
                    decoration: const InputDecoration(
                      hintText: 'Add location...',
                      hintStyle: TextStyle(color: Colors.grey),
                      prefixIcon: Icon(Icons.location_on, color: Colors.grey),
                      border: InputBorder.none,
                    ),
                  ),
                ),

                // Caption input
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: TextField(
                    controller: _captionController,
                    style: const TextStyle(color: Colors.white),
                    decoration: const InputDecoration(
                      hintText: 'Write a caption...',
                      hintStyle: TextStyle(color: Colors.grey),
                      border: InputBorder.none,
                    ),
                    maxLines: 3,
                  ),
                ),

                // Tagged users display
                if (_taggedUsers.isNotEmpty)
                  Container(
                    height: 40,
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: _taggedUsers.length,
                      itemBuilder: (context, index) {
                        final user = _taggedUsers[index];
                        return Container(
                          margin: const EdgeInsets.only(right: 8),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.blue.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(color: Colors.blue),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                '@$user',
                                style: const TextStyle(
                                  color: Colors.blue,
                                  fontSize: 12,
                                ),
                              ),
                              const SizedBox(width: 4),
                              GestureDetector(
                                onTap: () {
                                  setState(() {
                                    _taggedUsers.removeAt(index);
                                  });
                                },
                                child: const Icon(
                                  Icons.close,
                                  color: Colors.blue,
                                  size: 14,
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
