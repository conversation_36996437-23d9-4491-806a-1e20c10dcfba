import 'package:flutter/material.dart';
import 'package:billionaires_social/features/stories/widgets/music_selector.dart';

class ReelTemplatesService {
  /// Get available reel templates
  static Future<List<ReelTemplate>> getTemplates() async {
    await Future.delayed(const Duration(milliseconds: 500));
    return _templates;
  }

  /// Get popular templates
  static Future<List<ReelTemplate>> getPopularTemplates() async {
    await Future.delayed(const Duration(milliseconds: 300));
    return _templates.where((template) => template.isPopular).toList();
  }

  /// Get trending templates
  static Future<List<ReelTemplate>> getTrendingTemplates() async {
    await Future.delayed(const Duration(milliseconds: 300));
    return _templates.where((template) => template.isTrending).toList();
  }

  /// Search templates
  static Future<List<ReelTemplate>> searchTemplates(String query) async {
    await Future.delayed(const Duration(milliseconds: 200));
    
    if (query.isEmpty) {
      return _templates;
    }
    
    return _templates.where((template) =>
      template.title.toLowerCase().contains(query.toLowerCase()) ||
      template.description.toLowerCase().contains(query.toLowerCase()) ||
      template.category.toLowerCase().contains(query.toLowerCase())
    ).toList();
  }

  /// Get templates by category
  static Future<List<ReelTemplate>> getTemplatesByCategory(String category) async {
    await Future.delayed(const Duration(milliseconds: 200));
    return _templates.where((template) => template.category == category).toList();
  }

  /// Get template by ID
  static Future<ReelTemplate?> getTemplateById(String id) async {
    await Future.delayed(const Duration(milliseconds: 100));
    try {
      return _templates.firstWhere((template) => template.id == id);
    } catch (e) {
      return null;
    }
  }

  static final List<ReelTemplate> _templates = [
    ReelTemplate(
      id: 'luxury_day',
      title: 'Luxury Day in the Life',
      description: 'Show your daily luxury routine',
      category: 'Lifestyle',
      thumbnailUrl: 'assets/templates/luxury_day_thumb.jpg',
      duration: Duration(seconds: 30),
      isPopular: true,
      isTrending: true,
      usageCount: 15420,
      suggestedMusic: MusicTrack(
        id: 'luxury_1',
        title: 'Golden Hour',
        artist: 'Elite Sounds',
        duration: Duration(seconds: 30),
        path: 'assets/music/golden_hour.mp3',
        genre: 'Ambient',
      ),
      steps: [
        TemplateStep(
          title: 'Morning Routine',
          description: 'Show your morning luxury routine',
          duration: Duration(seconds: 8),
          suggestedShots: ['Close-up of expensive watch', 'Luxury coffee setup'],
        ),
        TemplateStep(
          title: 'Outfit Selection',
          description: 'Display your designer wardrobe',
          duration: Duration(seconds: 7),
          suggestedShots: ['Designer clothes laid out', 'Jewelry selection'],
        ),
        TemplateStep(
          title: 'Transportation',
          description: 'Show your premium ride',
          duration: Duration(seconds: 8),
          suggestedShots: ['Car exterior', 'Luxury interior details'],
        ),
        TemplateStep(
          title: 'Evening Wind Down',
          description: 'End with relaxation',
          duration: Duration(seconds: 7),
          suggestedShots: ['Fine dining', 'Premium spa/relaxation'],
        ),
      ],
    ),
    ReelTemplate(
      id: 'before_after',
      title: 'Transformation',
      description: 'Show amazing before and after results',
      category: 'Transformation',
      thumbnailUrl: 'assets/templates/transformation_thumb.jpg',
      duration: Duration(seconds: 15),
      isPopular: true,
      isTrending: false,
      usageCount: 12350,
      suggestedMusic: MusicTrack(
        id: 'transform_1',
        title: 'Rise Up',
        artist: 'Motivation Beats',
        duration: Duration(seconds: 15),
        path: 'assets/music/rise_up.mp3',
        genre: 'Electronic',
      ),
      steps: [
        TemplateStep(
          title: 'Before',
          description: 'Show the starting point',
          duration: Duration(seconds: 3),
          suggestedShots: ['Initial state', 'Close-up details'],
        ),
        TemplateStep(
          title: 'Process',
          description: 'Quick montage of the process',
          duration: Duration(seconds: 9),
          suggestedShots: ['Time-lapse of process', 'Key moments'],
        ),
        TemplateStep(
          title: 'After',
          description: 'Reveal the amazing result',
          duration: Duration(seconds: 3),
          suggestedShots: ['Final result reveal', 'Multiple angles'],
        ),
      ],
    ),
    ReelTemplate(
      id: 'add_yours',
      title: 'Add Yours Challenge',
      description: 'Start a viral "Add Yours" trend',
      category: 'Challenge',
      thumbnailUrl: 'assets/templates/add_yours_thumb.jpg',
      duration: Duration(seconds: 10),
      isPopular: false,
      isTrending: true,
      usageCount: 9876,
      suggestedMusic: MusicTrack(
        id: 'challenge_1',
        title: 'Viral Beat',
        artist: 'Trend Makers',
        duration: Duration(seconds: 10),
        path: 'assets/music/viral_beat.mp3',
        genre: 'Hip Hop',
      ),
      steps: [
        TemplateStep(
          title: 'Setup',
          description: 'Introduce the challenge',
          duration: Duration(seconds: 2),
          suggestedShots: ['Text overlay explaining challenge'],
        ),
        TemplateStep(
          title: 'Your Take',
          description: 'Show your unique version',
          duration: Duration(seconds: 6),
          suggestedShots: ['Your interpretation', 'Creative shots'],
        ),
        TemplateStep(
          title: 'Call to Action',
          description: 'Encourage others to participate',
          duration: Duration(seconds: 2),
          suggestedShots: ['Text: "Add Yours"', 'Encouraging gesture'],
        ),
      ],
    ),
    ReelTemplate(
      id: 'business_tips',
      title: 'Business Tips',
      description: 'Share valuable business insights',
      category: 'Business',
      thumbnailUrl: 'assets/templates/business_tips_thumb.jpg',
      duration: Duration(seconds: 45),
      isPopular: true,
      isTrending: false,
      usageCount: 8543,
      suggestedMusic: MusicTrack(
        id: 'business_1',
        title: 'Executive Energy',
        artist: 'Corporate Sounds',
        duration: Duration(seconds: 45),
        path: 'assets/music/executive_energy.mp3',
        genre: 'Electronic',
      ),
      steps: [
        TemplateStep(
          title: 'Hook',
          description: 'Grab attention with a bold statement',
          duration: Duration(seconds: 5),
          suggestedShots: ['Strong opening statement', 'Eye contact'],
        ),
        TemplateStep(
          title: 'Problem',
          description: 'Identify the business problem',
          duration: Duration(seconds: 10),
          suggestedShots: ['Visual of problem', 'Relatable scenarios'],
        ),
        TemplateStep(
          title: 'Solution',
          description: 'Present your valuable solution',
          duration: Duration(seconds: 25),
          suggestedShots: ['Step-by-step breakdown', 'Visual examples'],
        ),
        TemplateStep(
          title: 'CTA',
          description: 'Call to action for engagement',
          duration: Duration(seconds: 5),
          suggestedShots: ['Follow/share request', 'Question for comments'],
        ),
      ],
    ),
    ReelTemplate(
      id: 'product_showcase',
      title: 'Product Spotlight',
      description: 'Showcase products in style',
      category: 'Business',
      thumbnailUrl: 'assets/templates/product_thumb.jpg',
      duration: Duration(seconds: 20),
      isPopular: false,
      isTrending: false,
      usageCount: 7621,
      suggestedMusic: MusicTrack(
        id: 'product_1',
        title: 'Showcase Vibes',
        artist: 'Commercial Beats',
        duration: Duration(seconds: 20),
        path: 'assets/music/showcase_vibes.mp3',
        genre: 'Pop',
      ),
      steps: [
        TemplateStep(
          title: 'Product Reveal',
          description: 'Dramatic product introduction',
          duration: Duration(seconds: 5),
          suggestedShots: ['Slow reveal', 'Multiple angles'],
        ),
        TemplateStep(
          title: 'Features',
          description: 'Highlight key features',
          duration: Duration(seconds: 10),
          suggestedShots: ['Close-up details', 'Features in action'],
        ),
        TemplateStep(
          title: 'Lifestyle Integration',
          description: 'Show product in use',
          duration: Duration(seconds: 5),
          suggestedShots: ['Real-world usage', 'Happy customer'],
        ),
      ],
    ),
  ];
}

/// Reel template model
class ReelTemplate {
  final String id;
  final String title;
  final String description;
  final String category;
  final String thumbnailUrl;
  final Duration duration;
  final bool isPopular;
  final bool isTrending;
  final int usageCount;
  final MusicTrack suggestedMusic;
  final List<TemplateStep> steps;

  const ReelTemplate({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.thumbnailUrl,
    required this.duration,
    required this.isPopular,
    required this.isTrending,
    required this.usageCount,
    required this.suggestedMusic,
    required this.steps,
  });

  String get formattedUsageCount {
    if (usageCount >= 1000000) {
      return '${(usageCount / 1000000).toStringAsFixed(1)}M';
    } else if (usageCount >= 1000) {
      return '${(usageCount / 1000).toStringAsFixed(1)}K';
    } else {
      return usageCount.toString();
    }
  }
}

/// Individual step in a template
class TemplateStep {
  final String title;
  final String description;
  final Duration duration;
  final List<String> suggestedShots;

  const TemplateStep({
    required this.title,
    required this.description,
    required this.duration,
    required this.suggestedShots,
  });
}

/// Template categories
enum TemplateCategory {
  lifestyle,
  business,
  transformation,
  challenge,
  educational,
  entertainment,
  food,
  travel,
  fitness,
  fashion,
}

extension TemplateCategoryExtension on TemplateCategory {
  String get displayName {
    switch (this) {
      case TemplateCategory.lifestyle:
        return 'Lifestyle';
      case TemplateCategory.business:
        return 'Business';
      case TemplateCategory.transformation:
        return 'Transformation';
      case TemplateCategory.challenge:
        return 'Challenge';
      case TemplateCategory.educational:
        return 'Educational';
      case TemplateCategory.entertainment:
        return 'Entertainment';
      case TemplateCategory.food:
        return 'Food';
      case TemplateCategory.travel:
        return 'Travel';
      case TemplateCategory.fitness:
        return 'Fitness';
      case TemplateCategory.fashion:
        return 'Fashion';
    }
  }

  IconData get icon {
    switch (this) {
      case TemplateCategory.lifestyle:
        return Icons.home;
      case TemplateCategory.business:
        return Icons.business;
      case TemplateCategory.transformation:
        return Icons.transform;
      case TemplateCategory.challenge:
        return Icons.emoji_events;
      case TemplateCategory.educational:
        return Icons.school;
      case TemplateCategory.entertainment:
        return Icons.movie;
      case TemplateCategory.food:
        return Icons.restaurant;
      case TemplateCategory.travel:
        return Icons.flight;
      case TemplateCategory.fitness:
        return Icons.fitness_center;
      case TemplateCategory.fashion:
        return Icons.checkroom;
    }
  }
}
