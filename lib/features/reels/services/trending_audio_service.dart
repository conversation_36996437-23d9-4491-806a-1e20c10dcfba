import 'package:billionaires_social/features/stories/widgets/music_selector.dart';

class TrendingAudioService {
  static final List<MusicTrack> _trendingTracks = [
    MusicTrack(
      id: 'trending_1',
      title: 'Viral Luxury Beat',
      artist: 'Elite Productions',
      duration: Duration(minutes: 0, seconds: 30),
      path: 'assets/music/viral_luxury_beat.mp3',
      genre: 'Trending',
    ),
    MusicTrack(
      id: 'trending_2',
      title: 'Billionaire Energy',
      artist: 'Wealth Waves',
      duration: Duration(minutes: 0, seconds: 45),
      path: 'assets/music/billionaire_energy.mp3',
      genre: 'Trending',
    ),
    MusicTrack(
      id: 'trending_3',
      title: 'Success Symphony',
      artist: 'Victory Sounds',
      duration: Duration(minutes: 1, seconds: 0),
      path: 'assets/music/success_symphony.mp3',
      genre: 'Trending',
    ),
    MusicTrack(
      id: 'trending_4',
      title: 'Golden Hour Vibes',
      artist: 'Luxe Beats',
      duration: Duration(minutes: 0, seconds: 30),
      path: 'assets/music/golden_hour_vibes.mp3',
      genre: 'Trending',
    ),
    MusicTrack(
      id: 'trending_5',
      title: 'Diamond Dreams',
      artist: 'Elite Records',
      duration: Duration(minutes: 0, seconds: 40),
      path: 'assets/music/diamond_dreams.mp3',
      genre: 'Trending',
    ),
  ];

  /// Get trending audio tracks
  static Future<List<MusicTrack>> getTrendingTracks() async {
    // Simulate API call delay
    await Future.delayed(const Duration(milliseconds: 500));
    return _trendingTracks;
  }

  /// Search trending tracks
  static Future<List<MusicTrack>> searchTrendingTracks(String query) async {
    await Future.delayed(const Duration(milliseconds: 300));
    
    if (query.isEmpty) {
      return _trendingTracks;
    }
    
    return _trendingTracks.where((track) =>
      track.title.toLowerCase().contains(query.toLowerCase()) ||
      track.artist.toLowerCase().contains(query.toLowerCase())
    ).toList();
  }

  /// Get track usage count (for trending purposes)
  static Future<int> getTrackUsageCount(String trackId) async {
    // Simulate API call
    await Future.delayed(const Duration(milliseconds: 200));
    
    // Mock usage counts
    final Map<String, int> usageCounts = {
      'trending_1': 15420,
      'trending_2': 12350,
      'trending_3': 9876,
      'trending_4': 8543,
      'trending_5': 7621,
    };
    
    return usageCounts[trackId] ?? 0;
  }

  /// Get tracks used in templates
  static Future<List<MusicTrack>> getTemplateMusic() async {
    await Future.delayed(const Duration(milliseconds: 300));
    
    // Return subset of trending tracks that work well with templates
    return _trendingTracks.take(3).toList();
  }

  /// Format usage count for display
  static String formatUsageCount(int count) {
    if (count >= 1000000) {
      return '${(count / 1000000).toStringAsFixed(1)}M';
    } else if (count >= 1000) {
      return '${(count / 1000).toStringAsFixed(1)}K';
    } else {
      return count.toString();
    }
  }

  /// Check if track is currently trending
  static bool isTrending(String trackId) {
    return _trendingTracks.any((track) => track.id == trackId);
  }

  /// Get recommended tracks based on user activity
  static Future<List<MusicTrack>> getRecommendedTracks() async {
    await Future.delayed(const Duration(milliseconds: 400));
    
    // For now, return a shuffled version of trending tracks
    final tracks = List<MusicTrack>.from(_trendingTracks);
    tracks.shuffle();
    return tracks.take(3).toList();
  }
}

/// Audio usage statistics
class AudioUsageStats {
  final String trackId;
  final int usageCount;
  final double trendingScore;
  final DateTime lastUsed;

  const AudioUsageStats({
    required this.trackId,
    required this.usageCount,
    required this.trendingScore,
    required this.lastUsed,
  });
}

/// Audio category for organization
enum AudioCategory {
  trending,
  popular,
  new_,
  recommended,
  classical,
  electronic,
  hipHop,
  pop,
  ambient,
}

extension AudioCategoryExtension on AudioCategory {
  String get displayName {
    switch (this) {
      case AudioCategory.trending:
        return 'Trending';
      case AudioCategory.popular:
        return 'Popular';
      case AudioCategory.new_:
        return 'New';
      case AudioCategory.recommended:
        return 'For You';
      case AudioCategory.classical:
        return 'Classical';
      case AudioCategory.electronic:
        return 'Electronic';
      case AudioCategory.hipHop:
        return 'Hip Hop';
      case AudioCategory.pop:
        return 'Pop';
      case AudioCategory.ambient:
        return 'Ambient';
    }
  }
}
