import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';

class AdvancedCropScreen extends StatefulWidget {
  final File imageFile;

  const AdvancedCropScreen({super.key, required this.imageFile});

  @override
  State<AdvancedCropScreen> createState() => _AdvancedCropScreenState();
}

class _AdvancedCropScreenState extends State<AdvancedCropScreen> {
  final GlobalKey _cropKey = GlobalKey();

  late ui.Image _image;
  bool _imageLoaded = false;

  // Crop area
  Rect _cropRect = const Rect.fromLTWH(0.1, 0.1, 0.8, 0.8);
  double _aspectRatio = 1.0; // Default to square
  String _selectedAspectRatio = '1:1';

  // Aspect ratio presets
  final Map<String, double> _aspectRatios = {
    'Original': 0.0, // 0 means original aspect ratio
    '1:1': 1.0,
    '4:3': 4.0 / 3.0,
    '3:4': 3.0 / 4.0,
    '16:9': 16.0 / 9.0,
    '9:16': 9.0 / 16.0,
    '3:2': 3.0 / 2.0,
    '2:3': 2.0 / 3.0,
    'Free': -1.0, // -1 means free form
  };

  // Pan and zoom
  double _scale = 1.0;
  Offset _offset = Offset.zero;

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  Future<void> _loadImage() async {
    try {
      final bytes = await widget.imageFile.readAsBytes();
      final codec = await ui.instantiateImageCodec(bytes);
      final frame = await codec.getNextFrame();

      setState(() {
        _image = frame.image;
        _imageLoaded = true;

        // Set original aspect ratio as default
        final originalAspectRatio = _image.width / _image.height;
        _aspectRatios['Original'] = originalAspectRatio;
        _aspectRatio = originalAspectRatio;
        _selectedAspectRatio = 'Original';

        // Initialize crop rect to center
        _initializeCropRect();
      });
    } catch (e) {
      debugPrint('Error loading image: $e');
    }
  }

  void _initializeCropRect() {
    if (_aspectRatio == -1.0) {
      // Free form - use default rect
      _cropRect = const Rect.fromLTWH(0.1, 0.1, 0.8, 0.8);
    } else if (_aspectRatio == 0.0) {
      // Original aspect ratio
      _cropRect = const Rect.fromLTWH(0.1, 0.1, 0.8, 0.8);
    } else {
      // Fixed aspect ratio - calculate centered rect
      const margin = 0.1;
      const maxWidth = 1.0 - (2 * margin);
      const maxHeight = 1.0 - (2 * margin);

      double width, height;

      if (_aspectRatio > 1.0) {
        // Landscape
        width = maxWidth;
        height = width / _aspectRatio;
        if (height > maxHeight) {
          height = maxHeight;
          width = height * _aspectRatio;
        }
      } else {
        // Portrait or square
        height = maxHeight;
        width = height * _aspectRatio;
        if (width > maxWidth) {
          width = maxWidth;
          height = width / _aspectRatio;
        }
      }

      final left = (1.0 - width) / 2;
      final top = (1.0 - height) / 2;

      _cropRect = Rect.fromLTWH(left, top, width, height);
    }
  }

  void _setAspectRatio(String ratio) {
    setState(() {
      _selectedAspectRatio = ratio;
      _aspectRatio = _aspectRatios[ratio]!;
      _initializeCropRect();
    });
  }

  Future<File?> _cropImage() async {
    try {
      if (!_imageLoaded) return null;

      // Calculate actual crop coordinates
      final imageWidth = _image.width.toDouble();
      final imageHeight = _image.height.toDouble();

      final cropX = (_cropRect.left * imageWidth).round();
      final cropY = (_cropRect.top * imageHeight).round();
      final cropWidth = (_cropRect.width * imageWidth).round();
      final cropHeight = (_cropRect.height * imageHeight).round();

      // Create a new image with the cropped area
      final recorder = ui.PictureRecorder();
      final canvas = Canvas(recorder);

      // Draw the cropped portion
      final srcRect = Rect.fromLTWH(
        cropX.toDouble(),
        cropY.toDouble(),
        cropWidth.toDouble(),
        cropHeight.toDouble(),
      );
      final dstRect = Rect.fromLTWH(
        0,
        0,
        cropWidth.toDouble(),
        cropHeight.toDouble(),
      );

      canvas.drawImageRect(_image, srcRect, dstRect, Paint());

      final picture = recorder.endRecording();
      final croppedImage = await picture.toImage(cropWidth, cropHeight);

      // Convert to bytes
      final byteData = await croppedImage.toByteData(
        format: ui.ImageByteFormat.png,
      );
      if (byteData == null) return null;

      // Save to file
      final tempDir = await getTemporaryDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final filePath = '${tempDir.path}/cropped_image_$timestamp.png';

      final file = File(filePath);
      await file.writeAsBytes(byteData.buffer.asUint8List());

      return file;
    } catch (e) {
      debugPrint('Error cropping image: $e');
      return null;
    }
  }

  void _finishCropping() async {
    final result = await _cropImage();
    if (mounted) {
      Navigator.of(context).pop(result);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        title: const Text('Crop Image'),
        actions: [
          TextButton(
            onPressed: _finishCropping,
            child: const Text(
              'Done',
              style: TextStyle(color: Colors.blue, fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Crop area
          Expanded(
            flex: 3,
            child: Container(
              width: double.infinity,
              color: Colors.black,
              child: _imageLoaded
                  ? CustomPaint(
                      key: _cropKey,
                      painter: CropPainter(
                        image: _image,
                        cropRect: _cropRect,
                        scale: _scale,
                        offset: _offset,
                      ),
                      child: GestureDetector(
                        onPanUpdate: (details) {
                          if (_aspectRatio == -1.0) {
                            // Free form cropping - allow manual adjustment
                            _updateCropRect(details);
                          }
                        },
                        onScaleUpdate: (details) {
                          setState(() {
                            _scale = (_scale * details.scale).clamp(0.5, 3.0);
                            _offset += details.focalPointDelta;
                          });
                        },
                      ),
                    )
                  : const Center(
                      child: CircularProgressIndicator(color: Colors.white),
                    ),
            ),
          ),

          // Controls
          Container(
            color: Colors.grey[900],
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Aspect Ratio',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 12),

                // Aspect ratio buttons
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: _aspectRatios.keys.map((ratio) {
                    final isSelected = _selectedAspectRatio == ratio;
                    return GestureDetector(
                      onTap: () => _setAspectRatio(ratio),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: isSelected ? Colors.blue : Colors.grey[700],
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: isSelected ? Colors.blue : Colors.grey[600]!,
                          ),
                        ),
                        child: Text(
                          ratio,
                          style: TextStyle(
                            color: isSelected ? Colors.white : Colors.grey[300],
                            fontWeight: isSelected
                                ? FontWeight.w600
                                : FontWeight.normal,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),

                const SizedBox(height: 16),

                // Instructions
                Text(
                  _aspectRatio == -1.0
                      ? 'Drag to adjust crop area • Pinch to zoom'
                      : 'Pinch to zoom and pan the image',
                  style: TextStyle(color: Colors.grey[400], fontSize: 12),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _updateCropRect(DragUpdateDetails details) {
    // This would implement manual crop rect adjustment for free form mode
    // For now, we'll keep it simple and use the gesture detector for zoom/pan
  }
}

class CropPainter extends CustomPainter {
  final ui.Image image;
  final Rect cropRect;
  final double scale;
  final Offset offset;

  CropPainter({
    required this.image,
    required this.cropRect,
    required this.scale,
    required this.offset,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Calculate image display size and position
    final imageAspectRatio = image.width / image.height;
    final displayAspectRatio = size.width / size.height;

    late double displayWidth, displayHeight, displayX, displayY;

    if (imageAspectRatio > displayAspectRatio) {
      // Image is wider than display
      displayWidth = size.width;
      displayHeight = size.width / imageAspectRatio;
      displayX = 0;
      displayY = (size.height - displayHeight) / 2;
    } else {
      // Image is taller than display
      displayHeight = size.height;
      displayWidth = size.height * imageAspectRatio;
      displayX = (size.width - displayWidth) / 2;
      displayY = 0;
    }

    // Apply scale and offset
    displayWidth *= scale;
    displayHeight *= scale;
    displayX += offset.dx;
    displayY += offset.dy;

    // Draw the image
    final imageRect = Rect.fromLTWH(
      displayX,
      displayY,
      displayWidth,
      displayHeight,
    );
    canvas.drawImageRect(
      image,
      Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble()),
      imageRect,
      Paint(),
    );

    // Draw crop overlay
    final cropX = displayX + (cropRect.left * displayWidth);
    final cropY = displayY + (cropRect.top * displayHeight);
    final cropWidth = cropRect.width * displayWidth;
    final cropHeight = cropRect.height * displayHeight;

    // Draw dark overlay outside crop area
    final overlayPaint = Paint()..color = Colors.black.withValues(alpha: 0.5);

    // Top
    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, cropY), overlayPaint);

    // Bottom
    canvas.drawRect(
      Rect.fromLTWH(
        0,
        cropY + cropHeight,
        size.width,
        size.height - cropY - cropHeight,
      ),
      overlayPaint,
    );

    // Left
    canvas.drawRect(Rect.fromLTWH(0, cropY, cropX, cropHeight), overlayPaint);

    // Right
    canvas.drawRect(
      Rect.fromLTWH(
        cropX + cropWidth,
        cropY,
        size.width - cropX - cropWidth,
        cropHeight,
      ),
      overlayPaint,
    );

    // Draw crop border
    final borderPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    canvas.drawRect(
      Rect.fromLTWH(cropX, cropY, cropWidth, cropHeight),
      borderPaint,
    );

    // Draw grid lines
    final gridPaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.5)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    // Vertical lines
    for (int i = 1; i < 3; i++) {
      final x = cropX + (cropWidth * i / 3);
      canvas.drawLine(
        Offset(x, cropY),
        Offset(x, cropY + cropHeight),
        gridPaint,
      );
    }

    // Horizontal lines
    for (int i = 1; i < 3; i++) {
      final y = cropY + (cropHeight * i / 3);
      canvas.drawLine(
        Offset(cropX, y),
        Offset(cropX + cropWidth, y),
        gridPaint,
      );
    }
  }

  @override
  bool shouldRepaint(CropPainter oldDelegate) {
    return oldDelegate.cropRect != cropRect ||
        oldDelegate.scale != scale ||
        oldDelegate.offset != offset;
  }
}
