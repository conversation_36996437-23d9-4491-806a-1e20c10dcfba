import 'package:flutter/material.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/features/explore/widgets/feature_list_widget.dart';

class LiveStreamSetupScreen extends StatelessWidget {
  const LiveStreamSetupScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final liveStreamOptions = [
      FeatureListItem(
        title: 'Go Live Now',
        subtitle: 'Start streaming instantly',
        imageUrl:
            'https://images.unsplash.com/photo-1462392246754-28dfa2df8e6b',
        onTap: () {},
        icon: Icons.wifi_tethering,
      ),
      FeatureListItem(
        title: 'Schedule Stream',
        subtitle: 'Plan a future live event',
        imageUrl:
            'https://images.unsplash.com/photo-1506744038136-46273834b3fb',
        onTap: () {},
        icon: Icons.schedule,
      ),
      FeatureListItem(
        title: 'Invite Guests',
        subtitle: 'Collaborate with other hosts',
        imageUrl:
            'https://images.unsplash.com/photo-1519125323398-675f0ddb6308',
        onTap: () {},
        icon: Icons.group_add,
      ),
    ];

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Live Stream Setup',
          style: AppTheme.fontStyles.title.copyWith(
            color: AppTheme.accentColor,
          ),
        ),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: FeatureListWidget(
          sectionTitle: 'Live Stream Options',
          items: liveStreamOptions,
        ),
      ),
    );
  }
}
