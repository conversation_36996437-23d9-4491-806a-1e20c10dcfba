import 'package:flutter/material.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/features/explore/widgets/feature_list_widget.dart';

class EventCreationScreen extends StatelessWidget {
  const EventCreationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final eventTemplates = [
      FeatureListItem(
        title: 'Private Gala',
        subtitle: 'Host an exclusive black-tie event',
        imageUrl:
            'https://images.unsplash.com/photo-1504384308090-c894fdcc538d',
        onTap: () {},
        icon: Icons.celebration,
      ),
      FeatureListItem(
        title: 'Charity Auction',
        subtitle: 'Organize a high-profile fundraiser',
        imageUrl:
            'https://images.unsplash.com/photo-1464983953574-0892a716854b',
        onTap: () {},
        icon: Icons.volunteer_activism,
      ),
      FeatureListItem(
        title: 'Luxury Retreat',
        subtitle: 'Plan a getaway for VIPs',
        imageUrl:
            'https://images.unsplash.com/photo-1519125323398-675f0ddb6308',
        onTap: () {},
        icon: Icons.beach_access,
      ),
    ];

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Create Event',
          style: AppTheme.fontStyles.title.copyWith(
            color: AppTheme.accentColor,
          ),
        ),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: FeatureListWidget(
          sectionTitle: 'Event Templates',
          items: eventTemplates,
        ),
      ),
    );
  }
}
