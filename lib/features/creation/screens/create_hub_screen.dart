import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/features/creation/screens/post_creation_screen.dart';
import 'package:billionaires_social/features/stories/screens/story_creation_screen.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class CreateHubScreen extends StatelessWidget {
  const CreateHubScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.luxuryGrey,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(25.0)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
            child: Container(
              width: 40,
              height: 5,
              decoration: BoxDecoration(
                color: Colors.grey[700],
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
          const SizedBox(height: 20),
          Text('Create', style: AppTheme.fontStyles.title),
          const SizedBox(height: 20),
          _CreateOption(
            icon: FontAwesomeIcons.penToSquare,
            title: 'New Post',
            subtitle: 'Share a moment with your followers',
            onTap: () {
              Navigator.pop(context); // Close the modal
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (_) => const PostCreationScreen(),
                  fullscreenDialog: true,
                ),
              );
            },
          ),
          const Divider(height: 30),
          _CreateOption(
            icon: FontAwesomeIcons.solidCircle,
            title: 'New Story',
            subtitle: 'Share a photo or video that disappears',
            onTap: () {
              Navigator.pop(context); // Close the modal
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (_) => const StoryCreationScreen(),
                  fullscreenDialog: true,
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}

class _CreateOption extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;

  const _CreateOption({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Row(
        children: [
          FaIcon(icon, color: AppTheme.accentColor, size: 24),
          const SizedBox(width: 20),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: AppTheme.fontStyles.subtitle),
                const SizedBox(height: 4),
                Text(subtitle, style: AppTheme.fontStyles.caption),
              ],
            ),
          ),
          const Icon(Icons.arrow_forward_ios, color: Colors.white, size: 16),
        ],
      ),
    );
  }
}
