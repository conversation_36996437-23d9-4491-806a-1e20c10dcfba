import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/core/widgets/back_button_widget.dart';

class BetaTesterOnboardingScreen extends StatefulWidget {
  final VoidCallback? onComplete;

  const BetaTesterOnboardingScreen({super.key, this.onComplete});

  @override
  State<BetaTesterOnboardingScreen> createState() =>
      _BetaTesterOnboardingScreenState();
}

class _BetaTesterOnboardingScreenState
    extends State<BetaTesterOnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  bool _isLastPage = false;

  final List<OnboardingPage> _pages = [
    OnboardingPage(
      title: 'Welcome to Billionaires Social',
      subtitle: 'The exclusive social platform for high-net-worth individuals',
      description:
          'Connect with fellow billionaires, share your lifestyle, and discover exclusive opportunities.',
      icon: FontAwesomeIcons.crown,
      color: AppTheme.accentColor,
    ),
    OnboardingPage(
      title: 'Exclusive Features',
      subtitle: 'Premium tools designed for your lifestyle',
      description:
          'Create stunning stories, host exclusive events, and showcase your luxury marketplace items.',
      icon: FontAwesomeIcons.gem,
      color: AppTheme.accentPurple,
    ),
    OnboardingPage(
      title: 'Privacy & Security',
      subtitle: 'Your privacy is our priority',
      description:
          'Advanced privacy controls, verified profiles, and secure messaging ensure your data stays protected.',
      icon: FontAwesomeIcons.shieldHalved,
      color: AppTheme.accentBlue,
    ),
    OnboardingPage(
      title: 'Beta Testing',
      subtitle: 'Help us perfect the experience',
      description:
          'As a beta tester, you\'ll get early access to new features and help shape the future of the platform.',
      icon: FontAwesomeIcons.flask,
      color: AppTheme.accentEmerald,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _updateLastPageStatus();
  }

  void _updateLastPageStatus() {
    setState(() {
      _isLastPage = _currentPage == _pages.length - 1;
    });
  }

  void _nextPage() {
    if (_currentPage < _pages.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _completeOnboarding();
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _completeOnboarding() {
    // Call the completion callback if provided
    widget.onComplete?.call();

    // Navigate to main app
    Navigator.of(context).pushReplacementNamed('/');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.primaryColor,
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                    _updateLastPageStatus();
                  });
                },
                itemCount: _pages.length,
                itemBuilder: (context, index) {
                  return _buildPage(_pages[index]);
                },
              ),
            ),
            _buildFooter(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          if (_currentPage > 0)
            CustomBackButton(onPressed: _previousPage, size: 36),
          const Spacer(),
          Text(
            'Beta Tester',
            style: AppTheme.fontStyles.title.copyWith(
              color: AppTheme.accentColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          if (_currentPage > 0) SizedBox(width: 36), // Placeholder for symmetry
        ],
      ),
    );
  }

  Widget _buildPage(OnboardingPage page) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildIcon(page),
          const SizedBox(height: 32),
          _buildTitle(page),
          const SizedBox(height: 16),
          _buildSubtitle(page),
          const SizedBox(height: 24),
          _buildDescription(page),
          const SizedBox(height: 32),
          _buildPageIndicator(),
        ],
      ),
    );
  }

  Widget _buildIcon(OnboardingPage page) {
    return Container(
      width: 120,
      height: 120,
      decoration: BoxDecoration(
        color: page.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(60),
        border: Border.all(color: page.color.withValues(alpha: 0.3), width: 2),
      ),
      child: Center(child: FaIcon(page.icon, size: 48, color: page.color)),
    );
  }

  Widget _buildTitle(OnboardingPage page) {
    return Text(
      page.title,
      style: AppTheme.fontStyles.display.copyWith(
        fontSize: 28,
        color: AppTheme.luxuryWhite,
        fontWeight: FontWeight.bold,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildSubtitle(OnboardingPage page) {
    return Text(
      page.subtitle,
      style: AppTheme.fontStyles.subtitle.copyWith(
        color: page.color,
        fontWeight: FontWeight.w600,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildDescription(OnboardingPage page) {
    return Text(
      page.description,
      style: AppTheme.fontStyles.body.copyWith(
        fontSize: 16,
        height: 1.6,
        color: AppTheme.secondaryAccentColor,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildPageIndicator() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(_pages.length, (index) {
        final isActive = index == _currentPage;
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 4),
          width: isActive ? 24 : 8,
          height: 8,
          decoration: BoxDecoration(
            color: isActive
                ? AppTheme.accentColor
                : AppTheme.secondaryAccentColor.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(4),
          ),
        );
      }),
    );
  }

  Widget _buildFooter() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          if (_isLastPage) ...[
            _buildBetaTesterBenefits(),
            const SizedBox(height: 24),
          ],
          _buildNavigationButtons(),
          const SizedBox(height: 16),
          _buildSkipButton(),
        ],
      ),
    );
  }

  Widget _buildBetaTesterBenefits() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.accentColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.accentColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              FaIcon(
                FontAwesomeIcons.star,
                color: AppTheme.accentColor,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                'Beta Tester Benefits',
                style: AppTheme.fontStyles.bodyBold.copyWith(
                  color: AppTheme.accentColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildBenefitItem('Early access to new features'),
          _buildBenefitItem('Direct feedback channel to developers'),
          _buildBenefitItem('Exclusive beta tester badge'),
          _buildBenefitItem('Priority support and assistance'),
        ],
      ),
    );
  }

  Widget _buildBenefitItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Icon(Icons.check_circle, color: AppTheme.accentColor, size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: AppTheme.fontStyles.body.copyWith(
                color: AppTheme.secondaryAccentColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Row(
      children: [
        if (_currentPage > 0) ...[
          Expanded(
            child: OutlinedButton(
              onPressed: _previousPage,
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: AppTheme.accentColor),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: Text(
                'Previous',
                style: AppTheme.fontStyles.body.copyWith(
                  color: AppTheme.accentColor,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
        ],
        Expanded(
          child: ElevatedButton(
            onPressed: _nextPage,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.accentColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: Text(
              _isLastPage ? 'Get Started' : 'Next',
              style: AppTheme.fontStyles.body.copyWith(
                color: AppTheme.luxuryBlack,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSkipButton() {
    return TextButton(
      onPressed: _completeOnboarding,
      child: Text(
        'Skip Onboarding',
        style: AppTheme.fontStyles.body.copyWith(
          color: AppTheme.secondaryAccentColor.withValues(alpha: 0.7),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }
}

class OnboardingPage {
  final String title;
  final String subtitle;
  final String description;
  final IconData icon;
  final Color color;

  const OnboardingPage({
    required this.title,
    required this.subtitle,
    required this.description,
    required this.icon,
    required this.color,
  });
}
