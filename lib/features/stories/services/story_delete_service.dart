import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/story_reel_model.dart';
import '../providers/story_provider.dart';
import 'story_service.dart';

class StoryDeleteService {
  static Future<bool> deleteStory(
    BuildContext context,
    StoryItem story, {
    VoidCallback? onSuccess,
    WidgetRef? ref,
  }) async {
    final currentUser = FirebaseAuth.instance.currentUser;

    // Ownership validation
    if (currentUser == null) {
      _showError(context, 'You must be logged in to delete stories.');
      return false;
    }

    if (story.userId != currentUser.uid) {
      _showError(context, 'You can only delete your own stories.');
      return false;
    }

    try {
      debugPrint(
        '[StoryDeleteService] Starting deletion for story: ${story.id}',
      );

      // Delete from Firestore first
      await GetIt.I<StoryService>().deleteStory(story.id);
      debugPrint('[StoryDeleteService] Story deleted from Firestore');

      // Delete from Storage if mediaUrl exists
      if (story.mediaUrl.isNotEmpty) {
        try {
          final ref = FirebaseStorage.instance.refFromURL(story.mediaUrl);
          await ref.delete();
          debugPrint('[StoryDeleteService] Media deleted from Storage');
        } catch (e) {
          debugPrint(
            '[StoryDeleteService] Storage delete failed (non-critical): $e',
          );
          // Don't fail the whole operation if storage deletion fails
        }
      }

      // Update provider state if available
      if (ref != null) {
        try {
          await ref.read(storyReelsProvider.notifier).refresh();
          debugPrint('[StoryDeleteService] Story provider refreshed');
        } catch (e) {
          debugPrint('[StoryDeleteService] Provider refresh failed: $e');
        }
      }

      // Execute success callback
      if (onSuccess != null) {
        onSuccess();
      }

      // Show success message (helper method handles context safety)
      // ignore: use_build_context_synchronously
      _showSuccess(context, 'Story deleted successfully');
      return true;
    } catch (e) {
      debugPrint('[StoryDeleteService] Error deleting story: $e');
      // Show error message (helper method handles context safety)
      // ignore: use_build_context_synchronously
      _showError(context, 'Failed to delete story. Please try again.');
      return false;
    }
  }

  static Future<bool> canDeleteStory(StoryItem story) async {
    final currentUser = FirebaseAuth.instance.currentUser;
    return currentUser != null && story.userId == currentUser.uid;
  }

  // ignore: use_build_context_synchronously
  static void _showSuccess(BuildContext context, String message) {
    // Use a post-frame callback to ensure context is still valid
    // This method is safe to call after async operations
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white, size: 20),
                const SizedBox(width: 12),
                Text(message),
              ],
            ),
            backgroundColor: Colors.green.shade600,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    });
  }

  // ignore: use_build_context_synchronously
  static void _showError(BuildContext context, String message) {
    // Use a post-frame callback to ensure context is still valid
    // This method is safe to call after async operations
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error_outline, color: Colors.white, size: 20),
                const SizedBox(width: 12),
                Expanded(child: Text(message)),
              ],
            ),
            backgroundColor: Colors.red.shade600,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    });
  }
}
