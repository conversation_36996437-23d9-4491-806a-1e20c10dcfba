import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';
import 'package:flutter/services.dart';
import 'package:billionaires_social/features/stories/models/story_reel_model.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/features/stories/services/story_service.dart';

/// Missing implementations from story_viewer_screen.dart
class StoryInteractionService {
  final StoryService _storyService = getIt<StoryService>();

  /// FIXED: Add reaction to story
  Future<void> addReaction(StoryItem story, String reaction) async {
    try {
      await _storyService.addStoryReaction(
        storyId: story.id,
        userId: story.userId,
        reaction: reaction,
      );
      debugPrint('✅ Added reaction $reaction to story ${story.id}');
    } catch (e) {
      debugPrint('❌ Failed to add reaction: $e');
      rethrow;
    }
  }

  /// FIXED: Share story to platform
  Future<void> shareToPlatform(StoryItem story, String platform) async {
    try {
      final shareUrl = 'https://billionaires-social.app/story/${story.id}';
      final shareText = 'Check out this story on Billionaires Social!';

      switch (platform.toLowerCase()) {
        case 'whatsapp':
          await SharePlus.instance.share(
            ShareParams(text: '$shareText $shareUrl'),
          );
          break;
        case 'telegram':
          await SharePlus.instance.share(
            ShareParams(text: '$shareText $shareUrl'),
          );
          break;
        case 'twitter':
          await SharePlus.instance.share(
            ShareParams(text: '$shareText $shareUrl'),
          );
          break;
        case 'facebook':
          await SharePlus.instance.share(
            ShareParams(text: '$shareText $shareUrl'),
          );
          break;
        case 'instagram':
          await SharePlus.instance.share(
            ShareParams(text: '$shareText $shareUrl'),
          );
          break;
        case 'email':
          await SharePlus.instance.share(
            ShareParams(
              text: '$shareText $shareUrl',
              subject: 'Story from Billionaires Social',
            ),
          );
          break;
        case 'sms':
          await SharePlus.instance.share(
            ShareParams(text: '$shareText $shareUrl'),
          );
          break;
        default:
          await SharePlus.instance.share(
            ShareParams(text: '$shareText $shareUrl'),
          );
      }

      // Track analytics
      await _storyService.trackStoryShare(story.id, platform);
      debugPrint('✅ Shared story ${story.id} to $platform');
    } catch (e) {
      debugPrint('❌ Failed to share story: $e');
      rethrow;
    }
  }

  /// FIXED: Copy story link
  Future<void> copyStoryLink(StoryItem story) async {
    try {
      final storyUrl = 'https://billionaires-social.app/story/${story.id}';
      await Clipboard.setData(ClipboardData(text: storyUrl));

      // Track analytics
      await _storyService.trackStoryShare(story.id, 'copy_link');
      debugPrint('✅ Copied story link: $storyUrl');
    } catch (e) {
      debugPrint('❌ Failed to copy story link: $e');
      rethrow;
    }
  }

  /// FIXED: Add interactive feature to story
  Future<void> addInteractiveFeature(StoryItem story, String feature) async {
    try {
      switch (feature.toLowerCase()) {
        case 'poll':
          await _navigateToPollCreation(story);
          break;
        case 'question':
          await _navigateToQuestionCreation(story);
          break;
        case 'countdown':
          await _navigateToCountdownCreation(story);
          break;
        case 'collaborative':
          await _navigateToCollaborativeCreation(story);
          break;
        case 'quiz':
          await _navigateToQuizCreation(story);
          break;
        case 'music':
          await _navigateToMusicCreation(story);
          break;
        default:
          throw Exception('Unknown feature type: $feature');
      }

      debugPrint('✅ Added $feature feature to story ${story.id}');
    } catch (e) {
      debugPrint('❌ Failed to add interactive feature: $e');
      rethrow;
    }
  }

  /// FIXED: Mute user's stories
  Future<void> muteUserStories(String userId) async {
    try {
      await _storyService.muteUserStories(userId);
      debugPrint('✅ Muted stories from user $userId');
    } catch (e) {
      debugPrint('❌ Failed to mute user stories: $e');
      rethrow;
    }
  }

  /// FIXED: Report story
  Future<void> reportStory(StoryItem story, String reason) async {
    try {
      await _storyService.reportStory(
        storyId: story.id,
        reportedUserId: story.userId,
        reason: reason,
      );
      debugPrint('✅ Reported story ${story.id} for: $reason');
    } catch (e) {
      debugPrint('❌ Failed to report story: $e');
      rethrow;
    }
  }

  // Private helper methods for navigation
  Future<void> _navigateToPollCreation(StoryItem story) async {
    // TODO: Navigate to poll creation screen
    // This should open a modal to create a poll overlay for the story
    throw UnimplementedError('Poll creation screen not implemented');
  }

  Future<void> _navigateToQuestionCreation(StoryItem story) async {
    // TODO: Navigate to question creation screen
    throw UnimplementedError('Question creation screen not implemented');
  }

  Future<void> _navigateToCountdownCreation(StoryItem story) async {
    // TODO: Navigate to countdown creation screen
    throw UnimplementedError('Countdown creation screen not implemented');
  }

  Future<void> _navigateToCollaborativeCreation(StoryItem story) async {
    // TODO: Navigate to collaborative story creation screen
    throw UnimplementedError('Collaborative creation screen not implemented');
  }

  Future<void> _navigateToQuizCreation(StoryItem story) async {
    // TODO: Navigate to quiz creation screen
    throw UnimplementedError('Quiz creation screen not implemented');
  }

  Future<void> _navigateToMusicCreation(StoryItem story) async {
    // TODO: Navigate to music selection screen
    throw UnimplementedError('Music creation screen not implemented');
  }
}

// Extension to StoryService to add missing methods
extension StoryServiceExtension on StoryService {
  Future<void> addStoryReaction({
    required String storyId,
    required String userId,
    required String reaction,
  }) async {
    // TODO: Implement in main StoryService
    debugPrint('Adding reaction $reaction to story $storyId');
  }

  Future<void> trackStoryShare(String storyId, String platform) async {
    // TODO: Implement analytics tracking
    debugPrint('Tracking story share: $storyId -> $platform');
  }

  Future<void> muteUserStories(String userId) async {
    // TODO: Implement user muting
    debugPrint('Muting stories from user: $userId');
  }

  Future<void> reportStory({
    required String storyId,
    required String reportedUserId,
    required String reason,
  }) async {
    // TODO: Implement story reporting
    debugPrint('Reporting story $storyId for: $reason');
  }
}
