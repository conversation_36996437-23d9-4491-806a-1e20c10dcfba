import 'package:firebase_auth/firebase_auth.dart';
import 'dart:math';

/// Mock Story Analytics Service for development
/// This service provides sample analytics data without requiring Firestore permissions
class MockStoryAnalyticsService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final Random _random = Random();

  /// Get mock story analytics data
  Future<Map<String, dynamic>> getStoryAnalytics(String storyId) async {
    await Future.delayed(
      const Duration(milliseconds: 500),
    ); // Simulate network delay

    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User must be authenticated to view analytics');
    }

    // Generate mock data
    final viewCount = _random.nextInt(500) + 10;
    final uniqueViewers = (viewCount * 0.8).round();
    final reactionCount = _random.nextInt(50);
    final replyCount = _random.nextInt(10);

    // Generate mock viewers list
    final viewers = List.generate(
      min(viewCount, 20),
      (index) => {
        'viewerId': 'user_${_random.nextInt(10000)}',
        'timestamp': DateTime.now().subtract(
          Duration(minutes: _random.nextInt(1440)), // Random time in last 24h
        ),
        'profilePicture': 'https://i.pravatar.cc/150?u=user_$index',
        'username': 'user${_random.nextInt(1000)}',
      },
    );

    // Generate mock reaction breakdown
    final reactions = ['❤️', '🔥', '👏', '😍', '😂', '👍', '✨'];
    final reactionBreakdown = <String, int>{};
    for (final reaction in reactions) {
      reactionBreakdown[reaction] = _random.nextInt(reactionCount ~/ 2);
    }

    return {
      'totalViews': viewCount,
      'uniqueViewers': uniqueViewers,
      'viewCount': viewCount,
      'replyCount': replyCount,
      'reactionCount': reactionCount,
      'shareCount': _random.nextInt(20),
      'completionRate': 0.75 + (_random.nextDouble() * 0.25), // 75-100%
      'skipRate': _random.nextDouble() * 0.25, // 0-25%
      'topViewers': viewers.take(10).map((v) => v['viewerId']).toList(),
      'reactionBreakdown': reactionBreakdown,
      'viewers': viewers,
      'hourlyViews': _generateHourlyViews(),
      'demographics': _generateDemographics(),
      'engagement': {
        'averageViewTime': 3.2 + (_random.nextDouble() * 2), // 3.2-5.2 seconds
        'bounceRate': _random.nextDouble() * 0.3, // 0-30%
        'interactionRate': _random.nextDouble() * 0.15, // 0-15%
      },
    };
  }

  /// Check if user can view analytics for a story
  Future<bool> canViewAnalytics(String storyId, String storyUserId) async {
    final currentUser = _auth.currentUser;
    return currentUser != null && currentUser.uid == storyUserId;
  }

  /// Generate mock hourly views data for charts
  List<Map<String, dynamic>> _generateHourlyViews() {
    final now = DateTime.now();
    final hours = <Map<String, dynamic>>[];

    for (int i = 23; i >= 0; i--) {
      final hour = now.subtract(Duration(hours: i));
      final views = _random.nextInt(50);

      hours.add({
        'hour': hour.hour,
        'timestamp': hour,
        'views': views,
        'uniqueViewers': (views * 0.8).round(),
      });
    }

    return hours;
  }

  /// Generate mock demographics data
  Map<String, dynamic> _generateDemographics() {
    return {
      'ageGroups': {
        '18-24': _random.nextInt(30) + 10,
        '25-34': _random.nextInt(40) + 20,
        '35-44': _random.nextInt(20) + 10,
        '45-54': _random.nextInt(15) + 5,
        '55+': _random.nextInt(10) + 2,
      },
      'genderSplit': {
        'male': _random.nextInt(40) + 30, // 30-70%
        'female': _random.nextInt(40) + 30, // 30-70%
        'other': _random.nextInt(5), // 0-5%
      },
      'topLocations': [
        {'country': 'United States', 'percentage': _random.nextInt(30) + 20},
        {'country': 'United Kingdom', 'percentage': _random.nextInt(15) + 10},
        {'country': 'Canada', 'percentage': _random.nextInt(10) + 5},
        {'country': 'Australia', 'percentage': _random.nextInt(8) + 3},
        {'country': 'Germany', 'percentage': _random.nextInt(6) + 2},
      ],
    };
  }

  /// Generate mock story performance comparison
  Future<Map<String, dynamic>> getStoryComparison(String storyId) async {
    await Future.delayed(const Duration(milliseconds: 300));

    return {
      'currentStory': await getStoryAnalytics(storyId),
      'averagePerformance': {
        'viewCount': _random.nextInt(200) + 50,
        'reactionCount': _random.nextInt(25) + 5,
        'replyCount': _random.nextInt(5) + 1,
        'shareCount': _random.nextInt(10) + 2,
        'completionRate': 0.7 + (_random.nextDouble() * 0.2),
      },
      'bestPerformingStory': {
        'storyId': 'story_${_random.nextInt(1000)}',
        'viewCount': _random.nextInt(1000) + 100,
        'date': DateTime.now().subtract(Duration(days: _random.nextInt(30))),
      },
    };
  }

  /// Get trending insights
  Future<List<String>> getTrendingInsights(String storyId) async {
    await Future.delayed(const Duration(milliseconds: 200));

    final insights = [
      'Your story is performing 23% better than your average',
      'Peak viewing time was around 8 PM',
      'Stories with location tags get 15% more views',
      'Your engagement rate is above average',
      'Most viewers come from mobile devices',
      'Stories with music get 20% more completion rate',
      'Your follower growth increased by 3% this week',
    ];

    insights.shuffle(_random);
    return insights.take(3).toList();
  }
}

/// Provider for mock analytics service
final mockStoryAnalyticsService = MockStoryAnalyticsService();
