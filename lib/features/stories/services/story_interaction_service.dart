import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:billionaires_social/features/stories/models/story_interaction_model.dart';
import 'package:billionaires_social/features/stories/services/story_viewer_service.dart';
import 'package:billionaires_social/core/services/analytics_service.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/core/services/content_moderation_service.dart';

class StoryInteractionService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final AnalyticsService _analyticsService = getIt<AnalyticsService>();
  final ContentModerationService _moderationService =
      getIt<ContentModerationService>();

  // Add reaction to a story
  Future<void> addReaction(
    String storyId,
    StoryReactionType reactionType,
  ) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    final reactionRef = _firestore
        .collection('stories')
        .doc(storyId)
        .collection('reactions')
        .doc(currentUser.uid);

    await _firestore.runTransaction((transaction) async {
      final reactionDoc = await transaction.get(reactionRef);
      final existingReaction = reactionDoc.exists ? reactionDoc.data() : null;

      // Get user profile for reaction data
      final userProfile = await _getUserProfile(currentUser.uid);
      final username =
          userProfile['name'] ?? userProfile['username'] ?? 'Unknown User';
      final userAvatarUrl = userProfile['profilePictureUrl'] ?? '';

      if (existingReaction != null &&
          existingReaction['type'] == reactionType.name) {
        // Remove reaction if same type
        transaction.delete(reactionRef);
        transaction.update(_firestore.collection('stories').doc(storyId), {
          'reactionCount': FieldValue.increment(-1),
          'updatedAt': FieldValue.serverTimestamp(),
        });
      } else {
        // Add or update reaction
        final reactionData = {
          'storyId': storyId,
          'userId': currentUser.uid,
          'username': username,
          'userAvatarUrl': userAvatarUrl,
          'type': reactionType.name,
          'timestamp': FieldValue.serverTimestamp(),
        };

        transaction.set(reactionRef, reactionData);

        // Update story reaction count
        final increment = existingReaction != null ? 0 : 1;
        transaction.update(_firestore.collection('stories').doc(storyId), {
          'reactionCount': FieldValue.increment(increment),
          'updatedAt': FieldValue.serverTimestamp(),
        });
      }
    });

    // Log analytics
    await _analyticsService.logStoryReaction(
      storyId: storyId,
      reactionType: reactionType.name,
      userId: currentUser.uid,
    );
  }

  // Add reply to a story
  Future<void> addReply(
    String storyId,
    String content, {
    String? mediaUrl,
  }) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    // Content moderation for reply
    final severity = await _moderationService.moderateContent(content);
    if (severity == ContentSeverity.blocked) {
      throw Exception('Reply content violates community guidelines.');
    } else if (severity == ContentSeverity.severe) {
      throw Exception(
        'Reply contains inappropriate content. Please review and try again.',
      );
    }

    // Get user profile
    final userProfile = await _getUserProfile(currentUser.uid);
    final username =
        userProfile['name'] ?? userProfile['username'] ?? 'Unknown User';
    final userAvatarUrl = userProfile['profilePictureUrl'] ?? '';

    final replyData = {
      'storyId': storyId,
      'userId': currentUser.uid,
      'username': username,
      'userAvatarUrl': userAvatarUrl,
      'content': content,
      'mediaUrl': mediaUrl,
      'timestamp': FieldValue.serverTimestamp(),
      'isRead': false,
      'isReplied': false,
    };

    await _firestore
        .collection('stories')
        .doc(storyId)
        .collection('replies')
        .add(replyData);

    // Update story reply count
    await _firestore.collection('stories').doc(storyId).update({
      'replyCount': FieldValue.increment(1),
      'updatedAt': FieldValue.serverTimestamp(),
    });

    // Log analytics
    await _analyticsService.logStoryReply(
      storyId: storyId,
      hasMedia: mediaUrl != null,
      userId: currentUser.uid,
    );
  }

  // Get reactions for a story
  Stream<List<StoryReaction>> getStoryReactions(String storyId) {
    return _firestore
        .collection('stories')
        .doc(storyId)
        .collection('reactions')
        .orderBy('timestamp', descending: true)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs.map((doc) {
            final data = doc.data();
            return StoryReaction(
              id: doc.id,
              storyId: data['storyId'] ?? '',
              userId: data['userId'] ?? '',
              username: data['username'] ?? '',
              userAvatarUrl: data['userAvatarUrl'] ?? '',
              type: StoryReactionType.values.firstWhere(
                (e) => e.name == (data['type'] ?? 'like'),
                orElse: () => StoryReactionType.like,
              ),
              timestamp:
                  (data['timestamp'] as Timestamp?)?.toDate() ?? DateTime.now(),
            );
          }).toList(),
        );
  }

  // Get replies for a story
  Stream<List<StoryReply>> getStoryReplies(String storyId) {
    return _firestore
        .collection('stories')
        .doc(storyId)
        .collection('replies')
        .orderBy('timestamp', descending: true)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs.map((doc) {
            final data = doc.data();
            return StoryReply(
              id: doc.id,
              storyId: data['storyId'] ?? '',
              userId: data['userId'] ?? '',
              username: data['username'] ?? '',
              userAvatarUrl: data['userAvatarUrl'] ?? '',
              content: data['content'] ?? '',
              mediaUrl: data['mediaUrl'],
              timestamp:
                  (data['timestamp'] as Timestamp?)?.toDate() ?? DateTime.now(),
              isRead: data['isRead'] ?? false,
              isReplied: data['isReplied'] ?? false,
            );
          }).toList(),
        );
  }

  // Get story analytics
  /// Enhanced analytics calculation with real-time metrics
  Future<StoryAnalytics?> getStoryAnalytics(String storyId) async {
    try {
      debugPrint(
        '📊 StoryInteractionService: Getting analytics for story: $storyId',
      );
      final storyDoc = await _firestore
          .collection('stories')
          .doc(storyId)
          .get();
      debugPrint(
        '📊 StoryInteractionService: Story document exists: ${storyDoc.exists}',
      );
      if (!storyDoc.exists) {
        debugPrint(
          '❌ StoryInteractionService: Story document not found: $storyId',
        );
        return null;
      }

      final data = storyDoc.data()!;
      debugPrint('📊 StoryInteractionService: Story data: $data');

      // Try to get reactions, but handle permission errors gracefully
      QuerySnapshot? reactions;
      try {
        reactions = await _firestore
            .collection('stories')
            .doc(storyId)
            .collection('reactions')
            .get();
        debugPrint(
          '📊 StoryInteractionService: Found ${reactions.docs.length} reactions',
        );
      } catch (e) {
        debugPrint(
          '📊 StoryInteractionService: Could not access reactions: $e',
        );
        reactions = null;
      }

      // Try to get replies, but handle permission errors gracefully
      QuerySnapshot? replies;
      try {
        replies = await _firestore
            .collection('stories')
            .doc(storyId)
            .collection('replies')
            .get();
        debugPrint(
          '📊 StoryInteractionService: Found ${replies.docs.length} replies',
        );
      } catch (e) {
        debugPrint('📊 StoryInteractionService: Could not access replies: $e');
        replies = null;
      }

      // Calculate reaction breakdown
      final reactionBreakdown = <StoryReactionType, int>{};
      if (reactions != null) {
        for (final doc in reactions.docs) {
          final docData = doc.data() as Map<String, dynamic>?;
          final type = StoryReactionType.values.firstWhere(
            (e) => e.name == (docData?['type'] ?? 'like'),
            orElse: () => StoryReactionType.like,
          );
          reactionBreakdown[type] = (reactionBreakdown[type] ?? 0) + 1;
        }
      }

      // Extract real analytics data from Firebase
      final viewCount = data['viewCount'] ?? 0;
      final viewedBy = List<String>.from(data['viewedBy'] ?? []);
      final uniqueViewers = viewedBy.length;
      final averageViewDuration = data['averageViewDuration'] ?? 0;

      final replyCount = replies?.docs.length ?? 0;
      final reactionCount = reactions?.docs.length ?? 0;

      final repliedBy =
          replies?.docs
              .map(
                (doc) =>
                    (doc.data() as Map<String, dynamic>?)?['userId'] as String?,
              )
              .where((userId) => userId != null)
              .cast<String>()
              .toList() ??
          <String>[];

      final reactedBy =
          reactions?.docs
              .map(
                (doc) =>
                    (doc.data() as Map<String, dynamic>?)?['userId'] as String?,
              )
              .where((userId) => userId != null)
              .cast<String>()
              .toList() ??
          <String>[];

      // Calculate enhanced engagement metrics
      final enhancedAnalytics = await _calculateEnhancedMetrics(
        storyId: storyId,
        baseData: data,
        viewCount: viewCount,
        uniqueViewers: uniqueViewers,
        replyCount: replyCount,
        reactionCount: reactionCount,
        reactionBreakdown: reactionBreakdown,
        averageViewDuration: averageViewDuration,
        viewedBy: viewedBy,
        repliedBy: repliedBy,
        reactedBy: reactedBy,
      );

      final analytics = enhancedAnalytics;
      debugPrint(
        '✅ StoryInteractionService: Analytics created successfully: ${analytics.viewCount} views, ${analytics.reactionCount} reactions, ${analytics.replyCount} replies',
      );
      return analytics;
    } catch (e) {
      debugPrint('Error getting story analytics: $e');
      return null;
    }
  }

  /// Calculate enhanced engagement metrics with real-time data
  Future<StoryAnalytics> _calculateEnhancedMetrics({
    required String storyId,
    required Map<String, dynamic> baseData,
    required int viewCount,
    required int uniqueViewers,
    required int replyCount,
    required int reactionCount,
    required Map<StoryReactionType, int> reactionBreakdown,
    required int averageViewDuration,
    required List<String> viewedBy,
    required List<String> repliedBy,
    required List<String> reactedBy,
  }) async {
    try {
      debugPrint('📊 Calculating enhanced metrics for story: $storyId');

      // Calculate engagement rate
      final engagementCount = reactionCount + replyCount;
      final engagementRate = viewCount > 0
          ? (engagementCount / viewCount) * 100
          : 0.0;

      // Calculate completion rate (if available)
      final completionRate = baseData['completionRate'] ?? 0.0;
      final skipRate = baseData['skipRate'] ?? 0.0;

      // Get story duration for better metrics
      final storyDuration = baseData['duration'] ?? 5;
      final viewDurationPercentage = storyDuration > 0
          ? (averageViewDuration / storyDuration) * 100
          : 0.0;

      debugPrint('📊 Enhanced metrics calculated:');
      debugPrint('   - Engagement rate: ${engagementRate.toStringAsFixed(1)}%');
      debugPrint(
        '   - View duration: ${viewDurationPercentage.toStringAsFixed(1)}%',
      );
      debugPrint('   - Completion rate: ${completionRate.toStringAsFixed(1)}%');
      debugPrint('   - Skip rate: ${skipRate.toStringAsFixed(1)}%');

      return StoryAnalytics(
        storyId: storyId,
        viewCount: viewCount,
        uniqueViewers: uniqueViewers,
        replyCount: replyCount,
        reactionCount: reactionCount,
        reactionBreakdown: reactionBreakdown,
        averageViewDuration: Duration(seconds: averageViewDuration),
        createdAt:
            (baseData['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
        expiresAt: (baseData['expiresAt'] as Timestamp?)?.toDate(),
        viewedBy: viewedBy,
        repliedBy: repliedBy,
        reactedBy: reactedBy,
      );
    } catch (e) {
      debugPrint('❌ Error calculating enhanced metrics: $e');
      // Fallback to basic analytics
      return StoryAnalytics(
        storyId: storyId,
        viewCount: viewCount,
        uniqueViewers: uniqueViewers,
        replyCount: replyCount,
        reactionCount: reactionCount,
        reactionBreakdown: reactionBreakdown,
        averageViewDuration: Duration(seconds: averageViewDuration),
        createdAt:
            (baseData['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
        expiresAt: (baseData['expiresAt'] as Timestamp?)?.toDate(),
        viewedBy: viewedBy,
        repliedBy: repliedBy,
        reactedBy: reactedBy,
      );
    }
  }

  /// Enhanced view tracking with detailed analytics using StoryViewerService
  Future<void> trackStoryViewWithAnalytics({
    required String storyId,
    required String viewerId,
    required Duration viewDuration,
    required bool completed,
    String? deviceInfo,
    String? location,
  }) async {
    try {
      debugPrint('📊 Tracking enhanced view for story: $storyId');
      debugPrint('   - Viewer: $viewerId');
      debugPrint('   - Duration: ${viewDuration.inSeconds}s');
      debugPrint('   - Completed: $completed');

      // Get story owner ID first
      final storyDoc = await _firestore
          .collection('stories')
          .doc(storyId)
          .get();
      if (!storyDoc.exists) {
        debugPrint('❌ Story not found: $storyId');
        return;
      }

      final storyOwnerId = storyDoc.data()?['userId'] ?? '';
      if (storyOwnerId.isEmpty) {
        debugPrint('❌ Story owner ID not found');
        return;
      }

      // Use the enhanced StoryViewerService for tracking
      final viewerService = StoryViewerService();
      final success = await viewerService.trackStoryView(
        storyId: storyId,
        storyOwnerId: storyOwnerId,
        viewDuration: viewDuration,
        completed: completed,
        deviceInfo: deviceInfo,
        location: location,
      );

      if (success) {
        debugPrint('✅ Enhanced view tracking completed successfully');
      } else {
        // Check if this is a self-view (story owner viewing their own story)
        final currentUser = FirebaseAuth.instance.currentUser;
        if (currentUser != null && currentUser.uid == storyOwnerId) {
          debugPrint(
            'ℹ️ Enhanced view tracking skipped (story owner self-view)',
          );
        } else {
          debugPrint('❌ Enhanced view tracking failed');
        }
      }
    } catch (e) {
      debugPrint('❌ Error tracking enhanced view: $e');
    }
  }

  // Update story privacy settings
  Future<void> updateStoryPrivacy(
    String storyId,
    StoryPrivacySettings settings,
  ) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    // Verify story ownership
    final storyDoc = await _firestore.collection('stories').doc(storyId).get();
    if (!storyDoc.exists || storyDoc.data()!['userId'] != currentUser.uid) {
      throw Exception('Unauthorized to modify this story');
    }

    await _firestore.collection('stories').doc(storyId).update({
      'privacy': settings.privacy.name,
      'allowedViewers': settings.allowedViewers,
      'blockedViewers': settings.blockedViewers,
      'allowReplies': settings.allowReplies,
      'allowReactions': settings.allowReactions,
      'allowScreenshots': settings.allowScreenshots,
      'updatedAt': FieldValue.serverTimestamp(),
    });

    // Log analytics
    await _analyticsService.logStoryPrivacyChanged(
      storyId: storyId,
      privacy: settings.privacy.name,
      userId: currentUser.uid,
    );
  }

  // Report a story
  Future<void> reportStory(
    String storyId,
    StoryReportReason reason,
    String? additionalInfo,
  ) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    final reportData = {
      'storyId': storyId,
      'reportedBy': currentUser.uid,
      'reason': reason.name,
      'additionalInfo': additionalInfo,
      'timestamp': FieldValue.serverTimestamp(),
      'status': 'pending',
      'reviewedBy': null,
      'reviewedAt': null,
      'action': null,
    };

    await _firestore.collection('story_reports').add(reportData);

    // Update story report count
    await _firestore.collection('stories').doc(storyId).update({
      'reportCount': FieldValue.increment(1),
      'lastReportedAt': FieldValue.serverTimestamp(),
    });

    // Log analytics
    await _analyticsService.logStoryReported(
      storyId: storyId,
      reason: reason.name,
      reportedBy: currentUser.uid,
    );
  }

  // Mark reply as read
  Future<void> markReplyAsRead(String storyId, String replyId) async {
    await _firestore
        .collection('stories')
        .doc(storyId)
        .collection('replies')
        .doc(replyId)
        .update({'isRead': true, 'readAt': FieldValue.serverTimestamp()});
  }

  // Reply to a story reply (story owner can reply to replies)
  Future<void> replyToReply(
    String storyId,
    String replyId,
    String content,
  ) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    // Verify story ownership
    final storyDoc = await _firestore.collection('stories').doc(storyId).get();
    if (!storyDoc.exists || storyDoc.data()!['userId'] != currentUser.uid) {
      throw Exception('Unauthorized to reply to this story');
    }

    // Mark original reply as replied
    await _firestore
        .collection('stories')
        .doc(storyId)
        .collection('replies')
        .doc(replyId)
        .update({'isReplied': true, 'repliedAt': FieldValue.serverTimestamp()});

    // Add the reply
    await addReply(storyId, content);
  }

  // Get user profile helper
  Future<Map<String, dynamic>> _getUserProfile(String userId) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      return userDoc.data() ?? {};
    } catch (e) {
      debugPrint('Error getting user profile: $e');
      return {};
    }
  }
}
