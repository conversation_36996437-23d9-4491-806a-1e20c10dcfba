import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For Clipboard and ClipboardData
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;
import 'dart:io';
import '../models/unified_story_model.dart';

class StorySharingService {
  static const String _appName = 'Billionaires Social';
  static const String _appUrl = 'https://billionaires-social.app';
  static const String _deepLinkBase = 'billionaires-social://story';

  /// Share story to other apps
  static Future<void> shareStory({
    required UnifiedStory story,
    required String storyImagePath,
    String? customMessage,
  }) async {
    try {
      final message = customMessage ?? _generateShareMessage(story);
      final subject = 'Check out this story on $_appName!';

      await SharePlus.instance.share(
        ShareParams(
          files: [XFile(storyImagePath)],
          text: message,
          subject: subject,
        ),
      );

      debugPrint('✅ Story shared successfully');
    } catch (e) {
      debugPrint('❌ Error sharing story: $e');
      rethrow;
    }
  }

  /// Share story as text only
  static Future<void> shareStoryText({
    required UnifiedStory story,
    String? customMessage,
  }) async {
    try {
      final message = customMessage ?? _generateShareMessage(story);

      await SharePlus.instance.share(
        ShareParams(
          text: message,
          subject: 'Check out this story on $_appName!',
        ),
      );

      debugPrint('✅ Story text shared successfully');
    } catch (e) {
      debugPrint('❌ Error sharing story text: $e');
      rethrow;
    }
  }

  /// Generate shareable link for story
  static String generateShareLink(UnifiedStory story) {
    return '$_deepLinkBase/${story.id}';
  }

  /// Generate shareable web link (if web version exists)
  static String generateWebLink(UnifiedStory story) {
    return '$_appUrl/story/${story.id}';
  }

  /// Share story to specific platform
  static Future<void> shareToPlatform({
    required UnifiedStory story,
    required String platform,
    String? customMessage,
  }) async {
    try {
      final message = customMessage ?? _generateShareMessage(story);
      final url = generateWebLink(story);

      switch (platform.toLowerCase()) {
        case 'whatsapp':
          await _shareToWhatsApp(message, url);
          break;
        case 'telegram':
          await _shareToTelegram(message, url);
          break;
        case 'twitter':
          await _shareToTwitter(message, url);
          break;
        case 'facebook':
          await _shareToFacebook(message, url);
          break;
        case 'instagram':
          await _shareToInstagram(message, url);
          break;
        case 'email':
          await _shareToEmail(message, url);
          break;
        case 'sms':
          await _shareToSMS(message, url);
          break;
        default:
          throw Exception('Unsupported platform: $platform');
      }

      debugPrint('✅ Story shared to $platform successfully');
    } catch (e) {
      debugPrint('❌ Error sharing to $platform: $e');
      rethrow;
    }
  }

  /// Download story media
  static Future<String> downloadStoryMedia(UnifiedStory story) async {
    try {
      final response = await http.get(Uri.parse(story.mediaUrl));
      if (response.statusCode != 200) {
        throw Exception('Failed to download media');
      }

      final directory = await getTemporaryDirectory();
      final fileName =
          'story_${story.id}_${DateTime.now().millisecondsSinceEpoch}';
      final extension = _getFileExtension(story.mediaUrl);
      final filePath = '${directory.path}/$fileName.$extension';

      final file = File(filePath);
      await file.writeAsBytes(response.bodyBytes);

      debugPrint('✅ Story media downloaded to: $filePath');
      return filePath;
    } catch (e) {
      debugPrint('❌ Error downloading story media: $e');
      rethrow;
    }
  }

  /// Generate story preview image
  static Future<String> generateStoryPreview({
    required UnifiedStory story,
    required Size size,
  }) async {
    try {
      // Download the original media first
      final originalMediaPath = await downloadStoryMedia(story);

      // For now, return the downloaded media path
      // In a full implementation, you would:
      // 1. Load the image/video frame
      // 2. Apply any filters or effects from the story
      // 3. Overlay text, stickers, or other elements
      // 4. Resize to the specified size
      // 5. Save the composite image
      // 6. Return the path to the generated preview

      debugPrint(
        '✅ Story preview generated (using original media): $originalMediaPath',
      );
      return originalMediaPath;
    } catch (e) {
      debugPrint('❌ Error generating story preview: $e');
      // Fallback to original media URL if preview generation fails
      return story.mediaUrl;
    }
  }

  /// Copy story link to clipboard
  static Future<void> copyStoryLink(UnifiedStory story) async {
    try {
      final link = generateWebLink(story);
      await Clipboard.setData(ClipboardData(text: link));

      debugPrint('✅ Story link copied to clipboard');
    } catch (e) {
      debugPrint('❌ Error copying story link: $e');
      rethrow;
    }
  }

  /// Share story to WhatsApp
  static Future<void> _shareToWhatsApp(String message, String url) async {
    final whatsappUrl =
        'whatsapp://send?text=${Uri.encodeComponent('$message\n\n$url')}';
    if (await canLaunchUrl(Uri.parse(whatsappUrl))) {
      await launchUrl(Uri.parse(whatsappUrl));
    } else {
      throw Exception('WhatsApp not installed');
    }
  }

  /// Share story to Telegram
  static Future<void> _shareToTelegram(String message, String url) async {
    final telegramUrl =
        'https://t.me/share/url?url=${Uri.encodeComponent(url)}&text=${Uri.encodeComponent(message)}';
    if (await canLaunchUrl(Uri.parse(telegramUrl))) {
      await launchUrl(Uri.parse(telegramUrl));
    } else {
      throw Exception('Telegram not installed');
    }
  }

  /// Share story to Twitter
  static Future<void> _shareToTwitter(String message, String url) async {
    final twitterUrl =
        'https://twitter.com/intent/tweet?text=${Uri.encodeComponent(message)}&url=${Uri.encodeComponent(url)}';
    if (await canLaunchUrl(Uri.parse(twitterUrl))) {
      await launchUrl(Uri.parse(twitterUrl));
    } else {
      throw Exception('Twitter not available');
    }
  }

  /// Share story to Facebook
  static Future<void> _shareToFacebook(String message, String url) async {
    final facebookUrl =
        'https://www.facebook.com/sharer/sharer.php?u=${Uri.encodeComponent(url)}&quote=${Uri.encodeComponent(message)}';
    if (await canLaunchUrl(Uri.parse(facebookUrl))) {
      await launchUrl(Uri.parse(facebookUrl));
    } else {
      throw Exception('Facebook not available');
    }
  }

  /// Share story to Instagram
  static Future<void> _shareToInstagram(String message, String url) async {
    // Instagram doesn't support direct sharing via URL
    // This would typically open Instagram with the image in clipboard
    final instagramUrl =
        'instagram://library?AssetPath=${Uri.encodeComponent(url)}';
    if (await canLaunchUrl(Uri.parse(instagramUrl))) {
      await launchUrl(Uri.parse(instagramUrl));
    } else {
      throw Exception('Instagram not installed');
    }
  }

  /// Share story via email
  static Future<void> _shareToEmail(String message, String url) async {
    final emailUrl =
        'mailto:?subject=${Uri.encodeComponent('Check out this story!')}&body=${Uri.encodeComponent('$message\n\n$url')}';
    if (await canLaunchUrl(Uri.parse(emailUrl))) {
      await launchUrl(Uri.parse(emailUrl));
    } else {
      throw Exception('Email app not available');
    }
  }

  /// Share story via SMS
  static Future<void> _shareToSMS(String message, String url) async {
    final smsUrl = 'sms:?body=${Uri.encodeComponent('$message\n\n$url')}';
    if (await canLaunchUrl(Uri.parse(smsUrl))) {
      await launchUrl(Uri.parse(smsUrl));
    } else {
      throw Exception('SMS app not available');
    }
  }

  /// Generate share message
  static String _generateShareMessage(UnifiedStory story) {
    final userName = story.userName;
    final hashtags = story.hashtags.isNotEmpty
        ? ' ${story.hashtags.map((tag) => '#$tag').join(' ')}'
        : '';

    return 'Check out this story by $userName on $_appName!$hashtags';
  }

  /// Get file extension from URL
  static String _getFileExtension(String url) {
    final uri = Uri.parse(url);
    final path = uri.path;
    final extension = path.split('.').last;
    return extension.isNotEmpty ? extension : 'jpg';
  }
}

/// Share options for stories
class StoryShareOptions {
  final bool includeImage;
  final bool includeText;
  final bool includeLink;
  final String? customMessage;
  final List<String> platforms;

  const StoryShareOptions({
    this.includeImage = true,
    this.includeText = true,
    this.includeLink = true,
    this.customMessage,
    this.platforms = const [],
  });
}

/// Share platform information
class SharePlatform {
  final String name;
  final String displayName;
  final IconData icon;
  final Color color;
  final bool requiresImage;

  const SharePlatform({
    required this.name,
    required this.displayName,
    required this.icon,
    required this.color,
    this.requiresImage = false,
  });
}

/// Available share platforms
class SharePlatforms {
  static const List<SharePlatform> available = [
    SharePlatform(
      name: 'whatsapp',
      displayName: 'WhatsApp',
      icon: Icons.chat_bubble,
      color: Color(0xFF25D366),
    ),
    SharePlatform(
      name: 'telegram',
      displayName: 'Telegram',
      icon: Icons.send,
      color: Color(0xFF0088CC),
    ),
    SharePlatform(
      name: 'twitter',
      displayName: 'Twitter',
      icon: Icons.flutter_dash,
      color: Color(0xFF1DA1F2),
    ),
    SharePlatform(
      name: 'facebook',
      displayName: 'Facebook',
      icon: Icons.facebook,
      color: Color(0xFF1877F2),
    ),
    SharePlatform(
      name: 'instagram',
      displayName: 'Instagram',
      icon: Icons.camera_alt,
      color: Color(0xFFE4405F),
      requiresImage: true,
    ),
    SharePlatform(
      name: 'email',
      displayName: 'Email',
      icon: Icons.email,
      color: Color(0xFFEA4335),
    ),
    SharePlatform(
      name: 'sms',
      displayName: 'SMS',
      icon: Icons.sms,
      color: Color(0xFF34A853),
    ),
  ];

  static SharePlatform? getByName(String name) {
    try {
      return available.firstWhere((platform) => platform.name == name);
    } catch (e) {
      return null;
    }
  }
}
