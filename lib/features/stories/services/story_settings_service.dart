import 'package:billionaires_social/features/stories/models/story_settings_model.dart';

class StorySettingsService {
  StorySettings? _settings;

  Future<StorySettings> getSettings() async {
    await Future.delayed(const Duration(milliseconds: 200));
    return _settings ??= StorySettings(
      id: 'default',
      duration: StoryDuration.twentyFourHours,
      visibility: StoryVisibility.public,
      hiddenFromUserIds: [],
      allowEditing: true,
      allowComments: true,
      allowReactions: true,
      allowMentions: true,
      allowMusic: true,
      allowFilters: true,
      allowTextOverlays: true,
      allowVideoUpload: true,
      allowedGroupIds: [],
    );
  }

  Future<void> updateSettings(StorySettings settings) async {
    await Future.delayed(const Duration(milliseconds: 200));
    _settings = settings;
  }
}
