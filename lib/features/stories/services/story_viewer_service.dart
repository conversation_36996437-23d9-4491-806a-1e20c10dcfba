import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:billionaires_social/features/stories/models/story_view.dart';

/// Enhanced story viewer service with comprehensive tracking and analytics
class StoryViewerService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Track a story view with comprehensive data and duplicate prevention
  Future<bool> trackStoryView({
    required String storyId,
    required String storyOwnerId,
    required Duration viewDuration,
    required bool completed,
    String? deviceInfo,
    String? location,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        debugPrint('❌ StoryViewerService: No authenticated user');
        return false;
      }

      final viewerId = currentUser.uid;

      // ✅ 1. Exclude story owner from viewer count
      if (viewerId == storyOwnerId) {
        debugPrint('⚠️ StoryViewerService: Ignoring self-view by story owner');
        return false;
      }

      debugPrint('📊 StoryViewerService: Tracking view for story: $storyId');
      debugPrint('   - Viewer: $viewerId');
      debugPrint('   - Owner: $storyOwnerId');
      debugPrint('   - Duration: ${viewDuration.inSeconds}s');
      debugPrint('   - Completed: $completed');

      // Get viewer profile information (simplified for now)
      final viewerProfile = await _getSimpleUserProfile(viewerId);
      if (viewerProfile == null) {
        debugPrint('❌ StoryViewerService: Could not fetch viewer profile');
        return false;
      }

      // Get relationship information (simplified for now)
      final isFollowing = false; // TODO: Implement follow checking
      final isFollower = false; // TODO: Implement follower checking
      final isMutualFollower = false;

      // Check if viewer is in close friends (this would need to be implemented)
      final isCloseFriend = false; // TODO: Implement close friends check

      final viewId = '${storyId}_$viewerId';
      final viewRef = _firestore
          .collection('stories')
          .doc(storyId)
          .collection('views')
          .doc(viewId);

      // ✅ 2. Prevent duplicate viewer counts - use document ID to ensure uniqueness
      final existingView = await viewRef.get();
      final isNewView = !existingView.exists;

      final storyView = StoryView(
        id: viewId,
        storyId: storyId,
        userId: viewerId,
        username: viewerProfile['username'] ?? 'user_$viewerId',
        displayName: viewerProfile['name'] ?? 'User',
        profilePictureUrl: viewerProfile['profilePictureUrl'],
        viewedAt: DateTime.now(),
        viewDuration: viewDuration,
        completed: completed,
        deviceInfo: deviceInfo,
        location: location,
        isFollower: isFollower,
        isFollowing: isFollowing,
        isCloseFriend: isCloseFriend,
        isMutualFollower: isMutualFollower,
      );

      // Use transaction to ensure data consistency
      await _firestore.runTransaction((transaction) async {
        // Update or create the view document
        transaction.set(
          viewRef,
          storyView.toFirestore(),
          SetOptions(merge: true),
        );

        // Update story statistics only for new views
        if (isNewView) {
          final storyRef = _firestore.collection('stories').doc(storyId);
          final storyDoc = await transaction.get(storyRef);

          if (storyDoc.exists) {
            final currentData = storyDoc.data()!;
            final currentViewCount = currentData['viewCount'] ?? 0;
            final currentUniqueViewers = currentData['uniqueViewers'] ?? 0;
            final currentViewedBy = List<String>.from(
              currentData['viewedBy'] ?? [],
            );

            // Add viewer to the list if not already present
            if (!currentViewedBy.contains(viewerId)) {
              currentViewedBy.add(viewerId);
            }

            transaction.update(storyRef, {
              'viewCount': currentViewCount + 1,
              'uniqueViewers': currentUniqueViewers + 1,
              'viewedBy': currentViewedBy,
              'lastViewedAt': FieldValue.serverTimestamp(),
            });

            debugPrint(
              '✅ StoryViewerService: Updated story stats - Views: ${currentViewCount + 1}',
            );
          }
        } else {
          debugPrint(
            '📊 StoryViewerService: Updated existing view (no count increment)',
          );
        }
      });

      return true;
    } catch (e) {
      debugPrint('❌ StoryViewerService: Error tracking view: $e');
      return false;
    }
  }

  /// Get comprehensive viewer statistics for a story
  Future<StoryViewerStats?> getStoryViewerStats({
    required String storyId,
    ViewerSortType sortType = ViewerSortType.mostRecent,
    ViewerFilterType filterType = ViewerFilterType.all,
    int limit = 50,
  }) async {
    try {
      debugPrint(
        '📊 StoryViewerService: Getting viewer stats for story: $storyId',
      );

      // Get all views for the story
      Query viewsQuery = _firestore
          .collection('stories')
          .doc(storyId)
          .collection('views');

      // Apply sorting
      switch (sortType) {
        case ViewerSortType.mostRecent:
          viewsQuery = viewsQuery.orderBy('viewedAt', descending: true);
          break;
        case ViewerSortType.oldest:
          viewsQuery = viewsQuery.orderBy('viewedAt', descending: false);
          break;
        case ViewerSortType.viewDuration:
          viewsQuery = viewsQuery.orderBy('viewDuration', descending: true);
          break;
        default:
          viewsQuery = viewsQuery.orderBy('viewedAt', descending: true);
      }

      final viewsSnapshot = await viewsQuery.limit(limit).get();
      final allViews = viewsSnapshot.docs
          .map((doc) => StoryView.fromFirestore(doc))
          .toList();

      // Apply filters
      List<StoryView> filteredViews = allViews;
      switch (filterType) {
        case ViewerFilterType.followers:
          filteredViews = allViews.where((view) => view.isFollower).toList();
          break;
        case ViewerFilterType.nonFollowers:
          filteredViews = allViews.where((view) => !view.isFollower).toList();
          break;
        case ViewerFilterType.closeFriends:
          filteredViews = allViews.where((view) => view.isCloseFriend).toList();
          break;
        case ViewerFilterType.mutualFollowers:
          filteredViews = allViews
              .where((view) => view.isMutualFollower)
              .toList();
          break;
        case ViewerFilterType.completed:
          filteredViews = allViews.where((view) => view.completed).toList();
          break;
        case ViewerFilterType.incomplete:
          filteredViews = allViews.where((view) => !view.completed).toList();
          break;
        case ViewerFilterType.all:
          // No filtering
          break;
      }

      // Apply additional sorting for specific types
      if (sortType == ViewerSortType.followersFirst) {
        filteredViews.sort((a, b) {
          if (a.isFollower && !b.isFollower) return -1;
          if (!a.isFollower && b.isFollower) return 1;
          return b.viewedAt.compareTo(a.viewedAt);
        });
      } else if (sortType == ViewerSortType.closeFriendsFirst) {
        filteredViews.sort((a, b) {
          if (a.isCloseFriend && !b.isCloseFriend) return -1;
          if (!a.isCloseFriend && b.isCloseFriend) return 1;
          return b.viewedAt.compareTo(a.viewedAt);
        });
      } else if (sortType == ViewerSortType.alphabetical) {
        filteredViews.sort((a, b) => a.displayName.compareTo(b.displayName));
      }

      // Calculate statistics
      final totalViews = allViews.length;
      final uniqueViewers =
          allViews.length; // Each document represents a unique viewer
      final recentViewers = filteredViews.take(10).toList();
      final lastViewedAt = allViews.isNotEmpty
          ? allViews.first.viewedAt
          : DateTime.now();

      final totalDuration = allViews.fold<Duration>(
        Duration.zero,
        (total, view) => total + view.viewDuration,
      );
      final averageViewDuration = totalViews > 0
          ? Duration(seconds: totalDuration.inSeconds ~/ totalViews)
          : Duration.zero;

      final completedViews = allViews.where((view) => view.completed).length;
      final completionRate = totalViews > 0
          ? (completedViews / totalViews) * 100
          : 0.0;

      final followerViews = allViews.where((view) => view.isFollower).length;
      final nonFollowerViews = totalViews - followerViews;
      final closeFriendViews = allViews
          .where((view) => view.isCloseFriend)
          .length;

      final stats = StoryViewerStats(
        storyId: storyId,
        totalViews: totalViews,
        uniqueViewers: uniqueViewers,
        recentViewers: recentViewers,
        allViewers: filteredViews,
        lastViewedAt: lastViewedAt,
        averageViewDuration: averageViewDuration,
        completionRate: completionRate,
        followerViews: followerViews,
        nonFollowerViews: nonFollowerViews,
        closeFriendViews: closeFriendViews,
      );

      debugPrint('✅ StoryViewerService: Stats calculated:');
      debugPrint('   - Total views: $totalViews');
      debugPrint('   - Unique viewers: $uniqueViewers');
      debugPrint('   - Completion rate: ${completionRate.toStringAsFixed(1)}%');
      debugPrint('   - Follower views: $followerViews');

      return stats;
    } catch (e) {
      debugPrint('❌ StoryViewerService: Error getting viewer stats: $e');
      return null;
    }
  }

  /// Get paginated viewers list
  Future<List<StoryView>> getViewersPaginated({
    required String storyId,
    DocumentSnapshot? lastDocument,
    ViewerSortType sortType = ViewerSortType.mostRecent,
    ViewerFilterType filterType = ViewerFilterType.all,
    int limit = 20,
  }) async {
    try {
      Query query = _firestore
          .collection('stories')
          .doc(storyId)
          .collection('views');

      // Apply sorting
      switch (sortType) {
        case ViewerSortType.mostRecent:
          query = query.orderBy('viewedAt', descending: true);
          break;
        case ViewerSortType.oldest:
          query = query.orderBy('viewedAt', descending: false);
          break;
        case ViewerSortType.viewDuration:
          query = query.orderBy('viewDuration', descending: true);
          break;
        default:
          query = query.orderBy('viewedAt', descending: true);
      }

      // Apply pagination
      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      final snapshot = await query.limit(limit).get();
      return snapshot.docs.map((doc) => StoryView.fromFirestore(doc)).toList();
    } catch (e) {
      debugPrint('❌ StoryViewerService: Error getting paginated viewers: $e');
      return [];
    }
  }

  /// Get simple user profile information
  Future<Map<String, dynamic>?> _getSimpleUserProfile(String userId) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (userDoc.exists) {
        final data = userDoc.data()!;
        return {
          'username': data['username'] ?? 'user_$userId',
          'name': data['name'] ?? 'User',
          'profilePictureUrl': data['profilePictureUrl'],
        };
      }
      return null;
    } catch (e) {
      debugPrint('❌ StoryViewerService: Error getting user profile: $e');
      return null;
    }
  }
}
