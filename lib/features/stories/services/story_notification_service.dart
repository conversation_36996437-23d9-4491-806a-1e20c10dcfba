import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

class StoryNotificationService {
  static final StoryNotificationService _instance =
      StoryNotificationService._internal();
  factory StoryNotificationService() => _instance;
  StoryNotificationService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();

  // Initialize notification service
  Future<void> initialize() async {
    // Request permission for notifications
    NotificationSettings settings = await _messaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      debugPrint('User granted permission for notifications');
    } else {
      debugPrint(
        'User declined or has not accepted permission for notifications',
      );
    }

    // Initialize local notifications
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
          requestAlertPermission: true,
          requestBadgePermission: true,
          requestSoundPermission: true,
        );

    const InitializationSettings initializationSettings =
        InitializationSettings(
          android: initializationSettingsAndroid,
          iOS: initializationSettingsIOS,
        );

    await _localNotifications.initialize(initializationSettings);

    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle notification taps
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);
  }

  // Send notification for story reaction
  Future<void> sendReactionNotification({
    required String storyId,
    required String storyOwnerId,
    required String reactorId,
    required String reactorName,
    required String reactionType,
  }) async {
    if (storyOwnerId == reactorId) return; // Don't notify self

    try {
      // Get story owner's FCM token
      final storyOwnerDoc = await _firestore
          .collection('users')
          .doc(storyOwnerId)
          .get();
      final fcmToken = storyOwnerDoc.data()?['fcmToken'];

      if (fcmToken != null) {
        // Send push notification
        await _sendPushNotification(
          token: fcmToken,
          title: 'New Reaction',
          body: '$reactorName reacted to your story',
          data: {
            'type': 'story_reaction',
            'storyId': storyId,
            'reactorId': reactorId,
            'reactionType': reactionType,
          },
        );
      }

      // Save notification to Firestore
      await _firestore.collection('notifications').add({
        'userId': storyOwnerId,
        'type': 'story_reaction',
        'title': 'New Reaction',
        'body': '$reactorName reacted to your story',
        'storyId': storyId,
        'reactorId': reactorId,
        'reactorName': reactorName,
        'reactionType': reactionType,
        'timestamp': FieldValue.serverTimestamp(),
        'isRead': false,
      });
    } catch (e) {
      debugPrint('Error sending reaction notification: $e');
    }
  }

  // Send notification for story reply
  Future<void> sendReplyNotification({
    required String storyId,
    required String storyOwnerId,
    required String replierId,
    required String replierName,
    required String replyMessage,
  }) async {
    if (storyOwnerId == replierId) return; // Don't notify self

    try {
      // Get story owner's FCM token
      final storyOwnerDoc = await _firestore
          .collection('users')
          .doc(storyOwnerId)
          .get();
      final fcmToken = storyOwnerDoc.data()?['fcmToken'];

      if (fcmToken != null) {
        // Send push notification
        await _sendPushNotification(
          token: fcmToken,
          title: 'New Reply',
          body: '$replierName replied to your story',
          data: {
            'type': 'story_reply',
            'storyId': storyId,
            'replierId': replierId,
            'replyMessage': replyMessage,
          },
        );
      }

      // Save notification to Firestore
      await _firestore.collection('notifications').add({
        'userId': storyOwnerId,
        'type': 'story_reply',
        'title': 'New Reply',
        'body': '$replierName replied to your story',
        'storyId': storyId,
        'replierId': replierId,
        'replierName': replierName,
        'replyMessage': replyMessage,
        'timestamp': FieldValue.serverTimestamp(),
        'isRead': false,
      });
    } catch (e) {
      debugPrint('Error sending reply notification: $e');
    }
  }

  // Send notification for story view (optional, for close friends or high engagement)
  Future<void> sendViewNotification({
    required String storyId,
    required String storyOwnerId,
    required String viewerId,
    required String viewerName,
  }) async {
    if (storyOwnerId == viewerId) return; // Don't notify self

    // Only send view notifications for close friends or if user has enabled this setting
    final storyOwnerDoc = await _firestore
        .collection('users')
        .doc(storyOwnerId)
        .get();
    final settings = storyOwnerDoc.data()?['notificationSettings'] ?? {};
    final notifyOnViews = settings['notifyOnStoryViews'] ?? false;

    if (!notifyOnViews) return;

    try {
      final fcmToken = storyOwnerDoc.data()?['fcmToken'];

      if (fcmToken != null) {
        await _sendPushNotification(
          token: fcmToken,
          title: 'Story Viewed',
          body: '$viewerName viewed your story',
          data: {
            'type': 'story_view',
            'storyId': storyId,
            'viewerId': viewerId,
          },
        );
      }

      // Save notification to Firestore
      await _firestore.collection('notifications').add({
        'userId': storyOwnerId,
        'type': 'story_view',
        'title': 'Story Viewed',
        'body': '$viewerName viewed your story',
        'storyId': storyId,
        'viewerId': viewerId,
        'viewerName': viewerName,
        'timestamp': FieldValue.serverTimestamp(),
        'isRead': false,
      });
    } catch (e) {
      debugPrint('Error sending view notification: $e');
    }
  }

  // Send notification for story mention
  Future<void> sendMentionNotification({
    required String storyId,
    required String storyOwnerId,
    required String mentionedUserId,
    required String mentionedUserName,
  }) async {
    if (storyOwnerId == mentionedUserId) return; // Don't notify self

    try {
      final mentionedUserDoc = await _firestore
          .collection('users')
          .doc(mentionedUserId)
          .get();
      final fcmToken = mentionedUserDoc.data()?['fcmToken'];

      if (fcmToken != null) {
        await _sendPushNotification(
          token: fcmToken,
          title: 'You were mentioned',
          body: 'You were mentioned in a story',
          data: {
            'type': 'story_mention',
            'storyId': storyId,
            'storyOwnerId': storyOwnerId,
          },
        );
      }

      // Save notification to Firestore
      await _firestore.collection('notifications').add({
        'userId': mentionedUserId,
        'type': 'story_mention',
        'title': 'You were mentioned',
        'body': 'You were mentioned in a story',
        'storyId': storyId,
        'storyOwnerId': storyOwnerId,
        'storyOwnerName': storyOwnerId, // TODO: Get actual name
        'timestamp': FieldValue.serverTimestamp(),
        'isRead': false,
      });
    } catch (e) {
      debugPrint('Error sending mention notification: $e');
    }
  }

  // Send push notification using Firebase Cloud Messaging
  Future<void> _sendPushNotification({
    required String token,
    required String title,
    required String body,
    required Map<String, dynamic> data,
  }) async {
    // This would typically be done through a Cloud Function
    // For now, we'll just log the notification
    debugPrint('Sending push notification to $token: $title - $body');
    debugPrint('Notification data: $data');
  }

  // Handle foreground messages
  void _handleForegroundMessage(RemoteMessage message) {
    debugPrint('Got a message whilst in the foreground!');
    debugPrint('Message data: ${message.data}');

    if (message.notification != null) {
      debugPrint(
        'Message also contained a notification: ${message.notification}',
      );

      // Show local notification
      _showLocalNotification(
        title: message.notification!.title ?? 'New Notification',
        body: message.notification!.body ?? '',
        payload: message.data.toString(),
      );
    }
  }

  // Handle notification taps
  void _handleNotificationTap(RemoteMessage message) {
    debugPrint('Notification tapped: ${message.data}');

    // Navigate to appropriate screen based on notification type
    final type = message.data['type'];
    final storyId = message.data['storyId'];

    switch (type) {
      case 'story_reaction':
      case 'story_reply':
      case 'story_view':
      case 'story_mention':
        // Navigate to story viewer
        debugPrint('Navigate to story: $storyId');
        break;
      default:
        debugPrint('Unknown notification type: $type');
    }
  }

  // Show local notification
  Future<void> _showLocalNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
          'story_notifications',
          'Story Notifications',
          channelDescription: 'Notifications for story interactions',
          importance: Importance.max,
          priority: Priority.high,
        );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails();

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _localNotifications.show(
      DateTime.now().millisecondsSinceEpoch ~/ 1000,
      title,
      body,
      platformChannelSpecifics,
      payload: payload,
    );
  }

  // Update user's FCM token
  Future<void> updateFCMToken(String userId) async {
    try {
      final token = await _messaging.getToken();
      if (token != null) {
        await _firestore.collection('users').doc(userId).update({
          'fcmToken': token,
          'lastTokenUpdate': FieldValue.serverTimestamp(),
        });
        debugPrint('FCM token updated for user: $userId');
      }
    } catch (e) {
      debugPrint('Error updating FCM token: $e');
    }
  }

  // Get user's notification settings
  Future<Map<String, dynamic>> getNotificationSettings(String userId) async {
    try {
      final doc = await _firestore.collection('users').doc(userId).get();
      return doc.data()?['notificationSettings'] ?? {};
    } catch (e) {
      debugPrint('Error getting notification settings: $e');
      return {};
    }
  }

  // Update user's notification settings
  Future<void> updateNotificationSettings(
    String userId,
    Map<String, dynamic> settings,
  ) async {
    try {
      await _firestore.collection('users').doc(userId).update({
        'notificationSettings': settings,
      });
      debugPrint('Notification settings updated for user: $userId');
    } catch (e) {
      debugPrint('Error updating notification settings: $e');
    }
  }

  // Mark notification as read
  Future<void> markNotificationAsRead(String notificationId) async {
    try {
      await _firestore.collection('notifications').doc(notificationId).update({
        'isRead': true,
        'readAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error marking notification as read: $e');
    }
  }

  // Get user's unread notifications
  Stream<QuerySnapshot> getUnreadNotifications(String userId) {
    return _firestore
        .collection('notifications')
        .where('userId', isEqualTo: userId)
        .where('isRead', isEqualTo: false)
        .orderBy('timestamp', descending: true)
        .snapshots();
  }
}

// Background message handler
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  debugPrint('Handling a background message: ${message.messageId}');
  debugPrint('Message data: ${message.data}');
}
