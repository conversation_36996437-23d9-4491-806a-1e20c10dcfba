import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/story_model.dart';
import '../models/story_reel_model.dart';
import 'package:billionaires_social/core/services/firebase_service.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/features/notifications/services/notification_service.dart';
import 'package:billionaires_social/features/notifications/models/notification_model.dart';
import 'package:uuid/uuid.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';

/// Enhanced Story Service that combines the existing implementation with additional features
class EnhancedStoryService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseService _firebaseService = FirebaseService();
  final NotificationService _notificationService = getIt<NotificationService>();
  final Uuid _uuid = const Uuid();

  CollectionReference get _storiesCollection =>
      _firestore.collection('stories');

  /// Upload a story with enhanced features
  Future<String> uploadStory({
    required File file,
    required StoryMediaType mediaType,
    StoryPrivacy privacy = StoryPrivacy.public,
    List<String>? allowedTo,
    String? caption,
    String? location,
    List<String>? hashtags,
    List<String>? mentions,
    String? musicUrl,
    String? musicTitle,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('User must be logged in to upload a story');
      }

      // Get user profile for additional data
      final userProfile = await _firebaseService.getUserProfile(user.uid);
      final userName =
          userProfile?.name ?? userProfile?.username ?? 'Unknown User';
      final userAvatarUrl = userProfile?.profilePictureUrl ?? '';

      // Create unique filename
      final fileName = '${DateTime.now().millisecondsSinceEpoch}_${_uuid.v4()}';
      final storageRef = FirebaseStorage.instance.ref().child(
        'stories/${user.uid}/$fileName',
      );

      // Upload file
      final uploadTask = storageRef.putFile(file);
      final snapshot = await uploadTask.whenComplete(() => {});
      final downloadUrl = await snapshot.ref.getDownloadURL();

      // Calculate expiration (24 hours from now)
      final expiresAt = DateTime.now().add(const Duration(hours: 24));

      // Create story object
      final story = Story(
        id: '',
        userId: user.uid,
        userName: userName,
        userAvatarUrl: userAvatarUrl,
        mediaUrl: downloadUrl,
        mediaType: mediaType,
        createdAt: DateTime.now(),
        expiresAt: expiresAt,
        viewers: [],
        allowedTo: allowedTo ?? [],
        reactions: [],
        replies: [],
        privacy: privacy,
        caption: caption,
        location: location,
        hashtags: hashtags ?? [],
        mentions: mentions ?? [],
        isHighlighted: false,
        isArchived: false,
        musicUrl: musicUrl,
        musicTitle: musicTitle,
        storyType: StoryType.regular,
        metadata: metadata,
      );

      // Save to Firestore
      final docRef = await _storiesCollection.add(story.toFirestore());

      debugPrint('✅ Story uploaded successfully: ${docRef.id}');

      // Notify close friends if it's a close friends story
      if (privacy == StoryPrivacy.closeFriends) {
        await _notifyCloseFriends(docRef.id, userName);
      }

      return docRef.id;
    } catch (e) {
      debugPrint('❌ Error uploading story: $e');
      throw Exception('Failed to upload story: $e');
    }
  }

  /// Get stories with enhanced filtering and privacy controls
  Stream<List<Story>> getStories({
    StoryPrivacy? privacyFilter,
    bool includeExpired = false,
    int limit = 50,
  }) {
    final user = _auth.currentUser;
    if (user == null) {
      return Stream.value([]);
    }

    // Calculate cutoff time (24 hours ago)
    final cutoff = DateTime.now().subtract(const Duration(hours: 24));

    Query query = _storiesCollection
        .where('createdAt', isGreaterThanOrEqualTo: Timestamp.fromDate(cutoff))
        .orderBy('createdAt', descending: true)
        .limit(limit);

    // Apply privacy filter if specified
    if (privacyFilter != null) {
      query = query.where(
        'privacy',
        isEqualTo: privacyFilter.toString().split('.').last,
      );
    }

    return query.snapshots().map((snapshot) {
      final stories = snapshot.docs
          .map(
            (doc) => StoryExtensions.fromFirestore(
              doc.id,
              doc.data() as Map<String, dynamic>,
            ),
          )
          .toList();

      // Filter based on privacy settings
      return stories.where((story) {
        // Skip expired stories unless specifically requested
        if (!includeExpired && story.expiresAt.isBefore(DateTime.now())) {
          return false;
        }

        // Handle different privacy levels
        switch (story.privacy) {
          case StoryPrivacy.public:
            return true;
          case StoryPrivacy.followers:
            // TODO: Check if current user follows the story author
            return true;
          case StoryPrivacy.closeFriends:
            return story.allowedTo.contains(user.uid) ||
                story.userId == user.uid;
          case StoryPrivacy.custom:
            return story.allowedTo.contains(user.uid) ||
                story.userId == user.uid;
          case StoryPrivacy.private:
            return story.userId == user.uid;
        }
      }).toList();
    });
  }

  /// Mark story as viewed with detailed tracking
  Future<void> markStoryViewed(String storyId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      final now = Timestamp.now();

      // Get current story document to check if user already viewed it
      final storyDoc = await _storiesCollection.doc(storyId).get();
      if (!storyDoc.exists) return;

      final data = storyDoc.data() as Map<String, dynamic>;
      final currentViewerDetails = List<Map<String, dynamic>>.from(
        data['viewerDetails'] ?? [],
      );

      // Check if user already viewed this story
      final alreadyViewed = currentViewerDetails.any(
        (viewer) => viewer['viewerId'] == user.uid,
      );

      if (!alreadyViewed) {
        // Add to simple viewers array (for backward compatibility)
        await _storiesCollection.doc(storyId).update({
          'viewers': FieldValue.arrayUnion([user.uid]),
          'viewerDetails': FieldValue.arrayUnion([
            {
              'viewerId': user.uid,
              'timestamp': now,
              'viewedAt': now.toDate().toIso8601String(),
            },
          ]),
        });

        debugPrint(
          '✅ Marked story $storyId as viewed by ${user.uid} at ${now.toDate()}',
        );
      } else {
        debugPrint('ℹ️ Story $storyId already viewed by ${user.uid}');
      }
    } catch (e) {
      debugPrint('❌ Error marking story as viewed: $e');
    }
  }

  /// Add reaction to story
  Future<void> addStoryReaction(String storyId, String reactionType) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      final userProfile = await _firebaseService.getUserProfile(user.uid);
      final userName =
          userProfile?.name ?? userProfile?.username ?? 'Unknown User';
      final userAvatarUrl = userProfile?.profilePictureUrl ?? '';

      final reaction = StoryReaction(
        userId: user.uid,
        userName: userName,
        userAvatarUrl: userAvatarUrl,
        reactionType: reactionType,
        timestamp: DateTime.now(),
      );

      await _storiesCollection.doc(storyId).update({
        'reactions': FieldValue.arrayUnion([reaction.toJson()]),
      });

      debugPrint('✅ Added reaction $reactionType to story $storyId');
    } catch (e) {
      debugPrint('❌ Error adding story reaction: $e');
    }
  }

  /// Add reply to story
  Future<void> addStoryReply(String storyId, String message) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      final userProfile = await _firebaseService.getUserProfile(user.uid);
      final userName =
          userProfile?.name ?? userProfile?.username ?? 'Unknown User';
      final userAvatarUrl = userProfile?.profilePictureUrl ?? '';

      final reply = StoryReply(
        id: _uuid.v4(),
        userId: user.uid,
        userName: userName,
        userAvatarUrl: userAvatarUrl,
        message: message,
        timestamp: DateTime.now(),
        isRead: false,
      );

      await _storiesCollection.doc(storyId).update({
        'replies': FieldValue.arrayUnion([reply.toJson()]),
      });

      debugPrint('✅ Added reply to story $storyId');
    } catch (e) {
      debugPrint('❌ Error adding story reply: $e');
    }
  }

  /// Get story statistics with proper ownership validation
  Future<StoryStats> getStoryStats(String storyId) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User must be authenticated to view analytics');
      }

      final doc = await _storiesCollection.doc(storyId).get();
      if (!doc.exists) {
        throw Exception('Story not found');
      }

      final data = doc.data() as Map<String, dynamic>;
      final storyUserId = data['userId'] as String?;

      // Only allow story owner to view analytics
      if (storyUserId != currentUser.uid) {
        throw Exception('You can only view analytics for your own stories');
      }

      final story = StoryExtensions.fromFirestore(doc.id, data);

      final viewCount = story.viewers.length;
      final replyCount = story.replies.length;
      final reactionCount = story.reactions.length;

      // Calculate completion rate (simplified)
      final completionRate = viewCount > 0 ? 0.85 : 0.0; // Placeholder
      final skipRate = viewCount > 0 ? 0.15 : 0.0; // Placeholder

      // Get top viewers (first 10)
      final topViewers = story.viewers.take(10).toList();

      // Calculate reaction breakdown
      final reactionBreakdown = <String, int>{};
      for (final reaction in story.reactions) {
        reactionBreakdown[reaction.reactionType] =
            (reactionBreakdown[reaction.reactionType] ?? 0) + 1;
      }

      return StoryStats(
        viewCount: viewCount,
        replyCount: replyCount,
        reactionCount: reactionCount,
        shareCount: 0, // TODO: Implement share tracking
        completionRate: completionRate,
        skipRate: skipRate,
        topViewers: topViewers,
        reactionBreakdown: reactionBreakdown,
      );
    } catch (e) {
      debugPrint('❌ Error getting story stats: $e');
      if (e.toString().contains('permission-denied')) {
        throw Exception(
          'Permission denied: You can only view analytics for your own stories',
        );
      }
      throw Exception('Failed to get story analytics: $e');
    }
  }

  /// Archive a story
  Future<void> archiveStory(String storyId) async {
    try {
      await _storiesCollection.doc(storyId).update({'isArchived': true});

      debugPrint('✅ Archived story $storyId');
    } catch (e) {
      debugPrint('❌ Error archiving story: $e');
    }
  }

  /// Highlight a story
  Future<void> highlightStory(String storyId) async {
    try {
      await _storiesCollection.doc(storyId).update({'isHighlighted': true});

      debugPrint('✅ Highlighted story $storyId');
    } catch (e) {
      debugPrint('❌ Error highlighting story: $e');
    }
  }

  /// Delete a story
  Future<void> deleteStory(String storyId) async {
    try {
      final doc = await _storiesCollection.doc(storyId).get();
      if (!doc.exists) return;

      final data = doc.data() as Map<String, dynamic>;
      final mediaUrl = data['mediaUrl'] as String?;

      // Delete from Storage if media exists
      if (mediaUrl != null && mediaUrl.isNotEmpty) {
        try {
          final ref = FirebaseStorage.instance.refFromURL(mediaUrl);
          await ref.delete();
        } catch (e) {
          debugPrint('⚠️ Could not delete media file: $e');
        }
      }

      // Delete from Firestore
      await _storiesCollection.doc(storyId).delete();

      debugPrint('✅ Deleted story $storyId');
    } catch (e) {
      debugPrint('❌ Error deleting story: $e');
      throw Exception('Failed to delete story: $e');
    }
  }

  /// Notify close friends about new story
  Future<void> _notifyCloseFriends(String storyId, String authorName) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      // Get close friends
      final closeFriendsSnapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('closeFriends')
          .get();

      for (final friendDoc in closeFriendsSnapshot.docs) {
        await _notificationService.sendNotification(
          type: NotificationType.story,
          senderId: user.uid,
          senderName: authorName,
          senderAvatarUrl: '',
          userId: friendDoc.id,
          title: 'New Close Friends Story',
          body: '$authorName added a story for close friends',
          data: {
            'type': 'close_friends_story',
            'storyId': storyId,
            'authorId': user.uid,
          },
        );
      }

      debugPrint(
        '✅ Notified ${closeFriendsSnapshot.docs.length} close friends',
      );
    } catch (e) {
      debugPrint('❌ Error notifying close friends: $e');
    }
  }

  /// Get user's story highlights
  Stream<List<Story>> getUserHighlights(String userId) {
    return _storiesCollection
        .where('userId', isEqualTo: userId)
        .where('isHighlighted', isEqualTo: true)
        .where('isArchived', isEqualTo: false)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map(
                (doc) => StoryExtensions.fromFirestore(
                  doc.id,
                  doc.data() as Map<String, dynamic>,
                ),
              )
              .toList(),
        );
  }

  /// Get user's archived stories
  Stream<List<Story>> getUserArchivedStories(String userId) {
    return _storiesCollection
        .where('userId', isEqualTo: userId)
        .where('isArchived', isEqualTo: true)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map(
                (doc) => StoryExtensions.fromFirestore(
                  doc.id,
                  doc.data() as Map<String, dynamic>,
                ),
              )
              .toList(),
        );
  }

  // Compatibility methods for existing provider interface
  Future<List<StoryReel>> getStoryReels({List<String>? filteredUserIds}) async {
    try {
      final stories = await getStories().first;
      final reels = <StoryReel>[];

      // Group stories by user
      final userStories = <String, List<Story>>{};
      for (final story in stories) {
        // Apply user filter if provided
        if (filteredUserIds != null &&
            !filteredUserIds.contains(story.userId)) {
          continue;
        }
        userStories.putIfAbsent(story.userId, () => []).add(story);
      }

      for (final entry in userStories.entries) {
        final userId = entry.key;
        final userStories = entry.value;

        // Get user profile
        final userProfile = await _firebaseService.getUserProfile(userId);
        final username =
            userProfile?.name ?? userProfile?.username ?? 'Unknown User';
        final userAvatarUrl = userProfile?.profilePictureUrl ?? '';

        // Convert stories to StoryItems
        final storyItems = userStories
            .map(
              (story) => StoryItem(
                id: story.id,
                userId: story.userId,
                mediaUrl: story.mediaUrl,
                mediaType: _convertMediaType(story.mediaType),
                duration: const Duration(seconds: 5),
                timestamp: story.createdAt,
                textOverlay: story.caption,
                textColor: 0xFFFFFFFF, // White
                textSize: 24.0,
                textPosition: {'x': 100.0, 'y': 100.0},
                backgroundColor: 0x00000000, // Transparent
                filter: 'Normal',
                textElements: const [], // Empty for legacy stories
                privacy: story.privacy.toString().split('.').last,
                isPublic: story.privacy == StoryPrivacy.public,
                isSeen: story.viewers.isNotEmpty,
                isCloseFriend: story.privacy == StoryPrivacy.closeFriends,
              ),
            )
            .toList();

        final isAllViewed = storyItems.every((item) => item.isSeen == true);
        final isCloseFriend = await _isCloseFriend(userId);

        reels.add(
          StoryReel(
            id: userId,
            userId: userId,
            username: username,
            userAvatarUrl: userAvatarUrl,
            stories: storyItems,
            isAllViewed: isAllViewed,
            isCloseFriend: isCloseFriend,
          ),
        );
      }

      return reels;
    } catch (e) {
      debugPrint('❌ Error getting story reels: $e');
      return [];
    }
  }

  Future<void> markStoryAsViewed(String reelId) async {
    try {
      final stories = await _storiesCollection
          .where('userId', isEqualTo: reelId)
          .get();

      final currentUser = _auth.currentUser;
      if (currentUser == null) return;

      for (final doc in stories.docs) {
        await doc.reference.update({
          'viewers': FieldValue.arrayUnion([currentUser.uid]),
        });
      }

      debugPrint('✅ Marked all stories for user $reelId as viewed');
    } catch (e) {
      debugPrint('❌ Error marking stories as viewed: $e');
    }
  }

  Future<StoryItem?> getStoryById(String storyId) async {
    try {
      final doc = await _storiesCollection.doc(storyId).get();
      if (!doc.exists) return null;

      final data = doc.data() as Map<String, dynamic>;
      final story = StoryExtensions.fromFirestore(doc.id, data);

      return StoryItem(
        id: story.id,
        userId: story.userId,
        mediaUrl: story.mediaUrl,
        mediaType: _convertMediaType(story.mediaType),
        duration: const Duration(seconds: 5),
        timestamp: story.createdAt,
        textOverlay: story.caption,
        textColor: 0xFFFFFFFF,
        textSize: 24.0,
        textPosition: {'x': 100.0, 'y': 100.0},
        backgroundColor: 0x00000000,
        filter: 'Normal',
        textElements: const [], // Empty for legacy stories
        privacy: story.privacy.toString().split('.').last,
        isPublic: story.privacy == StoryPrivacy.public,
        isSeen: story.viewers.isNotEmpty,
        isCloseFriend: story.privacy == StoryPrivacy.closeFriends,
      );
    } catch (e) {
      debugPrint('❌ Error getting story by ID: $e');
      return null;
    }
  }

  Future<Map<String, String>> getUserDetails(String userId) async {
    try {
      final userProfile = await _firebaseService.getUserProfile(userId);
      return {
        'name': userProfile?.name ?? userProfile?.username ?? 'Unknown',
        'profilePictureUrl': userProfile?.profilePictureUrl ?? '',
      };
    } catch (e) {
      debugPrint('❌ Error getting user details: $e');
      return {'name': 'Unknown', 'profilePictureUrl': ''};
    }
  }

  Future<void> trackStoryView(String storyId, String viewerId) async {
    await markStoryViewed(storyId);
  }

  Future<Map<String, dynamic>> getStoryAnalytics(String storyId) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User must be authenticated to view analytics');
      }

      // Get story document directly to access viewerDetails
      final doc = await _storiesCollection.doc(storyId).get();
      if (!doc.exists) {
        throw Exception('Story not found');
      }

      final data = doc.data() as Map<String, dynamic>;
      final storyUserId = data['userId'] as String?;

      // Only allow story owner to view analytics
      if (storyUserId != currentUser.uid) {
        throw Exception('You can only view analytics for your own stories');
      }

      // Get real viewer details with timestamps
      final viewerDetails = List<Map<String, dynamic>>.from(
        data['viewerDetails'] ?? [],
      );
      final viewers = List<String>.from(data['viewers'] ?? []);

      // Get basic stats
      final stats = await getStoryStats(storyId);

      debugPrint('📊 Story analytics for $storyId:');
      debugPrint('   Total viewers: ${viewers.length}');
      debugPrint('   Viewer details: ${viewerDetails.length}');

      return {
        'totalViews': viewers.length,
        'uniqueViewers': viewers.length,
        'viewCount': viewers.length,
        'replyCount': stats.replyCount,
        'reactionCount': stats.reactionCount,
        'shareCount': stats.shareCount,
        'completionRate': stats.completionRate,
        'skipRate': stats.skipRate,
        'topViewers': stats.topViewers,
        'reactionBreakdown': stats.reactionBreakdown,
        'viewers': viewerDetails, // Use real viewer details with timestamps
      };
    } catch (e) {
      debugPrint('❌ Error getting story analytics: $e');
      rethrow; // Re-throw to let the UI handle the error properly
    }
  }

  Future<void> deleteExpiredStories() async {
    try {
      final cutoff = DateTime.now().subtract(const Duration(hours: 24));
      final expiredStories = await _storiesCollection
          .where('createdAt', isLessThan: Timestamp.fromDate(cutoff))
          .get();

      for (final doc in expiredStories.docs) {
        await doc.reference.delete();
      }

      debugPrint('✅ Deleted ${expiredStories.docs.length} expired stories');
    } catch (e) {
      debugPrint('❌ Error deleting expired stories: $e');
    }
  }

  Future<void> previewMusic(String musicPath) async {
    // TODO: Implement music preview
    debugPrint('🎵 Music preview not implemented yet');
  }

  Future<void> stopMusicPreview() async {
    // TODO: Implement music preview stop
    debugPrint('🔇 Music preview stop not implemented yet');
  }

  Future<List<LocationResult>> searchLocations(String query) async {
    // TODO: Implement location search
    debugPrint('📍 Location search not implemented yet');
    return [];
  }

  Future<LocationResult?> getCurrentLocation() async {
    // TODO: Implement current location
    debugPrint('📍 Current location not implemented yet');
    return null;
  }

  bool isStoryExpired(DateTime createdAt) {
    return DateTime.now().difference(createdAt).inHours >= 24;
  }

  Duration getTimeRemaining(DateTime createdAt) {
    final expirationTime = createdAt.add(const Duration(hours: 24));
    final remaining = expirationTime.difference(DateTime.now());
    return remaining.isNegative ? Duration.zero : remaining;
  }

  // Helper methods
  MediaType _convertMediaType(StoryMediaType mediaType) {
    switch (mediaType) {
      case StoryMediaType.image:
        return MediaType.image;
      case StoryMediaType.video:
        return MediaType.video;
      case StoryMediaType.text:
        return MediaType.text;
      default:
        return MediaType.image;
    }
  }

  Future<bool> _isCloseFriend(String userId) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return false;

      final closeFriendsDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .collection('closeFriends')
          .doc(userId)
          .get();

      return closeFriendsDoc.exists;
    } catch (e) {
      debugPrint('❌ Error checking close friend status: $e');
      return false;
    }
  }
}

// Provider for the enhanced story service
final enhancedStoryServiceProvider = Provider<EnhancedStoryService>((ref) {
  return EnhancedStoryService();
});
