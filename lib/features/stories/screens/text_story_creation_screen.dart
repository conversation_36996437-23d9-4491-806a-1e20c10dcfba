import 'package:billionaires_social/features/stories/providers/story_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class TextStoryCreationScreen extends ConsumerStatefulWidget {
  const TextStoryCreationScreen({super.key});

  @override
  ConsumerState<TextStoryCreationScreen> createState() =>
      _TextStoryCreationScreenState();
}

class _TextStoryCreationScreenState
    extends ConsumerState<TextStoryCreationScreen> {
  final TextEditingController _textController = TextEditingController();
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  Color _backgroundColor = Colors.black;
  Color _textColor = Colors.white;
  double _textSize = 32.0;
  String _selectedFont = 'Default';
  TextAlign _textAlign = TextAlign.center;
  bool _isBold = false;
  bool _isItalic = false;
  bool _isPublishing = false;

  final List<Color> _backgroundColors = [
    Colors.black,
    Colors.white,
    Colors.red,
    Colors.blue,
    Colors.green,
    Colors.yellow,
    Colors.purple,
    Colors.orange,
    Colors.pink,
    Colors.cyan,
    Colors.teal,
    Colors.indigo,
    Colors.amber,
    Colors.deepPurple,
    Colors.lightBlue,
    Colors.lime,
  ];

  final List<Color> _textColors = [
    Colors.white,
    Colors.black,
    Colors.red,
    Colors.blue,
    Colors.green,
    Colors.yellow,
    Colors.purple,
    Colors.orange,
    Colors.pink,
    Colors.cyan,
  ];

  final List<String> _fonts = [
    'Default',
    'Roboto',
    'OpenSans',
    'Lato',
    'Poppins',
    'Montserrat',
    'PlayfairDisplay',
    'DancingScript',
  ];

  @override
  void initState() {
    super.initState();
    _textController.text = 'Type your story here...';
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  void _showColorPicker(bool isBackground) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildColorPicker(isBackground),
    );
  }

  Widget _buildColorPicker(bool isBackground) {
    final colors = isBackground ? _backgroundColors : _textColors;
    final currentColor = isBackground ? _backgroundColor : _textColor;

    return Container(
      height: 200,
      decoration: const BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Text(
                  isBackground ? 'Background Color' : 'Text Color',
                  style: const TextStyle(color: Colors.white, fontSize: 18),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close, color: Colors.white),
                ),
              ],
            ),
          ),
          Expanded(
            child: GridView.builder(
              padding: const EdgeInsets.all(16),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 8,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
              ),
              itemCount: colors.length,
              itemBuilder: (context, index) {
                final color = colors[index];
                final isSelected = currentColor == color;
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      if (isBackground) {
                        _backgroundColor = color;
                      } else {
                        _textColor = color;
                      }
                    });
                    Navigator.pop(context);
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: color,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: isSelected ? Colors.white : Colors.transparent,
                        width: 3,
                      ),
                    ),
                    child: isSelected
                        ? const Icon(Icons.check, color: Colors.black)
                        : null,
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _showFontPicker() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildFontPicker(),
    );
  }

  Widget _buildFontPicker() {
    return Container(
      height: 300,
      decoration: const BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                const Text(
                  'Choose Font',
                  style: TextStyle(color: Colors.white, fontSize: 18),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close, color: Colors.white),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView.builder(
              itemCount: _fonts.length,
              itemBuilder: (context, index) {
                final font = _fonts[index];
                final isSelected = _selectedFont == font;
                return ListTile(
                  title: Text(
                    font,
                    style: TextStyle(
                      color: Colors.white,
                      fontFamily: font == 'Default' ? null : font,
                      fontWeight: isSelected
                          ? FontWeight.bold
                          : FontWeight.normal,
                    ),
                  ),
                  trailing: isSelected
                      ? const Icon(Icons.check, color: Colors.white)
                      : null,
                  onTap: () {
                    setState(() {
                      _selectedFont = font;
                    });
                    Navigator.pop(context);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _showTextAlignmentPicker() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildTextAlignmentPicker(),
    );
  }

  Widget _buildTextAlignmentPicker() {
    return Container(
      height: 200,
      decoration: const BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                const Text(
                  'Text Alignment',
                  style: TextStyle(color: Colors.white, fontSize: 18),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close, color: Colors.white),
                ),
              ],
            ),
          ),
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildAlignmentOption(Icons.format_align_left, TextAlign.left),
                _buildAlignmentOption(
                  Icons.format_align_center,
                  TextAlign.center,
                ),
                _buildAlignmentOption(
                  Icons.format_align_right,
                  TextAlign.right,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAlignmentOption(IconData icon, TextAlign alignment) {
    final isSelected = _textAlign == alignment;
    return GestureDetector(
      onTap: () {
        setState(() {
          _textAlign = alignment;
        });
        Navigator.pop(context);
      },
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: isSelected ? Colors.white : Colors.transparent,
          shape: BoxShape.circle,
          border: Border.all(color: Colors.white, width: 2),
        ),
        child: Icon(
          icon,
          color: isSelected ? Colors.black : Colors.white,
          size: 24,
        ),
      ),
    );
  }

  Future<void> _publishTextStory() async {
    if (_textController.text.trim().isEmpty ||
        _textController.text == 'Type your story here...') {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please add some text to your story')),
      );
      return;
    }

    setState(() {
      _isPublishing = true;
    });

    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Create story document in Firestore (text-based story)
      final storyData = {
        'userId': currentUser.uid,
        'mediaType': 'text',
        'textContent': _textController.text,
        'backgroundColor': _backgroundColor.toARGB32(),
        'textColor': _textColor.toARGB32(),
        'textSize': _textSize,
        'font': _selectedFont,
        'textAlign': _textAlign.name,
        'isBold': _isBold,
        'isItalic': _isItalic,
        'isPublic': true,
        'isSeen': false,
        'isCloseFriend': false,
        'createdAt': FieldValue.serverTimestamp(),
        'expiresAt': DateTime.now().add(const Duration(hours: 24)),
      };

      await _firestore.collection('stories').add(storyData);

      // Refresh stories
      ref.read(storyReelsProvider.notifier).refresh();

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Text story published successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error publishing text story: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isPublishing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: _isPublishing ? null : () => Navigator.pop(context),
        ),
        title: const Text(
          'Create Text Story',
          style: TextStyle(color: Colors.white),
        ),
        actions: [
          TextButton(
            onPressed: _isPublishing ? null : _publishTextStory,
            child: _isPublishing
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Share', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
      body: Column(
        children: [
          // Story preview
          Expanded(
            child: Container(
              width: double.infinity,
              height: double.infinity,
              color: _backgroundColor,
              child: Center(
                child: Padding(
                  padding: const EdgeInsets.all(32),
                  child: Text(
                    _textController.text,
                    style: TextStyle(
                      color: _textColor,
                      fontSize: _textSize,
                      fontFamily: _selectedFont == 'Default'
                          ? null
                          : _selectedFont,
                      fontWeight: _isBold ? FontWeight.bold : FontWeight.normal,
                      fontStyle: _isItalic
                          ? FontStyle.italic
                          : FontStyle.normal,
                    ),
                    textAlign: _textAlign,
                  ),
                ),
              ),
            ),
          ),

          // Text input and controls
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.black,
            child: Column(
              children: [
                // Text input
                TextField(
                  controller: _textController,
                  style: const TextStyle(color: Colors.white),
                  decoration: const InputDecoration(
                    hintText: 'Type your story here...',
                    hintStyle: TextStyle(color: Colors.grey),
                    border: OutlineInputBorder(),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.grey),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.white),
                    ),
                  ),
                  maxLines: 3,
                ),

                const SizedBox(height: 16),

                // Controls
                Row(
                  children: [
                    // Background color
                    IconButton(
                      onPressed: () => _showColorPicker(true),
                      icon: Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          color: _backgroundColor,
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 2),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),

                    // Text color
                    IconButton(
                      onPressed: () => _showColorPicker(false),
                      icon: Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          color: _textColor,
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 2),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),

                    // Font picker
                    IconButton(
                      onPressed: _showFontPicker,
                      icon: const Icon(
                        Icons.font_download,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(width: 8),

                    // Text alignment
                    IconButton(
                      onPressed: _showTextAlignmentPicker,
                      icon: Icon(_getAlignmentIcon(), color: Colors.white),
                    ),
                    const SizedBox(width: 8),

                    // Bold
                    IconButton(
                      onPressed: () {
                        setState(() {
                          _isBold = !_isBold;
                        });
                      },
                      icon: Icon(
                        Icons.format_bold,
                        color: _isBold ? Colors.blue : Colors.white,
                      ),
                    ),
                    const SizedBox(width: 8),

                    // Italic
                    IconButton(
                      onPressed: () {
                        setState(() {
                          _isItalic = !_isItalic;
                        });
                      },
                      icon: Icon(
                        Icons.format_italic,
                        color: _isItalic ? Colors.blue : Colors.white,
                      ),
                    ),
                    const Spacer(),

                    // Text size slider
                    Expanded(
                      child: Slider(
                        value: _textSize,
                        min: 16,
                        max: 72,
                        onChanged: (value) {
                          setState(() {
                            _textSize = value;
                          });
                        },
                        activeColor: Colors.white,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  IconData _getAlignmentIcon() {
    switch (_textAlign) {
      case TextAlign.left:
        return Icons.format_align_left;
      case TextAlign.center:
        return Icons.format_align_center;
      case TextAlign.right:
        return Icons.format_align_right;
      default:
        return Icons.format_align_center;
    }
  }
}
