import 'package:billionaires_social/features/profile/providers/close_friends_groups_provider.dart';
import 'package:billionaires_social/features/stories/models/story_settings_model.dart';
import 'package:billionaires_social/features/stories/providers/story_settings_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/profile/models/profile_model.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/features/profile/services/profile_service.dart';

class StorySettingsScreen extends ConsumerWidget {
  const StorySettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settingsAsync = ref.watch(storySettingsNotifierProvider);
    final groupsAsync = ref.watch(closeFriendsGroupsNotifierProvider);
    return Scaffold(
      appBar: AppBar(title: const Text('Story Settings')),
      body: settingsAsync.when(
        data: (settings) => ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Duration
            const Text(
              'Story Duration',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            DropdownButton<StoryDuration>(
              value: settings.duration,
              items: const [
                DropdownMenuItem(
                  value: StoryDuration.sixHours,
                  child: Text('6 hours'),
                ),
                DropdownMenuItem(
                  value: StoryDuration.twelveHours,
                  child: Text('12 hours'),
                ),
                DropdownMenuItem(
                  value: StoryDuration.twentyFourHours,
                  child: Text('24 hours'),
                ),
              ],
              onChanged: (val) {
                if (val != null) _update(ref, settings.copyWith(duration: val));
              },
            ),
            const Divider(),
            // Visibility
            const Text(
              'Who can view your story?',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            DropdownButton<StoryVisibility>(
              value: settings.visibility,
              items: const [
                DropdownMenuItem(
                  value: StoryVisibility.public,
                  child: Text('Public'),
                ),
                DropdownMenuItem(
                  value: StoryVisibility.followers,
                  child: Text('Followers'),
                ),
                DropdownMenuItem(
                  value: StoryVisibility.specificGroups,
                  child: Text('Specific Groups'),
                ),
                DropdownMenuItem(
                  value: StoryVisibility.closeFriends,
                  child: Text('Close Friends Groups'),
                ),
              ],
              onChanged: (val) {
                if (val != null) {
                  _update(ref, settings.copyWith(visibility: val));
                }
              },
            ),
            if (settings.visibility == StoryVisibility.specificGroups ||
                settings.visibility == StoryVisibility.closeFriends)
              groupsAsync.when(
                data: (groups) => Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 8),
                    const Text('Select Groups:'),
                    ...groups.map(
                      (g) => CheckboxListTile(
                        value: settings.allowedGroupIds.contains(g.id),
                        title: Text('${g.emoji} ${g.name}'),
                        onChanged: (checked) {
                          final ids = List<String>.from(
                            settings.allowedGroupIds,
                          );
                          if (checked == true) {
                            ids.add(g.id);
                          } else {
                            ids.remove(g.id);
                          }
                          _update(ref, settings.copyWith(allowedGroupIds: ids));
                        },
                      ),
                    ),
                  ],
                ),
                loading: () => const LinearProgressIndicator(),
                error: (e, s) => Text('Error loading groups'),
              ),
            const Divider(),
            // Hide from users
            ListTile(
              title: const Text('Hide story from specific users'),
              subtitle: Text(
                settings.hiddenFromUserIds.isEmpty
                    ? 'None'
                    : '${settings.hiddenFromUserIds.length} users',
              ),
              onTap: () => _showHideFromUsersModal(context, ref, settings),
              trailing: const Icon(Icons.chevron_right),
            ),
            const Divider(),
            // Feature toggles
            _toggle(
              ref,
              settings,
              'Allow Editing',
              settings.allowEditing,
              (v) => settings.copyWith(allowEditing: v),
            ),
            _toggle(
              ref,
              settings,
              'Allow Comments',
              settings.allowComments,
              (v) => settings.copyWith(allowComments: v),
            ),
            _toggle(
              ref,
              settings,
              'Allow Reactions',
              settings.allowReactions,
              (v) => settings.copyWith(allowReactions: v),
            ),
            _toggle(
              ref,
              settings,
              'Allow Mentions',
              settings.allowMentions,
              (v) => settings.copyWith(allowMentions: v),
            ),
            _toggle(
              ref,
              settings,
              'Allow Music',
              settings.allowMusic,
              (v) => settings.copyWith(allowMusic: v),
            ),
            _toggle(
              ref,
              settings,
              'Allow Filters',
              settings.allowFilters,
              (v) => settings.copyWith(allowFilters: v),
            ),
            _toggle(
              ref,
              settings,
              'Allow Text Overlays',
              settings.allowTextOverlays,
              (v) => settings.copyWith(allowTextOverlays: v),
            ),
            _toggle(
              ref,
              settings,
              'Allow Video Upload',
              settings.allowVideoUpload,
              (v) => settings.copyWith(allowVideoUpload: v),
            ),
          ],
        ),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (e, s) => Center(child: Text('Error: $e')),
      ),
    );
  }

  void _update(WidgetRef ref, StorySettings newSettings) {
    ref
        .read(storySettingsNotifierProvider.notifier)
        .updateSettings(newSettings);
  }

  void _showHideFromUsersModal(
    BuildContext context,
    WidgetRef ref,
    StorySettings settings,
  ) {
    final profileService = getIt<ProfileService>();
    String search = '';
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) {
        return Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom + 16,
            left: 16,
            right: 16,
            top: 24,
          ),
          child: FutureBuilder<List<ProfileModel>>(
            future: profileService.getFollowers('current_user'),
            builder: (context, snapshot) {
              final followers = snapshot.data ?? [];
              List<ProfileModel> filtered = followers
                  .where(
                    (f) =>
                        f.name.toLowerCase().contains(search.toLowerCase()) ||
                        f.username.toLowerCase().contains(search.toLowerCase()),
                  )
                  .toList();
              return StatefulBuilder(
                builder: (context, setState) => Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Text(
                      'Hide story from users',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      decoration: const InputDecoration(
                        labelText: 'Search users',
                        prefixIcon: Icon(Icons.search),
                      ),
                      onChanged: (val) => setState(() => search = val),
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      height: 300,
                      child: snapshot.connectionState == ConnectionState.waiting
                          ? const Center(child: CircularProgressIndicator())
                          : ListView.builder(
                              itemCount: filtered.length,
                              itemBuilder: (context, i) {
                                final user = filtered[i];
                                final isHidden = settings.hiddenFromUserIds
                                    .contains(user.id);
                                return CheckboxListTile(
                                  value: isHidden,
                                  title: Text(user.name),
                                  subtitle: Text('@${user.username}'),
                                  secondary: CircleAvatar(
                                    backgroundImage: NetworkImage(
                                      user.profilePictureUrl,
                                    ),
                                  ),
                                  onChanged: (checked) {
                                    final ids = List<String>.from(
                                      settings.hiddenFromUserIds,
                                    );
                                    if (checked == true) {
                                      ids.add(user.id);
                                    } else {
                                      ids.remove(user.id);
                                    }
                                    _update(
                                      ref,
                                      settings.copyWith(hiddenFromUserIds: ids),
                                    );
                                    setState(() {});
                                  },
                                );
                              },
                            ),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('Done'),
                    ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _toggle(
    WidgetRef ref,
    StorySettings settings,
    String label,
    bool value,
    StorySettings Function(bool) copyWith,
  ) {
    return SwitchListTile(
      title: Text(label),
      value: value,
      onChanged: (v) => _update(ref, copyWith(v)),
    );
  }
}
