import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/shared/story_shared_models.dart';
import '../providers/unified_story_provider.dart';
import '../providers/story_provider.dart' as story_provider;
import '../../creation/widgets/smart_mention_text_field.dart';

class UnifiedStoryEditorScreen extends ConsumerStatefulWidget {
  final File mediaFile;
  const UnifiedStoryEditorScreen({super.key, required this.mediaFile});

  @override
  ConsumerState<UnifiedStoryEditorScreen> createState() =>
      _UnifiedStoryEditorScreenState();
}

class _UnifiedStoryEditorScreenState
    extends ConsumerState<UnifiedStoryEditorScreen>
    with TickerProviderStateMixin {
  late final StoryCreationNotifier _notifier;
  late final AnimationController _toolbarAnimationController;
  late final Animation<double> _toolbarAnimation;
  bool _isDialogOpen = false;
  Offset? _lastTapPosition;

  @override
  void initState() {
    super.initState();
    _notifier = ref.read(storyCreationProvider.notifier);
    _initializeAnimations();

    // Defer the media setting to avoid modifying provider during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Reset state first to clear any previous story data
      _notifier.reset();

      _notifier.setMedia(
        widget.mediaFile,
        StoryMediaType.image,
      ); // Default, can be changed

      // Show the toolbar immediately so users can see editing options
      _toolbarAnimationController.forward();
    });
  }

  void _initializeAnimations() {
    _toolbarAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _toolbarAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _toolbarAnimationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _toolbarAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(storyCreationProvider);
    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Story'),
        actions: [
          IconButton(
            icon: const Icon(Icons.check),
            onPressed: state.isLoading
                ? null
                : () async {
                    final navigator = Navigator.of(context);
                    final scaffoldMessenger = ScaffoldMessenger.of(context);
                    final success = await _notifier.createStory();
                    if (success && mounted) {
                      // Refresh stories after successful creation
                      ref
                          .read(story_provider.storyReelsProvider.notifier)
                          .refresh();
                      navigator.pop(true);
                    } else if (state.error != null && mounted) {
                      scaffoldMessenger.showSnackBar(
                        SnackBar(
                          content: Text(state.error!),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  },
          ),
        ],
      ),
      body: GestureDetector(
        onTapDown: (details) {
          // Capture tap position for text placement
          _lastTapPosition = details.localPosition;
        },
        onTap: () {
          // Only toggle toolbar if no dialogs are open
          if (!_isDialogOpen) {
            // Toggle toolbar visibility when tapping on the screen
            if (_toolbarAnimationController.isCompleted) {
              _toolbarAnimationController.reverse();
            } else {
              _toolbarAnimationController.forward();
            }
          }
        },
        onDoubleTap: () {
          // Double tap anywhere to add text at that location
          if (!_isDialogOpen) {
            _showTextDialog(context, tapPosition: _lastTapPosition);
          }
        },
        onLongPress: () {
          // Long press to add text at tap location
          if (!_isDialogOpen) {
            _showTextDialog(context, tapPosition: _lastTapPosition);
          }
        },
        child: Stack(
          children: [
            // Media preview
            Positioned.fill(
              child: Container(
                color: Colors.black,
                child: Image.file(
                  widget.mediaFile,
                  fit: BoxFit.contain, // Use contain to prevent zoom issues
                  width: double.infinity,
                  height: double.infinity,
                ),
              ),
            ),
            // Drawing overlay
            if (state.drawingPoints.isNotEmpty)
              Positioned.fill(
                child: CustomPaint(
                  painter: _DrawingPainter(
                    points: state.drawingPoints,
                    color: state.drawingColor ?? Colors.white,
                    width: state.drawingWidth ?? 3.0,
                  ),
                ),
              ),
            // Text overlays
            ...state.textElements.asMap().entries.map((entry) {
              final index = entry.key;
              final element = entry.value;
              return Positioned(
                left: element.position.dx,
                top: element.position.dy,
                child: RawGestureDetector(
                  gestures: {
                    // Pan gesture for dragging
                    PanGestureRecognizer:
                        GestureRecognizerFactoryWithHandlers<
                          PanGestureRecognizer
                        >(() => PanGestureRecognizer(), (
                          PanGestureRecognizer instance,
                        ) {
                          instance.onUpdate = (details) {
                            _notifier.updateTextElement(
                              index,
                              element.copyWith(
                                position: element.position + details.delta,
                              ),
                            );
                          };
                        }),
                    // Scale gesture for pinch-to-resize
                    ScaleGestureRecognizer:
                        GestureRecognizerFactoryWithHandlers<
                          ScaleGestureRecognizer
                        >(() => ScaleGestureRecognizer(), (
                          ScaleGestureRecognizer instance,
                        ) {
                          instance.onUpdate = (details) {
                            // Only handle scale if it's a pinch gesture (2+ pointers)
                            if (details.pointerCount >= 2) {
                              final newSize = (element.size * details.scale)
                                  .clamp(12.0, 72.0);
                              _notifier.updateTextElement(
                                index,
                                element.copyWith(size: newSize),
                              );
                            }
                          };
                        }),
                    // Double tap gesture for editing
                    DoubleTapGestureRecognizer:
                        GestureRecognizerFactoryWithHandlers<
                          DoubleTapGestureRecognizer
                        >(() => DoubleTapGestureRecognizer(), (
                          DoubleTapGestureRecognizer instance,
                        ) {
                          instance.onDoubleTap = () {
                            // Double tap to edit text
                            _editTextElement(context, index, element);
                          };
                        }),
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: element.backgroundColor,
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      element.text,
                      style: TextStyle(
                        color: element.color,
                        fontSize: element.size,
                        fontFamily: element.fontFamily,
                        fontWeight: element.fontWeight,
                        fontStyle: element.isItalic
                            ? FontStyle.italic
                            : FontStyle.normal,
                        shadows: [
                          Shadow(
                            offset: const Offset(1, 1),
                            blurRadius: 2,
                            color: Colors.black.withValues(alpha: 0.5),
                          ),
                        ],
                      ),
                      textAlign: element.textAlign,
                    ),
                  ),
                ),
              );
            }),
            // Hint overlay for first-time users
            if (state.textElements.isEmpty && state.drawingPoints.isEmpty)
              Positioned(
                top: 100,
                left: 20,
                right: 20,
                child: FadeTransition(
                  opacity: _toolbarAnimation,
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.7),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Text(
                      '✨ Tap anywhere to access editing tools\n📝 Add text, music, filters & more!',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ),

            // UI toolbars and controls
            _buildToolbar(context, state),
            if (state.isLoading)
              const Center(child: CircularProgressIndicator()),
          ],
        ),
      ),
    );
  }

  Widget _buildToolbar(BuildContext context, StoryCreationState state) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: FadeTransition(
        opacity: _toolbarAnimation,
        child: Container(
          margin: const EdgeInsets.only(bottom: 20),
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 20),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.8),
            borderRadius: BorderRadius.circular(25),
          ),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildToolButton(
                  Icons.text_fields,
                  'Text',
                  () =>
                      _showTextDialog(context), // No tap position from toolbar
                ),
                const SizedBox(width: 8),
                _buildToolButton(
                  Icons.brush,
                  'Draw',
                  () => _showDrawingDialog(context),
                ),
                const SizedBox(width: 8),
                _buildToolButton(
                  Icons.tag,
                  'Tag',
                  () => _showTagDialog(context),
                ),
                const SizedBox(width: 8),
                _buildToolButton(
                  Icons.music_note,
                  'Music',
                  () => _showMusicDialog(context),
                ),
                const SizedBox(width: 8),
                _buildToolButton(
                  Icons.location_on,
                  'Location',
                  () => _showLocationDialog(context),
                ),
                const SizedBox(width: 8),
                _buildToolButton(
                  Icons.filter,
                  'Filter',
                  () => _showFilterDialog(context),
                ),
                const SizedBox(width: 8),
                _buildToolButton(
                  Icons.privacy_tip,
                  'Privacy',
                  () => _showPrivacyDialog(context),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildToolButton(IconData icon, String label, VoidCallback onPressed) {
    return GestureDetector(
      onTap: onPressed,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: Colors.white, size: 20),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  void _showTextDialog(BuildContext context, {Offset? tapPosition}) {
    setState(() => _isDialogOpen = true);
    final controller = TextEditingController();
    List<String> mentions = [];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Text'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SmartMentionTextField(
              controller: controller,
              hintText: 'Enter text... Use @ to mention people',
              maxLines: 3,
              onMentionsChanged: (newMentions) {
                mentions = newMentions;
              },
              style: const TextStyle(fontSize: 16),
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.all(12),
              ),
            ),
            if (mentions.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                'Mentions: ${mentions.join(', ')}',
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() => _isDialogOpen = false);
              Navigator.of(context).pop();
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              if (controller.text.isNotEmpty) {
                // Use tap position if available, otherwise center of screen
                final position =
                    tapPosition ??
                    Offset(
                      MediaQuery.of(context).size.width / 2 - 50,
                      MediaQuery.of(context).size.height / 2 - 50,
                    );

                _notifier.addTextElement(
                  TextElement(
                    text: controller.text,
                    color: Colors.white,
                    size: 28.0,
                    position: position,
                    backgroundColor: Colors.black.withValues(alpha: 0.6),
                    fontWeight: FontWeight.bold,
                    mentions: mentions, // Store mentions with text element
                  ),
                );
              }
              setState(() => _isDialogOpen = false);
              Navigator.of(context).pop();
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  void _editTextElement(BuildContext context, int index, TextElement element) {
    setState(() => _isDialogOpen = true);
    final controller = TextEditingController(text: element.text);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Text'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(hintText: 'Enter text'),
          autofocus: true,
          showCursor: true, // Always show cursor when focused
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() => _isDialogOpen = false);
              Navigator.of(context).pop();
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              _notifier.removeTextElement(index);
              setState(() => _isDialogOpen = false);
              Navigator.of(context).pop();
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
          TextButton(
            onPressed: () {
              if (controller.text.isNotEmpty) {
                _notifier.updateTextElement(
                  index,
                  element.copyWith(text: controller.text),
                );
              }
              setState(() => _isDialogOpen = false);
              Navigator.of(context).pop();
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }

  void _showDrawingDialog(BuildContext context) {
    // TODO: Implement drawing UI
  }

  void _showTagDialog(BuildContext context) {
    // TODO: Implement tagging UI
  }

  void _showMusicDialog(BuildContext context) {
    // TODO: Implement music selection UI
  }

  void _showLocationDialog(BuildContext context) {
    // TODO: Implement location selection UI
  }

  void _showFilterDialog(BuildContext context) {
    // TODO: Implement filter selection UI
  }

  void _showPrivacyDialog(BuildContext context) {
    // TODO: Implement privacy selection UI
  }
}

class _DrawingPainter extends CustomPainter {
  final List<DrawingPoint> points;
  final Color color;
  final double width;
  _DrawingPainter({
    required this.points,
    required this.color,
    required this.width,
  });
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = width
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;
    for (final point in points) {
      canvas.drawCircle(point.position, width / 2, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
