import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/stories/models/story_view.dart';
import 'package:billionaires_social/features/stories/services/story_viewer_service.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:timeago/timeago.dart' as timeago;

/// Comprehensive Story Viewers Screen with enhanced UX
class StoryViewersScreen extends ConsumerStatefulWidget {
  final String storyId;
  final String storyTitle;

  const StoryViewersScreen({
    super.key,
    required this.storyId,
    required this.storyTitle,
  });

  @override
  ConsumerState<StoryViewersScreen> createState() => _StoryViewersScreenState();
}

class _StoryViewersScreenState extends ConsumerState<StoryViewersScreen> {
  final StoryViewerService _viewerService = StoryViewerService();
  final ScrollController _scrollController = ScrollController();

  StoryViewerStats? _stats;
  List<StoryView> _viewers = [];
  bool _isLoading = true;
  bool _isLoadingMore = false;
  bool _hasError = false;
  String _errorMessage = '';

  ViewerSortType _currentSort = ViewerSortType.mostRecent;
  ViewerFilterType _currentFilter = ViewerFilterType.all;

  @override
  void initState() {
    super.initState();
    _loadViewerStats();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreViewers();
    }
  }

  Future<void> _loadViewerStats() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      final stats = await _viewerService.getStoryViewerStats(
        storyId: widget.storyId,
        sortType: _currentSort,
        filterType: _currentFilter,
        limit: 50,
      );

      if (mounted) {
        setState(() {
          _stats = stats;
          _viewers = stats?.allViewers ?? [];
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _hasError = true;
          _errorMessage = 'Failed to load viewers: $e';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadMoreViewers() async {
    if (_isLoadingMore || _viewers.isEmpty) return;

    setState(() => _isLoadingMore = true);

    try {
      // Implementation for pagination would go here
      // For now, we'll just simulate loading more
      await Future.delayed(const Duration(milliseconds: 500));
    } catch (e) {
      debugPrint('Error loading more viewers: $e');
    } finally {
      if (mounted) {
        setState(() => _isLoadingMore = false);
      }
    }
  }

  void _showSortOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Sort Viewers',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ...ViewerSortType.values.map(
              (sort) => ListTile(
                title: Text(_getSortTitle(sort)),
                leading: Radio<ViewerSortType>(
                  value: sort,
                  groupValue: _currentSort,
                  onChanged: (value) {
                    Navigator.pop(context);
                    setState(() => _currentSort = value!);
                    _loadViewerStats();
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showFilterOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Filter Viewers',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ...ViewerFilterType.values.map(
              (filter) => ListTile(
                title: Text(_getFilterTitle(filter)),
                leading: Radio<ViewerFilterType>(
                  value: filter,
                  groupValue: _currentFilter,
                  onChanged: (value) {
                    Navigator.pop(context);
                    setState(() => _currentFilter = value!);
                    _loadViewerStats();
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getSortTitle(ViewerSortType sort) {
    switch (sort) {
      case ViewerSortType.mostRecent:
        return 'Most Recent';
      case ViewerSortType.oldest:
        return 'Oldest First';
      case ViewerSortType.followersFirst:
        return 'Followers First';
      case ViewerSortType.closeFriendsFirst:
        return 'Close Friends First';
      case ViewerSortType.alphabetical:
        return 'Alphabetical';
      case ViewerSortType.viewDuration:
        return 'View Duration';
    }
  }

  String _getFilterTitle(ViewerFilterType filter) {
    switch (filter) {
      case ViewerFilterType.all:
        return 'All Viewers';
      case ViewerFilterType.followers:
        return 'Followers Only';
      case ViewerFilterType.nonFollowers:
        return 'Non-Followers';
      case ViewerFilterType.closeFriends:
        return 'Close Friends';
      case ViewerFilterType.mutualFollowers:
        return 'Mutual Followers';
      case ViewerFilterType.completed:
        return 'Completed Views';
      case ViewerFilterType.incomplete:
        return 'Incomplete Views';
    }
  }

  void _openProfile(String userId) {
    // TODO: Navigate to user profile screen
    debugPrint('Opening profile for user: $userId');
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Opening profile for user: $userId')),
    );
  }

  void _openChat(String userId, String username) {
    // TODO: Navigate to chat screen
    debugPrint('Opening chat with user: $userId ($username)');
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('Opening chat with: $username')));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Story Viewers',
              style: const TextStyle(color: Colors.white, fontSize: 18),
            ),
            if (_stats != null)
              Text(
                '${_stats!.totalViews} views',
                style: const TextStyle(color: Colors.grey, fontSize: 14),
              ),
          ],
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.sort, color: Colors.white),
            onPressed: _showSortOptions,
          ),
          IconButton(
            icon: const Icon(Icons.filter_list, color: Colors.white),
            onPressed: _showFilterOptions,
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return _buildLoadingSkeleton();
    }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              _errorMessage,
              style: const TextStyle(color: Colors.white, fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadViewerStats,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_viewers.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.visibility_off, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No viewers yet',
              style: TextStyle(color: Colors.white, fontSize: 18),
            ),
            Text(
              'When people view your story, they\'ll appear here',
              style: TextStyle(color: Colors.grey, fontSize: 14),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        if (_stats != null) _buildStatsHeader(),
        Expanded(
          child: ListView.builder(
            controller: _scrollController,
            itemCount: _viewers.length + (_isLoadingMore ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == _viewers.length) {
                return const Center(
                  child: Padding(
                    padding: EdgeInsets.all(16),
                    child: CircularProgressIndicator(),
                  ),
                );
              }
              return _buildViewerTile(_viewers[index]);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingSkeleton() {
    return ListView.builder(
      itemCount: 10,
      itemBuilder: (context, index) => Container(
        height: 72,
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.grey[800],
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Center(child: CircularProgressIndicator()),
      ),
    );
  }

  Widget _buildStatsHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Color(0xFF1A1A1A),
        border: Border(bottom: BorderSide(color: Colors.grey, width: 0.5)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem('Views', _stats!.totalViews.toString()),
          _buildStatItem(
            'Completion',
            '${_stats!.completionRate.toStringAsFixed(1)}%',
          ),
          _buildStatItem(
            'Avg Duration',
            '${_stats!.averageViewDuration.inSeconds}s',
          ),
          _buildStatItem('Followers', _stats!.followerViews.toString()),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(label, style: const TextStyle(color: Colors.grey, fontSize: 12)),
      ],
    );
  }

  Widget _buildViewerTile(StoryView viewer) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A1A),
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: _buildProfileImage(viewer),
        title: Row(
          children: [
            Expanded(
              child: Text(
                viewer.displayName,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (viewer.isFollower) _buildFollowerBadge(),
            if (viewer.isCloseFriend) _buildCloseFriendBadge(),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '@${viewer.username}',
              style: const TextStyle(color: Colors.grey, fontSize: 14),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Text(
                  timeago.format(viewer.viewedAt),
                  style: const TextStyle(color: Colors.grey, fontSize: 12),
                ),
                const SizedBox(width: 8),
                if (viewer.completed)
                  const Icon(Icons.check_circle, color: Colors.green, size: 16)
                else
                  const Icon(
                    Icons.remove_circle,
                    color: Colors.orange,
                    size: 16,
                  ),
                const SizedBox(width: 4),
                Text(
                  '${viewer.viewDuration.inSeconds}s',
                  style: const TextStyle(color: Colors.grey, fontSize: 12),
                ),
              ],
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // ✅ 4. Message icon for instant chat
            IconButton(
              icon: const Icon(Icons.message, color: Colors.white, size: 20),
              onPressed: () => _openChat(viewer.userId, viewer.username),
              tooltip: 'Send message',
            ),
            const SizedBox(width: 8),
            const Icon(Icons.chevron_right, color: Colors.grey),
          ],
        ),
        // ✅ 4. Tap on profile picture or name → opens profile screen
        onTap: () => _openProfile(viewer.userId),
      ),
    );
  }

  Widget _buildProfileImage(StoryView viewer) {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: viewer.isCloseFriend
              ? Colors.green
              : viewer.isFollower
              ? Colors.blue
              : Colors.grey,
          width: 2,
        ),
      ),
      child: ClipOval(
        child: viewer.profilePictureUrl != null
            ? CachedNetworkImage(
                imageUrl: viewer.profilePictureUrl!,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: Colors.grey[800],
                  child: const Icon(Icons.person, color: Colors.white),
                ),
                errorWidget: (context, url, error) => Container(
                  color: Colors.grey[800],
                  child: const Icon(Icons.person, color: Colors.white),
                ),
              )
            : Container(
                color: Colors.grey[800],
                child: const Icon(Icons.person, color: Colors.white),
              ),
      ),
    );
  }

  Widget _buildFollowerBadge() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.blue,
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Text(
        'Follower',
        style: TextStyle(color: Colors.white, fontSize: 10),
      ),
    );
  }

  Widget _buildCloseFriendBadge() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.green,
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Text(
        'Close Friend',
        style: TextStyle(color: Colors.white, fontSize: 10),
      ),
    );
  }
}
