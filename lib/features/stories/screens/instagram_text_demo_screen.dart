import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../widgets/story_text_overlay_manager.dart';
import '../widgets/interactive_text_editor.dart';

/// Demo screen showing Instagram-style text editing
class InstagramTextDemoScreen extends StatefulWidget {
  const InstagramTextDemoScreen({super.key});

  @override
  State<InstagramTextDemoScreen> createState() => _InstagramTextDemoScreenState();
}

class _InstagramTextDemoScreenState extends State<InstagramTextDemoScreen> {
  List<TextElement> _textElements = [];
  bool _showInstructions = true;

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Background image/video placeholder
          Container(
            width: double.infinity,
            height: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.purple.withValues(alpha: 0.8),
                  Colors.blue.withValues(alpha: 0.8),
                  Colors.pink.withValues(alpha: 0.8),
                ],
              ),
            ),
            child: const Center(
              child: Icon(
                Icons.image,
                size: 100,
                color: Colors.white24,
              ),
            ),
          ),
          
          // Text overlay manager
          StoryTextOverlayManager(
            screenSize: screenSize,
            initialTextElements: _textElements,
            onTextElementsChanged: (elements) {
              setState(() {
                _textElements = elements;
                _showInstructions = elements.isEmpty;
              });
            },
          ),
          
          // Top controls
          Positioned(
            top: MediaQuery.of(context).padding.top + 16,
            left: 16,
            right: 16,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Back button
                GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.5),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.arrow_back,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
                
                // Add text button
                GestureDetector(
                  onTap: _addNewText,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.5),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.text_fields,
                          color: Colors.white,
                          size: 20,
                        ),
                        SizedBox(width: 8),
                        Text(
                          'Add Text',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Instructions overlay
          if (_showInstructions)
            Positioned.fill(
              child: Container(
                color: Colors.black.withValues(alpha: 0.7),
                child: Center(
                  child: Container(
                    margin: const EdgeInsets.all(32),
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.text_fields,
                          size: 48,
                          color: Colors.blue,
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'Instagram-Style Text Editing',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          '• Tap "Add Text" or double-tap anywhere\n'
                          '• Type your text\n'
                          '• Pinch with two fingers to resize\n'
                          '• Rotate with two fingers\n'
                          '• Drag to move text around\n'
                          '• Swipe left/right on text to change fonts\n'
                          '• Tap text to open styling controls\n'
                          '• Use color palette for text/background colors\n'
                          '• Toggle between shadow, background, and plain styles',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.black87,
                            height: 1.5,
                          ),
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton(
                          onPressed: () {
                            setState(() {
                              _showInstructions = false;
                            });
                            _addNewText();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 32,
                              vertical: 12,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(25),
                            ),
                          ),
                          child: const Text(
                            'Get Started',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          
          // Bottom hint
          if (!_showInstructions && _textElements.isEmpty)
            Positioned(
              bottom: 100,
              left: 0,
              right: 0,
              child: Center(
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 12,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: const Text(
                    'Double-tap anywhere to add text',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  void _addNewText() {
    setState(() {
      _showInstructions = false;
    });
    
    // Add a new text element at center
    final screenSize = MediaQuery.of(context).size;
    final newElement = TextElement(
      text: '',
      position: Offset(
        screenSize.width / 2 - 50,
        screenSize.height / 2 - 50,
      ),
      size: 32.0,
      color: Colors.white,
      fontFamily: 'Classic',
      hasShadow: true,
    );
    
    setState(() {
      _textElements.add(newElement);
    });
    
    HapticFeedback.lightImpact();
  }
}
