import 'dart:io';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/features/stories/screens/text_story_creation_screen.dart'
    as text_story;
import 'package:billionaires_social/features/creation/screens/post_creation_screen.dart';
import 'package:billionaires_social/features/reels/screens/reel_creation_screen.dart';
import 'package:billionaires_social/features/live/screens/live_setup_screen.dart';
import 'package:billionaires_social/features/stories/screens/story_settings_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:camera/camera.dart';
import 'dart:async';
import 'package:flutter/services.dart';
import 'package:billionaires_social/features/stories/screens/unified_story_editor_screen.dart';
import 'package:billionaires_social/core/main_navigation.dart';

class StoryCreationScreen extends ConsumerStatefulWidget {
  final CreationMode initialMode;

  const StoryCreationScreen({super.key, this.initialMode = CreationMode.story});

  @override
  ConsumerState<StoryCreationScreen> createState() =>
      _StoryCreationScreenState();
}

class _StoryCreationScreenState extends ConsumerState<StoryCreationScreen>
    with TickerProviderStateMixin {
  CameraController? _cameraController;
  List<CameraDescription>? _cameras;
  int _selectedCameraIndex = 0;
  FlashMode _currentFlashMode = FlashMode.off;
  bool _isCameraInitialized = false;
  String? _cameraError;
  bool _isCapturing = false;
  bool _isRecording = false;
  bool _isMuted = false;
  CreationMode _currentMode = CreationMode.story;

  // Animation controllers
  late AnimationController _captureAnimationController;
  late AnimationController _modeSwitchAnimationController;
  late Animation<double> _captureScaleAnimation;

  // Instagram-style continuous recording
  final List<File> _videoSegments = [];
  Timer? _segmentTimer;
  static const int _maxSegmentDuration = 15; // 15 seconds per segment
  int _currentSegmentDuration = 0;

  @override
  void initState() {
    super.initState();
    _currentMode = widget.initialMode;
    _initializeAnimations();
    _initializeCamera();
  }

  void _initializeAnimations() {
    _captureAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _modeSwitchAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _captureScaleAnimation = Tween<double>(begin: 1.0, end: 0.8).animate(
      CurvedAnimation(
        parent: _captureAnimationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _cameraController?.dispose();
    _captureAnimationController.dispose();
    _modeSwitchAnimationController.dispose();
    _segmentTimer?.cancel();
    super.dispose();
  }

  Future<void> _initializeCamera({int cameraIndex = 0}) async {
    try {
      _cameras = await availableCameras();
      if (_cameras == null || _cameras!.isEmpty) {
        setState(() => _cameraError = 'No cameras available.');
        return;
      }

      _selectedCameraIndex = cameraIndex;
      final camera = _cameras![_selectedCameraIndex];

      _cameraController = CameraController(
        camera,
        ResolutionPreset.high,
        enableAudio: true,
      );

      await _cameraController!.initialize();

      if (mounted) {
        setState(() {
          _isCameraInitialized = true;
          _cameraError = null;
        });
      }
    } on CameraException catch (e) {
      setState(() {
        _cameraError = 'Error initializing camera: ${e.code}';
        _isCameraInitialized = false;
      });
    } catch (e) {
      setState(() {
        _cameraError = 'An unexpected error occurred.';
        _isCameraInitialized = false;
      });
    }
  }

  Future<void> _onGalleryPick() async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickMedia(
        imageQuality: 80,
        maxWidth: 1080,
        maxHeight: 1920,
      );

      if (pickedFile != null) {
        final file = File(pickedFile.path);
        _navigateToEditor(file);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error picking media: ${e.toString()}')),
        );
      }
    }
  }

  Future<void> _onCapture() async {
    if (!_isCameraInitialized ||
        _cameraController!.value.isTakingPicture ||
        _isCapturing) {
      return;
    }

    setState(() {
      _isCapturing = true;
    });

    // Animate capture button
    _captureAnimationController.forward().then((_) {
      _captureAnimationController.reverse();
    });

    try {
      final file = await _cameraController!.takePicture();
      final capturedFile = File(file.path);
      _navigateToEditor(capturedFile);
    } on CameraException catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Camera error: ${e.description}')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error capturing photo: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isCapturing = false;
        });
      }
    }
  }

  Future<void> _onVideoRecord() async {
    debugPrint(
      '🎥 _onVideoRecord called - isCameraInitialized: $_isCameraInitialized, isRecording: $_isRecording',
    );

    if (!_isCameraInitialized) {
      debugPrint('❌ Camera not initialized');
      return;
    }

    if (!_isRecording) {
      // Start recording
      debugPrint('🎬 Starting video recording...');
      try {
        await _cameraController!.startVideoRecording();
        debugPrint('✅ Video recording started successfully');

        setState(() {
          _isRecording = true;
          _currentSegmentDuration = 0;
        });

        // Start segment timer for Instagram-style continuous recording
        _segmentTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
          setState(() {
            _currentSegmentDuration++;
          });
          debugPrint('⏱️ Recording duration: ${_currentSegmentDuration}s');

          // Auto-stop segment at 15 seconds and start next segment
          if (_currentSegmentDuration >= _maxSegmentDuration) {
            debugPrint('🔄 Auto-stopping segment at ${_maxSegmentDuration}s');
            _stopCurrentSegment();
          }
        });
      } on CameraException catch (e) {
        debugPrint(
          '❌ CameraException starting recording: ${e.code} - ${e.description}',
        );
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error starting recording: ${e.description}'),
            ),
          );
        }
      } catch (e) {
        debugPrint('❌ Unexpected error starting recording: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Unexpected error: ${e.toString()}')),
          );
        }
      }
    } else {
      // Stop recording (user released button)
      debugPrint('🛑 Stopping video recording...');
      _stopRecording();
    }
  }

  Future<void> _stopCurrentSegment() async {
    if (!_isRecording) return;

    try {
      final file = await _cameraController!.stopVideoRecording();
      final recordedFile = File(file.path);

      setState(() {
        _videoSegments.add(recordedFile);
        _currentSegmentDuration = 0;
      });

      // If user is still holding, start next segment immediately
      if (_isRecording) {
        await _cameraController!.startVideoRecording();
      }
    } on CameraException catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error stopping segment: ${e.description}')),
        );
      }
      _stopRecording();
    }
  }

  Future<void> _stopRecording() async {
    _segmentTimer?.cancel();

    if (_isRecording) {
      try {
        final file = await _cameraController!.stopVideoRecording();
        final recordedFile = File(file.path);

        setState(() {
          _videoSegments.add(recordedFile);
          _isRecording = false;
          _currentSegmentDuration = 0;
        });

        // Navigate to editor with all segments
        if (_videoSegments.isNotEmpty) {
          _navigateToEditorWithSegments();
        }
      } on CameraException catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error stopping recording: ${e.description}'),
            ),
          );
        }
        setState(() {
          _isRecording = false;
          _currentSegmentDuration = 0;
        });
      }
    }
  }

  void _navigateToEditor(File mediaFile) {
    // If in reel mode, return the video file to the calling screen
    if (_currentMode == CreationMode.reel) {
      Navigator.of(context).pop(mediaFile);
      return;
    }

    // Otherwise, navigate to story editor for story creation
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => UnifiedStoryEditorScreen(mediaFile: mediaFile),
      ),
    );
  }

  void _navigateToEditorWithSegments() {
    // For now, use the last segment as the main file
    // In a full implementation, you'd want to merge segments or handle them separately
    final mainFile = _videoSegments.last;

    // If in reel mode, return the video file to the calling screen
    if (_currentMode == CreationMode.reel) {
      Navigator.of(context).pop(mainFile);
      return;
    }

    // Otherwise, navigate to story editor for story creation
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => UnifiedStoryEditorScreen(mediaFile: mainFile),
      ),
    );
  }

  void _switchCamera() async {
    if (_cameras == null || _cameras!.length < 2) return;

    final newIndex = (_selectedCameraIndex + 1) % _cameras!.length;
    await _initializeCamera(cameraIndex: newIndex);
  }

  void _toggleFlash() {
    if (!_isCameraInitialized) return;

    final newMode = _currentFlashMode == FlashMode.off
        ? FlashMode.torch
        : FlashMode.off;

    _cameraController!
        .setFlashMode(newMode)
        .then((_) {
          if (mounted) {
            setState(() {
              _currentFlashMode = newMode;
            });
          }
        })
        .catchError((error) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Error toggling flash: ${error.toString()}'),
              ),
            );
          }
        });
  }

  void _toggleMute() {
    setState(() {
      _isMuted = !_isMuted;
    });
  }

  void _switchMode(CreationMode mode) {
    // If switching to a different mode, navigate to the appropriate screen
    if (mode != _currentMode && mode != CreationMode.story) {
      Navigator.of(context).pop(); // Close current screen

      Widget targetScreen;
      switch (mode) {
        case CreationMode.post:
          targetScreen = const PostCreationScreen();
          break;
        case CreationMode.reel:
          targetScreen = const ReelCreationScreen();
          break;
        case CreationMode.live:
          targetScreen = const LiveSetupScreen();
          break;
        case CreationMode.story:
          return; // Stay in current screen
      }

      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => targetScreen,
          fullscreenDialog: true,
        ),
      );
      return;
    }

    // If staying in story mode, just update the UI
    setState(() {
      _currentMode = mode;
    });
    _modeSwitchAnimationController.forward().then((_) {
      _modeSwitchAnimationController.reverse();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Camera preview
          _buildCameraPreview(),

          // Top controls
          _buildTopControls(),

          // Mode switcher
          _buildModeSwitcher(),

          // Recording indicator
          if (_isRecording) _buildRecordingIndicator(),

          // Left side controls
          _buildLeftControls(),

          // Right side controls
          _buildRightControls(),

          // Bottom controls
          _buildBottomControls(),

          // Error overlay
          if (_cameraError != null) _buildErrorOverlay(),
        ],
      ),
    );
  }

  Widget _buildCameraPreview() {
    if (!_isCameraInitialized) {
      return Container(
        color: Colors.black,
        child: const Center(
          child: CircularProgressIndicator(color: Colors.white),
        ),
      );
    }

    return CameraPreview(_cameraController!);
  }

  Widget _buildTopControls() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: EdgeInsets.only(
          top: MediaQuery.of(context).padding.top + 16,
          left: 16,
          right: 16,
          bottom: 16,
        ),
        child: Row(
          children: [
            IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(Icons.close, color: Colors.white, size: 28),
            ),
            const Spacer(),
            // Flash control
            IconButton(
              onPressed: _toggleFlash,
              icon: Icon(
                _currentFlashMode == FlashMode.torch
                    ? Icons.flash_on
                    : Icons.flash_off,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            // Mute control
            IconButton(
              onPressed: _toggleMute,
              icon: Icon(
                _isMuted ? Icons.mic_off : Icons.mic,
                color: Colors.white,
                size: 24,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModeSwitcher() {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 80,
      left: 0,
      right: 0,
      child: Center(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: CreationMode.values.map((mode) {
              final isSelected = _currentMode == mode;
              return GestureDetector(
                onTap: () => _switchMode(mode),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: isSelected ? Colors.white : Colors.transparent,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    mode.name.toUpperCase(),
                    style: TextStyle(
                      color: isSelected ? Colors.black : Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ),
    );
  }

  Widget _buildRecordingIndicator() {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 80,
      left: 0,
      right: 0,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Column(
          children: [
            // Progress bar
            Container(
              height: 2,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(1),
              ),
              child: FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: _currentSegmentDuration / _maxSegmentDuration,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(1),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 8),
            // Time and segment info
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${_currentSegmentDuration.toString().padLeft(2, '0')}s',
                  style: AppTheme.fontStyles.body.copyWith(color: Colors.white),
                ),
                Text(
                  'Segment ${_videoSegments.length + 1}',
                  style: AppTheme.fontStyles.body.copyWith(color: Colors.white),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLeftControls() {
    return Positioned(
      left: 16,
      top: MediaQuery.of(context).size.height * 0.4,
      child: Column(
        children: [
          // Story options
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.5),
              shape: BoxShape.circle,
            ),
            child: IconButton(
              onPressed: _onGalleryPick,
              icon: const Icon(Icons.photo_library, color: Colors.white),
            ),
          ),
          const SizedBox(height: 16),
          // Text story
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.5),
              shape: BoxShape.circle,
            ),
            child: IconButton(
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) =>
                        const text_story.TextStoryCreationScreen(),
                  ),
                );
              },
              icon: const Icon(Icons.text_fields, color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRightControls() {
    return Positioned(
      right: 16,
      top: MediaQuery.of(context).size.height * 0.4,
      child: Column(
        children: [
          // Switch camera
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.5),
              shape: BoxShape.circle,
            ),
            child: IconButton(
              onPressed: _switchCamera,
              icon: const Icon(Icons.flip_camera_ios, color: Colors.white),
            ),
          ),
          const SizedBox(height: 16),
          // Settings
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.5),
              shape: BoxShape.circle,
            ),
            child: IconButton(
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const StorySettingsScreen(),
                  ),
                );
              },
              icon: const Icon(Icons.settings, color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomControls() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).padding.bottom + 32,
          left: 32,
          right: 32,
          top: 32,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // Gallery button
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.5),
                shape: BoxShape.circle,
              ),
              child: IconButton(
                onPressed: _onGalleryPick,
                icon: const Icon(Icons.photo_library, color: Colors.white),
              ),
            ),
            const SizedBox(width: 32),
            // Capture button
            GestureDetector(
              onTapDown: (_) {
                debugPrint('👆 Tap detected on capture button');
                if (_currentMode == CreationMode.story) {
                  // Tap = take photo or short clip
                  _onCapture();
                } else {
                  _onVideoRecord();
                }
              },
              onLongPressStart: (_) {
                debugPrint('🔴 Long press start detected on capture button');
                if (_currentMode == CreationMode.story) {
                  // Long press = start continuous recording
                  HapticFeedback.mediumImpact();
                  _onVideoRecord();
                }
              },
              onLongPressEnd: (_) {
                debugPrint('🔵 Long press end detected on capture button');
                if (_currentMode == CreationMode.story && _isRecording) {
                  // Release long press = stop recording
                  HapticFeedback.lightImpact();
                  _stopRecording();
                }
              },
              child: AnimatedBuilder(
                animation: _captureAnimationController,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _captureScaleAnimation.value,
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: _isRecording ? Colors.red : Colors.white,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: _isRecording ? Colors.red : Colors.white,
                          width: 4,
                        ),
                        boxShadow: _isRecording
                            ? [
                                BoxShadow(
                                  color: Colors.red.withValues(alpha: 0.5),
                                  blurRadius: 10,
                                  spreadRadius: 2,
                                ),
                              ]
                            : null,
                      ),
                      child: _isRecording
                          ? const Icon(
                              Icons.stop,
                              color: Colors.white,
                              size: 32,
                            )
                          : null,
                    ),
                  );
                },
              ),
            ),
            const SizedBox(width: 32),
            // Mode-specific button
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.5),
                shape: BoxShape.circle,
              ),
              child: IconButton(
                onPressed: () {
                  // Only handle story mode text creation since other modes
                  // are now routed correctly from main navigation
                  if (_currentMode == CreationMode.story) {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) =>
                            const text_story.TextStoryCreationScreen(),
                      ),
                    );
                  }
                },
                icon: Icon(_getModeIcon(), color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getModeIcon() {
    switch (_currentMode) {
      case CreationMode.post:
        return Icons.grid_on;
      case CreationMode.story:
        return Icons.text_fields;
      case CreationMode.reel:
        return Icons.music_note;
      case CreationMode.live:
        return Icons.live_tv;
    }
  }

  Widget _buildErrorOverlay() {
    return Container(
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.white, size: 64),
            const SizedBox(height: 16),
            Text(
              'Camera Error',
              style: AppTheme.fontStyles.title.copyWith(color: Colors.white),
            ),
            const SizedBox(height: 8),
            Text(
              _cameraError!,
              style: AppTheme.fontStyles.body.copyWith(color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => _initializeCamera(),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }
}
