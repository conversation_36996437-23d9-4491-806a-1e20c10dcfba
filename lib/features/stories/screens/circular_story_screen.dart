import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:billionaires_social/features/stories/widgets/circular_story_carousel.dart';
import 'package:billionaires_social/features/stories/providers/story_provider.dart';
import 'package:billionaires_social/features/stories/screens/story_creation_screen.dart';
import 'package:billionaires_social/features/profile/providers/profile_provider.dart';

/// Luxurious circular story carousel screen
class CircularStoryScreen extends ConsumerStatefulWidget {
  const CircularStoryScreen({super.key});

  @override
  ConsumerState<CircularStoryScreen> createState() =>
      _CircularStoryScreenState();
}

class _CircularStoryScreenState extends ConsumerState<CircularStoryScreen>
    with TickerProviderStateMixin {
  late AnimationController _backgroundController;
  late Animation<double> _backgroundAnimation;

  @override
  void initState() {
    super.initState();

    _backgroundController = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    )..repeat();

    _backgroundAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_backgroundController);
  }

  @override
  void dispose() {
    _backgroundController.dispose();
    super.dispose();
  }

  void _navigateToStoryCreation() {
    Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const StoryCreationScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return SlideTransition(
            position:
                Tween<Offset>(
                  begin: const Offset(0.0, 1.0),
                  end: Offset.zero,
                ).animate(
                  CurvedAnimation(
                    parent: animation,
                    curve: Curves.easeOutCubic,
                  ),
                ),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 400),
      ),
    );
  }

  Widget _buildLuxuriousBackground() {
    return AnimatedBuilder(
      animation: _backgroundAnimation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: RadialGradient(
              center: Alignment.center,
              radius: 1.5,
              colors: [
                const Color(0xFF1A1A2E).withValues(alpha: 0.9),
                const Color(0xFF16213E).withValues(alpha: 0.95),
                const Color(0xFF0F3460).withValues(alpha: 1.0),
              ],
              stops: const [0.0, 0.6, 1.0],
            ),
          ),
          child: Stack(
            children: [
              // Animated particles/stars
              ...List.generate(20, (index) {
                final offset = _backgroundAnimation.value * 2 * 3.14159;
                final x = 50 + (index * 30) % MediaQuery.of(context).size.width;
                final y =
                    50 + (index * 50) % MediaQuery.of(context).size.height;

                return Positioned(
                  left: x,
                  top: y,
                  child: Transform.rotate(
                    angle: offset + (index * 0.5),
                    child: Container(
                      width: 2 + (index % 3),
                      height: 2 + (index % 3),
                      decoration: BoxDecoration(
                        color: const Color(0xFFD4AF37).withValues(alpha: 0.6),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: const Color(
                              0xFFD4AF37,
                            ).withValues(alpha: 0.3),
                            blurRadius: 4.0,
                            spreadRadius: 1.0,
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: Row(
        children: [
          // Back button
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.arrow_back_ios,
              color: Colors.white,
              size: 24,
            ),
          ),

          const Spacer(),

          // Title
          const Text(
            'Stories',
            style: TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
              letterSpacing: 1.2,
            ),
          ),

          const Spacer(),

          // Settings button
          IconButton(
            onPressed: () {
              // TODO: Navigate to story settings
            },
            icon: const Icon(Icons.settings, color: Colors.white70, size: 24),
          ),
        ],
      ),
    );
  }

  Widget _buildStoryTypeIndicators() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildTypeIndicator('Public', const Color(0xFFD4AF37), Icons.public),
          _buildTypeIndicator(
            'Close Friends',
            const Color(0xFF00D4AA),
            Icons.people,
          ),
          _buildTypeIndicator('VIP', const Color(0xFFFF6B6B), Icons.star),
        ],
      ),
    );
  }

  Widget _buildTypeIndicator(String label, Color color, IconData icon) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.2),
            shape: BoxShape.circle,
            border: Border.all(color: color, width: 2),
          ),
          child: Icon(icon, color: color, size: 20),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: TextStyle(
            color: color,
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildInstructions() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 40),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFFD4AF37).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: const Column(
        children: [
          Icon(Icons.touch_app, color: Color(0xFFD4AF37), size: 32),
          SizedBox(height: 12),
          Text(
            'Drag to rotate • Tap to view',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 8),
          Text(
            'Tap center to create your story',
            style: TextStyle(color: Colors.white54, fontSize: 14),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final storyReelsAsync = ref.watch(storyReelsProvider);
    final currentUser = FirebaseAuth.instance.currentUser;
    final profileAsync = currentUser != null
        ? ref.watch(profileProvider(currentUser.uid))
        : null;

    return Scaffold(
      body: Stack(
        children: [
          // Luxurious animated background
          _buildLuxuriousBackground(),

          // Main content
          SafeArea(
            child: Column(
              children: [
                // Header
                _buildHeader(),

                const SizedBox(height: 20),

                // Story type indicators
                _buildStoryTypeIndicators(),

                const Spacer(),

                // Circular story carousel
                Center(
                  child: storyReelsAsync.when(
                    data: (storyReels) {
                      return CircularStoryCarousel(
                        storyReels: storyReels,
                        centerAvatarUrl:
                            profileAsync?.valueOrNull?.profilePictureUrl,
                        centerUsername: profileAsync?.valueOrNull?.username,
                        onCenterTap: _navigateToStoryCreation,
                        radius: 140.0,
                        itemSize: 70.0,
                      );
                    },
                    loading: () => Container(
                      width: 280,
                      height: 280,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.white.withValues(alpha: 0.1),
                        border: Border.all(
                          color: const Color(0xFFD4AF37).withValues(alpha: 0.3),
                          width: 2,
                        ),
                      ),
                      child: const Center(
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Color(0xFFD4AF37),
                          ),
                        ),
                      ),
                    ),
                    error: (error, stack) => Container(
                      width: 280,
                      height: 280,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.white.withValues(alpha: 0.1),
                        border: Border.all(
                          color: const Color(0xFFD4AF37).withValues(alpha: 0.3),
                          width: 2,
                        ),
                      ),
                      child: const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.error_outline,
                              color: Colors.white54,
                              size: 48,
                            ),
                            SizedBox(height: 16),
                            Text(
                              'Failed to load stories',
                              style: TextStyle(
                                color: Colors.white54,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

                const Spacer(),

                // Instructions
                _buildInstructions(),

                const SizedBox(height: 40),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
