import 'package:flutter/material.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/features/explore/widgets/feature_list_widget.dart';

class StoryArchiveScreen extends StatelessWidget {
  const StoryArchiveScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final archiveItems = [
      FeatureListItem(
        title: 'Dubai Luxury Expo',
        subtitle: 'Stories from the world\'s elite event',
        imageUrl:
            'https://images.unsplash.com/photo-1504384308090-c894fdcc538d',
        onTap: () {},
        icon: Icons.archive,
      ),
      FeatureListItem(
        title: 'Art Basel Miami',
        subtitle: 'A look back at exclusive art stories',
        imageUrl:
            'https://images.unsplash.com/photo-1464983953574-0892a716854b',
        onTap: () {},
        icon: Icons.archive,
      ),
      FeatureListItem(
        title: 'Monaco Grand Prix',
        subtitle: 'Racing legends and luxury lifestyle',
        imageUrl:
            'https://images.unsplash.com/photo-1511918984145-48de785d4c4e',
        onTap: () {},
        icon: Icons.archive,
      ),
    ];

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Story Archive',
          style: AppTheme.fontStyles.title.copyWith(
            color: AppTheme.accentColor,
          ),
        ),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: FeatureListWidget(
          sectionTitle: 'Archived Stories',
          items: archiveItems,
        ),
      ),
    );
  }
}
