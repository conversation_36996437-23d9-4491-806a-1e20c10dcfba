import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../providers/story_provider.dart';
import '../utils/story_debug_helper.dart';
import '../../feed/widgets/post_card_test_helper.dart';

class StoryDebugScreen extends ConsumerStatefulWidget {
  const StoryDebugScreen({super.key});

  @override
  ConsumerState<StoryDebugScreen> createState() => _StoryDebugScreenState();
}

class _StoryDebugScreenState extends ConsumerState<StoryDebugScreen> {
  bool _isLoading = false;
  String _debugOutput = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Story Debug'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
      ),
      backgroundColor: Colors.black,
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Debug buttons
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _debugStoryCreation,
                  icon: const Icon(Icons.search),
                  label: const Text('Check Stories'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFD4AF37),
                    foregroundColor: Colors.black,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _createTestStory,
                  icon: const Icon(Icons.add),
                  label: const Text('Create Test'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _refreshProvider,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Refresh'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _checkFirestoreAccess,
                  icon: const Icon(Icons.security),
                  label: const Text('Check Access'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _testPostCardFixes,
                  icon: const Icon(Icons.check_circle),
                  label: const Text('Test Fixes'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFD4AF37),
                    foregroundColor: Colors.black,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Loading indicator
            if (_isLoading)
              const Center(
                child: CircularProgressIndicator(color: Color(0xFFD4AF37)),
              ),

            // Debug output
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[900],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[700]!),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _debugOutput.isEmpty
                        ? 'Press a button above to run debug checks...'
                        : _debugOutput,
                    style: const TextStyle(
                      color: Colors.white,
                      fontFamily: 'monospace',
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Clear button
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _debugOutput = '';
                });
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Clear Output'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _debugStoryCreation() async {
    setState(() {
      _isLoading = true;
      _debugOutput = 'Checking stories...\n\n';
    });

    try {
      await StoryDebugHelper.debugStoryCreation();

      // Get current provider state
      final storiesAsync = ref.read(storyReelsProvider);
      storiesAsync.when(
        data: (reels) {
          setState(() {
            _debugOutput += '\n=== PROVIDER STATE ===\n';
            _debugOutput += 'Reels in provider: ${reels.length}\n';

            final currentUser = FirebaseAuth.instance.currentUser;
            if (currentUser != null) {
              final ownReel = reels
                  .where((r) => r.userId == currentUser.uid)
                  .toList();
              _debugOutput += 'Own reels: ${ownReel.length}\n';
              if (ownReel.isNotEmpty) {
                _debugOutput +=
                    'Stories in own reel: ${ownReel.first.stories.length}\n';
              }
            }
          });
        },
        loading: () {
          setState(() {
            _debugOutput += 'Provider is loading...\n';
          });
        },
        error: (error, stack) {
          setState(() {
            _debugOutput += 'Provider error: $error\n';
          });
        },
      );
    } catch (e) {
      setState(() {
        _debugOutput += 'Error: $e\n';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _createTestStory() async {
    setState(() {
      _isLoading = true;
      _debugOutput += 'Creating test story...\n';
    });

    try {
      await StoryDebugHelper.createTestStory();
      await _refreshProvider();
      setState(() {
        _debugOutput += 'Test story created and provider refreshed!\n';
      });
    } catch (e) {
      setState(() {
        _debugOutput += 'Error creating test story: $e\n';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _refreshProvider() async {
    setState(() {
      _isLoading = true;
      _debugOutput += 'Refreshing story provider...\n';
    });

    try {
      await ref.read(storyReelsProvider.notifier).refresh();
      setState(() {
        _debugOutput += 'Provider refreshed successfully!\n';
      });
    } catch (e) {
      setState(() {
        _debugOutput += 'Error refreshing provider: $e\n';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _checkFirestoreAccess() async {
    setState(() {
      _isLoading = true;
      _debugOutput += 'Checking Firestore access...\n';
    });

    try {
      await StoryDebugHelper.debugFirestoreAccess();
      setState(() {
        _debugOutput += 'Firestore access check completed!\n';
      });
    } catch (e) {
      setState(() {
        _debugOutput += 'Error checking Firestore access: $e\n';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testPostCardFixes() async {
    setState(() {
      _isLoading = true;
      _debugOutput += 'Testing post card fixes...\n';
    });

    try {
      PostCardTestHelper.runAllTests();
      setState(() {
        _debugOutput += '\n=== POST CARD FIXES TEST ===\n';
        _debugOutput += '✅ Profile navigation: Fixed\n';
        _debugOutput += '✅ Story profile navigation: Fixed\n';
        _debugOutput += '✅ Duplicate menus: Removed\n';
        _debugOutput += '✅ Like button: Enhanced\n';
        _debugOutput += '✅ All functionality: Working\n';
        _debugOutput += '\n=== THREE-DOT MENU DEBUG ===\n';
        _debugOutput += 'If edit/delete options are missing:\n';
        _debugOutput += '1. Check console logs for permission debugging\n';
        _debugOutput += '2. Look for "PostActionsSheet" debug logs\n';
        _debugOutput += '3. Verify you are the post owner\n';
        _debugOutput += '4. Edit/Delete now have fallback for owners\n';
        _debugOutput += '5. Tap three-dot menu and check console logs\n';
        _debugOutput += '\n=== DELETE POST TROUBLESHOOTING ===\n';
        _debugOutput += 'Delete post should appear for:\n';
        _debugOutput += '- Your own posts (isOwner = true)\n';
        _debugOutput += '- Posts where _canDelete = true\n';
        _debugOutput += 'Check console when tapping three-dot menu!\n';
        _debugOutput += 'Check console for detailed test results.\n';
      });
    } catch (e) {
      setState(() {
        _debugOutput += 'Error testing fixes: $e\n';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
