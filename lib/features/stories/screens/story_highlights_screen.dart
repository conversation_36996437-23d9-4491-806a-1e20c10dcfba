import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:billionaires_social/core/utils/dialog_utils.dart';

class StoryHighlightsScreen extends ConsumerStatefulWidget {
  const StoryHighlightsScreen({super.key});

  @override
  ConsumerState<StoryHighlightsScreen> createState() =>
      _StoryHighlightsScreenState();
}

class _StoryHighlightsScreenState extends ConsumerState<StoryHighlightsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _highlightNameController =
      TextEditingController();
  final TextEditingController _highlightDescriptionController =
      TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _highlightNameController.dispose();
    _highlightDescriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentUser = FirebaseAuth.instance.currentUser;

    if (currentUser == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Story Highlights')),
        body: const Center(child: Text('Please log in to view highlights')),
      );
    }

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('Story Highlights'),
        backgroundColor: theme.appBarTheme.backgroundColor,
        foregroundColor: theme.appBarTheme.foregroundColor,
        elevation: 0,

        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'My Highlights'),
            Tab(text: 'Saved Stories'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [_buildHighlightsTab(), _buildSavedStoriesTab()],
      ),
    );
  }

  Widget _buildHighlightsTab() {
    final theme = Theme.of(context);
    final currentUser = FirebaseAuth.instance.currentUser!;

    return StreamBuilder<QuerySnapshot>(
      stream: FirebaseFirestore.instance
          .collection('highlights')
          .where('userId', isEqualTo: currentUser.uid)
          .orderBy('createdAt', descending: true)
          .snapshots(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: theme.colorScheme.error,
                ),
                const SizedBox(height: 16),
                Text(
                  'Failed to load highlights',
                  style: theme.textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                ElevatedButton(
                  onPressed: () => setState(() {}),
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        final highlights = snapshot.data?.docs ?? [];

        if (highlights.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.bookmark_border,
                  size: 64,
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                ),
                const SizedBox(height: 16),
                Text('No highlights yet', style: theme.textTheme.titleMedium),
                const SizedBox(height: 8),
                Text(
                  'Create your first highlight to save and organize your favorite stories',
                  style: theme.textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: () => _showCreateHighlightDialog(context),
                  icon: const Icon(Icons.add),
                  label: const Text('Create Highlight'),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: highlights.length,
          itemBuilder: (context, index) {
            final highlight = highlights[index].data() as Map<String, dynamic>;
            final highlightId = highlights[index].id;
            final name = highlight['name'] ?? 'Untitled Highlight';
            final description = highlight['description'] ?? '';
            final coverImageUrl = highlight['coverImageUrl'] ?? '';
            final storyCount = highlight['storyCount'] ?? 0;
            final createdAt = highlight['createdAt'] as Timestamp?;

            return Card(
              margin: const EdgeInsets.only(bottom: 12),
              child: InkWell(
                onTap: () => _openHighlight(highlightId, name),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      // Cover image
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            color: theme.colorScheme.surface,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: coverImageUrl.isNotEmpty
                              ? CachedNetworkImage(
                                  imageUrl: coverImageUrl,
                                  fit: BoxFit.cover,
                                  placeholder: (context, url) => Container(
                                    color: theme.colorScheme.surface,
                                    child: Icon(
                                      Icons.image,
                                      color: theme.colorScheme.onSurface
                                          .withValues(alpha: 0.5),
                                    ),
                                  ),
                                  errorWidget: (context, url, error) =>
                                      Container(
                                        color: theme.colorScheme.surface,
                                        child: Icon(
                                          Icons.image,
                                          color: theme.colorScheme.onSurface
                                              .withValues(alpha: 0.5),
                                        ),
                                      ),
                                )
                              : Icon(
                                  Icons.bookmark,
                                  color: theme.colorScheme.primary,
                                  size: 32,
                                ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      // Highlight info
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              name,
                              style: theme.textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            if (description.isNotEmpty) ...[
                              const SizedBox(height: 4),
                              Text(
                                description,
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: theme.colorScheme.onSurface.withValues(
                                    alpha: 0.7,
                                  ),
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Icon(
                                  Icons.photo_library,
                                  size: 16,
                                  color: theme.colorScheme.onSurface.withValues(
                                    alpha: 0.7,
                                  ),
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  '$storyCount stories',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: theme.colorScheme.onSurface
                                        .withValues(alpha: 0.7),
                                  ),
                                ),
                                const Spacer(),
                                if (createdAt != null)
                                  Text(
                                    _formatTimestamp(createdAt.toDate()),
                                    style: theme.textTheme.bodySmall?.copyWith(
                                      color: theme.colorScheme.onSurface
                                          .withValues(alpha: 0.7),
                                    ),
                                  ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildSavedStoriesTab() {
    final theme = Theme.of(context);
    final currentUser = FirebaseAuth.instance.currentUser!;

    return StreamBuilder<QuerySnapshot>(
      stream: FirebaseFirestore.instance
          .collection('savedStories')
          .where('userId', isEqualTo: currentUser.uid)
          .orderBy('savedAt', descending: true)
          .snapshots(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: theme.colorScheme.error,
                ),
                const SizedBox(height: 16),
                Text(
                  'Failed to load saved stories',
                  style: theme.textTheme.titleMedium,
                ),
              ],
            ),
          );
        }

        final savedStories = snapshot.data?.docs ?? [];

        if (savedStories.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.bookmark_border,
                  size: 64,
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                ),
                const SizedBox(height: 16),
                Text('No saved stories', style: theme.textTheme.titleMedium),
                const SizedBox(height: 8),
                Text(
                  'Stories you save will appear here',
                  style: theme.textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: savedStories.length,
          itemBuilder: (context, index) {
            final savedStory =
                savedStories[index].data() as Map<String, dynamic>;
            final storyId = savedStory['storyId'] ?? '';
            final savedAt = savedStory['savedAt'] as Timestamp?;
            final storyData =
                savedStory['storyData'] as Map<String, dynamic>? ?? {};

            return Card(
              margin: const EdgeInsets.only(bottom: 12),
              child: InkWell(
                onTap: () => _openSavedStory(storyId, storyData),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      // Story thumbnail
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Container(
                          width: 60,
                          height: 60,
                          decoration: BoxDecoration(
                            color: theme.colorScheme.surface,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: storyData['mediaUrl']?.isNotEmpty == true
                              ? CachedNetworkImage(
                                  imageUrl: storyData['mediaUrl'],
                                  fit: BoxFit.cover,
                                  placeholder: (context, url) => Container(
                                    color: theme.colorScheme.surface,
                                    child: Icon(
                                      Icons.image,
                                      color: theme.colorScheme.onSurface
                                          .withValues(alpha: 0.5),
                                    ),
                                  ),
                                  errorWidget: (context, url, error) =>
                                      Container(
                                        color: theme.colorScheme.surface,
                                        child: Icon(
                                          Icons.image,
                                          color: theme.colorScheme.onSurface
                                              .withValues(alpha: 0.5),
                                        ),
                                      ),
                                )
                              : Icon(
                                  Icons.text_fields,
                                  color: theme.colorScheme.onSurface.withValues(
                                    alpha: 0.5,
                                  ),
                                ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      // Story info
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              storyData['textOverlay'] ?? 'Story',
                              style: theme.textTheme.titleMedium,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Saved ${savedAt != null ? _formatTimestamp(savedAt.toDate()) : 'recently'}',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurface.withValues(
                                  alpha: 0.7,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Remove button
                      IconButton(
                        onPressed: () =>
                            _removeSavedStory(savedStories[index].id),
                        icon: const Icon(Icons.close),
                        tooltip: 'Remove from saved',
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  void _showCreateHighlightDialog(BuildContext context) {
    _highlightNameController.clear();
    _highlightDescriptionController.clear();

    showAppDialog(
      context: context,
      title: Text('Create Highlight'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _highlightNameController,
            decoration: const InputDecoration(
              labelText: 'Highlight Name',
              hintText: 'Enter highlight name',
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _highlightDescriptionController,
            decoration: const InputDecoration(
              labelText: 'Description (Optional)',
              hintText: 'Enter description',
            ),
            maxLines: 3,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_highlightNameController.text.trim().isNotEmpty) {
              _createHighlight();
              Navigator.of(context).pop();
            }
          },
          child: const Text('Create'),
        ),
      ],
    );
  }

  Future<void> _createHighlight() async {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) return;

    try {
      await FirebaseFirestore.instance.collection('highlights').add({
        'userId': currentUser.uid,
        'name': _highlightNameController.text.trim(),
        'description': _highlightDescriptionController.text.trim(),
        'coverImageUrl': '',
        'storyCount': 0,
        'stories': [],
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Highlight created successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error creating highlight: $e')));
      }
    }
  }

  Future<void> _removeSavedStory(String savedStoryId) async {
    try {
      await FirebaseFirestore.instance
          .collection('savedStories')
          .doc(savedStoryId)
          .delete();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Story removed from saved')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error removing story: $e')));
      }
    }
  }

  void _openHighlight(String highlightId, String highlightName) {
    // TODO: Navigate to highlight detail screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Opening highlight: $highlightName')),
    );
  }

  void _openSavedStory(String storyId, Map<String, dynamic> storyData) {
    // TODO: Navigate to story viewer
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Opening saved story')));
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${(difference.inDays / 7).round()}w ago';
    }
  }
}
