import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:billionaires_social/features/stories/models/unified_story_model.dart';

class StoryAnalyticsDashboardScreen extends ConsumerStatefulWidget {
  const StoryAnalyticsDashboardScreen({super.key});

  @override
  ConsumerState<StoryAnalyticsDashboardScreen> createState() =>
      _StoryAnalyticsDashboardScreenState();
}

class _StoryAnalyticsDashboardScreenState
    extends ConsumerState<StoryAnalyticsDashboardScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _selectedTimeRange = '7d'; // 7d, 30d, 90d
  final List<String> _timeRanges = ['7d', '30d', '90d'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentUser = FirebaseAuth.instance.currentUser;

    if (currentUser == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Story Analytics')),
        body: const Center(child: Text('Please log in to view analytics')),
      );
    }

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('Story Analytics'),
        backgroundColor: theme.appBarTheme.backgroundColor,
        foregroundColor: theme.appBarTheme.foregroundColor,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Overview'),
            Tab(text: 'Performance'),
            Tab(text: 'Audience'),
            Tab(text: 'Trends'),
          ],
        ),
      ),
      body: Column(
        children: [
          // Time range selector
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Text('Time Range:', style: theme.textTheme.titleMedium),
                const SizedBox(width: 16),
                ..._timeRanges.map((range) {
                  final isSelected = _selectedTimeRange == range;
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: Text(range),
                      selected: isSelected,
                      onSelected: (selected) {
                        if (selected) {
                          setState(() {
                            _selectedTimeRange = range;
                          });
                        }
                      },
                    ),
                  );
                }),
              ],
            ),
          ),
          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildPerformanceTab(),
                _buildAudienceTab(),
                _buildTrendsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    final theme = Theme.of(context);
    final currentUser = FirebaseAuth.instance.currentUser!;

    return FutureBuilder<Map<String, dynamic>>(
      future: _getOverviewAnalytics(currentUser.uid),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: theme.colorScheme.error,
                ),
                const SizedBox(height: 16),
                Text(
                  'Failed to load analytics',
                  style: theme.textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                ElevatedButton(
                  onPressed: () => setState(() {}),
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        final analytics = snapshot.data ?? {};
        final totalStories = analytics['totalStories'] ?? 0;
        final totalViews = analytics['totalViews'] ?? 0;
        final totalReactions = analytics['totalReactions'] ?? 0;
        final totalReplies = analytics['totalReplies'] ?? 0;
        final avgViewsPerStory = totalStories > 0
            ? (totalViews / totalStories).round()
            : 0;
        final engagementRate = totalViews > 0
            ? ((totalReactions + totalReplies) / totalViews * 100).round()
            : 0;

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Key metrics cards
              Text(
                'Key Metrics',
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 1.2,
                children: [
                  _buildMetricCard(
                    'Total Stories',
                    totalStories.toString(),
                    Icons.photo_library,
                    Colors.blue,
                  ),
                  _buildMetricCard(
                    'Total Views',
                    totalViews.toString(),
                    Icons.visibility,
                    Colors.green,
                  ),
                  _buildMetricCard(
                    'Avg Views/Story',
                    avgViewsPerStory.toString(),
                    Icons.trending_up,
                    Colors.orange,
                  ),
                  _buildMetricCard(
                    'Engagement Rate',
                    '$engagementRate%',
                    Icons.favorite,
                    Colors.red,
                  ),
                ],
              ),
              const SizedBox(height: 32),

              // Recent activity
              Text(
                'Recent Activity',
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              _buildRecentActivityList(analytics['recentActivity'] ?? []),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPerformanceTab() {
    final theme = Theme.of(context);
    final currentUser = FirebaseAuth.instance.currentUser!;

    return FutureBuilder<List<UnifiedStory>>(
      future: _getUserStories(currentUser.uid),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: theme.colorScheme.error,
                ),
                const SizedBox(height: 16),
                Text(
                  'Failed to load stories',
                  style: theme.textTheme.titleMedium,
                ),
              ],
            ),
          );
        }

        final stories = snapshot.data ?? [];

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Story Performance',
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              if (stories.isEmpty)
                Center(
                  child: Column(
                    children: [
                      Icon(
                        Icons.photo_library_outlined,
                        size: 64,
                        color: theme.colorScheme.onSurface.withValues(
                          alpha: 0.5,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No stories yet',
                        style: theme.textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Create your first story to see performance metrics',
                        style: theme.textTheme.bodyMedium,
                      ),
                    ],
                  ),
                )
              else
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: stories.length,
                  itemBuilder: (context, index) {
                    final story = stories[index];
                    return _buildStoryPerformanceCard(story);
                  },
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAudienceTab() {
    final theme = Theme.of(context);
    final currentUser = FirebaseAuth.instance.currentUser!;

    return FutureBuilder<Map<String, dynamic>>(
      future: _getAudienceAnalytics(currentUser.uid),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: theme.colorScheme.error,
                ),
                const SizedBox(height: 16),
                Text(
                  'Failed to load audience data',
                  style: theme.textTheme.titleMedium,
                ),
              ],
            ),
          );
        }

        final analytics = snapshot.data ?? {};
        final topViewers = analytics['topViewers'] ?? [];
        final viewerDemographics = analytics['demographics'] ?? {};

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Audience Insights',
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              // Top viewers
              Text('Top Viewers', style: theme.textTheme.titleMedium),
              const SizedBox(height: 8),
              if (topViewers.isEmpty)
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surface,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'No viewer data available',
                    style: theme.textTheme.bodyMedium,
                  ),
                )
              else
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: topViewers.length > 5 ? 5 : topViewers.length,
                  itemBuilder: (context, index) {
                    final viewer = topViewers[index];
                    return ListTile(
                      leading: CircleAvatar(child: Text('${index + 1}')),
                      title: Text(viewer['username'] ?? 'Unknown User'),
                      subtitle: Text('${viewer['viewCount']} views'),
                      trailing: const Icon(Icons.visibility),
                    );
                  },
                ),

              const SizedBox(height: 24),

              // Demographics
              Text('Demographics', style: theme.textTheme.titleMedium),
              const SizedBox(height: 8),
              _buildDemographicsSection(viewerDemographics, theme),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTrendsTab() {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Trends & Insights',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // View trends chart placeholder
          Container(
            height: 200,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.show_chart,
                  size: 48,
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                ),
                const SizedBox(height: 8),
                Text('View Trends Chart', style: theme.textTheme.titleMedium),
                const SizedBox(height: 4),
                Text(
                  'Interactive charts coming soon',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Best performing times
          Text('Best Performing Times', style: theme.textTheme.titleMedium),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.schedule,
                  size: 48,
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                ),
                const SizedBox(height: 8),
                Text(
                  'Time analysis coming soon',
                  style: theme.textTheme.bodyMedium,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetricCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(color: color.withValues(alpha: 0.8), fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStoryPerformanceCard(UnifiedStory story) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                if (story.mediaUrl.isNotEmpty)
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.network(
                      story.mediaUrl,
                      width: 60,
                      height: 60,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          width: 60,
                          height: 60,
                          decoration: BoxDecoration(
                            color: theme.colorScheme.surface,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.image,
                            color: theme.colorScheme.onSurface.withValues(
                              alpha: 0.5,
                            ),
                          ),
                        );
                      },
                    ),
                  )
                else
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surface,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.text_fields,
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                    ),
                  ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        story.textOverlay ?? 'Story',
                        style: theme.textTheme.titleMedium,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        _formatTimestamp(story.createdAt),
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withValues(
                            alpha: 0.7,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                _buildPerformanceMetric('Views', '0', Icons.visibility),
                const SizedBox(width: 16),
                _buildPerformanceMetric('Reactions', '0', Icons.favorite),
                const SizedBox(width: 16),
                _buildPerformanceMetric('Replies', '0', Icons.reply),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceMetric(String label, String value, IconData icon) {
    final theme = Theme.of(context);

    return Expanded(
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
          const SizedBox(width: 4),
          Text('$value $label', style: theme.textTheme.bodySmall),
        ],
      ),
    );
  }

  Widget _buildRecentActivityList(List<dynamic> activities) {
    final theme = Theme.of(context);

    if (activities.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text('No recent activity', style: theme.textTheme.bodyMedium),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: activities.length > 5 ? 5 : activities.length,
      itemBuilder: (context, index) {
        final activity = activities[index];
        return ListTile(
          leading: CircleAvatar(
            backgroundColor: theme.colorScheme.primary.withValues(alpha: 0.1),
            child: Icon(
              _getActivityIcon(activity['type']),
              color: theme.colorScheme.primary,
              size: 20,
            ),
          ),
          title: Text(activity['description'] ?? 'Activity'),
          subtitle: Text(_formatTimestamp(activity['timestamp'])),
        );
      },
    );
  }

  IconData _getActivityIcon(String type) {
    switch (type) {
      case 'view':
        return Icons.visibility;
      case 'reaction':
        return Icons.favorite;
      case 'reply':
        return Icons.reply;
      default:
        return Icons.info;
    }
  }

  String _formatTimestamp(dynamic timestamp) {
    if (timestamp == null) return 'Unknown time';

    DateTime dateTime;
    if (timestamp is Timestamp) {
      dateTime = timestamp.toDate();
    } else if (timestamp is DateTime) {
      dateTime = timestamp;
    } else {
      return 'Unknown time';
    }

    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  Future<Map<String, dynamic>> _getOverviewAnalytics(String userId) async {
    // TODO: Implement actual analytics fetching
    // This is a placeholder that returns mock data
    await Future.delayed(const Duration(seconds: 1));

    return {
      'totalStories': 15,
      'totalViews': 1250,
      'totalReactions': 89,
      'totalReplies': 23,
      'recentActivity': [
        {
          'type': 'view',
          'description': 'Someone viewed your story',
          'timestamp': Timestamp.now(),
        },
        {
          'type': 'reaction',
          'description': 'Someone reacted to your story',
          'timestamp': Timestamp.fromDate(
            DateTime.now().subtract(const Duration(minutes: 30)),
          ),
        },
      ],
    };
  }

  Future<List<UnifiedStory>> _getUserStories(String userId) async {
    // TODO: Implement actual story fetching
    // This is a placeholder that returns mock data
    await Future.delayed(const Duration(seconds: 1));

    return [];
  }

  Future<Map<String, dynamic>> _getAudienceAnalytics(String userId) async {
    // TODO: Implement actual audience analytics
    // This is a placeholder that returns mock data
    await Future.delayed(const Duration(seconds: 1));

    return {
      'topViewers': [
        {'username': 'user1', 'viewCount': 5},
        {'username': 'user2', 'viewCount': 3},
      ],
      'demographics': {
        'ageGroups': {'18-24': 30, '25-34': 45, '35-44': 20, '45+': 5},
        'genders': {'male': 60, 'female': 35, 'other': 5},
        'locations': {'US': 40, 'UK': 25, 'CA': 15, 'Other': 20},
      },
    };
  }

  Widget _buildDemographicsSection(
    Map<String, dynamic> demographics,
    ThemeData theme,
  ) {
    if (demographics.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Icon(
              Icons.people_outline,
              size: 48,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 8),
            Text(
              'Demographics data coming soon',
              style: theme.textTheme.bodyMedium,
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (demographics['ageGroups'] != null) ...[
            Text('Age Groups', style: theme.textTheme.titleSmall),
            const SizedBox(height: 8),
            ...((demographics['ageGroups'] as Map<String, dynamic>).entries.map(
              (entry) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 2),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [Text(entry.key), Text('${entry.value}%')],
                ),
              ),
            )),
            const SizedBox(height: 16),
          ],
          if (demographics['genders'] != null) ...[
            Text('Gender Distribution', style: theme.textTheme.titleSmall),
            const SizedBox(height: 8),
            ...((demographics['genders'] as Map<String, dynamic>).entries.map(
              (entry) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 2),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(entry.key.toString().toUpperCase()),
                    Text('${entry.value}%'),
                  ],
                ),
              ),
            )),
          ],
        ],
      ),
    );
  }
}
