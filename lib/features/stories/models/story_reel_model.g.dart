// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'story_reel_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_StoryTag _$StoryTagFromJson(Map<String, dynamic> json) => _StoryTag(
  userId: json['userId'] as String,
  username: json['username'] as String,
  name: json['name'] as String?,
  x: (json['x'] as num).toDouble(),
  y: (json['y'] as num).toDouble(),
);

Map<String, dynamic> _$StoryTagToJson(_StoryTag instance) => <String, dynamic>{
  'userId': instance.userId,
  'username': instance.username,
  'name': instance.name,
  'x': instance.x,
  'y': instance.y,
};

_StoryReel _$StoryReelFromJson(Map<String, dynamic> json) => _StoryReel(
  id: json['id'] as String,
  userId: json['userId'] as String,
  username: json['username'] as String,
  userAvatarUrl: json['userAvatarUrl'] as String,
  stories: (json['stories'] as List<dynamic>)
      .map((e) => StoryItem.fromJson(e as Map<String, dynamic>))
      .toList(),
  isAllViewed: json['isAllViewed'] as bool? ?? false,
  isCloseFriend: json['isCloseFriend'] as bool? ?? false,
);

Map<String, dynamic> _$StoryReelToJson(_StoryReel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'username': instance.username,
      'userAvatarUrl': instance.userAvatarUrl,
      'stories': instance.stories,
      'isAllViewed': instance.isAllViewed,
      'isCloseFriend': instance.isCloseFriend,
    };

_StoryItem _$StoryItemFromJson(Map<String, dynamic> json) => _StoryItem(
  id: json['id'] as String,
  userId: json['userId'] as String,
  mediaUrl: json['mediaUrl'] as String,
  mediaType: $enumDecode(_$MediaTypeEnumMap, json['mediaType']),
  duration: Duration(microseconds: (json['duration'] as num).toInt()),
  timestamp: DateTime.parse(json['timestamp'] as String),
  textOverlay: json['textOverlay'] as String?,
  textColor: (json['textColor'] as num?)?.toInt(),
  textSize: (json['textSize'] as num?)?.toDouble(),
  textPosition: (json['textPosition'] as Map<String, dynamic>?)?.map(
    (k, e) => MapEntry(k, (e as num).toDouble()),
  ),
  backgroundColor: (json['backgroundColor'] as num?)?.toInt(),
  filter: json['filter'] as String?,
  drawingPoints: (json['drawingPoints'] as List<dynamic>?)
      ?.map((e) => e as Map<String, dynamic>)
      .toList(),
  drawingColor: (json['drawingColor'] as num?)?.toInt(),
  drawingWidth: (json['drawingWidth'] as num?)?.toDouble(),
  tags: (json['tags'] as List<dynamic>?)
      ?.map((e) => StoryTag.fromJson(e as Map<String, dynamic>))
      .toList(),
  music: json['music'] as Map<String, dynamic>?,
  musicArtist: json['musicArtist'] as String?,
  location: json['location'] as Map<String, dynamic>?,
  privacy: json['privacy'] as String?,
  isPublic: json['isPublic'] as bool?,
  isSeen: json['isSeen'] as bool?,
  isCloseFriend: json['isCloseFriend'] as bool?,
);

Map<String, dynamic> _$StoryItemToJson(_StoryItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'mediaUrl': instance.mediaUrl,
      'mediaType': _$MediaTypeEnumMap[instance.mediaType]!,
      'duration': instance.duration.inMicroseconds,
      'timestamp': instance.timestamp.toIso8601String(),
      'textOverlay': instance.textOverlay,
      'textColor': instance.textColor,
      'textSize': instance.textSize,
      'textPosition': instance.textPosition,
      'backgroundColor': instance.backgroundColor,
      'filter': instance.filter,
      'drawingPoints': instance.drawingPoints,
      'drawingColor': instance.drawingColor,
      'drawingWidth': instance.drawingWidth,
      'tags': instance.tags,
      'music': instance.music,
      'musicArtist': instance.musicArtist,
      'location': instance.location,
      'privacy': instance.privacy,
      'isPublic': instance.isPublic,
      'isSeen': instance.isSeen,
      'isCloseFriend': instance.isCloseFriend,
    };

const _$MediaTypeEnumMap = {
  MediaType.image: 'image',
  MediaType.video: 'video',
  MediaType.text: 'text',
};
