// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'unified_story_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UnifiedStory {

 String get id; String get userId; String get userName; String get userAvatarUrl; String get mediaUrl; StoryMediaType get mediaType; DateTime get createdAt; DateTime get expiresAt; Duration get duration; DateTime get timestamp;// Privacy and visibility
 StoryPrivacy get privacy; StoryVisibility get visibility; List<String> get viewers; List<String> get allowedTo; List<String> get hiddenFromUserIds; List<String> get allowedGroupIds;// Content metadata
 String? get caption; String? get textOverlay; int? get textColor; double? get textSize; Map<String, double>? get textPosition; int? get backgroundColor; String? get filter;// Drawing and text elements
 List<DrawingPoint> get drawingPoints; int? get drawingColor; double? get drawingWidth; List<TextElement> get textElements;// User interactions
 List<StoryTag> get tags; List<StoryReaction> get reactions; List<StoryReply> get replies; List<String> get hashtags; List<String> get mentions;// Media and location
 Map<String, dynamic>? get music; String? get musicArtist; Map<String, dynamic>? get location; String? get locationName;// Story features
 bool get isHighlighted; bool get isArchived; bool get isPublic; bool get isSeen; bool get isCloseFriend; StoryType get storyType;// Analytics and tracking
 int get viewCount; int get replyCount; int get reactionCount; int get shareCount; double get completionRate; double get skipRate; List<String> get topViewers; Map<String, int> get reactionBreakdown;// Additional metadata
 Map<String, dynamic>? get metadata;
/// Create a copy of UnifiedStory
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UnifiedStoryCopyWith<UnifiedStory> get copyWith => _$UnifiedStoryCopyWithImpl<UnifiedStory>(this as UnifiedStory, _$identity);

  /// Serializes this UnifiedStory to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UnifiedStory&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.userName, userName) || other.userName == userName)&&(identical(other.userAvatarUrl, userAvatarUrl) || other.userAvatarUrl == userAvatarUrl)&&(identical(other.mediaUrl, mediaUrl) || other.mediaUrl == mediaUrl)&&(identical(other.mediaType, mediaType) || other.mediaType == mediaType)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.expiresAt, expiresAt) || other.expiresAt == expiresAt)&&(identical(other.duration, duration) || other.duration == duration)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.privacy, privacy) || other.privacy == privacy)&&(identical(other.visibility, visibility) || other.visibility == visibility)&&const DeepCollectionEquality().equals(other.viewers, viewers)&&const DeepCollectionEquality().equals(other.allowedTo, allowedTo)&&const DeepCollectionEquality().equals(other.hiddenFromUserIds, hiddenFromUserIds)&&const DeepCollectionEquality().equals(other.allowedGroupIds, allowedGroupIds)&&(identical(other.caption, caption) || other.caption == caption)&&(identical(other.textOverlay, textOverlay) || other.textOverlay == textOverlay)&&(identical(other.textColor, textColor) || other.textColor == textColor)&&(identical(other.textSize, textSize) || other.textSize == textSize)&&const DeepCollectionEquality().equals(other.textPosition, textPosition)&&(identical(other.backgroundColor, backgroundColor) || other.backgroundColor == backgroundColor)&&(identical(other.filter, filter) || other.filter == filter)&&const DeepCollectionEquality().equals(other.drawingPoints, drawingPoints)&&(identical(other.drawingColor, drawingColor) || other.drawingColor == drawingColor)&&(identical(other.drawingWidth, drawingWidth) || other.drawingWidth == drawingWidth)&&const DeepCollectionEquality().equals(other.textElements, textElements)&&const DeepCollectionEquality().equals(other.tags, tags)&&const DeepCollectionEquality().equals(other.reactions, reactions)&&const DeepCollectionEquality().equals(other.replies, replies)&&const DeepCollectionEquality().equals(other.hashtags, hashtags)&&const DeepCollectionEquality().equals(other.mentions, mentions)&&const DeepCollectionEquality().equals(other.music, music)&&(identical(other.musicArtist, musicArtist) || other.musicArtist == musicArtist)&&const DeepCollectionEquality().equals(other.location, location)&&(identical(other.locationName, locationName) || other.locationName == locationName)&&(identical(other.isHighlighted, isHighlighted) || other.isHighlighted == isHighlighted)&&(identical(other.isArchived, isArchived) || other.isArchived == isArchived)&&(identical(other.isPublic, isPublic) || other.isPublic == isPublic)&&(identical(other.isSeen, isSeen) || other.isSeen == isSeen)&&(identical(other.isCloseFriend, isCloseFriend) || other.isCloseFriend == isCloseFriend)&&(identical(other.storyType, storyType) || other.storyType == storyType)&&(identical(other.viewCount, viewCount) || other.viewCount == viewCount)&&(identical(other.replyCount, replyCount) || other.replyCount == replyCount)&&(identical(other.reactionCount, reactionCount) || other.reactionCount == reactionCount)&&(identical(other.shareCount, shareCount) || other.shareCount == shareCount)&&(identical(other.completionRate, completionRate) || other.completionRate == completionRate)&&(identical(other.skipRate, skipRate) || other.skipRate == skipRate)&&const DeepCollectionEquality().equals(other.topViewers, topViewers)&&const DeepCollectionEquality().equals(other.reactionBreakdown, reactionBreakdown)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,userId,userName,userAvatarUrl,mediaUrl,mediaType,createdAt,expiresAt,duration,timestamp,privacy,visibility,const DeepCollectionEquality().hash(viewers),const DeepCollectionEquality().hash(allowedTo),const DeepCollectionEquality().hash(hiddenFromUserIds),const DeepCollectionEquality().hash(allowedGroupIds),caption,textOverlay,textColor,textSize,const DeepCollectionEquality().hash(textPosition),backgroundColor,filter,const DeepCollectionEquality().hash(drawingPoints),drawingColor,drawingWidth,const DeepCollectionEquality().hash(textElements),const DeepCollectionEquality().hash(tags),const DeepCollectionEquality().hash(reactions),const DeepCollectionEquality().hash(replies),const DeepCollectionEquality().hash(hashtags),const DeepCollectionEquality().hash(mentions),const DeepCollectionEquality().hash(music),musicArtist,const DeepCollectionEquality().hash(location),locationName,isHighlighted,isArchived,isPublic,isSeen,isCloseFriend,storyType,viewCount,replyCount,reactionCount,shareCount,completionRate,skipRate,const DeepCollectionEquality().hash(topViewers),const DeepCollectionEquality().hash(reactionBreakdown),const DeepCollectionEquality().hash(metadata)]);

@override
String toString() {
  return 'UnifiedStory(id: $id, userId: $userId, userName: $userName, userAvatarUrl: $userAvatarUrl, mediaUrl: $mediaUrl, mediaType: $mediaType, createdAt: $createdAt, expiresAt: $expiresAt, duration: $duration, timestamp: $timestamp, privacy: $privacy, visibility: $visibility, viewers: $viewers, allowedTo: $allowedTo, hiddenFromUserIds: $hiddenFromUserIds, allowedGroupIds: $allowedGroupIds, caption: $caption, textOverlay: $textOverlay, textColor: $textColor, textSize: $textSize, textPosition: $textPosition, backgroundColor: $backgroundColor, filter: $filter, drawingPoints: $drawingPoints, drawingColor: $drawingColor, drawingWidth: $drawingWidth, textElements: $textElements, tags: $tags, reactions: $reactions, replies: $replies, hashtags: $hashtags, mentions: $mentions, music: $music, musicArtist: $musicArtist, location: $location, locationName: $locationName, isHighlighted: $isHighlighted, isArchived: $isArchived, isPublic: $isPublic, isSeen: $isSeen, isCloseFriend: $isCloseFriend, storyType: $storyType, viewCount: $viewCount, replyCount: $replyCount, reactionCount: $reactionCount, shareCount: $shareCount, completionRate: $completionRate, skipRate: $skipRate, topViewers: $topViewers, reactionBreakdown: $reactionBreakdown, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $UnifiedStoryCopyWith<$Res>  {
  factory $UnifiedStoryCopyWith(UnifiedStory value, $Res Function(UnifiedStory) _then) = _$UnifiedStoryCopyWithImpl;
@useResult
$Res call({
 String id, String userId, String userName, String userAvatarUrl, String mediaUrl, StoryMediaType mediaType, DateTime createdAt, DateTime expiresAt, Duration duration, DateTime timestamp, StoryPrivacy privacy, StoryVisibility visibility, List<String> viewers, List<String> allowedTo, List<String> hiddenFromUserIds, List<String> allowedGroupIds, String? caption, String? textOverlay, int? textColor, double? textSize, Map<String, double>? textPosition, int? backgroundColor, String? filter, List<DrawingPoint> drawingPoints, int? drawingColor, double? drawingWidth, List<TextElement> textElements, List<StoryTag> tags, List<StoryReaction> reactions, List<StoryReply> replies, List<String> hashtags, List<String> mentions, Map<String, dynamic>? music, String? musicArtist, Map<String, dynamic>? location, String? locationName, bool isHighlighted, bool isArchived, bool isPublic, bool isSeen, bool isCloseFriend, StoryType storyType, int viewCount, int replyCount, int reactionCount, int shareCount, double completionRate, double skipRate, List<String> topViewers, Map<String, int> reactionBreakdown, Map<String, dynamic>? metadata
});




}
/// @nodoc
class _$UnifiedStoryCopyWithImpl<$Res>
    implements $UnifiedStoryCopyWith<$Res> {
  _$UnifiedStoryCopyWithImpl(this._self, this._then);

  final UnifiedStory _self;
  final $Res Function(UnifiedStory) _then;

/// Create a copy of UnifiedStory
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? userName = null,Object? userAvatarUrl = null,Object? mediaUrl = null,Object? mediaType = null,Object? createdAt = null,Object? expiresAt = null,Object? duration = null,Object? timestamp = null,Object? privacy = null,Object? visibility = null,Object? viewers = null,Object? allowedTo = null,Object? hiddenFromUserIds = null,Object? allowedGroupIds = null,Object? caption = freezed,Object? textOverlay = freezed,Object? textColor = freezed,Object? textSize = freezed,Object? textPosition = freezed,Object? backgroundColor = freezed,Object? filter = freezed,Object? drawingPoints = null,Object? drawingColor = freezed,Object? drawingWidth = freezed,Object? textElements = null,Object? tags = null,Object? reactions = null,Object? replies = null,Object? hashtags = null,Object? mentions = null,Object? music = freezed,Object? musicArtist = freezed,Object? location = freezed,Object? locationName = freezed,Object? isHighlighted = null,Object? isArchived = null,Object? isPublic = null,Object? isSeen = null,Object? isCloseFriend = null,Object? storyType = null,Object? viewCount = null,Object? replyCount = null,Object? reactionCount = null,Object? shareCount = null,Object? completionRate = null,Object? skipRate = null,Object? topViewers = null,Object? reactionBreakdown = null,Object? metadata = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,userName: null == userName ? _self.userName : userName // ignore: cast_nullable_to_non_nullable
as String,userAvatarUrl: null == userAvatarUrl ? _self.userAvatarUrl : userAvatarUrl // ignore: cast_nullable_to_non_nullable
as String,mediaUrl: null == mediaUrl ? _self.mediaUrl : mediaUrl // ignore: cast_nullable_to_non_nullable
as String,mediaType: null == mediaType ? _self.mediaType : mediaType // ignore: cast_nullable_to_non_nullable
as StoryMediaType,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,expiresAt: null == expiresAt ? _self.expiresAt : expiresAt // ignore: cast_nullable_to_non_nullable
as DateTime,duration: null == duration ? _self.duration : duration // ignore: cast_nullable_to_non_nullable
as Duration,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,privacy: null == privacy ? _self.privacy : privacy // ignore: cast_nullable_to_non_nullable
as StoryPrivacy,visibility: null == visibility ? _self.visibility : visibility // ignore: cast_nullable_to_non_nullable
as StoryVisibility,viewers: null == viewers ? _self.viewers : viewers // ignore: cast_nullable_to_non_nullable
as List<String>,allowedTo: null == allowedTo ? _self.allowedTo : allowedTo // ignore: cast_nullable_to_non_nullable
as List<String>,hiddenFromUserIds: null == hiddenFromUserIds ? _self.hiddenFromUserIds : hiddenFromUserIds // ignore: cast_nullable_to_non_nullable
as List<String>,allowedGroupIds: null == allowedGroupIds ? _self.allowedGroupIds : allowedGroupIds // ignore: cast_nullable_to_non_nullable
as List<String>,caption: freezed == caption ? _self.caption : caption // ignore: cast_nullable_to_non_nullable
as String?,textOverlay: freezed == textOverlay ? _self.textOverlay : textOverlay // ignore: cast_nullable_to_non_nullable
as String?,textColor: freezed == textColor ? _self.textColor : textColor // ignore: cast_nullable_to_non_nullable
as int?,textSize: freezed == textSize ? _self.textSize : textSize // ignore: cast_nullable_to_non_nullable
as double?,textPosition: freezed == textPosition ? _self.textPosition : textPosition // ignore: cast_nullable_to_non_nullable
as Map<String, double>?,backgroundColor: freezed == backgroundColor ? _self.backgroundColor : backgroundColor // ignore: cast_nullable_to_non_nullable
as int?,filter: freezed == filter ? _self.filter : filter // ignore: cast_nullable_to_non_nullable
as String?,drawingPoints: null == drawingPoints ? _self.drawingPoints : drawingPoints // ignore: cast_nullable_to_non_nullable
as List<DrawingPoint>,drawingColor: freezed == drawingColor ? _self.drawingColor : drawingColor // ignore: cast_nullable_to_non_nullable
as int?,drawingWidth: freezed == drawingWidth ? _self.drawingWidth : drawingWidth // ignore: cast_nullable_to_non_nullable
as double?,textElements: null == textElements ? _self.textElements : textElements // ignore: cast_nullable_to_non_nullable
as List<TextElement>,tags: null == tags ? _self.tags : tags // ignore: cast_nullable_to_non_nullable
as List<StoryTag>,reactions: null == reactions ? _self.reactions : reactions // ignore: cast_nullable_to_non_nullable
as List<StoryReaction>,replies: null == replies ? _self.replies : replies // ignore: cast_nullable_to_non_nullable
as List<StoryReply>,hashtags: null == hashtags ? _self.hashtags : hashtags // ignore: cast_nullable_to_non_nullable
as List<String>,mentions: null == mentions ? _self.mentions : mentions // ignore: cast_nullable_to_non_nullable
as List<String>,music: freezed == music ? _self.music : music // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,musicArtist: freezed == musicArtist ? _self.musicArtist : musicArtist // ignore: cast_nullable_to_non_nullable
as String?,location: freezed == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,locationName: freezed == locationName ? _self.locationName : locationName // ignore: cast_nullable_to_non_nullable
as String?,isHighlighted: null == isHighlighted ? _self.isHighlighted : isHighlighted // ignore: cast_nullable_to_non_nullable
as bool,isArchived: null == isArchived ? _self.isArchived : isArchived // ignore: cast_nullable_to_non_nullable
as bool,isPublic: null == isPublic ? _self.isPublic : isPublic // ignore: cast_nullable_to_non_nullable
as bool,isSeen: null == isSeen ? _self.isSeen : isSeen // ignore: cast_nullable_to_non_nullable
as bool,isCloseFriend: null == isCloseFriend ? _self.isCloseFriend : isCloseFriend // ignore: cast_nullable_to_non_nullable
as bool,storyType: null == storyType ? _self.storyType : storyType // ignore: cast_nullable_to_non_nullable
as StoryType,viewCount: null == viewCount ? _self.viewCount : viewCount // ignore: cast_nullable_to_non_nullable
as int,replyCount: null == replyCount ? _self.replyCount : replyCount // ignore: cast_nullable_to_non_nullable
as int,reactionCount: null == reactionCount ? _self.reactionCount : reactionCount // ignore: cast_nullable_to_non_nullable
as int,shareCount: null == shareCount ? _self.shareCount : shareCount // ignore: cast_nullable_to_non_nullable
as int,completionRate: null == completionRate ? _self.completionRate : completionRate // ignore: cast_nullable_to_non_nullable
as double,skipRate: null == skipRate ? _self.skipRate : skipRate // ignore: cast_nullable_to_non_nullable
as double,topViewers: null == topViewers ? _self.topViewers : topViewers // ignore: cast_nullable_to_non_nullable
as List<String>,reactionBreakdown: null == reactionBreakdown ? _self.reactionBreakdown : reactionBreakdown // ignore: cast_nullable_to_non_nullable
as Map<String, int>,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [UnifiedStory].
extension UnifiedStoryPatterns on UnifiedStory {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _UnifiedStory value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _UnifiedStory() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _UnifiedStory value)  $default,){
final _that = this;
switch (_that) {
case _UnifiedStory():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _UnifiedStory value)?  $default,){
final _that = this;
switch (_that) {
case _UnifiedStory() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  String userName,  String userAvatarUrl,  String mediaUrl,  StoryMediaType mediaType,  DateTime createdAt,  DateTime expiresAt,  Duration duration,  DateTime timestamp,  StoryPrivacy privacy,  StoryVisibility visibility,  List<String> viewers,  List<String> allowedTo,  List<String> hiddenFromUserIds,  List<String> allowedGroupIds,  String? caption,  String? textOverlay,  int? textColor,  double? textSize,  Map<String, double>? textPosition,  int? backgroundColor,  String? filter,  List<DrawingPoint> drawingPoints,  int? drawingColor,  double? drawingWidth,  List<TextElement> textElements,  List<StoryTag> tags,  List<StoryReaction> reactions,  List<StoryReply> replies,  List<String> hashtags,  List<String> mentions,  Map<String, dynamic>? music,  String? musicArtist,  Map<String, dynamic>? location,  String? locationName,  bool isHighlighted,  bool isArchived,  bool isPublic,  bool isSeen,  bool isCloseFriend,  StoryType storyType,  int viewCount,  int replyCount,  int reactionCount,  int shareCount,  double completionRate,  double skipRate,  List<String> topViewers,  Map<String, int> reactionBreakdown,  Map<String, dynamic>? metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _UnifiedStory() when $default != null:
return $default(_that.id,_that.userId,_that.userName,_that.userAvatarUrl,_that.mediaUrl,_that.mediaType,_that.createdAt,_that.expiresAt,_that.duration,_that.timestamp,_that.privacy,_that.visibility,_that.viewers,_that.allowedTo,_that.hiddenFromUserIds,_that.allowedGroupIds,_that.caption,_that.textOverlay,_that.textColor,_that.textSize,_that.textPosition,_that.backgroundColor,_that.filter,_that.drawingPoints,_that.drawingColor,_that.drawingWidth,_that.textElements,_that.tags,_that.reactions,_that.replies,_that.hashtags,_that.mentions,_that.music,_that.musicArtist,_that.location,_that.locationName,_that.isHighlighted,_that.isArchived,_that.isPublic,_that.isSeen,_that.isCloseFriend,_that.storyType,_that.viewCount,_that.replyCount,_that.reactionCount,_that.shareCount,_that.completionRate,_that.skipRate,_that.topViewers,_that.reactionBreakdown,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  String userName,  String userAvatarUrl,  String mediaUrl,  StoryMediaType mediaType,  DateTime createdAt,  DateTime expiresAt,  Duration duration,  DateTime timestamp,  StoryPrivacy privacy,  StoryVisibility visibility,  List<String> viewers,  List<String> allowedTo,  List<String> hiddenFromUserIds,  List<String> allowedGroupIds,  String? caption,  String? textOverlay,  int? textColor,  double? textSize,  Map<String, double>? textPosition,  int? backgroundColor,  String? filter,  List<DrawingPoint> drawingPoints,  int? drawingColor,  double? drawingWidth,  List<TextElement> textElements,  List<StoryTag> tags,  List<StoryReaction> reactions,  List<StoryReply> replies,  List<String> hashtags,  List<String> mentions,  Map<String, dynamic>? music,  String? musicArtist,  Map<String, dynamic>? location,  String? locationName,  bool isHighlighted,  bool isArchived,  bool isPublic,  bool isSeen,  bool isCloseFriend,  StoryType storyType,  int viewCount,  int replyCount,  int reactionCount,  int shareCount,  double completionRate,  double skipRate,  List<String> topViewers,  Map<String, int> reactionBreakdown,  Map<String, dynamic>? metadata)  $default,) {final _that = this;
switch (_that) {
case _UnifiedStory():
return $default(_that.id,_that.userId,_that.userName,_that.userAvatarUrl,_that.mediaUrl,_that.mediaType,_that.createdAt,_that.expiresAt,_that.duration,_that.timestamp,_that.privacy,_that.visibility,_that.viewers,_that.allowedTo,_that.hiddenFromUserIds,_that.allowedGroupIds,_that.caption,_that.textOverlay,_that.textColor,_that.textSize,_that.textPosition,_that.backgroundColor,_that.filter,_that.drawingPoints,_that.drawingColor,_that.drawingWidth,_that.textElements,_that.tags,_that.reactions,_that.replies,_that.hashtags,_that.mentions,_that.music,_that.musicArtist,_that.location,_that.locationName,_that.isHighlighted,_that.isArchived,_that.isPublic,_that.isSeen,_that.isCloseFriend,_that.storyType,_that.viewCount,_that.replyCount,_that.reactionCount,_that.shareCount,_that.completionRate,_that.skipRate,_that.topViewers,_that.reactionBreakdown,_that.metadata);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  String userName,  String userAvatarUrl,  String mediaUrl,  StoryMediaType mediaType,  DateTime createdAt,  DateTime expiresAt,  Duration duration,  DateTime timestamp,  StoryPrivacy privacy,  StoryVisibility visibility,  List<String> viewers,  List<String> allowedTo,  List<String> hiddenFromUserIds,  List<String> allowedGroupIds,  String? caption,  String? textOverlay,  int? textColor,  double? textSize,  Map<String, double>? textPosition,  int? backgroundColor,  String? filter,  List<DrawingPoint> drawingPoints,  int? drawingColor,  double? drawingWidth,  List<TextElement> textElements,  List<StoryTag> tags,  List<StoryReaction> reactions,  List<StoryReply> replies,  List<String> hashtags,  List<String> mentions,  Map<String, dynamic>? music,  String? musicArtist,  Map<String, dynamic>? location,  String? locationName,  bool isHighlighted,  bool isArchived,  bool isPublic,  bool isSeen,  bool isCloseFriend,  StoryType storyType,  int viewCount,  int replyCount,  int reactionCount,  int shareCount,  double completionRate,  double skipRate,  List<String> topViewers,  Map<String, int> reactionBreakdown,  Map<String, dynamic>? metadata)?  $default,) {final _that = this;
switch (_that) {
case _UnifiedStory() when $default != null:
return $default(_that.id,_that.userId,_that.userName,_that.userAvatarUrl,_that.mediaUrl,_that.mediaType,_that.createdAt,_that.expiresAt,_that.duration,_that.timestamp,_that.privacy,_that.visibility,_that.viewers,_that.allowedTo,_that.hiddenFromUserIds,_that.allowedGroupIds,_that.caption,_that.textOverlay,_that.textColor,_that.textSize,_that.textPosition,_that.backgroundColor,_that.filter,_that.drawingPoints,_that.drawingColor,_that.drawingWidth,_that.textElements,_that.tags,_that.reactions,_that.replies,_that.hashtags,_that.mentions,_that.music,_that.musicArtist,_that.location,_that.locationName,_that.isHighlighted,_that.isArchived,_that.isPublic,_that.isSeen,_that.isCloseFriend,_that.storyType,_that.viewCount,_that.replyCount,_that.reactionCount,_that.shareCount,_that.completionRate,_that.skipRate,_that.topViewers,_that.reactionBreakdown,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _UnifiedStory implements UnifiedStory {
  const _UnifiedStory({required this.id, required this.userId, required this.userName, required this.userAvatarUrl, required this.mediaUrl, required this.mediaType, required this.createdAt, required this.expiresAt, required this.duration, required this.timestamp, this.privacy = StoryPrivacy.public, this.visibility = StoryVisibility.public, final  List<String> viewers = const [], final  List<String> allowedTo = const [], final  List<String> hiddenFromUserIds = const [], final  List<String> allowedGroupIds = const [], this.caption, this.textOverlay, this.textColor, this.textSize, final  Map<String, double>? textPosition, this.backgroundColor, this.filter, final  List<DrawingPoint> drawingPoints = const [], this.drawingColor, this.drawingWidth, final  List<TextElement> textElements = const [], final  List<StoryTag> tags = const [], final  List<StoryReaction> reactions = const [], final  List<StoryReply> replies = const [], final  List<String> hashtags = const [], final  List<String> mentions = const [], final  Map<String, dynamic>? music, this.musicArtist, final  Map<String, dynamic>? location, this.locationName, this.isHighlighted = false, this.isArchived = false, this.isPublic = false, this.isSeen = false, this.isCloseFriend = false, this.storyType = StoryType.regular, this.viewCount = 0, this.replyCount = 0, this.reactionCount = 0, this.shareCount = 0, this.completionRate = 0.0, this.skipRate = 0.0, final  List<String> topViewers = const [], final  Map<String, int> reactionBreakdown = const {}, final  Map<String, dynamic>? metadata}): _viewers = viewers,_allowedTo = allowedTo,_hiddenFromUserIds = hiddenFromUserIds,_allowedGroupIds = allowedGroupIds,_textPosition = textPosition,_drawingPoints = drawingPoints,_textElements = textElements,_tags = tags,_reactions = reactions,_replies = replies,_hashtags = hashtags,_mentions = mentions,_music = music,_location = location,_topViewers = topViewers,_reactionBreakdown = reactionBreakdown,_metadata = metadata;
  factory _UnifiedStory.fromJson(Map<String, dynamic> json) => _$UnifiedStoryFromJson(json);

@override final  String id;
@override final  String userId;
@override final  String userName;
@override final  String userAvatarUrl;
@override final  String mediaUrl;
@override final  StoryMediaType mediaType;
@override final  DateTime createdAt;
@override final  DateTime expiresAt;
@override final  Duration duration;
@override final  DateTime timestamp;
// Privacy and visibility
@override@JsonKey() final  StoryPrivacy privacy;
@override@JsonKey() final  StoryVisibility visibility;
 final  List<String> _viewers;
@override@JsonKey() List<String> get viewers {
  if (_viewers is EqualUnmodifiableListView) return _viewers;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_viewers);
}

 final  List<String> _allowedTo;
@override@JsonKey() List<String> get allowedTo {
  if (_allowedTo is EqualUnmodifiableListView) return _allowedTo;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_allowedTo);
}

 final  List<String> _hiddenFromUserIds;
@override@JsonKey() List<String> get hiddenFromUserIds {
  if (_hiddenFromUserIds is EqualUnmodifiableListView) return _hiddenFromUserIds;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_hiddenFromUserIds);
}

 final  List<String> _allowedGroupIds;
@override@JsonKey() List<String> get allowedGroupIds {
  if (_allowedGroupIds is EqualUnmodifiableListView) return _allowedGroupIds;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_allowedGroupIds);
}

// Content metadata
@override final  String? caption;
@override final  String? textOverlay;
@override final  int? textColor;
@override final  double? textSize;
 final  Map<String, double>? _textPosition;
@override Map<String, double>? get textPosition {
  final value = _textPosition;
  if (value == null) return null;
  if (_textPosition is EqualUnmodifiableMapView) return _textPosition;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override final  int? backgroundColor;
@override final  String? filter;
// Drawing and text elements
 final  List<DrawingPoint> _drawingPoints;
// Drawing and text elements
@override@JsonKey() List<DrawingPoint> get drawingPoints {
  if (_drawingPoints is EqualUnmodifiableListView) return _drawingPoints;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_drawingPoints);
}

@override final  int? drawingColor;
@override final  double? drawingWidth;
 final  List<TextElement> _textElements;
@override@JsonKey() List<TextElement> get textElements {
  if (_textElements is EqualUnmodifiableListView) return _textElements;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_textElements);
}

// User interactions
 final  List<StoryTag> _tags;
// User interactions
@override@JsonKey() List<StoryTag> get tags {
  if (_tags is EqualUnmodifiableListView) return _tags;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_tags);
}

 final  List<StoryReaction> _reactions;
@override@JsonKey() List<StoryReaction> get reactions {
  if (_reactions is EqualUnmodifiableListView) return _reactions;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_reactions);
}

 final  List<StoryReply> _replies;
@override@JsonKey() List<StoryReply> get replies {
  if (_replies is EqualUnmodifiableListView) return _replies;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_replies);
}

 final  List<String> _hashtags;
@override@JsonKey() List<String> get hashtags {
  if (_hashtags is EqualUnmodifiableListView) return _hashtags;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_hashtags);
}

 final  List<String> _mentions;
@override@JsonKey() List<String> get mentions {
  if (_mentions is EqualUnmodifiableListView) return _mentions;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_mentions);
}

// Media and location
 final  Map<String, dynamic>? _music;
// Media and location
@override Map<String, dynamic>? get music {
  final value = _music;
  if (value == null) return null;
  if (_music is EqualUnmodifiableMapView) return _music;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override final  String? musicArtist;
 final  Map<String, dynamic>? _location;
@override Map<String, dynamic>? get location {
  final value = _location;
  if (value == null) return null;
  if (_location is EqualUnmodifiableMapView) return _location;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override final  String? locationName;
// Story features
@override@JsonKey() final  bool isHighlighted;
@override@JsonKey() final  bool isArchived;
@override@JsonKey() final  bool isPublic;
@override@JsonKey() final  bool isSeen;
@override@JsonKey() final  bool isCloseFriend;
@override@JsonKey() final  StoryType storyType;
// Analytics and tracking
@override@JsonKey() final  int viewCount;
@override@JsonKey() final  int replyCount;
@override@JsonKey() final  int reactionCount;
@override@JsonKey() final  int shareCount;
@override@JsonKey() final  double completionRate;
@override@JsonKey() final  double skipRate;
 final  List<String> _topViewers;
@override@JsonKey() List<String> get topViewers {
  if (_topViewers is EqualUnmodifiableListView) return _topViewers;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_topViewers);
}

 final  Map<String, int> _reactionBreakdown;
@override@JsonKey() Map<String, int> get reactionBreakdown {
  if (_reactionBreakdown is EqualUnmodifiableMapView) return _reactionBreakdown;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_reactionBreakdown);
}

// Additional metadata
 final  Map<String, dynamic>? _metadata;
// Additional metadata
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of UnifiedStory
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UnifiedStoryCopyWith<_UnifiedStory> get copyWith => __$UnifiedStoryCopyWithImpl<_UnifiedStory>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UnifiedStoryToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UnifiedStory&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.userName, userName) || other.userName == userName)&&(identical(other.userAvatarUrl, userAvatarUrl) || other.userAvatarUrl == userAvatarUrl)&&(identical(other.mediaUrl, mediaUrl) || other.mediaUrl == mediaUrl)&&(identical(other.mediaType, mediaType) || other.mediaType == mediaType)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.expiresAt, expiresAt) || other.expiresAt == expiresAt)&&(identical(other.duration, duration) || other.duration == duration)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.privacy, privacy) || other.privacy == privacy)&&(identical(other.visibility, visibility) || other.visibility == visibility)&&const DeepCollectionEquality().equals(other._viewers, _viewers)&&const DeepCollectionEquality().equals(other._allowedTo, _allowedTo)&&const DeepCollectionEquality().equals(other._hiddenFromUserIds, _hiddenFromUserIds)&&const DeepCollectionEquality().equals(other._allowedGroupIds, _allowedGroupIds)&&(identical(other.caption, caption) || other.caption == caption)&&(identical(other.textOverlay, textOverlay) || other.textOverlay == textOverlay)&&(identical(other.textColor, textColor) || other.textColor == textColor)&&(identical(other.textSize, textSize) || other.textSize == textSize)&&const DeepCollectionEquality().equals(other._textPosition, _textPosition)&&(identical(other.backgroundColor, backgroundColor) || other.backgroundColor == backgroundColor)&&(identical(other.filter, filter) || other.filter == filter)&&const DeepCollectionEquality().equals(other._drawingPoints, _drawingPoints)&&(identical(other.drawingColor, drawingColor) || other.drawingColor == drawingColor)&&(identical(other.drawingWidth, drawingWidth) || other.drawingWidth == drawingWidth)&&const DeepCollectionEquality().equals(other._textElements, _textElements)&&const DeepCollectionEquality().equals(other._tags, _tags)&&const DeepCollectionEquality().equals(other._reactions, _reactions)&&const DeepCollectionEquality().equals(other._replies, _replies)&&const DeepCollectionEquality().equals(other._hashtags, _hashtags)&&const DeepCollectionEquality().equals(other._mentions, _mentions)&&const DeepCollectionEquality().equals(other._music, _music)&&(identical(other.musicArtist, musicArtist) || other.musicArtist == musicArtist)&&const DeepCollectionEquality().equals(other._location, _location)&&(identical(other.locationName, locationName) || other.locationName == locationName)&&(identical(other.isHighlighted, isHighlighted) || other.isHighlighted == isHighlighted)&&(identical(other.isArchived, isArchived) || other.isArchived == isArchived)&&(identical(other.isPublic, isPublic) || other.isPublic == isPublic)&&(identical(other.isSeen, isSeen) || other.isSeen == isSeen)&&(identical(other.isCloseFriend, isCloseFriend) || other.isCloseFriend == isCloseFriend)&&(identical(other.storyType, storyType) || other.storyType == storyType)&&(identical(other.viewCount, viewCount) || other.viewCount == viewCount)&&(identical(other.replyCount, replyCount) || other.replyCount == replyCount)&&(identical(other.reactionCount, reactionCount) || other.reactionCount == reactionCount)&&(identical(other.shareCount, shareCount) || other.shareCount == shareCount)&&(identical(other.completionRate, completionRate) || other.completionRate == completionRate)&&(identical(other.skipRate, skipRate) || other.skipRate == skipRate)&&const DeepCollectionEquality().equals(other._topViewers, _topViewers)&&const DeepCollectionEquality().equals(other._reactionBreakdown, _reactionBreakdown)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,userId,userName,userAvatarUrl,mediaUrl,mediaType,createdAt,expiresAt,duration,timestamp,privacy,visibility,const DeepCollectionEquality().hash(_viewers),const DeepCollectionEquality().hash(_allowedTo),const DeepCollectionEquality().hash(_hiddenFromUserIds),const DeepCollectionEquality().hash(_allowedGroupIds),caption,textOverlay,textColor,textSize,const DeepCollectionEquality().hash(_textPosition),backgroundColor,filter,const DeepCollectionEquality().hash(_drawingPoints),drawingColor,drawingWidth,const DeepCollectionEquality().hash(_textElements),const DeepCollectionEquality().hash(_tags),const DeepCollectionEquality().hash(_reactions),const DeepCollectionEquality().hash(_replies),const DeepCollectionEquality().hash(_hashtags),const DeepCollectionEquality().hash(_mentions),const DeepCollectionEquality().hash(_music),musicArtist,const DeepCollectionEquality().hash(_location),locationName,isHighlighted,isArchived,isPublic,isSeen,isCloseFriend,storyType,viewCount,replyCount,reactionCount,shareCount,completionRate,skipRate,const DeepCollectionEquality().hash(_topViewers),const DeepCollectionEquality().hash(_reactionBreakdown),const DeepCollectionEquality().hash(_metadata)]);

@override
String toString() {
  return 'UnifiedStory(id: $id, userId: $userId, userName: $userName, userAvatarUrl: $userAvatarUrl, mediaUrl: $mediaUrl, mediaType: $mediaType, createdAt: $createdAt, expiresAt: $expiresAt, duration: $duration, timestamp: $timestamp, privacy: $privacy, visibility: $visibility, viewers: $viewers, allowedTo: $allowedTo, hiddenFromUserIds: $hiddenFromUserIds, allowedGroupIds: $allowedGroupIds, caption: $caption, textOverlay: $textOverlay, textColor: $textColor, textSize: $textSize, textPosition: $textPosition, backgroundColor: $backgroundColor, filter: $filter, drawingPoints: $drawingPoints, drawingColor: $drawingColor, drawingWidth: $drawingWidth, textElements: $textElements, tags: $tags, reactions: $reactions, replies: $replies, hashtags: $hashtags, mentions: $mentions, music: $music, musicArtist: $musicArtist, location: $location, locationName: $locationName, isHighlighted: $isHighlighted, isArchived: $isArchived, isPublic: $isPublic, isSeen: $isSeen, isCloseFriend: $isCloseFriend, storyType: $storyType, viewCount: $viewCount, replyCount: $replyCount, reactionCount: $reactionCount, shareCount: $shareCount, completionRate: $completionRate, skipRate: $skipRate, topViewers: $topViewers, reactionBreakdown: $reactionBreakdown, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$UnifiedStoryCopyWith<$Res> implements $UnifiedStoryCopyWith<$Res> {
  factory _$UnifiedStoryCopyWith(_UnifiedStory value, $Res Function(_UnifiedStory) _then) = __$UnifiedStoryCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, String userName, String userAvatarUrl, String mediaUrl, StoryMediaType mediaType, DateTime createdAt, DateTime expiresAt, Duration duration, DateTime timestamp, StoryPrivacy privacy, StoryVisibility visibility, List<String> viewers, List<String> allowedTo, List<String> hiddenFromUserIds, List<String> allowedGroupIds, String? caption, String? textOverlay, int? textColor, double? textSize, Map<String, double>? textPosition, int? backgroundColor, String? filter, List<DrawingPoint> drawingPoints, int? drawingColor, double? drawingWidth, List<TextElement> textElements, List<StoryTag> tags, List<StoryReaction> reactions, List<StoryReply> replies, List<String> hashtags, List<String> mentions, Map<String, dynamic>? music, String? musicArtist, Map<String, dynamic>? location, String? locationName, bool isHighlighted, bool isArchived, bool isPublic, bool isSeen, bool isCloseFriend, StoryType storyType, int viewCount, int replyCount, int reactionCount, int shareCount, double completionRate, double skipRate, List<String> topViewers, Map<String, int> reactionBreakdown, Map<String, dynamic>? metadata
});




}
/// @nodoc
class __$UnifiedStoryCopyWithImpl<$Res>
    implements _$UnifiedStoryCopyWith<$Res> {
  __$UnifiedStoryCopyWithImpl(this._self, this._then);

  final _UnifiedStory _self;
  final $Res Function(_UnifiedStory) _then;

/// Create a copy of UnifiedStory
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? userName = null,Object? userAvatarUrl = null,Object? mediaUrl = null,Object? mediaType = null,Object? createdAt = null,Object? expiresAt = null,Object? duration = null,Object? timestamp = null,Object? privacy = null,Object? visibility = null,Object? viewers = null,Object? allowedTo = null,Object? hiddenFromUserIds = null,Object? allowedGroupIds = null,Object? caption = freezed,Object? textOverlay = freezed,Object? textColor = freezed,Object? textSize = freezed,Object? textPosition = freezed,Object? backgroundColor = freezed,Object? filter = freezed,Object? drawingPoints = null,Object? drawingColor = freezed,Object? drawingWidth = freezed,Object? textElements = null,Object? tags = null,Object? reactions = null,Object? replies = null,Object? hashtags = null,Object? mentions = null,Object? music = freezed,Object? musicArtist = freezed,Object? location = freezed,Object? locationName = freezed,Object? isHighlighted = null,Object? isArchived = null,Object? isPublic = null,Object? isSeen = null,Object? isCloseFriend = null,Object? storyType = null,Object? viewCount = null,Object? replyCount = null,Object? reactionCount = null,Object? shareCount = null,Object? completionRate = null,Object? skipRate = null,Object? topViewers = null,Object? reactionBreakdown = null,Object? metadata = freezed,}) {
  return _then(_UnifiedStory(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,userName: null == userName ? _self.userName : userName // ignore: cast_nullable_to_non_nullable
as String,userAvatarUrl: null == userAvatarUrl ? _self.userAvatarUrl : userAvatarUrl // ignore: cast_nullable_to_non_nullable
as String,mediaUrl: null == mediaUrl ? _self.mediaUrl : mediaUrl // ignore: cast_nullable_to_non_nullable
as String,mediaType: null == mediaType ? _self.mediaType : mediaType // ignore: cast_nullable_to_non_nullable
as StoryMediaType,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,expiresAt: null == expiresAt ? _self.expiresAt : expiresAt // ignore: cast_nullable_to_non_nullable
as DateTime,duration: null == duration ? _self.duration : duration // ignore: cast_nullable_to_non_nullable
as Duration,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,privacy: null == privacy ? _self.privacy : privacy // ignore: cast_nullable_to_non_nullable
as StoryPrivacy,visibility: null == visibility ? _self.visibility : visibility // ignore: cast_nullable_to_non_nullable
as StoryVisibility,viewers: null == viewers ? _self._viewers : viewers // ignore: cast_nullable_to_non_nullable
as List<String>,allowedTo: null == allowedTo ? _self._allowedTo : allowedTo // ignore: cast_nullable_to_non_nullable
as List<String>,hiddenFromUserIds: null == hiddenFromUserIds ? _self._hiddenFromUserIds : hiddenFromUserIds // ignore: cast_nullable_to_non_nullable
as List<String>,allowedGroupIds: null == allowedGroupIds ? _self._allowedGroupIds : allowedGroupIds // ignore: cast_nullable_to_non_nullable
as List<String>,caption: freezed == caption ? _self.caption : caption // ignore: cast_nullable_to_non_nullable
as String?,textOverlay: freezed == textOverlay ? _self.textOverlay : textOverlay // ignore: cast_nullable_to_non_nullable
as String?,textColor: freezed == textColor ? _self.textColor : textColor // ignore: cast_nullable_to_non_nullable
as int?,textSize: freezed == textSize ? _self.textSize : textSize // ignore: cast_nullable_to_non_nullable
as double?,textPosition: freezed == textPosition ? _self._textPosition : textPosition // ignore: cast_nullable_to_non_nullable
as Map<String, double>?,backgroundColor: freezed == backgroundColor ? _self.backgroundColor : backgroundColor // ignore: cast_nullable_to_non_nullable
as int?,filter: freezed == filter ? _self.filter : filter // ignore: cast_nullable_to_non_nullable
as String?,drawingPoints: null == drawingPoints ? _self._drawingPoints : drawingPoints // ignore: cast_nullable_to_non_nullable
as List<DrawingPoint>,drawingColor: freezed == drawingColor ? _self.drawingColor : drawingColor // ignore: cast_nullable_to_non_nullable
as int?,drawingWidth: freezed == drawingWidth ? _self.drawingWidth : drawingWidth // ignore: cast_nullable_to_non_nullable
as double?,textElements: null == textElements ? _self._textElements : textElements // ignore: cast_nullable_to_non_nullable
as List<TextElement>,tags: null == tags ? _self._tags : tags // ignore: cast_nullable_to_non_nullable
as List<StoryTag>,reactions: null == reactions ? _self._reactions : reactions // ignore: cast_nullable_to_non_nullable
as List<StoryReaction>,replies: null == replies ? _self._replies : replies // ignore: cast_nullable_to_non_nullable
as List<StoryReply>,hashtags: null == hashtags ? _self._hashtags : hashtags // ignore: cast_nullable_to_non_nullable
as List<String>,mentions: null == mentions ? _self._mentions : mentions // ignore: cast_nullable_to_non_nullable
as List<String>,music: freezed == music ? _self._music : music // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,musicArtist: freezed == musicArtist ? _self.musicArtist : musicArtist // ignore: cast_nullable_to_non_nullable
as String?,location: freezed == location ? _self._location : location // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,locationName: freezed == locationName ? _self.locationName : locationName // ignore: cast_nullable_to_non_nullable
as String?,isHighlighted: null == isHighlighted ? _self.isHighlighted : isHighlighted // ignore: cast_nullable_to_non_nullable
as bool,isArchived: null == isArchived ? _self.isArchived : isArchived // ignore: cast_nullable_to_non_nullable
as bool,isPublic: null == isPublic ? _self.isPublic : isPublic // ignore: cast_nullable_to_non_nullable
as bool,isSeen: null == isSeen ? _self.isSeen : isSeen // ignore: cast_nullable_to_non_nullable
as bool,isCloseFriend: null == isCloseFriend ? _self.isCloseFriend : isCloseFriend // ignore: cast_nullable_to_non_nullable
as bool,storyType: null == storyType ? _self.storyType : storyType // ignore: cast_nullable_to_non_nullable
as StoryType,viewCount: null == viewCount ? _self.viewCount : viewCount // ignore: cast_nullable_to_non_nullable
as int,replyCount: null == replyCount ? _self.replyCount : replyCount // ignore: cast_nullable_to_non_nullable
as int,reactionCount: null == reactionCount ? _self.reactionCount : reactionCount // ignore: cast_nullable_to_non_nullable
as int,shareCount: null == shareCount ? _self.shareCount : shareCount // ignore: cast_nullable_to_non_nullable
as int,completionRate: null == completionRate ? _self.completionRate : completionRate // ignore: cast_nullable_to_non_nullable
as double,skipRate: null == skipRate ? _self.skipRate : skipRate // ignore: cast_nullable_to_non_nullable
as double,topViewers: null == topViewers ? _self._topViewers : topViewers // ignore: cast_nullable_to_non_nullable
as List<String>,reactionBreakdown: null == reactionBreakdown ? _self._reactionBreakdown : reactionBreakdown // ignore: cast_nullable_to_non_nullable
as Map<String, int>,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}


/// @nodoc
mixin _$StoryTag {

 String get userId; String get username; String? get name; double get x; double get y;
/// Create a copy of StoryTag
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StoryTagCopyWith<StoryTag> get copyWith => _$StoryTagCopyWithImpl<StoryTag>(this as StoryTag, _$identity);

  /// Serializes this StoryTag to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StoryTag&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.username, username) || other.username == username)&&(identical(other.name, name) || other.name == name)&&(identical(other.x, x) || other.x == x)&&(identical(other.y, y) || other.y == y));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,userId,username,name,x,y);

@override
String toString() {
  return 'StoryTag(userId: $userId, username: $username, name: $name, x: $x, y: $y)';
}


}

/// @nodoc
abstract mixin class $StoryTagCopyWith<$Res>  {
  factory $StoryTagCopyWith(StoryTag value, $Res Function(StoryTag) _then) = _$StoryTagCopyWithImpl;
@useResult
$Res call({
 String userId, String username, String? name, double x, double y
});




}
/// @nodoc
class _$StoryTagCopyWithImpl<$Res>
    implements $StoryTagCopyWith<$Res> {
  _$StoryTagCopyWithImpl(this._self, this._then);

  final StoryTag _self;
  final $Res Function(StoryTag) _then;

/// Create a copy of StoryTag
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? userId = null,Object? username = null,Object? name = freezed,Object? x = null,Object? y = null,}) {
  return _then(_self.copyWith(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,x: null == x ? _self.x : x // ignore: cast_nullable_to_non_nullable
as double,y: null == y ? _self.y : y // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [StoryTag].
extension StoryTagPatterns on StoryTag {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _StoryTag value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _StoryTag() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _StoryTag value)  $default,){
final _that = this;
switch (_that) {
case _StoryTag():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _StoryTag value)?  $default,){
final _that = this;
switch (_that) {
case _StoryTag() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String userId,  String username,  String? name,  double x,  double y)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _StoryTag() when $default != null:
return $default(_that.userId,_that.username,_that.name,_that.x,_that.y);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String userId,  String username,  String? name,  double x,  double y)  $default,) {final _that = this;
switch (_that) {
case _StoryTag():
return $default(_that.userId,_that.username,_that.name,_that.x,_that.y);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String userId,  String username,  String? name,  double x,  double y)?  $default,) {final _that = this;
switch (_that) {
case _StoryTag() when $default != null:
return $default(_that.userId,_that.username,_that.name,_that.x,_that.y);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _StoryTag implements StoryTag {
  const _StoryTag({required this.userId, required this.username, this.name, required this.x, required this.y});
  factory _StoryTag.fromJson(Map<String, dynamic> json) => _$StoryTagFromJson(json);

@override final  String userId;
@override final  String username;
@override final  String? name;
@override final  double x;
@override final  double y;

/// Create a copy of StoryTag
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StoryTagCopyWith<_StoryTag> get copyWith => __$StoryTagCopyWithImpl<_StoryTag>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$StoryTagToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StoryTag&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.username, username) || other.username == username)&&(identical(other.name, name) || other.name == name)&&(identical(other.x, x) || other.x == x)&&(identical(other.y, y) || other.y == y));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,userId,username,name,x,y);

@override
String toString() {
  return 'StoryTag(userId: $userId, username: $username, name: $name, x: $x, y: $y)';
}


}

/// @nodoc
abstract mixin class _$StoryTagCopyWith<$Res> implements $StoryTagCopyWith<$Res> {
  factory _$StoryTagCopyWith(_StoryTag value, $Res Function(_StoryTag) _then) = __$StoryTagCopyWithImpl;
@override @useResult
$Res call({
 String userId, String username, String? name, double x, double y
});




}
/// @nodoc
class __$StoryTagCopyWithImpl<$Res>
    implements _$StoryTagCopyWith<$Res> {
  __$StoryTagCopyWithImpl(this._self, this._then);

  final _StoryTag _self;
  final $Res Function(_StoryTag) _then;

/// Create a copy of StoryTag
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? userId = null,Object? username = null,Object? name = freezed,Object? x = null,Object? y = null,}) {
  return _then(_StoryTag(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,x: null == x ? _self.x : x // ignore: cast_nullable_to_non_nullable
as double,y: null == y ? _self.y : y // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}


/// @nodoc
mixin _$StoryReaction {

 String get userId; String get userName; String get userAvatarUrl; String get reactionType; DateTime get timestamp;
/// Create a copy of StoryReaction
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StoryReactionCopyWith<StoryReaction> get copyWith => _$StoryReactionCopyWithImpl<StoryReaction>(this as StoryReaction, _$identity);

  /// Serializes this StoryReaction to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StoryReaction&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.userName, userName) || other.userName == userName)&&(identical(other.userAvatarUrl, userAvatarUrl) || other.userAvatarUrl == userAvatarUrl)&&(identical(other.reactionType, reactionType) || other.reactionType == reactionType)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,userId,userName,userAvatarUrl,reactionType,timestamp);

@override
String toString() {
  return 'StoryReaction(userId: $userId, userName: $userName, userAvatarUrl: $userAvatarUrl, reactionType: $reactionType, timestamp: $timestamp)';
}


}

/// @nodoc
abstract mixin class $StoryReactionCopyWith<$Res>  {
  factory $StoryReactionCopyWith(StoryReaction value, $Res Function(StoryReaction) _then) = _$StoryReactionCopyWithImpl;
@useResult
$Res call({
 String userId, String userName, String userAvatarUrl, String reactionType, DateTime timestamp
});




}
/// @nodoc
class _$StoryReactionCopyWithImpl<$Res>
    implements $StoryReactionCopyWith<$Res> {
  _$StoryReactionCopyWithImpl(this._self, this._then);

  final StoryReaction _self;
  final $Res Function(StoryReaction) _then;

/// Create a copy of StoryReaction
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? userId = null,Object? userName = null,Object? userAvatarUrl = null,Object? reactionType = null,Object? timestamp = null,}) {
  return _then(_self.copyWith(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,userName: null == userName ? _self.userName : userName // ignore: cast_nullable_to_non_nullable
as String,userAvatarUrl: null == userAvatarUrl ? _self.userAvatarUrl : userAvatarUrl // ignore: cast_nullable_to_non_nullable
as String,reactionType: null == reactionType ? _self.reactionType : reactionType // ignore: cast_nullable_to_non_nullable
as String,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// Adds pattern-matching-related methods to [StoryReaction].
extension StoryReactionPatterns on StoryReaction {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _StoryReaction value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _StoryReaction() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _StoryReaction value)  $default,){
final _that = this;
switch (_that) {
case _StoryReaction():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _StoryReaction value)?  $default,){
final _that = this;
switch (_that) {
case _StoryReaction() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String userId,  String userName,  String userAvatarUrl,  String reactionType,  DateTime timestamp)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _StoryReaction() when $default != null:
return $default(_that.userId,_that.userName,_that.userAvatarUrl,_that.reactionType,_that.timestamp);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String userId,  String userName,  String userAvatarUrl,  String reactionType,  DateTime timestamp)  $default,) {final _that = this;
switch (_that) {
case _StoryReaction():
return $default(_that.userId,_that.userName,_that.userAvatarUrl,_that.reactionType,_that.timestamp);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String userId,  String userName,  String userAvatarUrl,  String reactionType,  DateTime timestamp)?  $default,) {final _that = this;
switch (_that) {
case _StoryReaction() when $default != null:
return $default(_that.userId,_that.userName,_that.userAvatarUrl,_that.reactionType,_that.timestamp);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _StoryReaction implements StoryReaction {
  const _StoryReaction({required this.userId, required this.userName, required this.userAvatarUrl, required this.reactionType, required this.timestamp});
  factory _StoryReaction.fromJson(Map<String, dynamic> json) => _$StoryReactionFromJson(json);

@override final  String userId;
@override final  String userName;
@override final  String userAvatarUrl;
@override final  String reactionType;
@override final  DateTime timestamp;

/// Create a copy of StoryReaction
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StoryReactionCopyWith<_StoryReaction> get copyWith => __$StoryReactionCopyWithImpl<_StoryReaction>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$StoryReactionToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StoryReaction&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.userName, userName) || other.userName == userName)&&(identical(other.userAvatarUrl, userAvatarUrl) || other.userAvatarUrl == userAvatarUrl)&&(identical(other.reactionType, reactionType) || other.reactionType == reactionType)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,userId,userName,userAvatarUrl,reactionType,timestamp);

@override
String toString() {
  return 'StoryReaction(userId: $userId, userName: $userName, userAvatarUrl: $userAvatarUrl, reactionType: $reactionType, timestamp: $timestamp)';
}


}

/// @nodoc
abstract mixin class _$StoryReactionCopyWith<$Res> implements $StoryReactionCopyWith<$Res> {
  factory _$StoryReactionCopyWith(_StoryReaction value, $Res Function(_StoryReaction) _then) = __$StoryReactionCopyWithImpl;
@override @useResult
$Res call({
 String userId, String userName, String userAvatarUrl, String reactionType, DateTime timestamp
});




}
/// @nodoc
class __$StoryReactionCopyWithImpl<$Res>
    implements _$StoryReactionCopyWith<$Res> {
  __$StoryReactionCopyWithImpl(this._self, this._then);

  final _StoryReaction _self;
  final $Res Function(_StoryReaction) _then;

/// Create a copy of StoryReaction
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? userId = null,Object? userName = null,Object? userAvatarUrl = null,Object? reactionType = null,Object? timestamp = null,}) {
  return _then(_StoryReaction(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,userName: null == userName ? _self.userName : userName // ignore: cast_nullable_to_non_nullable
as String,userAvatarUrl: null == userAvatarUrl ? _self.userAvatarUrl : userAvatarUrl // ignore: cast_nullable_to_non_nullable
as String,reactionType: null == reactionType ? _self.reactionType : reactionType // ignore: cast_nullable_to_non_nullable
as String,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}


/// @nodoc
mixin _$StoryReply {

 String get id; String get userId; String get userName; String get userAvatarUrl; String get message; DateTime get timestamp; bool get isRead;
/// Create a copy of StoryReply
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StoryReplyCopyWith<StoryReply> get copyWith => _$StoryReplyCopyWithImpl<StoryReply>(this as StoryReply, _$identity);

  /// Serializes this StoryReply to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StoryReply&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.userName, userName) || other.userName == userName)&&(identical(other.userAvatarUrl, userAvatarUrl) || other.userAvatarUrl == userAvatarUrl)&&(identical(other.message, message) || other.message == message)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.isRead, isRead) || other.isRead == isRead));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,userName,userAvatarUrl,message,timestamp,isRead);

@override
String toString() {
  return 'StoryReply(id: $id, userId: $userId, userName: $userName, userAvatarUrl: $userAvatarUrl, message: $message, timestamp: $timestamp, isRead: $isRead)';
}


}

/// @nodoc
abstract mixin class $StoryReplyCopyWith<$Res>  {
  factory $StoryReplyCopyWith(StoryReply value, $Res Function(StoryReply) _then) = _$StoryReplyCopyWithImpl;
@useResult
$Res call({
 String id, String userId, String userName, String userAvatarUrl, String message, DateTime timestamp, bool isRead
});




}
/// @nodoc
class _$StoryReplyCopyWithImpl<$Res>
    implements $StoryReplyCopyWith<$Res> {
  _$StoryReplyCopyWithImpl(this._self, this._then);

  final StoryReply _self;
  final $Res Function(StoryReply) _then;

/// Create a copy of StoryReply
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? userName = null,Object? userAvatarUrl = null,Object? message = null,Object? timestamp = null,Object? isRead = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,userName: null == userName ? _self.userName : userName // ignore: cast_nullable_to_non_nullable
as String,userAvatarUrl: null == userAvatarUrl ? _self.userAvatarUrl : userAvatarUrl // ignore: cast_nullable_to_non_nullable
as String,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,isRead: null == isRead ? _self.isRead : isRead // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [StoryReply].
extension StoryReplyPatterns on StoryReply {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _StoryReply value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _StoryReply() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _StoryReply value)  $default,){
final _that = this;
switch (_that) {
case _StoryReply():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _StoryReply value)?  $default,){
final _that = this;
switch (_that) {
case _StoryReply() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  String userName,  String userAvatarUrl,  String message,  DateTime timestamp,  bool isRead)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _StoryReply() when $default != null:
return $default(_that.id,_that.userId,_that.userName,_that.userAvatarUrl,_that.message,_that.timestamp,_that.isRead);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  String userName,  String userAvatarUrl,  String message,  DateTime timestamp,  bool isRead)  $default,) {final _that = this;
switch (_that) {
case _StoryReply():
return $default(_that.id,_that.userId,_that.userName,_that.userAvatarUrl,_that.message,_that.timestamp,_that.isRead);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  String userName,  String userAvatarUrl,  String message,  DateTime timestamp,  bool isRead)?  $default,) {final _that = this;
switch (_that) {
case _StoryReply() when $default != null:
return $default(_that.id,_that.userId,_that.userName,_that.userAvatarUrl,_that.message,_that.timestamp,_that.isRead);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _StoryReply implements StoryReply {
  const _StoryReply({required this.id, required this.userId, required this.userName, required this.userAvatarUrl, required this.message, required this.timestamp, this.isRead = false});
  factory _StoryReply.fromJson(Map<String, dynamic> json) => _$StoryReplyFromJson(json);

@override final  String id;
@override final  String userId;
@override final  String userName;
@override final  String userAvatarUrl;
@override final  String message;
@override final  DateTime timestamp;
@override@JsonKey() final  bool isRead;

/// Create a copy of StoryReply
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StoryReplyCopyWith<_StoryReply> get copyWith => __$StoryReplyCopyWithImpl<_StoryReply>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$StoryReplyToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StoryReply&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.userName, userName) || other.userName == userName)&&(identical(other.userAvatarUrl, userAvatarUrl) || other.userAvatarUrl == userAvatarUrl)&&(identical(other.message, message) || other.message == message)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.isRead, isRead) || other.isRead == isRead));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,userName,userAvatarUrl,message,timestamp,isRead);

@override
String toString() {
  return 'StoryReply(id: $id, userId: $userId, userName: $userName, userAvatarUrl: $userAvatarUrl, message: $message, timestamp: $timestamp, isRead: $isRead)';
}


}

/// @nodoc
abstract mixin class _$StoryReplyCopyWith<$Res> implements $StoryReplyCopyWith<$Res> {
  factory _$StoryReplyCopyWith(_StoryReply value, $Res Function(_StoryReply) _then) = __$StoryReplyCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, String userName, String userAvatarUrl, String message, DateTime timestamp, bool isRead
});




}
/// @nodoc
class __$StoryReplyCopyWithImpl<$Res>
    implements _$StoryReplyCopyWith<$Res> {
  __$StoryReplyCopyWithImpl(this._self, this._then);

  final _StoryReply _self;
  final $Res Function(_StoryReply) _then;

/// Create a copy of StoryReply
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? userName = null,Object? userAvatarUrl = null,Object? message = null,Object? timestamp = null,Object? isRead = null,}) {
  return _then(_StoryReply(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,userName: null == userName ? _self.userName : userName // ignore: cast_nullable_to_non_nullable
as String,userAvatarUrl: null == userAvatarUrl ? _self.userAvatarUrl : userAvatarUrl // ignore: cast_nullable_to_non_nullable
as String,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,isRead: null == isRead ? _self.isRead : isRead // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$StoryReel {

 String get id; String get userId; String get username; String get userAvatarUrl; List<UnifiedStory> get stories; bool get isAllViewed; bool get isCloseFriend;
/// Create a copy of StoryReel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StoryReelCopyWith<StoryReel> get copyWith => _$StoryReelCopyWithImpl<StoryReel>(this as StoryReel, _$identity);

  /// Serializes this StoryReel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StoryReel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.username, username) || other.username == username)&&(identical(other.userAvatarUrl, userAvatarUrl) || other.userAvatarUrl == userAvatarUrl)&&const DeepCollectionEquality().equals(other.stories, stories)&&(identical(other.isAllViewed, isAllViewed) || other.isAllViewed == isAllViewed)&&(identical(other.isCloseFriend, isCloseFriend) || other.isCloseFriend == isCloseFriend));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,username,userAvatarUrl,const DeepCollectionEquality().hash(stories),isAllViewed,isCloseFriend);

@override
String toString() {
  return 'StoryReel(id: $id, userId: $userId, username: $username, userAvatarUrl: $userAvatarUrl, stories: $stories, isAllViewed: $isAllViewed, isCloseFriend: $isCloseFriend)';
}


}

/// @nodoc
abstract mixin class $StoryReelCopyWith<$Res>  {
  factory $StoryReelCopyWith(StoryReel value, $Res Function(StoryReel) _then) = _$StoryReelCopyWithImpl;
@useResult
$Res call({
 String id, String userId, String username, String userAvatarUrl, List<UnifiedStory> stories, bool isAllViewed, bool isCloseFriend
});




}
/// @nodoc
class _$StoryReelCopyWithImpl<$Res>
    implements $StoryReelCopyWith<$Res> {
  _$StoryReelCopyWithImpl(this._self, this._then);

  final StoryReel _self;
  final $Res Function(StoryReel) _then;

/// Create a copy of StoryReel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? username = null,Object? userAvatarUrl = null,Object? stories = null,Object? isAllViewed = null,Object? isCloseFriend = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,userAvatarUrl: null == userAvatarUrl ? _self.userAvatarUrl : userAvatarUrl // ignore: cast_nullable_to_non_nullable
as String,stories: null == stories ? _self.stories : stories // ignore: cast_nullable_to_non_nullable
as List<UnifiedStory>,isAllViewed: null == isAllViewed ? _self.isAllViewed : isAllViewed // ignore: cast_nullable_to_non_nullable
as bool,isCloseFriend: null == isCloseFriend ? _self.isCloseFriend : isCloseFriend // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [StoryReel].
extension StoryReelPatterns on StoryReel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _StoryReel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _StoryReel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _StoryReel value)  $default,){
final _that = this;
switch (_that) {
case _StoryReel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _StoryReel value)?  $default,){
final _that = this;
switch (_that) {
case _StoryReel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  String username,  String userAvatarUrl,  List<UnifiedStory> stories,  bool isAllViewed,  bool isCloseFriend)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _StoryReel() when $default != null:
return $default(_that.id,_that.userId,_that.username,_that.userAvatarUrl,_that.stories,_that.isAllViewed,_that.isCloseFriend);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  String username,  String userAvatarUrl,  List<UnifiedStory> stories,  bool isAllViewed,  bool isCloseFriend)  $default,) {final _that = this;
switch (_that) {
case _StoryReel():
return $default(_that.id,_that.userId,_that.username,_that.userAvatarUrl,_that.stories,_that.isAllViewed,_that.isCloseFriend);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  String username,  String userAvatarUrl,  List<UnifiedStory> stories,  bool isAllViewed,  bool isCloseFriend)?  $default,) {final _that = this;
switch (_that) {
case _StoryReel() when $default != null:
return $default(_that.id,_that.userId,_that.username,_that.userAvatarUrl,_that.stories,_that.isAllViewed,_that.isCloseFriend);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _StoryReel implements StoryReel {
  const _StoryReel({required this.id, required this.userId, required this.username, required this.userAvatarUrl, required final  List<UnifiedStory> stories, this.isAllViewed = false, this.isCloseFriend = false}): _stories = stories;
  factory _StoryReel.fromJson(Map<String, dynamic> json) => _$StoryReelFromJson(json);

@override final  String id;
@override final  String userId;
@override final  String username;
@override final  String userAvatarUrl;
 final  List<UnifiedStory> _stories;
@override List<UnifiedStory> get stories {
  if (_stories is EqualUnmodifiableListView) return _stories;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_stories);
}

@override@JsonKey() final  bool isAllViewed;
@override@JsonKey() final  bool isCloseFriend;

/// Create a copy of StoryReel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StoryReelCopyWith<_StoryReel> get copyWith => __$StoryReelCopyWithImpl<_StoryReel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$StoryReelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StoryReel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.username, username) || other.username == username)&&(identical(other.userAvatarUrl, userAvatarUrl) || other.userAvatarUrl == userAvatarUrl)&&const DeepCollectionEquality().equals(other._stories, _stories)&&(identical(other.isAllViewed, isAllViewed) || other.isAllViewed == isAllViewed)&&(identical(other.isCloseFriend, isCloseFriend) || other.isCloseFriend == isCloseFriend));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,username,userAvatarUrl,const DeepCollectionEquality().hash(_stories),isAllViewed,isCloseFriend);

@override
String toString() {
  return 'StoryReel(id: $id, userId: $userId, username: $username, userAvatarUrl: $userAvatarUrl, stories: $stories, isAllViewed: $isAllViewed, isCloseFriend: $isCloseFriend)';
}


}

/// @nodoc
abstract mixin class _$StoryReelCopyWith<$Res> implements $StoryReelCopyWith<$Res> {
  factory _$StoryReelCopyWith(_StoryReel value, $Res Function(_StoryReel) _then) = __$StoryReelCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, String username, String userAvatarUrl, List<UnifiedStory> stories, bool isAllViewed, bool isCloseFriend
});




}
/// @nodoc
class __$StoryReelCopyWithImpl<$Res>
    implements _$StoryReelCopyWith<$Res> {
  __$StoryReelCopyWithImpl(this._self, this._then);

  final _StoryReel _self;
  final $Res Function(_StoryReel) _then;

/// Create a copy of StoryReel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? username = null,Object? userAvatarUrl = null,Object? stories = null,Object? isAllViewed = null,Object? isCloseFriend = null,}) {
  return _then(_StoryReel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,userAvatarUrl: null == userAvatarUrl ? _self.userAvatarUrl : userAvatarUrl // ignore: cast_nullable_to_non_nullable
as String,stories: null == stories ? _self._stories : stories // ignore: cast_nullable_to_non_nullable
as List<UnifiedStory>,isAllViewed: null == isAllViewed ? _self.isAllViewed : isAllViewed // ignore: cast_nullable_to_non_nullable
as bool,isCloseFriend: null == isCloseFriend ? _self.isCloseFriend : isCloseFriend // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$StoryStats {

 int get viewCount; int get replyCount; int get reactionCount; int get shareCount; double get completionRate; double get skipRate; List<String> get topViewers; Map<String, int> get reactionBreakdown;
/// Create a copy of StoryStats
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StoryStatsCopyWith<StoryStats> get copyWith => _$StoryStatsCopyWithImpl<StoryStats>(this as StoryStats, _$identity);

  /// Serializes this StoryStats to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StoryStats&&(identical(other.viewCount, viewCount) || other.viewCount == viewCount)&&(identical(other.replyCount, replyCount) || other.replyCount == replyCount)&&(identical(other.reactionCount, reactionCount) || other.reactionCount == reactionCount)&&(identical(other.shareCount, shareCount) || other.shareCount == shareCount)&&(identical(other.completionRate, completionRate) || other.completionRate == completionRate)&&(identical(other.skipRate, skipRate) || other.skipRate == skipRate)&&const DeepCollectionEquality().equals(other.topViewers, topViewers)&&const DeepCollectionEquality().equals(other.reactionBreakdown, reactionBreakdown));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,viewCount,replyCount,reactionCount,shareCount,completionRate,skipRate,const DeepCollectionEquality().hash(topViewers),const DeepCollectionEquality().hash(reactionBreakdown));

@override
String toString() {
  return 'StoryStats(viewCount: $viewCount, replyCount: $replyCount, reactionCount: $reactionCount, shareCount: $shareCount, completionRate: $completionRate, skipRate: $skipRate, topViewers: $topViewers, reactionBreakdown: $reactionBreakdown)';
}


}

/// @nodoc
abstract mixin class $StoryStatsCopyWith<$Res>  {
  factory $StoryStatsCopyWith(StoryStats value, $Res Function(StoryStats) _then) = _$StoryStatsCopyWithImpl;
@useResult
$Res call({
 int viewCount, int replyCount, int reactionCount, int shareCount, double completionRate, double skipRate, List<String> topViewers, Map<String, int> reactionBreakdown
});




}
/// @nodoc
class _$StoryStatsCopyWithImpl<$Res>
    implements $StoryStatsCopyWith<$Res> {
  _$StoryStatsCopyWithImpl(this._self, this._then);

  final StoryStats _self;
  final $Res Function(StoryStats) _then;

/// Create a copy of StoryStats
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? viewCount = null,Object? replyCount = null,Object? reactionCount = null,Object? shareCount = null,Object? completionRate = null,Object? skipRate = null,Object? topViewers = null,Object? reactionBreakdown = null,}) {
  return _then(_self.copyWith(
viewCount: null == viewCount ? _self.viewCount : viewCount // ignore: cast_nullable_to_non_nullable
as int,replyCount: null == replyCount ? _self.replyCount : replyCount // ignore: cast_nullable_to_non_nullable
as int,reactionCount: null == reactionCount ? _self.reactionCount : reactionCount // ignore: cast_nullable_to_non_nullable
as int,shareCount: null == shareCount ? _self.shareCount : shareCount // ignore: cast_nullable_to_non_nullable
as int,completionRate: null == completionRate ? _self.completionRate : completionRate // ignore: cast_nullable_to_non_nullable
as double,skipRate: null == skipRate ? _self.skipRate : skipRate // ignore: cast_nullable_to_non_nullable
as double,topViewers: null == topViewers ? _self.topViewers : topViewers // ignore: cast_nullable_to_non_nullable
as List<String>,reactionBreakdown: null == reactionBreakdown ? _self.reactionBreakdown : reactionBreakdown // ignore: cast_nullable_to_non_nullable
as Map<String, int>,
  ));
}

}


/// Adds pattern-matching-related methods to [StoryStats].
extension StoryStatsPatterns on StoryStats {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _StoryStats value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _StoryStats() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _StoryStats value)  $default,){
final _that = this;
switch (_that) {
case _StoryStats():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _StoryStats value)?  $default,){
final _that = this;
switch (_that) {
case _StoryStats() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int viewCount,  int replyCount,  int reactionCount,  int shareCount,  double completionRate,  double skipRate,  List<String> topViewers,  Map<String, int> reactionBreakdown)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _StoryStats() when $default != null:
return $default(_that.viewCount,_that.replyCount,_that.reactionCount,_that.shareCount,_that.completionRate,_that.skipRate,_that.topViewers,_that.reactionBreakdown);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int viewCount,  int replyCount,  int reactionCount,  int shareCount,  double completionRate,  double skipRate,  List<String> topViewers,  Map<String, int> reactionBreakdown)  $default,) {final _that = this;
switch (_that) {
case _StoryStats():
return $default(_that.viewCount,_that.replyCount,_that.reactionCount,_that.shareCount,_that.completionRate,_that.skipRate,_that.topViewers,_that.reactionBreakdown);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int viewCount,  int replyCount,  int reactionCount,  int shareCount,  double completionRate,  double skipRate,  List<String> topViewers,  Map<String, int> reactionBreakdown)?  $default,) {final _that = this;
switch (_that) {
case _StoryStats() when $default != null:
return $default(_that.viewCount,_that.replyCount,_that.reactionCount,_that.shareCount,_that.completionRate,_that.skipRate,_that.topViewers,_that.reactionBreakdown);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _StoryStats implements StoryStats {
  const _StoryStats({required this.viewCount, required this.replyCount, required this.reactionCount, required this.shareCount, required this.completionRate, required this.skipRate, required final  List<String> topViewers, required final  Map<String, int> reactionBreakdown}): _topViewers = topViewers,_reactionBreakdown = reactionBreakdown;
  factory _StoryStats.fromJson(Map<String, dynamic> json) => _$StoryStatsFromJson(json);

@override final  int viewCount;
@override final  int replyCount;
@override final  int reactionCount;
@override final  int shareCount;
@override final  double completionRate;
@override final  double skipRate;
 final  List<String> _topViewers;
@override List<String> get topViewers {
  if (_topViewers is EqualUnmodifiableListView) return _topViewers;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_topViewers);
}

 final  Map<String, int> _reactionBreakdown;
@override Map<String, int> get reactionBreakdown {
  if (_reactionBreakdown is EqualUnmodifiableMapView) return _reactionBreakdown;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_reactionBreakdown);
}


/// Create a copy of StoryStats
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StoryStatsCopyWith<_StoryStats> get copyWith => __$StoryStatsCopyWithImpl<_StoryStats>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$StoryStatsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StoryStats&&(identical(other.viewCount, viewCount) || other.viewCount == viewCount)&&(identical(other.replyCount, replyCount) || other.replyCount == replyCount)&&(identical(other.reactionCount, reactionCount) || other.reactionCount == reactionCount)&&(identical(other.shareCount, shareCount) || other.shareCount == shareCount)&&(identical(other.completionRate, completionRate) || other.completionRate == completionRate)&&(identical(other.skipRate, skipRate) || other.skipRate == skipRate)&&const DeepCollectionEquality().equals(other._topViewers, _topViewers)&&const DeepCollectionEquality().equals(other._reactionBreakdown, _reactionBreakdown));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,viewCount,replyCount,reactionCount,shareCount,completionRate,skipRate,const DeepCollectionEquality().hash(_topViewers),const DeepCollectionEquality().hash(_reactionBreakdown));

@override
String toString() {
  return 'StoryStats(viewCount: $viewCount, replyCount: $replyCount, reactionCount: $reactionCount, shareCount: $shareCount, completionRate: $completionRate, skipRate: $skipRate, topViewers: $topViewers, reactionBreakdown: $reactionBreakdown)';
}


}

/// @nodoc
abstract mixin class _$StoryStatsCopyWith<$Res> implements $StoryStatsCopyWith<$Res> {
  factory _$StoryStatsCopyWith(_StoryStats value, $Res Function(_StoryStats) _then) = __$StoryStatsCopyWithImpl;
@override @useResult
$Res call({
 int viewCount, int replyCount, int reactionCount, int shareCount, double completionRate, double skipRate, List<String> topViewers, Map<String, int> reactionBreakdown
});




}
/// @nodoc
class __$StoryStatsCopyWithImpl<$Res>
    implements _$StoryStatsCopyWith<$Res> {
  __$StoryStatsCopyWithImpl(this._self, this._then);

  final _StoryStats _self;
  final $Res Function(_StoryStats) _then;

/// Create a copy of StoryStats
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? viewCount = null,Object? replyCount = null,Object? reactionCount = null,Object? shareCount = null,Object? completionRate = null,Object? skipRate = null,Object? topViewers = null,Object? reactionBreakdown = null,}) {
  return _then(_StoryStats(
viewCount: null == viewCount ? _self.viewCount : viewCount // ignore: cast_nullable_to_non_nullable
as int,replyCount: null == replyCount ? _self.replyCount : replyCount // ignore: cast_nullable_to_non_nullable
as int,reactionCount: null == reactionCount ? _self.reactionCount : reactionCount // ignore: cast_nullable_to_non_nullable
as int,shareCount: null == shareCount ? _self.shareCount : shareCount // ignore: cast_nullable_to_non_nullable
as int,completionRate: null == completionRate ? _self.completionRate : completionRate // ignore: cast_nullable_to_non_nullable
as double,skipRate: null == skipRate ? _self.skipRate : skipRate // ignore: cast_nullable_to_non_nullable
as double,topViewers: null == topViewers ? _self._topViewers : topViewers // ignore: cast_nullable_to_non_nullable
as List<String>,reactionBreakdown: null == reactionBreakdown ? _self._reactionBreakdown : reactionBreakdown // ignore: cast_nullable_to_non_nullable
as Map<String, int>,
  ));
}


}

// dart format on
