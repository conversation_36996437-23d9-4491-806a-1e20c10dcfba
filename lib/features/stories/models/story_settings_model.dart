import 'package:freezed_annotation/freezed_annotation.dart';

part 'story_settings_model.freezed.dart';
part 'story_settings_model.g.dart';

enum StoryDuration { sixHours, twelveHours, twentyFourHours }

enum StoryVisibility { public, followers, specificGroups, closeFriends }

@freezed
abstract class StorySettings with _$StorySettings {
  const factory StorySettings({
    required String id,
    required StoryDuration duration,
    required StoryVisibility visibility,
    required List<String> hiddenFromUserIds,
    required bool allowEditing,
    required bool allowComments,
    required bool allowReactions,
    required bool allowMentions,
    required bool allowMusic,
    required bool allowFilters,
    required bool allowTextOverlays,
    required bool allowVideoUpload,
    required List<String> allowedGroupIds,
  }) = _StorySettings;

  factory StorySettings.fromJson(Map<String, dynamic> json) =>
      _$StorySettingsFromJson(json);
}
