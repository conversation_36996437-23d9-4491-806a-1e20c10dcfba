import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'shared/story_shared_models.dart';

part 'unified_story_model.freezed.dart';
part 'unified_story_model.g.dart';

/// Unified Story model that combines all story features
@freezed
abstract class UnifiedStory with _$UnifiedStory {
  const factory UnifiedStory({
    required String id,
    required String userId,
    required String userName,
    required String userAvatarUrl,
    required String mediaUrl,
    required StoryMediaType mediaType,
    required DateTime createdAt,
    required DateTime expiresAt,
    required Duration duration,
    required DateTime timestamp,

    // Privacy and visibility
    @Default(StoryPrivacy.public) StoryPrivacy privacy,
    @Default(StoryVisibility.public) StoryVisibility visibility,
    @Default([]) List<String> viewers,
    @Default([]) List<String> allowedTo,
    @Default([]) List<String> hiddenFromUserIds,
    @Default([]) List<String> allowedGroupIds,

    // Content metadata
    String? caption,
    String? textOverlay,
    int? textColor,
    double? textSize,
    Map<String, double>? textPosition,
    int? backgroundColor,
    String? filter,

    // Drawing and text elements
    @Default([]) List<DrawingPoint> drawingPoints,
    int? drawingColor,
    double? drawingWidth,
    @Default([]) List<TextElement> textElements,

    // User interactions
    @Default([]) List<StoryTag> tags,
    @Default([]) List<StoryReaction> reactions,
    @Default([]) List<StoryReply> replies,
    @Default([]) List<String> hashtags,
    @Default([]) List<String> mentions,

    // Media and location
    Map<String, dynamic>? music,
    String? musicArtist,
    Map<String, dynamic>? location,
    String? locationName,

    // Story features
    @Default(false) bool isHighlighted,
    @Default(false) bool isArchived,
    @Default(false) bool isPublic,
    @Default(false) bool isSeen,
    @Default(false) bool isCloseFriend,
    @Default(StoryType.regular) StoryType storyType,

    // Analytics and tracking
    @Default(0) int viewCount,
    @Default(0) int replyCount,
    @Default(0) int reactionCount,
    @Default(0) int shareCount,
    @Default(0.0) double completionRate,
    @Default(0.0) double skipRate,
    @Default([]) List<String> topViewers,
    @Default({}) Map<String, int> reactionBreakdown,

    // Additional metadata
    Map<String, dynamic>? metadata,
  }) = _UnifiedStory;

  factory UnifiedStory.fromJson(Map<String, dynamic> json) =>
      _$UnifiedStoryFromJson(json);
}

/// Story tag model for user tagging
@freezed
abstract class StoryTag with _$StoryTag {
  const factory StoryTag({
    required String userId,
    required String username,
    String? name,
    required double x,
    required double y,
  }) = _StoryTag;

  factory StoryTag.fromJson(Map<String, dynamic> json) =>
      _$StoryTagFromJson(json);
}

/// Story reaction model
@freezed
abstract class StoryReaction with _$StoryReaction {
  const factory StoryReaction({
    required String userId,
    required String userName,
    required String userAvatarUrl,
    required String reactionType,
    required DateTime timestamp,
  }) = _StoryReaction;

  factory StoryReaction.fromJson(Map<String, dynamic> json) =>
      _$StoryReactionFromJson(json);
}

/// Story reply model
@freezed
abstract class StoryReply with _$StoryReply {
  const factory StoryReply({
    required String id,
    required String userId,
    required String userName,
    required String userAvatarUrl,
    required String message,
    required DateTime timestamp,
    @Default(false) bool isRead,
  }) = _StoryReply;

  factory StoryReply.fromJson(Map<String, dynamic> json) =>
      _$StoryReplyFromJson(json);
}

/// Story reel model for grouping stories by user
@freezed
abstract class StoryReel with _$StoryReel {
  const factory StoryReel({
    required String id,
    required String userId,
    required String username,
    required String userAvatarUrl,
    required List<UnifiedStory> stories,
    @Default(false) bool isAllViewed,
    @Default(false) bool isCloseFriend,
  }) = _StoryReel;

  factory StoryReel.fromJson(Map<String, dynamic> json) =>
      _$StoryReelFromJson(json);
}

/// Story statistics model
@freezed
abstract class StoryStats with _$StoryStats {
  const factory StoryStats({
    required int viewCount,
    required int replyCount,
    required int reactionCount,
    required int shareCount,
    required double completionRate,
    required double skipRate,
    required List<String> topViewers,
    required Map<String, int> reactionBreakdown,
  }) = _StoryStats;

  factory StoryStats.fromJson(Map<String, dynamic> json) =>
      _$StoryStatsFromJson(json);
}

/// Location result for story location search
class LocationResult {
  final String name;
  final String address;
  final double latitude;
  final double longitude;

  LocationResult({
    required this.name,
    required this.address,
    required this.latitude,
    required this.longitude,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  factory LocationResult.fromJson(Map<String, dynamic> json) {
    return LocationResult(
      name: json['name'] ?? '',
      address: json['address'] ?? '',
      latitude: (json['latitude'] as num?)?.toDouble() ?? 0.0,
      longitude: (json['longitude'] as num?)?.toDouble() ?? 0.0,
    );
  }
}

/// Extension methods for UnifiedStory model
extension UnifiedStoryExtensions on UnifiedStory {
  /// Create UnifiedStory from Firestore document
  static UnifiedStory fromFirestore(String id, Map<String, dynamic> data) {
    return UnifiedStory(
      id: id,
      userId: data['userId'] ?? '',
      userName: data['userName'] ?? '',
      userAvatarUrl: data['userAvatarUrl'] ?? '',
      mediaUrl: data['mediaUrl'] ?? '',
      mediaType: StoryMediaType.values.firstWhere(
        (e) => e.toString().split('.').last == (data['mediaType'] ?? 'image'),
        orElse: () => StoryMediaType.image,
      ),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      expiresAt: (data['expiresAt'] as Timestamp).toDate(),
      duration: Duration(seconds: data['duration'] ?? 5),
      timestamp: (data['timestamp'] as Timestamp?)?.toDate() ?? DateTime.now(),

      // Privacy and visibility
      privacy: StoryPrivacy.values.firstWhere(
        (e) => e.toString().split('.').last == (data['privacy'] ?? 'public'),
        orElse: () => StoryPrivacy.public,
      ),
      visibility: StoryVisibility.values.firstWhere(
        (e) => e.toString().split('.').last == (data['visibility'] ?? 'public'),
        orElse: () => StoryVisibility.public,
      ),
      viewers: List<String>.from(data['viewers'] ?? []),
      allowedTo: List<String>.from(data['allowedTo'] ?? []),
      hiddenFromUserIds: List<String>.from(data['hiddenFromUserIds'] ?? []),
      allowedGroupIds: List<String>.from(data['allowedGroupIds'] ?? []),

      // Content metadata
      caption: data['caption'],
      textOverlay: data['textOverlay'],
      textColor: data['textColor'] as int?,
      textSize: (data['textSize'] as num?)?.toDouble(),
      textPosition: _parseTextPosition(data['textPosition']),
      backgroundColor: data['backgroundColor'] as int?,
      filter: data['filter'],

      // Drawing and text elements
      drawingPoints: _parseDrawingPoints(data['drawingPoints']),
      drawingColor: data['drawingColor'] as int?,
      drawingWidth: (data['drawingWidth'] as num?)?.toDouble(),
      textElements: _parseTextElements(data['textElements']),

      // User interactions
      tags: _parseStoryTags(data['tags']),
      reactions: _parseStoryReactions(data['reactions']),
      replies: _parseStoryReplies(data['replies']),
      hashtags: List<String>.from(data['hashtags'] ?? []),
      mentions: List<String>.from(data['mentions'] ?? []),

      // Media and location
      music: data['music'] as Map<String, dynamic>?,
      musicArtist: data['musicArtist'],
      location: data['location'] as Map<String, dynamic>?,
      locationName: data['locationName'],

      // Story features
      isHighlighted: data['isHighlighted'] ?? false,
      isArchived: data['isArchived'] ?? false,
      isPublic: data['isPublic'] ?? true,
      isSeen: data['isSeen'] ?? false,
      isCloseFriend: data['isCloseFriend'] ?? false,
      storyType: StoryType.values.firstWhere(
        (e) => e.toString().split('.').last == (data['storyType'] ?? 'regular'),
        orElse: () => StoryType.regular,
      ),

      // Analytics and tracking
      viewCount: data['viewCount'] ?? 0,
      replyCount: data['replyCount'] ?? 0,
      reactionCount: data['reactionCount'] ?? 0,
      shareCount: data['shareCount'] ?? 0,
      completionRate: (data['completionRate'] as num?)?.toDouble() ?? 0.0,
      skipRate: (data['skipRate'] as num?)?.toDouble() ?? 0.0,
      topViewers: List<String>.from(data['topViewers'] ?? []),
      reactionBreakdown: Map<String, int>.from(data['reactionBreakdown'] ?? {}),

      // Additional metadata
      metadata: data['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'userName': userName,
      'userAvatarUrl': userAvatarUrl,
      'mediaUrl': mediaUrl,
      'mediaType': mediaType.toString().split('.').last,
      'createdAt': Timestamp.fromDate(createdAt),
      'expiresAt': Timestamp.fromDate(expiresAt),
      'duration': duration.inSeconds,
      'timestamp': Timestamp.fromDate(timestamp),

      // Privacy and visibility
      'privacy': privacy.toString().split('.').last,
      'visibility': visibility.toString().split('.').last,
      'viewers': viewers,
      'allowedTo': allowedTo,
      'hiddenFromUserIds': hiddenFromUserIds,
      'allowedGroupIds': allowedGroupIds,

      // Content metadata
      'caption': caption,
      'textOverlay': textOverlay,
      'textColor': textColor,
      'textSize': textSize,
      'textPosition': textPosition,
      'backgroundColor': backgroundColor,
      'filter': filter,

      // Drawing and text elements
      'drawingPoints': drawingPoints.map((e) => e.toJson()).toList(),
      'drawingColor': drawingColor,
      'drawingWidth': drawingWidth,
      'textElements': textElements.map((e) => e.toJson()).toList(),

      // User interactions
      'tags': tags.map((e) => e.toJson()).toList(),
      'reactions': reactions.map((e) => e.toJson()).toList(),
      'replies': replies.map((e) => e.toJson()).toList(),
      'hashtags': hashtags,
      'mentions': mentions,

      // Media and location
      'music': music,
      'musicArtist': musicArtist,
      'location': location,
      'locationName': locationName,

      // Story features
      'isHighlighted': isHighlighted,
      'isArchived': isArchived,
      'isPublic': isPublic,
      'isSeen': isSeen,
      'isCloseFriend': isCloseFriend,
      'storyType': storyType.toString().split('.').last,

      // Analytics and tracking
      'viewCount': viewCount,
      'replyCount': replyCount,
      'reactionCount': reactionCount,
      'shareCount': shareCount,
      'completionRate': completionRate,
      'skipRate': skipRate,
      'topViewers': topViewers,
      'reactionBreakdown': reactionBreakdown,

      // Additional metadata
      'metadata': metadata,
    };
  }

  /// Check if story is expired
  bool get isExpired => DateTime.now().isAfter(expiresAt);

  /// Get time remaining until expiration
  Duration get timeRemaining {
    final remaining = expiresAt.difference(DateTime.now());
    return remaining.isNegative ? Duration.zero : remaining;
  }

  /// Check if user can view this story
  bool canBeViewedBy(String viewerId, bool isFollowing, bool isCloseFriend) {
    if (hiddenFromUserIds.contains(viewerId)) return false;

    switch (privacy) {
      case StoryPrivacy.public:
        return true;
      case StoryPrivacy.followers:
        return isFollowing;
      case StoryPrivacy.closeFriends:
        return isCloseFriend;
      case StoryPrivacy.custom:
        return allowedTo.contains(viewerId);
      case StoryPrivacy.private:
        return userId == viewerId;
    }
  }

  // Helper methods for parsing
  static Map<String, double>? _parseTextPosition(dynamic data) {
    if (data is Map<String, dynamic>) {
      return data.map((key, value) => MapEntry(key, (value as num).toDouble()));
    }
    return null;
  }

  static List<DrawingPoint> _parseDrawingPoints(dynamic data) {
    if (data is List) {
      return data
          .whereType<Map<String, dynamic>>()
          .map((e) => DrawingPoint.fromJson(e))
          .toList();
    }
    return [];
  }

  static List<TextElement> _parseTextElements(dynamic data) {
    if (data is List) {
      return data
          .whereType<Map<String, dynamic>>()
          .map((e) => TextElement.fromJson(e))
          .toList();
    }
    return [];
  }

  static List<StoryTag> _parseStoryTags(dynamic data) {
    if (data is List) {
      return data
          .whereType<Map<String, dynamic>>()
          .map((e) => StoryTag.fromJson(e))
          .toList();
    }
    return [];
  }

  static List<StoryReaction> _parseStoryReactions(dynamic data) {
    if (data is List) {
      return data
          .whereType<Map<String, dynamic>>()
          .map((e) => StoryReaction.fromJson(e))
          .toList();
    }
    return [];
  }

  static List<StoryReply> _parseStoryReplies(dynamic data) {
    if (data is List) {
      return data
          .whereType<Map<String, dynamic>>()
          .map((e) => StoryReply.fromJson(e))
          .toList();
    }
    return [];
  }
}
