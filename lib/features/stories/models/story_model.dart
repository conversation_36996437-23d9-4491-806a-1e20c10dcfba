import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'story_model.freezed.dart';
part 'story_model.g.dart';

/// Enhanced Story model with additional features for the billionaires social app
@freezed
abstract class Story with _$Story {
  const factory Story({
    required String id,
    required String userId,
    required String userName,
    required String userAvatarUrl,
    required String mediaUrl,
    required StoryMediaType mediaType,
    required DateTime createdAt,
    required DateTime expiresAt,
    @Default([]) List<String> viewers,
    @Default([]) List<String> allowedTo,
    @Default([]) List<StoryReaction> reactions,
    @Default([]) List<StoryReply> replies,
    @Default(StoryPrivacy.public) StoryPrivacy privacy,
    String? caption,
    String? location,
    @Default([]) List<String> hashtags,
    @Default([]) List<String> mentions,
    @Default(false) bool isHighlighted,
    @Default(false) bool isArchived,
    String? musicUrl,
    String? musicTitle,
    @Default(StoryType.regular) StoryType storyType,
    Map<String, dynamic>? metadata,
  }) = _Story;

  factory Story.fromJson(Map<String, dynamic> json) => _$StoryFromJson(json);
}

/// Story media types
enum StoryMediaType {
  image,
  video,
  text,
  music,
  poll,
  question,
  countdown,
  quiz,
}

/// Story privacy settings
enum StoryPrivacy { public, followers, closeFriends, custom, private }

/// Story types
enum StoryType { regular, highlight, archive, featured, sponsored }

/// Story reaction model
@freezed
abstract class StoryReaction with _$StoryReaction {
  const factory StoryReaction({
    required String userId,
    required String userName,
    required String userAvatarUrl,
    required String reactionType,
    required DateTime timestamp,
  }) = _StoryReaction;

  factory StoryReaction.fromJson(Map<String, dynamic> json) =>
      _$StoryReactionFromJson(json);
}

/// Story reply model
@freezed
abstract class StoryReply with _$StoryReply {
  const factory StoryReply({
    required String id,
    required String userId,
    required String userName,
    required String userAvatarUrl,
    required String message,
    required DateTime timestamp,
    @Default(false) bool isRead,
  }) = _StoryReply;

  factory StoryReply.fromJson(Map<String, dynamic> json) =>
      _$StoryReplyFromJson(json);
}

/// Story statistics model
@freezed
abstract class StoryStats with _$StoryStats {
  const factory StoryStats({
    required int viewCount,
    required int replyCount,
    required int reactionCount,
    required int shareCount,
    required double completionRate,
    required double skipRate,
    required List<String> topViewers,
    required Map<String, int> reactionBreakdown,
  }) = _StoryStats;

  factory StoryStats.fromJson(Map<String, dynamic> json) =>
      _$StoryStatsFromJson(json);
}

/// Extension methods for Story model
extension StoryExtensions on Story {
  /// Create Story from Firestore document
  static Story fromFirestore(String id, Map<String, dynamic> data) {
    return Story(
      id: id,
      userId: data['userId'] ?? '',
      userName: data['userName'] ?? '',
      userAvatarUrl: data['userAvatarUrl'] ?? '',
      mediaUrl: data['mediaUrl'] ?? '',
      mediaType: StoryMediaType.values.firstWhere(
        (e) => e.toString().split('.').last == (data['mediaType'] ?? 'image'),
        orElse: () => StoryMediaType.image,
      ),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      expiresAt: (data['expiresAt'] as Timestamp).toDate(),
      viewers: List<String>.from(data['viewers'] ?? []),
      allowedTo: List<String>.from(data['allowedTo'] ?? []),
      reactions:
          (data['reactions'] as List<dynamic>?)
              ?.map((e) => StoryReaction.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      replies:
          (data['replies'] as List<dynamic>?)
              ?.map((e) => StoryReply.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      privacy: StoryPrivacy.values.firstWhere(
        (e) => e.toString().split('.').last == (data['privacy'] ?? 'public'),
        orElse: () => StoryPrivacy.public,
      ),
      caption: data['caption'],
      location: data['location'],
      hashtags: List<String>.from(data['hashtags'] ?? []),
      mentions: List<String>.from(data['mentions'] ?? []),
      isHighlighted: data['isHighlighted'] ?? false,
      isArchived: data['isArchived'] ?? false,
      musicUrl: data['musicUrl'],
      musicTitle: data['musicTitle'],
      storyType: StoryType.values.firstWhere(
        (e) => e.toString().split('.').last == (data['storyType'] ?? 'regular'),
        orElse: () => StoryType.regular,
      ),
      metadata: data['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'userName': userName,
      'userAvatarUrl': userAvatarUrl,
      'mediaUrl': mediaUrl,
      'mediaType': mediaType.toString().split('.').last,
      'createdAt': Timestamp.fromDate(createdAt),
      'expiresAt': Timestamp.fromDate(expiresAt),
      'viewers': viewers,
      'allowedTo': allowedTo,
      'reactions': reactions.map((e) => e.toJson()).toList(),
      'replies': replies.map((e) => e.toJson()).toList(),
      'privacy': privacy.toString().split('.').last,
      'caption': caption,
      'location': location,
      'hashtags': hashtags,
      'mentions': mentions,
      'isHighlighted': isHighlighted,
      'isArchived': isArchived,
      'musicUrl': musicUrl,
      'musicTitle': musicTitle,
      'storyType': storyType.toString().split('.').last,
      'metadata': metadata,
    };
  }
}
