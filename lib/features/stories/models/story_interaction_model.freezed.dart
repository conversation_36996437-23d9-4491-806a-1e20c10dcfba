// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'story_interaction_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$StoryReaction {

 String get id; String get storyId; String get userId; String get username; String get userAvatarUrl; StoryReactionType get type; DateTime get timestamp;
/// Create a copy of StoryReaction
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StoryReactionCopyWith<StoryReaction> get copyWith => _$StoryReactionCopyWithImpl<StoryReaction>(this as StoryReaction, _$identity);

  /// Serializes this StoryReaction to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StoryReaction&&(identical(other.id, id) || other.id == id)&&(identical(other.storyId, storyId) || other.storyId == storyId)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.username, username) || other.username == username)&&(identical(other.userAvatarUrl, userAvatarUrl) || other.userAvatarUrl == userAvatarUrl)&&(identical(other.type, type) || other.type == type)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,storyId,userId,username,userAvatarUrl,type,timestamp);

@override
String toString() {
  return 'StoryReaction(id: $id, storyId: $storyId, userId: $userId, username: $username, userAvatarUrl: $userAvatarUrl, type: $type, timestamp: $timestamp)';
}


}

/// @nodoc
abstract mixin class $StoryReactionCopyWith<$Res>  {
  factory $StoryReactionCopyWith(StoryReaction value, $Res Function(StoryReaction) _then) = _$StoryReactionCopyWithImpl;
@useResult
$Res call({
 String id, String storyId, String userId, String username, String userAvatarUrl, StoryReactionType type, DateTime timestamp
});




}
/// @nodoc
class _$StoryReactionCopyWithImpl<$Res>
    implements $StoryReactionCopyWith<$Res> {
  _$StoryReactionCopyWithImpl(this._self, this._then);

  final StoryReaction _self;
  final $Res Function(StoryReaction) _then;

/// Create a copy of StoryReaction
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? storyId = null,Object? userId = null,Object? username = null,Object? userAvatarUrl = null,Object? type = null,Object? timestamp = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,storyId: null == storyId ? _self.storyId : storyId // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,userAvatarUrl: null == userAvatarUrl ? _self.userAvatarUrl : userAvatarUrl // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as StoryReactionType,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// Adds pattern-matching-related methods to [StoryReaction].
extension StoryReactionPatterns on StoryReaction {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _StoryReaction value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _StoryReaction() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _StoryReaction value)  $default,){
final _that = this;
switch (_that) {
case _StoryReaction():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _StoryReaction value)?  $default,){
final _that = this;
switch (_that) {
case _StoryReaction() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String storyId,  String userId,  String username,  String userAvatarUrl,  StoryReactionType type,  DateTime timestamp)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _StoryReaction() when $default != null:
return $default(_that.id,_that.storyId,_that.userId,_that.username,_that.userAvatarUrl,_that.type,_that.timestamp);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String storyId,  String userId,  String username,  String userAvatarUrl,  StoryReactionType type,  DateTime timestamp)  $default,) {final _that = this;
switch (_that) {
case _StoryReaction():
return $default(_that.id,_that.storyId,_that.userId,_that.username,_that.userAvatarUrl,_that.type,_that.timestamp);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String storyId,  String userId,  String username,  String userAvatarUrl,  StoryReactionType type,  DateTime timestamp)?  $default,) {final _that = this;
switch (_that) {
case _StoryReaction() when $default != null:
return $default(_that.id,_that.storyId,_that.userId,_that.username,_that.userAvatarUrl,_that.type,_that.timestamp);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _StoryReaction implements StoryReaction {
  const _StoryReaction({required this.id, required this.storyId, required this.userId, required this.username, required this.userAvatarUrl, required this.type, required this.timestamp});
  factory _StoryReaction.fromJson(Map<String, dynamic> json) => _$StoryReactionFromJson(json);

@override final  String id;
@override final  String storyId;
@override final  String userId;
@override final  String username;
@override final  String userAvatarUrl;
@override final  StoryReactionType type;
@override final  DateTime timestamp;

/// Create a copy of StoryReaction
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StoryReactionCopyWith<_StoryReaction> get copyWith => __$StoryReactionCopyWithImpl<_StoryReaction>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$StoryReactionToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StoryReaction&&(identical(other.id, id) || other.id == id)&&(identical(other.storyId, storyId) || other.storyId == storyId)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.username, username) || other.username == username)&&(identical(other.userAvatarUrl, userAvatarUrl) || other.userAvatarUrl == userAvatarUrl)&&(identical(other.type, type) || other.type == type)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,storyId,userId,username,userAvatarUrl,type,timestamp);

@override
String toString() {
  return 'StoryReaction(id: $id, storyId: $storyId, userId: $userId, username: $username, userAvatarUrl: $userAvatarUrl, type: $type, timestamp: $timestamp)';
}


}

/// @nodoc
abstract mixin class _$StoryReactionCopyWith<$Res> implements $StoryReactionCopyWith<$Res> {
  factory _$StoryReactionCopyWith(_StoryReaction value, $Res Function(_StoryReaction) _then) = __$StoryReactionCopyWithImpl;
@override @useResult
$Res call({
 String id, String storyId, String userId, String username, String userAvatarUrl, StoryReactionType type, DateTime timestamp
});




}
/// @nodoc
class __$StoryReactionCopyWithImpl<$Res>
    implements _$StoryReactionCopyWith<$Res> {
  __$StoryReactionCopyWithImpl(this._self, this._then);

  final _StoryReaction _self;
  final $Res Function(_StoryReaction) _then;

/// Create a copy of StoryReaction
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? storyId = null,Object? userId = null,Object? username = null,Object? userAvatarUrl = null,Object? type = null,Object? timestamp = null,}) {
  return _then(_StoryReaction(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,storyId: null == storyId ? _self.storyId : storyId // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,userAvatarUrl: null == userAvatarUrl ? _self.userAvatarUrl : userAvatarUrl // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as StoryReactionType,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}


/// @nodoc
mixin _$StoryReply {

 String get id; String get storyId; String get userId; String get username; String get userAvatarUrl; String get content; String? get mediaUrl; DateTime get timestamp; bool get isRead; bool get isReplied;
/// Create a copy of StoryReply
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StoryReplyCopyWith<StoryReply> get copyWith => _$StoryReplyCopyWithImpl<StoryReply>(this as StoryReply, _$identity);

  /// Serializes this StoryReply to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StoryReply&&(identical(other.id, id) || other.id == id)&&(identical(other.storyId, storyId) || other.storyId == storyId)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.username, username) || other.username == username)&&(identical(other.userAvatarUrl, userAvatarUrl) || other.userAvatarUrl == userAvatarUrl)&&(identical(other.content, content) || other.content == content)&&(identical(other.mediaUrl, mediaUrl) || other.mediaUrl == mediaUrl)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.isRead, isRead) || other.isRead == isRead)&&(identical(other.isReplied, isReplied) || other.isReplied == isReplied));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,storyId,userId,username,userAvatarUrl,content,mediaUrl,timestamp,isRead,isReplied);

@override
String toString() {
  return 'StoryReply(id: $id, storyId: $storyId, userId: $userId, username: $username, userAvatarUrl: $userAvatarUrl, content: $content, mediaUrl: $mediaUrl, timestamp: $timestamp, isRead: $isRead, isReplied: $isReplied)';
}


}

/// @nodoc
abstract mixin class $StoryReplyCopyWith<$Res>  {
  factory $StoryReplyCopyWith(StoryReply value, $Res Function(StoryReply) _then) = _$StoryReplyCopyWithImpl;
@useResult
$Res call({
 String id, String storyId, String userId, String username, String userAvatarUrl, String content, String? mediaUrl, DateTime timestamp, bool isRead, bool isReplied
});




}
/// @nodoc
class _$StoryReplyCopyWithImpl<$Res>
    implements $StoryReplyCopyWith<$Res> {
  _$StoryReplyCopyWithImpl(this._self, this._then);

  final StoryReply _self;
  final $Res Function(StoryReply) _then;

/// Create a copy of StoryReply
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? storyId = null,Object? userId = null,Object? username = null,Object? userAvatarUrl = null,Object? content = null,Object? mediaUrl = freezed,Object? timestamp = null,Object? isRead = null,Object? isReplied = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,storyId: null == storyId ? _self.storyId : storyId // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,userAvatarUrl: null == userAvatarUrl ? _self.userAvatarUrl : userAvatarUrl // ignore: cast_nullable_to_non_nullable
as String,content: null == content ? _self.content : content // ignore: cast_nullable_to_non_nullable
as String,mediaUrl: freezed == mediaUrl ? _self.mediaUrl : mediaUrl // ignore: cast_nullable_to_non_nullable
as String?,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,isRead: null == isRead ? _self.isRead : isRead // ignore: cast_nullable_to_non_nullable
as bool,isReplied: null == isReplied ? _self.isReplied : isReplied // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [StoryReply].
extension StoryReplyPatterns on StoryReply {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _StoryReply value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _StoryReply() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _StoryReply value)  $default,){
final _that = this;
switch (_that) {
case _StoryReply():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _StoryReply value)?  $default,){
final _that = this;
switch (_that) {
case _StoryReply() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String storyId,  String userId,  String username,  String userAvatarUrl,  String content,  String? mediaUrl,  DateTime timestamp,  bool isRead,  bool isReplied)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _StoryReply() when $default != null:
return $default(_that.id,_that.storyId,_that.userId,_that.username,_that.userAvatarUrl,_that.content,_that.mediaUrl,_that.timestamp,_that.isRead,_that.isReplied);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String storyId,  String userId,  String username,  String userAvatarUrl,  String content,  String? mediaUrl,  DateTime timestamp,  bool isRead,  bool isReplied)  $default,) {final _that = this;
switch (_that) {
case _StoryReply():
return $default(_that.id,_that.storyId,_that.userId,_that.username,_that.userAvatarUrl,_that.content,_that.mediaUrl,_that.timestamp,_that.isRead,_that.isReplied);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String storyId,  String userId,  String username,  String userAvatarUrl,  String content,  String? mediaUrl,  DateTime timestamp,  bool isRead,  bool isReplied)?  $default,) {final _that = this;
switch (_that) {
case _StoryReply() when $default != null:
return $default(_that.id,_that.storyId,_that.userId,_that.username,_that.userAvatarUrl,_that.content,_that.mediaUrl,_that.timestamp,_that.isRead,_that.isReplied);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _StoryReply implements StoryReply {
  const _StoryReply({required this.id, required this.storyId, required this.userId, required this.username, required this.userAvatarUrl, required this.content, this.mediaUrl, required this.timestamp, this.isRead = false, this.isReplied = false});
  factory _StoryReply.fromJson(Map<String, dynamic> json) => _$StoryReplyFromJson(json);

@override final  String id;
@override final  String storyId;
@override final  String userId;
@override final  String username;
@override final  String userAvatarUrl;
@override final  String content;
@override final  String? mediaUrl;
@override final  DateTime timestamp;
@override@JsonKey() final  bool isRead;
@override@JsonKey() final  bool isReplied;

/// Create a copy of StoryReply
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StoryReplyCopyWith<_StoryReply> get copyWith => __$StoryReplyCopyWithImpl<_StoryReply>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$StoryReplyToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StoryReply&&(identical(other.id, id) || other.id == id)&&(identical(other.storyId, storyId) || other.storyId == storyId)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.username, username) || other.username == username)&&(identical(other.userAvatarUrl, userAvatarUrl) || other.userAvatarUrl == userAvatarUrl)&&(identical(other.content, content) || other.content == content)&&(identical(other.mediaUrl, mediaUrl) || other.mediaUrl == mediaUrl)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.isRead, isRead) || other.isRead == isRead)&&(identical(other.isReplied, isReplied) || other.isReplied == isReplied));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,storyId,userId,username,userAvatarUrl,content,mediaUrl,timestamp,isRead,isReplied);

@override
String toString() {
  return 'StoryReply(id: $id, storyId: $storyId, userId: $userId, username: $username, userAvatarUrl: $userAvatarUrl, content: $content, mediaUrl: $mediaUrl, timestamp: $timestamp, isRead: $isRead, isReplied: $isReplied)';
}


}

/// @nodoc
abstract mixin class _$StoryReplyCopyWith<$Res> implements $StoryReplyCopyWith<$Res> {
  factory _$StoryReplyCopyWith(_StoryReply value, $Res Function(_StoryReply) _then) = __$StoryReplyCopyWithImpl;
@override @useResult
$Res call({
 String id, String storyId, String userId, String username, String userAvatarUrl, String content, String? mediaUrl, DateTime timestamp, bool isRead, bool isReplied
});




}
/// @nodoc
class __$StoryReplyCopyWithImpl<$Res>
    implements _$StoryReplyCopyWith<$Res> {
  __$StoryReplyCopyWithImpl(this._self, this._then);

  final _StoryReply _self;
  final $Res Function(_StoryReply) _then;

/// Create a copy of StoryReply
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? storyId = null,Object? userId = null,Object? username = null,Object? userAvatarUrl = null,Object? content = null,Object? mediaUrl = freezed,Object? timestamp = null,Object? isRead = null,Object? isReplied = null,}) {
  return _then(_StoryReply(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,storyId: null == storyId ? _self.storyId : storyId // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,userAvatarUrl: null == userAvatarUrl ? _self.userAvatarUrl : userAvatarUrl // ignore: cast_nullable_to_non_nullable
as String,content: null == content ? _self.content : content // ignore: cast_nullable_to_non_nullable
as String,mediaUrl: freezed == mediaUrl ? _self.mediaUrl : mediaUrl // ignore: cast_nullable_to_non_nullable
as String?,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,isRead: null == isRead ? _self.isRead : isRead // ignore: cast_nullable_to_non_nullable
as bool,isReplied: null == isReplied ? _self.isReplied : isReplied // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$StoryPrivacySettings {

 StoryPrivacy get privacy; List<String> get allowedViewers; List<String> get blockedViewers; bool get allowReplies; bool get allowReactions; bool get allowScreenshots; bool get hideFromStory; bool get muteStory;
/// Create a copy of StoryPrivacySettings
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StoryPrivacySettingsCopyWith<StoryPrivacySettings> get copyWith => _$StoryPrivacySettingsCopyWithImpl<StoryPrivacySettings>(this as StoryPrivacySettings, _$identity);

  /// Serializes this StoryPrivacySettings to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StoryPrivacySettings&&(identical(other.privacy, privacy) || other.privacy == privacy)&&const DeepCollectionEquality().equals(other.allowedViewers, allowedViewers)&&const DeepCollectionEquality().equals(other.blockedViewers, blockedViewers)&&(identical(other.allowReplies, allowReplies) || other.allowReplies == allowReplies)&&(identical(other.allowReactions, allowReactions) || other.allowReactions == allowReactions)&&(identical(other.allowScreenshots, allowScreenshots) || other.allowScreenshots == allowScreenshots)&&(identical(other.hideFromStory, hideFromStory) || other.hideFromStory == hideFromStory)&&(identical(other.muteStory, muteStory) || other.muteStory == muteStory));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,privacy,const DeepCollectionEquality().hash(allowedViewers),const DeepCollectionEquality().hash(blockedViewers),allowReplies,allowReactions,allowScreenshots,hideFromStory,muteStory);

@override
String toString() {
  return 'StoryPrivacySettings(privacy: $privacy, allowedViewers: $allowedViewers, blockedViewers: $blockedViewers, allowReplies: $allowReplies, allowReactions: $allowReactions, allowScreenshots: $allowScreenshots, hideFromStory: $hideFromStory, muteStory: $muteStory)';
}


}

/// @nodoc
abstract mixin class $StoryPrivacySettingsCopyWith<$Res>  {
  factory $StoryPrivacySettingsCopyWith(StoryPrivacySettings value, $Res Function(StoryPrivacySettings) _then) = _$StoryPrivacySettingsCopyWithImpl;
@useResult
$Res call({
 StoryPrivacy privacy, List<String> allowedViewers, List<String> blockedViewers, bool allowReplies, bool allowReactions, bool allowScreenshots, bool hideFromStory, bool muteStory
});




}
/// @nodoc
class _$StoryPrivacySettingsCopyWithImpl<$Res>
    implements $StoryPrivacySettingsCopyWith<$Res> {
  _$StoryPrivacySettingsCopyWithImpl(this._self, this._then);

  final StoryPrivacySettings _self;
  final $Res Function(StoryPrivacySettings) _then;

/// Create a copy of StoryPrivacySettings
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? privacy = null,Object? allowedViewers = null,Object? blockedViewers = null,Object? allowReplies = null,Object? allowReactions = null,Object? allowScreenshots = null,Object? hideFromStory = null,Object? muteStory = null,}) {
  return _then(_self.copyWith(
privacy: null == privacy ? _self.privacy : privacy // ignore: cast_nullable_to_non_nullable
as StoryPrivacy,allowedViewers: null == allowedViewers ? _self.allowedViewers : allowedViewers // ignore: cast_nullable_to_non_nullable
as List<String>,blockedViewers: null == blockedViewers ? _self.blockedViewers : blockedViewers // ignore: cast_nullable_to_non_nullable
as List<String>,allowReplies: null == allowReplies ? _self.allowReplies : allowReplies // ignore: cast_nullable_to_non_nullable
as bool,allowReactions: null == allowReactions ? _self.allowReactions : allowReactions // ignore: cast_nullable_to_non_nullable
as bool,allowScreenshots: null == allowScreenshots ? _self.allowScreenshots : allowScreenshots // ignore: cast_nullable_to_non_nullable
as bool,hideFromStory: null == hideFromStory ? _self.hideFromStory : hideFromStory // ignore: cast_nullable_to_non_nullable
as bool,muteStory: null == muteStory ? _self.muteStory : muteStory // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [StoryPrivacySettings].
extension StoryPrivacySettingsPatterns on StoryPrivacySettings {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _StoryPrivacySettings value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _StoryPrivacySettings() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _StoryPrivacySettings value)  $default,){
final _that = this;
switch (_that) {
case _StoryPrivacySettings():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _StoryPrivacySettings value)?  $default,){
final _that = this;
switch (_that) {
case _StoryPrivacySettings() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( StoryPrivacy privacy,  List<String> allowedViewers,  List<String> blockedViewers,  bool allowReplies,  bool allowReactions,  bool allowScreenshots,  bool hideFromStory,  bool muteStory)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _StoryPrivacySettings() when $default != null:
return $default(_that.privacy,_that.allowedViewers,_that.blockedViewers,_that.allowReplies,_that.allowReactions,_that.allowScreenshots,_that.hideFromStory,_that.muteStory);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( StoryPrivacy privacy,  List<String> allowedViewers,  List<String> blockedViewers,  bool allowReplies,  bool allowReactions,  bool allowScreenshots,  bool hideFromStory,  bool muteStory)  $default,) {final _that = this;
switch (_that) {
case _StoryPrivacySettings():
return $default(_that.privacy,_that.allowedViewers,_that.blockedViewers,_that.allowReplies,_that.allowReactions,_that.allowScreenshots,_that.hideFromStory,_that.muteStory);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( StoryPrivacy privacy,  List<String> allowedViewers,  List<String> blockedViewers,  bool allowReplies,  bool allowReactions,  bool allowScreenshots,  bool hideFromStory,  bool muteStory)?  $default,) {final _that = this;
switch (_that) {
case _StoryPrivacySettings() when $default != null:
return $default(_that.privacy,_that.allowedViewers,_that.blockedViewers,_that.allowReplies,_that.allowReactions,_that.allowScreenshots,_that.hideFromStory,_that.muteStory);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _StoryPrivacySettings implements StoryPrivacySettings {
  const _StoryPrivacySettings({this.privacy = StoryPrivacy.public, final  List<String> allowedViewers = const [], final  List<String> blockedViewers = const [], this.allowReplies = true, this.allowReactions = true, this.allowScreenshots = true, this.hideFromStory = false, this.muteStory = false}): _allowedViewers = allowedViewers,_blockedViewers = blockedViewers;
  factory _StoryPrivacySettings.fromJson(Map<String, dynamic> json) => _$StoryPrivacySettingsFromJson(json);

@override@JsonKey() final  StoryPrivacy privacy;
 final  List<String> _allowedViewers;
@override@JsonKey() List<String> get allowedViewers {
  if (_allowedViewers is EqualUnmodifiableListView) return _allowedViewers;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_allowedViewers);
}

 final  List<String> _blockedViewers;
@override@JsonKey() List<String> get blockedViewers {
  if (_blockedViewers is EqualUnmodifiableListView) return _blockedViewers;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_blockedViewers);
}

@override@JsonKey() final  bool allowReplies;
@override@JsonKey() final  bool allowReactions;
@override@JsonKey() final  bool allowScreenshots;
@override@JsonKey() final  bool hideFromStory;
@override@JsonKey() final  bool muteStory;

/// Create a copy of StoryPrivacySettings
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StoryPrivacySettingsCopyWith<_StoryPrivacySettings> get copyWith => __$StoryPrivacySettingsCopyWithImpl<_StoryPrivacySettings>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$StoryPrivacySettingsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StoryPrivacySettings&&(identical(other.privacy, privacy) || other.privacy == privacy)&&const DeepCollectionEquality().equals(other._allowedViewers, _allowedViewers)&&const DeepCollectionEquality().equals(other._blockedViewers, _blockedViewers)&&(identical(other.allowReplies, allowReplies) || other.allowReplies == allowReplies)&&(identical(other.allowReactions, allowReactions) || other.allowReactions == allowReactions)&&(identical(other.allowScreenshots, allowScreenshots) || other.allowScreenshots == allowScreenshots)&&(identical(other.hideFromStory, hideFromStory) || other.hideFromStory == hideFromStory)&&(identical(other.muteStory, muteStory) || other.muteStory == muteStory));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,privacy,const DeepCollectionEquality().hash(_allowedViewers),const DeepCollectionEquality().hash(_blockedViewers),allowReplies,allowReactions,allowScreenshots,hideFromStory,muteStory);

@override
String toString() {
  return 'StoryPrivacySettings(privacy: $privacy, allowedViewers: $allowedViewers, blockedViewers: $blockedViewers, allowReplies: $allowReplies, allowReactions: $allowReactions, allowScreenshots: $allowScreenshots, hideFromStory: $hideFromStory, muteStory: $muteStory)';
}


}

/// @nodoc
abstract mixin class _$StoryPrivacySettingsCopyWith<$Res> implements $StoryPrivacySettingsCopyWith<$Res> {
  factory _$StoryPrivacySettingsCopyWith(_StoryPrivacySettings value, $Res Function(_StoryPrivacySettings) _then) = __$StoryPrivacySettingsCopyWithImpl;
@override @useResult
$Res call({
 StoryPrivacy privacy, List<String> allowedViewers, List<String> blockedViewers, bool allowReplies, bool allowReactions, bool allowScreenshots, bool hideFromStory, bool muteStory
});




}
/// @nodoc
class __$StoryPrivacySettingsCopyWithImpl<$Res>
    implements _$StoryPrivacySettingsCopyWith<$Res> {
  __$StoryPrivacySettingsCopyWithImpl(this._self, this._then);

  final _StoryPrivacySettings _self;
  final $Res Function(_StoryPrivacySettings) _then;

/// Create a copy of StoryPrivacySettings
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? privacy = null,Object? allowedViewers = null,Object? blockedViewers = null,Object? allowReplies = null,Object? allowReactions = null,Object? allowScreenshots = null,Object? hideFromStory = null,Object? muteStory = null,}) {
  return _then(_StoryPrivacySettings(
privacy: null == privacy ? _self.privacy : privacy // ignore: cast_nullable_to_non_nullable
as StoryPrivacy,allowedViewers: null == allowedViewers ? _self._allowedViewers : allowedViewers // ignore: cast_nullable_to_non_nullable
as List<String>,blockedViewers: null == blockedViewers ? _self._blockedViewers : blockedViewers // ignore: cast_nullable_to_non_nullable
as List<String>,allowReplies: null == allowReplies ? _self.allowReplies : allowReplies // ignore: cast_nullable_to_non_nullable
as bool,allowReactions: null == allowReactions ? _self.allowReactions : allowReactions // ignore: cast_nullable_to_non_nullable
as bool,allowScreenshots: null == allowScreenshots ? _self.allowScreenshots : allowScreenshots // ignore: cast_nullable_to_non_nullable
as bool,hideFromStory: null == hideFromStory ? _self.hideFromStory : hideFromStory // ignore: cast_nullable_to_non_nullable
as bool,muteStory: null == muteStory ? _self.muteStory : muteStory // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$StoryAnalytics {

 String get storyId; int get viewCount; int get uniqueViewers; int get replyCount; int get reactionCount; Map<StoryReactionType, int> get reactionBreakdown; Duration get averageViewDuration; DateTime get createdAt; DateTime? get expiresAt; List<String> get viewedBy; List<String> get repliedBy; List<String> get reactedBy;
/// Create a copy of StoryAnalytics
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StoryAnalyticsCopyWith<StoryAnalytics> get copyWith => _$StoryAnalyticsCopyWithImpl<StoryAnalytics>(this as StoryAnalytics, _$identity);

  /// Serializes this StoryAnalytics to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StoryAnalytics&&(identical(other.storyId, storyId) || other.storyId == storyId)&&(identical(other.viewCount, viewCount) || other.viewCount == viewCount)&&(identical(other.uniqueViewers, uniqueViewers) || other.uniqueViewers == uniqueViewers)&&(identical(other.replyCount, replyCount) || other.replyCount == replyCount)&&(identical(other.reactionCount, reactionCount) || other.reactionCount == reactionCount)&&const DeepCollectionEquality().equals(other.reactionBreakdown, reactionBreakdown)&&(identical(other.averageViewDuration, averageViewDuration) || other.averageViewDuration == averageViewDuration)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.expiresAt, expiresAt) || other.expiresAt == expiresAt)&&const DeepCollectionEquality().equals(other.viewedBy, viewedBy)&&const DeepCollectionEquality().equals(other.repliedBy, repliedBy)&&const DeepCollectionEquality().equals(other.reactedBy, reactedBy));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,storyId,viewCount,uniqueViewers,replyCount,reactionCount,const DeepCollectionEquality().hash(reactionBreakdown),averageViewDuration,createdAt,expiresAt,const DeepCollectionEquality().hash(viewedBy),const DeepCollectionEquality().hash(repliedBy),const DeepCollectionEquality().hash(reactedBy));

@override
String toString() {
  return 'StoryAnalytics(storyId: $storyId, viewCount: $viewCount, uniqueViewers: $uniqueViewers, replyCount: $replyCount, reactionCount: $reactionCount, reactionBreakdown: $reactionBreakdown, averageViewDuration: $averageViewDuration, createdAt: $createdAt, expiresAt: $expiresAt, viewedBy: $viewedBy, repliedBy: $repliedBy, reactedBy: $reactedBy)';
}


}

/// @nodoc
abstract mixin class $StoryAnalyticsCopyWith<$Res>  {
  factory $StoryAnalyticsCopyWith(StoryAnalytics value, $Res Function(StoryAnalytics) _then) = _$StoryAnalyticsCopyWithImpl;
@useResult
$Res call({
 String storyId, int viewCount, int uniqueViewers, int replyCount, int reactionCount, Map<StoryReactionType, int> reactionBreakdown, Duration averageViewDuration, DateTime createdAt, DateTime? expiresAt, List<String> viewedBy, List<String> repliedBy, List<String> reactedBy
});




}
/// @nodoc
class _$StoryAnalyticsCopyWithImpl<$Res>
    implements $StoryAnalyticsCopyWith<$Res> {
  _$StoryAnalyticsCopyWithImpl(this._self, this._then);

  final StoryAnalytics _self;
  final $Res Function(StoryAnalytics) _then;

/// Create a copy of StoryAnalytics
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? storyId = null,Object? viewCount = null,Object? uniqueViewers = null,Object? replyCount = null,Object? reactionCount = null,Object? reactionBreakdown = null,Object? averageViewDuration = null,Object? createdAt = null,Object? expiresAt = freezed,Object? viewedBy = null,Object? repliedBy = null,Object? reactedBy = null,}) {
  return _then(_self.copyWith(
storyId: null == storyId ? _self.storyId : storyId // ignore: cast_nullable_to_non_nullable
as String,viewCount: null == viewCount ? _self.viewCount : viewCount // ignore: cast_nullable_to_non_nullable
as int,uniqueViewers: null == uniqueViewers ? _self.uniqueViewers : uniqueViewers // ignore: cast_nullable_to_non_nullable
as int,replyCount: null == replyCount ? _self.replyCount : replyCount // ignore: cast_nullable_to_non_nullable
as int,reactionCount: null == reactionCount ? _self.reactionCount : reactionCount // ignore: cast_nullable_to_non_nullable
as int,reactionBreakdown: null == reactionBreakdown ? _self.reactionBreakdown : reactionBreakdown // ignore: cast_nullable_to_non_nullable
as Map<StoryReactionType, int>,averageViewDuration: null == averageViewDuration ? _self.averageViewDuration : averageViewDuration // ignore: cast_nullable_to_non_nullable
as Duration,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,expiresAt: freezed == expiresAt ? _self.expiresAt : expiresAt // ignore: cast_nullable_to_non_nullable
as DateTime?,viewedBy: null == viewedBy ? _self.viewedBy : viewedBy // ignore: cast_nullable_to_non_nullable
as List<String>,repliedBy: null == repliedBy ? _self.repliedBy : repliedBy // ignore: cast_nullable_to_non_nullable
as List<String>,reactedBy: null == reactedBy ? _self.reactedBy : reactedBy // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}

}


/// Adds pattern-matching-related methods to [StoryAnalytics].
extension StoryAnalyticsPatterns on StoryAnalytics {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _StoryAnalytics value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _StoryAnalytics() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _StoryAnalytics value)  $default,){
final _that = this;
switch (_that) {
case _StoryAnalytics():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _StoryAnalytics value)?  $default,){
final _that = this;
switch (_that) {
case _StoryAnalytics() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String storyId,  int viewCount,  int uniqueViewers,  int replyCount,  int reactionCount,  Map<StoryReactionType, int> reactionBreakdown,  Duration averageViewDuration,  DateTime createdAt,  DateTime? expiresAt,  List<String> viewedBy,  List<String> repliedBy,  List<String> reactedBy)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _StoryAnalytics() when $default != null:
return $default(_that.storyId,_that.viewCount,_that.uniqueViewers,_that.replyCount,_that.reactionCount,_that.reactionBreakdown,_that.averageViewDuration,_that.createdAt,_that.expiresAt,_that.viewedBy,_that.repliedBy,_that.reactedBy);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String storyId,  int viewCount,  int uniqueViewers,  int replyCount,  int reactionCount,  Map<StoryReactionType, int> reactionBreakdown,  Duration averageViewDuration,  DateTime createdAt,  DateTime? expiresAt,  List<String> viewedBy,  List<String> repliedBy,  List<String> reactedBy)  $default,) {final _that = this;
switch (_that) {
case _StoryAnalytics():
return $default(_that.storyId,_that.viewCount,_that.uniqueViewers,_that.replyCount,_that.reactionCount,_that.reactionBreakdown,_that.averageViewDuration,_that.createdAt,_that.expiresAt,_that.viewedBy,_that.repliedBy,_that.reactedBy);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String storyId,  int viewCount,  int uniqueViewers,  int replyCount,  int reactionCount,  Map<StoryReactionType, int> reactionBreakdown,  Duration averageViewDuration,  DateTime createdAt,  DateTime? expiresAt,  List<String> viewedBy,  List<String> repliedBy,  List<String> reactedBy)?  $default,) {final _that = this;
switch (_that) {
case _StoryAnalytics() when $default != null:
return $default(_that.storyId,_that.viewCount,_that.uniqueViewers,_that.replyCount,_that.reactionCount,_that.reactionBreakdown,_that.averageViewDuration,_that.createdAt,_that.expiresAt,_that.viewedBy,_that.repliedBy,_that.reactedBy);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _StoryAnalytics implements StoryAnalytics {
  const _StoryAnalytics({required this.storyId, required this.viewCount, required this.uniqueViewers, required this.replyCount, required this.reactionCount, required final  Map<StoryReactionType, int> reactionBreakdown, required this.averageViewDuration, required this.createdAt, required this.expiresAt, final  List<String> viewedBy = const [], final  List<String> repliedBy = const [], final  List<String> reactedBy = const []}): _reactionBreakdown = reactionBreakdown,_viewedBy = viewedBy,_repliedBy = repliedBy,_reactedBy = reactedBy;
  factory _StoryAnalytics.fromJson(Map<String, dynamic> json) => _$StoryAnalyticsFromJson(json);

@override final  String storyId;
@override final  int viewCount;
@override final  int uniqueViewers;
@override final  int replyCount;
@override final  int reactionCount;
 final  Map<StoryReactionType, int> _reactionBreakdown;
@override Map<StoryReactionType, int> get reactionBreakdown {
  if (_reactionBreakdown is EqualUnmodifiableMapView) return _reactionBreakdown;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_reactionBreakdown);
}

@override final  Duration averageViewDuration;
@override final  DateTime createdAt;
@override final  DateTime? expiresAt;
 final  List<String> _viewedBy;
@override@JsonKey() List<String> get viewedBy {
  if (_viewedBy is EqualUnmodifiableListView) return _viewedBy;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_viewedBy);
}

 final  List<String> _repliedBy;
@override@JsonKey() List<String> get repliedBy {
  if (_repliedBy is EqualUnmodifiableListView) return _repliedBy;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_repliedBy);
}

 final  List<String> _reactedBy;
@override@JsonKey() List<String> get reactedBy {
  if (_reactedBy is EqualUnmodifiableListView) return _reactedBy;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_reactedBy);
}


/// Create a copy of StoryAnalytics
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StoryAnalyticsCopyWith<_StoryAnalytics> get copyWith => __$StoryAnalyticsCopyWithImpl<_StoryAnalytics>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$StoryAnalyticsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StoryAnalytics&&(identical(other.storyId, storyId) || other.storyId == storyId)&&(identical(other.viewCount, viewCount) || other.viewCount == viewCount)&&(identical(other.uniqueViewers, uniqueViewers) || other.uniqueViewers == uniqueViewers)&&(identical(other.replyCount, replyCount) || other.replyCount == replyCount)&&(identical(other.reactionCount, reactionCount) || other.reactionCount == reactionCount)&&const DeepCollectionEquality().equals(other._reactionBreakdown, _reactionBreakdown)&&(identical(other.averageViewDuration, averageViewDuration) || other.averageViewDuration == averageViewDuration)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.expiresAt, expiresAt) || other.expiresAt == expiresAt)&&const DeepCollectionEquality().equals(other._viewedBy, _viewedBy)&&const DeepCollectionEquality().equals(other._repliedBy, _repliedBy)&&const DeepCollectionEquality().equals(other._reactedBy, _reactedBy));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,storyId,viewCount,uniqueViewers,replyCount,reactionCount,const DeepCollectionEquality().hash(_reactionBreakdown),averageViewDuration,createdAt,expiresAt,const DeepCollectionEquality().hash(_viewedBy),const DeepCollectionEquality().hash(_repliedBy),const DeepCollectionEquality().hash(_reactedBy));

@override
String toString() {
  return 'StoryAnalytics(storyId: $storyId, viewCount: $viewCount, uniqueViewers: $uniqueViewers, replyCount: $replyCount, reactionCount: $reactionCount, reactionBreakdown: $reactionBreakdown, averageViewDuration: $averageViewDuration, createdAt: $createdAt, expiresAt: $expiresAt, viewedBy: $viewedBy, repliedBy: $repliedBy, reactedBy: $reactedBy)';
}


}

/// @nodoc
abstract mixin class _$StoryAnalyticsCopyWith<$Res> implements $StoryAnalyticsCopyWith<$Res> {
  factory _$StoryAnalyticsCopyWith(_StoryAnalytics value, $Res Function(_StoryAnalytics) _then) = __$StoryAnalyticsCopyWithImpl;
@override @useResult
$Res call({
 String storyId, int viewCount, int uniqueViewers, int replyCount, int reactionCount, Map<StoryReactionType, int> reactionBreakdown, Duration averageViewDuration, DateTime createdAt, DateTime? expiresAt, List<String> viewedBy, List<String> repliedBy, List<String> reactedBy
});




}
/// @nodoc
class __$StoryAnalyticsCopyWithImpl<$Res>
    implements _$StoryAnalyticsCopyWith<$Res> {
  __$StoryAnalyticsCopyWithImpl(this._self, this._then);

  final _StoryAnalytics _self;
  final $Res Function(_StoryAnalytics) _then;

/// Create a copy of StoryAnalytics
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? storyId = null,Object? viewCount = null,Object? uniqueViewers = null,Object? replyCount = null,Object? reactionCount = null,Object? reactionBreakdown = null,Object? averageViewDuration = null,Object? createdAt = null,Object? expiresAt = freezed,Object? viewedBy = null,Object? repliedBy = null,Object? reactedBy = null,}) {
  return _then(_StoryAnalytics(
storyId: null == storyId ? _self.storyId : storyId // ignore: cast_nullable_to_non_nullable
as String,viewCount: null == viewCount ? _self.viewCount : viewCount // ignore: cast_nullable_to_non_nullable
as int,uniqueViewers: null == uniqueViewers ? _self.uniqueViewers : uniqueViewers // ignore: cast_nullable_to_non_nullable
as int,replyCount: null == replyCount ? _self.replyCount : replyCount // ignore: cast_nullable_to_non_nullable
as int,reactionCount: null == reactionCount ? _self.reactionCount : reactionCount // ignore: cast_nullable_to_non_nullable
as int,reactionBreakdown: null == reactionBreakdown ? _self._reactionBreakdown : reactionBreakdown // ignore: cast_nullable_to_non_nullable
as Map<StoryReactionType, int>,averageViewDuration: null == averageViewDuration ? _self.averageViewDuration : averageViewDuration // ignore: cast_nullable_to_non_nullable
as Duration,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,expiresAt: freezed == expiresAt ? _self.expiresAt : expiresAt // ignore: cast_nullable_to_non_nullable
as DateTime?,viewedBy: null == viewedBy ? _self._viewedBy : viewedBy // ignore: cast_nullable_to_non_nullable
as List<String>,repliedBy: null == repliedBy ? _self._repliedBy : repliedBy // ignore: cast_nullable_to_non_nullable
as List<String>,reactedBy: null == reactedBy ? _self._reactedBy : reactedBy // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}


}


/// @nodoc
mixin _$StoryReport {

 String get id; String get storyId; String get reportedBy; String get reason; String? get additionalInfo; DateTime get timestamp; String get status;// pending, reviewed, resolved, dismissed
 String? get reviewedBy; DateTime? get reviewedAt; String? get action;
/// Create a copy of StoryReport
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StoryReportCopyWith<StoryReport> get copyWith => _$StoryReportCopyWithImpl<StoryReport>(this as StoryReport, _$identity);

  /// Serializes this StoryReport to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StoryReport&&(identical(other.id, id) || other.id == id)&&(identical(other.storyId, storyId) || other.storyId == storyId)&&(identical(other.reportedBy, reportedBy) || other.reportedBy == reportedBy)&&(identical(other.reason, reason) || other.reason == reason)&&(identical(other.additionalInfo, additionalInfo) || other.additionalInfo == additionalInfo)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.status, status) || other.status == status)&&(identical(other.reviewedBy, reviewedBy) || other.reviewedBy == reviewedBy)&&(identical(other.reviewedAt, reviewedAt) || other.reviewedAt == reviewedAt)&&(identical(other.action, action) || other.action == action));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,storyId,reportedBy,reason,additionalInfo,timestamp,status,reviewedBy,reviewedAt,action);

@override
String toString() {
  return 'StoryReport(id: $id, storyId: $storyId, reportedBy: $reportedBy, reason: $reason, additionalInfo: $additionalInfo, timestamp: $timestamp, status: $status, reviewedBy: $reviewedBy, reviewedAt: $reviewedAt, action: $action)';
}


}

/// @nodoc
abstract mixin class $StoryReportCopyWith<$Res>  {
  factory $StoryReportCopyWith(StoryReport value, $Res Function(StoryReport) _then) = _$StoryReportCopyWithImpl;
@useResult
$Res call({
 String id, String storyId, String reportedBy, String reason, String? additionalInfo, DateTime timestamp, String status, String? reviewedBy, DateTime? reviewedAt, String? action
});




}
/// @nodoc
class _$StoryReportCopyWithImpl<$Res>
    implements $StoryReportCopyWith<$Res> {
  _$StoryReportCopyWithImpl(this._self, this._then);

  final StoryReport _self;
  final $Res Function(StoryReport) _then;

/// Create a copy of StoryReport
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? storyId = null,Object? reportedBy = null,Object? reason = null,Object? additionalInfo = freezed,Object? timestamp = null,Object? status = null,Object? reviewedBy = freezed,Object? reviewedAt = freezed,Object? action = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,storyId: null == storyId ? _self.storyId : storyId // ignore: cast_nullable_to_non_nullable
as String,reportedBy: null == reportedBy ? _self.reportedBy : reportedBy // ignore: cast_nullable_to_non_nullable
as String,reason: null == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String,additionalInfo: freezed == additionalInfo ? _self.additionalInfo : additionalInfo // ignore: cast_nullable_to_non_nullable
as String?,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,reviewedBy: freezed == reviewedBy ? _self.reviewedBy : reviewedBy // ignore: cast_nullable_to_non_nullable
as String?,reviewedAt: freezed == reviewedAt ? _self.reviewedAt : reviewedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,action: freezed == action ? _self.action : action // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [StoryReport].
extension StoryReportPatterns on StoryReport {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _StoryReport value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _StoryReport() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _StoryReport value)  $default,){
final _that = this;
switch (_that) {
case _StoryReport():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _StoryReport value)?  $default,){
final _that = this;
switch (_that) {
case _StoryReport() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String storyId,  String reportedBy,  String reason,  String? additionalInfo,  DateTime timestamp,  String status,  String? reviewedBy,  DateTime? reviewedAt,  String? action)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _StoryReport() when $default != null:
return $default(_that.id,_that.storyId,_that.reportedBy,_that.reason,_that.additionalInfo,_that.timestamp,_that.status,_that.reviewedBy,_that.reviewedAt,_that.action);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String storyId,  String reportedBy,  String reason,  String? additionalInfo,  DateTime timestamp,  String status,  String? reviewedBy,  DateTime? reviewedAt,  String? action)  $default,) {final _that = this;
switch (_that) {
case _StoryReport():
return $default(_that.id,_that.storyId,_that.reportedBy,_that.reason,_that.additionalInfo,_that.timestamp,_that.status,_that.reviewedBy,_that.reviewedAt,_that.action);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String storyId,  String reportedBy,  String reason,  String? additionalInfo,  DateTime timestamp,  String status,  String? reviewedBy,  DateTime? reviewedAt,  String? action)?  $default,) {final _that = this;
switch (_that) {
case _StoryReport() when $default != null:
return $default(_that.id,_that.storyId,_that.reportedBy,_that.reason,_that.additionalInfo,_that.timestamp,_that.status,_that.reviewedBy,_that.reviewedAt,_that.action);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _StoryReport implements StoryReport {
  const _StoryReport({required this.id, required this.storyId, required this.reportedBy, required this.reason, this.additionalInfo, required this.timestamp, this.status = 'pending', this.reviewedBy, this.reviewedAt, this.action});
  factory _StoryReport.fromJson(Map<String, dynamic> json) => _$StoryReportFromJson(json);

@override final  String id;
@override final  String storyId;
@override final  String reportedBy;
@override final  String reason;
@override final  String? additionalInfo;
@override final  DateTime timestamp;
@override@JsonKey() final  String status;
// pending, reviewed, resolved, dismissed
@override final  String? reviewedBy;
@override final  DateTime? reviewedAt;
@override final  String? action;

/// Create a copy of StoryReport
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StoryReportCopyWith<_StoryReport> get copyWith => __$StoryReportCopyWithImpl<_StoryReport>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$StoryReportToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StoryReport&&(identical(other.id, id) || other.id == id)&&(identical(other.storyId, storyId) || other.storyId == storyId)&&(identical(other.reportedBy, reportedBy) || other.reportedBy == reportedBy)&&(identical(other.reason, reason) || other.reason == reason)&&(identical(other.additionalInfo, additionalInfo) || other.additionalInfo == additionalInfo)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.status, status) || other.status == status)&&(identical(other.reviewedBy, reviewedBy) || other.reviewedBy == reviewedBy)&&(identical(other.reviewedAt, reviewedAt) || other.reviewedAt == reviewedAt)&&(identical(other.action, action) || other.action == action));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,storyId,reportedBy,reason,additionalInfo,timestamp,status,reviewedBy,reviewedAt,action);

@override
String toString() {
  return 'StoryReport(id: $id, storyId: $storyId, reportedBy: $reportedBy, reason: $reason, additionalInfo: $additionalInfo, timestamp: $timestamp, status: $status, reviewedBy: $reviewedBy, reviewedAt: $reviewedAt, action: $action)';
}


}

/// @nodoc
abstract mixin class _$StoryReportCopyWith<$Res> implements $StoryReportCopyWith<$Res> {
  factory _$StoryReportCopyWith(_StoryReport value, $Res Function(_StoryReport) _then) = __$StoryReportCopyWithImpl;
@override @useResult
$Res call({
 String id, String storyId, String reportedBy, String reason, String? additionalInfo, DateTime timestamp, String status, String? reviewedBy, DateTime? reviewedAt, String? action
});




}
/// @nodoc
class __$StoryReportCopyWithImpl<$Res>
    implements _$StoryReportCopyWith<$Res> {
  __$StoryReportCopyWithImpl(this._self, this._then);

  final _StoryReport _self;
  final $Res Function(_StoryReport) _then;

/// Create a copy of StoryReport
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? storyId = null,Object? reportedBy = null,Object? reason = null,Object? additionalInfo = freezed,Object? timestamp = null,Object? status = null,Object? reviewedBy = freezed,Object? reviewedAt = freezed,Object? action = freezed,}) {
  return _then(_StoryReport(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,storyId: null == storyId ? _self.storyId : storyId // ignore: cast_nullable_to_non_nullable
as String,reportedBy: null == reportedBy ? _self.reportedBy : reportedBy // ignore: cast_nullable_to_non_nullable
as String,reason: null == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String,additionalInfo: freezed == additionalInfo ? _self.additionalInfo : additionalInfo // ignore: cast_nullable_to_non_nullable
as String?,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,reviewedBy: freezed == reviewedBy ? _self.reviewedBy : reviewedBy // ignore: cast_nullable_to_non_nullable
as String?,reviewedAt: freezed == reviewedAt ? _self.reviewedAt : reviewedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,action: freezed == action ? _self.action : action // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
