// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'story_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Story {

 String get id; String get userId; String get userName; String get userAvatarUrl; String get mediaUrl; StoryMediaType get mediaType; DateTime get createdAt; DateTime get expiresAt; List<String> get viewers; List<String> get allowedTo; List<StoryReaction> get reactions; List<StoryReply> get replies; StoryPrivacy get privacy; String? get caption; String? get location; List<String> get hashtags; List<String> get mentions; bool get isHighlighted; bool get isArchived; String? get musicUrl; String? get musicTitle; StoryType get storyType; Map<String, dynamic>? get metadata;
/// Create a copy of Story
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StoryCopyWith<Story> get copyWith => _$StoryCopyWithImpl<Story>(this as Story, _$identity);

  /// Serializes this Story to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Story&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.userName, userName) || other.userName == userName)&&(identical(other.userAvatarUrl, userAvatarUrl) || other.userAvatarUrl == userAvatarUrl)&&(identical(other.mediaUrl, mediaUrl) || other.mediaUrl == mediaUrl)&&(identical(other.mediaType, mediaType) || other.mediaType == mediaType)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.expiresAt, expiresAt) || other.expiresAt == expiresAt)&&const DeepCollectionEquality().equals(other.viewers, viewers)&&const DeepCollectionEquality().equals(other.allowedTo, allowedTo)&&const DeepCollectionEquality().equals(other.reactions, reactions)&&const DeepCollectionEquality().equals(other.replies, replies)&&(identical(other.privacy, privacy) || other.privacy == privacy)&&(identical(other.caption, caption) || other.caption == caption)&&(identical(other.location, location) || other.location == location)&&const DeepCollectionEquality().equals(other.hashtags, hashtags)&&const DeepCollectionEquality().equals(other.mentions, mentions)&&(identical(other.isHighlighted, isHighlighted) || other.isHighlighted == isHighlighted)&&(identical(other.isArchived, isArchived) || other.isArchived == isArchived)&&(identical(other.musicUrl, musicUrl) || other.musicUrl == musicUrl)&&(identical(other.musicTitle, musicTitle) || other.musicTitle == musicTitle)&&(identical(other.storyType, storyType) || other.storyType == storyType)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,userId,userName,userAvatarUrl,mediaUrl,mediaType,createdAt,expiresAt,const DeepCollectionEquality().hash(viewers),const DeepCollectionEquality().hash(allowedTo),const DeepCollectionEquality().hash(reactions),const DeepCollectionEquality().hash(replies),privacy,caption,location,const DeepCollectionEquality().hash(hashtags),const DeepCollectionEquality().hash(mentions),isHighlighted,isArchived,musicUrl,musicTitle,storyType,const DeepCollectionEquality().hash(metadata)]);

@override
String toString() {
  return 'Story(id: $id, userId: $userId, userName: $userName, userAvatarUrl: $userAvatarUrl, mediaUrl: $mediaUrl, mediaType: $mediaType, createdAt: $createdAt, expiresAt: $expiresAt, viewers: $viewers, allowedTo: $allowedTo, reactions: $reactions, replies: $replies, privacy: $privacy, caption: $caption, location: $location, hashtags: $hashtags, mentions: $mentions, isHighlighted: $isHighlighted, isArchived: $isArchived, musicUrl: $musicUrl, musicTitle: $musicTitle, storyType: $storyType, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $StoryCopyWith<$Res>  {
  factory $StoryCopyWith(Story value, $Res Function(Story) _then) = _$StoryCopyWithImpl;
@useResult
$Res call({
 String id, String userId, String userName, String userAvatarUrl, String mediaUrl, StoryMediaType mediaType, DateTime createdAt, DateTime expiresAt, List<String> viewers, List<String> allowedTo, List<StoryReaction> reactions, List<StoryReply> replies, StoryPrivacy privacy, String? caption, String? location, List<String> hashtags, List<String> mentions, bool isHighlighted, bool isArchived, String? musicUrl, String? musicTitle, StoryType storyType, Map<String, dynamic>? metadata
});




}
/// @nodoc
class _$StoryCopyWithImpl<$Res>
    implements $StoryCopyWith<$Res> {
  _$StoryCopyWithImpl(this._self, this._then);

  final Story _self;
  final $Res Function(Story) _then;

/// Create a copy of Story
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? userName = null,Object? userAvatarUrl = null,Object? mediaUrl = null,Object? mediaType = null,Object? createdAt = null,Object? expiresAt = null,Object? viewers = null,Object? allowedTo = null,Object? reactions = null,Object? replies = null,Object? privacy = null,Object? caption = freezed,Object? location = freezed,Object? hashtags = null,Object? mentions = null,Object? isHighlighted = null,Object? isArchived = null,Object? musicUrl = freezed,Object? musicTitle = freezed,Object? storyType = null,Object? metadata = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,userName: null == userName ? _self.userName : userName // ignore: cast_nullable_to_non_nullable
as String,userAvatarUrl: null == userAvatarUrl ? _self.userAvatarUrl : userAvatarUrl // ignore: cast_nullable_to_non_nullable
as String,mediaUrl: null == mediaUrl ? _self.mediaUrl : mediaUrl // ignore: cast_nullable_to_non_nullable
as String,mediaType: null == mediaType ? _self.mediaType : mediaType // ignore: cast_nullable_to_non_nullable
as StoryMediaType,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,expiresAt: null == expiresAt ? _self.expiresAt : expiresAt // ignore: cast_nullable_to_non_nullable
as DateTime,viewers: null == viewers ? _self.viewers : viewers // ignore: cast_nullable_to_non_nullable
as List<String>,allowedTo: null == allowedTo ? _self.allowedTo : allowedTo // ignore: cast_nullable_to_non_nullable
as List<String>,reactions: null == reactions ? _self.reactions : reactions // ignore: cast_nullable_to_non_nullable
as List<StoryReaction>,replies: null == replies ? _self.replies : replies // ignore: cast_nullable_to_non_nullable
as List<StoryReply>,privacy: null == privacy ? _self.privacy : privacy // ignore: cast_nullable_to_non_nullable
as StoryPrivacy,caption: freezed == caption ? _self.caption : caption // ignore: cast_nullable_to_non_nullable
as String?,location: freezed == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String?,hashtags: null == hashtags ? _self.hashtags : hashtags // ignore: cast_nullable_to_non_nullable
as List<String>,mentions: null == mentions ? _self.mentions : mentions // ignore: cast_nullable_to_non_nullable
as List<String>,isHighlighted: null == isHighlighted ? _self.isHighlighted : isHighlighted // ignore: cast_nullable_to_non_nullable
as bool,isArchived: null == isArchived ? _self.isArchived : isArchived // ignore: cast_nullable_to_non_nullable
as bool,musicUrl: freezed == musicUrl ? _self.musicUrl : musicUrl // ignore: cast_nullable_to_non_nullable
as String?,musicTitle: freezed == musicTitle ? _self.musicTitle : musicTitle // ignore: cast_nullable_to_non_nullable
as String?,storyType: null == storyType ? _self.storyType : storyType // ignore: cast_nullable_to_non_nullable
as StoryType,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [Story].
extension StoryPatterns on Story {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Story value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Story() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Story value)  $default,){
final _that = this;
switch (_that) {
case _Story():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Story value)?  $default,){
final _that = this;
switch (_that) {
case _Story() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  String userName,  String userAvatarUrl,  String mediaUrl,  StoryMediaType mediaType,  DateTime createdAt,  DateTime expiresAt,  List<String> viewers,  List<String> allowedTo,  List<StoryReaction> reactions,  List<StoryReply> replies,  StoryPrivacy privacy,  String? caption,  String? location,  List<String> hashtags,  List<String> mentions,  bool isHighlighted,  bool isArchived,  String? musicUrl,  String? musicTitle,  StoryType storyType,  Map<String, dynamic>? metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Story() when $default != null:
return $default(_that.id,_that.userId,_that.userName,_that.userAvatarUrl,_that.mediaUrl,_that.mediaType,_that.createdAt,_that.expiresAt,_that.viewers,_that.allowedTo,_that.reactions,_that.replies,_that.privacy,_that.caption,_that.location,_that.hashtags,_that.mentions,_that.isHighlighted,_that.isArchived,_that.musicUrl,_that.musicTitle,_that.storyType,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  String userName,  String userAvatarUrl,  String mediaUrl,  StoryMediaType mediaType,  DateTime createdAt,  DateTime expiresAt,  List<String> viewers,  List<String> allowedTo,  List<StoryReaction> reactions,  List<StoryReply> replies,  StoryPrivacy privacy,  String? caption,  String? location,  List<String> hashtags,  List<String> mentions,  bool isHighlighted,  bool isArchived,  String? musicUrl,  String? musicTitle,  StoryType storyType,  Map<String, dynamic>? metadata)  $default,) {final _that = this;
switch (_that) {
case _Story():
return $default(_that.id,_that.userId,_that.userName,_that.userAvatarUrl,_that.mediaUrl,_that.mediaType,_that.createdAt,_that.expiresAt,_that.viewers,_that.allowedTo,_that.reactions,_that.replies,_that.privacy,_that.caption,_that.location,_that.hashtags,_that.mentions,_that.isHighlighted,_that.isArchived,_that.musicUrl,_that.musicTitle,_that.storyType,_that.metadata);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  String userName,  String userAvatarUrl,  String mediaUrl,  StoryMediaType mediaType,  DateTime createdAt,  DateTime expiresAt,  List<String> viewers,  List<String> allowedTo,  List<StoryReaction> reactions,  List<StoryReply> replies,  StoryPrivacy privacy,  String? caption,  String? location,  List<String> hashtags,  List<String> mentions,  bool isHighlighted,  bool isArchived,  String? musicUrl,  String? musicTitle,  StoryType storyType,  Map<String, dynamic>? metadata)?  $default,) {final _that = this;
switch (_that) {
case _Story() when $default != null:
return $default(_that.id,_that.userId,_that.userName,_that.userAvatarUrl,_that.mediaUrl,_that.mediaType,_that.createdAt,_that.expiresAt,_that.viewers,_that.allowedTo,_that.reactions,_that.replies,_that.privacy,_that.caption,_that.location,_that.hashtags,_that.mentions,_that.isHighlighted,_that.isArchived,_that.musicUrl,_that.musicTitle,_that.storyType,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Story implements Story {
  const _Story({required this.id, required this.userId, required this.userName, required this.userAvatarUrl, required this.mediaUrl, required this.mediaType, required this.createdAt, required this.expiresAt, final  List<String> viewers = const [], final  List<String> allowedTo = const [], final  List<StoryReaction> reactions = const [], final  List<StoryReply> replies = const [], this.privacy = StoryPrivacy.public, this.caption, this.location, final  List<String> hashtags = const [], final  List<String> mentions = const [], this.isHighlighted = false, this.isArchived = false, this.musicUrl, this.musicTitle, this.storyType = StoryType.regular, final  Map<String, dynamic>? metadata}): _viewers = viewers,_allowedTo = allowedTo,_reactions = reactions,_replies = replies,_hashtags = hashtags,_mentions = mentions,_metadata = metadata;
  factory _Story.fromJson(Map<String, dynamic> json) => _$StoryFromJson(json);

@override final  String id;
@override final  String userId;
@override final  String userName;
@override final  String userAvatarUrl;
@override final  String mediaUrl;
@override final  StoryMediaType mediaType;
@override final  DateTime createdAt;
@override final  DateTime expiresAt;
 final  List<String> _viewers;
@override@JsonKey() List<String> get viewers {
  if (_viewers is EqualUnmodifiableListView) return _viewers;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_viewers);
}

 final  List<String> _allowedTo;
@override@JsonKey() List<String> get allowedTo {
  if (_allowedTo is EqualUnmodifiableListView) return _allowedTo;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_allowedTo);
}

 final  List<StoryReaction> _reactions;
@override@JsonKey() List<StoryReaction> get reactions {
  if (_reactions is EqualUnmodifiableListView) return _reactions;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_reactions);
}

 final  List<StoryReply> _replies;
@override@JsonKey() List<StoryReply> get replies {
  if (_replies is EqualUnmodifiableListView) return _replies;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_replies);
}

@override@JsonKey() final  StoryPrivacy privacy;
@override final  String? caption;
@override final  String? location;
 final  List<String> _hashtags;
@override@JsonKey() List<String> get hashtags {
  if (_hashtags is EqualUnmodifiableListView) return _hashtags;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_hashtags);
}

 final  List<String> _mentions;
@override@JsonKey() List<String> get mentions {
  if (_mentions is EqualUnmodifiableListView) return _mentions;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_mentions);
}

@override@JsonKey() final  bool isHighlighted;
@override@JsonKey() final  bool isArchived;
@override final  String? musicUrl;
@override final  String? musicTitle;
@override@JsonKey() final  StoryType storyType;
 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of Story
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StoryCopyWith<_Story> get copyWith => __$StoryCopyWithImpl<_Story>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$StoryToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Story&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.userName, userName) || other.userName == userName)&&(identical(other.userAvatarUrl, userAvatarUrl) || other.userAvatarUrl == userAvatarUrl)&&(identical(other.mediaUrl, mediaUrl) || other.mediaUrl == mediaUrl)&&(identical(other.mediaType, mediaType) || other.mediaType == mediaType)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.expiresAt, expiresAt) || other.expiresAt == expiresAt)&&const DeepCollectionEquality().equals(other._viewers, _viewers)&&const DeepCollectionEquality().equals(other._allowedTo, _allowedTo)&&const DeepCollectionEquality().equals(other._reactions, _reactions)&&const DeepCollectionEquality().equals(other._replies, _replies)&&(identical(other.privacy, privacy) || other.privacy == privacy)&&(identical(other.caption, caption) || other.caption == caption)&&(identical(other.location, location) || other.location == location)&&const DeepCollectionEquality().equals(other._hashtags, _hashtags)&&const DeepCollectionEquality().equals(other._mentions, _mentions)&&(identical(other.isHighlighted, isHighlighted) || other.isHighlighted == isHighlighted)&&(identical(other.isArchived, isArchived) || other.isArchived == isArchived)&&(identical(other.musicUrl, musicUrl) || other.musicUrl == musicUrl)&&(identical(other.musicTitle, musicTitle) || other.musicTitle == musicTitle)&&(identical(other.storyType, storyType) || other.storyType == storyType)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,userId,userName,userAvatarUrl,mediaUrl,mediaType,createdAt,expiresAt,const DeepCollectionEquality().hash(_viewers),const DeepCollectionEquality().hash(_allowedTo),const DeepCollectionEquality().hash(_reactions),const DeepCollectionEquality().hash(_replies),privacy,caption,location,const DeepCollectionEquality().hash(_hashtags),const DeepCollectionEquality().hash(_mentions),isHighlighted,isArchived,musicUrl,musicTitle,storyType,const DeepCollectionEquality().hash(_metadata)]);

@override
String toString() {
  return 'Story(id: $id, userId: $userId, userName: $userName, userAvatarUrl: $userAvatarUrl, mediaUrl: $mediaUrl, mediaType: $mediaType, createdAt: $createdAt, expiresAt: $expiresAt, viewers: $viewers, allowedTo: $allowedTo, reactions: $reactions, replies: $replies, privacy: $privacy, caption: $caption, location: $location, hashtags: $hashtags, mentions: $mentions, isHighlighted: $isHighlighted, isArchived: $isArchived, musicUrl: $musicUrl, musicTitle: $musicTitle, storyType: $storyType, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$StoryCopyWith<$Res> implements $StoryCopyWith<$Res> {
  factory _$StoryCopyWith(_Story value, $Res Function(_Story) _then) = __$StoryCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, String userName, String userAvatarUrl, String mediaUrl, StoryMediaType mediaType, DateTime createdAt, DateTime expiresAt, List<String> viewers, List<String> allowedTo, List<StoryReaction> reactions, List<StoryReply> replies, StoryPrivacy privacy, String? caption, String? location, List<String> hashtags, List<String> mentions, bool isHighlighted, bool isArchived, String? musicUrl, String? musicTitle, StoryType storyType, Map<String, dynamic>? metadata
});




}
/// @nodoc
class __$StoryCopyWithImpl<$Res>
    implements _$StoryCopyWith<$Res> {
  __$StoryCopyWithImpl(this._self, this._then);

  final _Story _self;
  final $Res Function(_Story) _then;

/// Create a copy of Story
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? userName = null,Object? userAvatarUrl = null,Object? mediaUrl = null,Object? mediaType = null,Object? createdAt = null,Object? expiresAt = null,Object? viewers = null,Object? allowedTo = null,Object? reactions = null,Object? replies = null,Object? privacy = null,Object? caption = freezed,Object? location = freezed,Object? hashtags = null,Object? mentions = null,Object? isHighlighted = null,Object? isArchived = null,Object? musicUrl = freezed,Object? musicTitle = freezed,Object? storyType = null,Object? metadata = freezed,}) {
  return _then(_Story(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,userName: null == userName ? _self.userName : userName // ignore: cast_nullable_to_non_nullable
as String,userAvatarUrl: null == userAvatarUrl ? _self.userAvatarUrl : userAvatarUrl // ignore: cast_nullable_to_non_nullable
as String,mediaUrl: null == mediaUrl ? _self.mediaUrl : mediaUrl // ignore: cast_nullable_to_non_nullable
as String,mediaType: null == mediaType ? _self.mediaType : mediaType // ignore: cast_nullable_to_non_nullable
as StoryMediaType,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,expiresAt: null == expiresAt ? _self.expiresAt : expiresAt // ignore: cast_nullable_to_non_nullable
as DateTime,viewers: null == viewers ? _self._viewers : viewers // ignore: cast_nullable_to_non_nullable
as List<String>,allowedTo: null == allowedTo ? _self._allowedTo : allowedTo // ignore: cast_nullable_to_non_nullable
as List<String>,reactions: null == reactions ? _self._reactions : reactions // ignore: cast_nullable_to_non_nullable
as List<StoryReaction>,replies: null == replies ? _self._replies : replies // ignore: cast_nullable_to_non_nullable
as List<StoryReply>,privacy: null == privacy ? _self.privacy : privacy // ignore: cast_nullable_to_non_nullable
as StoryPrivacy,caption: freezed == caption ? _self.caption : caption // ignore: cast_nullable_to_non_nullable
as String?,location: freezed == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String?,hashtags: null == hashtags ? _self._hashtags : hashtags // ignore: cast_nullable_to_non_nullable
as List<String>,mentions: null == mentions ? _self._mentions : mentions // ignore: cast_nullable_to_non_nullable
as List<String>,isHighlighted: null == isHighlighted ? _self.isHighlighted : isHighlighted // ignore: cast_nullable_to_non_nullable
as bool,isArchived: null == isArchived ? _self.isArchived : isArchived // ignore: cast_nullable_to_non_nullable
as bool,musicUrl: freezed == musicUrl ? _self.musicUrl : musicUrl // ignore: cast_nullable_to_non_nullable
as String?,musicTitle: freezed == musicTitle ? _self.musicTitle : musicTitle // ignore: cast_nullable_to_non_nullable
as String?,storyType: null == storyType ? _self.storyType : storyType // ignore: cast_nullable_to_non_nullable
as StoryType,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}


/// @nodoc
mixin _$StoryReaction {

 String get userId; String get userName; String get userAvatarUrl; String get reactionType; DateTime get timestamp;
/// Create a copy of StoryReaction
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StoryReactionCopyWith<StoryReaction> get copyWith => _$StoryReactionCopyWithImpl<StoryReaction>(this as StoryReaction, _$identity);

  /// Serializes this StoryReaction to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StoryReaction&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.userName, userName) || other.userName == userName)&&(identical(other.userAvatarUrl, userAvatarUrl) || other.userAvatarUrl == userAvatarUrl)&&(identical(other.reactionType, reactionType) || other.reactionType == reactionType)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,userId,userName,userAvatarUrl,reactionType,timestamp);

@override
String toString() {
  return 'StoryReaction(userId: $userId, userName: $userName, userAvatarUrl: $userAvatarUrl, reactionType: $reactionType, timestamp: $timestamp)';
}


}

/// @nodoc
abstract mixin class $StoryReactionCopyWith<$Res>  {
  factory $StoryReactionCopyWith(StoryReaction value, $Res Function(StoryReaction) _then) = _$StoryReactionCopyWithImpl;
@useResult
$Res call({
 String userId, String userName, String userAvatarUrl, String reactionType, DateTime timestamp
});




}
/// @nodoc
class _$StoryReactionCopyWithImpl<$Res>
    implements $StoryReactionCopyWith<$Res> {
  _$StoryReactionCopyWithImpl(this._self, this._then);

  final StoryReaction _self;
  final $Res Function(StoryReaction) _then;

/// Create a copy of StoryReaction
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? userId = null,Object? userName = null,Object? userAvatarUrl = null,Object? reactionType = null,Object? timestamp = null,}) {
  return _then(_self.copyWith(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,userName: null == userName ? _self.userName : userName // ignore: cast_nullable_to_non_nullable
as String,userAvatarUrl: null == userAvatarUrl ? _self.userAvatarUrl : userAvatarUrl // ignore: cast_nullable_to_non_nullable
as String,reactionType: null == reactionType ? _self.reactionType : reactionType // ignore: cast_nullable_to_non_nullable
as String,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// Adds pattern-matching-related methods to [StoryReaction].
extension StoryReactionPatterns on StoryReaction {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _StoryReaction value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _StoryReaction() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _StoryReaction value)  $default,){
final _that = this;
switch (_that) {
case _StoryReaction():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _StoryReaction value)?  $default,){
final _that = this;
switch (_that) {
case _StoryReaction() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String userId,  String userName,  String userAvatarUrl,  String reactionType,  DateTime timestamp)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _StoryReaction() when $default != null:
return $default(_that.userId,_that.userName,_that.userAvatarUrl,_that.reactionType,_that.timestamp);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String userId,  String userName,  String userAvatarUrl,  String reactionType,  DateTime timestamp)  $default,) {final _that = this;
switch (_that) {
case _StoryReaction():
return $default(_that.userId,_that.userName,_that.userAvatarUrl,_that.reactionType,_that.timestamp);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String userId,  String userName,  String userAvatarUrl,  String reactionType,  DateTime timestamp)?  $default,) {final _that = this;
switch (_that) {
case _StoryReaction() when $default != null:
return $default(_that.userId,_that.userName,_that.userAvatarUrl,_that.reactionType,_that.timestamp);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _StoryReaction implements StoryReaction {
  const _StoryReaction({required this.userId, required this.userName, required this.userAvatarUrl, required this.reactionType, required this.timestamp});
  factory _StoryReaction.fromJson(Map<String, dynamic> json) => _$StoryReactionFromJson(json);

@override final  String userId;
@override final  String userName;
@override final  String userAvatarUrl;
@override final  String reactionType;
@override final  DateTime timestamp;

/// Create a copy of StoryReaction
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StoryReactionCopyWith<_StoryReaction> get copyWith => __$StoryReactionCopyWithImpl<_StoryReaction>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$StoryReactionToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StoryReaction&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.userName, userName) || other.userName == userName)&&(identical(other.userAvatarUrl, userAvatarUrl) || other.userAvatarUrl == userAvatarUrl)&&(identical(other.reactionType, reactionType) || other.reactionType == reactionType)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,userId,userName,userAvatarUrl,reactionType,timestamp);

@override
String toString() {
  return 'StoryReaction(userId: $userId, userName: $userName, userAvatarUrl: $userAvatarUrl, reactionType: $reactionType, timestamp: $timestamp)';
}


}

/// @nodoc
abstract mixin class _$StoryReactionCopyWith<$Res> implements $StoryReactionCopyWith<$Res> {
  factory _$StoryReactionCopyWith(_StoryReaction value, $Res Function(_StoryReaction) _then) = __$StoryReactionCopyWithImpl;
@override @useResult
$Res call({
 String userId, String userName, String userAvatarUrl, String reactionType, DateTime timestamp
});




}
/// @nodoc
class __$StoryReactionCopyWithImpl<$Res>
    implements _$StoryReactionCopyWith<$Res> {
  __$StoryReactionCopyWithImpl(this._self, this._then);

  final _StoryReaction _self;
  final $Res Function(_StoryReaction) _then;

/// Create a copy of StoryReaction
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? userId = null,Object? userName = null,Object? userAvatarUrl = null,Object? reactionType = null,Object? timestamp = null,}) {
  return _then(_StoryReaction(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,userName: null == userName ? _self.userName : userName // ignore: cast_nullable_to_non_nullable
as String,userAvatarUrl: null == userAvatarUrl ? _self.userAvatarUrl : userAvatarUrl // ignore: cast_nullable_to_non_nullable
as String,reactionType: null == reactionType ? _self.reactionType : reactionType // ignore: cast_nullable_to_non_nullable
as String,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}


/// @nodoc
mixin _$StoryReply {

 String get id; String get userId; String get userName; String get userAvatarUrl; String get message; DateTime get timestamp; bool get isRead;
/// Create a copy of StoryReply
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StoryReplyCopyWith<StoryReply> get copyWith => _$StoryReplyCopyWithImpl<StoryReply>(this as StoryReply, _$identity);

  /// Serializes this StoryReply to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StoryReply&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.userName, userName) || other.userName == userName)&&(identical(other.userAvatarUrl, userAvatarUrl) || other.userAvatarUrl == userAvatarUrl)&&(identical(other.message, message) || other.message == message)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.isRead, isRead) || other.isRead == isRead));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,userName,userAvatarUrl,message,timestamp,isRead);

@override
String toString() {
  return 'StoryReply(id: $id, userId: $userId, userName: $userName, userAvatarUrl: $userAvatarUrl, message: $message, timestamp: $timestamp, isRead: $isRead)';
}


}

/// @nodoc
abstract mixin class $StoryReplyCopyWith<$Res>  {
  factory $StoryReplyCopyWith(StoryReply value, $Res Function(StoryReply) _then) = _$StoryReplyCopyWithImpl;
@useResult
$Res call({
 String id, String userId, String userName, String userAvatarUrl, String message, DateTime timestamp, bool isRead
});




}
/// @nodoc
class _$StoryReplyCopyWithImpl<$Res>
    implements $StoryReplyCopyWith<$Res> {
  _$StoryReplyCopyWithImpl(this._self, this._then);

  final StoryReply _self;
  final $Res Function(StoryReply) _then;

/// Create a copy of StoryReply
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? userName = null,Object? userAvatarUrl = null,Object? message = null,Object? timestamp = null,Object? isRead = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,userName: null == userName ? _self.userName : userName // ignore: cast_nullable_to_non_nullable
as String,userAvatarUrl: null == userAvatarUrl ? _self.userAvatarUrl : userAvatarUrl // ignore: cast_nullable_to_non_nullable
as String,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,isRead: null == isRead ? _self.isRead : isRead // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [StoryReply].
extension StoryReplyPatterns on StoryReply {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _StoryReply value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _StoryReply() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _StoryReply value)  $default,){
final _that = this;
switch (_that) {
case _StoryReply():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _StoryReply value)?  $default,){
final _that = this;
switch (_that) {
case _StoryReply() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  String userName,  String userAvatarUrl,  String message,  DateTime timestamp,  bool isRead)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _StoryReply() when $default != null:
return $default(_that.id,_that.userId,_that.userName,_that.userAvatarUrl,_that.message,_that.timestamp,_that.isRead);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  String userName,  String userAvatarUrl,  String message,  DateTime timestamp,  bool isRead)  $default,) {final _that = this;
switch (_that) {
case _StoryReply():
return $default(_that.id,_that.userId,_that.userName,_that.userAvatarUrl,_that.message,_that.timestamp,_that.isRead);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  String userName,  String userAvatarUrl,  String message,  DateTime timestamp,  bool isRead)?  $default,) {final _that = this;
switch (_that) {
case _StoryReply() when $default != null:
return $default(_that.id,_that.userId,_that.userName,_that.userAvatarUrl,_that.message,_that.timestamp,_that.isRead);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _StoryReply implements StoryReply {
  const _StoryReply({required this.id, required this.userId, required this.userName, required this.userAvatarUrl, required this.message, required this.timestamp, this.isRead = false});
  factory _StoryReply.fromJson(Map<String, dynamic> json) => _$StoryReplyFromJson(json);

@override final  String id;
@override final  String userId;
@override final  String userName;
@override final  String userAvatarUrl;
@override final  String message;
@override final  DateTime timestamp;
@override@JsonKey() final  bool isRead;

/// Create a copy of StoryReply
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StoryReplyCopyWith<_StoryReply> get copyWith => __$StoryReplyCopyWithImpl<_StoryReply>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$StoryReplyToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StoryReply&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.userName, userName) || other.userName == userName)&&(identical(other.userAvatarUrl, userAvatarUrl) || other.userAvatarUrl == userAvatarUrl)&&(identical(other.message, message) || other.message == message)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.isRead, isRead) || other.isRead == isRead));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,userName,userAvatarUrl,message,timestamp,isRead);

@override
String toString() {
  return 'StoryReply(id: $id, userId: $userId, userName: $userName, userAvatarUrl: $userAvatarUrl, message: $message, timestamp: $timestamp, isRead: $isRead)';
}


}

/// @nodoc
abstract mixin class _$StoryReplyCopyWith<$Res> implements $StoryReplyCopyWith<$Res> {
  factory _$StoryReplyCopyWith(_StoryReply value, $Res Function(_StoryReply) _then) = __$StoryReplyCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, String userName, String userAvatarUrl, String message, DateTime timestamp, bool isRead
});




}
/// @nodoc
class __$StoryReplyCopyWithImpl<$Res>
    implements _$StoryReplyCopyWith<$Res> {
  __$StoryReplyCopyWithImpl(this._self, this._then);

  final _StoryReply _self;
  final $Res Function(_StoryReply) _then;

/// Create a copy of StoryReply
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? userName = null,Object? userAvatarUrl = null,Object? message = null,Object? timestamp = null,Object? isRead = null,}) {
  return _then(_StoryReply(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,userName: null == userName ? _self.userName : userName // ignore: cast_nullable_to_non_nullable
as String,userAvatarUrl: null == userAvatarUrl ? _self.userAvatarUrl : userAvatarUrl // ignore: cast_nullable_to_non_nullable
as String,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,isRead: null == isRead ? _self.isRead : isRead // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$StoryStats {

 int get viewCount; int get replyCount; int get reactionCount; int get shareCount; double get completionRate; double get skipRate; List<String> get topViewers; Map<String, int> get reactionBreakdown;
/// Create a copy of StoryStats
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StoryStatsCopyWith<StoryStats> get copyWith => _$StoryStatsCopyWithImpl<StoryStats>(this as StoryStats, _$identity);

  /// Serializes this StoryStats to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StoryStats&&(identical(other.viewCount, viewCount) || other.viewCount == viewCount)&&(identical(other.replyCount, replyCount) || other.replyCount == replyCount)&&(identical(other.reactionCount, reactionCount) || other.reactionCount == reactionCount)&&(identical(other.shareCount, shareCount) || other.shareCount == shareCount)&&(identical(other.completionRate, completionRate) || other.completionRate == completionRate)&&(identical(other.skipRate, skipRate) || other.skipRate == skipRate)&&const DeepCollectionEquality().equals(other.topViewers, topViewers)&&const DeepCollectionEquality().equals(other.reactionBreakdown, reactionBreakdown));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,viewCount,replyCount,reactionCount,shareCount,completionRate,skipRate,const DeepCollectionEquality().hash(topViewers),const DeepCollectionEquality().hash(reactionBreakdown));

@override
String toString() {
  return 'StoryStats(viewCount: $viewCount, replyCount: $replyCount, reactionCount: $reactionCount, shareCount: $shareCount, completionRate: $completionRate, skipRate: $skipRate, topViewers: $topViewers, reactionBreakdown: $reactionBreakdown)';
}


}

/// @nodoc
abstract mixin class $StoryStatsCopyWith<$Res>  {
  factory $StoryStatsCopyWith(StoryStats value, $Res Function(StoryStats) _then) = _$StoryStatsCopyWithImpl;
@useResult
$Res call({
 int viewCount, int replyCount, int reactionCount, int shareCount, double completionRate, double skipRate, List<String> topViewers, Map<String, int> reactionBreakdown
});




}
/// @nodoc
class _$StoryStatsCopyWithImpl<$Res>
    implements $StoryStatsCopyWith<$Res> {
  _$StoryStatsCopyWithImpl(this._self, this._then);

  final StoryStats _self;
  final $Res Function(StoryStats) _then;

/// Create a copy of StoryStats
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? viewCount = null,Object? replyCount = null,Object? reactionCount = null,Object? shareCount = null,Object? completionRate = null,Object? skipRate = null,Object? topViewers = null,Object? reactionBreakdown = null,}) {
  return _then(_self.copyWith(
viewCount: null == viewCount ? _self.viewCount : viewCount // ignore: cast_nullable_to_non_nullable
as int,replyCount: null == replyCount ? _self.replyCount : replyCount // ignore: cast_nullable_to_non_nullable
as int,reactionCount: null == reactionCount ? _self.reactionCount : reactionCount // ignore: cast_nullable_to_non_nullable
as int,shareCount: null == shareCount ? _self.shareCount : shareCount // ignore: cast_nullable_to_non_nullable
as int,completionRate: null == completionRate ? _self.completionRate : completionRate // ignore: cast_nullable_to_non_nullable
as double,skipRate: null == skipRate ? _self.skipRate : skipRate // ignore: cast_nullable_to_non_nullable
as double,topViewers: null == topViewers ? _self.topViewers : topViewers // ignore: cast_nullable_to_non_nullable
as List<String>,reactionBreakdown: null == reactionBreakdown ? _self.reactionBreakdown : reactionBreakdown // ignore: cast_nullable_to_non_nullable
as Map<String, int>,
  ));
}

}


/// Adds pattern-matching-related methods to [StoryStats].
extension StoryStatsPatterns on StoryStats {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _StoryStats value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _StoryStats() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _StoryStats value)  $default,){
final _that = this;
switch (_that) {
case _StoryStats():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _StoryStats value)?  $default,){
final _that = this;
switch (_that) {
case _StoryStats() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int viewCount,  int replyCount,  int reactionCount,  int shareCount,  double completionRate,  double skipRate,  List<String> topViewers,  Map<String, int> reactionBreakdown)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _StoryStats() when $default != null:
return $default(_that.viewCount,_that.replyCount,_that.reactionCount,_that.shareCount,_that.completionRate,_that.skipRate,_that.topViewers,_that.reactionBreakdown);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int viewCount,  int replyCount,  int reactionCount,  int shareCount,  double completionRate,  double skipRate,  List<String> topViewers,  Map<String, int> reactionBreakdown)  $default,) {final _that = this;
switch (_that) {
case _StoryStats():
return $default(_that.viewCount,_that.replyCount,_that.reactionCount,_that.shareCount,_that.completionRate,_that.skipRate,_that.topViewers,_that.reactionBreakdown);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int viewCount,  int replyCount,  int reactionCount,  int shareCount,  double completionRate,  double skipRate,  List<String> topViewers,  Map<String, int> reactionBreakdown)?  $default,) {final _that = this;
switch (_that) {
case _StoryStats() when $default != null:
return $default(_that.viewCount,_that.replyCount,_that.reactionCount,_that.shareCount,_that.completionRate,_that.skipRate,_that.topViewers,_that.reactionBreakdown);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _StoryStats implements StoryStats {
  const _StoryStats({required this.viewCount, required this.replyCount, required this.reactionCount, required this.shareCount, required this.completionRate, required this.skipRate, required final  List<String> topViewers, required final  Map<String, int> reactionBreakdown}): _topViewers = topViewers,_reactionBreakdown = reactionBreakdown;
  factory _StoryStats.fromJson(Map<String, dynamic> json) => _$StoryStatsFromJson(json);

@override final  int viewCount;
@override final  int replyCount;
@override final  int reactionCount;
@override final  int shareCount;
@override final  double completionRate;
@override final  double skipRate;
 final  List<String> _topViewers;
@override List<String> get topViewers {
  if (_topViewers is EqualUnmodifiableListView) return _topViewers;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_topViewers);
}

 final  Map<String, int> _reactionBreakdown;
@override Map<String, int> get reactionBreakdown {
  if (_reactionBreakdown is EqualUnmodifiableMapView) return _reactionBreakdown;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_reactionBreakdown);
}


/// Create a copy of StoryStats
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StoryStatsCopyWith<_StoryStats> get copyWith => __$StoryStatsCopyWithImpl<_StoryStats>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$StoryStatsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StoryStats&&(identical(other.viewCount, viewCount) || other.viewCount == viewCount)&&(identical(other.replyCount, replyCount) || other.replyCount == replyCount)&&(identical(other.reactionCount, reactionCount) || other.reactionCount == reactionCount)&&(identical(other.shareCount, shareCount) || other.shareCount == shareCount)&&(identical(other.completionRate, completionRate) || other.completionRate == completionRate)&&(identical(other.skipRate, skipRate) || other.skipRate == skipRate)&&const DeepCollectionEquality().equals(other._topViewers, _topViewers)&&const DeepCollectionEquality().equals(other._reactionBreakdown, _reactionBreakdown));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,viewCount,replyCount,reactionCount,shareCount,completionRate,skipRate,const DeepCollectionEquality().hash(_topViewers),const DeepCollectionEquality().hash(_reactionBreakdown));

@override
String toString() {
  return 'StoryStats(viewCount: $viewCount, replyCount: $replyCount, reactionCount: $reactionCount, shareCount: $shareCount, completionRate: $completionRate, skipRate: $skipRate, topViewers: $topViewers, reactionBreakdown: $reactionBreakdown)';
}


}

/// @nodoc
abstract mixin class _$StoryStatsCopyWith<$Res> implements $StoryStatsCopyWith<$Res> {
  factory _$StoryStatsCopyWith(_StoryStats value, $Res Function(_StoryStats) _then) = __$StoryStatsCopyWithImpl;
@override @useResult
$Res call({
 int viewCount, int replyCount, int reactionCount, int shareCount, double completionRate, double skipRate, List<String> topViewers, Map<String, int> reactionBreakdown
});




}
/// @nodoc
class __$StoryStatsCopyWithImpl<$Res>
    implements _$StoryStatsCopyWith<$Res> {
  __$StoryStatsCopyWithImpl(this._self, this._then);

  final _StoryStats _self;
  final $Res Function(_StoryStats) _then;

/// Create a copy of StoryStats
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? viewCount = null,Object? replyCount = null,Object? reactionCount = null,Object? shareCount = null,Object? completionRate = null,Object? skipRate = null,Object? topViewers = null,Object? reactionBreakdown = null,}) {
  return _then(_StoryStats(
viewCount: null == viewCount ? _self.viewCount : viewCount // ignore: cast_nullable_to_non_nullable
as int,replyCount: null == replyCount ? _self.replyCount : replyCount // ignore: cast_nullable_to_non_nullable
as int,reactionCount: null == reactionCount ? _self.reactionCount : reactionCount // ignore: cast_nullable_to_non_nullable
as int,shareCount: null == shareCount ? _self.shareCount : shareCount // ignore: cast_nullable_to_non_nullable
as int,completionRate: null == completionRate ? _self.completionRate : completionRate // ignore: cast_nullable_to_non_nullable
as double,skipRate: null == skipRate ? _self.skipRate : skipRate // ignore: cast_nullable_to_non_nullable
as double,topViewers: null == topViewers ? _self._topViewers : topViewers // ignore: cast_nullable_to_non_nullable
as List<String>,reactionBreakdown: null == reactionBreakdown ? _self._reactionBreakdown : reactionBreakdown // ignore: cast_nullable_to_non_nullable
as Map<String, int>,
  ));
}


}

// dart format on
