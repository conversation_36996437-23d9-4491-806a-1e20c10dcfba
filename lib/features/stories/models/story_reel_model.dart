import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/features/stories/models/shared/story_shared_models.dart';

part 'story_reel_model.freezed.dart';
part 'story_reel_model.g.dart';

@freezed
abstract class StoryTag with _$StoryTag {
  const factory StoryTag({
    required String userId,
    required String username,
    String? name,
    required double x,
    required double y,
  }) = _StoryTag;

  factory StoryTag.fromJson(Map<String, dynamic> json) =>
      _$StoryTagFromJson(json);

  // Manual toJson for use in editors
  @override
  Map<String, dynamic> toJson() => {
    'userId': userId,
    'username': username,
    'name': name,
    'x': x,
    'y': y,
  };
}

@freezed
abstract class StoryReel with _$StoryReel {
  const factory StoryReel({
    required String id,
    required String userId,
    required String username,
    required String userAvatarUrl,
    required List<StoryItem> stories,
    @Default(false) bool isAllViewed,
    @Default(false) bool isCloseFriend,
  }) = _StoryReel;

  factory StoryReel.fromJson(Map<String, dynamic> json) =>
      _$StoryReelFromJson(json);
}

@freezed
abstract class StoryItem with _$StoryItem {
  const factory StoryItem({
    required String id,
    required String userId,
    required String mediaUrl,
    required MediaType mediaType,
    required Duration duration,
    required DateTime timestamp,
    // Story metadata
    String? textOverlay,
    int? textColor,
    double? textSize,
    Map<String, double>? textPosition,
    int? backgroundColor,
    String? filter,
    List<Map<String, dynamic>>? drawingPoints,
    int? drawingColor,
    double? drawingWidth,
    @Default([]) List<TextElement> textElements,
    List<StoryTag>? tags,
    Map<String, dynamic>? music,
    String? musicArtist,
    Map<String, dynamic>? location,
    String? privacy,
    bool? isPublic,
    bool? isSeen,
    bool? isCloseFriend,
  }) = _StoryItem;

  factory StoryItem.fromJson(Map<String, dynamic> json) =>
      _$StoryItemFromJson(json);
}

// Location result for story location search
class LocationResult {
  final String name;
  final String address;
  final double latitude;
  final double longitude;

  LocationResult({
    required this.name,
    required this.address,
    required this.latitude,
    required this.longitude,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  factory LocationResult.fromJson(Map<String, dynamic> json) {
    return LocationResult(
      name: json['name'] ?? '',
      address: json['address'] ?? '',
      latitude: (json['latitude'] as num?)?.toDouble() ?? 0.0,
      longitude: (json['longitude'] as num?)?.toDouble() ?? 0.0,
    );
  }
}
