// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'story_settings_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_StorySettings _$StorySettingsFromJson(Map<String, dynamic> json) =>
    _StorySettings(
      id: json['id'] as String,
      duration: $enumDecode(_$StoryDurationEnumMap, json['duration']),
      visibility: $enumDecode(_$StoryVisibilityEnumMap, json['visibility']),
      hiddenFromUserIds: (json['hiddenFromUserIds'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      allowEditing: json['allowEditing'] as bool,
      allowComments: json['allowComments'] as bool,
      allowReactions: json['allowReactions'] as bool,
      allowMentions: json['allowMentions'] as bool,
      allowMusic: json['allowMusic'] as bool,
      allowFilters: json['allowFilters'] as bool,
      allowTextOverlays: json['allowTextOverlays'] as bool,
      allowVideoUpload: json['allowVideoUpload'] as bool,
      allowedGroupIds: (json['allowedGroupIds'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$StorySettingsToJson(_StorySettings instance) =>
    <String, dynamic>{
      'id': instance.id,
      'duration': _$StoryDurationEnumMap[instance.duration]!,
      'visibility': _$StoryVisibilityEnumMap[instance.visibility]!,
      'hiddenFromUserIds': instance.hiddenFromUserIds,
      'allowEditing': instance.allowEditing,
      'allowComments': instance.allowComments,
      'allowReactions': instance.allowReactions,
      'allowMentions': instance.allowMentions,
      'allowMusic': instance.allowMusic,
      'allowFilters': instance.allowFilters,
      'allowTextOverlays': instance.allowTextOverlays,
      'allowVideoUpload': instance.allowVideoUpload,
      'allowedGroupIds': instance.allowedGroupIds,
    };

const _$StoryDurationEnumMap = {
  StoryDuration.sixHours: 'sixHours',
  StoryDuration.twelveHours: 'twelveHours',
  StoryDuration.twentyFourHours: 'twentyFourHours',
};

const _$StoryVisibilityEnumMap = {
  StoryVisibility.public: 'public',
  StoryVisibility.followers: 'followers',
  StoryVisibility.specificGroups: 'specificGroups',
  StoryVisibility.closeFriends: 'closeFriends',
};
