import 'package:flutter/material.dart';

/// Shared text element for story editing
class TextElement {
  final String text;
  final Color color;
  final double size;
  final Offset position;
  final Color backgroundColor;
  final String? fontFamily;
  final FontWeight fontWeight;
  final bool isItalic;
  final TextAlign textAlign;
  final List<String> mentions;

  TextElement({
    required this.text,
    required this.color,
    required this.size,
    required this.position,
    required this.backgroundColor,
    this.fontFamily,
    this.fontWeight = FontWeight.normal,
    this.isItalic = false,
    this.textAlign = TextAlign.center,
    this.mentions = const [],
  });

  TextElement copyWith({
    String? text,
    Color? color,
    double? size,
    Offset? position,
    Color? backgroundColor,
    String? fontFamily,
    FontWeight? fontWeight,
    bool? isItalic,
    TextAlign? textAlign,
    List<String>? mentions,
  }) {
    return TextElement(
      text: text ?? this.text,
      color: color ?? this.color,
      size: size ?? this.size,
      position: position ?? this.position,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      fontFamily: fontFamily ?? this.fontFamily,
      fontWeight: fontWeight ?? this.fontWeight,
      isItalic: isItalic ?? this.isItalic,
      textAlign: textAlign ?? this.textAlign,
      mentions: mentions ?? this.mentions,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'text': text,
      'color': color.toARGB32(),
      'size': size,
      'position': {'x': position.dx, 'y': position.dy},
      'backgroundColor': backgroundColor.toARGB32(),
      'fontFamily': fontFamily,
      'fontWeight': fontWeight.index,
      'isItalic': isItalic,
      'textAlign': textAlign.index,
      'mentions': mentions,
    };
  }

  factory TextElement.fromJson(Map<String, dynamic> json) {
    return TextElement(
      text: json['text'] ?? '',
      color: Color(json['color'] ?? 0xFFFFFFFF),
      size: (json['size'] as num?)?.toDouble() ?? 24.0,
      position: Offset(
        (json['position']?['x'] as num?)?.toDouble() ?? 100.0,
        (json['position']?['y'] as num?)?.toDouble() ?? 100.0,
      ),
      backgroundColor: Color(json['backgroundColor'] ?? 0x00000000),
      fontFamily: json['fontFamily'],
      fontWeight: FontWeight.values[json['fontWeight'] ?? 0],
      isItalic: json['isItalic'] ?? false,
      textAlign: TextAlign.values[json['textAlign'] ?? 2],
      mentions: List<String>.from(json['mentions'] ?? []),
    );
  }
}

/// Shared music track model
class MusicTrack {
  final String id;
  final String title;
  final String artist;
  final Duration duration;
  final String path;
  final String genre;
  final String? albumArt;
  final bool isLocal;

  MusicTrack({
    required this.id,
    required this.title,
    required this.artist,
    required this.duration,
    required this.path,
    required this.genre,
    this.albumArt,
    this.isLocal = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'artist': artist,
      'duration': duration.inMilliseconds,
      'path': path,
      'genre': genre,
      'albumArt': albumArt,
      'isLocal': isLocal,
    };
  }

  factory MusicTrack.fromJson(Map<String, dynamic> json) {
    return MusicTrack(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      artist: json['artist'] ?? '',
      duration: Duration(milliseconds: json['duration'] ?? 0),
      path: json['path'] ?? '',
      genre: json['genre'] ?? '',
      albumArt: json['albumArt'],
      isLocal: json['isLocal'] ?? false,
    );
  }
}

/// Shared drawing point model
class DrawingPoint {
  final Offset position;
  final Color color;
  final double width;
  final DateTime timestamp;

  DrawingPoint({
    required this.position,
    required this.color,
    required this.width,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'position': {'x': position.dx, 'y': position.dy},
      'color': color.toARGB32(),
      'width': width,
      'timestamp': timestamp.millisecondsSinceEpoch,
    };
  }

  factory DrawingPoint.fromJson(Map<String, dynamic> json) {
    return DrawingPoint(
      position: Offset(
        (json['position']?['x'] as num?)?.toDouble() ?? 0.0,
        (json['position']?['y'] as num?)?.toDouble() ?? 0.0,
      ),
      color: Color(json['color'] ?? 0xFFFFFFFF),
      width: (json['width'] as num?)?.toDouble() ?? 3.0,
      timestamp: DateTime.fromMillisecondsSinceEpoch(json['timestamp'] ?? 0),
    );
  }
}

/// Shared story filter model
class StoryFilter {
  final String name;
  final String displayName;
  final String? iconPath;
  final Map<String, dynamic>? parameters;

  const StoryFilter({
    required this.name,
    required this.displayName,
    this.iconPath,
    this.parameters,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'displayName': displayName,
      'iconPath': iconPath,
      'parameters': parameters,
    };
  }

  factory StoryFilter.fromJson(Map<String, dynamic> json) {
    return StoryFilter(
      name: json['name'] ?? '',
      displayName: json['displayName'] ?? '',
      iconPath: json['iconPath'],
      parameters: json['parameters'] as Map<String, dynamic>?,
    );
  }
}

/// Shared story privacy enum
enum StoryPrivacy { public, followers, closeFriends, custom, private }

/// Shared story media type enum
enum StoryMediaType {
  image,
  video,
  text,
  music,
  poll,
  question,
  countdown,
  quiz,
}

/// Shared story type enum
enum StoryType { regular, highlight, archive, featured, sponsored }

/// Shared story duration enum
enum StoryDuration { sixHours, twelveHours, twentyFourHours }

/// Shared story visibility enum
enum StoryVisibility { public, followers, specificGroups, closeFriends }

/// Shared constants for story features
class StoryConstants {
  // Default values
  static const Duration defaultStoryDuration = Duration(seconds: 5);
  static const double defaultTextSize = 24.0;
  static const double defaultDrawingWidth = 3.0;
  static const Offset defaultTextPosition = Offset(100, 100);

  // Available filters
  static const List<String> availableFilters = [
    'Normal',
    'Vintage',
    'Black & White',
    'Sepia',
    'Cool',
    'Warm',
    'Dramatic',
    'Fade',
    'Chrome',
    'Noir',
    'B&W',
    'Instant',
    'Mono',
    'Process',
    'Tonal',
    'Transfer',
  ];

  // Available text colors
  static const List<Color> textColors = [
    Colors.white,
    Colors.black,
    Colors.red,
    Colors.blue,
    Colors.green,
    Colors.yellow,
    Colors.purple,
    Colors.orange,
    Colors.pink,
    Colors.cyan,
    Colors.teal,
    Colors.indigo,
    Colors.amber,
    Colors.lime,
    Colors.deepPurple,
    Colors.deepOrange,
  ];

  // Available background colors
  static const List<Color> backgroundColors = [
    Colors.transparent,
    Colors.black,
    Colors.white,
    Colors.red,
    Colors.blue,
    Colors.green,
    Colors.yellow,
    Colors.purple,
    Colors.orange,
    Colors.pink,
    Colors.cyan,
    Colors.teal,
    Colors.indigo,
    Colors.amber,
    Colors.lime,
    Colors.deepPurple,
    Colors.deepOrange,
    Colors.brown,
    Colors.grey,
  ];

  // Available fonts
  static const List<String> availableFonts = [
    'Default',
    'Roboto',
    'OpenSans',
    'Lato',
    'Poppins',
    'Montserrat',
    'PlayfairDisplay',
    'DancingScript',
  ];

  // Story expiration durations
  static const Map<StoryDuration, Duration> expirationDurations = {
    StoryDuration.sixHours: Duration(hours: 6),
    StoryDuration.twelveHours: Duration(hours: 12),
    StoryDuration.twentyFourHours: Duration(hours: 24),
  };
}

/// Story poll model for interactive polls
class StoryPoll {
  final String question;
  final List<String> options;
  final Map<String, List<String>> votes; // option -> list of user IDs
  final DateTime endTime;
  final bool isMultipleChoice;
  final bool isAnonymous;

  StoryPoll({
    required this.question,
    required this.options,
    required this.votes,
    required this.endTime,
    this.isMultipleChoice = false,
    this.isAnonymous = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'question': question,
      'options': options,
      'votes': votes,
      'endTime': endTime.millisecondsSinceEpoch,
      'isMultipleChoice': isMultipleChoice,
      'isAnonymous': isAnonymous,
    };
  }

  factory StoryPoll.fromJson(Map<String, dynamic> json) {
    return StoryPoll(
      question: json['question'] ?? '',
      options: List<String>.from(json['options'] ?? []),
      votes: Map<String, List<String>>.from(
        (json['votes'] as Map<String, dynamic>?)?.map(
              (key, value) => MapEntry(key, List<String>.from(value)),
            ) ??
            {},
      ),
      endTime: DateTime.fromMillisecondsSinceEpoch(json['endTime'] ?? 0),
      isMultipleChoice: json['isMultipleChoice'] ?? false,
      isAnonymous: json['isAnonymous'] ?? false,
    );
  }

  /// Get total votes for an option
  int getVoteCount(String option) {
    return votes[option]?.length ?? 0;
  }

  /// Get total votes across all options
  int get totalVotes {
    return votes.values.fold(0, (sum, voters) => sum + voters.length);
  }

  /// Get percentage for an option
  double getPercentage(String option) {
    if (totalVotes == 0) return 0.0;
    return (getVoteCount(option) / totalVotes) * 100;
  }

  /// Check if poll is expired
  bool get isExpired => DateTime.now().isAfter(endTime);

  /// Check if user has voted
  bool hasUserVoted(String userId) {
    return votes.values.any((voters) => voters.contains(userId));
  }

  /// Get user's vote
  List<String> getUserVote(String userId) {
    return votes.entries
        .where((entry) => entry.value.contains(userId))
        .map((entry) => entry.key)
        .toList();
  }
}

/// Story question model for interactive Q&A
class StoryQuestion {
  final String question;
  final String? answer;
  final String? answeredBy;
  final DateTime? answeredAt;
  final bool isAnonymous;
  final List<String> upvotes;
  final List<String> downvotes;

  StoryQuestion({
    required this.question,
    this.answer,
    this.answeredBy,
    this.answeredAt,
    this.isAnonymous = false,
    this.upvotes = const [],
    this.downvotes = const [],
  });

  Map<String, dynamic> toJson() {
    return {
      'question': question,
      'answer': answer,
      'answeredBy': answeredBy,
      'answeredAt': answeredAt?.millisecondsSinceEpoch,
      'isAnonymous': isAnonymous,
      'upvotes': upvotes,
      'downvotes': downvotes,
    };
  }

  factory StoryQuestion.fromJson(Map<String, dynamic> json) {
    return StoryQuestion(
      question: json['question'] ?? '',
      answer: json['answer'],
      answeredBy: json['answeredBy'],
      answeredAt: json['answeredAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['answeredAt'])
          : null,
      isAnonymous: json['isAnonymous'] ?? false,
      upvotes: List<String>.from(json['upvotes'] ?? []),
      downvotes: List<String>.from(json['downvotes'] ?? []),
    );
  }

  /// Check if question is answered
  bool get isAnswered => answer != null && answeredBy != null;

  /// Get net votes (upvotes - downvotes)
  int get netVotes => upvotes.length - downvotes.length;

  /// Check if user has upvoted
  bool hasUserUpvoted(String userId) => upvotes.contains(userId);

  /// Check if user has downvoted
  bool hasUserDownvoted(String userId) => downvotes.contains(userId);
}

/// Story countdown model for time-based features
class StoryCountdown {
  final String title;
  final DateTime targetDate;
  final DateTime createdAt;
  final String? description;
  final bool isRepeating;
  final Duration? repeatInterval;
  final bool isPublic;
  final List<String> participants;

  StoryCountdown({
    required this.title,
    required this.targetDate,
    required this.createdAt,
    this.description,
    this.isRepeating = false,
    this.repeatInterval,
    this.isPublic = true,
    this.participants = const [],
  });

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'targetDate': targetDate.millisecondsSinceEpoch,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'description': description,
      'isRepeating': isRepeating,
      'repeatInterval': repeatInterval?.inSeconds,
      'isPublic': isPublic,
      'participants': participants,
    };
  }

  factory StoryCountdown.fromJson(Map<String, dynamic> json) {
    return StoryCountdown(
      title: json['title'] ?? '',
      targetDate: DateTime.fromMillisecondsSinceEpoch(json['targetDate'] ?? 0),
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt'] ?? 0),
      description: json['description'],
      isRepeating: json['isRepeating'] ?? false,
      repeatInterval: json['repeatInterval'] != null
          ? Duration(seconds: json['repeatInterval'])
          : null,
      isPublic: json['isPublic'] ?? true,
      participants: List<String>.from(json['participants'] ?? []),
    );
  }

  /// Get time remaining until target date
  Duration get timeRemaining {
    final remaining = targetDate.difference(DateTime.now());
    return remaining.isNegative ? Duration.zero : remaining;
  }

  /// Check if countdown is expired
  bool get isExpired => DateTime.now().isAfter(targetDate);

  /// Get formatted time remaining string
  String get formattedTimeRemaining {
    final remaining = timeRemaining;
    if (remaining.inDays > 0) {
      return '${remaining.inDays}d ${remaining.inHours % 24}h';
    } else if (remaining.inHours > 0) {
      return '${remaining.inHours}h ${remaining.inMinutes % 60}m';
    } else if (remaining.inMinutes > 0) {
      return '${remaining.inMinutes}m ${remaining.inSeconds % 60}s';
    } else {
      return '${remaining.inSeconds}s';
    }
  }
}

/// Collaborative story model for multi-user stories
class CollaborativeStory {
  final String id;
  final String ownerId;
  final List<String> contributors;
  final List<CollaborativeStorySegment> segments;
  final DateTime createdAt;
  final DateTime expiresAt;
  final bool isActive;
  final String? title;
  final String? description;
  final int maxContributors;
  final Duration contributionTimeLimit;

  CollaborativeStory({
    required this.id,
    required this.ownerId,
    required this.contributors,
    required this.segments,
    required this.createdAt,
    required this.expiresAt,
    this.isActive = true,
    this.title,
    this.description,
    this.maxContributors = 10,
    this.contributionTimeLimit = const Duration(hours: 24),
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'ownerId': ownerId,
      'contributors': contributors,
      'segments': segments.map((s) => s.toJson()).toList(),
      'createdAt': createdAt.millisecondsSinceEpoch,
      'expiresAt': expiresAt.millisecondsSinceEpoch,
      'isActive': isActive,
      'title': title,
      'description': description,
      'maxContributors': maxContributors,
      'contributionTimeLimit': contributionTimeLimit.inSeconds,
    };
  }

  factory CollaborativeStory.fromJson(Map<String, dynamic> json) {
    return CollaborativeStory(
      id: json['id'] ?? '',
      ownerId: json['ownerId'] ?? '',
      contributors: List<String>.from(json['contributors'] ?? []),
      segments:
          (json['segments'] as List<dynamic>?)
              ?.map((s) => CollaborativeStorySegment.fromJson(s))
              .toList() ??
          [],
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt'] ?? 0),
      expiresAt: DateTime.fromMillisecondsSinceEpoch(json['expiresAt'] ?? 0),
      isActive: json['isActive'] ?? true,
      title: json['title'],
      description: json['description'],
      maxContributors: json['maxContributors'] ?? 10,
      contributionTimeLimit: Duration(
        seconds: json['contributionTimeLimit'] ?? 86400,
      ),
    );
  }

  /// Check if user can contribute
  bool canUserContribute(String userId) {
    if (!isActive) return false;
    if (contributors.length >= maxContributors) return false;
    if (DateTime.now().isAfter(expiresAt)) return false;
    return contributors.contains(userId) || userId == ownerId;
  }

  /// Get next contributor
  String? get nextContributor {
    if (segments.isEmpty) return ownerId;
    final lastSegment = segments.last;
    final lastContributorIndex = contributors.indexOf(
      lastSegment.contributorId,
    );
    final nextIndex = (lastContributorIndex + 1) % contributors.length;
    return contributors[nextIndex];
  }

  /// Check if story is complete
  bool get isComplete => segments.length >= maxContributors || !isActive;
}

/// Individual segment in a collaborative story
class CollaborativeStorySegment {
  final String id;
  final String contributorId;
  final String mediaUrl;
  final StoryMediaType mediaType;
  final String? caption;
  final DateTime createdAt;
  final Duration duration;
  final Map<String, dynamic>? metadata;

  CollaborativeStorySegment({
    required this.id,
    required this.contributorId,
    required this.mediaUrl,
    required this.mediaType,
    this.caption,
    required this.createdAt,
    this.duration = const Duration(seconds: 5),
    this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'contributorId': contributorId,
      'mediaUrl': mediaUrl,
      'mediaType': mediaType.toString().split('.').last,
      'caption': caption,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'duration': duration.inMicroseconds,
      'metadata': metadata,
    };
  }

  factory CollaborativeStorySegment.fromJson(Map<String, dynamic> json) {
    return CollaborativeStorySegment(
      id: json['id'] ?? '',
      contributorId: json['contributorId'] ?? '',
      mediaUrl: json['mediaUrl'] ?? '',
      mediaType: StoryMediaType.values.firstWhere(
        (e) => e.toString().split('.').last == (json['mediaType'] ?? 'image'),
        orElse: () => StoryMediaType.image,
      ),
      caption: json['caption'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt'] ?? 0),
      duration: Duration(microseconds: json['duration'] ?? 5000000),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }
}

/// Story quiz model for interactive quizzes
class StoryQuiz {
  final String question;
  final List<String> options;
  final int correctAnswerIndex;
  final String? explanation;
  final Map<String, int> answers; // user ID -> selected option index
  final bool isAnonymous;
  final DateTime endTime;

  StoryQuiz({
    required this.question,
    required this.options,
    required this.correctAnswerIndex,
    this.explanation,
    this.answers = const {},
    this.isAnonymous = false,
    required this.endTime,
  });

  Map<String, dynamic> toJson() {
    return {
      'question': question,
      'options': options,
      'correctAnswerIndex': correctAnswerIndex,
      'explanation': explanation,
      'answers': answers,
      'isAnonymous': isAnonymous,
      'endTime': endTime.millisecondsSinceEpoch,
    };
  }

  factory StoryQuiz.fromJson(Map<String, dynamic> json) {
    return StoryQuiz(
      question: json['question'] ?? '',
      options: List<String>.from(json['options'] ?? []),
      correctAnswerIndex: json['correctAnswerIndex'] ?? 0,
      explanation: json['explanation'],
      answers: Map<String, int>.from(json['answers'] ?? {}),
      isAnonymous: json['isAnonymous'] ?? false,
      endTime: DateTime.fromMillisecondsSinceEpoch(json['endTime'] ?? 0),
    );
  }

  /// Get answer count for an option
  int getAnswerCount(int optionIndex) {
    return answers.values.where((answer) => answer == optionIndex).length;
  }

  /// Get total answers
  int get totalAnswers => answers.length;

  /// Get correct answer percentage
  double get correctAnswerPercentage {
    if (totalAnswers == 0) return 0.0;
    final correctAnswers = answers.values
        .where((answer) => answer == correctAnswerIndex)
        .length;
    return (correctAnswers / totalAnswers) * 100;
  }

  /// Check if quiz is expired
  bool get isExpired => DateTime.now().isAfter(endTime);

  /// Check if user has answered
  bool hasUserAnswered(String userId) => answers.containsKey(userId);

  /// Get user's answer
  int? getUserAnswer(String userId) => answers[userId];

  /// Check if user's answer is correct
  bool isUserAnswerCorrect(String userId) {
    final userAnswer = answers[userId];
    return userAnswer != null && userAnswer == correctAnswerIndex;
  }
}
