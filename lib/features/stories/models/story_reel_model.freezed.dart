// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'story_reel_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$StoryTag {

 String get userId; String get username; String? get name; double get x; double get y;
/// Create a copy of StoryTag
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StoryTagCopyWith<StoryTag> get copyWith => _$StoryTagCopyWithImpl<StoryTag>(this as StoryTag, _$identity);

  /// Serializes this StoryTag to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StoryTag&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.username, username) || other.username == username)&&(identical(other.name, name) || other.name == name)&&(identical(other.x, x) || other.x == x)&&(identical(other.y, y) || other.y == y));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,userId,username,name,x,y);

@override
String toString() {
  return 'StoryTag(userId: $userId, username: $username, name: $name, x: $x, y: $y)';
}


}

/// @nodoc
abstract mixin class $StoryTagCopyWith<$Res>  {
  factory $StoryTagCopyWith(StoryTag value, $Res Function(StoryTag) _then) = _$StoryTagCopyWithImpl;
@useResult
$Res call({
 String userId, String username, String? name, double x, double y
});




}
/// @nodoc
class _$StoryTagCopyWithImpl<$Res>
    implements $StoryTagCopyWith<$Res> {
  _$StoryTagCopyWithImpl(this._self, this._then);

  final StoryTag _self;
  final $Res Function(StoryTag) _then;

/// Create a copy of StoryTag
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? userId = null,Object? username = null,Object? name = freezed,Object? x = null,Object? y = null,}) {
  return _then(_self.copyWith(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,x: null == x ? _self.x : x // ignore: cast_nullable_to_non_nullable
as double,y: null == y ? _self.y : y // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [StoryTag].
extension StoryTagPatterns on StoryTag {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _StoryTag value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _StoryTag() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _StoryTag value)  $default,){
final _that = this;
switch (_that) {
case _StoryTag():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _StoryTag value)?  $default,){
final _that = this;
switch (_that) {
case _StoryTag() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String userId,  String username,  String? name,  double x,  double y)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _StoryTag() when $default != null:
return $default(_that.userId,_that.username,_that.name,_that.x,_that.y);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String userId,  String username,  String? name,  double x,  double y)  $default,) {final _that = this;
switch (_that) {
case _StoryTag():
return $default(_that.userId,_that.username,_that.name,_that.x,_that.y);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String userId,  String username,  String? name,  double x,  double y)?  $default,) {final _that = this;
switch (_that) {
case _StoryTag() when $default != null:
return $default(_that.userId,_that.username,_that.name,_that.x,_that.y);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _StoryTag implements StoryTag {
  const _StoryTag({required this.userId, required this.username, this.name, required this.x, required this.y});
  factory _StoryTag.fromJson(Map<String, dynamic> json) => _$StoryTagFromJson(json);

@override final  String userId;
@override final  String username;
@override final  String? name;
@override final  double x;
@override final  double y;

/// Create a copy of StoryTag
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StoryTagCopyWith<_StoryTag> get copyWith => __$StoryTagCopyWithImpl<_StoryTag>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$StoryTagToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StoryTag&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.username, username) || other.username == username)&&(identical(other.name, name) || other.name == name)&&(identical(other.x, x) || other.x == x)&&(identical(other.y, y) || other.y == y));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,userId,username,name,x,y);

@override
String toString() {
  return 'StoryTag(userId: $userId, username: $username, name: $name, x: $x, y: $y)';
}


}

/// @nodoc
abstract mixin class _$StoryTagCopyWith<$Res> implements $StoryTagCopyWith<$Res> {
  factory _$StoryTagCopyWith(_StoryTag value, $Res Function(_StoryTag) _then) = __$StoryTagCopyWithImpl;
@override @useResult
$Res call({
 String userId, String username, String? name, double x, double y
});




}
/// @nodoc
class __$StoryTagCopyWithImpl<$Res>
    implements _$StoryTagCopyWith<$Res> {
  __$StoryTagCopyWithImpl(this._self, this._then);

  final _StoryTag _self;
  final $Res Function(_StoryTag) _then;

/// Create a copy of StoryTag
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? userId = null,Object? username = null,Object? name = freezed,Object? x = null,Object? y = null,}) {
  return _then(_StoryTag(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,x: null == x ? _self.x : x // ignore: cast_nullable_to_non_nullable
as double,y: null == y ? _self.y : y // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}


/// @nodoc
mixin _$StoryReel {

 String get id; String get userId; String get username; String get userAvatarUrl; List<StoryItem> get stories; bool get isAllViewed; bool get isCloseFriend;
/// Create a copy of StoryReel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StoryReelCopyWith<StoryReel> get copyWith => _$StoryReelCopyWithImpl<StoryReel>(this as StoryReel, _$identity);

  /// Serializes this StoryReel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StoryReel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.username, username) || other.username == username)&&(identical(other.userAvatarUrl, userAvatarUrl) || other.userAvatarUrl == userAvatarUrl)&&const DeepCollectionEquality().equals(other.stories, stories)&&(identical(other.isAllViewed, isAllViewed) || other.isAllViewed == isAllViewed)&&(identical(other.isCloseFriend, isCloseFriend) || other.isCloseFriend == isCloseFriend));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,username,userAvatarUrl,const DeepCollectionEquality().hash(stories),isAllViewed,isCloseFriend);

@override
String toString() {
  return 'StoryReel(id: $id, userId: $userId, username: $username, userAvatarUrl: $userAvatarUrl, stories: $stories, isAllViewed: $isAllViewed, isCloseFriend: $isCloseFriend)';
}


}

/// @nodoc
abstract mixin class $StoryReelCopyWith<$Res>  {
  factory $StoryReelCopyWith(StoryReel value, $Res Function(StoryReel) _then) = _$StoryReelCopyWithImpl;
@useResult
$Res call({
 String id, String userId, String username, String userAvatarUrl, List<StoryItem> stories, bool isAllViewed, bool isCloseFriend
});




}
/// @nodoc
class _$StoryReelCopyWithImpl<$Res>
    implements $StoryReelCopyWith<$Res> {
  _$StoryReelCopyWithImpl(this._self, this._then);

  final StoryReel _self;
  final $Res Function(StoryReel) _then;

/// Create a copy of StoryReel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? username = null,Object? userAvatarUrl = null,Object? stories = null,Object? isAllViewed = null,Object? isCloseFriend = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,userAvatarUrl: null == userAvatarUrl ? _self.userAvatarUrl : userAvatarUrl // ignore: cast_nullable_to_non_nullable
as String,stories: null == stories ? _self.stories : stories // ignore: cast_nullable_to_non_nullable
as List<StoryItem>,isAllViewed: null == isAllViewed ? _self.isAllViewed : isAllViewed // ignore: cast_nullable_to_non_nullable
as bool,isCloseFriend: null == isCloseFriend ? _self.isCloseFriend : isCloseFriend // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [StoryReel].
extension StoryReelPatterns on StoryReel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _StoryReel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _StoryReel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _StoryReel value)  $default,){
final _that = this;
switch (_that) {
case _StoryReel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _StoryReel value)?  $default,){
final _that = this;
switch (_that) {
case _StoryReel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  String username,  String userAvatarUrl,  List<StoryItem> stories,  bool isAllViewed,  bool isCloseFriend)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _StoryReel() when $default != null:
return $default(_that.id,_that.userId,_that.username,_that.userAvatarUrl,_that.stories,_that.isAllViewed,_that.isCloseFriend);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  String username,  String userAvatarUrl,  List<StoryItem> stories,  bool isAllViewed,  bool isCloseFriend)  $default,) {final _that = this;
switch (_that) {
case _StoryReel():
return $default(_that.id,_that.userId,_that.username,_that.userAvatarUrl,_that.stories,_that.isAllViewed,_that.isCloseFriend);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  String username,  String userAvatarUrl,  List<StoryItem> stories,  bool isAllViewed,  bool isCloseFriend)?  $default,) {final _that = this;
switch (_that) {
case _StoryReel() when $default != null:
return $default(_that.id,_that.userId,_that.username,_that.userAvatarUrl,_that.stories,_that.isAllViewed,_that.isCloseFriend);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _StoryReel implements StoryReel {
  const _StoryReel({required this.id, required this.userId, required this.username, required this.userAvatarUrl, required final  List<StoryItem> stories, this.isAllViewed = false, this.isCloseFriend = false}): _stories = stories;
  factory _StoryReel.fromJson(Map<String, dynamic> json) => _$StoryReelFromJson(json);

@override final  String id;
@override final  String userId;
@override final  String username;
@override final  String userAvatarUrl;
 final  List<StoryItem> _stories;
@override List<StoryItem> get stories {
  if (_stories is EqualUnmodifiableListView) return _stories;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_stories);
}

@override@JsonKey() final  bool isAllViewed;
@override@JsonKey() final  bool isCloseFriend;

/// Create a copy of StoryReel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StoryReelCopyWith<_StoryReel> get copyWith => __$StoryReelCopyWithImpl<_StoryReel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$StoryReelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StoryReel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.username, username) || other.username == username)&&(identical(other.userAvatarUrl, userAvatarUrl) || other.userAvatarUrl == userAvatarUrl)&&const DeepCollectionEquality().equals(other._stories, _stories)&&(identical(other.isAllViewed, isAllViewed) || other.isAllViewed == isAllViewed)&&(identical(other.isCloseFriend, isCloseFriend) || other.isCloseFriend == isCloseFriend));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,username,userAvatarUrl,const DeepCollectionEquality().hash(_stories),isAllViewed,isCloseFriend);

@override
String toString() {
  return 'StoryReel(id: $id, userId: $userId, username: $username, userAvatarUrl: $userAvatarUrl, stories: $stories, isAllViewed: $isAllViewed, isCloseFriend: $isCloseFriend)';
}


}

/// @nodoc
abstract mixin class _$StoryReelCopyWith<$Res> implements $StoryReelCopyWith<$Res> {
  factory _$StoryReelCopyWith(_StoryReel value, $Res Function(_StoryReel) _then) = __$StoryReelCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, String username, String userAvatarUrl, List<StoryItem> stories, bool isAllViewed, bool isCloseFriend
});




}
/// @nodoc
class __$StoryReelCopyWithImpl<$Res>
    implements _$StoryReelCopyWith<$Res> {
  __$StoryReelCopyWithImpl(this._self, this._then);

  final _StoryReel _self;
  final $Res Function(_StoryReel) _then;

/// Create a copy of StoryReel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? username = null,Object? userAvatarUrl = null,Object? stories = null,Object? isAllViewed = null,Object? isCloseFriend = null,}) {
  return _then(_StoryReel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,userAvatarUrl: null == userAvatarUrl ? _self.userAvatarUrl : userAvatarUrl // ignore: cast_nullable_to_non_nullable
as String,stories: null == stories ? _self._stories : stories // ignore: cast_nullable_to_non_nullable
as List<StoryItem>,isAllViewed: null == isAllViewed ? _self.isAllViewed : isAllViewed // ignore: cast_nullable_to_non_nullable
as bool,isCloseFriend: null == isCloseFriend ? _self.isCloseFriend : isCloseFriend // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$StoryItem {

 String get id; String get userId; String get mediaUrl; MediaType get mediaType; Duration get duration; DateTime get timestamp;// Story metadata
 String? get textOverlay; int? get textColor; double? get textSize; Map<String, double>? get textPosition; int? get backgroundColor; String? get filter; List<Map<String, dynamic>>? get drawingPoints; int? get drawingColor; double? get drawingWidth; List<TextElement> get textElements; List<StoryTag>? get tags; Map<String, dynamic>? get music; String? get musicArtist; Map<String, dynamic>? get location; String? get privacy; bool? get isPublic; bool? get isSeen; bool? get isCloseFriend;
/// Create a copy of StoryItem
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StoryItemCopyWith<StoryItem> get copyWith => _$StoryItemCopyWithImpl<StoryItem>(this as StoryItem, _$identity);

  /// Serializes this StoryItem to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StoryItem&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.mediaUrl, mediaUrl) || other.mediaUrl == mediaUrl)&&(identical(other.mediaType, mediaType) || other.mediaType == mediaType)&&(identical(other.duration, duration) || other.duration == duration)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.textOverlay, textOverlay) || other.textOverlay == textOverlay)&&(identical(other.textColor, textColor) || other.textColor == textColor)&&(identical(other.textSize, textSize) || other.textSize == textSize)&&const DeepCollectionEquality().equals(other.textPosition, textPosition)&&(identical(other.backgroundColor, backgroundColor) || other.backgroundColor == backgroundColor)&&(identical(other.filter, filter) || other.filter == filter)&&const DeepCollectionEquality().equals(other.drawingPoints, drawingPoints)&&(identical(other.drawingColor, drawingColor) || other.drawingColor == drawingColor)&&(identical(other.drawingWidth, drawingWidth) || other.drawingWidth == drawingWidth)&&const DeepCollectionEquality().equals(other.textElements, textElements)&&const DeepCollectionEquality().equals(other.tags, tags)&&const DeepCollectionEquality().equals(other.music, music)&&(identical(other.musicArtist, musicArtist) || other.musicArtist == musicArtist)&&const DeepCollectionEquality().equals(other.location, location)&&(identical(other.privacy, privacy) || other.privacy == privacy)&&(identical(other.isPublic, isPublic) || other.isPublic == isPublic)&&(identical(other.isSeen, isSeen) || other.isSeen == isSeen)&&(identical(other.isCloseFriend, isCloseFriend) || other.isCloseFriend == isCloseFriend));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,userId,mediaUrl,mediaType,duration,timestamp,textOverlay,textColor,textSize,const DeepCollectionEquality().hash(textPosition),backgroundColor,filter,const DeepCollectionEquality().hash(drawingPoints),drawingColor,drawingWidth,const DeepCollectionEquality().hash(textElements),const DeepCollectionEquality().hash(tags),const DeepCollectionEquality().hash(music),musicArtist,const DeepCollectionEquality().hash(location),privacy,isPublic,isSeen,isCloseFriend]);

@override
String toString() {
  return 'StoryItem(id: $id, userId: $userId, mediaUrl: $mediaUrl, mediaType: $mediaType, duration: $duration, timestamp: $timestamp, textOverlay: $textOverlay, textColor: $textColor, textSize: $textSize, textPosition: $textPosition, backgroundColor: $backgroundColor, filter: $filter, drawingPoints: $drawingPoints, drawingColor: $drawingColor, drawingWidth: $drawingWidth, textElements: $textElements, tags: $tags, music: $music, musicArtist: $musicArtist, location: $location, privacy: $privacy, isPublic: $isPublic, isSeen: $isSeen, isCloseFriend: $isCloseFriend)';
}


}

/// @nodoc
abstract mixin class $StoryItemCopyWith<$Res>  {
  factory $StoryItemCopyWith(StoryItem value, $Res Function(StoryItem) _then) = _$StoryItemCopyWithImpl;
@useResult
$Res call({
 String id, String userId, String mediaUrl, MediaType mediaType, Duration duration, DateTime timestamp, String? textOverlay, int? textColor, double? textSize, Map<String, double>? textPosition, int? backgroundColor, String? filter, List<Map<String, dynamic>>? drawingPoints, int? drawingColor, double? drawingWidth, List<TextElement> textElements, List<StoryTag>? tags, Map<String, dynamic>? music, String? musicArtist, Map<String, dynamic>? location, String? privacy, bool? isPublic, bool? isSeen, bool? isCloseFriend
});




}
/// @nodoc
class _$StoryItemCopyWithImpl<$Res>
    implements $StoryItemCopyWith<$Res> {
  _$StoryItemCopyWithImpl(this._self, this._then);

  final StoryItem _self;
  final $Res Function(StoryItem) _then;

/// Create a copy of StoryItem
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? mediaUrl = null,Object? mediaType = null,Object? duration = null,Object? timestamp = null,Object? textOverlay = freezed,Object? textColor = freezed,Object? textSize = freezed,Object? textPosition = freezed,Object? backgroundColor = freezed,Object? filter = freezed,Object? drawingPoints = freezed,Object? drawingColor = freezed,Object? drawingWidth = freezed,Object? textElements = null,Object? tags = freezed,Object? music = freezed,Object? musicArtist = freezed,Object? location = freezed,Object? privacy = freezed,Object? isPublic = freezed,Object? isSeen = freezed,Object? isCloseFriend = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,mediaUrl: null == mediaUrl ? _self.mediaUrl : mediaUrl // ignore: cast_nullable_to_non_nullable
as String,mediaType: null == mediaType ? _self.mediaType : mediaType // ignore: cast_nullable_to_non_nullable
as MediaType,duration: null == duration ? _self.duration : duration // ignore: cast_nullable_to_non_nullable
as Duration,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,textOverlay: freezed == textOverlay ? _self.textOverlay : textOverlay // ignore: cast_nullable_to_non_nullable
as String?,textColor: freezed == textColor ? _self.textColor : textColor // ignore: cast_nullable_to_non_nullable
as int?,textSize: freezed == textSize ? _self.textSize : textSize // ignore: cast_nullable_to_non_nullable
as double?,textPosition: freezed == textPosition ? _self.textPosition : textPosition // ignore: cast_nullable_to_non_nullable
as Map<String, double>?,backgroundColor: freezed == backgroundColor ? _self.backgroundColor : backgroundColor // ignore: cast_nullable_to_non_nullable
as int?,filter: freezed == filter ? _self.filter : filter // ignore: cast_nullable_to_non_nullable
as String?,drawingPoints: freezed == drawingPoints ? _self.drawingPoints : drawingPoints // ignore: cast_nullable_to_non_nullable
as List<Map<String, dynamic>>?,drawingColor: freezed == drawingColor ? _self.drawingColor : drawingColor // ignore: cast_nullable_to_non_nullable
as int?,drawingWidth: freezed == drawingWidth ? _self.drawingWidth : drawingWidth // ignore: cast_nullable_to_non_nullable
as double?,textElements: null == textElements ? _self.textElements : textElements // ignore: cast_nullable_to_non_nullable
as List<TextElement>,tags: freezed == tags ? _self.tags : tags // ignore: cast_nullable_to_non_nullable
as List<StoryTag>?,music: freezed == music ? _self.music : music // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,musicArtist: freezed == musicArtist ? _self.musicArtist : musicArtist // ignore: cast_nullable_to_non_nullable
as String?,location: freezed == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,privacy: freezed == privacy ? _self.privacy : privacy // ignore: cast_nullable_to_non_nullable
as String?,isPublic: freezed == isPublic ? _self.isPublic : isPublic // ignore: cast_nullable_to_non_nullable
as bool?,isSeen: freezed == isSeen ? _self.isSeen : isSeen // ignore: cast_nullable_to_non_nullable
as bool?,isCloseFriend: freezed == isCloseFriend ? _self.isCloseFriend : isCloseFriend // ignore: cast_nullable_to_non_nullable
as bool?,
  ));
}

}


/// Adds pattern-matching-related methods to [StoryItem].
extension StoryItemPatterns on StoryItem {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _StoryItem value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _StoryItem() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _StoryItem value)  $default,){
final _that = this;
switch (_that) {
case _StoryItem():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _StoryItem value)?  $default,){
final _that = this;
switch (_that) {
case _StoryItem() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  String mediaUrl,  MediaType mediaType,  Duration duration,  DateTime timestamp,  String? textOverlay,  int? textColor,  double? textSize,  Map<String, double>? textPosition,  int? backgroundColor,  String? filter,  List<Map<String, dynamic>>? drawingPoints,  int? drawingColor,  double? drawingWidth,  List<TextElement> textElements,  List<StoryTag>? tags,  Map<String, dynamic>? music,  String? musicArtist,  Map<String, dynamic>? location,  String? privacy,  bool? isPublic,  bool? isSeen,  bool? isCloseFriend)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _StoryItem() when $default != null:
return $default(_that.id,_that.userId,_that.mediaUrl,_that.mediaType,_that.duration,_that.timestamp,_that.textOverlay,_that.textColor,_that.textSize,_that.textPosition,_that.backgroundColor,_that.filter,_that.drawingPoints,_that.drawingColor,_that.drawingWidth,_that.textElements,_that.tags,_that.music,_that.musicArtist,_that.location,_that.privacy,_that.isPublic,_that.isSeen,_that.isCloseFriend);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  String mediaUrl,  MediaType mediaType,  Duration duration,  DateTime timestamp,  String? textOverlay,  int? textColor,  double? textSize,  Map<String, double>? textPosition,  int? backgroundColor,  String? filter,  List<Map<String, dynamic>>? drawingPoints,  int? drawingColor,  double? drawingWidth,  List<TextElement> textElements,  List<StoryTag>? tags,  Map<String, dynamic>? music,  String? musicArtist,  Map<String, dynamic>? location,  String? privacy,  bool? isPublic,  bool? isSeen,  bool? isCloseFriend)  $default,) {final _that = this;
switch (_that) {
case _StoryItem():
return $default(_that.id,_that.userId,_that.mediaUrl,_that.mediaType,_that.duration,_that.timestamp,_that.textOverlay,_that.textColor,_that.textSize,_that.textPosition,_that.backgroundColor,_that.filter,_that.drawingPoints,_that.drawingColor,_that.drawingWidth,_that.textElements,_that.tags,_that.music,_that.musicArtist,_that.location,_that.privacy,_that.isPublic,_that.isSeen,_that.isCloseFriend);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  String mediaUrl,  MediaType mediaType,  Duration duration,  DateTime timestamp,  String? textOverlay,  int? textColor,  double? textSize,  Map<String, double>? textPosition,  int? backgroundColor,  String? filter,  List<Map<String, dynamic>>? drawingPoints,  int? drawingColor,  double? drawingWidth,  List<TextElement> textElements,  List<StoryTag>? tags,  Map<String, dynamic>? music,  String? musicArtist,  Map<String, dynamic>? location,  String? privacy,  bool? isPublic,  bool? isSeen,  bool? isCloseFriend)?  $default,) {final _that = this;
switch (_that) {
case _StoryItem() when $default != null:
return $default(_that.id,_that.userId,_that.mediaUrl,_that.mediaType,_that.duration,_that.timestamp,_that.textOverlay,_that.textColor,_that.textSize,_that.textPosition,_that.backgroundColor,_that.filter,_that.drawingPoints,_that.drawingColor,_that.drawingWidth,_that.textElements,_that.tags,_that.music,_that.musicArtist,_that.location,_that.privacy,_that.isPublic,_that.isSeen,_that.isCloseFriend);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _StoryItem implements StoryItem {
  const _StoryItem({required this.id, required this.userId, required this.mediaUrl, required this.mediaType, required this.duration, required this.timestamp, this.textOverlay, this.textColor, this.textSize, final  Map<String, double>? textPosition, this.backgroundColor, this.filter, final  List<Map<String, dynamic>>? drawingPoints, this.drawingColor, this.drawingWidth, final  List<TextElement> textElements = const [], final  List<StoryTag>? tags, final  Map<String, dynamic>? music, this.musicArtist, final  Map<String, dynamic>? location, this.privacy, this.isPublic, this.isSeen, this.isCloseFriend}): _textPosition = textPosition,_drawingPoints = drawingPoints,_textElements = textElements,_tags = tags,_music = music,_location = location;
  factory _StoryItem.fromJson(Map<String, dynamic> json) => _$StoryItemFromJson(json);

@override final  String id;
@override final  String userId;
@override final  String mediaUrl;
@override final  MediaType mediaType;
@override final  Duration duration;
@override final  DateTime timestamp;
// Story metadata
@override final  String? textOverlay;
@override final  int? textColor;
@override final  double? textSize;
 final  Map<String, double>? _textPosition;
@override Map<String, double>? get textPosition {
  final value = _textPosition;
  if (value == null) return null;
  if (_textPosition is EqualUnmodifiableMapView) return _textPosition;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override final  int? backgroundColor;
@override final  String? filter;
 final  List<Map<String, dynamic>>? _drawingPoints;
@override List<Map<String, dynamic>>? get drawingPoints {
  final value = _drawingPoints;
  if (value == null) return null;
  if (_drawingPoints is EqualUnmodifiableListView) return _drawingPoints;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override final  int? drawingColor;
@override final  double? drawingWidth;
 final  List<TextElement> _textElements;
@override@JsonKey() List<TextElement> get textElements {
  if (_textElements is EqualUnmodifiableListView) return _textElements;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_textElements);
}

 final  List<StoryTag>? _tags;
@override List<StoryTag>? get tags {
  final value = _tags;
  if (value == null) return null;
  if (_tags is EqualUnmodifiableListView) return _tags;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  Map<String, dynamic>? _music;
@override Map<String, dynamic>? get music {
  final value = _music;
  if (value == null) return null;
  if (_music is EqualUnmodifiableMapView) return _music;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override final  String? musicArtist;
 final  Map<String, dynamic>? _location;
@override Map<String, dynamic>? get location {
  final value = _location;
  if (value == null) return null;
  if (_location is EqualUnmodifiableMapView) return _location;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override final  String? privacy;
@override final  bool? isPublic;
@override final  bool? isSeen;
@override final  bool? isCloseFriend;

/// Create a copy of StoryItem
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StoryItemCopyWith<_StoryItem> get copyWith => __$StoryItemCopyWithImpl<_StoryItem>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$StoryItemToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StoryItem&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.mediaUrl, mediaUrl) || other.mediaUrl == mediaUrl)&&(identical(other.mediaType, mediaType) || other.mediaType == mediaType)&&(identical(other.duration, duration) || other.duration == duration)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.textOverlay, textOverlay) || other.textOverlay == textOverlay)&&(identical(other.textColor, textColor) || other.textColor == textColor)&&(identical(other.textSize, textSize) || other.textSize == textSize)&&const DeepCollectionEquality().equals(other._textPosition, _textPosition)&&(identical(other.backgroundColor, backgroundColor) || other.backgroundColor == backgroundColor)&&(identical(other.filter, filter) || other.filter == filter)&&const DeepCollectionEquality().equals(other._drawingPoints, _drawingPoints)&&(identical(other.drawingColor, drawingColor) || other.drawingColor == drawingColor)&&(identical(other.drawingWidth, drawingWidth) || other.drawingWidth == drawingWidth)&&const DeepCollectionEquality().equals(other._textElements, _textElements)&&const DeepCollectionEquality().equals(other._tags, _tags)&&const DeepCollectionEquality().equals(other._music, _music)&&(identical(other.musicArtist, musicArtist) || other.musicArtist == musicArtist)&&const DeepCollectionEquality().equals(other._location, _location)&&(identical(other.privacy, privacy) || other.privacy == privacy)&&(identical(other.isPublic, isPublic) || other.isPublic == isPublic)&&(identical(other.isSeen, isSeen) || other.isSeen == isSeen)&&(identical(other.isCloseFriend, isCloseFriend) || other.isCloseFriend == isCloseFriend));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,userId,mediaUrl,mediaType,duration,timestamp,textOverlay,textColor,textSize,const DeepCollectionEquality().hash(_textPosition),backgroundColor,filter,const DeepCollectionEquality().hash(_drawingPoints),drawingColor,drawingWidth,const DeepCollectionEquality().hash(_textElements),const DeepCollectionEquality().hash(_tags),const DeepCollectionEquality().hash(_music),musicArtist,const DeepCollectionEquality().hash(_location),privacy,isPublic,isSeen,isCloseFriend]);

@override
String toString() {
  return 'StoryItem(id: $id, userId: $userId, mediaUrl: $mediaUrl, mediaType: $mediaType, duration: $duration, timestamp: $timestamp, textOverlay: $textOverlay, textColor: $textColor, textSize: $textSize, textPosition: $textPosition, backgroundColor: $backgroundColor, filter: $filter, drawingPoints: $drawingPoints, drawingColor: $drawingColor, drawingWidth: $drawingWidth, textElements: $textElements, tags: $tags, music: $music, musicArtist: $musicArtist, location: $location, privacy: $privacy, isPublic: $isPublic, isSeen: $isSeen, isCloseFriend: $isCloseFriend)';
}


}

/// @nodoc
abstract mixin class _$StoryItemCopyWith<$Res> implements $StoryItemCopyWith<$Res> {
  factory _$StoryItemCopyWith(_StoryItem value, $Res Function(_StoryItem) _then) = __$StoryItemCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, String mediaUrl, MediaType mediaType, Duration duration, DateTime timestamp, String? textOverlay, int? textColor, double? textSize, Map<String, double>? textPosition, int? backgroundColor, String? filter, List<Map<String, dynamic>>? drawingPoints, int? drawingColor, double? drawingWidth, List<TextElement> textElements, List<StoryTag>? tags, Map<String, dynamic>? music, String? musicArtist, Map<String, dynamic>? location, String? privacy, bool? isPublic, bool? isSeen, bool? isCloseFriend
});




}
/// @nodoc
class __$StoryItemCopyWithImpl<$Res>
    implements _$StoryItemCopyWith<$Res> {
  __$StoryItemCopyWithImpl(this._self, this._then);

  final _StoryItem _self;
  final $Res Function(_StoryItem) _then;

/// Create a copy of StoryItem
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? mediaUrl = null,Object? mediaType = null,Object? duration = null,Object? timestamp = null,Object? textOverlay = freezed,Object? textColor = freezed,Object? textSize = freezed,Object? textPosition = freezed,Object? backgroundColor = freezed,Object? filter = freezed,Object? drawingPoints = freezed,Object? drawingColor = freezed,Object? drawingWidth = freezed,Object? textElements = null,Object? tags = freezed,Object? music = freezed,Object? musicArtist = freezed,Object? location = freezed,Object? privacy = freezed,Object? isPublic = freezed,Object? isSeen = freezed,Object? isCloseFriend = freezed,}) {
  return _then(_StoryItem(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,mediaUrl: null == mediaUrl ? _self.mediaUrl : mediaUrl // ignore: cast_nullable_to_non_nullable
as String,mediaType: null == mediaType ? _self.mediaType : mediaType // ignore: cast_nullable_to_non_nullable
as MediaType,duration: null == duration ? _self.duration : duration // ignore: cast_nullable_to_non_nullable
as Duration,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,textOverlay: freezed == textOverlay ? _self.textOverlay : textOverlay // ignore: cast_nullable_to_non_nullable
as String?,textColor: freezed == textColor ? _self.textColor : textColor // ignore: cast_nullable_to_non_nullable
as int?,textSize: freezed == textSize ? _self.textSize : textSize // ignore: cast_nullable_to_non_nullable
as double?,textPosition: freezed == textPosition ? _self._textPosition : textPosition // ignore: cast_nullable_to_non_nullable
as Map<String, double>?,backgroundColor: freezed == backgroundColor ? _self.backgroundColor : backgroundColor // ignore: cast_nullable_to_non_nullable
as int?,filter: freezed == filter ? _self.filter : filter // ignore: cast_nullable_to_non_nullable
as String?,drawingPoints: freezed == drawingPoints ? _self._drawingPoints : drawingPoints // ignore: cast_nullable_to_non_nullable
as List<Map<String, dynamic>>?,drawingColor: freezed == drawingColor ? _self.drawingColor : drawingColor // ignore: cast_nullable_to_non_nullable
as int?,drawingWidth: freezed == drawingWidth ? _self.drawingWidth : drawingWidth // ignore: cast_nullable_to_non_nullable
as double?,textElements: null == textElements ? _self._textElements : textElements // ignore: cast_nullable_to_non_nullable
as List<TextElement>,tags: freezed == tags ? _self._tags : tags // ignore: cast_nullable_to_non_nullable
as List<StoryTag>?,music: freezed == music ? _self._music : music // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,musicArtist: freezed == musicArtist ? _self.musicArtist : musicArtist // ignore: cast_nullable_to_non_nullable
as String?,location: freezed == location ? _self._location : location // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,privacy: freezed == privacy ? _self.privacy : privacy // ignore: cast_nullable_to_non_nullable
as String?,isPublic: freezed == isPublic ? _self.isPublic : isPublic // ignore: cast_nullable_to_non_nullable
as bool?,isSeen: freezed == isSeen ? _self.isSeen : isSeen // ignore: cast_nullable_to_non_nullable
as bool?,isCloseFriend: freezed == isCloseFriend ? _self.isCloseFriend : isCloseFriend // ignore: cast_nullable_to_non_nullable
as bool?,
  ));
}


}

// dart format on
