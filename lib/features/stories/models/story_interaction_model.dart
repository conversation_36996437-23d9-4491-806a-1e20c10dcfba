import 'package:freezed_annotation/freezed_annotation.dart';

part 'story_interaction_model.freezed.dart';
part 'story_interaction_model.g.dart';

enum StoryReactionType { like, love, haha, wow, sad, angry }

enum StoryPrivacy { public, followers, closeFriends, custom, private }

@freezed
abstract class StoryReaction with _$StoryReaction {
  const factory StoryReaction({
    required String id,
    required String storyId,
    required String userId,
    required String username,
    required String userAvatarUrl,
    required StoryReactionType type,
    required DateTime timestamp,
  }) = _StoryReaction;

  factory StoryReaction.fromJson(Map<String, dynamic> json) =>
      _$StoryReactionFromJson(json);
}

@freezed
abstract class StoryReply with _$StoryReply {
  const factory StoryReply({
    required String id,
    required String storyId,
    required String userId,
    required String username,
    required String userAvatarUrl,
    required String content,
    String? mediaUrl,
    required DateTime timestamp,
    @Default(false) bool isRead,
    @Default(false) bool isReplied,
  }) = _StoryReply;

  factory StoryReply.fromJson(Map<String, dynamic> json) =>
      _$StoryReplyFromJson(json);
}

@freezed
abstract class StoryPrivacySettings with _$StoryPrivacySettings {
  const factory StoryPrivacySettings({
    @Default(StoryPrivacy.public) StoryPrivacy privacy,
    @Default([]) List<String> allowedViewers,
    @Default([]) List<String> blockedViewers,
    @Default(true) bool allowReplies,
    @Default(true) bool allowReactions,
    @Default(true) bool allowScreenshots,
    @Default(false) bool hideFromStory,
    @Default(false) bool muteStory,
  }) = _StoryPrivacySettings;

  factory StoryPrivacySettings.fromJson(Map<String, dynamic> json) =>
      _$StoryPrivacySettingsFromJson(json);
}

@freezed
abstract class StoryAnalytics with _$StoryAnalytics {
  const factory StoryAnalytics({
    required String storyId,
    required int viewCount,
    required int uniqueViewers,
    required int replyCount,
    required int reactionCount,
    required Map<StoryReactionType, int> reactionBreakdown,
    required Duration averageViewDuration,
    required DateTime createdAt,
    required DateTime? expiresAt,
    @Default([]) List<String> viewedBy,
    @Default([]) List<String> repliedBy,
    @Default([]) List<String> reactedBy,
  }) = _StoryAnalytics;

  factory StoryAnalytics.fromJson(Map<String, dynamic> json) =>
      _$StoryAnalyticsFromJson(json);
}

@freezed
abstract class StoryReport with _$StoryReport {
  const factory StoryReport({
    required String id,
    required String storyId,
    required String reportedBy,
    required String reason,
    String? additionalInfo,
    required DateTime timestamp,
    @Default('pending') String status, // pending, reviewed, resolved, dismissed
    String? reviewedBy,
    DateTime? reviewedAt,
    String? action, // warn, delete, ban, none
  }) = _StoryReport;

  factory StoryReport.fromJson(Map<String, dynamic> json) =>
      _$StoryReportFromJson(json);
}

enum StoryReportReason {
  inappropriate,
  harassment,
  spam,
  fakeNews,
  violence,
  copyright,
  other,
}
