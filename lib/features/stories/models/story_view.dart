import 'package:cloud_firestore/cloud_firestore.dart';

/// Represents a single view of a story with detailed viewer information
class StoryView {
  final String id;
  final String storyId;
  final String userId;
  final String username;
  final String displayName;
  final String? profilePictureUrl;
  final DateTime viewedAt;
  final Duration viewDuration;
  final bool completed;
  final String? deviceInfo;
  final String? location;
  final bool isFollower;
  final bool isFollowing;
  final bool isCloseFriend;
  final bool isMutualFollower;

  const StoryView({
    required this.id,
    required this.storyId,
    required this.userId,
    required this.username,
    required this.displayName,
    this.profilePictureUrl,
    required this.viewedAt,
    required this.viewDuration,
    required this.completed,
    this.deviceInfo,
    this.location,
    this.isFollower = false,
    this.isFollowing = false,
    this.isCloseFriend = false,
    this.isMutualFollower = false,
  });

  /// Create from Firestore document
  factory StoryView.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return StoryView(
      id: doc.id,
      storyId: data['storyId'] ?? '',
      userId: data['userId'] ?? '',
      username: data['username'] ?? '',
      displayName: data['displayName'] ?? '',
      profilePictureUrl: data['profilePictureUrl'],
      viewedAt: (data['viewedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      viewDuration: Duration(seconds: data['viewDuration'] ?? 0),
      completed: data['completed'] ?? false,
      deviceInfo: data['deviceInfo'],
      location: data['location'],
      isFollower: data['isFollower'] ?? false,
      isFollowing: data['isFollowing'] ?? false,
      isCloseFriend: data['isCloseFriend'] ?? false,
      isMutualFollower: data['isMutualFollower'] ?? false,
    );
  }

  /// Create from JSON
  factory StoryView.fromJson(Map<String, dynamic> json) {
    return StoryView(
      id: json['id'] ?? '',
      storyId: json['storyId'] ?? '',
      userId: json['userId'] ?? '',
      username: json['username'] ?? '',
      displayName: json['displayName'] ?? '',
      profilePictureUrl: json['profilePictureUrl'],
      viewedAt: DateTime.parse(
        json['viewedAt'] ?? DateTime.now().toIso8601String(),
      ),
      viewDuration: Duration(seconds: json['viewDuration'] ?? 0),
      completed: json['completed'] ?? false,
      deviceInfo: json['deviceInfo'],
      location: json['location'],
      isFollower: json['isFollower'] ?? false,
      isFollowing: json['isFollowing'] ?? false,
      isCloseFriend: json['isCloseFriend'] ?? false,
      isMutualFollower: json['isMutualFollower'] ?? false,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'storyId': storyId,
      'userId': userId,
      'username': username,
      'displayName': displayName,
      'profilePictureUrl': profilePictureUrl,
      'viewedAt': viewedAt.toIso8601String(),
      'viewDuration': viewDuration.inSeconds,
      'completed': completed,
      'deviceInfo': deviceInfo,
      'location': location,
      'isFollower': isFollower,
      'isFollowing': isFollowing,
      'isCloseFriend': isCloseFriend,
      'isMutualFollower': isMutualFollower,
    };
  }
}

/// Extension for Firestore operations
extension StoryViewFirestore on StoryView {
  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'storyId': storyId,
      'userId': userId,
      'username': username,
      'displayName': displayName,
      'profilePictureUrl': profilePictureUrl,
      'viewedAt': Timestamp.fromDate(viewedAt),
      'viewDuration': viewDuration.inSeconds,
      'completed': completed,
      'deviceInfo': deviceInfo,
      'location': location,
      'isFollower': isFollower,
      'isFollowing': isFollowing,
      'isCloseFriend': isCloseFriend,
      'isMutualFollower': isMutualFollower,
    };
  }
}

/// Aggregated story viewer statistics
class StoryViewerStats {
  final String storyId;
  final int totalViews;
  final int uniqueViewers;
  final List<StoryView> recentViewers;
  final List<StoryView> allViewers;
  final DateTime lastViewedAt;
  final Duration averageViewDuration;
  final double completionRate;
  final int followerViews;
  final int nonFollowerViews;
  final int closeFriendViews;

  const StoryViewerStats({
    required this.storyId,
    required this.totalViews,
    required this.uniqueViewers,
    required this.recentViewers,
    required this.allViewers,
    required this.lastViewedAt,
    required this.averageViewDuration,
    required this.completionRate,
    this.followerViews = 0,
    this.nonFollowerViews = 0,
    this.closeFriendViews = 0,
  });

  factory StoryViewerStats.fromJson(Map<String, dynamic> json) {
    return StoryViewerStats(
      storyId: json['storyId'] ?? '',
      totalViews: json['totalViews'] ?? 0,
      uniqueViewers: json['uniqueViewers'] ?? 0,
      recentViewers:
          (json['recentViewers'] as List<dynamic>?)
              ?.map((e) => StoryView.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      allViewers:
          (json['allViewers'] as List<dynamic>?)
              ?.map((e) => StoryView.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      lastViewedAt: DateTime.parse(
        json['lastViewedAt'] ?? DateTime.now().toIso8601String(),
      ),
      averageViewDuration: Duration(seconds: json['averageViewDuration'] ?? 0),
      completionRate: (json['completionRate'] ?? 0.0).toDouble(),
      followerViews: json['followerViews'] ?? 0,
      nonFollowerViews: json['nonFollowerViews'] ?? 0,
      closeFriendViews: json['closeFriendViews'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'storyId': storyId,
      'totalViews': totalViews,
      'uniqueViewers': uniqueViewers,
      'recentViewers': recentViewers.map((e) => e.toJson()).toList(),
      'allViewers': allViewers.map((e) => e.toJson()).toList(),
      'lastViewedAt': lastViewedAt.toIso8601String(),
      'averageViewDuration': averageViewDuration.inSeconds,
      'completionRate': completionRate,
      'followerViews': followerViews,
      'nonFollowerViews': nonFollowerViews,
      'closeFriendViews': closeFriendViews,
    };
  }
}

/// Viewer sorting options
enum ViewerSortType {
  mostRecent,
  oldest,
  followersFirst,
  closeFriendsFirst,
  alphabetical,
  viewDuration,
}

/// Viewer filter options
enum ViewerFilterType {
  all,
  followers,
  nonFollowers,
  closeFriends,
  mutualFollowers,
  completed,
  incomplete,
}
