// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'story_settings_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$StorySettings {

 String get id; StoryDuration get duration; StoryVisibility get visibility; List<String> get hiddenFromUserIds; bool get allowEditing; bool get allowComments; bool get allowReactions; bool get allowMentions; bool get allowMusic; bool get allowFilters; bool get allowTextOverlays; bool get allowVideoUpload; List<String> get allowedGroupIds;
/// Create a copy of StorySettings
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StorySettingsCopyWith<StorySettings> get copyWith => _$StorySettingsCopyWithImpl<StorySettings>(this as StorySettings, _$identity);

  /// Serializes this StorySettings to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StorySettings&&(identical(other.id, id) || other.id == id)&&(identical(other.duration, duration) || other.duration == duration)&&(identical(other.visibility, visibility) || other.visibility == visibility)&&const DeepCollectionEquality().equals(other.hiddenFromUserIds, hiddenFromUserIds)&&(identical(other.allowEditing, allowEditing) || other.allowEditing == allowEditing)&&(identical(other.allowComments, allowComments) || other.allowComments == allowComments)&&(identical(other.allowReactions, allowReactions) || other.allowReactions == allowReactions)&&(identical(other.allowMentions, allowMentions) || other.allowMentions == allowMentions)&&(identical(other.allowMusic, allowMusic) || other.allowMusic == allowMusic)&&(identical(other.allowFilters, allowFilters) || other.allowFilters == allowFilters)&&(identical(other.allowTextOverlays, allowTextOverlays) || other.allowTextOverlays == allowTextOverlays)&&(identical(other.allowVideoUpload, allowVideoUpload) || other.allowVideoUpload == allowVideoUpload)&&const DeepCollectionEquality().equals(other.allowedGroupIds, allowedGroupIds));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,duration,visibility,const DeepCollectionEquality().hash(hiddenFromUserIds),allowEditing,allowComments,allowReactions,allowMentions,allowMusic,allowFilters,allowTextOverlays,allowVideoUpload,const DeepCollectionEquality().hash(allowedGroupIds));

@override
String toString() {
  return 'StorySettings(id: $id, duration: $duration, visibility: $visibility, hiddenFromUserIds: $hiddenFromUserIds, allowEditing: $allowEditing, allowComments: $allowComments, allowReactions: $allowReactions, allowMentions: $allowMentions, allowMusic: $allowMusic, allowFilters: $allowFilters, allowTextOverlays: $allowTextOverlays, allowVideoUpload: $allowVideoUpload, allowedGroupIds: $allowedGroupIds)';
}


}

/// @nodoc
abstract mixin class $StorySettingsCopyWith<$Res>  {
  factory $StorySettingsCopyWith(StorySettings value, $Res Function(StorySettings) _then) = _$StorySettingsCopyWithImpl;
@useResult
$Res call({
 String id, StoryDuration duration, StoryVisibility visibility, List<String> hiddenFromUserIds, bool allowEditing, bool allowComments, bool allowReactions, bool allowMentions, bool allowMusic, bool allowFilters, bool allowTextOverlays, bool allowVideoUpload, List<String> allowedGroupIds
});




}
/// @nodoc
class _$StorySettingsCopyWithImpl<$Res>
    implements $StorySettingsCopyWith<$Res> {
  _$StorySettingsCopyWithImpl(this._self, this._then);

  final StorySettings _self;
  final $Res Function(StorySettings) _then;

/// Create a copy of StorySettings
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? duration = null,Object? visibility = null,Object? hiddenFromUserIds = null,Object? allowEditing = null,Object? allowComments = null,Object? allowReactions = null,Object? allowMentions = null,Object? allowMusic = null,Object? allowFilters = null,Object? allowTextOverlays = null,Object? allowVideoUpload = null,Object? allowedGroupIds = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,duration: null == duration ? _self.duration : duration // ignore: cast_nullable_to_non_nullable
as StoryDuration,visibility: null == visibility ? _self.visibility : visibility // ignore: cast_nullable_to_non_nullable
as StoryVisibility,hiddenFromUserIds: null == hiddenFromUserIds ? _self.hiddenFromUserIds : hiddenFromUserIds // ignore: cast_nullable_to_non_nullable
as List<String>,allowEditing: null == allowEditing ? _self.allowEditing : allowEditing // ignore: cast_nullable_to_non_nullable
as bool,allowComments: null == allowComments ? _self.allowComments : allowComments // ignore: cast_nullable_to_non_nullable
as bool,allowReactions: null == allowReactions ? _self.allowReactions : allowReactions // ignore: cast_nullable_to_non_nullable
as bool,allowMentions: null == allowMentions ? _self.allowMentions : allowMentions // ignore: cast_nullable_to_non_nullable
as bool,allowMusic: null == allowMusic ? _self.allowMusic : allowMusic // ignore: cast_nullable_to_non_nullable
as bool,allowFilters: null == allowFilters ? _self.allowFilters : allowFilters // ignore: cast_nullable_to_non_nullable
as bool,allowTextOverlays: null == allowTextOverlays ? _self.allowTextOverlays : allowTextOverlays // ignore: cast_nullable_to_non_nullable
as bool,allowVideoUpload: null == allowVideoUpload ? _self.allowVideoUpload : allowVideoUpload // ignore: cast_nullable_to_non_nullable
as bool,allowedGroupIds: null == allowedGroupIds ? _self.allowedGroupIds : allowedGroupIds // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}

}


/// Adds pattern-matching-related methods to [StorySettings].
extension StorySettingsPatterns on StorySettings {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _StorySettings value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _StorySettings() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _StorySettings value)  $default,){
final _that = this;
switch (_that) {
case _StorySettings():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _StorySettings value)?  $default,){
final _that = this;
switch (_that) {
case _StorySettings() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  StoryDuration duration,  StoryVisibility visibility,  List<String> hiddenFromUserIds,  bool allowEditing,  bool allowComments,  bool allowReactions,  bool allowMentions,  bool allowMusic,  bool allowFilters,  bool allowTextOverlays,  bool allowVideoUpload,  List<String> allowedGroupIds)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _StorySettings() when $default != null:
return $default(_that.id,_that.duration,_that.visibility,_that.hiddenFromUserIds,_that.allowEditing,_that.allowComments,_that.allowReactions,_that.allowMentions,_that.allowMusic,_that.allowFilters,_that.allowTextOverlays,_that.allowVideoUpload,_that.allowedGroupIds);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  StoryDuration duration,  StoryVisibility visibility,  List<String> hiddenFromUserIds,  bool allowEditing,  bool allowComments,  bool allowReactions,  bool allowMentions,  bool allowMusic,  bool allowFilters,  bool allowTextOverlays,  bool allowVideoUpload,  List<String> allowedGroupIds)  $default,) {final _that = this;
switch (_that) {
case _StorySettings():
return $default(_that.id,_that.duration,_that.visibility,_that.hiddenFromUserIds,_that.allowEditing,_that.allowComments,_that.allowReactions,_that.allowMentions,_that.allowMusic,_that.allowFilters,_that.allowTextOverlays,_that.allowVideoUpload,_that.allowedGroupIds);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  StoryDuration duration,  StoryVisibility visibility,  List<String> hiddenFromUserIds,  bool allowEditing,  bool allowComments,  bool allowReactions,  bool allowMentions,  bool allowMusic,  bool allowFilters,  bool allowTextOverlays,  bool allowVideoUpload,  List<String> allowedGroupIds)?  $default,) {final _that = this;
switch (_that) {
case _StorySettings() when $default != null:
return $default(_that.id,_that.duration,_that.visibility,_that.hiddenFromUserIds,_that.allowEditing,_that.allowComments,_that.allowReactions,_that.allowMentions,_that.allowMusic,_that.allowFilters,_that.allowTextOverlays,_that.allowVideoUpload,_that.allowedGroupIds);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _StorySettings implements StorySettings {
  const _StorySettings({required this.id, required this.duration, required this.visibility, required final  List<String> hiddenFromUserIds, required this.allowEditing, required this.allowComments, required this.allowReactions, required this.allowMentions, required this.allowMusic, required this.allowFilters, required this.allowTextOverlays, required this.allowVideoUpload, required final  List<String> allowedGroupIds}): _hiddenFromUserIds = hiddenFromUserIds,_allowedGroupIds = allowedGroupIds;
  factory _StorySettings.fromJson(Map<String, dynamic> json) => _$StorySettingsFromJson(json);

@override final  String id;
@override final  StoryDuration duration;
@override final  StoryVisibility visibility;
 final  List<String> _hiddenFromUserIds;
@override List<String> get hiddenFromUserIds {
  if (_hiddenFromUserIds is EqualUnmodifiableListView) return _hiddenFromUserIds;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_hiddenFromUserIds);
}

@override final  bool allowEditing;
@override final  bool allowComments;
@override final  bool allowReactions;
@override final  bool allowMentions;
@override final  bool allowMusic;
@override final  bool allowFilters;
@override final  bool allowTextOverlays;
@override final  bool allowVideoUpload;
 final  List<String> _allowedGroupIds;
@override List<String> get allowedGroupIds {
  if (_allowedGroupIds is EqualUnmodifiableListView) return _allowedGroupIds;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_allowedGroupIds);
}


/// Create a copy of StorySettings
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StorySettingsCopyWith<_StorySettings> get copyWith => __$StorySettingsCopyWithImpl<_StorySettings>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$StorySettingsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StorySettings&&(identical(other.id, id) || other.id == id)&&(identical(other.duration, duration) || other.duration == duration)&&(identical(other.visibility, visibility) || other.visibility == visibility)&&const DeepCollectionEquality().equals(other._hiddenFromUserIds, _hiddenFromUserIds)&&(identical(other.allowEditing, allowEditing) || other.allowEditing == allowEditing)&&(identical(other.allowComments, allowComments) || other.allowComments == allowComments)&&(identical(other.allowReactions, allowReactions) || other.allowReactions == allowReactions)&&(identical(other.allowMentions, allowMentions) || other.allowMentions == allowMentions)&&(identical(other.allowMusic, allowMusic) || other.allowMusic == allowMusic)&&(identical(other.allowFilters, allowFilters) || other.allowFilters == allowFilters)&&(identical(other.allowTextOverlays, allowTextOverlays) || other.allowTextOverlays == allowTextOverlays)&&(identical(other.allowVideoUpload, allowVideoUpload) || other.allowVideoUpload == allowVideoUpload)&&const DeepCollectionEquality().equals(other._allowedGroupIds, _allowedGroupIds));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,duration,visibility,const DeepCollectionEquality().hash(_hiddenFromUserIds),allowEditing,allowComments,allowReactions,allowMentions,allowMusic,allowFilters,allowTextOverlays,allowVideoUpload,const DeepCollectionEquality().hash(_allowedGroupIds));

@override
String toString() {
  return 'StorySettings(id: $id, duration: $duration, visibility: $visibility, hiddenFromUserIds: $hiddenFromUserIds, allowEditing: $allowEditing, allowComments: $allowComments, allowReactions: $allowReactions, allowMentions: $allowMentions, allowMusic: $allowMusic, allowFilters: $allowFilters, allowTextOverlays: $allowTextOverlays, allowVideoUpload: $allowVideoUpload, allowedGroupIds: $allowedGroupIds)';
}


}

/// @nodoc
abstract mixin class _$StorySettingsCopyWith<$Res> implements $StorySettingsCopyWith<$Res> {
  factory _$StorySettingsCopyWith(_StorySettings value, $Res Function(_StorySettings) _then) = __$StorySettingsCopyWithImpl;
@override @useResult
$Res call({
 String id, StoryDuration duration, StoryVisibility visibility, List<String> hiddenFromUserIds, bool allowEditing, bool allowComments, bool allowReactions, bool allowMentions, bool allowMusic, bool allowFilters, bool allowTextOverlays, bool allowVideoUpload, List<String> allowedGroupIds
});




}
/// @nodoc
class __$StorySettingsCopyWithImpl<$Res>
    implements _$StorySettingsCopyWith<$Res> {
  __$StorySettingsCopyWithImpl(this._self, this._then);

  final _StorySettings _self;
  final $Res Function(_StorySettings) _then;

/// Create a copy of StorySettings
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? duration = null,Object? visibility = null,Object? hiddenFromUserIds = null,Object? allowEditing = null,Object? allowComments = null,Object? allowReactions = null,Object? allowMentions = null,Object? allowMusic = null,Object? allowFilters = null,Object? allowTextOverlays = null,Object? allowVideoUpload = null,Object? allowedGroupIds = null,}) {
  return _then(_StorySettings(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,duration: null == duration ? _self.duration : duration // ignore: cast_nullable_to_non_nullable
as StoryDuration,visibility: null == visibility ? _self.visibility : visibility // ignore: cast_nullable_to_non_nullable
as StoryVisibility,hiddenFromUserIds: null == hiddenFromUserIds ? _self._hiddenFromUserIds : hiddenFromUserIds // ignore: cast_nullable_to_non_nullable
as List<String>,allowEditing: null == allowEditing ? _self.allowEditing : allowEditing // ignore: cast_nullable_to_non_nullable
as bool,allowComments: null == allowComments ? _self.allowComments : allowComments // ignore: cast_nullable_to_non_nullable
as bool,allowReactions: null == allowReactions ? _self.allowReactions : allowReactions // ignore: cast_nullable_to_non_nullable
as bool,allowMentions: null == allowMentions ? _self.allowMentions : allowMentions // ignore: cast_nullable_to_non_nullable
as bool,allowMusic: null == allowMusic ? _self.allowMusic : allowMusic // ignore: cast_nullable_to_non_nullable
as bool,allowFilters: null == allowFilters ? _self.allowFilters : allowFilters // ignore: cast_nullable_to_non_nullable
as bool,allowTextOverlays: null == allowTextOverlays ? _self.allowTextOverlays : allowTextOverlays // ignore: cast_nullable_to_non_nullable
as bool,allowVideoUpload: null == allowVideoUpload ? _self.allowVideoUpload : allowVideoUpload // ignore: cast_nullable_to_non_nullable
as bool,allowedGroupIds: null == allowedGroupIds ? _self._allowedGroupIds : allowedGroupIds // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}


}

// dart format on
