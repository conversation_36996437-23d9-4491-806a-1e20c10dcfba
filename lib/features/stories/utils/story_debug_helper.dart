import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

class StoryDebugHelper {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Debug method to check if stories exist in Firestore
  static Future<void> debugStoryCreation() async {
    if (!kDebugMode) return;

    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        debugPrint('🚫 No authenticated user found');
        return;
      }

      debugPrint('🔍 === STORY DEBUG REPORT ===');
      debugPrint('👤 Current User ID: ${currentUser.uid}');

      // Check all stories in Firestore
      final allStoriesSnapshot = await _firestore
          .collection('stories')
          .orderBy('createdAt', descending: true)
          .limit(20)
          .get();

      debugPrint('📊 Total stories in Firestore: ${allStoriesSnapshot.docs.length}');

      if (allStoriesSnapshot.docs.isEmpty) {
        debugPrint('📭 No stories found in Firestore');
        return;
      }

      // Check user's own stories
      final userStoriesSnapshot = await _firestore
          .collection('stories')
          .where('userId', isEqualTo: currentUser.uid)
          .orderBy('createdAt', descending: true)
          .get();

      debugPrint('👤 User\'s stories: ${userStoriesSnapshot.docs.length}');

      // Analyze each story
      for (int i = 0; i < allStoriesSnapshot.docs.length; i++) {
        final doc = allStoriesSnapshot.docs[i];
        final data = doc.data();
        
        final userId = data['userId'] as String?;
        final mediaUrl = data['mediaUrl'] as String?;
        final mediaType = data['mediaType'] as String?;
        final createdAt = data['createdAt'] as Timestamp?;
        final isPublic = data['isPublic'] as bool?;
        final isCloseFriend = data['isCloseFriend'] as bool?;
        
        final isOwnStory = userId == currentUser.uid;
        final createdTime = createdAt?.toDate();
        final timeAgo = createdTime != null 
            ? DateTime.now().difference(createdTime).inMinutes
            : null;

        debugPrint('📱 Story ${i + 1}:');
        debugPrint('   ID: ${doc.id}');
        debugPrint('   User: $userId ${isOwnStory ? "(YOU)" : ""}');
        debugPrint('   Media URL: ${mediaUrl?.isNotEmpty == true ? "✅ Valid" : "❌ Empty/Invalid"}');
        debugPrint('   Media Type: $mediaType');
        debugPrint('   Created: ${timeAgo != null ? "$timeAgo minutes ago" : "Unknown"}');
        debugPrint('   Public: $isPublic');
        debugPrint('   Close Friend: $isCloseFriend');
        debugPrint('   ---');
      }

      // Check recent stories (last 24 hours)
      final twentyFourHoursAgo = DateTime.now().subtract(const Duration(hours: 24));
      final recentStoriesSnapshot = await _firestore
          .collection('stories')
          .where('createdAt', isGreaterThan: Timestamp.fromDate(twentyFourHoursAgo))
          .get();

      debugPrint('⏰ Stories in last 24 hours: ${recentStoriesSnapshot.docs.length}');

      // Check stories with valid media URLs
      final validStories = allStoriesSnapshot.docs.where((doc) {
        final data = doc.data();
        final mediaUrl = data['mediaUrl'] as String?;
        return mediaUrl != null && mediaUrl.trim().isNotEmpty;
      }).toList();

      debugPrint('✅ Stories with valid media URLs: ${validStories.length}');

      debugPrint('🔍 === END DEBUG REPORT ===');

    } catch (e) {
      debugPrint('❌ Error in story debug: $e');
    }
  }

  /// Debug method to check story provider state
  static void debugProviderState(List<dynamic> reels) {
    if (!kDebugMode) return;

    debugPrint('🔍 === PROVIDER DEBUG ===');
    debugPrint('📊 Provider returned ${reels.length} reels');

    final currentUser = _auth.currentUser;
    if (currentUser != null) {
      final ownReel = reels.where((reel) => reel.userId == currentUser.uid).toList();
      debugPrint('👤 Own reels: ${ownReel.length}');
      
      if (ownReel.isNotEmpty) {
        final reel = ownReel.first;
        debugPrint('   Stories in own reel: ${reel.stories?.length ?? 0}');
      }
    }

    debugPrint('🔍 === END PROVIDER DEBUG ===');
  }

  /// Create a test story for debugging
  static Future<void> createTestStory() async {
    if (!kDebugMode) return;

    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        debugPrint('🚫 Cannot create test story: No authenticated user');
        return;
      }

      debugPrint('🧪 Creating test story...');

      final testStoryData = {
        'userId': currentUser.uid,
        'mediaUrl': 'https://picsum.photos/seed/${DateTime.now().millisecondsSinceEpoch}/400/600',
        'mediaType': 'image',
        'textOverlay': 'Test Story ${DateTime.now().millisecondsSinceEpoch}',
        'textColor': **********, // White
        'textSize': 24.0,
        'textPosition': {'x': 100, 'y': 100},
        'isPublic': true,
        'isSeen': false,
        'isCloseFriend': false,
        'createdAt': FieldValue.serverTimestamp(),
        'expiresAt': DateTime.now().add(const Duration(hours: 24)),
        'viewCount': 0,
        'viewedBy': [],
        'isActive': true,
      };

      final docRef = await _firestore.collection('stories').add(testStoryData);
      debugPrint('✅ Test story created with ID: ${docRef.id}');

      // Wait a moment and then check if it appears
      await Future.delayed(const Duration(seconds: 2));
      await debugStoryCreation();

    } catch (e) {
      debugPrint('❌ Error creating test story: $e');
    }
  }

  /// Check Firestore rules and permissions
  static Future<void> debugFirestoreAccess() async {
    if (!kDebugMode) return;

    try {
      debugPrint('🔍 === FIRESTORE ACCESS DEBUG ===');

      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        debugPrint('🚫 No authenticated user');
        return;
      }

      // Test read access
      try {
        final testRead = await _firestore.collection('stories').limit(1).get();
        debugPrint('✅ Read access: OK (${testRead.docs.length} docs)');
      } catch (e) {
        debugPrint('❌ Read access: FAILED - $e');
      }

      // Test write access
      try {
        final testDoc = _firestore.collection('stories').doc('test_${DateTime.now().millisecondsSinceEpoch}');
        await testDoc.set({
          'test': true,
          'userId': currentUser.uid,
          'createdAt': FieldValue.serverTimestamp(),
        });
        await testDoc.delete(); // Clean up
        debugPrint('✅ Write access: OK');
      } catch (e) {
        debugPrint('❌ Write access: FAILED - $e');
      }

      debugPrint('🔍 === END FIRESTORE ACCESS DEBUG ===');

    } catch (e) {
      debugPrint('❌ Error in Firestore access debug: $e');
    }
  }
}
