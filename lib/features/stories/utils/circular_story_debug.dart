import 'package:flutter/foundation.dart';
import 'package:billionaires_social/features/stories/models/story_reel_model.dart';

/// Debug helper for circular story carousel troubleshooting
class CircularStoryDebug {
  /// Debug story reels data
  static void debugStoryReels(List<StoryReel> reels) {
    if (!kDebugMode) return;
    
    debugPrint('🎯 === CIRCULAR STORY DEBUG ===');
    debugPrint('Story reels count: ${reels.length}');
    
    if (reels.isEmpty) {
      debugPrint('❌ No story reels found!');
      debugPrint('   This could be why the circular carousel is not appearing.');
      debugPrint('   Solutions:');
      debugPrint('   1. Create some test stories');
      debugPrint('   2. Check if storyReelsProvider is working');
      debugPrint('   3. Verify Firestore connection');
      return;
    }
    
    for (int i = 0; i < reels.length; i++) {
      final reel = reels[i];
      debugPrint('📱 Reel $i:');
      debugPrint('   ID: ${reel.id}');
      debugPrint('   Username: ${reel.username}');
      debugPrint('   Stories: ${reel.stories.length}');
      debugPrint('   Avatar: ${reel.userAvatarUrl.isNotEmpty ? "✅" : "❌"}');
      debugPrint('   Close Friend: ${reel.isCloseFriend}');
      debugPrint('   All Viewed: ${reel.isAllViewed}');
    }
    
    debugPrint('🎯 === END CIRCULAR STORY DEBUG ===');
  }

  /// Debug circular carousel widget state
  static void debugCarouselState({
    required List<StoryReel> storyReels,
    required double radius,
    required double itemSize,
    String? centerAvatarUrl,
    String? centerUsername,
  }) {
    if (!kDebugMode) return;
    
    debugPrint('🎯 === CIRCULAR CAROUSEL STATE ===');
    debugPrint('Story reels: ${storyReels.length}');
    debugPrint('Radius: $radius');
    debugPrint('Item size: $itemSize');
    debugPrint('Center avatar: ${centerAvatarUrl ?? "None"}');
    debugPrint('Center username: ${centerUsername ?? "None"}');
    debugPrint('Widget size: ${radius * 2} x ${radius * 2}');
    
    if (storyReels.isEmpty) {
      debugPrint('⚠️ Empty story reels - only center avatar will show');
    } else {
      debugPrint('✅ ${storyReels.length} stories will be arranged in circle');
    }
    
    debugPrint('🎯 === END CAROUSEL STATE ===');
  }

  /// Debug story provider state
  static void debugProviderState(String providerName, dynamic state) {
    if (!kDebugMode) return;
    
    debugPrint('🎯 === PROVIDER DEBUG: $providerName ===');
    debugPrint('State type: ${state.runtimeType}');
    debugPrint('State: $state');
    debugPrint('🎯 === END PROVIDER DEBUG ===');
  }

  /// Debug circular story rendering
  static void debugStoryRendering({
    required int storyIndex,
    required String storyId,
    required String username,
    required double angle,
    required double x,
    required double y,
    required bool isSelected,
  }) {
    if (!kDebugMode) return;
    
    debugPrint('🎯 Rendering story $storyIndex ($username):');
    debugPrint('   ID: $storyId');
    debugPrint('   Angle: ${(angle * 180 / 3.14159).toStringAsFixed(1)}°');
    debugPrint('   Position: (${x.toStringAsFixed(1)}, ${y.toStringAsFixed(1)})');
    debugPrint('   Selected: $isSelected');
  }

  /// Debug gesture handling
  static void debugGesture({
    required String gestureType,
    required double rotation,
    required int selectedIndex,
    required bool isDragging,
  }) {
    if (!kDebugMode) return;
    
    debugPrint('🎯 Gesture: $gestureType');
    debugPrint('   Rotation: ${(rotation * 180 / 3.14159).toStringAsFixed(1)}°');
    debugPrint('   Selected: $selectedIndex');
    debugPrint('   Dragging: $isDragging');
  }

  /// Debug story tap
  static void debugStoryTap({
    required String storyId,
    required String username,
    required int index,
  }) {
    if (!kDebugMode) return;
    
    debugPrint('🎯 Story tapped:');
    debugPrint('   Story: $storyId');
    debugPrint('   User: $username');
    debugPrint('   Index: $index');
  }

  /// Debug center tap
  static void debugCenterTap() {
    if (!kDebugMode) return;
    
    debugPrint('🎯 Center avatar tapped - should navigate to story creation');
  }

  /// Debug story border colors
  static void debugStoryBorderColor({
    required String username,
    required String colorName,
    required String colorHex,
    required bool isViewed,
    required bool isCloseFriend,
  }) {
    if (!kDebugMode) return;
    
    debugPrint('🎯 Story border for $username:');
    debugPrint('   Color: $colorName ($colorHex)');
    debugPrint('   Viewed: $isViewed');
    debugPrint('   Close Friend: $isCloseFriend');
  }

  /// Debug animation state
  static void debugAnimation({
    required String animationType,
    required double value,
    required bool isRunning,
  }) {
    if (!kDebugMode) return;
    
    debugPrint('🎯 Animation: $animationType');
    debugPrint('   Value: ${value.toStringAsFixed(3)}');
    debugPrint('   Running: $isRunning');
  }

  /// Comprehensive troubleshooting guide
  static void showTroubleshootingGuide() {
    if (!kDebugMode) return;
    
    debugPrint('🎯 === CIRCULAR STORY TROUBLESHOOTING ===');
    debugPrint('');
    debugPrint('If circular carousel is not appearing:');
    debugPrint('');
    debugPrint('1. CHECK STORY DATA:');
    debugPrint('   • Run: CircularStoryDebug.debugStoryReels(reels)');
    debugPrint('   • Verify storyReelsProvider returns data');
    debugPrint('   • Check Firestore for story documents');
    debugPrint('');
    debugPrint('2. CHECK WIDGET INTEGRATION:');
    debugPrint('   • Ensure CircularStoryCarousel is in widget tree');
    debugPrint('   • Check if parent container has proper size');
    debugPrint('   • Verify no overlapping widgets');
    debugPrint('');
    debugPrint('3. CHECK PROVIDER STATE:');
    debugPrint('   • Watch storyReelsProvider.when() states');
    debugPrint('   • Check loading/error states');
    debugPrint('   • Verify ref.watch() is working');
    debugPrint('');
    debugPrint('4. CHECK RENDERING:');
    debugPrint('   • Enable debugStoryRendering() calls');
    debugPrint('   • Check console for rendering logs');
    debugPrint('   • Verify story positions are calculated');
    debugPrint('');
    debugPrint('5. TEST WITH MOCK DATA:');
    debugPrint('   • Use CircularStoryTest widget');
    debugPrint('   • Create test story reels');
    debugPrint('   • Verify carousel works with test data');
    debugPrint('');
    debugPrint('6. CHECK GESTURES:');
    debugPrint('   • Enable debugGesture() calls');
    debugPrint('   • Test drag/tap interactions');
    debugPrint('   • Verify GestureDetector is working');
    debugPrint('');
    debugPrint('🎯 === END TROUBLESHOOTING ===');
  }

  /// Quick diagnostic check
  static void runQuickDiagnostic(List<StoryReel> reels) {
    if (!kDebugMode) return;
    
    debugPrint('🎯 === QUICK DIAGNOSTIC ===');
    
    // Check story data
    if (reels.isEmpty) {
      debugPrint('❌ ISSUE: No story reels found');
      debugPrint('   Solution: Create stories or use test data');
    } else {
      debugPrint('✅ Story reels: ${reels.length} found');
    }
    
    // Check story content
    int validStories = 0;
    for (final reel in reels) {
      if (reel.userAvatarUrl.isNotEmpty && reel.stories.isNotEmpty) {
        validStories++;
      }
    }
    
    if (validStories == 0) {
      debugPrint('❌ ISSUE: No valid stories with avatars');
      debugPrint('   Solution: Check story data integrity');
    } else {
      debugPrint('✅ Valid stories: $validStories/$reels.length');
    }
    
    // Check user authentication
    debugPrint('ℹ️ Check Firebase Auth for current user');
    debugPrint('ℹ️ Check profile provider for user data');
    
    debugPrint('🎯 === END DIAGNOSTIC ===');
  }
}
