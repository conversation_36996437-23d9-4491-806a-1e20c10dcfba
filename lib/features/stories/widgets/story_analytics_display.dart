import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/stories/services/story_interaction_service.dart';
import 'package:billionaires_social/features/stories/models/story_interaction_model.dart';
import 'package:billionaires_social/features/stories/screens/story_viewers_screen.dart';
import 'package:get_it/get_it.dart';

class StoryAnalyticsDisplay extends ConsumerStatefulWidget {
  final String storyId;
  final bool showDetailed;
  final VoidCallback? onClose;

  const StoryAnalyticsDisplay({
    super.key,
    required this.storyId,
    this.showDetailed = false,
    this.onClose,
  });

  @override
  ConsumerState<StoryAnalyticsDisplay> createState() =>
      _StoryAnalyticsDisplayState();
}

class _StoryAnalyticsDisplayState extends ConsumerState<StoryAnalyticsDisplay> {
  final StoryInteractionService _interactionService =
      GetIt.I<StoryInteractionService>();
  StoryAnalytics? _analytics;
  bool _isLoading = true;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _loadAnalytics();
  }

  void _openViewersScreen() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StoryViewersScreen(
          storyId: widget.storyId,
          storyTitle: 'Story Viewers',
        ),
      ),
    );
  }

  Future<void> _loadAnalytics() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      debugPrint('📊 Loading enhanced analytics for story: ${widget.storyId}');
      final analytics = await _interactionService.getStoryAnalytics(
        widget.storyId,
      );

      if (mounted) {
        setState(() {
          _analytics = analytics;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _hasError = true;
          _isLoading = false;
        });
      }
      debugPrint('Error loading story analytics: $e');

      // Show user-friendly error message
      if (mounted) {
        String errorMessage = 'Failed to load analytics';
        if (e.toString().contains('permission') ||
            e.toString().contains('Permission')) {
          errorMessage = 'You can only view analytics for your own stories';
        } else if (e.toString().contains('not found')) {
          errorMessage = 'Story not found';
        } else if (e.toString().contains('authenticated')) {
          errorMessage = 'Please log in to view analytics';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error_outline, color: Colors.white, size: 20),
                const SizedBox(width: 12),
                Expanded(child: Text(errorMessage)),
              ],
            ),
            backgroundColor: Colors.red.shade600,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.black87,
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Center(
          child: CircularProgressIndicator(color: Colors.white),
        ),
      );
    }

    if (_hasError) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.black87,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.analytics_outlined, color: Colors.red, size: 32),
            const SizedBox(height: 8),
            const Text(
              'Analytics Unavailable',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 4),
            const Text(
              'Analytics are only available for your own stories',
              style: TextStyle(color: Colors.white70, fontSize: 12),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                TextButton(
                  onPressed: () {
                    if (widget.onClose != null) {
                      widget.onClose!();
                    } else {
                      Navigator.of(context).pop();
                    }
                  },
                  child: const Text(
                    'Close',
                    style: TextStyle(color: Colors.white70),
                  ),
                ),
                ElevatedButton(
                  onPressed: _loadAnalytics,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Retry'),
                ),
              ],
            ),
          ],
        ),
      );
    }

    if (_analytics == null) {
      return const Center(
        child: Text(
          'No analytics data available',
          style: TextStyle(color: Colors.white),
        ),
      );
    }

    final totalViews = _analytics!.viewCount;
    final uniqueViewers = _analytics!.uniqueViewers;
    final viewers = _analytics!.viewedBy;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.white),
                onPressed: () {
                  if (widget.onClose != null) {
                    widget.onClose!();
                  } else {
                    Navigator.of(context).pop();
                  }
                },
                tooltip: 'Back',
              ),
              const Icon(Icons.analytics, color: Colors.white, size: 20),
              const SizedBox(width: 8),
              const Text(
                'Story Analytics',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              IconButton(
                icon: const Icon(Icons.refresh, color: Colors.white),
                onPressed: _loadAnalytics,
                tooltip: 'Refresh',
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Stats grid
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Total Views',
                  totalViews.toString(),
                  Icons.visibility,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: GestureDetector(
                  onTap: () => _openViewersScreen(),
                  child: _buildStatCard(
                    'Unique Viewers',
                    uniqueViewers.toString(),
                    Icons.people,
                    Colors.green,
                  ),
                ),
              ),
            ],
          ),

          // Detailed analytics
          if (widget.showDetailed && viewers.isNotEmpty) ...[
            const SizedBox(height: 16),
            const Text(
              'Recent Viewers',
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              height: 200,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
              ),
              child: ListView.builder(
                itemCount: viewers.length > 10 ? 10 : viewers.length,
                itemBuilder: (context, index) {
                  final viewerId = viewers[index];
                  // For now, we don't have detailed timestamp data in the simple format
                  // This could be enhanced later with more detailed viewer tracking

                  return ListTile(
                    leading: CircleAvatar(
                      radius: 16,
                      backgroundColor: Colors.grey[800],
                      child: Text(
                        (index + 1).toString(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                        ),
                      ),
                    ),
                    title: Text(
                      'Viewer ${viewerId.substring(0, 8)}',
                      style: const TextStyle(color: Colors.white, fontSize: 14),
                    ),
                    subtitle: Text(
                      'Recently viewed',
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 12,
                      ),
                    ),
                    trailing: const Icon(
                      Icons.visibility,
                      color: Colors.white54,
                      size: 16,
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 8),
            Center(
              child: TextButton.icon(
                onPressed: _openViewersScreen,
                icon: const Icon(Icons.people, color: Colors.blue, size: 16),
                label: const Text(
                  'View All Viewers',
                  style: TextStyle(color: Colors.blue, fontSize: 14),
                ),
              ),
            ),
          ],

          // View trend chart placeholder
          if (widget.showDetailed) ...[
            const SizedBox(height: 16),
            Container(
              height: 100,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Center(
                child: Text(
                  'View Trend Chart\n(Coming Soon)',
                  style: TextStyle(color: Colors.white54, fontSize: 12),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.5)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(color: color.withValues(alpha: 0.8), fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

// Compact analytics widget for story preview
class CompactStoryAnalytics extends ConsumerWidget {
  final String storyId;

  const CompactStoryAnalytics({super.key, required this.storyId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return FutureBuilder<StoryAnalytics?>(
      future: GetIt.I<StoryInteractionService>().getStoryAnalytics(storyId),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              color: Colors.white,
              strokeWidth: 2,
            ),
          );
        }

        if (snapshot.hasError || !snapshot.hasData) {
          return const Icon(
            Icons.analytics_outlined,
            color: Colors.white54,
            size: 20,
          );
        }

        final analytics = snapshot.data!;
        final totalViews = analytics.viewCount;

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.7),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.visibility, color: Colors.white, size: 16),
              const SizedBox(width: 4),
              Text(
                totalViews.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
