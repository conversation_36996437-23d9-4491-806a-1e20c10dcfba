import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/stories/models/story_reel_model.dart';
import 'package:billionaires_social/features/stories/providers/story_provider.dart';
import 'package:billionaires_social/features/stories/widgets/story_analytics_display.dart';
import 'package:billionaires_social/features/stories/widgets/story_viewer_list.dart';
import 'package:billionaires_social/features/stories/widgets/close_friends_quick_manager.dart';
import 'package:billionaires_social/features/stories/screens/story_settings_screen.dart';
import 'package:billionaires_social/features/stories/services/story_delete_service.dart';
import 'package:billionaires_social/core/utils/dialog_utils.dart';

class StoryInsightsPanel extends ConsumerStatefulWidget {
  final StoryItem story;
  final VoidCallback onClose;
  final VoidCallback? onStoryDeleted;

  const StoryInsightsPanel({
    super.key,
    required this.story,
    required this.onClose,
    this.onStoryDeleted,
  });

  @override
  ConsumerState<StoryInsightsPanel> createState() => _StoryInsightsPanelState();
}

class _StoryInsightsPanelState extends ConsumerState<StoryInsightsPanel>
    with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isDeleting = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.85,
      decoration: const BoxDecoration(
        color: Color(0xFF1C1C1E),
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
            child: Row(
              children: [
                IconButton(
                  onPressed: widget.onClose,
                  icon: const Icon(Icons.close, color: Colors.white),
                ),
                const Expanded(
                  child: Text(
                    'Story Insights',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                // Delete button
                IconButton(
                  onPressed: _isDeleting ? null : _showDeleteConfirmation,
                  icon: _isDeleting
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.red,
                          ),
                        )
                      : const Icon(Icons.delete_outline, color: Colors.red),
                ),
              ],
            ),
          ),

          // Tab bar
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 20),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: TabBar(
              controller: _tabController,
              indicator: BoxDecoration(
                color: const Color(0xFFD4AF37), // Luxury gold color
                borderRadius: BorderRadius.circular(12),
              ),
              indicatorSize: TabBarIndicatorSize.tab,
              dividerColor: Colors.transparent,
              labelColor: Colors.black,
              unselectedLabelColor: Colors.white70,
              labelStyle: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
              tabs: const [
                Tab(text: 'Analytics'),
                Tab(text: 'Viewers'),
                Tab(text: 'Friends'),
                Tab(text: 'Settings'),
              ],
            ),
          ),

          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // Analytics tab
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: StoryAnalyticsDisplay(
                    storyId: widget.story.id,
                    showDetailed: true,
                  ),
                ),

                // Viewers tab
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: StoryViewerList(storyId: widget.story.id),
                ),

                // Close Friends tab
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: CloseFriendsQuickManager(
                    onUpdated: () {
                      // Refresh story data if needed
                      ref.read(storyReelsProvider.notifier).refresh();
                    },
                  ),
                ),

                // Settings tab
                const Padding(
                  padding: EdgeInsets.all(20),
                  child: _StorySettingsTab(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation() {
    showAppDialog(
      context: context,
      title: const Text('Delete Story?', style: TextStyle(color: Colors.white)),
      content: const Text(
        'This story will be permanently deleted and cannot be recovered. Your followers will no longer be able to view it.',
        style: TextStyle(color: Colors.white70),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel', style: TextStyle(color: Colors.white70)),
        ),
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
            _deleteStory();
          },
          child: const Text(
            'Delete',
            style: TextStyle(color: Colors.red, fontWeight: FontWeight.w600),
          ),
        ),
      ],
    );
  }

  Future<void> _deleteStory() async {
    if (_isDeleting) return;

    setState(() {
      _isDeleting = true;
    });

    try {
      final success = await StoryDeleteService.deleteStory(
        context,
        widget.story,
        ref: ref,
      );

      if (!success) {
        setState(() {
          _isDeleting = false;
        });
        return;
      }

      // Refresh stories
      ref.read(storyReelsProvider.notifier).refresh();

      if (mounted) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 12),
                Text('Story deleted successfully'),
              ],
            ),
            backgroundColor: Colors.green,
          ),
        );

        // Close panel and notify parent
        widget.onClose();
        widget.onStoryDeleted?.call();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isDeleting = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error_outline, color: Colors.white),
                const SizedBox(width: 12),
                Expanded(
                  child: Text('Failed to delete story: ${e.toString()}'),
                ),
              ],
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

class _StorySettingsTab extends StatelessWidget {
  const _StorySettingsTab();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Story Settings',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 20),

        // Quick settings options
        _buildSettingOption(
          icon: Icons.reply,
          title: 'Who can reply',
          subtitle: 'Everyone',
          onTap: () => _navigateToFullSettings(context),
        ),
        _buildSettingOption(
          icon: Icons.save_alt,
          title: 'Who can save',
          subtitle: 'Followers',
          onTap: () => _navigateToFullSettings(context),
        ),
        _buildSettingOption(
          icon: Icons.share,
          title: 'Who can share',
          subtitle: 'Close Friends',
          onTap: () => _navigateToFullSettings(context),
        ),

        const SizedBox(height: 20),

        // Full settings button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () => _navigateToFullSettings(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFD4AF37),
              foregroundColor: Colors.black,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              'Open Full Settings',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSettingOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: Colors.white),
      title: Text(title, style: const TextStyle(color: Colors.white)),
      subtitle: Text(subtitle, style: const TextStyle(color: Colors.white70)),
      trailing: const Icon(
        Icons.arrow_forward_ios,
        color: Colors.white54,
        size: 16,
      ),
      onTap: onTap,
    );
  }

  void _navigateToFullSettings(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const StorySettingsScreen()),
    );
  }
}
