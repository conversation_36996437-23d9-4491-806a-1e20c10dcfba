import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:billionaires_social/features/stories/providers/story_provider.dart';
import 'package:billionaires_social/features/profile/screens/user_profile_screen.dart';
import 'package:billionaires_social/features/profile/screens/main_profile_screen.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:billionaires_social/core/services/firebase_service.dart';

import 'package:cloud_firestore/cloud_firestore.dart';

class StoryViewerList extends ConsumerStatefulWidget {
  final String storyId;

  const StoryViewerList({super.key, required this.storyId});

  @override
  ConsumerState<StoryViewerList> createState() => _StoryViewerListState();
}

class _StoryViewerListState extends ConsumerState<StoryViewerList> {
  List<StoryViewer> _viewers = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadViewers();
  }

  Future<void> _loadViewers() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final analytics = await ref
          .read(storyReelsProvider.notifier)
          .getStoryAnalytics(widget.storyId);

      final viewerIds = analytics['viewers'] as List<dynamic>? ?? [];
      final viewers = <StoryViewer>[];

      debugPrint('📊 Loading story viewers for ${widget.storyId}:');
      debugPrint('   Raw viewer data count: ${viewerIds.length}');
      debugPrint('   Analytics data: ${analytics.keys.toList()}');

      for (final viewerData in viewerIds) {
        try {
          final viewerId = viewerData['viewerId'] as String?;

          // Handle both old and new timestamp formats
          DateTime viewedAt = DateTime.now();
          if (viewerData['timestamp'] != null) {
            final timestamp = viewerData['timestamp'];
            if (timestamp is Timestamp) {
              viewedAt = timestamp.toDate();
            } else if (timestamp is String) {
              try {
                viewedAt = DateTime.parse(timestamp);
              } catch (e) {
                debugPrint('Error parsing timestamp string: $e');
              }
            }
          } else if (viewerData['viewedAt'] != null) {
            try {
              viewedAt = DateTime.parse(viewerData['viewedAt'] as String);
            } catch (e) {
              debugPrint('Error parsing viewedAt: $e');
            }
          }

          if (viewerId != null) {
            final firebaseService = FirebaseService();
            final profile = await firebaseService.getUserProfile(viewerId);

            if (profile != null) {
              viewers.add(
                StoryViewer(
                  userId: viewerId,
                  username: profile.username,
                  name: profile.name,
                  profilePictureUrl: profile.profilePictureUrl,
                  viewedAt: viewedAt,
                  isVerified: profile.isVerified,
                  isBillionaire: profile.isBillionaire,
                ),
              );

              debugPrint(
                '✅ Loaded viewer: ${profile.username} (viewed at: $viewedAt)',
              );
            } else {
              debugPrint('⚠️ Profile not found for viewer: $viewerId');
            }
          }
        } catch (e) {
          debugPrint('❌ Error loading viewer data: $e');
        }
      }

      // Sort by most recent first
      viewers.sort((a, b) => b.viewedAt.compareTo(a.viewedAt));

      if (mounted) {
        setState(() {
          _viewers = viewers;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: Color(0xFFD4AF37)),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 48),
            const SizedBox(height: 16),
            Text(
              'Failed to load viewers',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: const TextStyle(color: Colors.white70, fontSize: 14),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadViewers,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFD4AF37),
                foregroundColor: Colors.black,
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_viewers.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.visibility_off, color: Colors.white54, size: 48),
            SizedBox(height: 16),
            Text(
              'No viewers yet',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'When people view your story, they\'ll appear here',
              style: TextStyle(color: Colors.white70, fontSize: 14),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with count
        Row(
          children: [
            const Icon(Icons.visibility, color: Color(0xFFD4AF37), size: 20),
            const SizedBox(width: 8),
            Text(
              '${_viewers.length} ${_viewers.length == 1 ? 'viewer' : 'viewers'}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            IconButton(
              onPressed: _loadViewers,
              icon: const Icon(Icons.refresh, color: Colors.white54, size: 20),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Viewers list
        Expanded(
          child: ListView.builder(
            itemCount: _viewers.length,
            itemBuilder: (context, index) {
              final viewer = _viewers[index];
              return _buildViewerTile(viewer);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildViewerTile(StoryViewer viewer) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(vertical: 8),
      leading: Stack(
        children: [
          CircleAvatar(
            radius: 24,
            backgroundImage: viewer.profilePictureUrl.isNotEmpty
                ? CachedNetworkImageProvider(viewer.profilePictureUrl)
                : null,
            child: viewer.profilePictureUrl.isEmpty
                ? const Icon(Icons.person, color: Colors.white)
                : null,
          ),
          // Verification badge
          if (viewer.isVerified || viewer.isBillionaire)
            Positioned(
              bottom: 0,
              right: 0,
              child: Container(
                width: 16,
                height: 16,
                decoration: BoxDecoration(
                  color: viewer.isBillionaire
                      ? const Color(0xFFD4AF37)
                      : Colors.blue,
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.black, width: 1),
                ),
                child: Icon(
                  viewer.isBillionaire ? Icons.diamond : Icons.check,
                  color: Colors.white,
                  size: 10,
                ),
              ),
            ),
        ],
      ),
      title: Row(
        children: [
          Expanded(
            child: Text(
              viewer.name.isNotEmpty ? viewer.name : viewer.username,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (viewer.name.isNotEmpty)
            Text(
              '@${viewer.username}',
              style: const TextStyle(color: Colors.white70, fontSize: 12),
            ),
          Text(
            _formatViewTime(viewer.viewedAt),
            style: const TextStyle(color: Colors.white54, fontSize: 12),
          ),
        ],
      ),
      trailing: IconButton(
        onPressed: () => _navigateToProfile(viewer.userId),
        icon: const Icon(
          Icons.arrow_forward_ios,
          color: Colors.white54,
          size: 16,
        ),
      ),
      onTap: () => _navigateToProfile(viewer.userId),
    );
  }

  String _formatViewTime(DateTime viewedAt) {
    final now = DateTime.now();
    final difference = now.difference(viewedAt);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  void _navigateToProfile(String userId) {
    final currentUserId = FirebaseAuth.instance.currentUser?.uid;

    debugPrint(
      '🔗 Navigating to profile: $userId (current user: $currentUserId)',
    );

    // Navigate to appropriate profile screen
    if (currentUserId == userId) {
      // Navigate to own profile (MainProfileScreen)
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => MainProfileScreen(userId: userId),
        ),
      );
    } else {
      // Navigate to other user's profile (UserProfileScreen)
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => UserProfileScreen(userId: userId),
        ),
      );
    }
  }
}

class StoryViewer {
  final String userId;
  final String username;
  final String name;
  final String profilePictureUrl;
  final DateTime viewedAt;
  final bool isVerified;
  final bool isBillionaire;

  StoryViewer({
    required this.userId,
    required this.username,
    required this.name,
    required this.profilePictureUrl,
    required this.viewedAt,
    this.isVerified = false,
    this.isBillionaire = false,
  });
}
