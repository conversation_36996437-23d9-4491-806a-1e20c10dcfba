import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/stories/screens/circular_story_screen.dart';
import 'package:billionaires_social/features/stories/widgets/circular_story_carousel.dart';
import 'package:billionaires_social/features/stories/providers/story_provider.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// Demo widget to showcase the circular story carousel
class CircularStoryDemo extends ConsumerWidget {
  const CircularStoryDemo({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: const Color(0xFF0F3460),
      appBar: AppBar(
        title: const Text(
          'Circular Story Carousel Demo',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF1A1A2E),
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Demo description
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: const Color(0xFFD4AF37).withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '🎯 Circular Story Carousel',
                    style: TextStyle(
                      color: Color(0xFFD4AF37),
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 12),
                  Text(
                    'A luxurious circular layout for displaying stories with:',
                    style: TextStyle(color: Colors.white, fontSize: 16),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '• Circular rotation with drag gestures\n'
                    '• Color-coded borders for story types\n'
                    '• Smooth animations and haptic feedback\n'
                    '• Premium visual effects and shadows\n'
                    '• Center avatar for story creation',
                    style: TextStyle(color: Colors.white70, fontSize: 14),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 30),
            
            // Story type legend
            const Text(
              'Story Types:',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildLegendItem(
                  'Public',
                  const Color(0xFF4ECDC4),
                  Icons.public,
                ),
                _buildLegendItem(
                  'Close Friends',
                  const Color(0xFF00D4AA),
                  Icons.people,
                ),
                _buildLegendItem(
                  'VIP',
                  const Color(0xFFFF6B6B),
                  Icons.star,
                ),
                _buildLegendItem(
                  'Billionaire',
                  const Color(0xFFD4AF37),
                  Icons.diamond,
                ),
              ],
            ),
            
            const SizedBox(height: 40),
            
            // Compact circular carousel demo
            const Text(
              'Compact Version:',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            
            Center(
              child: ref.watch(storyReelsProvider).when(
                data: (storyReels) => CircularStoryCarousel(
                  storyReels: storyReels.take(6).toList(), // Limit for demo
                  centerAvatarUrl: FirebaseAuth.instance.currentUser?.photoURL,
                  radius: 100.0,
                  itemSize: 50.0,
                  onCenterTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Create Story tapped!'),
                        backgroundColor: Color(0xFFD4AF37),
                      ),
                    );
                  },
                ),
                loading: () => Container(
                  width: 200,
                  height: 200,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withValues(alpha: 0.1),
                  ),
                  child: const Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFD4AF37)),
                    ),
                  ),
                ),
                error: (error, stack) => Container(
                  width: 200,
                  height: 200,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withValues(alpha: 0.1),
                  ),
                  child: const Center(
                    child: Icon(
                      Icons.error_outline,
                      color: Colors.white54,
                      size: 48,
                    ),
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 40),
            
            // Action buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const CircularStoryScreen(),
                        ),
                      );
                    },
                    icon: const Icon(Icons.fullscreen),
                    label: const Text('Full Screen Demo'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFD4AF37),
                      foregroundColor: Colors.black,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () {
                      _showImplementationGuide(context);
                    },
                    icon: const Icon(Icons.code),
                    label: const Text('Implementation'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: const Color(0xFFD4AF37),
                      side: const BorderSide(color: Color(0xFFD4AF37)),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildLegendItem(String label, Color color, IconData icon) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.2),
            shape: BoxShape.circle,
            border: Border.all(color: color, width: 2),
          ),
          child: Icon(icon, color: color, size: 16),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: TextStyle(
            color: color,
            fontSize: 10,
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  void _showImplementationGuide(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1A1A2E),
        title: const Text(
          'Implementation Guide',
          style: TextStyle(color: Color(0xFFD4AF37)),
        ),
        content: const SingleChildScrollView(
          child: Text(
            '''
// Basic Usage:
CircularStoryCarousel(
  storyReels: storyReels,
  centerAvatarUrl: userAvatarUrl,
  radius: 120.0,
  itemSize: 60.0,
  onCenterTap: () => createStory(),
)

// Features:
• Drag to rotate stories in circle
• Tap story to open viewer
• Color-coded borders by story type
• Smooth animations with haptic feedback
• Customizable radius and item sizes
• Center avatar for story creation

// Story Types:
• Public: Teal border
• Close Friends: Green border  
• VIP: Red/Yellow gradient
• Billionaire: Golden border
• Viewed: Gray border

// Integration:
Replace horizontal story list with 
CircularStoryCarousel for a more 
luxurious and interactive experience.
            ''',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 12,
              fontFamily: 'monospace',
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'Close',
              style: TextStyle(color: Color(0xFFD4AF37)),
            ),
          ),
        ],
      ),
    );
  }
}
