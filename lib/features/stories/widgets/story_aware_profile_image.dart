import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:billionaires_social/features/stories/providers/story_provider.dart';
import 'package:billionaires_social/features/stories/screens/story_viewer_screen.dart';
import 'package:billionaires_social/features/stories/models/story_reel_model.dart';
import 'package:billionaires_social/features/profile/screens/main_profile_screen.dart';
import 'package:billionaires_social/features/profile/screens/user_profile_screen.dart';
import 'package:firebase_auth/firebase_auth.dart';

class StoryAwareProfileImage extends ConsumerWidget {
  final String userId;
  final String? profileImageUrl;
  final double size;
  final bool showStoryIndicator;
  final VoidCallback? onTap;
  final bool showBorder;

  const StoryAwareProfileImage({
    super.key,
    required this.userId,
    this.profileImageUrl,
    this.size = 40,
    this.showStoryIndicator = true,
    this.onTap,
    this.showBorder = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final storyReelsAsync = ref.watch(storyReelsProvider);
    final currentUser = FirebaseAuth.instance.currentUser;
    final isOwnStory = currentUser != null && userId == currentUser.uid;

    // Debug: Log profile image data
    debugPrint('🎨 StoryAwareProfileImage building for userId: $userId');
    debugPrint('   - Provided profileImageUrl: $profileImageUrl');
    debugPrint('   - Current user: ${currentUser?.uid}');
    debugPrint('   - Is own story: $isOwnStory');

    return storyReelsAsync.when(
      data: (reels) {
        final userReel = reels.firstWhere(
          (reel) => reel.userId == userId,
          orElse: () => StoryReel(
            id: userId,
            userId: userId,
            username: 'Unknown',
            userAvatarUrl: profileImageUrl ?? '',
            stories: [],
            isAllViewed: true,
            isCloseFriend: false,
          ),
        );

        final hasStories = userReel.stories.isNotEmpty;
        final isCloseFriend = userReel.isCloseFriend;
        final isAllViewed = userReel.isAllViewed;

        // Use the provided profileImageUrl first, then fall back to reel data
        final effectiveProfileUrl = profileImageUrl?.isNotEmpty == true
            ? profileImageUrl
            : userReel.userAvatarUrl;

        debugPrint('   - Effective profile URL: $effectiveProfileUrl');
        debugPrint('   - From reel: ${userReel.userAvatarUrl}');

        // Debug logging for border colors
        debugPrint('🎨 StoryAwareProfileImage for user $userId:');
        debugPrint('  - hasStories: $hasStories');
        debugPrint('  - isCloseFriend: $isCloseFriend');
        debugPrint('  - isAllViewed: $isAllViewed');
        debugPrint(
          '  - Border color: ${_getStoryBorderColor(isCloseFriend, isAllViewed)}',
        );
        debugPrint(
          '  - Show border: ${showBorder && showStoryIndicator && hasStories}',
        );

        return GestureDetector(
          onTap: () {
            if (hasStories) {
              // Navigate to story viewer
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) =>
                      StoryViewerScreen(reel: userReel, isOwnStory: isOwnStory),
                ),
              );
            } else if (onTap != null) {
              // Fallback to custom onTap if no stories
              onTap!();
            } else {
              // Default navigation to user profile
              _navigateToProfile(context, userId);
            }
          },
          child: Stack(
            children: [
              Container(
                padding: showBorder && showStoryIndicator && hasStories
                    ? const EdgeInsets.all(2.5)
                    : EdgeInsets.zero,
                decoration: showBorder && showStoryIndicator && hasStories
                    ? BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: _getStoryBorderColor(
                            isCloseFriend,
                            isAllViewed,
                          ),
                          width: 2.5,
                        ),
                      )
                    : null,
                child: CircleAvatar(
                  radius: size / 2,
                  backgroundColor: Colors.grey.shade200,
                  backgroundImage: _isValidImageUrl(effectiveProfileUrl)
                      ? CachedNetworkImageProvider(effectiveProfileUrl!)
                      : null,
                  child: !_isValidImageUrl(effectiveProfileUrl)
                      ? Icon(
                          Icons.person,
                          size: size * 0.6,
                          color: Colors.grey.shade600,
                        )
                      : null,
                ),
              ),

              // Settings button only for own stories in specific contexts (not feed)
              // Removed to prevent settings icon appearing on feed post cards
            ],
          ),
        );
      },
      loading: () => CircleAvatar(
        radius: size / 2,
        backgroundColor: Theme.of(context).colorScheme.surface,
        child: SizedBox(
          width: size,
          height: size,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).colorScheme.primary,
            ),
          ),
        ),
      ),
      error: (error, stack) => CircleAvatar(
        radius: size / 2,
        backgroundColor: Colors.grey.shade200,
        backgroundImage: _isValidImageUrl(profileImageUrl)
            ? CachedNetworkImageProvider(profileImageUrl!)
            : null,
        child: !_isValidImageUrl(profileImageUrl)
            ? Icon(Icons.person, size: size * 0.6, color: Colors.grey.shade600)
            : null,
      ),
    );
  }

  Color _getStoryBorderColor(bool isCloseFriend, bool isAllViewed) {
    if (isAllViewed) {
      return Colors.grey.shade600;
    }

    if (isCloseFriend) {
      return Colors.green.shade600;
    }

    return Colors.red.shade600;
  }

  bool _isValidImageUrl(String? url) {
    if (url == null || url.isEmpty) return false;
    return url.startsWith('http://') || url.startsWith('https://');
  }

  void _navigateToProfile(BuildContext context, String userId) {
    final currentUserId = FirebaseAuth.instance.currentUser?.uid;
    final isCurrentUser = currentUserId == userId;

    debugPrint(
      '🔍 StoryAwareProfileImage Navigation: userId=$userId, currentUserId=$currentUserId, isCurrentUser=$isCurrentUser',
    );

    if (isCurrentUser) {
      // Navigate to current user's main profile (with edit options)
      debugPrint('🏠 Navigating to current user main profile from story');
      Navigator.of(context).push(
        MaterialPageRoute(builder: (context) => const MainProfileScreen()),
      );
    } else {
      // Navigate to other user's profile (view only)
      debugPrint('👤 Navigating to other user profile from story: $userId');
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => UserProfileScreen(userId: userId),
        ),
      );
    }
  }
}
