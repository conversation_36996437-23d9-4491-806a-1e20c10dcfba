import 'dart:async';
import 'package:flutter/material.dart';
import 'package:billionaires_social/features/stories/services/story_service.dart';
import 'package:billionaires_social/core/service_locator.dart';

class StoryExpirationTimer extends StatefulWidget {
  final DateTime createdAt;
  final bool showFullTimer;

  const StoryExpirationTimer({
    super.key,
    required this.createdAt,
    this.showFullTimer = false,
  });

  @override
  State<StoryExpirationTimer> createState() => _StoryExpirationTimerState();
}

class _StoryExpirationTimerState extends State<StoryExpirationTimer>
    with TickerProviderStateMixin {
  late Timer _timer;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final StoryService _storyService = getIt<StoryService>();
  Duration _timeRemaining = Duration.zero;
  bool _isExpired = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 1.0, end: 0.5).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _updateTimeRemaining();
    _startTimer();
  }

  @override
  void dispose() {
    _timer.cancel();
    _animationController.dispose();
    super.dispose();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _updateTimeRemaining();
    });
  }

  void _updateTimeRemaining() {
    final timeRemaining = _storyService.getTimeRemaining(widget.createdAt);
    final isExpired = _storyService.isStoryExpired(widget.createdAt);

    setState(() {
      _timeRemaining = timeRemaining;
      _isExpired = isExpired;
    });

    // Start pulsing animation when less than 1 hour remaining
    if (timeRemaining.inHours < 1 && !_animationController.isAnimating) {
      _animationController.repeat(reverse: true);
    }

    // Stop timer if expired
    if (isExpired) {
      _timer.cancel();
      _animationController.stop();
    }
  }

  String _formatDuration(Duration duration) {
    if (duration.isNegative) return 'Expired';

    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    } else {
      return '${seconds}s';
    }
  }

  Color _getTimerColor() {
    if (_isExpired) return Colors.red;
    if (_timeRemaining.inHours < 1) return Colors.orange;
    if (_timeRemaining.inHours < 6) return Colors.yellow;
    return Colors.green;
  }

  @override
  Widget build(BuildContext context) {
    if (_isExpired) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.red.withValues(alpha: 0.8),
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.access_time, color: Colors.white, size: 16),
            SizedBox(width: 4),
            Text(
              'Expired',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _fadeAnimation.value,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getTimerColor().withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.access_time,
                  color: Colors.white,
                  size: widget.showFullTimer ? 18 : 16,
                ),
                const SizedBox(width: 4),
                Text(
                  _formatDuration(_timeRemaining),
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: widget.showFullTimer ? 14 : 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

// Full story expiration widget for story editor
class StoryExpirationWidget extends StatefulWidget {
  final DateTime createdAt;
  final VoidCallback? onExpired;

  const StoryExpirationWidget({
    super.key,
    required this.createdAt,
    this.onExpired,
  });

  @override
  State<StoryExpirationWidget> createState() => _StoryExpirationWidgetState();
}

class _StoryExpirationWidgetState extends State<StoryExpirationWidget> {
  final StoryService _storyService = getIt<StoryService>();
  Timer? _timer;
  Duration _timeRemaining = Duration.zero;
  bool _isExpired = false;

  @override
  void initState() {
    super.initState();
    _updateTimeRemaining();
    _startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _updateTimeRemaining();
    });
  }

  void _updateTimeRemaining() {
    final timeRemaining = _storyService.getTimeRemaining(widget.createdAt);
    final isExpired = _storyService.isStoryExpired(widget.createdAt);

    setState(() {
      _timeRemaining = timeRemaining;
      _isExpired = isExpired;
    });

    if (isExpired && widget.onExpired != null) {
      widget.onExpired!();
    }
  }

  String _formatFullDuration(Duration duration) {
    if (duration.isNegative) return 'Story has expired';

    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '$hours hours, $minutes minutes, $seconds seconds remaining';
    } else if (minutes > 0) {
      return '$minutes minutes, $seconds seconds remaining';
    } else {
      return '$seconds seconds remaining';
    }
  }

  double _getProgressPercentage() {
    const totalLifetime = Duration(hours: 24);
    final elapsed = totalLifetime - _timeRemaining;
    return elapsed.inSeconds / totalLifetime.inSeconds;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.access_time,
                color: _isExpired ? Colors.red : Colors.white,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Story Expiration',
                style: TextStyle(
                  color: _isExpired ? Colors.red : Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              if (_isExpired)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text(
                    'EXPIRED',
                    style: TextStyle(
                      color: Colors.red,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 12),

          // Progress bar
          LinearProgressIndicator(
            value: _getProgressPercentage(),
            backgroundColor: Colors.white.withValues(alpha: 0.2),
            valueColor: AlwaysStoppedAnimation<Color>(
              _isExpired ? Colors.red : Colors.blue,
            ),
          ),
          const SizedBox(height: 8),

          // Time remaining text
          Text(
            _formatFullDuration(_timeRemaining),
            style: TextStyle(
              color: _isExpired ? Colors.red : Colors.white70,
              fontSize: 14,
            ),
          ),

          // Warning when less than 1 hour remaining
          if (_timeRemaining.inHours < 1 && !_isExpired)
            Container(
              margin: const EdgeInsets.only(top: 8),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.withValues(alpha: 0.5)),
              ),
              child: Row(
                children: [
                  const Icon(Icons.warning, color: Colors.orange, size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Story will expire soon!',
                      style: TextStyle(
                        color: Colors.orange,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }
}
