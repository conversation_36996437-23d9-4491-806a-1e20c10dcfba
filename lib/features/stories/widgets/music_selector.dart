import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/stories/services/story_service.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:permission_handler/permission_handler.dart';

class MusicSelector extends ConsumerStatefulWidget {
  final String? selectedMusicPath;
  final Function(String?) onMusicSelected;

  const MusicSelector({
    super.key,
    this.selectedMusicPath,
    required this.onMusicSelected,
  });

  @override
  ConsumerState<MusicSelector> createState() => _MusicSelectorState();
}

class _MusicSelectorState extends ConsumerState<MusicSelector> {
  final StoryService _storyService = getIt<StoryService>();
  final TextEditingController _searchController = TextEditingController();
  List<MusicTrack> _searchResults = [];

  // Mock music tracks - in a real app, this would come from an API
  final List<MusicTrack> _availableTracks = [
    MusicTrack(
      id: '1',
      title: 'Luxury Vibes',
      artist: 'Elite Beats',
      duration: const Duration(minutes: 2, seconds: 30),
      path: 'assets/music/luxury_vibes.mp3',
      genre: 'Electronic',
    ),
    MusicTrack(
      id: '2',
      title: 'Golden Hour',
      artist: 'Sophisticated Sounds',
      duration: const Duration(minutes: 3, seconds: 15),
      path: 'assets/music/golden_hour.mp3',
      genre: 'Ambient',
    ),
    MusicTrack(
      id: '3',
      title: 'Billionaire Dreams',
      artist: 'Luxury Records',
      duration: const Duration(minutes: 2, seconds: 45),
      path: 'assets/music/billionaire_dreams.mp3',
      genre: 'Hip Hop',
    ),
    MusicTrack(
      id: '4',
      title: 'Champagne Flow',
      artist: 'Elite Productions',
      duration: const Duration(minutes: 3, seconds: 0),
      path: 'assets/music/champagne_flow.mp3',
      genre: 'Pop',
    ),
    MusicTrack(
      id: '5',
      title: 'Diamond Life',
      artist: 'Luxury Beats',
      duration: const Duration(minutes: 2, seconds: 55),
      path: 'assets/music/diamond_life.mp3',
      genre: 'R&B',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
    _searchResults = _availableTracks;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    if (_searchController.text.isEmpty) {
      setState(() {
        _searchResults = _availableTracks;
      });
    } else {
      setState(() {
        _searchResults = _availableTracks
            .where(
              (track) =>
                  track.title.toLowerCase().contains(
                    _searchController.text.toLowerCase(),
                  ) ||
                  track.artist.toLowerCase().contains(
                    _searchController.text.toLowerCase(),
                  ) ||
                  track.genre.toLowerCase().contains(
                    _searchController.text.toLowerCase(),
                  ),
            )
            .toList();
      });
    }
  }

  Future<void> _previewMusic(MusicTrack track) async {
    try {
      await _storyService.previewMusic(track.path);
      setState(() {}); // Rebuild to show playing state
    } catch (e) {
      final errorMsg = e.toString();
      if (errorMsg.contains('permission')) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text(
                'Audio permission denied. Please enable it in settings.',
              ),
              backgroundColor: Colors.red,
              action: SnackBarAction(
                label: 'Settings',
                textColor: Colors.white,
                onPressed: () => openAppSettings(),
              ),
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error previewing music: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _stopPreview() async {
    try {
      await _storyService.stopMusicPreview();
      setState(() {}); // Rebuild to show stopped state
    } catch (e) {
      final errorMsg = e.toString();
      if (errorMsg.contains('permission')) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text(
                'Audio permission denied. Please enable it in settings.',
              ),
              backgroundColor: Colors.red,
              action: SnackBarAction(
                label: 'Settings',
                textColor: Colors.white,
                onPressed: () => openAppSettings(),
              ),
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error stopping preview: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _selectMusic(MusicTrack track) {
    widget.onMusicSelected(track.path);
    Navigator.of(context).pop();
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 400,
      decoration: BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Colors.white24, width: 0.5),
              ),
            ),
            child: Row(
              children: [
                const Text(
                  'Select Music',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (widget.selectedMusicPath != null)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'Selected',
                      style: TextStyle(
                        color: Colors.blue,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
              ],
            ),
          ),

          // Search bar
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              style: const TextStyle(color: Colors.white),
              decoration: InputDecoration(
                hintText: 'Search music...',
                hintStyle: const TextStyle(color: Colors.white54),
                prefixIcon: const Icon(Icons.search, color: Colors.white54),
                filled: true,
                fillColor: Colors.white10,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
          ),

          // Music tracks list
          Expanded(
            child: ListView.builder(
              itemCount: _searchResults.length,
              itemBuilder: (context, index) {
                final track = _searchResults[index];
                final isSelected = widget.selectedMusicPath == track.path;
                final isPlaying =
                    _storyService.isPlaying &&
                    _storyService.currentMusicPath == track.path;

                return Container(
                  margin: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? Colors.blue.withValues(alpha: 0.2)
                        : Colors.white10,
                    borderRadius: BorderRadius.circular(12),
                    border: isSelected
                        ? Border.all(color: Colors.blue, width: 2)
                        : null,
                  ),
                  child: ListTile(
                    leading: Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: Colors.grey[800],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        isPlaying ? Icons.pause : Icons.music_note,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    title: Text(
                      track.title,
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: isSelected
                            ? FontWeight.bold
                            : FontWeight.w500,
                      ),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          track.artist,
                          style: const TextStyle(color: Colors.white70),
                        ),
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                track.genre,
                                style: const TextStyle(
                                  color: Colors.white70,
                                  fontSize: 10,
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              _formatDuration(track.duration),
                              style: const TextStyle(
                                color: Colors.white54,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Preview button
                        IconButton(
                          icon: Icon(
                            isPlaying ? Icons.pause : Icons.play_arrow,
                            color: Colors.white,
                          ),
                          onPressed: () {
                            if (isPlaying) {
                              _stopPreview();
                            } else {
                              _previewMusic(track);
                            }
                          },
                        ),
                        // Select button
                        if (!isSelected)
                          TextButton(
                            onPressed: () => _selectMusic(track),
                            child: const Text(
                              'Select',
                              style: TextStyle(color: Colors.blue),
                            ),
                          ),
                      ],
                    ),
                    onTap: () => _selectMusic(track),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

// Music track model
class MusicTrack {
  final String id;
  final String title;
  final String artist;
  final Duration duration;
  final String path;
  final String genre;

  MusicTrack({
    required this.id,
    required this.title,
    required this.artist,
    required this.duration,
    required this.path,
    required this.genre,
  });
}
