import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Text styling controls for story text editor
class TextStylingControls extends StatefulWidget {
  final Color currentTextColor;
  final Color currentBackgroundColor;
  final bool hasShadow;
  final bool hasBackground;
  final String currentFont;
  final Function(Color) onTextColorChanged;
  final Function(Color) onBackgroundColorChanged;
  final VoidCallback onStyleToggle;
  final Function(String) onFontChanged;
  final VoidCallback? onDelete;
  final VoidCallback? onDone;

  const TextStylingControls({
    super.key,
    required this.currentTextColor,
    required this.currentBackgroundColor,
    required this.hasShadow,
    required this.hasBackground,
    required this.currentFont,
    required this.onTextColorChanged,
    required this.onBackgroundColorChanged,
    required this.onStyleToggle,
    required this.onFontChanged,
    this.onDelete,
    this.onDone,
  });

  @override
  State<TextStylingControls> createState() => _TextStylingControlsState();
}

class _TextStylingControlsState extends State<TextStylingControls>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;

  bool _showColorPalette = false;
  bool _isTextColorMode = true; // true for text color, false for background

  // Color palette
  final List<Color> _colors = [
    Colors.white,
    Colors.black,
    Colors.red,
    Colors.pink,
    Colors.purple,
    Colors.deepPurple,
    Colors.indigo,
    Colors.blue,
    Colors.lightBlue,
    Colors.cyan,
    Colors.teal,
    Colors.green,
    Colors.lightGreen,
    Colors.lime,
    Colors.yellow,
    Colors.amber,
    Colors.orange,
    Colors.deepOrange,
    Colors.brown,
    Colors.grey,
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // Start animation
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleColorPalette() {
    setState(() {
      _showColorPalette = !_showColorPalette;
    });
    HapticFeedback.lightImpact();
  }

  void _selectColor(Color color) {
    if (_isTextColorMode) {
      widget.onTextColorChanged(color);
    } else {
      widget.onBackgroundColorChanged(color);
    }
    HapticFeedback.selectionClick();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(0, 1),
        end: Offset.zero,
      ).animate(_slideAnimation),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.8),
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Top controls
            _buildTopControls(),

            const SizedBox(height: 16),

            // Color palette (if visible)
            if (_showColorPalette) ...[
              _buildColorModeToggle(),
              const SizedBox(height: 12),
              _buildColorPalette(),
              const SizedBox(height: 16),
            ],

            // Bottom controls
            _buildBottomControls(),
          ],
        ),
      ),
    );
  }

  Widget _buildTopControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Style toggle (A icon)
        _buildControlButton(
          icon: Icons.text_fields,
          label: _getStyleLabel(),
          onTap: widget.onStyleToggle,
          isActive: widget.hasShadow || widget.hasBackground,
        ),

        // Color palette toggle
        _buildControlButton(
          icon: Icons.palette,
          label: 'Colors',
          onTap: _toggleColorPalette,
          isActive: _showColorPalette,
        ),

        // Font style (swipe hint)
        _buildControlButton(
          icon: Icons.font_download,
          label: widget.currentFont,
          onTap: () {
            // Show swipe hint
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Swipe left/right on text to change font'),
                duration: Duration(seconds: 2),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildColorModeToggle() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[800],
        borderRadius: BorderRadius.circular(25),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildModeButton(
            'Text',
            _isTextColorMode,
            () => setState(() => _isTextColorMode = true),
          ),
          _buildModeButton(
            'Background',
            !_isTextColorMode,
            () => setState(() => _isTextColorMode = false),
          ),
        ],
      ),
    );
  }

  Widget _buildModeButton(String label, bool isActive, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
        decoration: BoxDecoration(
          color: isActive ? Colors.white : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isActive ? Colors.black : Colors.white,
            fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Widget _buildColorPalette() {
    return SizedBox(
      height: 60,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _colors.length,
        itemBuilder: (context, index) {
          final color = _colors[index];
          final isSelected = _isTextColorMode
              ? color == widget.currentTextColor
              : color == widget.currentBackgroundColor;

          return GestureDetector(
            onTap: () => _selectColor(color),
            child: Container(
              width: 50,
              height: 50,
              margin: const EdgeInsets.symmetric(horizontal: 4),
              decoration: BoxDecoration(
                color: color,
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected ? Colors.white : Colors.grey[600]!,
                  width: isSelected ? 3 : 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: isSelected
                  ? const Icon(Icons.check, color: Colors.white, size: 20)
                  : null,
            ),
          );
        },
      ),
    );
  }

  Widget _buildBottomControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Delete button
        if (widget.onDelete != null)
          _buildControlButton(
            icon: Icons.delete,
            label: 'Delete',
            onTap: widget.onDelete!,
            color: Colors.red,
          ),

        // Done button
        if (widget.onDone != null)
          _buildControlButton(
            icon: Icons.check,
            label: 'Done',
            onTap: widget.onDone!,
            color: Colors.green,
          ),
      ],
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    bool isActive = false,
    Color? color,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: isActive
              ? Colors.white.withValues(alpha: 0.2)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: color ?? (isActive ? Colors.white : Colors.grey[600]!),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color ?? Colors.white, size: 24),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                color: color ?? Colors.white,
                fontSize: 12,
                fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getStyleLabel() {
    if (widget.hasBackground) return 'Background';
    if (widget.hasShadow) return 'Shadow';
    return 'Plain';
  }
}
