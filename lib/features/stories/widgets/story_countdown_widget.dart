import 'package:flutter/material.dart';
import 'dart:async';
import '../models/shared/story_shared_models.dart';

class StoryCountdownWidget extends StatefulWidget {
  final StoryCountdown countdown;
  final VoidCallback? onExpired;

  const StoryCountdownWidget({
    super.key,
    required this.countdown,
    this.onExpired,
  });

  @override
  State<StoryCountdownWidget> createState() => _StoryCountdownWidgetState();
}

class _StoryCountdownWidgetState extends State<StoryCountdownWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _fadeController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _fadeAnimation;
  Timer? _timer;
  Duration _timeRemaining = Duration.zero;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _updateTimeRemaining();
    _startTimer();
    _fadeController.forward();

    // Start pulse animation if countdown is less than 1 hour
    if (widget.countdown.timeRemaining.inHours < 1) {
      _pulseController.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    _pulseController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _updateTimeRemaining();
    });
  }

  void _updateTimeRemaining() {
    setState(() {
      _timeRemaining = widget.countdown.timeRemaining;
    });

    if (_timeRemaining.inSeconds <= 0) {
      _timer?.cancel();
      widget.onExpired?.call();
    }
  }

  Color _getCountdownColor() {
    final hours = _timeRemaining.inHours;
    final minutes = _timeRemaining.inMinutes;

    if (hours < 1 && minutes < 30) {
      return Colors.red;
    } else if (hours < 6) {
      return Colors.orange;
    } else {
      return Colors.green;
    }
  }

  @override
  Widget build(BuildContext context) {
    final countdownColor = _getCountdownColor();
    final isExpired = _timeRemaining.inSeconds <= 0;

    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.8),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: countdownColor.withValues(alpha: 0.5),
            width: 2,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Title
            Row(
              children: [
                Icon(Icons.timer, color: countdownColor, size: 24),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    widget.countdown.title,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Description
            if (widget.countdown.description != null) ...[
              Text(
                widget.countdown.description!,
                style: const TextStyle(color: Colors.white70, fontSize: 14),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
            ],

            // Countdown timer
            AnimatedBuilder(
              animation: _pulseAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _pulseAnimation.value,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 16,
                    ),
                    decoration: BoxDecoration(
                      color: countdownColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: countdownColor.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      children: [
                        if (isExpired) ...[
                          Icon(
                            Icons.check_circle,
                            color: countdownColor,
                            size: 32,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'COMPLETED!',
                            style: TextStyle(
                              color: countdownColor,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ] else ...[
                          Text(
                            _formatTimeRemaining(),
                            style: TextStyle(
                              color: countdownColor,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              fontFamily: 'monospace',
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            _getTimeUnit(),
                            style: TextStyle(
                              color: countdownColor.withValues(alpha: 0.8),
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                );
              },
            ),

            const SizedBox(height: 16),

            // Additional info
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (widget.countdown.isRepeating)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Text(
                      'Repeating',
                      style: TextStyle(
                        color: Colors.blue,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                if (widget.countdown.participants.isNotEmpty)
                  Text(
                    '${widget.countdown.participants.length} participants',
                    style: const TextStyle(color: Colors.white70, fontSize: 12),
                  ),
              ],
            ),

            // Progress bar for time remaining
            if (!isExpired &&
                widget.countdown.targetDate.isAfter(DateTime.now())) ...[
              const SizedBox(height: 12),
              LinearProgressIndicator(
                value: _getProgressValue(),
                backgroundColor: Colors.white.withValues(alpha: 0.2),
                valueColor: AlwaysStoppedAnimation<Color>(countdownColor),
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _formatTimeRemaining() {
    final days = _timeRemaining.inDays;
    final hours = _timeRemaining.inHours % 24;
    final minutes = _timeRemaining.inMinutes % 60;
    final seconds = _timeRemaining.inSeconds % 60;

    if (days > 0) {
      return '${days.toString().padLeft(2, '0')}:${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}';
    } else if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  String _getTimeUnit() {
    final days = _timeRemaining.inDays;
    final hours = _timeRemaining.inHours;

    if (days > 0) {
      return 'DAYS : HOURS : MINUTES';
    } else if (hours > 0) {
      return 'HOURS : MINUTES : SECONDS';
    } else {
      return 'MINUTES : SECONDS';
    }
  }

  double _getProgressValue() {
    final totalDuration = widget.countdown.targetDate.difference(
      widget.countdown.createdAt,
    );
    final elapsed = DateTime.now().difference(widget.countdown.createdAt);

    if (totalDuration.inSeconds <= 0) return 1.0;

    final progress = elapsed.inSeconds / totalDuration.inSeconds;
    return progress.clamp(0.0, 1.0);
  }
}
