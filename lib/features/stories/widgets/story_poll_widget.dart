import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/shared/story_shared_models.dart';

class StoryPollWidget extends StatefulWidget {
  final StoryPoll poll;
  final Function(String) onVote;
  final bool showResults;

  const StoryPollWidget({
    super.key,
    required this.poll,
    required this.onVote,
    this.showResults = false,
  });

  @override
  State<StoryPollWidget> createState() => _StoryPollWidgetState();
}

class _StoryPollWidgetState extends State<StoryPollWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  String? _selectedOption;
  bool _hasVoted = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser != null) {
      _hasVoted = widget.poll.hasUserVoted(currentUser.uid);
      if (_hasVoted) {
        _selectedOption = widget.poll.getUserVote(currentUser.uid).firstOrNull;
      }
    }

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleVote(String option) {
    if (_hasVoted || widget.poll.isExpired) return;

    setState(() {
      _selectedOption = option;
      _hasVoted = true;
    });

    widget.onVote(option);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Question
            Text(
              widget.poll.question,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Options
            ...widget.poll.options.map((option) {
              final isSelected = _selectedOption == option;
              final voteCount = widget.poll.getVoteCount(option);
              final percentage = widget.poll.getPercentage(option);
              final hasVoted = _hasVoted || widget.poll.isExpired;

              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                child: GestureDetector(
                  onTap: hasVoted ? null : () => _handleVote(option),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? theme.colorScheme.primary
                          : Colors.white.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: isSelected
                            ? theme.colorScheme.primary
                            : Colors.white.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                option,
                                style: TextStyle(
                                  color: isSelected
                                      ? Colors.white
                                      : Colors.white,
                                  fontSize: 16,
                                  fontWeight: isSelected
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                                ),
                              ),
                              if (hasVoted) ...[
                                const SizedBox(height: 4),
                                Row(
                                  children: [
                                    Expanded(
                                      child: LinearProgressIndicator(
                                        value: percentage / 100,
                                        backgroundColor: Colors.white
                                            .withValues(alpha: 0.2),
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                              isSelected
                                                  ? Colors.white
                                                  : theme.colorScheme.primary,
                                            ),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      '${percentage.toStringAsFixed(1)}%',
                                      style: TextStyle(
                                        color: isSelected
                                            ? Colors.white
                                            : Colors.white.withValues(
                                                alpha: 0.7,
                                              ),
                                        fontSize: 12,
                                      ),
                                    ),
                                  ],
                                ),
                                if (voteCount > 0) ...[
                                  const SizedBox(height: 2),
                                  Text(
                                    '$voteCount vote${voteCount == 1 ? '' : 's'}',
                                    style: TextStyle(
                                      color: isSelected
                                          ? Colors.white.withValues(alpha: 0.8)
                                          : Colors.white.withValues(alpha: 0.5),
                                      fontSize: 11,
                                    ),
                                  ),
                                ],
                              ],
                            ],
                          ),
                        ),
                        if (isSelected)
                          Icon(
                            Icons.check_circle,
                            color: Colors.white,
                            size: 20,
                          ),
                      ],
                    ),
                  ),
                ),
              );
            }),

            // Footer info
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (widget.poll.isMultipleChoice)
                  const Text(
                    'Multiple choice',
                    style: TextStyle(color: Colors.white70, fontSize: 12),
                  ),
                if (widget.poll.isAnonymous)
                  const Text(
                    'Anonymous poll',
                    style: TextStyle(color: Colors.white70, fontSize: 12),
                  ),
                Text(
                  '${widget.poll.totalVotes} total votes',
                  style: const TextStyle(color: Colors.white70, fontSize: 12),
                ),
              ],
            ),

            // Expiration info
            if (widget.poll.isExpired)
              Container(
                margin: const EdgeInsets.only(top: 8),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Text(
                  'Poll ended',
                  style: TextStyle(
                    color: Colors.red,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
