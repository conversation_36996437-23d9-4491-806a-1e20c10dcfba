import 'package:billionaires_social/features/profile/providers/profile_provider.dart';
import 'package:billionaires_social/features/stories/models/story_reel_model.dart';
import 'package:billionaires_social/features/stories/providers/story_provider.dart';
import 'package:billionaires_social/features/stories/screens/story_creation_screen.dart';
import 'package:billionaires_social/features/stories/screens/story_viewer_screen.dart';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// Enhanced Stories Carousel with additional features
class EnhancedStoriesCarousel extends ConsumerWidget {
  const EnhancedStoriesCarousel({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final storiesAsync = ref.watch(storyReelsProvider);
    final currentUser = FirebaseAuth.instance.currentUser;

    return storiesAsync.when(
      data: (reels) {
        if (reels.isEmpty) {
          return _buildEmptyState(context, ref);
        }

        // Separate user's own story reel to show it first
        StoryReel? ownReel;
        List<StoryReel> otherReels = List.from(reels);

        if (currentUser != null) {
          ownReel = reels.firstWhere(
            (reel) => reel.userId == currentUser.uid,
            orElse: () => StoryReel(
              id: currentUser.uid,
              userId: currentUser.uid,
              username: 'Your Story',
              userAvatarUrl: '',
              stories: [],
              isAllViewed: true,
              isCloseFriend: false,
            ),
          );
          otherReels.removeWhere((reel) => reel.userId == currentUser.uid);
        }

        return Container(
          height: 120,
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            border: Border(
              bottom: BorderSide(
                color: Theme.of(context).dividerColor.withValues(alpha: 0.1),
                width: 1,
              ),
            ),
          ),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            itemCount: otherReels.length + (ownReel != null ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == 0 && ownReel != null) {
                final hasActiveStory = ownReel.stories.isNotEmpty;
                return _EnhancedStoryAvatar(
                  reel: ownReel,
                  isOwnStory: true,
                  onTap: () {
                    if (hasActiveStory) {
                      ref
                          .read(storyReelsProvider.notifier)
                          .markReelAsViewed(ownReel!.id);
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => StoryViewerScreen(
                            reel: ownReel!,
                            isOwnStory: true,
                            allReels: reels,
                            initialReelIndex: 0,
                          ),
                        ),
                      );
                    } else {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const StoryCreationScreen(),
                          fullscreenDialog: true,
                        ),
                      );
                    }
                  },
                );
              }

              final reelIndex = (ownReel != null) ? index - 1 : index;
              final reel = otherReels[reelIndex];
              return _EnhancedStoryAvatar(
                reel: reel,
                isOwnStory: false,
                onTap: () =>
                    _onStoryTap(context, ref, reel, false, reels, index),
              );
            },
          ),
        );
      },
      loading: () => _buildLoadingState(context),
      error: (err, stack) => _buildErrorState(context, err),
    );
  }

  Widget _buildEmptyState(BuildContext context, WidgetRef ref) {
    final currentUser = FirebaseAuth.instance.currentUser;

    return Container(
      height: 120,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          // Add story button for current user
          if (currentUser != null)
            _AddStoryButton(
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const StoryCreationScreen(),
                    fullscreenDialog: true,
                  ),
                );
              },
            ),

          // Empty state message
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.camera_alt_outlined,
                    size: 32,
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withValues(alpha: 0.5),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'No stories yet',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Be the first to share a story!',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withValues(alpha: 0.5),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    return Container(
      height: 120,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: 5,
        itemBuilder: (context, index) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Column(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Theme.of(context).colorScheme.surface,
                  ),
                  child: const Center(
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  width: 40,
                  height: 12,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, Object error) {
    return Container(
      height: 120,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 32,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 8),
            Text(
              'Failed to load stories',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.error,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Pull to refresh',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.5),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _onStoryTap(
    BuildContext context,
    WidgetRef ref,
    StoryReel reel,
    bool isOwnStory,
    List<StoryReel> allReels,
    int reelIndex,
  ) {
    if (isOwnStory) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => const StoryCreationScreen(),
          fullscreenDialog: true,
        ),
      );
    } else {
      ref.read(storyReelsProvider.notifier).markReelAsViewed(reel.id);
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => StoryViewerScreen(
            reel: reel,
            isOwnStory: isOwnStory,
            allReels: allReels,
            initialReelIndex: reelIndex,
          ),
        ),
      );
    }
  }
}

/// Enhanced Story Avatar with additional features
class _EnhancedStoryAvatar extends ConsumerWidget {
  final StoryReel reel;
  final bool isOwnStory;
  final VoidCallback onTap;

  const _EnhancedStoryAvatar({
    required this.reel,
    required this.isOwnStory,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final currentUser = FirebaseAuth.instance.currentUser;
    final isOwnStory = currentUser != null && reel.userId == currentUser.uid;

    // For own story, fetch the actual profile data
    if (isOwnStory) {
      final currentUserProfile = ref.watch(profileProvider(reel.userId));

      return currentUserProfile.when(
        data: (profile) {
          final updatedReel = reel.copyWith(
            username: profile.name,
            userAvatarUrl: profile.profilePictureUrl.isNotEmpty
                ? profile.profilePictureUrl
                : 'https://i.pravatar.cc/150?u=${reel.userId}',
          );

          return _buildStoryAvatar(context, theme, updatedReel, ref);
        },
        loading: () => _buildStoryAvatar(context, theme, reel, ref),
        error: (_, _) => _buildStoryAvatar(context, theme, reel, ref),
      );
    }

    return _buildStoryAvatar(context, theme, reel, ref);
  }

  Widget _buildStoryAvatar(
    BuildContext context,
    ThemeData theme,
    StoryReel reel,
    WidgetRef ref,
  ) {
    final hasStories = reel.stories.isNotEmpty;
    final isCloseFriend = reel.isCloseFriend;
    final isAllViewed = reel.isAllViewed;
    final currentUser = FirebaseAuth.instance.currentUser;
    final isOwnStory = currentUser != null && reel.userId == currentUser.uid;

    return GestureDetector(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              alignment: Alignment.center,
              children: [
                // Story ring
                Container(
                  padding: const EdgeInsets.all(2.5),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: hasStories && !isAllViewed && !isOwnStory
                        ? LinearGradient(
                            colors: isCloseFriend
                                ? [Colors.green, Colors.greenAccent]
                                : [Colors.purple, Colors.pink],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          )
                        : null,
                    border: hasStories && !isAllViewed && !isOwnStory
                        ? null
                        : Border.all(
                            color: theme.colorScheme.outline.withValues(
                              alpha: 0.3,
                            ),
                            width: 1,
                          ),
                  ),
                  child: Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: theme.colorScheme.surface,
                    ),
                    child: ClipOval(child: _buildAvatarImage(reel, theme)),
                  ),
                ),

                // Add story icon for own story
                if (isOwnStory && !hasStories)
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: theme.colorScheme.surface,
                          width: 2,
                        ),
                      ),
                      child: Icon(
                        Icons.add,
                        size: 12,
                        color: theme.colorScheme.onPrimary,
                      ),
                    ),
                  ),

                // Close friends indicator
                if (isCloseFriend && !isOwnStory)
                  Positioned(
                    top: 0,
                    right: 0,
                    child: Container(
                      width: 16,
                      height: 16,
                      decoration: BoxDecoration(
                        color: Colors.green,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: theme.colorScheme.surface,
                          width: 2,
                        ),
                      ),
                      child: Icon(Icons.favorite, size: 8, color: Colors.white),
                    ),
                  ),

                // Story count indicator
                if (hasStories && reel.stories.length > 1)
                  Positioned(
                    top: 0,
                    left: 0,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 4,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        '${reel.stories.length}',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onPrimary,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 8),

            // Username
            SizedBox(
              width: 70,
              child: Text(
                reel.username,
                style: theme.textTheme.bodySmall?.copyWith(
                  fontSize: 11,
                  fontWeight: hasStories && !isAllViewed
                      ? FontWeight.w600
                      : FontWeight.normal,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAvatarImage(StoryReel reel, ThemeData theme) {
    if (reel.userAvatarUrl.isNotEmpty) {
      return CachedNetworkImage(
        imageUrl: reel.userAvatarUrl,
        fit: BoxFit.cover,
        placeholder: (context, url) => Container(
          color: theme.colorScheme.surface,
          child: Icon(
            Icons.person,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
          ),
        ),
        errorWidget: (context, url, error) => Container(
          color: theme.colorScheme.surface,
          child: Icon(
            Icons.person,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
          ),
        ),
      );
    } else {
      return Container(
        color: theme.colorScheme.surface,
        child: Icon(
          Icons.person,
          color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
        ),
      );
    }
  }
}

/// Add Story Button Widget
class _AddStoryButton extends StatelessWidget {
  final VoidCallback onTap;

  const _AddStoryButton({required this.onTap});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: theme.colorScheme.primary.withValues(alpha: 0.1),
                border: Border.all(
                  color: theme.colorScheme.primary.withValues(alpha: 0.3),
                  width: 2,
                  style: BorderStyle.solid,
                ),
              ),
              child: Icon(
                Icons.add,
                color: theme.colorScheme.primary,
                size: 24,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Add Story',
              style: theme.textTheme.bodySmall?.copyWith(
                fontSize: 11,
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
