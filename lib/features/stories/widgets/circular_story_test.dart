import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/stories/widgets/circular_story_carousel.dart';
import 'package:billionaires_social/features/stories/providers/story_provider.dart';
import 'package:billionaires_social/features/stories/models/story_reel_model.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/features/stories/utils/circular_story_debug.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// Simple test widget to verify circular story carousel functionality
class CircularStoryTest extends ConsumerWidget {
  const CircularStoryTest({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final storyReelsAsync = ref.watch(storyReelsProvider);
    final currentUser = FirebaseAuth.instance.currentUser;

    return Container(
      height: 300,
      color: const Color(0xFF0F3460),
      child: Column(
        children: [
          const Padding(
            padding: EdgeInsets.all(16.0),
            child: Text(
              'Circular Story Carousel Test',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          Expanded(
            child: Center(
              child: storyReelsAsync.when(
                data: (storyReels) {
                  debugPrint(
                    '🎯 CircularStoryTest: Received ${storyReels.length} story reels',
                  );

                  // Run diagnostic
                  CircularStoryDebug.runQuickDiagnostic(storyReels);

                  // If no real stories, create some test data
                  if (storyReels.isEmpty) {
                    debugPrint(
                      '🎯 No real stories found, creating test data...',
                    );
                    final testReels = _createTestStoryReels();

                    return CircularStoryCarousel(
                      storyReels: testReels,
                      centerAvatarUrl: currentUser?.photoURL,
                      centerUsername: currentUser?.displayName ?? 'You',
                      onCenterTap: () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Create Story tapped!'),
                            backgroundColor: Color(0xFFD4AF37),
                          ),
                        );
                      },
                      radius: 100.0,
                      itemSize: 50.0,
                    );
                  }

                  return CircularStoryCarousel(
                    storyReels: storyReels,
                    centerAvatarUrl: currentUser?.photoURL,
                    centerUsername: currentUser?.displayName ?? 'You',
                    onCenterTap: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Create Story tapped!'),
                          backgroundColor: Color(0xFFD4AF37),
                        ),
                      );
                    },
                    radius: 100.0,
                    itemSize: 50.0,
                  );
                },
                loading: () {
                  debugPrint('🎯 CircularStoryTest: Loading stories...');
                  return Container(
                    width: 200,
                    height: 200,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white.withValues(alpha: 0.1),
                      border: Border.all(
                        color: const Color(0xFFD4AF37).withValues(alpha: 0.3),
                        width: 2,
                      ),
                    ),
                    child: const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Color(0xFFD4AF37),
                            ),
                          ),
                          SizedBox(height: 16),
                          Text(
                            'Loading Stories...',
                            style: TextStyle(color: Colors.white70),
                          ),
                        ],
                      ),
                    ),
                  );
                },
                error: (error, stack) {
                  debugPrint(
                    '🎯 CircularStoryTest: Error loading stories: $error',
                  );
                  return Container(
                    width: 200,
                    height: 200,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white.withValues(alpha: 0.1),
                      border: Border.all(
                        color: Colors.red.withValues(alpha: 0.5),
                        width: 2,
                      ),
                    ),
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.error_outline,
                            color: Colors.red,
                            size: 48,
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'Error Loading Stories',
                            style: TextStyle(color: Colors.red),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            error.toString(),
                            style: const TextStyle(
                              color: Colors.white54,
                              fontSize: 12,
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ),

          // Debug info
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(
              'Drag to rotate • Tap story to view • Tap center to create',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.7),
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  List<StoryReel> _createTestStoryReels() {
    // Create some test story reels for demonstration
    final testReels = <StoryReel>[];

    final testUsers = [
      {
        'name': 'John Doe',
        'username': 'john_verified',
        'avatar': 'https://i.pravatar.cc/150?img=1',
      },
      {
        'name': 'Jane Smith',
        'username': 'jane_vip',
        'avatar': 'https://i.pravatar.cc/150?img=2',
      },
      {
        'name': 'Bob Wilson',
        'username': 'bob_billionaire',
        'avatar': 'https://i.pravatar.cc/150?img=3',
      },
      {
        'name': 'Alice Brown',
        'username': 'alice_public',
        'avatar': 'https://i.pravatar.cc/150?img=4',
      },
      {
        'name': 'Charlie Davis',
        'username': 'charlie_premium',
        'avatar': 'https://i.pravatar.cc/150?img=5',
      },
    ];

    for (int i = 0; i < testUsers.length; i++) {
      final user = testUsers[i];
      final testStoryItem = StoryItem(
        id: 'test_story_$i',
        userId: 'test_user_$i',
        mediaUrl: 'https://picsum.photos/400/600?random=$i',
        mediaType: MediaType.image,
        duration: const Duration(seconds: 15),
        timestamp: DateTime.now().subtract(Duration(hours: i)),
        textOverlay: 'Test story ${i + 1}',
        isCloseFriend: user['username']!.contains('vip'),
        isPublic: !user['username']!.contains('vip'),
        isSeen: false,
      );

      final testReel = StoryReel(
        id: 'test_reel_$i',
        userId: 'test_user_$i',
        username: user['username']!,
        userAvatarUrl: user['avatar']!,
        stories: [testStoryItem],
        isCloseFriend: user['username']!.contains('vip'),
        isAllViewed: false,
      );

      testReels.add(testReel);
    }

    debugPrint('🎯 Created ${testReels.length} test story reels');
    return testReels;
  }
}

/// Simple button to add circular story test to any screen
class CircularStoryTestButton extends StatelessWidget {
  const CircularStoryTestButton({super.key});

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton.extended(
      onPressed: () {
        showModalBottomSheet(
          context: context,
          backgroundColor: Colors.transparent,
          isScrollControlled: true,
          builder: (context) => DraggableScrollableSheet(
            initialChildSize: 0.6,
            maxChildSize: 0.9,
            minChildSize: 0.3,
            builder: (context, scrollController) => Container(
              decoration: const BoxDecoration(
                color: Color(0xFF0F3460),
                borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
              ),
              child: const CircularStoryTest(),
            ),
          ),
        );
      },
      backgroundColor: const Color(0xFFD4AF37),
      foregroundColor: Colors.black,
      icon: const Icon(Icons.circle),
      label: const Text('Test Circular Stories'),
    );
  }
}
