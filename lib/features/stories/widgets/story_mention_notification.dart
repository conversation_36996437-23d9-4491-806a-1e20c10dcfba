import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:billionaires_social/features/stories/services/story_mention_service.dart';

/// Widget for displaying story mention notifications
class StoryMentionNotification extends StatelessWidget {
  final String notificationId;
  final String storyId;
  final String storyOwnerId;
  final String storyOwnerName;
  final String storyOwnerAvatar;
  final DateTime timestamp;
  final bool isRead;
  final VoidCallback? onTap;
  final VoidCallback? onReply;

  const StoryMentionNotification({
    super.key,
    required this.notificationId,
    required this.storyId,
    required this.storyOwnerId,
    required this.storyOwnerName,
    required this.storyOwnerAvatar,
    required this.timestamp,
    required this.isRead,
    this.onTap,
    this.onReply,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: isRead ? Colors.white : Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isRead
              ? Colors.grey.withValues(alpha: 0.2)
              : Colors.blue.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(12),
        leading: CircleAvatar(
          radius: 24,
          backgroundImage: storyOwnerAvatar.isNotEmpty
              ? CachedNetworkImageProvider(storyOwnerAvatar)
              : null,
          child: storyOwnerAvatar.isEmpty
              ? const Icon(Icons.person, color: Colors.white)
              : null,
        ),
        title: RichText(
          text: TextSpan(
            style: const TextStyle(
              color: Colors.black,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            children: [
              TextSpan(
                text: storyOwnerName,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const TextSpan(text: ' mentioned you in their story'),
            ],
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              _formatTimestamp(timestamp),
              style: TextStyle(color: Colors.grey[600], fontSize: 12),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: onTap,
                    icon: const Icon(Icons.visibility, size: 16),
                    label: const Text('View Story'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      textStyle: const TextStyle(fontSize: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _showReplyDialog(context),
                    icon: const Icon(Icons.reply, size: 16),
                    label: const Text('Reply'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.blue,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      textStyle: const TextStyle(fontSize: 12),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: !isRead
            ? Container(
                width: 8,
                height: 8,
                decoration: const BoxDecoration(
                  color: Colors.blue,
                  shape: BoxShape.circle,
                ),
              )
            : null,
      ),
    );
  }

  void _showReplyDialog(BuildContext context) {
    final controller = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Reply to $storyOwnerName'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Reply to their story mention:',
              style: TextStyle(color: Colors.grey[600], fontSize: 14),
            ),
            const SizedBox(height: 12),
            TextField(
              controller: controller,
              decoration: const InputDecoration(
                hintText: 'Type your reply...',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
              autofocus: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (controller.text.trim().isNotEmpty) {
                // Store context before async operation
                final navigator = Navigator.of(context);
                // Handle reply
                await _handleReply(context, controller.text.trim());
                // Use stored navigator to avoid context issues
                navigator.pop();
              }
            },
            child: const Text('Send Reply'),
          ),
        ],
      ),
    );
  }

  Future<void> _handleReply(BuildContext context, String replyMessage) async {
    // Store ScaffoldMessenger before async operations
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    try {
      // Show loading
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('Sending reply...'),
          duration: Duration(seconds: 1),
        ),
      );

      // Send reply through mention service
      await StoryMentionService.handleMentionReply(
        storyId: storyId,
        replyMessage: replyMessage,
        replierId: '', // Will be filled by the service from current user
        replierName: '', // Will be filled by the service from current user
      );

      // Show success
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('Reply sent successfully!'),
          backgroundColor: Colors.green,
        ),
      );

      // Call onReply callback if provided
      onReply?.call();
    } catch (e) {
      // Show error
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('Failed to send reply: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }
}

/// Widget for story mention reply notifications
class StoryMentionReplyNotification extends StatelessWidget {
  final String notificationId;
  final String storyId;
  final String chatId;
  final String replierId;
  final String replierName;
  final String replierAvatar;
  final DateTime timestamp;
  final bool isRead;
  final VoidCallback? onTap;

  const StoryMentionReplyNotification({
    super.key,
    required this.notificationId,
    required this.storyId,
    required this.chatId,
    required this.replierId,
    required this.replierName,
    required this.replierAvatar,
    required this.timestamp,
    required this.isRead,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: isRead ? Colors.white : Colors.green.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isRead
              ? Colors.grey.withValues(alpha: 0.2)
              : Colors.green.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(12),
        leading: CircleAvatar(
          radius: 24,
          backgroundImage: replierAvatar.isNotEmpty
              ? CachedNetworkImageProvider(replierAvatar)
              : null,
          child: replierAvatar.isEmpty
              ? const Icon(Icons.person, color: Colors.white)
              : null,
        ),
        title: RichText(
          text: TextSpan(
            style: const TextStyle(
              color: Colors.black,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            children: [
              TextSpan(
                text: replierName,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const TextSpan(text: ' replied to your story mention'),
            ],
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              _formatTimestamp(timestamp),
              style: TextStyle(color: Colors.grey[600], fontSize: 12),
            ),
            const SizedBox(height: 8),
            ElevatedButton.icon(
              onPressed: onTap,
              icon: const Icon(Icons.chat, size: 16),
              label: const Text('View Chat'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                textStyle: const TextStyle(fontSize: 12),
              ),
            ),
          ],
        ),
        trailing: !isRead
            ? Container(
                width: 8,
                height: 8,
                decoration: const BoxDecoration(
                  color: Colors.green,
                  shape: BoxShape.circle,
                ),
              )
            : null,
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }
}
