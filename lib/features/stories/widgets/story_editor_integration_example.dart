import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/stories/widgets/user_tag_selector.dart';
import 'package:billionaires_social/features/stories/widgets/music_selector.dart';
import 'package:billionaires_social/features/stories/widgets/location_selector.dart';
import 'package:billionaires_social/features/stories/widgets/story_expiration_timer.dart';
import 'package:billionaires_social/features/stories/widgets/story_analytics_display.dart';
import 'package:billionaires_social/features/stories/models/story_reel_model.dart';
import 'package:billionaires_social/features/stories/services/story_service.dart';
import 'package:billionaires_social/features/notifications/services/notification_service.dart';
import 'package:billionaires_social/core/service_locator.dart';

// Example integration of all new features in Story Editor
class StoryEditorIntegrationExample extends ConsumerStatefulWidget {
  const StoryEditorIntegrationExample({super.key});

  @override
  ConsumerState<StoryEditorIntegrationExample> createState() =>
      _StoryEditorIntegrationExampleState();
}

class _StoryEditorIntegrationExampleState
    extends ConsumerState<StoryEditorIntegrationExample> {
  final StoryService _storyService = getIt<StoryService>();
  final NotificationService _notificationService = getIt<NotificationService>();

  // Story data
  List<String> _taggedUserIds = [];
  String? _selectedMusicPath;
  LocationResult? _selectedLocation;
  DateTime _storyCreatedAt = DateTime.now();
  String? _storyId;

  @override
  void dispose() {
    // Stop music preview when leaving
    _storyService.stopMusicPreview();
    super.dispose();
  }

  // Show user tag selector
  void _showUserTagSelector() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: UserTagSelector(
          selectedUserIds: _taggedUserIds,
          onUsersSelected: (userIds) {
            setState(() {
              _taggedUserIds = userIds;
            });
          },
        ),
      ),
    );
  }

  // Show music selector
  void _showMusicSelector() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: MusicSelector(
          selectedMusicPath: _selectedMusicPath,
          onMusicSelected: (musicPath) {
            setState(() {
              _selectedMusicPath = musicPath;
            });
          },
        ),
      ),
    );
  }

  // Show location selector
  void _showLocationSelector() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: LocationSelector(
          selectedLocation: _selectedLocation,
          onLocationSelected: (location) {
            setState(() {
              _selectedLocation = location;
            });
          },
        ),
      ),
    );
  }

  // Show analytics
  void _showAnalytics() {
    if (_storyId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Story must be published first to view analytics'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: StoryAnalyticsDisplay(storyId: _storyId!, showDetailed: true),
      ),
    );
  }

  // Publish story with all features
  Future<void> _publishStory() async {
    try {
      // Simulate story creation
      _storyId = 'story_${DateTime.now().millisecondsSinceEpoch}';
      _storyCreatedAt = DateTime.now();

      // Send notifications to tagged users
      for (final userId in _taggedUserIds) {
        await _notificationService.createStoryTagNotification(
          _storyId!,
          userId,
          'current_user_id', // Replace with actual current user ID
        );
      }

      // Track initial view (creator viewing their own story)
      await _storyService.trackStoryView(_storyId!, 'current_user_id');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Story published successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error publishing story: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          'Story Editor',
          style: TextStyle(color: Colors.white),
        ),
        actions: [
          TextButton(
            onPressed: _publishStory,
            child: const Text(
              'Publish',
              style: TextStyle(color: Colors.blue, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Story preview area
            Container(
              height: 400,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey[900],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.white24),
              ),
              child: Stack(
                children: [
                  // Story content would go here
                  const Center(
                    child: Text(
                      'Story Content Preview',
                      style: TextStyle(color: Colors.white54),
                    ),
                  ),

                  // Location overlay
                  if (_selectedLocation != null)
                    Positioned(
                      bottom: 16,
                      left: 16,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.7),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.location_on,
                              color: Colors.white,
                              size: 16,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              _selectedLocation!.name,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                  // Music overlay
                  if (_selectedMusicPath != null)
                    Positioned(
                      top: 16,
                      right: 16,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.7),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.music_note,
                              color: Colors.white,
                              size: 16,
                            ),
                            const SizedBox(width: 4),
                            const Text(
                              'Music',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                  // Tagged users overlay
                  if (_taggedUserIds.isNotEmpty)
                    Positioned(
                      top: 16,
                      left: 16,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.7),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.person_add,
                              color: Colors.white,
                              size: 16,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${_taggedUserIds.length} tagged',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Feature buttons
            const Text(
              'Story Features',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Feature grid
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 2.5,
              children: [
                _buildFeatureButton(
                  'Tag People',
                  Icons.person_add,
                  _taggedUserIds.isNotEmpty
                      ? '${_taggedUserIds.length} tagged'
                      : null,
                  _showUserTagSelector,
                ),
                _buildFeatureButton(
                  'Add Music',
                  Icons.music_note,
                  _selectedMusicPath != null ? 'Selected' : null,
                  _showMusicSelector,
                ),
                _buildFeatureButton(
                  'Add Location',
                  Icons.location_on,
                  _selectedLocation?.name,
                  _showLocationSelector,
                ),
                _buildFeatureButton(
                  'Analytics',
                  Icons.analytics,
                  null,
                  _showAnalytics,
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Story expiration widget
            if (_storyId != null) ...[
              const Text(
                'Story Status',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              StoryExpirationWidget(
                createdAt: _storyCreatedAt,
                onExpired: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Story has expired!'),
                      backgroundColor: Colors.red,
                    ),
                  );
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureButton(
    String title,
    IconData icon,
    String? subtitle,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.white24),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: Colors.white, size: 24),
                const Spacer(),
                if (subtitle != null)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      subtitle,
                      style: const TextStyle(
                        color: Colors.blue,
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
