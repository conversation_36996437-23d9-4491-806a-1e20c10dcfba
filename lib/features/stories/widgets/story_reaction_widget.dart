import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/stories/models/story_interaction_model.dart';
import 'package:billionaires_social/features/stories/services/story_interaction_service.dart';
import 'package:billionaires_social/core/service_locator.dart';

class StoryReactionWidget extends ConsumerStatefulWidget {
  final String storyId;
  final StoryReactionType? currentReaction;
  final int reactionCount;
  final VoidCallback? onReactionChanged;

  const StoryReactionWidget({
    super.key,
    required this.storyId,
    this.currentReaction,
    this.reactionCount = 0,
    this.onReactionChanged,
  });

  @override
  ConsumerState<StoryReactionWidget> createState() =>
      _StoryReactionWidgetState();
}

class _StoryReactionWidgetState extends ConsumerState<StoryReactionWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _showReactionPicker = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.elasticOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _handleReaction(StoryReactionType reactionType) async {
    try {
      final interactionService = getIt<StoryInteractionService>();
      await interactionService.addReaction(widget.storyId, reactionType);

      // Animate the reaction
      _animationController.forward().then((_) {
        _animationController.reverse();
      });

      // Provide haptic feedback
      HapticFeedback.lightImpact();

      // Notify parent
      widget.onReactionChanged?.call();

      // Hide reaction picker
      setState(() {
        _showReactionPicker = false;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to add reaction: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _toggleReactionPicker() {
    setState(() {
      _showReactionPicker = !_showReactionPicker;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Main reaction button
        GestureDetector(
          onTap: _toggleReactionPicker,
          onLongPress: () {
            if (widget.currentReaction != null) {
              _handleReaction(widget.currentReaction!);
            }
          },
          child: AnimatedBuilder(
            animation: _scaleAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _scaleAnimation.value,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: widget.currentReaction != null
                        ? _getReactionColor(
                            widget.currentReaction!,
                          ).withValues(alpha: 0.2)
                        : Colors.black.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        _getReactionIcon(widget.currentReaction),
                        color: widget.currentReaction != null
                            ? _getReactionColor(widget.currentReaction!)
                            : Colors.white,
                        size: 20,
                      ),
                      if (widget.reactionCount > 0) ...[
                        const SizedBox(width: 4),
                        Text(
                          _formatCount(widget.reactionCount),
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                      ],
                    ],
                  ),
                ),
              );
            },
          ),
        ),

        // Reaction picker overlay
        if (_showReactionPicker)
          Positioned(
            bottom: 50,
            left: 0,
            right: 0,
            child: _buildReactionPicker(),
          ),
      ],
    );
  }

  Widget _buildReactionPicker() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: StoryReactionType.values.map((reaction) {
          final isSelected = widget.currentReaction == reaction;
          return GestureDetector(
            onTap: () => _handleReaction(reaction),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 150),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: isSelected
                    ? _getReactionColor(reaction).withValues(alpha: 0.3)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(
                _getReactionIcon(reaction),
                color: _getReactionColor(reaction),
                size: 24,
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  IconData _getReactionIcon(StoryReactionType? type) {
    switch (type) {
      case StoryReactionType.like:
        return Icons.favorite;
      case StoryReactionType.love:
        return Icons.favorite;
      case StoryReactionType.haha:
        return Icons.sentiment_satisfied;
      case StoryReactionType.wow:
        return Icons.sentiment_neutral;
      case StoryReactionType.sad:
        return Icons.sentiment_dissatisfied;
      case StoryReactionType.angry:
        return Icons.sentiment_very_dissatisfied;
      default:
        return Icons.favorite_border;
    }
  }

  Color _getReactionColor(StoryReactionType type) {
    switch (type) {
      case StoryReactionType.like:
      case StoryReactionType.love:
        return Colors.red;
      case StoryReactionType.haha:
        return Colors.yellow.shade700;
      case StoryReactionType.wow:
        return Colors.orange;
      case StoryReactionType.sad:
        return Colors.blue;
      case StoryReactionType.angry:
        return Colors.red.shade800;
    }
  }

  String _formatCount(int count) {
    if (count < 1000) {
      return count.toString();
    } else if (count < 1000000) {
      return '${(count / 1000).toStringAsFixed(1)}K';
    } else {
      return '${(count / 1000000).toStringAsFixed(1)}M';
    }
  }
}

class StoryReactionDisplay extends StatelessWidget {
  final Map<StoryReactionType, int> reactionBreakdown;
  final int totalReactions;

  const StoryReactionDisplay({
    super.key,
    required this.reactionBreakdown,
    required this.totalReactions,
  });

  @override
  Widget build(BuildContext context) {
    if (totalReactions == 0) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.6),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Show top 3 reactions
          ...reactionBreakdown.entries
              .take(3)
              .map(
                (entry) => Padding(
                  padding: const EdgeInsets.only(right: 4),
                  child: Icon(
                    _getReactionIcon(entry.key),
                    color: _getReactionColor(entry.key),
                    size: 16,
                  ),
                ),
              ),
          const SizedBox(width: 8),
          Text(
            _formatCount(totalReactions),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getReactionIcon(StoryReactionType type) {
    switch (type) {
      case StoryReactionType.like:
        return Icons.favorite;
      case StoryReactionType.love:
        return Icons.favorite;
      case StoryReactionType.haha:
        return Icons.sentiment_satisfied;
      case StoryReactionType.wow:
        return Icons.sentiment_neutral;
      case StoryReactionType.sad:
        return Icons.sentiment_dissatisfied;
      case StoryReactionType.angry:
        return Icons.sentiment_very_dissatisfied;
    }
  }

  Color _getReactionColor(StoryReactionType type) {
    switch (type) {
      case StoryReactionType.like:
      case StoryReactionType.love:
        return Colors.red;
      case StoryReactionType.haha:
        return Colors.yellow.shade700;
      case StoryReactionType.wow:
        return Colors.orange;
      case StoryReactionType.sad:
        return Colors.blue;
      case StoryReactionType.angry:
        return Colors.red.shade800;
    }
  }

  String _formatCount(int count) {
    if (count < 1000) {
      return count.toString();
    } else if (count < 1000000) {
      return '${(count / 1000).toStringAsFixed(1)}K';
    } else {
      return '${(count / 1000000).toStringAsFixed(1)}M';
    }
  }
}
