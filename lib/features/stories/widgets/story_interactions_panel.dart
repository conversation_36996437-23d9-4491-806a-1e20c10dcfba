import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:billionaires_social/features/stories/models/story_interaction_model.dart';
import 'package:billionaires_social/features/stories/services/story_interaction_service.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'dart:async';

/// Panel to show story interactions (replies and reactions) for story owners
class StoryInteractionsPanel extends ConsumerStatefulWidget {
  final String storyId;
  final VoidCallback onClose;

  const StoryInteractionsPanel({
    super.key,
    required this.storyId,
    required this.onClose,
  });

  @override
  ConsumerState<StoryInteractionsPanel> createState() =>
      _StoryInteractionsPanelState();
}

class _StoryInteractionsPanelState extends ConsumerState<StoryInteractionsPanel>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final StoryInteractionService _interactionService = StoryInteractionService();

  List<StoryReply> _replies = [];
  List<StoryReaction> _reactions = [];
  StoryAnalytics? _analytics;
  bool _isLoading = true;

  // Stream subscriptions for cleanup
  StreamSubscription<List<StoryReply>>? _repliesSubscription;
  StreamSubscription<List<StoryReaction>>? _reactionsSubscription;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: 3,
      vsync: this,
    ); // Added analytics tab
    _loadInteractions();
    _loadAnalytics();
  }

  @override
  void dispose() {
    _repliesSubscription?.cancel();
    _reactionsSubscription?.cancel();
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadInteractions() async {
    setState(() => _isLoading = true);

    try {
      // Listen to replies stream
      _repliesSubscription = _interactionService
          .getStoryReplies(widget.storyId)
          .listen(
            (replies) {
              if (mounted) {
                setState(() {
                  _replies = replies;
                });
              }
            },
            onError: (error) {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Failed to load replies: $error'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
          );

      // Listen to reactions stream
      _reactionsSubscription = _interactionService
          .getStoryReactions(widget.storyId)
          .listen(
            (reactions) {
              if (mounted) {
                setState(() {
                  _reactions = reactions;
                });
              }
            },
            onError: (error) {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Failed to load reactions: $error'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
          );

      setState(() => _isLoading = false);
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load interactions: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadAnalytics() async {
    try {
      debugPrint('📊 Loading analytics for story: ${widget.storyId}');
      final analytics = await _interactionService.getStoryAnalytics(
        widget.storyId,
      );
      debugPrint('📊 Analytics result: $analytics');
      if (mounted) {
        setState(() {
          _analytics = analytics;
        });
        debugPrint('📊 Analytics state updated: ${_analytics != null}');
      }
    } catch (e) {
      debugPrint('❌ Analytics loading error: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load analytics: $e'),
            backgroundColor: Colors.red,
          ),
        );
        // Set a default analytics object to prevent infinite loading
        setState(() {
          _analytics = StoryAnalytics(
            storyId: widget.storyId,
            viewCount: 0,
            uniqueViewers: 0,
            replyCount: 0,
            reactionCount: 0,
            reactionBreakdown: {},
            averageViewDuration: const Duration(seconds: 5),
            createdAt: DateTime.now(),
            expiresAt: DateTime.now().add(const Duration(hours: 24)),
            viewedBy: [],
            repliedBy: [],
            reactedBy: [],
          );
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: const BoxDecoration(
        color: Color(0xFF1C1C1E),
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[600],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header with close button
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            child: Row(
              children: [
                const Expanded(
                  child: Text(
                    'Story Insights',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: widget.onClose,
                  icon: const Icon(Icons.close, color: Colors.white),
                ),
              ],
            ),
          ),

          // Tab bar
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 20),
            decoration: BoxDecoration(
              color: Colors.grey[900],
              borderRadius: BorderRadius.circular(25),
            ),
            child: TabBar(
              controller: _tabController,
              indicator: BoxDecoration(
                color: Colors.blue,
                borderRadius: BorderRadius.circular(25),
              ),
              labelColor: Colors.white,
              unselectedLabelColor: Colors.grey[400],
              dividerColor: Colors.transparent,
              tabs: [
                Tab(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.analytics_outlined, size: 16),
                      const SizedBox(width: 4),
                      const Text('Analytics'),
                    ],
                  ),
                ),
                Tab(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.chat_bubble_outline, size: 16),
                      const SizedBox(width: 4),
                      Text('Replies (${_replies.length})'),
                    ],
                  ),
                ),
                Tab(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.favorite_border, size: 16),
                      const SizedBox(width: 4),
                      Text('Reactions (${_reactions.length})'),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // Tab content
          Expanded(
            child: _isLoading
                ? Center(child: AppTheme.loadingIndicator())
                : TabBarView(
                    controller: _tabController,
                    children: [
                      _buildAnalyticsTab(),
                      _buildRepliesTab(),
                      _buildReactionsTab(),
                    ],
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnalyticsTab() {
    if (_analytics == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.analytics_outlined, size: 64, color: Colors.grey[600]),
            const SizedBox(height: 16),
            Text(
              'Loading analytics...',
              style: TextStyle(color: Colors.grey[400], fontSize: 16),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Overview Cards
          Row(
            children: [
              Expanded(
                child: _buildAnalyticsCard(
                  title: 'Views',
                  value: _analytics!.viewCount.toString(),
                  icon: Icons.visibility_outlined,
                  color: Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildAnalyticsCard(
                  title: 'Reach',
                  value: _analytics!.uniqueViewers.toString(),
                  icon: Icons.people_outline,
                  color: Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          Row(
            children: [
              Expanded(
                child: _buildAnalyticsCard(
                  title: 'Replies',
                  value: _analytics!.replyCount.toString(),
                  icon: Icons.chat_bubble_outline,
                  color: Colors.orange,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildAnalyticsCard(
                  title: 'Reactions',
                  value: _analytics!.reactionCount.toString(),
                  icon: Icons.favorite_border,
                  color: Colors.red,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Engagement Rate
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.grey[900],
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.trending_up, color: Colors.purple, size: 24),
                    const SizedBox(width: 12),
                    const Text(
                      'Engagement Rate',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  '${_calculateEngagementRate().toStringAsFixed(1)}%',
                  style: const TextStyle(
                    color: Colors.purple,
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Based on interactions vs views',
                  style: TextStyle(color: Colors.grey[400], fontSize: 14),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Reaction Breakdown
          if (_analytics!.reactionBreakdown.isNotEmpty) ...[
            const Text(
              'Reaction Breakdown',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            ..._analytics!.reactionBreakdown.entries.map((entry) {
              return Container(
                margin: const EdgeInsets.only(bottom: 12),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[900],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    Text(
                      _getReactionEmoji(entry.key),
                      style: const TextStyle(fontSize: 24),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        entry.key.name.toUpperCase(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.blue,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        entry.value.toString(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }),
            const SizedBox(height: 24),
          ],

          // Time Stats
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.grey[900],
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.access_time, color: Colors.cyan, size: 24),
                    const SizedBox(width: 12),
                    const Text(
                      'Average View Time',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  '${_analytics!.averageViewDuration.inSeconds}s',
                  style: const TextStyle(
                    color: Colors.cyan,
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'How long people watched your story',
                  style: TextStyle(color: Colors.grey[400], fontSize: 14),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildAnalyticsCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Text(
                title,
                style: TextStyle(
                  color: Colors.grey[400],
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  double _calculateEngagementRate() {
    if (_analytics == null || _analytics!.viewCount == 0) return 0.0;

    final totalEngagements = _analytics!.replyCount + _analytics!.reactionCount;
    return (totalEngagements / _analytics!.viewCount) * 100;
  }

  Widget _buildRepliesTab() {
    if (_replies.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.chat_bubble_outline, size: 64, color: Colors.grey[600]),
            const SizedBox(height: 16),
            Text(
              'No replies yet',
              style: TextStyle(color: Colors.grey[400], fontSize: 16),
            ),
            const SizedBox(height: 8),
            Text(
              'When people reply to your story, you\'ll see them here',
              style: TextStyle(color: Colors.grey[600], fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      itemCount: _replies.length,
      itemBuilder: (context, index) {
        final reply = _replies[index];
        return _buildReplyItem(reply);
      },
    );
  }

  Widget _buildReactionsTab() {
    if (_reactions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.favorite_border, size: 64, color: Colors.grey[600]),
            const SizedBox(height: 16),
            Text(
              'No reactions yet',
              style: TextStyle(color: Colors.grey[400], fontSize: 16),
            ),
            const SizedBox(height: 8),
            Text(
              'When people react to your story, you\'ll see them here',
              style: TextStyle(color: Colors.grey[600], fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      itemCount: _reactions.length,
      itemBuilder: (context, index) {
        final reaction = _reactions[index];
        return _buildReactionItem(reaction);
      },
    );
  }

  Widget _buildReplyItem(StoryReply reply) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundImage: reply.userAvatarUrl.isNotEmpty
                    ? CachedNetworkImageProvider(reply.userAvatarUrl)
                    : null,
                child: reply.userAvatarUrl.isEmpty
                    ? const Icon(Icons.person, color: Colors.white)
                    : null,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      reply.username,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      _formatTimeAgo(reply.timestamp),
                      style: TextStyle(color: Colors.grey[400], fontSize: 12),
                    ),
                  ],
                ),
              ),
              IconButton(
                onPressed: () => _replyToMessage(reply),
                icon: const Icon(Icons.reply, color: Colors.blue, size: 20),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            reply.content,
            style: const TextStyle(color: Colors.white, fontSize: 14),
          ),
        ],
      ),
    );
  }

  Widget _buildReactionItem(StoryReaction reaction) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundImage: reaction.userAvatarUrl.isNotEmpty
                ? CachedNetworkImageProvider(reaction.userAvatarUrl)
                : null,
            child: reaction.userAvatarUrl.isEmpty
                ? const Icon(Icons.person, color: Colors.white)
                : null,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  reaction.username,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  _formatTimeAgo(reaction.timestamp),
                  style: TextStyle(color: Colors.grey[400], fontSize: 12),
                ),
              ],
            ),
          ),
          Text(
            _getReactionEmoji(reaction.type),
            style: const TextStyle(fontSize: 24),
          ),
        ],
      ),
    );
  }

  String _getReactionEmoji(StoryReactionType type) {
    switch (type) {
      case StoryReactionType.like:
        return '👍';
      case StoryReactionType.love:
        return '❤️';
      case StoryReactionType.haha:
        return '😂';
      case StoryReactionType.wow:
        return '😮';
      case StoryReactionType.sad:
        return '😢';
      case StoryReactionType.angry:
        return '😡';
    }
  }

  String _formatTimeAgo(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  void _replyToMessage(StoryReply reply) {
    // Navigate to direct message screen with this user
    try {
      // For now, show a confirmation that we would navigate to DM
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          backgroundColor: const Color(0xFF1C1C1E),
          title: const Text(
            'Reply to Message',
            style: TextStyle(color: Colors.white),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Original message:',
                style: TextStyle(color: Colors.grey[400], fontSize: 12),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[900],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  reply.content,
                  style: const TextStyle(color: Colors.white, fontSize: 14),
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'This will open a direct message conversation with ${reply.username}.',
                style: const TextStyle(color: Colors.white70),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // TODO: Navigate to actual DM screen when implemented
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Opening chat with ${reply.username}...'),
                    backgroundColor: Colors.blue,
                  ),
                );
              },
              child: const Text('Open Chat'),
            ),
          ],
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to open chat: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
