import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'interactive_text_editor.dart';
import 'text_styling_controls.dart';

/// Manages text overlays for story editing with Instagram-style gestures
class StoryTextOverlayManager extends StatefulWidget {
  final Size screenSize;
  final List<TextElement> initialTextElements;
  final Function(List<TextElement>) onTextElementsChanged;
  final VoidCallback? onAddText;

  const StoryTextOverlayManager({
    super.key,
    required this.screenSize,
    this.initialTextElements = const [],
    required this.onTextElementsChanged,
    this.onAddText,
  });

  @override
  State<StoryTextOverlayManager> createState() =>
      _StoryTextOverlayManagerState();
}

class _StoryTextOverlayManagerState extends State<StoryTextOverlayManager> {
  List<TextElement> _textElements = [];
  int? _selectedTextIndex;
  bool _showStylingControls = false;

  @override
  void initState() {
    super.initState();
    _textElements = List.from(widget.initialTextElements);
  }

  void _addNewTextElement({Offset? position}) {
    final newPosition =
        position ??
        Offset(
          widget.screenSize.width / 2 - 50,
          widget.screenSize.height / 2 - 50,
        );

    final newElement = TextElement(
      text: '',
      position: newPosition,
      size: 24.0,
      color: Colors.white,
      fontFamily: 'Classic',
    );

    setState(() {
      _textElements.add(newElement);
      _selectedTextIndex = _textElements.length - 1;
      _showStylingControls = true;
    });

    widget.onTextElementsChanged(_textElements);
    HapticFeedback.lightImpact();
  }

  void _updateTextElement(int index, TextElement element) {
    if (index >= 0 && index < _textElements.length) {
      setState(() {
        _textElements[index] = element;
      });
      widget.onTextElementsChanged(_textElements);
    }
  }

  void _deleteTextElement(int index) {
    if (index >= 0 && index < _textElements.length) {
      setState(() {
        _textElements.removeAt(index);
        _selectedTextIndex = null;
        _showStylingControls = false;
      });
      widget.onTextElementsChanged(_textElements);
      HapticFeedback.mediumImpact();
    }
  }

  void _selectTextElement(int index) {
    setState(() {
      _selectedTextIndex = index;
      _showStylingControls = true;
    });
    HapticFeedback.lightImpact();
  }

  void _deselectAll() {
    setState(() {
      _selectedTextIndex = null;
      _showStylingControls = false;
    });
  }

  void _onTextColorChanged(Color color) {
    if (_selectedTextIndex != null) {
      final element = _textElements[_selectedTextIndex!];
      _updateTextElement(
        _selectedTextIndex!,
        TextElement(
          text: element.text,
          position: element.position,
          size: element.size,
          rotation: element.rotation,
          color: color,
          backgroundColor: element.backgroundColor,
          fontFamily: element.fontFamily,
          alignment: element.alignment,
          hasShadow: element.hasShadow,
        ),
      );
    }
  }

  void _onBackgroundColorChanged(Color color) {
    if (_selectedTextIndex != null) {
      final element = _textElements[_selectedTextIndex!];
      _updateTextElement(
        _selectedTextIndex!,
        TextElement(
          text: element.text,
          position: element.position,
          size: element.size,
          rotation: element.rotation,
          color: element.color,
          backgroundColor: color,
          fontFamily: element.fontFamily,
          alignment: element.alignment,
          hasShadow: element.hasShadow,
        ),
      );
    }
  }

  void _onStyleToggle() {
    if (_selectedTextIndex != null) {
      final element = _textElements[_selectedTextIndex!];
      bool newHasShadow = element.hasShadow;
      Color newBackgroundColor = element.backgroundColor;

      if (element.backgroundColor != Colors.transparent) {
        // Has background -> Shadow
        newBackgroundColor = Colors.transparent;
        newHasShadow = true;
      } else if (element.hasShadow) {
        // Has shadow -> None
        newHasShadow = false;
      } else {
        // None -> Background
        newBackgroundColor = Colors.black.withValues(alpha: 0.7);
        newHasShadow = false;
      }

      _updateTextElement(
        _selectedTextIndex!,
        TextElement(
          text: element.text,
          position: element.position,
          size: element.size,
          rotation: element.rotation,
          color: element.color,
          backgroundColor: newBackgroundColor,
          fontFamily: element.fontFamily,
          alignment: element.alignment,
          hasShadow: newHasShadow,
        ),
      );
    }
  }

  void _onFontChanged(String fontFamily) {
    if (_selectedTextIndex != null) {
      final element = _textElements[_selectedTextIndex!];
      _updateTextElement(
        _selectedTextIndex!,
        TextElement(
          text: element.text,
          position: element.position,
          size: element.size,
          rotation: element.rotation,
          color: element.color,
          backgroundColor: element.backgroundColor,
          fontFamily: fontFamily,
          alignment: element.alignment,
          hasShadow: element.hasShadow,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Background tap detector
        Positioned.fill(
          child: GestureDetector(
            onTapDown: (details) {
              // Check if tap is on empty space
              bool tappedOnText = false;
              for (int i = 0; i < _textElements.length; i++) {
                final element = _textElements[i];
                final textRect = Rect.fromLTWH(
                  element.position.dx - 50,
                  element.position.dy - 25,
                  100,
                  50,
                );
                if (textRect.contains(details.localPosition)) {
                  tappedOnText = true;
                  break;
                }
              }

              if (!tappedOnText) {
                _deselectAll();
              }
            },
            onDoubleTap: () {
              // Double tap to add new text
              _addNewTextElement();
            },
            child: Container(color: Colors.transparent),
          ),
        ),

        // Text elements
        ..._textElements.asMap().entries.map((entry) {
          final index = entry.key;
          final element = entry.value;

          return InteractiveTextEditor(
            key: ValueKey('text_$index'),
            initialText: element.text,
            initialPosition: element.position,
            initialSize: element.size,
            initialRotation: element.rotation,
            initialTextColor: element.color,
            initialBackgroundColor: element.backgroundColor,
            initialFontFamily: element.fontFamily,
            initialAlignment: element.alignment,
            initialHasShadow: element.hasShadow,
            initialHasBackground: element.backgroundColor != Colors.transparent,
            onTextChanged: (updatedElement) {
              _updateTextElement(index, updatedElement);
            },
            onComplete: () {
              if (element.text.isEmpty) {
                _deleteTextElement(index);
              } else {
                _selectTextElement(index);
              }
            },
          );
        }),

        // Styling controls
        if (_showStylingControls && _selectedTextIndex != null)
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: TextStylingControls(
              currentTextColor: _textElements[_selectedTextIndex!].color,
              currentBackgroundColor:
                  _textElements[_selectedTextIndex!].backgroundColor,
              hasShadow: _textElements[_selectedTextIndex!].hasShadow,
              hasBackground:
                  _textElements[_selectedTextIndex!].backgroundColor !=
                  Colors.transparent,
              currentFont: _textElements[_selectedTextIndex!].fontFamily,
              onTextColorChanged: _onTextColorChanged,
              onBackgroundColorChanged: _onBackgroundColorChanged,
              onStyleToggle: _onStyleToggle,
              onFontChanged: _onFontChanged,
              onDelete: () => _deleteTextElement(_selectedTextIndex!),
              onDone: _deselectAll,
            ),
          ),
      ],
    );
  }
}
