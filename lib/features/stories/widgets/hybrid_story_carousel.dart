import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:billionaires_social/features/stories/models/story_reel_model.dart';
import 'package:billionaires_social/features/stories/screens/story_viewer_screen.dart';
import 'package:billionaires_social/features/stories/screens/story_creation_screen.dart';
import 'package:billionaires_social/core/widgets/billionaire_badge.dart';

/// Hybrid story carousel that combines circular layout with horizontal swipe navigation
class HybridStoryCarousel extends ConsumerStatefulWidget {
  final List<StoryReel> storyReels;
  final String? centerAvatarUrl;
  final String? centerUsername;
  final bool useCircularLayout;

  const HybridStoryCarousel({
    super.key,
    required this.storyReels,
    this.centerAvatarUrl,
    this.centerUsername,
    this.useCircularLayout = false, // Default to traditional horizontal
  });

  @override
  ConsumerState<HybridStoryCarousel> createState() =>
      _HybridStoryCarouselState();
}

class _HybridStoryCarouselState extends ConsumerState<HybridStoryCarousel>
    with TickerProviderStateMixin {
  final PageController _pageController = PageController();

  // Circular rotation state
  double _rotation = 0.0;
  int _selectedStoryIndex = 0;
  int _currentCircularPage = 0; // For pagination through 1000+ stories
  bool _isDragging = false;
  late AnimationController _rotationController;
  Animation<double>?
  _rotationAnimation; // Make nullable to avoid late init error

  // Get stories for current circular page (8 stories per page)
  List<StoryReel> get _currentPageStories {
    const storiesPerPage = 8;
    final startIndex = _currentCircularPage * storiesPerPage;
    final endIndex = math.min(
      startIndex + storiesPerPage,
      widget.storyReels.length,
    );

    if (startIndex >= widget.storyReels.length) return [];
    return widget.storyReels.sublist(startIndex, endIndex);
  }

  // Get total pages for circular navigation
  int get _totalCircularPages {
    const storiesPerPage = 8;
    return (widget.storyReels.length / storiesPerPage).ceil();
  }

  @override
  void initState() {
    super.initState();
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    _rotationController.dispose();
    super.dispose();
  }

  // Handle pan start for circular rotation
  void _onPanStart(DragStartDetails details) {
    if (!widget.useCircularLayout) return;
    _isDragging = true;
    _rotationController.stop();
  }

  // Handle pan update for circular rotation
  void _onPanUpdate(DragUpdateDetails details) {
    if (!widget.useCircularLayout || !_isDragging) return;

    // Calculate rotation based on drag
    final center = const Offset(100, 100); // Center of 200x200 circle
    final angle = math.atan2(
      details.localPosition.dy - center.dy,
      details.localPosition.dx - center.dx,
    );

    setState(() {
      _rotation = angle;
    });

    // Update selected story index based on current page
    final currentPageStories = _currentPageStories;
    if (currentPageStories.isNotEmpty) {
      final normalizedAngle = (_rotation + math.pi * 2) % (math.pi * 2);
      final newIndex =
          ((normalizedAngle / (math.pi * 2)) * currentPageStories.length)
              .round() %
          currentPageStories.length;

      if (newIndex != _selectedStoryIndex) {
        setState(() {
          _selectedStoryIndex = newIndex;
        });
        debugPrint(
          '🎯 Selected story index: $_selectedStoryIndex (Page ${_currentCircularPage + 1})',
        );
      }
    }
  }

  // Handle pan end for circular rotation
  void _onPanEnd(DragEndDetails details) {
    if (!widget.useCircularLayout) return;
    _isDragging = false;
    _snapToNearestStory();
  }

  // Snap to nearest story
  void _snapToNearestStory() {
    final currentPageStories = _currentPageStories;
    if (currentPageStories.isEmpty) return;

    final targetAngle =
        (_selectedStoryIndex * 2 * math.pi) / currentPageStories.length;

    _rotationAnimation = Tween<double>(begin: _rotation, end: targetAngle)
        .animate(
          CurvedAnimation(
            parent: _rotationController,
            curve: Curves.easeOutCubic,
          ),
        );

    _rotationController.forward(from: 0.0).then((_) {
      setState(() {
        _rotation = targetAngle;
      });
    });
  }

  // Navigate to next story in circle (with page support)
  void _nextStory() {
    if (!widget.useCircularLayout) return;
    final currentPageStories = _currentPageStories;
    if (currentPageStories.isEmpty) return;

    if (_selectedStoryIndex < currentPageStories.length - 1) {
      // Move to next story in current page
      setState(() {
        _selectedStoryIndex++;
      });
      _snapToNearestStory();
    } else if (_currentCircularPage < _totalCircularPages - 1) {
      // Move to first story of next page
      setState(() {
        _currentCircularPage++;
        _selectedStoryIndex = 0;
        _rotation = 0.0;
      });
      debugPrint(
        '🎯 Moved to page ${_currentCircularPage + 1} of $_totalCircularPages',
      );
    } else {
      // Wrap around to first page, first story
      setState(() {
        _currentCircularPage = 0;
        _selectedStoryIndex = 0;
        _rotation = 0.0;
      });
      debugPrint('🎯 Wrapped to first page');
    }
  }

  // Navigate to previous story in circle (with page support)
  void _previousStory() {
    if (!widget.useCircularLayout) return;
    final currentPageStories = _currentPageStories;
    if (currentPageStories.isEmpty) return;

    if (_selectedStoryIndex > 0) {
      // Move to previous story in current page
      setState(() {
        _selectedStoryIndex--;
      });
      _snapToNearestStory();
    } else if (_currentCircularPage > 0) {
      // Move to last story of previous page
      setState(() {
        _currentCircularPage--;
        final prevPageStories = _currentPageStories;
        _selectedStoryIndex = prevPageStories.length - 1;
        _rotation = 0.0;
      });
      debugPrint(
        '🎯 Moved to page ${_currentCircularPage + 1} of $_totalCircularPages',
      );
    } else {
      // Wrap around to last page, last story
      setState(() {
        _currentCircularPage = _totalCircularPages - 1;
        final lastPageStories = _currentPageStories;
        _selectedStoryIndex = lastPageStories.length - 1;
        _rotation = 0.0;
      });
      debugPrint('🎯 Wrapped to last page');
    }
  }

  // Navigate to next page of stories
  void _nextPage() {
    if (!widget.useCircularLayout) return;
    if (_currentCircularPage < _totalCircularPages - 1) {
      setState(() {
        _currentCircularPage++;
        _selectedStoryIndex = 0;
        _rotation = 0.0;
      });
      debugPrint(
        '🎯 Next page: ${_currentCircularPage + 1} of $_totalCircularPages',
      );
    }
  }

  // Navigate to previous page of stories
  void _previousPage() {
    if (!widget.useCircularLayout) return;
    if (_currentCircularPage > 0) {
      setState(() {
        _currentCircularPage--;
        _selectedStoryIndex = 0;
        _rotation = 0.0;
      });
      debugPrint(
        '🎯 Previous page: ${_currentCircularPage + 1} of $_totalCircularPages',
      );
    }
  }

  void _navigateToStoryCreation() {
    debugPrint('🎬 Navigating to story creation...');
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const StoryCreationScreen(),
        fullscreenDialog: true,
      ),
    );
  }

  void _onStoryTap(StoryReel reel, int index) {
    debugPrint('🎬 Story tapped: ${reel.username}');
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => StoryViewerScreen(
          reel: reel,
          allReels: widget.storyReels,
          initialReelIndex: index,
        ),
      ),
    );
  }

  Color _getStoryBorderColor(StoryReel reel) {
    if (reel.isAllViewed) {
      return Colors.grey.withValues(alpha: 0.5);
    }

    if (reel.isCloseFriend) {
      return const Color(0xFF00D4AA); // Close friends green
    }

    // VIP/Premium users get gradient border effect
    if (reel.username.contains('vip') || reel.username.contains('premium')) {
      return const Color(0xFFFF6B6B); // VIP red
    }

    // Verified/billionaire users get golden border
    if (reel.username.contains('verified') ||
        reel.username.contains('billionaire')) {
      return const Color(0xFFD4AF37); // Golden border
    }

    // Default public story border
    return const Color(0xFF4ECDC4); // Teal for public stories
  }

  Widget _buildCreateStoryButton() {
    return GestureDetector(
      onTap: _navigateToStoryCreation,
      child: Container(
        width: 80, // Match story item width
        margin: const EdgeInsets.symmetric(horizontal: 4),
        child: Column(
          children: [
            // Create story circle
            Container(
              width: 70,
              height: 70,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: const Color(0xFFD4AF37), width: 3),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFFD4AF37).withValues(alpha: 0.3),
                    blurRadius: 8,
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: ClipOval(
                child: widget.centerAvatarUrl != null
                    ? Stack(
                        children: [
                          CachedNetworkImage(
                            imageUrl: widget.centerAvatarUrl!,
                            fit: BoxFit.cover,
                            width: 70,
                            height: 70,
                            placeholder: (context, url) => Container(
                              color: Colors.grey[300],
                              child: const Icon(
                                Icons.person,
                                color: Colors.white,
                              ),
                            ),
                            errorWidget: (context, url, error) => Container(
                              color: Colors.grey[300],
                              child: const Icon(
                                Icons.person,
                                color: Colors.white,
                              ),
                            ),
                          ),
                          // Add overlay
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.black.withValues(alpha: 0.3),
                              shape: BoxShape.circle,
                            ),
                            child: const Center(
                              child: Icon(
                                Icons.add,
                                color: Colors.white,
                                size: 24,
                              ),
                            ),
                          ),
                        ],
                      )
                    : Container(
                        decoration: const BoxDecoration(
                          gradient: LinearGradient(
                            colors: [Color(0xFFD4AF37), Color(0xFFFFD700)],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                        ),
                        child: const Icon(
                          Icons.add,
                          color: Colors.white,
                          size: 32,
                        ),
                      ),
              ),
            ),
            // "Your Story" text below the circle
            const SizedBox(height: 4),
            const SizedBox(
              width: 80,
              child: Text(
                'Your Story',
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStoryItem(StoryReel reel, int index) {
    return GestureDetector(
      onTap: () => _onStoryTap(reel, index),
      child: Container(
        width: 80, // Increased width to accommodate username
        margin: const EdgeInsets.symmetric(horizontal: 4),
        child: Column(
          children: [
            // Story circle
            Container(
              width: 70,
              height: 70,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: _getStoryBorderColor(reel), width: 3),
                boxShadow: [
                  BoxShadow(
                    color: _getStoryBorderColor(reel).withValues(alpha: 0.3),
                    blurRadius: 6,
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: ClipOval(
                child: Stack(
                  children: [
                    // Profile image
                    CachedNetworkImage(
                      imageUrl: reel.userAvatarUrl,
                      fit: BoxFit.cover,
                      width: 70,
                      height: 70,
                      placeholder: (context, url) => Container(
                        color: Colors.grey[300],
                        child: const Icon(Icons.person, color: Colors.white),
                      ),
                      errorWidget: (context, url, error) => Container(
                        color: Colors.grey[300],
                        child: const Icon(Icons.person, color: Colors.white),
                      ),
                    ),

                    // Billionaire badge for verified users
                    if (reel.username.contains('verified'))
                      const Positioned(
                        bottom: 0,
                        right: 0,
                        child: BillionaireBadge(size: 16),
                      ),
                  ],
                ),
              ),
            ),
            // Username below the circle
            const SizedBox(height: 4),
            SizedBox(
              width: 80,
              child: Text(
                reel.username,
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHorizontalCarousel() {
    return Container(
      height: 120, // Increased height to accommodate usernames
      padding: const EdgeInsets.symmetric(vertical: 10),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: widget.storyReels.length + 1, // +1 for create story button
        itemBuilder: (context, index) {
          if (index == 0) {
            return _buildCreateStoryButton();
          }

          final storyIndex = index - 1;
          final reel = widget.storyReels[storyIndex];
          return _buildStoryItem(reel, storyIndex);
        },
      ),
    );
  }

  Widget _buildCircularLayout() {
    debugPrint(
      '🎯 Building circular layout with ${widget.storyReels.length} stories',
    );

    return Container(
      height: 320, // Larger height for circular layout + controls
      padding: const EdgeInsets.symmetric(vertical: 20),
      child: Column(
        children: [
          // Navigation controls
          Column(
            children: [
              // Story navigation (left/right arrows)
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // Previous story button
                  GestureDetector(
                    onTap: _previousStory,
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: const Color(0xFFD4AF37).withValues(alpha: 0.2),
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: const Color(0xFFD4AF37),
                          width: 1,
                        ),
                      ),
                      child: const Icon(
                        Icons.chevron_left,
                        color: Color(0xFFD4AF37),
                        size: 20,
                      ),
                    ),
                  ),

                  // Story counter with page info
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.7),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      children: [
                        Text(
                          'Story ${_selectedStoryIndex + 1} of ${_currentPageStories.length}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        if (_totalCircularPages > 1)
                          Text(
                            'Page ${_currentCircularPage + 1} of $_totalCircularPages',
                            style: const TextStyle(
                              color: Colors.white70,
                              fontSize: 10,
                            ),
                          ),
                      ],
                    ),
                  ),

                  // Next story button
                  GestureDetector(
                    onTap: _nextStory,
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: const Color(0xFFD4AF37).withValues(alpha: 0.2),
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: const Color(0xFFD4AF37),
                          width: 1,
                        ),
                      ),
                      child: const Icon(
                        Icons.chevron_right,
                        color: Color(0xFFD4AF37),
                        size: 20,
                      ),
                    ),
                  ),
                ],
              ),

              // Page navigation (if more than 8 stories)
              if (_totalCircularPages > 1) ...[
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Previous page button
                    GestureDetector(
                      onTap: _previousPage,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: _currentCircularPage > 0
                              ? Colors.blue.withValues(alpha: 0.2)
                              : Colors.grey.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: _currentCircularPage > 0
                                ? Colors.blue
                                : Colors.grey,
                            width: 1,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.skip_previous,
                              color: _currentCircularPage > 0
                                  ? Colors.blue
                                  : Colors.grey,
                              size: 16,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'Prev Page',
                              style: TextStyle(
                                color: _currentCircularPage > 0
                                    ? Colors.blue
                                    : Colors.grey,
                                fontSize: 10,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 16),

                    // Total stories indicator
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.purple.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.purple, width: 1),
                      ),
                      child: Text(
                        '${widget.storyReels.length} Total Stories',
                        style: const TextStyle(
                          color: Colors.purple,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),

                    const SizedBox(width: 16),

                    // Next page button
                    GestureDetector(
                      onTap: _nextPage,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: _currentCircularPage < _totalCircularPages - 1
                              ? Colors.blue.withValues(alpha: 0.2)
                              : Colors.grey.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color:
                                _currentCircularPage < _totalCircularPages - 1
                                ? Colors.blue
                                : Colors.grey,
                            width: 1,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              'Next Page',
                              style: TextStyle(
                                color:
                                    _currentCircularPage <
                                        _totalCircularPages - 1
                                    ? Colors.blue
                                    : Colors.grey,
                                fontSize: 10,
                              ),
                            ),
                            const SizedBox(width: 4),
                            Icon(
                              Icons.skip_next,
                              color:
                                  _currentCircularPage < _totalCircularPages - 1
                                  ? Colors.blue
                                  : Colors.grey,
                              size: 16,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),

          const SizedBox(height: 16),

          // Circular story arrangement with gesture detection
          Expanded(
            child: GestureDetector(
              onPanStart: _onPanStart,
              onPanUpdate: _onPanUpdate,
              onPanEnd: _onPanEnd,
              child: AnimatedBuilder(
                animation: _rotationAnimation ?? _rotationController,
                builder: (context, child) {
                  final currentRotation = _isDragging
                      ? _rotation
                      : (_rotationAnimation?.isCompleted == true
                            ? _rotation
                            : _rotationAnimation?.value ?? _rotation);

                  return Stack(
                    alignment: Alignment.center,
                    children: [
                      // Background circle indicator
                      Container(
                        width: 200,
                        height: 200,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: const Color(
                              0xFFD4AF37,
                            ).withValues(alpha: 0.3),
                            width: 2,
                          ),
                        ),
                      ),

                      // Circular arrangement of stories (current page only)
                      ..._currentPageStories.asMap().entries.map((entry) {
                        final index = entry.key;
                        final reel = entry.value;
                        final baseAngle =
                            (index * 2 * math.pi) / _currentPageStories.length;
                        final angle = baseAngle + currentRotation;
                        final radius = 80.0;

                        final x = radius * math.cos(angle - math.pi / 2);
                        final y = radius * math.sin(angle - math.pi / 2);

                        return Transform.translate(
                          offset: Offset(x, y),
                          child: AnimatedScale(
                            scale: index == _selectedStoryIndex ? 1.2 : 1.0,
                            duration: const Duration(milliseconds: 200),
                            child: Container(
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                boxShadow: index == _selectedStoryIndex
                                    ? [
                                        BoxShadow(
                                          color: const Color(
                                            0xFFD4AF37,
                                          ).withValues(alpha: 0.5),
                                          blurRadius: 10,
                                          spreadRadius: 2,
                                        ),
                                      ]
                                    : null,
                              ),
                              child: _buildStoryItem(reel, index),
                            ),
                          ),
                        );
                      }),

                      // Center create story button
                      Positioned(
                        left: 0,
                        right: 0,
                        top: 0,
                        bottom: 0,
                        child: Center(child: _buildCreateStoryButton()),
                      ),
                    ],
                  );
                },
              ),
            ),
          ),

          // Instructions
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Text(
              'Drag to rotate • Tap arrows to navigate • Tap story to view',
              style: TextStyle(color: Colors.grey[600], fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    debugPrint(
      '🎬 HybridStoryCarousel: Building with ${widget.storyReels.length} stories',
    );
    debugPrint('🎬 Use circular layout: ${widget.useCircularLayout}');

    // Add visual indicator of current layout mode
    final layoutIndicator = Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: widget.useCircularLayout
            ? const Color(0xFFD4AF37).withValues(alpha: 0.2)
            : Colors.blue.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: widget.useCircularLayout
              ? const Color(0xFFD4AF37)
              : Colors.blue,
          width: 1,
        ),
      ),
      child: Text(
        widget.useCircularLayout
            ? '🎯 CIRCULAR MODE ACTIVE'
            : '📱 TRADITIONAL MODE ACTIVE',
        style: TextStyle(
          color: widget.useCircularLayout
              ? const Color(0xFFD4AF37)
              : Colors.blue,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );

    if (widget.storyReels.isEmpty) {
      // Show only create story button when no stories
      return Column(
        children: [
          layoutIndicator,
          Container(
            height: 100,
            padding: const EdgeInsets.symmetric(vertical: 15),
            child: Row(
              children: [
                const SizedBox(width: 16),
                _buildCreateStoryButton(),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Create your first story!',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    }

    return Column(
      children: [
        layoutIndicator,
        widget.useCircularLayout
            ? _buildCircularLayout()
            : _buildHorizontalCarousel(),
      ],
    );
  }
}
