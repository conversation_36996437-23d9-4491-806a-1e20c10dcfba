import 'package:billionaires_social/features/profile/providers/profile_provider.dart';
import 'package:billionaires_social/features/stories/models/story_reel_model.dart';
import 'package:billionaires_social/features/stories/providers/story_provider.dart';
import 'package:billionaires_social/features/stories/screens/story_creation_screen.dart';
import 'package:billionaires_social/features/stories/screens/story_viewer_screen.dart';
import 'package:billionaires_social/core/instagram_theme.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:firebase_auth/firebase_auth.dart';
import '../utils/story_debug_helper.dart';

class StoriesCarousel extends ConsumerWidget {
  const StoriesCarousel({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final storiesAsync = ref.watch(storyReelsProvider);
    final currentUser = FirebaseAuth.instance.currentUser;

    return storiesAsync.when(
      data: (reels) {
        // Always show the carousel, even if empty, to allow story creation
        debugPrint('🎬 StoriesCarousel: Found ${reels.length} story reels');

        // Debug: Check Firestore directly
        StoryDebugHelper.debugStoryCreation();

        // Debug provider state
        StoryDebugHelper.debugProviderState(reels);

        if (reels.isEmpty && currentUser == null) {
          return const SizedBox.shrink();
        }

        // Separate user's own story reel to show it first
        StoryReel? ownReel;
        List<StoryReel> otherReels = List.from(reels);

        if (currentUser != null) {
          // Always create an own reel entry for story creation
          ownReel = reels.firstWhere(
            (reel) => reel.userId == currentUser.uid,
            orElse: () => StoryReel(
              id: currentUser.uid,
              userId: currentUser.uid,
              username: 'Your Story',
              userAvatarUrl: '', // Will be populated with actual profile data
              stories: [],
              isAllViewed: true,
              isCloseFriend: false,
            ),
          );
          otherReels.removeWhere((reel) => reel.userId == currentUser.uid);

          debugPrint('🎬 Own reel: ${ownReel.stories.length} stories');
          debugPrint('🎬 Other reels: ${otherReels.length} users');
        }

        return Container(
          height: 104, // Increased height to prevent overflow
          padding: const EdgeInsets.symmetric(
            vertical: InstagramTheme.spacingS,
          ),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(
              horizontal: InstagramTheme.spacingL,
            ),
            itemCount: otherReels.length + (ownReel != null ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == 0 && ownReel != null) {
                final hasActiveStory = ownReel.stories.isNotEmpty;
                return _StoryAvatar(
                  reel: ownReel,
                  isOwnStory: true,
                  onTap: () {
                    if (hasActiveStory) {
                      ref
                          .read(storyReelsProvider.notifier)
                          .markReelAsViewed(ownReel!.id);
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => StoryViewerScreen(
                            reel: ownReel!,
                            isOwnStory: true,
                            allReels: reels,
                            initialReelIndex: 0,
                          ),
                        ),
                      );
                    } else {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const StoryCreationScreen(),
                          fullscreenDialog: true,
                        ),
                      );
                    }
                  },
                );
              }
              // Adjust index for other reels
              final reelIndex = (ownReel != null) ? index - 1 : index;
              final reel = otherReels[reelIndex];
              return _StoryAvatar(
                reel: reel,
                isOwnStory: false,
                onTap: () =>
                    _onStoryTap(context, ref, reel, false, reels, index),
              );
            },
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (err, stack) => Center(child: Text('Error: $err')),
    );
  }

  void _onStoryTap(
    BuildContext context,
    WidgetRef ref,
    StoryReel reel,
    bool isOwnStory,
    List<StoryReel> allReels,
    int reelIndex,
  ) {
    if (isOwnStory) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => const StoryCreationScreen(),
          fullscreenDialog: true,
        ),
      );
    } else {
      ref.read(storyReelsProvider.notifier).markReelAsViewed(reel.id);
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => StoryViewerScreen(
            reel: reel,
            isOwnStory: isOwnStory,
            allReels: allReels,
            initialReelIndex: reelIndex,
          ),
        ),
      );
    }
  }
}

class _StoryAvatar extends ConsumerWidget {
  final StoryReel reel;
  final bool isOwnStory;
  final VoidCallback onTap;

  const _StoryAvatar({
    required this.reel,
    required this.isOwnStory,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final currentUser = FirebaseAuth.instance.currentUser;
    final isOwnStory = currentUser != null && reel.userId == currentUser.uid;

    // For own story, fetch the actual profile data
    if (isOwnStory) {
      final currentUserProfile = ref.watch(profileProvider(reel.userId));

      return currentUserProfile.when(
        data: (profile) {
          final updatedReel = reel.copyWith(
            username: profile.name,
            userAvatarUrl: profile.profilePictureUrl.isNotEmpty
                ? profile.profilePictureUrl
                : 'https://i.pravatar.cc/150?u=${reel.userId}',
          );

          return _buildStoryAvatar(context, theme, updatedReel, ref);
        },
        loading: () => _buildStoryAvatar(context, theme, reel, ref),
        error: (_, _) => _buildStoryAvatar(context, theme, reel, ref),
      );
    }

    return _buildStoryAvatar(context, theme, reel, ref);
  }

  Widget _buildStoryAvatar(
    BuildContext context,
    ThemeData theme,
    StoryReel reel,
    WidgetRef ref,
  ) {
    final hasStories = reel.stories.isNotEmpty;
    final isCloseFriend = reel.isCloseFriend;
    final isAllViewed = reel.isAllViewed;
    final currentUser = FirebaseAuth.instance.currentUser;
    final isOwnStory = currentUser != null && reel.userId == currentUser.uid;

    // Debug logging for stories carousel
    debugPrint('🎬 StoriesCarousel for user ${reel.userId}:');
    debugPrint('  - hasStories: $hasStories');
    debugPrint('  - isCloseFriend: $isCloseFriend');
    debugPrint('  - isAllViewed: $isAllViewed');
    debugPrint(
      '  - Show gradient: ${hasStories && !isAllViewed && !isOwnStory}',
    );
    debugPrint('  - Gradient colors: ${isCloseFriend ? "Green" : "Red"}');

    return GestureDetector(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: InstagramTheme.spacingS,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Stack(
              alignment: Alignment.center,
              children: [
                // Story ring with Instagram-style gradient
                Container(
                  width: InstagramTheme.storyAvatarSize,
                  height: InstagramTheme.storyAvatarSize,
                  decoration: InstagramTheme.storyRingDecoration(
                    hasUnviewedStories:
                        hasStories && !isAllViewed && !isOwnStory,
                    isCloseFriends: isCloseFriend,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(
                      InstagramTheme.storyRingWidth,
                    ),
                    child: CircleAvatar(
                      radius:
                          (InstagramTheme.storyAvatarSize -
                              InstagramTheme.storyRingWidth * 2) /
                          2,
                      backgroundColor: InstagramTheme.white,
                      child: CircleAvatar(
                        radius:
                            (InstagramTheme.storyAvatarSize -
                                InstagramTheme.storyRingWidth * 2 -
                                4) /
                            2,
                        backgroundColor: InstagramTheme.backgroundGray,
                        backgroundImage: _isValidImageUrl(reel.userAvatarUrl)
                            ? CachedNetworkImageProvider(reel.userAvatarUrl)
                            : null,
                        child: !_isValidImageUrl(reel.userAvatarUrl)
                            ? Icon(
                                Icons.person,
                                color: InstagramTheme.textSecondary,
                                size: 24,
                              )
                            : null,
                      ),
                    ),
                  ),
                ),

                // Add story button for own story without active stories
                if (isOwnStory && !hasStories)
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        color: InstagramTheme.instagramBlue,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: InstagramTheme.white,
                          width: 2,
                        ),
                      ),
                      child: const Icon(
                        Icons.add,
                        size: 12,
                        color: InstagramTheme.white,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 2), // Reduced spacing to prevent overflow
            SizedBox(
              width: InstagramTheme.storyAvatarSize,
              height: 16, // Fixed height for text to prevent overflow
              child: Text(
                isOwnStory ? 'Your Story' : reel.username,
                style: InstagramTheme.timestampStyle.copyWith(
                  fontSize: 11, // Slightly smaller font
                  fontWeight: FontWeight.normal,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  bool _isValidImageUrl(String? url) {
    if (url == null || url.isEmpty) return false;
    return url.startsWith('http://') || url.startsWith('https://');
  }
}
