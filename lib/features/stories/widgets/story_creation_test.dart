import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/stories/screens/story_creation_screen.dart';

/// Simple test widget to verify story creation functionality
class StoryCreationTest extends ConsumerWidget {
  const StoryCreationTest({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Story Creation Test'),
        backgroundColor: const Color(0xFF1A1A2E),
        foregroundColor: Colors.white,
      ),
      backgroundColor: const Color(0xFF0F3460),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.add_a_photo, size: 80, color: Color(0xFFD4AF37)),

            const SizedBox(height: 24),

            const Text(
              'Test Story Creation',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: 16),

            const Text(
              'Tap the button below to test\nstory creation functionality',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.white70, fontSize: 16),
            ),

            const SizedBox(height: 40),

            ElevatedButton.icon(
              onPressed: () {
                debugPrint('🎬 Testing story creation navigation...');
                final scaffoldMessenger = ScaffoldMessenger.of(context);
                Navigator.of(context)
                    .push(
                      MaterialPageRoute(
                        builder: (context) => const StoryCreationScreen(),
                        fullscreenDialog: true,
                      ),
                    )
                    .then((result) {
                      debugPrint('🎬 Returned from story creation: $result');
                      if (result != null) {
                        scaffoldMessenger.showSnackBar(
                          SnackBar(
                            content: Text('Story creation result: $result'),
                            backgroundColor: const Color(0xFFD4AF37),
                          ),
                        );
                      }
                    });
              },
              icon: const Icon(Icons.add),
              label: const Text('Create Story'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFD4AF37),
                foregroundColor: Colors.black,
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
            ),

            const SizedBox(height: 24),

            OutlinedButton.icon(
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    backgroundColor: const Color(0xFF1A1A2E),
                    title: const Text(
                      'Story Creation Debug',
                      style: TextStyle(color: Color(0xFFD4AF37)),
                    ),
                    content: const Text(
                      'Story creation should:\n\n'
                      '1. Open camera/gallery picker\n'
                      '2. Allow photo/video selection\n'
                      '3. Provide editing tools\n'
                      '4. Save story to Firestore\n'
                      '5. Return to previous screen\n\n'
                      'Check console for debug logs.',
                      style: TextStyle(color: Colors.white70),
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text(
                          'OK',
                          style: TextStyle(color: Color(0xFFD4AF37)),
                        ),
                      ),
                    ],
                  ),
                );
              },
              icon: const Icon(Icons.info_outline),
              label: const Text('Debug Info'),
              style: OutlinedButton.styleFrom(
                foregroundColor: const Color(0xFFD4AF37),
                side: const BorderSide(color: Color(0xFFD4AF37)),
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
            ),

            const SizedBox(height: 40),

            Container(
              padding: const EdgeInsets.all(16),
              margin: const EdgeInsets.symmetric(horizontal: 32),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: const Color(0xFFD4AF37).withValues(alpha: 0.3),
                ),
              ),
              child: const Column(
                children: [
                  Text(
                    '🔧 Troubleshooting',
                    style: TextStyle(
                      color: Color(0xFFD4AF37),
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 12),
                  Text(
                    'If story creation doesn\'t work:\n\n'
                    '• Check camera permissions\n'
                    '• Verify StoryCreationScreen exists\n'
                    '• Check Firebase configuration\n'
                    '• Look for console error messages',
                    style: TextStyle(color: Colors.white70, fontSize: 14),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
