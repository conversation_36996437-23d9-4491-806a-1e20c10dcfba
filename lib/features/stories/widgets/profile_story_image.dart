import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:billionaires_social/features/stories/providers/story_provider.dart';
import 'package:billionaires_social/features/stories/screens/story_viewer_screen.dart';
import 'package:billionaires_social/features/stories/models/story_reel_model.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../screens/story_settings_screen.dart';

/// Profile-specific story image widget that shows settings for own stories
/// Used only in profile screens, not in feed
class ProfileStoryImage extends ConsumerWidget {
  final String userId;
  final String? profileImageUrl;
  final double size;
  final bool showStoryIndicator;
  final VoidCallback? onTap;
  final bool showBorder;
  final bool showSettings; // New parameter to control settings visibility

  const ProfileStoryImage({
    super.key,
    required this.userId,
    this.profileImageUrl,
    this.size = 40,
    this.showStoryIndicator = true,
    this.onTap,
    this.showBorder = true,
    this.showSettings = false, // Default false for safety
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final storyReelsAsync = ref.watch(storyReelsProvider);
    final currentUser = FirebaseAuth.instance.currentUser;
    final isOwnStory = currentUser != null && userId == currentUser.uid;

    return storyReelsAsync.when(
      data: (reels) {
        final userReel = reels.firstWhere(
          (reel) => reel.userId == userId,
          orElse: () => StoryReel(
            id: userId,
            userId: userId,
            username: 'Unknown',
            userAvatarUrl: profileImageUrl ?? '',
            stories: [],
            isAllViewed: true,
            isCloseFriend: false,
          ),
        );

        final hasStories = userReel.stories.isNotEmpty;
        final isCloseFriend = userReel.isCloseFriend;
        final isAllViewed = userReel.isAllViewed;

        return GestureDetector(
          onTap: () {
            if (hasStories) {
              // Navigate to story viewer
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => StoryViewerScreen(
                    reel: userReel,
                    isOwnStory: isOwnStory,
                    allReels: [userReel], // Single reel for profile view
                    initialReelIndex: 0,
                  ),
                ),
              );
            } else if (onTap != null) {
              // Fallback to custom onTap if no stories
              onTap!();
            }
          },
          child: Stack(
            children: [
              Container(
                padding: showBorder && showStoryIndicator && hasStories
                    ? const EdgeInsets.all(2.5)
                    : EdgeInsets.zero,
                decoration: showBorder && showStoryIndicator && hasStories
                    ? BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: _getStoryBorderColor(
                            isCloseFriend,
                            isAllViewed,
                          ),
                          width: 2.5,
                        ),
                      )
                    : null,
                child: CircleAvatar(
                  radius: size / 2,
                  backgroundColor: Colors.grey.shade200,
                  backgroundImage: _isValidImageUrl(profileImageUrl)
                      ? CachedNetworkImageProvider(profileImageUrl!)
                      : null,
                  child: !_isValidImageUrl(profileImageUrl)
                      ? Icon(
                          Icons.person,
                          size: size * 0.6,
                          color: Colors.grey.shade600,
                        )
                      : null,
                ),
              ),

              // Settings button for own stories (only when explicitly enabled)
              if (isOwnStory && hasStories && showSettings)
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: GestureDetector(
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (_) => const StorySettingsScreen(),
                        ),
                      );
                    },
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: Colors.black54,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.settings,
                        size: 16,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
      loading: () => CircleAvatar(
        radius: size / 2,
        backgroundColor: Theme.of(context).colorScheme.surface,
        child: SizedBox(
          width: size,
          height: size,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).colorScheme.primary,
            ),
          ),
        ),
      ),
      error: (error, stack) => CircleAvatar(
        radius: size / 2,
        backgroundColor: Colors.grey.shade200,
        backgroundImage: _isValidImageUrl(profileImageUrl)
            ? CachedNetworkImageProvider(profileImageUrl!)
            : null,
        child: !_isValidImageUrl(profileImageUrl)
            ? Icon(Icons.person, size: size * 0.6, color: Colors.grey.shade600)
            : null,
      ),
    );
  }

  Color _getStoryBorderColor(bool isCloseFriend, bool isAllViewed) {
    if (isAllViewed) {
      return Colors.grey.shade600;
    }

    if (isCloseFriend) {
      return Colors.green.shade600;
    }

    return Colors.red.shade600;
  }

  bool _isValidImageUrl(String? url) {
    if (url == null || url.isEmpty) return false;
    return url.startsWith('http://') || url.startsWith('https://');
  }
}
