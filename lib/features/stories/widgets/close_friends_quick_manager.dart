import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:billionaires_social/features/profile/providers/close_friends_groups_provider.dart';
import 'package:billionaires_social/features/profile/models/close_friends_group_model.dart';

class CloseFriendsQuickManager extends ConsumerStatefulWidget {
  final VoidCallback? onUpdated;

  const CloseFriendsQuickManager({super.key, this.onUpdated});

  @override
  ConsumerState<CloseFriendsQuickManager> createState() =>
      _CloseFriendsQuickManagerState();
}

class _CloseFriendsQuickManagerState
    extends ConsumerState<CloseFriendsQuickManager> {
  @override
  Widget build(BuildContext context) {
    final groupsAsync = ref.watch(closeFriendsGroupsNotifierProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Row(
          children: [
            const Icon(Icons.group, color: Color(0xFFD4AF37), size: 20),
            const SizedBox(width: 8),
            const Text(
              'Close Friends',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            TextButton(
              onPressed: _navigateToFullManager,
              child: const Text(
                'Manage All',
                style: TextStyle(
                  color: Color(0xFFD4AF37),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Groups content
        Expanded(
          child: groupsAsync.when(
            data: (groups) => _buildGroupsList(groups),
            loading: () => const Center(
              child: CircularProgressIndicator(color: Color(0xFFD4AF37)),
            ),
            error: (error, stack) => _buildErrorState(error.toString()),
          ),
        ),
      ],
    );
  }

  Widget _buildGroupsList(List<CloseFriendsGroup> groups) {
    if (groups.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: [
        // Quick stats
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              _buildStatItem('Groups', groups.length.toString(), Icons.group),
              const SizedBox(width: 24),
              _buildStatItem(
                'Total Friends',
                groups
                    .fold<int>(0, (sum, group) => sum + group.memberIds.length)
                    .toString(),
                Icons.people,
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),

        // Groups list
        Expanded(
          child: ListView.builder(
            itemCount: groups.length,
            itemBuilder: (context, index) {
              final group = groups[index];
              return _buildGroupTile(group);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: const Color(0xFFD4AF37), size: 16),
        const SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              value,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              label,
              style: const TextStyle(color: Colors.white70, fontSize: 12),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildGroupTile(CloseFriendsGroup group) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: const Color(0xFFD4AF37).withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Center(
            child: Text(group.emoji, style: const TextStyle(fontSize: 24)),
          ),
        ),
        title: Text(
          group.name,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              '${group.memberIds.length} ${group.memberIds.length == 1 ? 'member' : 'members'}',
              style: const TextStyle(color: Colors.white70, fontSize: 12),
            ),
            const SizedBox(height: 4),
            Text(
              'Created ${group.createdAt.day}/${group.createdAt.month}/${group.createdAt.year}',
              style: const TextStyle(color: Colors.white54, fontSize: 11),
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Member avatars preview
            if (group.memberIds.isNotEmpty)
              SizedBox(
                width: 60,
                height: 32,
                child: Stack(
                  children: group.memberIds
                      .take(3)
                      .toList()
                      .asMap()
                      .entries
                      .map((entry) {
                        final index = entry.key;
                        return Positioned(
                          left: index * 15.0,
                          child: Container(
                            width: 24,
                            height: 24,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(color: Colors.black, width: 1),
                            ),
                            child: const CircleAvatar(
                              radius: 12,
                              backgroundColor: Colors.grey,
                              child: Icon(
                                Icons.person,
                                size: 12,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        );
                      })
                      .toList(),
                ),
              ),
            const SizedBox(width: 8),
            const Icon(
              Icons.arrow_forward_ios,
              color: Colors.white54,
              size: 16,
            ),
          ],
        ),
        onTap: () => _editGroup(group),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.group_add, color: Colors.white54, size: 48),
          const SizedBox(height: 16),
          const Text(
            'No Close Friends Groups',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Create groups to share stories with specific friends',
            style: TextStyle(color: Colors.white70, fontSize: 14),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _navigateToFullManager,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFD4AF37),
              foregroundColor: Colors.black,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              'Create First Group',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 48),
          const SizedBox(height: 16),
          const Text(
            'Failed to load groups',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: const TextStyle(color: Colors.white70, fontSize: 14),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              ref.invalidate(closeFriendsGroupsNotifierProvider);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFD4AF37),
              foregroundColor: Colors.black,
            ),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  void _editGroup(CloseFriendsGroup group) {
    // TODO: Navigate to group edit screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Edit group: ${group.name}'),
        backgroundColor: const Color(0xFFD4AF37),
      ),
    );
  }

  void _navigateToFullManager() {
    // TODO: Navigate to full close friends management screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Close Friends Management coming soon!'),
        backgroundColor: Color(0xFFD4AF37),
      ),
    );
  }
}
