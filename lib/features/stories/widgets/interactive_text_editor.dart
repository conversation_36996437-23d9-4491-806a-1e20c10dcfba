import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Interactive text editor with Instagram-style gestures
class InteractiveTextEditor extends StatefulWidget {
  final String initialText;
  final Offset initialPosition;
  final double initialSize;
  final double initialRotation;
  final Color initialTextColor;
  final Color initialBackgroundColor;
  final String initialFontFamily;
  final TextAlign initialAlignment;
  final bool initialHasShadow;
  final bool initialHasBackground;
  final Function(TextElement)? onTextChanged;
  final VoidCallback? onDelete;
  final VoidCallback? onComplete;

  const InteractiveTextEditor({
    super.key,
    this.initialText = '',
    this.initialPosition = const Offset(100, 100),
    this.initialSize = 24.0,
    this.initialRotation = 0.0,
    this.initialTextColor = Colors.white,
    this.initialBackgroundColor = Colors.transparent,
    this.initialFontFamily = 'Classic',
    this.initialAlignment = TextAlign.center,
    this.initialHasShadow = true,
    this.initialHasBackground = false,
    this.onTextChanged,
    this.onDelete,
    this.onComplete,
  });

  @override
  State<InteractiveTextEditor> createState() => _InteractiveTextEditorState();
}

class _InteractiveTextEditorState extends State<InteractiveTextEditor> {
  late TextEditingController _textController;
  late FocusNode _focusNode;

  // Text properties
  late String _text;
  late Offset _position;
  late double _size;
  late double _rotation;
  late Color _textColor;
  late Color _backgroundColor;
  late String _fontFamily;
  late TextAlign _alignment;
  late bool _hasShadow;
  late bool _hasBackground;

  // Gesture state
  bool _isEditing = false;
  double _baseScaleFactor = 1.0;
  double _baseRotation = 0.0;

  // Available fonts
  final List<String> _availableFonts = [
    'Classic',
    'Modern',
    'Neon',
    'Typewriter',
    'Script',
    'Bold',
    'Italic',
  ];

  int _currentFontIndex = 0;

  @override
  void initState() {
    super.initState();
    _textController = TextEditingController(text: widget.initialText);
    _focusNode = FocusNode();

    // Initialize properties
    _text = widget.initialText;
    _position = widget.initialPosition;
    _size = widget.initialSize;
    _rotation = widget.initialRotation;
    _textColor = widget.initialTextColor;
    _backgroundColor = widget.initialBackgroundColor;
    _fontFamily = widget.initialFontFamily;
    _alignment = widget.initialAlignment;
    _hasShadow = widget.initialHasShadow;
    _hasBackground = widget.initialHasBackground;

    // Find current font index
    _currentFontIndex = _availableFonts.indexOf(_fontFamily);
    if (_currentFontIndex == -1) _currentFontIndex = 0;

    _focusNode.addListener(_onFocusChanged);

    // Auto-focus if no initial text
    if (_text.isEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _startEditing();
      });
    }
  }

  @override
  void dispose() {
    _textController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _onFocusChanged() {
    if (!_focusNode.hasFocus && _isEditing) {
      _finishEditing();
    }
  }

  void _startEditing() {
    setState(() {
      _isEditing = true;
    });
    _focusNode.requestFocus();
    HapticFeedback.lightImpact();
  }

  void _finishEditing() {
    setState(() {
      _isEditing = false;
      _text = _textController.text;
    });
    _notifyTextChanged();
    widget.onComplete?.call();
  }

  void _notifyTextChanged() {
    final textElement = TextElement(
      text: _text,
      position: _position,
      size: _size,
      rotation: _rotation,
      color: _textColor,
      backgroundColor: _hasBackground ? _backgroundColor : Colors.transparent,
      fontFamily: _fontFamily,
      alignment: _alignment,
      hasShadow: _hasShadow,
    );
    widget.onTextChanged?.call(textElement);
  }

  void _cycleFontStyle() {
    setState(() {
      _currentFontIndex = (_currentFontIndex + 1) % _availableFonts.length;
      _fontFamily = _availableFonts[_currentFontIndex];
    });
    _notifyTextChanged();
    HapticFeedback.selectionClick();
  }

  void _handlePanUpdate(DragUpdateDetails details) {
    setState(() {
      _position += details.delta;
    });
    _notifyTextChanged();
  }

  void _handleScaleStart(ScaleStartDetails details) {
    _baseScaleFactor = _size / 24.0; // Normalize to base size
    _baseRotation = _rotation;
    HapticFeedback.lightImpact();
  }

  void _handleScaleUpdate(ScaleUpdateDetails details) {
    setState(() {
      // Handle scaling (pinch to resize)
      _size = (_baseScaleFactor * details.scale * 24.0).clamp(12.0, 72.0);

      // Handle rotation
      _rotation = _baseRotation + details.rotation;
    });
    _notifyTextChanged();
  }

  @override
  Widget build(BuildContext context) {
    if (_text.isEmpty && !_isEditing) {
      return const SizedBox.shrink();
    }

    return Positioned(
      left: _position.dx,
      top: _position.dy,
      child: GestureDetector(
        onTap: _isEditing ? null : _startEditing,
        onPanUpdate: _isEditing ? null : _handlePanUpdate,
        onScaleStart: _isEditing ? null : _handleScaleStart,
        onScaleUpdate: _isEditing ? null : _handleScaleUpdate,
        child: Transform.rotate(angle: _rotation, child: _buildTextWidget()),
      ),
    );
  }

  Widget _buildTextWidget() {
    if (_isEditing) {
      return _buildEditingWidget();
    } else {
      return _buildDisplayWidget();
    }
  }

  Widget _buildEditingWidget() {
    return Container(
      constraints: BoxConstraints(
        maxWidth: MediaQuery.of(context).size.width * 0.8,
        minWidth: 100,
      ),
      child: TextField(
        controller: _textController,
        focusNode: _focusNode,
        style: _getTextStyle(),
        textAlign: _alignment,
        maxLines: null,
        decoration: InputDecoration(
          border: InputBorder.none,
          hintText: 'Type something...',
          hintStyle: _getTextStyle().copyWith(
            color: _textColor.withValues(alpha: 0.5),
          ),
        ),
        onChanged: (value) {
          setState(() {
            _text = value;
          });
        },
        onSubmitted: (_) => _finishEditing(),
      ),
    );
  }

  Widget _buildDisplayWidget() {
    return GestureDetector(
      onHorizontalDragEnd: (details) {
        // Swipe to change font
        if (details.primaryVelocity! > 500) {
          _cycleFontStyle();
        } else if (details.primaryVelocity! < -500) {
          _cycleFontStyle();
        }
      },
      child: Container(
        padding: _hasBackground
            ? const EdgeInsets.symmetric(horizontal: 12, vertical: 6)
            : EdgeInsets.zero,
        decoration: _hasBackground
            ? BoxDecoration(
                color: _backgroundColor,
                borderRadius: BorderRadius.circular(8),
              )
            : null,
        child: Text(_text, style: _getTextStyle(), textAlign: _alignment),
      ),
    );
  }

  TextStyle _getTextStyle() {
    return TextStyle(
      fontSize: _size,
      color: _textColor,
      fontFamily: _getFontFamily(),
      fontWeight: _getFontWeight(),
      fontStyle: _getFontStyle(),
      shadows: _hasShadow && !_hasBackground
          ? [
              Shadow(
                offset: const Offset(2, 2),
                blurRadius: 4,
                color: Colors.black.withValues(alpha: 0.8),
              ),
            ]
          : null,
    );
  }

  String? _getFontFamily() {
    switch (_fontFamily) {
      case 'Classic':
        return null; // Default system font
      case 'Modern':
        return 'Roboto';
      case 'Neon':
        return 'Orbitron';
      case 'Typewriter':
        return 'Courier';
      case 'Script':
        return 'Dancing Script';
      case 'Bold':
        return null;
      case 'Italic':
        return null;
      default:
        return null;
    }
  }

  FontWeight _getFontWeight() {
    switch (_fontFamily) {
      case 'Bold':
        return FontWeight.bold;
      case 'Neon':
        return FontWeight.w600;
      default:
        return FontWeight.normal;
    }
  }

  FontStyle _getFontStyle() {
    switch (_fontFamily) {
      case 'Italic':
        return FontStyle.italic;
      default:
        return FontStyle.normal;
    }
  }
}

/// Text element data model
class TextElement {
  final String text;
  final Offset position;
  final double size;
  final double rotation;
  final Color color;
  final Color backgroundColor;
  final String fontFamily;
  final TextAlign alignment;
  final bool hasShadow;

  const TextElement({
    required this.text,
    required this.position,
    required this.size,
    this.rotation = 0.0,
    required this.color,
    this.backgroundColor = Colors.transparent,
    this.fontFamily = 'Classic',
    this.alignment = TextAlign.center,
    this.hasShadow = true,
  });
}
