import 'package:flutter/material.dart';
import 'package:billionaires_social/core/app_theme.dart';

class StoryFiltersWidget extends StatefulWidget {
  final String selectedFilter;
  final Function(String) onFilterChanged;
  final List<String> selectedTags;
  final Function(List<String>) onTagsChanged;
  final String selectedView;
  final Function(String) onViewChanged;

  const StoryFiltersWidget({
    super.key,
    required this.selectedFilter,
    required this.onFilterChanged,
    required this.selectedTags,
    required this.onTagsChanged,
    required this.selectedView,
    required this.onViewChanged,
  });

  @override
  State<StoryFiltersWidget> createState() => _StoryFiltersWidgetState();
}

class _StoryFiltersWidgetState extends State<StoryFiltersWidget> {
  final List<String> _availableFilters = [
    'None',
    'Vintage',
    'Black & White',
    'Sepia',
    'Vivid',
    'Cool',
    'Warm',
    'Dramatic',
    'Soft',
    'Sharp',
    'Blur',
    'Grain',
    'Fade',
    'Contrast',
    'Saturation',
    'Brightness',
  ];

  final List<String> _availableTags = [
    '#Luxury',
    '#Billionaire',
    '#Exclusive',
    '#VIP',
    '#Lifestyle',
    '#Success',
    '#Wealth',
    '#Elite',
    '#Premium',
    '#HighEnd',
    '#LuxuryLife',
    '#BillionaireLifestyle',
    '#ExclusiveAccess',
    '#VIPExperience',
    '#LuxuryTravel',
    '#EliteNetwork',
  ];

  final List<String> _availableViews = [
    'Standard',
    'Wide',
    'Square',
    'Portrait',
    'Cinematic',
    'Story',
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.luxuryBlack,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.accentColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('Filters'),
          const SizedBox(height: 12),
          _buildFilterGrid(),
          const SizedBox(height: 24),
          _buildSectionTitle('Tags'),
          const SizedBox(height: 12),
          _buildTagChips(),
          const SizedBox(height: 24),
          _buildSectionTitle('View'),
          const SizedBox(height: 12),
          _buildViewSelector(),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: AppTheme.fontStyles.subtitle.copyWith(
        color: AppTheme.accentColor,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildFilterGrid() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 1.2,
      ),
      itemCount: _availableFilters.length,
      itemBuilder: (context, index) {
        final filter = _availableFilters[index];
        final isSelected = widget.selectedFilter == filter;

        return GestureDetector(
          onTap: () => widget.onFilterChanged(filter),
          child: Container(
            decoration: BoxDecoration(
              color: isSelected ? AppTheme.accentColor : AppTheme.luxuryGrey,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isSelected
                    ? AppTheme.accentColor
                    : AppTheme.secondaryAccentColor.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Center(
              child: Text(
                filter,
                style: AppTheme.fontStyles.caption.copyWith(
                  color: isSelected
                      ? AppTheme.luxuryBlack
                      : AppTheme.secondaryAccentColor,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTagChips() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: _availableTags.map((tag) {
        final isSelected = widget.selectedTags.contains(tag);

        return GestureDetector(
          onTap: () {
            final newTags = List<String>.from(widget.selectedTags);
            if (isSelected) {
              newTags.remove(tag);
            } else {
              if (newTags.length < 5) {
                // Limit to 5 tags
                newTags.add(tag);
              }
            }
            widget.onTagsChanged(newTags);
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: isSelected ? AppTheme.accentColor : AppTheme.luxuryGrey,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: isSelected
                    ? AppTheme.accentColor
                    : AppTheme.secondaryAccentColor.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (isSelected)
                  Icon(Icons.check, size: 14, color: AppTheme.luxuryBlack),
                const SizedBox(width: 4),
                Text(
                  tag,
                  style: AppTheme.fontStyles.caption.copyWith(
                    color: isSelected
                        ? AppTheme.luxuryBlack
                        : AppTheme.secondaryAccentColor,
                    fontWeight: isSelected
                        ? FontWeight.bold
                        : FontWeight.normal,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildViewSelector() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: _availableViews.map((view) {
          final isSelected = widget.selectedView == view;

          return GestureDetector(
            onTap: () => widget.onViewChanged(view),
            child: Container(
              margin: const EdgeInsets.only(right: 12),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: isSelected ? AppTheme.accentColor : AppTheme.luxuryGrey,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: isSelected
                      ? AppTheme.accentColor
                      : AppTheme.secondaryAccentColor.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    _getViewIcon(view),
                    size: 16,
                    color: isSelected
                        ? AppTheme.luxuryBlack
                        : AppTheme.secondaryAccentColor,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    view,
                    style: AppTheme.fontStyles.body.copyWith(
                      color: isSelected
                          ? AppTheme.luxuryBlack
                          : AppTheme.secondaryAccentColor,
                      fontWeight: isSelected
                          ? FontWeight.bold
                          : FontWeight.normal,
                    ),
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  IconData _getViewIcon(String view) {
    switch (view) {
      case 'Standard':
        return Icons.crop_free;
      case 'Wide':
        return Icons.crop_landscape;
      case 'Square':
        return Icons.crop_square;
      case 'Portrait':
        return Icons.crop_portrait;
      case 'Cinematic':
        return Icons.movie;
      case 'Story':
        return Icons.phone_android;
      default:
        return Icons.crop_free;
    }
  }
}
