import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/material.dart';
import 'dart:io';
import '../models/unified_story_model.dart';
import '../models/shared/story_shared_models.dart';
import '../services/unified_story_service.dart';

/// Provider for the unified story service
final unifiedStoryServiceProvider = Provider<UnifiedStoryService>((ref) {
  return UnifiedStoryService();
});

/// Provider for story reels (feed)
final storyReelsProvider = FutureProvider<List<StoryReel>>((ref) async {
  final service = ref.read(unifiedStoryServiceProvider);
  return await service.fetchStoryReels();
});

/// Provider for user's own stories
final userStoriesProvider = FutureProvider.family<List<UnifiedStory>, String>((
  ref,
  userId,
) async {
  final service = ref.read(unifiedStoryServiceProvider);
  return await service.getUserStories(userId);
});

/// Provider for story analytics
final storyAnalyticsProvider = FutureProvider.family<StoryStats?, String>((
  ref,
  storyId,
) async {
  final service = ref.read(unifiedStoryServiceProvider);
  return await service.getStoryAnalytics(storyId);
});

/// Provider for story creation state
final storyCreationProvider =
    StateNotifierProvider<StoryCreationNotifier, StoryCreationState>((ref) {
      return StoryCreationNotifier(ref.read(unifiedStoryServiceProvider), ref);
    });

/// Provider for story viewer state
final storyViewerProvider =
    StateNotifierProvider.family<StoryViewerNotifier, StoryViewerState, String>(
      (ref, storyId) {
        return StoryViewerNotifier(
          ref.read(unifiedStoryServiceProvider),
          storyId,
        );
      },
    );

/// Provider for story search
final storySearchProvider = FutureProvider.family<List<UnifiedStory>, String>((
  ref,
  hashtag,
) async {
  final service = ref.read(unifiedStoryServiceProvider);
  return await service.searchStoriesByHashtag(hashtag);
});

/// Story creation state
class StoryCreationState {
  final bool isLoading;
  final String? error;
  final UnifiedStory? createdStory;
  final File? selectedMedia;
  final StoryMediaType? mediaType;
  final String? caption;
  final String? textOverlay;
  final Color? textColor;
  final double? textSize;
  final Offset? textPosition;
  final Color? backgroundColor;
  final String? filter;
  final List<DrawingPoint> drawingPoints;
  final Color? drawingColor;
  final double? drawingWidth;
  final List<TextElement> textElements;
  final List<StoryTag> tags;
  final List<String> hashtags;
  final List<String> mentions;
  final Map<String, dynamic>? music;
  final String? musicArtist;
  final Map<String, dynamic>? location;
  final String? locationName;
  final StoryPrivacy privacy;
  final StoryVisibility visibility;
  final List<String> allowedTo;
  final List<String> hiddenFromUserIds;
  final List<String> allowedGroupIds;
  final StoryDuration duration;
  final bool isHighlighted;
  final bool isCloseFriend;

  StoryCreationState({
    this.isLoading = false,
    this.error,
    this.createdStory,
    this.selectedMedia,
    this.mediaType,
    this.caption,
    this.textOverlay,
    this.textColor,
    this.textSize,
    this.textPosition,
    this.backgroundColor,
    this.filter,
    this.drawingPoints = const [],
    this.drawingColor,
    this.drawingWidth,
    this.textElements = const [],
    this.tags = const [],
    this.hashtags = const [],
    this.mentions = const [],
    this.music,
    this.musicArtist,
    this.location,
    this.locationName,
    this.privacy = StoryPrivacy.public,
    this.visibility = StoryVisibility.public,
    this.allowedTo = const [],
    this.hiddenFromUserIds = const [],
    this.allowedGroupIds = const [],
    this.duration = StoryDuration.twentyFourHours,
    this.isHighlighted = false,
    this.isCloseFriend = false,
  });

  StoryCreationState copyWith({
    bool? isLoading,
    String? error,
    UnifiedStory? createdStory,
    File? selectedMedia,
    StoryMediaType? mediaType,
    String? caption,
    String? textOverlay,
    Color? textColor,
    double? textSize,
    Offset? textPosition,
    Color? backgroundColor,
    String? filter,
    List<DrawingPoint>? drawingPoints,
    Color? drawingColor,
    double? drawingWidth,
    List<TextElement>? textElements,
    List<StoryTag>? tags,
    List<String>? hashtags,
    List<String>? mentions,
    Map<String, dynamic>? music,
    String? musicArtist,
    Map<String, dynamic>? location,
    String? locationName,
    StoryPrivacy? privacy,
    StoryVisibility? visibility,
    List<String>? allowedTo,
    List<String>? hiddenFromUserIds,
    List<String>? allowedGroupIds,
    StoryDuration? duration,
    bool? isHighlighted,
    bool? isCloseFriend,
  }) {
    return StoryCreationState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      createdStory: createdStory ?? this.createdStory,
      selectedMedia: selectedMedia ?? this.selectedMedia,
      mediaType: mediaType ?? this.mediaType,
      caption: caption ?? this.caption,
      textOverlay: textOverlay ?? this.textOverlay,
      textColor: textColor ?? this.textColor,
      textSize: textSize ?? this.textSize,
      textPosition: textPosition ?? this.textPosition,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      filter: filter ?? this.filter,
      drawingPoints: drawingPoints ?? this.drawingPoints,
      drawingColor: drawingColor ?? this.drawingColor,
      drawingWidth: drawingWidth ?? this.drawingWidth,
      textElements: textElements ?? this.textElements,
      tags: tags ?? this.tags,
      hashtags: hashtags ?? this.hashtags,
      mentions: mentions ?? this.mentions,
      music: music ?? this.music,
      musicArtist: musicArtist ?? this.musicArtist,
      location: location ?? this.location,
      locationName: locationName ?? this.locationName,
      privacy: privacy ?? this.privacy,
      visibility: visibility ?? this.visibility,
      allowedTo: allowedTo ?? this.allowedTo,
      hiddenFromUserIds: hiddenFromUserIds ?? this.hiddenFromUserIds,
      allowedGroupIds: allowedGroupIds ?? this.allowedGroupIds,
      duration: duration ?? this.duration,
      isHighlighted: isHighlighted ?? this.isHighlighted,
      isCloseFriend: isCloseFriend ?? this.isCloseFriend,
    );
  }
}

/// Story creation notifier
class StoryCreationNotifier extends StateNotifier<StoryCreationState> {
  final UnifiedStoryService _service;
  final Ref _ref;

  StoryCreationNotifier(this._service, this._ref) : super(StoryCreationState());

  /// Set selected media
  void setMedia(File file, StoryMediaType type) {
    state = state.copyWith(selectedMedia: file, mediaType: type);
  }

  /// Set caption
  void setCaption(String caption) {
    state = state.copyWith(caption: caption);
  }

  /// Set text overlay
  void setTextOverlay(String textOverlay) {
    state = state.copyWith(textOverlay: textOverlay);
  }

  /// Set text color
  void setTextColor(Color color) {
    state = state.copyWith(textColor: color);
  }

  /// Set text size
  void setTextSize(double size) {
    state = state.copyWith(textSize: size);
  }

  /// Set text position
  void setTextPosition(Offset position) {
    state = state.copyWith(textPosition: position);
  }

  /// Set background color
  void setBackgroundColor(Color color) {
    state = state.copyWith(backgroundColor: color);
  }

  /// Set filter
  void setFilter(String filter) {
    state = state.copyWith(filter: filter);
  }

  /// Add drawing point
  void addDrawingPoint(DrawingPoint point) {
    final newPoints = List<DrawingPoint>.from(state.drawingPoints)..add(point);
    state = state.copyWith(drawingPoints: newPoints);
  }

  /// Clear drawing points
  void clearDrawingPoints() {
    state = state.copyWith(drawingPoints: []);
  }

  /// Set drawing color
  void setDrawingColor(Color color) {
    state = state.copyWith(drawingColor: color);
  }

  /// Set drawing width
  void setDrawingWidth(double width) {
    state = state.copyWith(drawingWidth: width);
  }

  /// Add text element
  void addTextElement(TextElement element) {
    final newElements = List<TextElement>.from(state.textElements)
      ..add(element);
    state = state.copyWith(textElements: newElements);
  }

  /// Remove text element
  void removeTextElement(int index) {
    final newElements = List<TextElement>.from(state.textElements)
      ..removeAt(index);
    state = state.copyWith(textElements: newElements);
  }

  /// Update text element
  void updateTextElement(int index, TextElement element) {
    final newElements = List<TextElement>.from(state.textElements);
    newElements[index] = element;
    state = state.copyWith(textElements: newElements);
  }

  /// Set all text elements (for Instagram-style text editor)
  void setTextElements(List<TextElement> elements) {
    state = state.copyWith(textElements: elements);
  }

  /// Add tag
  void addTag(StoryTag tag) {
    final newTags = List<StoryTag>.from(state.tags)..add(tag);
    state = state.copyWith(tags: newTags);
  }

  /// Remove tag
  void removeTag(int index) {
    final newTags = List<StoryTag>.from(state.tags)..removeAt(index);
    state = state.copyWith(tags: newTags);
  }

  /// Add hashtag
  void addHashtag(String hashtag) {
    if (!state.hashtags.contains(hashtag)) {
      final newHashtags = List<String>.from(state.hashtags)..add(hashtag);
      state = state.copyWith(hashtags: newHashtags);
    }
  }

  /// Remove hashtag
  void removeHashtag(String hashtag) {
    final newHashtags = List<String>.from(state.hashtags)..remove(hashtag);
    state = state.copyWith(hashtags: newHashtags);
  }

  /// Add mention
  void addMention(String mention) {
    if (!state.mentions.contains(mention)) {
      final newMentions = List<String>.from(state.mentions)..add(mention);
      state = state.copyWith(mentions: newMentions);
    }
  }

  /// Remove mention
  void removeMention(String mention) {
    final newMentions = List<String>.from(state.mentions)..remove(mention);
    state = state.copyWith(mentions: newMentions);
  }

  /// Set music
  void setMusic(Map<String, dynamic> music, String artist) {
    state = state.copyWith(music: music, musicArtist: artist);
  }

  /// Set location
  void setLocation(Map<String, dynamic> location, String name) {
    state = state.copyWith(location: location, locationName: name);
  }

  /// Set privacy
  void setPrivacy(StoryPrivacy privacy) {
    state = state.copyWith(privacy: privacy);
  }

  /// Set visibility
  void setVisibility(StoryVisibility visibility) {
    state = state.copyWith(visibility: visibility);
  }

  /// Set duration
  void setDuration(StoryDuration duration) {
    state = state.copyWith(duration: duration);
  }

  /// Set close friend status
  void setCloseFriend(bool isCloseFriend) {
    state = state.copyWith(isCloseFriend: isCloseFriend);
  }

  /// Create story
  Future<bool> createStory() async {
    if (state.selectedMedia == null || state.mediaType == null) {
      state = state.copyWith(error: 'No media selected');
      return false;
    }

    state = state.copyWith(isLoading: true, error: null);

    // Collect mentions from text elements
    final allMentions = _collectMentionsFromTextElements();

    try {
      final story = await _service.createStory(
        mediaFile: state.selectedMedia!,
        mediaType: state.mediaType!,
        caption: state.caption,
        textOverlay: state.textOverlay,
        textColor: state.textColor,
        textSize: state.textSize,
        textPosition: state.textPosition,
        backgroundColor: state.backgroundColor,
        filter: state.filter,
        drawingPoints: state.drawingPoints,
        drawingColor: state.drawingColor,
        drawingWidth: state.drawingWidth,
        textElements: state.textElements,
        tags: state.tags,
        hashtags: state.hashtags,
        mentions: [...state.mentions, ...allMentions],
        music: state.music,
        musicArtist: state.musicArtist,
        location: state.location,
        locationName: state.locationName,
        privacy: state.privacy,
        visibility: state.visibility,
        allowedTo: state.allowedTo,
        hiddenFromUserIds: state.hiddenFromUserIds,
        allowedGroupIds: state.allowedGroupIds,
        duration: state.duration,
        isHighlighted: state.isHighlighted,
        isCloseFriend: state.isCloseFriend,
      );

      if (story != null) {
        state = state.copyWith(isLoading: false, createdStory: story);

        // Refresh the story reels provider to show the new story
        try {
          _ref.invalidate(storyReelsProvider);
          debugPrint('✅ Story reels provider refreshed after story creation');
        } catch (e) {
          debugPrint('⚠️ Failed to refresh story reels provider: $e');
        }

        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: 'Failed to create story',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  /// Reset state
  void reset() {
    state = StoryCreationState();
  }

  /// Collect mentions from all text elements
  List<String> _collectMentionsFromTextElements() {
    final allMentions = <String>[];

    for (final textElement in state.textElements) {
      // Add mentions from the text element's mentions list
      allMentions.addAll(textElement.mentions);

      // Also extract mentions from the text content itself
      final textMentions = _extractMentionsFromText(textElement.text);
      allMentions.addAll(textMentions);
    }

    // Remove duplicates and return
    return allMentions.toSet().toList();
  }

  /// Extract mentions from text using regex
  List<String> _extractMentionsFromText(String text) {
    final mentionRegex = RegExp(r'@(\w+)');
    return mentionRegex
        .allMatches(text)
        .map((match) => match.group(1)!)
        .toList();
  }
}

/// Story viewer state
class StoryViewerState {
  final bool isLoading;
  final String? error;
  final UnifiedStory? story;
  final bool isViewed;
  final bool isLiked;
  final List<StoryReply> replies;
  final List<StoryReaction> reactions;
  final bool showReplies;
  final bool showReactions;

  StoryViewerState({
    this.isLoading = false,
    this.error,
    this.story,
    this.isViewed = false,
    this.isLiked = false,
    this.replies = const [],
    this.reactions = const [],
    this.showReplies = false,
    this.showReactions = false,
  });

  StoryViewerState copyWith({
    bool? isLoading,
    String? error,
    UnifiedStory? story,
    bool? isViewed,
    bool? isLiked,
    List<StoryReply>? replies,
    List<StoryReaction>? reactions,
    bool? showReplies,
    bool? showReactions,
  }) {
    return StoryViewerState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      story: story ?? this.story,
      isViewed: isViewed ?? this.isViewed,
      isLiked: isLiked ?? this.isLiked,
      replies: replies ?? this.replies,
      reactions: reactions ?? this.reactions,
      showReplies: showReplies ?? this.showReplies,
      showReactions: showReactions ?? this.showReactions,
    );
  }
}

/// Story viewer notifier
class StoryViewerNotifier extends StateNotifier<StoryViewerState> {
  final UnifiedStoryService _service;
  final String _storyId;

  StoryViewerNotifier(this._service, this._storyId)
    : super(StoryViewerState()) {
    _loadStory();
  }

  /// Load story data
  Future<void> _loadStory() async {
    state = state.copyWith(isLoading: true);

    try {
      // Mark as viewed
      await _service.markStoryAsViewed(_storyId);
      state = state.copyWith(isViewed: true);

      // TODO: Load story details, replies, and reactions
      // This would require additional methods in the service

      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// Add reply
  Future<void> addReply(String message) async {
    try {
      await _service.addStoryReply(_storyId, message);
      // TODO: Refresh replies
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Add reaction
  Future<void> addReaction(String reactionType) async {
    try {
      await _service.addStoryReaction(_storyId, reactionType);
      state = state.copyWith(isLiked: true);
      // TODO: Refresh reactions
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Toggle replies visibility
  void toggleReplies() {
    state = state.copyWith(showReplies: !state.showReplies);
  }

  /// Toggle reactions visibility
  void toggleReactions() {
    state = state.copyWith(showReactions: !state.showReactions);
  }
}

/// Provider for refreshing story reels
final storyReelsRefreshProvider = Provider<Future<void> Function()>((ref) {
  return () async {
    ref.invalidate(storyReelsProvider);
  };
});

/// Provider for refreshing user stories
final userStoriesRefreshProvider = Provider<Future<void> Function(String)>((
  ref,
) {
  return (userId) async {
    ref.invalidate(userStoriesProvider(userId));
  };
});
