import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/features/stories/models/story_settings_model.dart';
import 'package:billionaires_social/features/stories/services/story_settings_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'story_settings_provider.g.dart';

@riverpod
class StorySettingsNotifier extends _$StorySettingsNotifier {
  late final StorySettingsService _service;

  @override
  Future<StorySettings> build() async {
    _service = getIt<StorySettingsService>();
    return _service.getSettings();
  }

  Future<void> updateSettings(StorySettings newSettings) async {
    state = AsyncValue.data(newSettings);
    await _service.updateSettings(newSettings);
  }
}
