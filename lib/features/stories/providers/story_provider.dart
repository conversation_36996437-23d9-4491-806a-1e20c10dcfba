import 'package:billionaires_social/features/stories/models/story_reel_model.dart';
import 'package:billionaires_social/features/stories/services/enhanced_story_service.dart';
import 'package:billionaires_social/features/feed/providers/feed_filter_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';

part 'story_provider.g.dart';

@Riverpod(keepAlive: true)
class StoryReels extends _$StoryReels {
  @override
  Future<List<StoryReel>> build() async {
    final storyService = ref.watch(enhancedStoryServiceProvider);
    final currentFilter = ref.watch(feedFilterProvider);

    // Get filtered user IDs based on current filter (now defaults to followed users)
    final feedFilter = ref.read(feedFilterProvider.notifier);
    final filteredUserIds = await feedFilter.getFilteredUserIds();

    debugPrint('🎬 Stories building with filter: $currentFilter');
    debugPrint(
      '🎬 Filtered user IDs for stories: ${filteredUserIds.length} users',
    );

    final reels = await storyService.getStoryReels(
      filteredUserIds: filteredUserIds.isNotEmpty ? filteredUserIds : null,
    );

    debugPrint('✅ Fetched ${reels.length} story reels for current filter');
    return reels.cast<StoryReel>();
  }

  Future<void> refresh() async {
    ref.invalidateSelf();
  }

  Future<void> markReelAsViewed(String reelId) async {
    try {
      debugPrint('👁️ Marking reel as viewed: $reelId');
      final storyService = ref.read(enhancedStoryServiceProvider);
      await storyService.markStoryAsViewed(reelId);

      // Refresh the stories after marking as viewed
      ref.invalidateSelf();
      debugPrint('✅ Reel marked as viewed and stories refreshed');
    } catch (e) {
      debugPrint('❌ Error marking reel as viewed: $e');
      // Don't throw the exception - let the UI handle it gracefully
      // The service now handles errors internally and won't crash the app
    }
  }

  Future<void> addStoryToReel(String storyId) async {
    final storyService = ref.read(enhancedStoryServiceProvider);
    final newStory = await storyService.getStoryById(storyId);
    if (newStory == null) return;

    final reels = state.valueOrNull ?? [];
    final reelIndex = reels.indexWhere((r) => r.userId == newStory.userId);

    if (reelIndex != -1) {
      // Add to existing reel
      final updatedReel = reels[reelIndex].copyWith(
        stories: [...reels[reelIndex].stories, newStory],
        isAllViewed: false, // New story is unviewed
      );
      state = AsyncData([...reels]..[reelIndex] = updatedReel);
    } else {
      // Create a new reel for the user
      final user = await storyService.getUserDetails(newStory.userId);
      final newReel = StoryReel(
        id: newStory.userId,
        userId: newStory.userId,
        username: user['name'] ?? 'Unknown',
        userAvatarUrl:
            user['profilePictureUrl'] ??
            'https://i.pravatar.cc/150?u=${newStory.userId}',
        stories: [newStory],
        isAllViewed: false,
        isCloseFriend: false, // Assuming public story for now
      );
      state = AsyncData([...reels, newReel]);
    }
  }

  Future<void> refreshStories() async {
    debugPrint('🔄 Refreshing stories...');
    ref.invalidateSelf();
  }

  // New methods for story features
  Future<void> trackStoryView(String storyId, String viewerId) async {
    try {
      final storyService = ref.read(enhancedStoryServiceProvider);
      await storyService.trackStoryView(storyId, viewerId);
      debugPrint('✅ Story view tracked: $storyId by $viewerId');
    } catch (e) {
      debugPrint('❌ Error tracking story view: $e');
    }
  }

  Future<Map<String, dynamic>> getStoryAnalytics(String storyId) async {
    try {
      final storyService = ref.read(enhancedStoryServiceProvider);
      return await storyService.getStoryAnalytics(storyId);
    } catch (e) {
      debugPrint('❌ Error getting story analytics: $e');
      return {};
    }
  }

  Future<void> deleteExpiredStories() async {
    try {
      final storyService = ref.read(enhancedStoryServiceProvider);
      await storyService.deleteExpiredStories();
      debugPrint('✅ Expired stories deleted');

      // Refresh stories after cleanup
      ref.invalidateSelf();
    } catch (e) {
      debugPrint('❌ Error deleting expired stories: $e');
    }
  }

  Future<void> previewMusic(String musicPath) async {
    try {
      final storyService = ref.read(enhancedStoryServiceProvider);
      await storyService.previewMusic(musicPath);
    } catch (e) {
      debugPrint('❌ Error previewing music: $e');
    }
  }

  Future<void> stopMusicPreview() async {
    try {
      final storyService = ref.read(enhancedStoryServiceProvider);
      await storyService.stopMusicPreview();
    } catch (e) {
      debugPrint('❌ Error stopping music preview: $e');
    }
  }

  Future<List<LocationResult>> searchLocations(String query) async {
    try {
      final storyService = ref.read(enhancedStoryServiceProvider);
      final results = await storyService.searchLocations(query);
      return results.cast<LocationResult>();
    } catch (e) {
      debugPrint('❌ Error searching locations: $e');
      return [];
    }
  }

  Future<LocationResult?> getCurrentLocation() async {
    try {
      final storyService = ref.read(enhancedStoryServiceProvider);
      return await storyService.getCurrentLocation();
    } catch (e) {
      debugPrint('❌ Error getting current location: $e');
      return null;
    }
  }

  bool isStoryExpired(DateTime createdAt) {
    final storyService = ref.read(enhancedStoryServiceProvider);
    return storyService.isStoryExpired(createdAt);
  }

  Duration getTimeRemaining(DateTime createdAt) {
    final storyService = ref.read(enhancedStoryServiceProvider);
    return storyService.getTimeRemaining(createdAt);
  }
}

// Provider for story analytics
@riverpod
Future<Map<String, dynamic>> storyAnalytics(Ref ref, String storyId) async {
  final storyService = ref.read(enhancedStoryServiceProvider);
  return storyService.getStoryAnalytics(storyId);
}

// Provider for story expiration status
@riverpod
bool storyExpired(Ref ref, DateTime createdAt) {
  final storyService = ref.read(enhancedStoryServiceProvider);
  return storyService.isStoryExpired(createdAt);
}

// Provider for story time remaining
@riverpod
Duration storyTimeRemaining(Ref ref, DateTime createdAt) {
  final storyService = ref.read(enhancedStoryServiceProvider);
  return storyService.getTimeRemaining(createdAt);
}
