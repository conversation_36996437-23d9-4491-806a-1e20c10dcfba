// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'story_settings_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$storySettingsNotifierHash() =>
    r'73cfb0d699339d90e63d8470b23338c410d1dc7a';

/// See also [StorySettingsNotifier].
@ProviderFor(StorySettingsNotifier)
final storySettingsNotifierProvider =
    AutoDisposeAsyncNotifierProvider<
      StorySettingsNotifier,
      StorySettings
    >.internal(
      StorySettingsNotifier.new,
      name: r'storySettingsNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$storySettingsNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$StorySettingsNotifier = AutoDisposeAsyncNotifier<StorySettings>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
