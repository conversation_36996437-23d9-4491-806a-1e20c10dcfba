// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'story_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$storyAnalyticsHash() => r'45f8a778338e9945536d6831019a18fa513e9a76';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [storyAnalytics].
@ProviderFor(storyAnalytics)
const storyAnalyticsProvider = StoryAnalyticsFamily();

/// See also [storyAnalytics].
class StoryAnalyticsFamily extends Family<AsyncValue<Map<String, dynamic>>> {
  /// See also [storyAnalytics].
  const StoryAnalyticsFamily();

  /// See also [storyAnalytics].
  StoryAnalyticsProvider call(String storyId) {
    return StoryAnalyticsProvider(storyId);
  }

  @override
  StoryAnalyticsProvider getProviderOverride(
    covariant StoryAnalyticsProvider provider,
  ) {
    return call(provider.storyId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'storyAnalyticsProvider';
}

/// See also [storyAnalytics].
class StoryAnalyticsProvider
    extends AutoDisposeFutureProvider<Map<String, dynamic>> {
  /// See also [storyAnalytics].
  StoryAnalyticsProvider(String storyId)
    : this._internal(
        (ref) => storyAnalytics(ref as StoryAnalyticsRef, storyId),
        from: storyAnalyticsProvider,
        name: r'storyAnalyticsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$storyAnalyticsHash,
        dependencies: StoryAnalyticsFamily._dependencies,
        allTransitiveDependencies:
            StoryAnalyticsFamily._allTransitiveDependencies,
        storyId: storyId,
      );

  StoryAnalyticsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.storyId,
  }) : super.internal();

  final String storyId;

  @override
  Override overrideWith(
    FutureOr<Map<String, dynamic>> Function(StoryAnalyticsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: StoryAnalyticsProvider._internal(
        (ref) => create(ref as StoryAnalyticsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        storyId: storyId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Map<String, dynamic>> createElement() {
    return _StoryAnalyticsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is StoryAnalyticsProvider && other.storyId == storyId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, storyId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin StoryAnalyticsRef on AutoDisposeFutureProviderRef<Map<String, dynamic>> {
  /// The parameter `storyId` of this provider.
  String get storyId;
}

class _StoryAnalyticsProviderElement
    extends AutoDisposeFutureProviderElement<Map<String, dynamic>>
    with StoryAnalyticsRef {
  _StoryAnalyticsProviderElement(super.provider);

  @override
  String get storyId => (origin as StoryAnalyticsProvider).storyId;
}

String _$storyExpiredHash() => r'27893e843fa6724d4b764ab741884c84d89f4d3e';

/// See also [storyExpired].
@ProviderFor(storyExpired)
const storyExpiredProvider = StoryExpiredFamily();

/// See also [storyExpired].
class StoryExpiredFamily extends Family<bool> {
  /// See also [storyExpired].
  const StoryExpiredFamily();

  /// See also [storyExpired].
  StoryExpiredProvider call(DateTime createdAt) {
    return StoryExpiredProvider(createdAt);
  }

  @override
  StoryExpiredProvider getProviderOverride(
    covariant StoryExpiredProvider provider,
  ) {
    return call(provider.createdAt);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'storyExpiredProvider';
}

/// See also [storyExpired].
class StoryExpiredProvider extends AutoDisposeProvider<bool> {
  /// See also [storyExpired].
  StoryExpiredProvider(DateTime createdAt)
    : this._internal(
        (ref) => storyExpired(ref as StoryExpiredRef, createdAt),
        from: storyExpiredProvider,
        name: r'storyExpiredProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$storyExpiredHash,
        dependencies: StoryExpiredFamily._dependencies,
        allTransitiveDependencies:
            StoryExpiredFamily._allTransitiveDependencies,
        createdAt: createdAt,
      );

  StoryExpiredProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.createdAt,
  }) : super.internal();

  final DateTime createdAt;

  @override
  Override overrideWith(bool Function(StoryExpiredRef provider) create) {
    return ProviderOverride(
      origin: this,
      override: StoryExpiredProvider._internal(
        (ref) => create(ref as StoryExpiredRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        createdAt: createdAt,
      ),
    );
  }

  @override
  AutoDisposeProviderElement<bool> createElement() {
    return _StoryExpiredProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is StoryExpiredProvider && other.createdAt == createdAt;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, createdAt.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin StoryExpiredRef on AutoDisposeProviderRef<bool> {
  /// The parameter `createdAt` of this provider.
  DateTime get createdAt;
}

class _StoryExpiredProviderElement extends AutoDisposeProviderElement<bool>
    with StoryExpiredRef {
  _StoryExpiredProviderElement(super.provider);

  @override
  DateTime get createdAt => (origin as StoryExpiredProvider).createdAt;
}

String _$storyTimeRemainingHash() =>
    r'f19e7a1c9ebf1d456ad9c8cfb9309b20c1fc23cc';

/// See also [storyTimeRemaining].
@ProviderFor(storyTimeRemaining)
const storyTimeRemainingProvider = StoryTimeRemainingFamily();

/// See also [storyTimeRemaining].
class StoryTimeRemainingFamily extends Family<Duration> {
  /// See also [storyTimeRemaining].
  const StoryTimeRemainingFamily();

  /// See also [storyTimeRemaining].
  StoryTimeRemainingProvider call(DateTime createdAt) {
    return StoryTimeRemainingProvider(createdAt);
  }

  @override
  StoryTimeRemainingProvider getProviderOverride(
    covariant StoryTimeRemainingProvider provider,
  ) {
    return call(provider.createdAt);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'storyTimeRemainingProvider';
}

/// See also [storyTimeRemaining].
class StoryTimeRemainingProvider extends AutoDisposeProvider<Duration> {
  /// See also [storyTimeRemaining].
  StoryTimeRemainingProvider(DateTime createdAt)
    : this._internal(
        (ref) => storyTimeRemaining(ref as StoryTimeRemainingRef, createdAt),
        from: storyTimeRemainingProvider,
        name: r'storyTimeRemainingProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$storyTimeRemainingHash,
        dependencies: StoryTimeRemainingFamily._dependencies,
        allTransitiveDependencies:
            StoryTimeRemainingFamily._allTransitiveDependencies,
        createdAt: createdAt,
      );

  StoryTimeRemainingProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.createdAt,
  }) : super.internal();

  final DateTime createdAt;

  @override
  Override overrideWith(
    Duration Function(StoryTimeRemainingRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: StoryTimeRemainingProvider._internal(
        (ref) => create(ref as StoryTimeRemainingRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        createdAt: createdAt,
      ),
    );
  }

  @override
  AutoDisposeProviderElement<Duration> createElement() {
    return _StoryTimeRemainingProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is StoryTimeRemainingProvider && other.createdAt == createdAt;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, createdAt.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin StoryTimeRemainingRef on AutoDisposeProviderRef<Duration> {
  /// The parameter `createdAt` of this provider.
  DateTime get createdAt;
}

class _StoryTimeRemainingProviderElement
    extends AutoDisposeProviderElement<Duration>
    with StoryTimeRemainingRef {
  _StoryTimeRemainingProviderElement(super.provider);

  @override
  DateTime get createdAt => (origin as StoryTimeRemainingProvider).createdAt;
}

String _$storyReelsHash() => r'b248fbade85282c4fa73fcb0176fed4c5770c77e';

/// See also [StoryReels].
@ProviderFor(StoryReels)
final storyReelsProvider =
    AsyncNotifierProvider<StoryReels, List<StoryReel>>.internal(
      StoryReels.new,
      name: r'storyReelsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$storyReelsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$StoryReels = AsyncNotifier<List<StoryReel>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
