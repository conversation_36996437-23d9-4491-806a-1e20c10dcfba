// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'payment_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PaymentMethod {

 String get id; String get userId; PaymentMethodType get type; String get lastFourDigits; String get cardBrand; DateTime get expiryDate; bool get isDefault; bool get isEnabled; String? get cardholderName; String? get billingAddress; String? get paymentMethodId;// External payment provider ID
 DateTime get createdAt; DateTime? get lastUsedAt; Map<String, dynamic>? get metadata;
/// Create a copy of PaymentMethod
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PaymentMethodCopyWith<PaymentMethod> get copyWith => _$PaymentMethodCopyWithImpl<PaymentMethod>(this as PaymentMethod, _$identity);

  /// Serializes this PaymentMethod to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PaymentMethod&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.type, type) || other.type == type)&&(identical(other.lastFourDigits, lastFourDigits) || other.lastFourDigits == lastFourDigits)&&(identical(other.cardBrand, cardBrand) || other.cardBrand == cardBrand)&&(identical(other.expiryDate, expiryDate) || other.expiryDate == expiryDate)&&(identical(other.isDefault, isDefault) || other.isDefault == isDefault)&&(identical(other.isEnabled, isEnabled) || other.isEnabled == isEnabled)&&(identical(other.cardholderName, cardholderName) || other.cardholderName == cardholderName)&&(identical(other.billingAddress, billingAddress) || other.billingAddress == billingAddress)&&(identical(other.paymentMethodId, paymentMethodId) || other.paymentMethodId == paymentMethodId)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.lastUsedAt, lastUsedAt) || other.lastUsedAt == lastUsedAt)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,type,lastFourDigits,cardBrand,expiryDate,isDefault,isEnabled,cardholderName,billingAddress,paymentMethodId,createdAt,lastUsedAt,const DeepCollectionEquality().hash(metadata));

@override
String toString() {
  return 'PaymentMethod(id: $id, userId: $userId, type: $type, lastFourDigits: $lastFourDigits, cardBrand: $cardBrand, expiryDate: $expiryDate, isDefault: $isDefault, isEnabled: $isEnabled, cardholderName: $cardholderName, billingAddress: $billingAddress, paymentMethodId: $paymentMethodId, createdAt: $createdAt, lastUsedAt: $lastUsedAt, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $PaymentMethodCopyWith<$Res>  {
  factory $PaymentMethodCopyWith(PaymentMethod value, $Res Function(PaymentMethod) _then) = _$PaymentMethodCopyWithImpl;
@useResult
$Res call({
 String id, String userId, PaymentMethodType type, String lastFourDigits, String cardBrand, DateTime expiryDate, bool isDefault, bool isEnabled, String? cardholderName, String? billingAddress, String? paymentMethodId, DateTime createdAt, DateTime? lastUsedAt, Map<String, dynamic>? metadata
});




}
/// @nodoc
class _$PaymentMethodCopyWithImpl<$Res>
    implements $PaymentMethodCopyWith<$Res> {
  _$PaymentMethodCopyWithImpl(this._self, this._then);

  final PaymentMethod _self;
  final $Res Function(PaymentMethod) _then;

/// Create a copy of PaymentMethod
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? type = null,Object? lastFourDigits = null,Object? cardBrand = null,Object? expiryDate = null,Object? isDefault = null,Object? isEnabled = null,Object? cardholderName = freezed,Object? billingAddress = freezed,Object? paymentMethodId = freezed,Object? createdAt = null,Object? lastUsedAt = freezed,Object? metadata = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as PaymentMethodType,lastFourDigits: null == lastFourDigits ? _self.lastFourDigits : lastFourDigits // ignore: cast_nullable_to_non_nullable
as String,cardBrand: null == cardBrand ? _self.cardBrand : cardBrand // ignore: cast_nullable_to_non_nullable
as String,expiryDate: null == expiryDate ? _self.expiryDate : expiryDate // ignore: cast_nullable_to_non_nullable
as DateTime,isDefault: null == isDefault ? _self.isDefault : isDefault // ignore: cast_nullable_to_non_nullable
as bool,isEnabled: null == isEnabled ? _self.isEnabled : isEnabled // ignore: cast_nullable_to_non_nullable
as bool,cardholderName: freezed == cardholderName ? _self.cardholderName : cardholderName // ignore: cast_nullable_to_non_nullable
as String?,billingAddress: freezed == billingAddress ? _self.billingAddress : billingAddress // ignore: cast_nullable_to_non_nullable
as String?,paymentMethodId: freezed == paymentMethodId ? _self.paymentMethodId : paymentMethodId // ignore: cast_nullable_to_non_nullable
as String?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,lastUsedAt: freezed == lastUsedAt ? _self.lastUsedAt : lastUsedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [PaymentMethod].
extension PaymentMethodPatterns on PaymentMethod {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PaymentMethod value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PaymentMethod() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PaymentMethod value)  $default,){
final _that = this;
switch (_that) {
case _PaymentMethod():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PaymentMethod value)?  $default,){
final _that = this;
switch (_that) {
case _PaymentMethod() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  PaymentMethodType type,  String lastFourDigits,  String cardBrand,  DateTime expiryDate,  bool isDefault,  bool isEnabled,  String? cardholderName,  String? billingAddress,  String? paymentMethodId,  DateTime createdAt,  DateTime? lastUsedAt,  Map<String, dynamic>? metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PaymentMethod() when $default != null:
return $default(_that.id,_that.userId,_that.type,_that.lastFourDigits,_that.cardBrand,_that.expiryDate,_that.isDefault,_that.isEnabled,_that.cardholderName,_that.billingAddress,_that.paymentMethodId,_that.createdAt,_that.lastUsedAt,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  PaymentMethodType type,  String lastFourDigits,  String cardBrand,  DateTime expiryDate,  bool isDefault,  bool isEnabled,  String? cardholderName,  String? billingAddress,  String? paymentMethodId,  DateTime createdAt,  DateTime? lastUsedAt,  Map<String, dynamic>? metadata)  $default,) {final _that = this;
switch (_that) {
case _PaymentMethod():
return $default(_that.id,_that.userId,_that.type,_that.lastFourDigits,_that.cardBrand,_that.expiryDate,_that.isDefault,_that.isEnabled,_that.cardholderName,_that.billingAddress,_that.paymentMethodId,_that.createdAt,_that.lastUsedAt,_that.metadata);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  PaymentMethodType type,  String lastFourDigits,  String cardBrand,  DateTime expiryDate,  bool isDefault,  bool isEnabled,  String? cardholderName,  String? billingAddress,  String? paymentMethodId,  DateTime createdAt,  DateTime? lastUsedAt,  Map<String, dynamic>? metadata)?  $default,) {final _that = this;
switch (_that) {
case _PaymentMethod() when $default != null:
return $default(_that.id,_that.userId,_that.type,_that.lastFourDigits,_that.cardBrand,_that.expiryDate,_that.isDefault,_that.isEnabled,_that.cardholderName,_that.billingAddress,_that.paymentMethodId,_that.createdAt,_that.lastUsedAt,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PaymentMethod implements PaymentMethod {
  const _PaymentMethod({required this.id, required this.userId, required this.type, required this.lastFourDigits, required this.cardBrand, required this.expiryDate, required this.isDefault, required this.isEnabled, this.cardholderName, this.billingAddress, this.paymentMethodId, required this.createdAt, this.lastUsedAt, final  Map<String, dynamic>? metadata}): _metadata = metadata;
  factory _PaymentMethod.fromJson(Map<String, dynamic> json) => _$PaymentMethodFromJson(json);

@override final  String id;
@override final  String userId;
@override final  PaymentMethodType type;
@override final  String lastFourDigits;
@override final  String cardBrand;
@override final  DateTime expiryDate;
@override final  bool isDefault;
@override final  bool isEnabled;
@override final  String? cardholderName;
@override final  String? billingAddress;
@override final  String? paymentMethodId;
// External payment provider ID
@override final  DateTime createdAt;
@override final  DateTime? lastUsedAt;
 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of PaymentMethod
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PaymentMethodCopyWith<_PaymentMethod> get copyWith => __$PaymentMethodCopyWithImpl<_PaymentMethod>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PaymentMethodToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PaymentMethod&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.type, type) || other.type == type)&&(identical(other.lastFourDigits, lastFourDigits) || other.lastFourDigits == lastFourDigits)&&(identical(other.cardBrand, cardBrand) || other.cardBrand == cardBrand)&&(identical(other.expiryDate, expiryDate) || other.expiryDate == expiryDate)&&(identical(other.isDefault, isDefault) || other.isDefault == isDefault)&&(identical(other.isEnabled, isEnabled) || other.isEnabled == isEnabled)&&(identical(other.cardholderName, cardholderName) || other.cardholderName == cardholderName)&&(identical(other.billingAddress, billingAddress) || other.billingAddress == billingAddress)&&(identical(other.paymentMethodId, paymentMethodId) || other.paymentMethodId == paymentMethodId)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.lastUsedAt, lastUsedAt) || other.lastUsedAt == lastUsedAt)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,type,lastFourDigits,cardBrand,expiryDate,isDefault,isEnabled,cardholderName,billingAddress,paymentMethodId,createdAt,lastUsedAt,const DeepCollectionEquality().hash(_metadata));

@override
String toString() {
  return 'PaymentMethod(id: $id, userId: $userId, type: $type, lastFourDigits: $lastFourDigits, cardBrand: $cardBrand, expiryDate: $expiryDate, isDefault: $isDefault, isEnabled: $isEnabled, cardholderName: $cardholderName, billingAddress: $billingAddress, paymentMethodId: $paymentMethodId, createdAt: $createdAt, lastUsedAt: $lastUsedAt, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$PaymentMethodCopyWith<$Res> implements $PaymentMethodCopyWith<$Res> {
  factory _$PaymentMethodCopyWith(_PaymentMethod value, $Res Function(_PaymentMethod) _then) = __$PaymentMethodCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, PaymentMethodType type, String lastFourDigits, String cardBrand, DateTime expiryDate, bool isDefault, bool isEnabled, String? cardholderName, String? billingAddress, String? paymentMethodId, DateTime createdAt, DateTime? lastUsedAt, Map<String, dynamic>? metadata
});




}
/// @nodoc
class __$PaymentMethodCopyWithImpl<$Res>
    implements _$PaymentMethodCopyWith<$Res> {
  __$PaymentMethodCopyWithImpl(this._self, this._then);

  final _PaymentMethod _self;
  final $Res Function(_PaymentMethod) _then;

/// Create a copy of PaymentMethod
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? type = null,Object? lastFourDigits = null,Object? cardBrand = null,Object? expiryDate = null,Object? isDefault = null,Object? isEnabled = null,Object? cardholderName = freezed,Object? billingAddress = freezed,Object? paymentMethodId = freezed,Object? createdAt = null,Object? lastUsedAt = freezed,Object? metadata = freezed,}) {
  return _then(_PaymentMethod(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as PaymentMethodType,lastFourDigits: null == lastFourDigits ? _self.lastFourDigits : lastFourDigits // ignore: cast_nullable_to_non_nullable
as String,cardBrand: null == cardBrand ? _self.cardBrand : cardBrand // ignore: cast_nullable_to_non_nullable
as String,expiryDate: null == expiryDate ? _self.expiryDate : expiryDate // ignore: cast_nullable_to_non_nullable
as DateTime,isDefault: null == isDefault ? _self.isDefault : isDefault // ignore: cast_nullable_to_non_nullable
as bool,isEnabled: null == isEnabled ? _self.isEnabled : isEnabled // ignore: cast_nullable_to_non_nullable
as bool,cardholderName: freezed == cardholderName ? _self.cardholderName : cardholderName // ignore: cast_nullable_to_non_nullable
as String?,billingAddress: freezed == billingAddress ? _self.billingAddress : billingAddress // ignore: cast_nullable_to_non_nullable
as String?,paymentMethodId: freezed == paymentMethodId ? _self.paymentMethodId : paymentMethodId // ignore: cast_nullable_to_non_nullable
as String?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,lastUsedAt: freezed == lastUsedAt ? _self.lastUsedAt : lastUsedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}


/// @nodoc
mixin _$PaymentTransaction {

 String get id; String get userId; String get merchantId; double get amount; String get currency; PaymentStatus get status; PaymentType get type; DateTime get createdAt; DateTime? get processedAt; DateTime? get failedAt; String? get description; String? get receiptUrl; String? get transactionId;// External payment provider transaction ID
 String? get failureReason; Map<String, dynamic>? get metadata; List<String>? get tags; String? get invoiceId; String? get subscriptionId;
/// Create a copy of PaymentTransaction
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PaymentTransactionCopyWith<PaymentTransaction> get copyWith => _$PaymentTransactionCopyWithImpl<PaymentTransaction>(this as PaymentTransaction, _$identity);

  /// Serializes this PaymentTransaction to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PaymentTransaction&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.merchantId, merchantId) || other.merchantId == merchantId)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.status, status) || other.status == status)&&(identical(other.type, type) || other.type == type)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.processedAt, processedAt) || other.processedAt == processedAt)&&(identical(other.failedAt, failedAt) || other.failedAt == failedAt)&&(identical(other.description, description) || other.description == description)&&(identical(other.receiptUrl, receiptUrl) || other.receiptUrl == receiptUrl)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.failureReason, failureReason) || other.failureReason == failureReason)&&const DeepCollectionEquality().equals(other.metadata, metadata)&&const DeepCollectionEquality().equals(other.tags, tags)&&(identical(other.invoiceId, invoiceId) || other.invoiceId == invoiceId)&&(identical(other.subscriptionId, subscriptionId) || other.subscriptionId == subscriptionId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,merchantId,amount,currency,status,type,createdAt,processedAt,failedAt,description,receiptUrl,transactionId,failureReason,const DeepCollectionEquality().hash(metadata),const DeepCollectionEquality().hash(tags),invoiceId,subscriptionId);

@override
String toString() {
  return 'PaymentTransaction(id: $id, userId: $userId, merchantId: $merchantId, amount: $amount, currency: $currency, status: $status, type: $type, createdAt: $createdAt, processedAt: $processedAt, failedAt: $failedAt, description: $description, receiptUrl: $receiptUrl, transactionId: $transactionId, failureReason: $failureReason, metadata: $metadata, tags: $tags, invoiceId: $invoiceId, subscriptionId: $subscriptionId)';
}


}

/// @nodoc
abstract mixin class $PaymentTransactionCopyWith<$Res>  {
  factory $PaymentTransactionCopyWith(PaymentTransaction value, $Res Function(PaymentTransaction) _then) = _$PaymentTransactionCopyWithImpl;
@useResult
$Res call({
 String id, String userId, String merchantId, double amount, String currency, PaymentStatus status, PaymentType type, DateTime createdAt, DateTime? processedAt, DateTime? failedAt, String? description, String? receiptUrl, String? transactionId, String? failureReason, Map<String, dynamic>? metadata, List<String>? tags, String? invoiceId, String? subscriptionId
});




}
/// @nodoc
class _$PaymentTransactionCopyWithImpl<$Res>
    implements $PaymentTransactionCopyWith<$Res> {
  _$PaymentTransactionCopyWithImpl(this._self, this._then);

  final PaymentTransaction _self;
  final $Res Function(PaymentTransaction) _then;

/// Create a copy of PaymentTransaction
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? merchantId = null,Object? amount = null,Object? currency = null,Object? status = null,Object? type = null,Object? createdAt = null,Object? processedAt = freezed,Object? failedAt = freezed,Object? description = freezed,Object? receiptUrl = freezed,Object? transactionId = freezed,Object? failureReason = freezed,Object? metadata = freezed,Object? tags = freezed,Object? invoiceId = freezed,Object? subscriptionId = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,merchantId: null == merchantId ? _self.merchantId : merchantId // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as PaymentStatus,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as PaymentType,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,processedAt: freezed == processedAt ? _self.processedAt : processedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,failedAt: freezed == failedAt ? _self.failedAt : failedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,receiptUrl: freezed == receiptUrl ? _self.receiptUrl : receiptUrl // ignore: cast_nullable_to_non_nullable
as String?,transactionId: freezed == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String?,failureReason: freezed == failureReason ? _self.failureReason : failureReason // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,tags: freezed == tags ? _self.tags : tags // ignore: cast_nullable_to_non_nullable
as List<String>?,invoiceId: freezed == invoiceId ? _self.invoiceId : invoiceId // ignore: cast_nullable_to_non_nullable
as String?,subscriptionId: freezed == subscriptionId ? _self.subscriptionId : subscriptionId // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [PaymentTransaction].
extension PaymentTransactionPatterns on PaymentTransaction {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PaymentTransaction value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PaymentTransaction() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PaymentTransaction value)  $default,){
final _that = this;
switch (_that) {
case _PaymentTransaction():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PaymentTransaction value)?  $default,){
final _that = this;
switch (_that) {
case _PaymentTransaction() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  String merchantId,  double amount,  String currency,  PaymentStatus status,  PaymentType type,  DateTime createdAt,  DateTime? processedAt,  DateTime? failedAt,  String? description,  String? receiptUrl,  String? transactionId,  String? failureReason,  Map<String, dynamic>? metadata,  List<String>? tags,  String? invoiceId,  String? subscriptionId)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PaymentTransaction() when $default != null:
return $default(_that.id,_that.userId,_that.merchantId,_that.amount,_that.currency,_that.status,_that.type,_that.createdAt,_that.processedAt,_that.failedAt,_that.description,_that.receiptUrl,_that.transactionId,_that.failureReason,_that.metadata,_that.tags,_that.invoiceId,_that.subscriptionId);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  String merchantId,  double amount,  String currency,  PaymentStatus status,  PaymentType type,  DateTime createdAt,  DateTime? processedAt,  DateTime? failedAt,  String? description,  String? receiptUrl,  String? transactionId,  String? failureReason,  Map<String, dynamic>? metadata,  List<String>? tags,  String? invoiceId,  String? subscriptionId)  $default,) {final _that = this;
switch (_that) {
case _PaymentTransaction():
return $default(_that.id,_that.userId,_that.merchantId,_that.amount,_that.currency,_that.status,_that.type,_that.createdAt,_that.processedAt,_that.failedAt,_that.description,_that.receiptUrl,_that.transactionId,_that.failureReason,_that.metadata,_that.tags,_that.invoiceId,_that.subscriptionId);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  String merchantId,  double amount,  String currency,  PaymentStatus status,  PaymentType type,  DateTime createdAt,  DateTime? processedAt,  DateTime? failedAt,  String? description,  String? receiptUrl,  String? transactionId,  String? failureReason,  Map<String, dynamic>? metadata,  List<String>? tags,  String? invoiceId,  String? subscriptionId)?  $default,) {final _that = this;
switch (_that) {
case _PaymentTransaction() when $default != null:
return $default(_that.id,_that.userId,_that.merchantId,_that.amount,_that.currency,_that.status,_that.type,_that.createdAt,_that.processedAt,_that.failedAt,_that.description,_that.receiptUrl,_that.transactionId,_that.failureReason,_that.metadata,_that.tags,_that.invoiceId,_that.subscriptionId);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PaymentTransaction implements PaymentTransaction {
  const _PaymentTransaction({required this.id, required this.userId, required this.merchantId, required this.amount, required this.currency, required this.status, required this.type, required this.createdAt, this.processedAt, this.failedAt, this.description, this.receiptUrl, this.transactionId, this.failureReason, final  Map<String, dynamic>? metadata, final  List<String>? tags, this.invoiceId, this.subscriptionId}): _metadata = metadata,_tags = tags;
  factory _PaymentTransaction.fromJson(Map<String, dynamic> json) => _$PaymentTransactionFromJson(json);

@override final  String id;
@override final  String userId;
@override final  String merchantId;
@override final  double amount;
@override final  String currency;
@override final  PaymentStatus status;
@override final  PaymentType type;
@override final  DateTime createdAt;
@override final  DateTime? processedAt;
@override final  DateTime? failedAt;
@override final  String? description;
@override final  String? receiptUrl;
@override final  String? transactionId;
// External payment provider transaction ID
@override final  String? failureReason;
 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

 final  List<String>? _tags;
@override List<String>? get tags {
  final value = _tags;
  if (value == null) return null;
  if (_tags is EqualUnmodifiableListView) return _tags;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override final  String? invoiceId;
@override final  String? subscriptionId;

/// Create a copy of PaymentTransaction
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PaymentTransactionCopyWith<_PaymentTransaction> get copyWith => __$PaymentTransactionCopyWithImpl<_PaymentTransaction>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PaymentTransactionToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PaymentTransaction&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.merchantId, merchantId) || other.merchantId == merchantId)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.status, status) || other.status == status)&&(identical(other.type, type) || other.type == type)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.processedAt, processedAt) || other.processedAt == processedAt)&&(identical(other.failedAt, failedAt) || other.failedAt == failedAt)&&(identical(other.description, description) || other.description == description)&&(identical(other.receiptUrl, receiptUrl) || other.receiptUrl == receiptUrl)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.failureReason, failureReason) || other.failureReason == failureReason)&&const DeepCollectionEquality().equals(other._metadata, _metadata)&&const DeepCollectionEquality().equals(other._tags, _tags)&&(identical(other.invoiceId, invoiceId) || other.invoiceId == invoiceId)&&(identical(other.subscriptionId, subscriptionId) || other.subscriptionId == subscriptionId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,merchantId,amount,currency,status,type,createdAt,processedAt,failedAt,description,receiptUrl,transactionId,failureReason,const DeepCollectionEquality().hash(_metadata),const DeepCollectionEquality().hash(_tags),invoiceId,subscriptionId);

@override
String toString() {
  return 'PaymentTransaction(id: $id, userId: $userId, merchantId: $merchantId, amount: $amount, currency: $currency, status: $status, type: $type, createdAt: $createdAt, processedAt: $processedAt, failedAt: $failedAt, description: $description, receiptUrl: $receiptUrl, transactionId: $transactionId, failureReason: $failureReason, metadata: $metadata, tags: $tags, invoiceId: $invoiceId, subscriptionId: $subscriptionId)';
}


}

/// @nodoc
abstract mixin class _$PaymentTransactionCopyWith<$Res> implements $PaymentTransactionCopyWith<$Res> {
  factory _$PaymentTransactionCopyWith(_PaymentTransaction value, $Res Function(_PaymentTransaction) _then) = __$PaymentTransactionCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, String merchantId, double amount, String currency, PaymentStatus status, PaymentType type, DateTime createdAt, DateTime? processedAt, DateTime? failedAt, String? description, String? receiptUrl, String? transactionId, String? failureReason, Map<String, dynamic>? metadata, List<String>? tags, String? invoiceId, String? subscriptionId
});




}
/// @nodoc
class __$PaymentTransactionCopyWithImpl<$Res>
    implements _$PaymentTransactionCopyWith<$Res> {
  __$PaymentTransactionCopyWithImpl(this._self, this._then);

  final _PaymentTransaction _self;
  final $Res Function(_PaymentTransaction) _then;

/// Create a copy of PaymentTransaction
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? merchantId = null,Object? amount = null,Object? currency = null,Object? status = null,Object? type = null,Object? createdAt = null,Object? processedAt = freezed,Object? failedAt = freezed,Object? description = freezed,Object? receiptUrl = freezed,Object? transactionId = freezed,Object? failureReason = freezed,Object? metadata = freezed,Object? tags = freezed,Object? invoiceId = freezed,Object? subscriptionId = freezed,}) {
  return _then(_PaymentTransaction(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,merchantId: null == merchantId ? _self.merchantId : merchantId // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as PaymentStatus,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as PaymentType,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,processedAt: freezed == processedAt ? _self.processedAt : processedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,failedAt: freezed == failedAt ? _self.failedAt : failedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,receiptUrl: freezed == receiptUrl ? _self.receiptUrl : receiptUrl // ignore: cast_nullable_to_non_nullable
as String?,transactionId: freezed == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String?,failureReason: freezed == failureReason ? _self.failureReason : failureReason // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,tags: freezed == tags ? _self._tags : tags // ignore: cast_nullable_to_non_nullable
as List<String>?,invoiceId: freezed == invoiceId ? _self.invoiceId : invoiceId // ignore: cast_nullable_to_non_nullable
as String?,subscriptionId: freezed == subscriptionId ? _self.subscriptionId : subscriptionId // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$Subscription {

 String get id; String get userId; String get planId; SubscriptionStatus get status; DateTime get startDate; DateTime get endDate; double get amount; String get currency; BillingCycle get billingCycle; bool get autoRenew; DateTime? get nextBillingDate; DateTime? get cancelledAt; String? get cancellationReason; String? get externalSubscriptionId; Map<String, dynamic>? get features; Map<String, dynamic>? get metadata;
/// Create a copy of Subscription
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SubscriptionCopyWith<Subscription> get copyWith => _$SubscriptionCopyWithImpl<Subscription>(this as Subscription, _$identity);

  /// Serializes this Subscription to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Subscription&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.planId, planId) || other.planId == planId)&&(identical(other.status, status) || other.status == status)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.billingCycle, billingCycle) || other.billingCycle == billingCycle)&&(identical(other.autoRenew, autoRenew) || other.autoRenew == autoRenew)&&(identical(other.nextBillingDate, nextBillingDate) || other.nextBillingDate == nextBillingDate)&&(identical(other.cancelledAt, cancelledAt) || other.cancelledAt == cancelledAt)&&(identical(other.cancellationReason, cancellationReason) || other.cancellationReason == cancellationReason)&&(identical(other.externalSubscriptionId, externalSubscriptionId) || other.externalSubscriptionId == externalSubscriptionId)&&const DeepCollectionEquality().equals(other.features, features)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,planId,status,startDate,endDate,amount,currency,billingCycle,autoRenew,nextBillingDate,cancelledAt,cancellationReason,externalSubscriptionId,const DeepCollectionEquality().hash(features),const DeepCollectionEquality().hash(metadata));

@override
String toString() {
  return 'Subscription(id: $id, userId: $userId, planId: $planId, status: $status, startDate: $startDate, endDate: $endDate, amount: $amount, currency: $currency, billingCycle: $billingCycle, autoRenew: $autoRenew, nextBillingDate: $nextBillingDate, cancelledAt: $cancelledAt, cancellationReason: $cancellationReason, externalSubscriptionId: $externalSubscriptionId, features: $features, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $SubscriptionCopyWith<$Res>  {
  factory $SubscriptionCopyWith(Subscription value, $Res Function(Subscription) _then) = _$SubscriptionCopyWithImpl;
@useResult
$Res call({
 String id, String userId, String planId, SubscriptionStatus status, DateTime startDate, DateTime endDate, double amount, String currency, BillingCycle billingCycle, bool autoRenew, DateTime? nextBillingDate, DateTime? cancelledAt, String? cancellationReason, String? externalSubscriptionId, Map<String, dynamic>? features, Map<String, dynamic>? metadata
});




}
/// @nodoc
class _$SubscriptionCopyWithImpl<$Res>
    implements $SubscriptionCopyWith<$Res> {
  _$SubscriptionCopyWithImpl(this._self, this._then);

  final Subscription _self;
  final $Res Function(Subscription) _then;

/// Create a copy of Subscription
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? planId = null,Object? status = null,Object? startDate = null,Object? endDate = null,Object? amount = null,Object? currency = null,Object? billingCycle = null,Object? autoRenew = null,Object? nextBillingDate = freezed,Object? cancelledAt = freezed,Object? cancellationReason = freezed,Object? externalSubscriptionId = freezed,Object? features = freezed,Object? metadata = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,planId: null == planId ? _self.planId : planId // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as SubscriptionStatus,startDate: null == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime,endDate: null == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,billingCycle: null == billingCycle ? _self.billingCycle : billingCycle // ignore: cast_nullable_to_non_nullable
as BillingCycle,autoRenew: null == autoRenew ? _self.autoRenew : autoRenew // ignore: cast_nullable_to_non_nullable
as bool,nextBillingDate: freezed == nextBillingDate ? _self.nextBillingDate : nextBillingDate // ignore: cast_nullable_to_non_nullable
as DateTime?,cancelledAt: freezed == cancelledAt ? _self.cancelledAt : cancelledAt // ignore: cast_nullable_to_non_nullable
as DateTime?,cancellationReason: freezed == cancellationReason ? _self.cancellationReason : cancellationReason // ignore: cast_nullable_to_non_nullable
as String?,externalSubscriptionId: freezed == externalSubscriptionId ? _self.externalSubscriptionId : externalSubscriptionId // ignore: cast_nullable_to_non_nullable
as String?,features: freezed == features ? _self.features : features // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [Subscription].
extension SubscriptionPatterns on Subscription {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Subscription value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Subscription() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Subscription value)  $default,){
final _that = this;
switch (_that) {
case _Subscription():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Subscription value)?  $default,){
final _that = this;
switch (_that) {
case _Subscription() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  String planId,  SubscriptionStatus status,  DateTime startDate,  DateTime endDate,  double amount,  String currency,  BillingCycle billingCycle,  bool autoRenew,  DateTime? nextBillingDate,  DateTime? cancelledAt,  String? cancellationReason,  String? externalSubscriptionId,  Map<String, dynamic>? features,  Map<String, dynamic>? metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Subscription() when $default != null:
return $default(_that.id,_that.userId,_that.planId,_that.status,_that.startDate,_that.endDate,_that.amount,_that.currency,_that.billingCycle,_that.autoRenew,_that.nextBillingDate,_that.cancelledAt,_that.cancellationReason,_that.externalSubscriptionId,_that.features,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  String planId,  SubscriptionStatus status,  DateTime startDate,  DateTime endDate,  double amount,  String currency,  BillingCycle billingCycle,  bool autoRenew,  DateTime? nextBillingDate,  DateTime? cancelledAt,  String? cancellationReason,  String? externalSubscriptionId,  Map<String, dynamic>? features,  Map<String, dynamic>? metadata)  $default,) {final _that = this;
switch (_that) {
case _Subscription():
return $default(_that.id,_that.userId,_that.planId,_that.status,_that.startDate,_that.endDate,_that.amount,_that.currency,_that.billingCycle,_that.autoRenew,_that.nextBillingDate,_that.cancelledAt,_that.cancellationReason,_that.externalSubscriptionId,_that.features,_that.metadata);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  String planId,  SubscriptionStatus status,  DateTime startDate,  DateTime endDate,  double amount,  String currency,  BillingCycle billingCycle,  bool autoRenew,  DateTime? nextBillingDate,  DateTime? cancelledAt,  String? cancellationReason,  String? externalSubscriptionId,  Map<String, dynamic>? features,  Map<String, dynamic>? metadata)?  $default,) {final _that = this;
switch (_that) {
case _Subscription() when $default != null:
return $default(_that.id,_that.userId,_that.planId,_that.status,_that.startDate,_that.endDate,_that.amount,_that.currency,_that.billingCycle,_that.autoRenew,_that.nextBillingDate,_that.cancelledAt,_that.cancellationReason,_that.externalSubscriptionId,_that.features,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Subscription implements Subscription {
  const _Subscription({required this.id, required this.userId, required this.planId, required this.status, required this.startDate, required this.endDate, required this.amount, required this.currency, required this.billingCycle, required this.autoRenew, this.nextBillingDate, this.cancelledAt, this.cancellationReason, this.externalSubscriptionId, final  Map<String, dynamic>? features, final  Map<String, dynamic>? metadata}): _features = features,_metadata = metadata;
  factory _Subscription.fromJson(Map<String, dynamic> json) => _$SubscriptionFromJson(json);

@override final  String id;
@override final  String userId;
@override final  String planId;
@override final  SubscriptionStatus status;
@override final  DateTime startDate;
@override final  DateTime endDate;
@override final  double amount;
@override final  String currency;
@override final  BillingCycle billingCycle;
@override final  bool autoRenew;
@override final  DateTime? nextBillingDate;
@override final  DateTime? cancelledAt;
@override final  String? cancellationReason;
@override final  String? externalSubscriptionId;
 final  Map<String, dynamic>? _features;
@override Map<String, dynamic>? get features {
  final value = _features;
  if (value == null) return null;
  if (_features is EqualUnmodifiableMapView) return _features;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of Subscription
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SubscriptionCopyWith<_Subscription> get copyWith => __$SubscriptionCopyWithImpl<_Subscription>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SubscriptionToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Subscription&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.planId, planId) || other.planId == planId)&&(identical(other.status, status) || other.status == status)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.billingCycle, billingCycle) || other.billingCycle == billingCycle)&&(identical(other.autoRenew, autoRenew) || other.autoRenew == autoRenew)&&(identical(other.nextBillingDate, nextBillingDate) || other.nextBillingDate == nextBillingDate)&&(identical(other.cancelledAt, cancelledAt) || other.cancelledAt == cancelledAt)&&(identical(other.cancellationReason, cancellationReason) || other.cancellationReason == cancellationReason)&&(identical(other.externalSubscriptionId, externalSubscriptionId) || other.externalSubscriptionId == externalSubscriptionId)&&const DeepCollectionEquality().equals(other._features, _features)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,planId,status,startDate,endDate,amount,currency,billingCycle,autoRenew,nextBillingDate,cancelledAt,cancellationReason,externalSubscriptionId,const DeepCollectionEquality().hash(_features),const DeepCollectionEquality().hash(_metadata));

@override
String toString() {
  return 'Subscription(id: $id, userId: $userId, planId: $planId, status: $status, startDate: $startDate, endDate: $endDate, amount: $amount, currency: $currency, billingCycle: $billingCycle, autoRenew: $autoRenew, nextBillingDate: $nextBillingDate, cancelledAt: $cancelledAt, cancellationReason: $cancellationReason, externalSubscriptionId: $externalSubscriptionId, features: $features, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$SubscriptionCopyWith<$Res> implements $SubscriptionCopyWith<$Res> {
  factory _$SubscriptionCopyWith(_Subscription value, $Res Function(_Subscription) _then) = __$SubscriptionCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, String planId, SubscriptionStatus status, DateTime startDate, DateTime endDate, double amount, String currency, BillingCycle billingCycle, bool autoRenew, DateTime? nextBillingDate, DateTime? cancelledAt, String? cancellationReason, String? externalSubscriptionId, Map<String, dynamic>? features, Map<String, dynamic>? metadata
});




}
/// @nodoc
class __$SubscriptionCopyWithImpl<$Res>
    implements _$SubscriptionCopyWith<$Res> {
  __$SubscriptionCopyWithImpl(this._self, this._then);

  final _Subscription _self;
  final $Res Function(_Subscription) _then;

/// Create a copy of Subscription
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? planId = null,Object? status = null,Object? startDate = null,Object? endDate = null,Object? amount = null,Object? currency = null,Object? billingCycle = null,Object? autoRenew = null,Object? nextBillingDate = freezed,Object? cancelledAt = freezed,Object? cancellationReason = freezed,Object? externalSubscriptionId = freezed,Object? features = freezed,Object? metadata = freezed,}) {
  return _then(_Subscription(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,planId: null == planId ? _self.planId : planId // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as SubscriptionStatus,startDate: null == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime,endDate: null == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,billingCycle: null == billingCycle ? _self.billingCycle : billingCycle // ignore: cast_nullable_to_non_nullable
as BillingCycle,autoRenew: null == autoRenew ? _self.autoRenew : autoRenew // ignore: cast_nullable_to_non_nullable
as bool,nextBillingDate: freezed == nextBillingDate ? _self.nextBillingDate : nextBillingDate // ignore: cast_nullable_to_non_nullable
as DateTime?,cancelledAt: freezed == cancelledAt ? _self.cancelledAt : cancelledAt // ignore: cast_nullable_to_non_nullable
as DateTime?,cancellationReason: freezed == cancellationReason ? _self.cancellationReason : cancellationReason // ignore: cast_nullable_to_non_nullable
as String?,externalSubscriptionId: freezed == externalSubscriptionId ? _self.externalSubscriptionId : externalSubscriptionId // ignore: cast_nullable_to_non_nullable
as String?,features: freezed == features ? _self._features : features // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}


/// @nodoc
mixin _$PaymentPlan {

 String get id; String get name; String get description; double get price; String get currency; BillingCycle get billingCycle; List<String> get features; bool get isActive; bool get isPopular; String? get imageUrl; Map<String, dynamic>? get metadata; DateTime? get validFrom; DateTime? get validUntil;
/// Create a copy of PaymentPlan
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PaymentPlanCopyWith<PaymentPlan> get copyWith => _$PaymentPlanCopyWithImpl<PaymentPlan>(this as PaymentPlan, _$identity);

  /// Serializes this PaymentPlan to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PaymentPlan&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.description, description) || other.description == description)&&(identical(other.price, price) || other.price == price)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.billingCycle, billingCycle) || other.billingCycle == billingCycle)&&const DeepCollectionEquality().equals(other.features, features)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.isPopular, isPopular) || other.isPopular == isPopular)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl)&&const DeepCollectionEquality().equals(other.metadata, metadata)&&(identical(other.validFrom, validFrom) || other.validFrom == validFrom)&&(identical(other.validUntil, validUntil) || other.validUntil == validUntil));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,description,price,currency,billingCycle,const DeepCollectionEquality().hash(features),isActive,isPopular,imageUrl,const DeepCollectionEquality().hash(metadata),validFrom,validUntil);

@override
String toString() {
  return 'PaymentPlan(id: $id, name: $name, description: $description, price: $price, currency: $currency, billingCycle: $billingCycle, features: $features, isActive: $isActive, isPopular: $isPopular, imageUrl: $imageUrl, metadata: $metadata, validFrom: $validFrom, validUntil: $validUntil)';
}


}

/// @nodoc
abstract mixin class $PaymentPlanCopyWith<$Res>  {
  factory $PaymentPlanCopyWith(PaymentPlan value, $Res Function(PaymentPlan) _then) = _$PaymentPlanCopyWithImpl;
@useResult
$Res call({
 String id, String name, String description, double price, String currency, BillingCycle billingCycle, List<String> features, bool isActive, bool isPopular, String? imageUrl, Map<String, dynamic>? metadata, DateTime? validFrom, DateTime? validUntil
});




}
/// @nodoc
class _$PaymentPlanCopyWithImpl<$Res>
    implements $PaymentPlanCopyWith<$Res> {
  _$PaymentPlanCopyWithImpl(this._self, this._then);

  final PaymentPlan _self;
  final $Res Function(PaymentPlan) _then;

/// Create a copy of PaymentPlan
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? description = null,Object? price = null,Object? currency = null,Object? billingCycle = null,Object? features = null,Object? isActive = null,Object? isPopular = null,Object? imageUrl = freezed,Object? metadata = freezed,Object? validFrom = freezed,Object? validUntil = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,billingCycle: null == billingCycle ? _self.billingCycle : billingCycle // ignore: cast_nullable_to_non_nullable
as BillingCycle,features: null == features ? _self.features : features // ignore: cast_nullable_to_non_nullable
as List<String>,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,isPopular: null == isPopular ? _self.isPopular : isPopular // ignore: cast_nullable_to_non_nullable
as bool,imageUrl: freezed == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,validFrom: freezed == validFrom ? _self.validFrom : validFrom // ignore: cast_nullable_to_non_nullable
as DateTime?,validUntil: freezed == validUntil ? _self.validUntil : validUntil // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [PaymentPlan].
extension PaymentPlanPatterns on PaymentPlan {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PaymentPlan value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PaymentPlan() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PaymentPlan value)  $default,){
final _that = this;
switch (_that) {
case _PaymentPlan():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PaymentPlan value)?  $default,){
final _that = this;
switch (_that) {
case _PaymentPlan() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  String description,  double price,  String currency,  BillingCycle billingCycle,  List<String> features,  bool isActive,  bool isPopular,  String? imageUrl,  Map<String, dynamic>? metadata,  DateTime? validFrom,  DateTime? validUntil)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PaymentPlan() when $default != null:
return $default(_that.id,_that.name,_that.description,_that.price,_that.currency,_that.billingCycle,_that.features,_that.isActive,_that.isPopular,_that.imageUrl,_that.metadata,_that.validFrom,_that.validUntil);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  String description,  double price,  String currency,  BillingCycle billingCycle,  List<String> features,  bool isActive,  bool isPopular,  String? imageUrl,  Map<String, dynamic>? metadata,  DateTime? validFrom,  DateTime? validUntil)  $default,) {final _that = this;
switch (_that) {
case _PaymentPlan():
return $default(_that.id,_that.name,_that.description,_that.price,_that.currency,_that.billingCycle,_that.features,_that.isActive,_that.isPopular,_that.imageUrl,_that.metadata,_that.validFrom,_that.validUntil);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  String description,  double price,  String currency,  BillingCycle billingCycle,  List<String> features,  bool isActive,  bool isPopular,  String? imageUrl,  Map<String, dynamic>? metadata,  DateTime? validFrom,  DateTime? validUntil)?  $default,) {final _that = this;
switch (_that) {
case _PaymentPlan() when $default != null:
return $default(_that.id,_that.name,_that.description,_that.price,_that.currency,_that.billingCycle,_that.features,_that.isActive,_that.isPopular,_that.imageUrl,_that.metadata,_that.validFrom,_that.validUntil);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PaymentPlan implements PaymentPlan {
  const _PaymentPlan({required this.id, required this.name, required this.description, required this.price, required this.currency, required this.billingCycle, required final  List<String> features, required this.isActive, required this.isPopular, this.imageUrl, final  Map<String, dynamic>? metadata, this.validFrom, this.validUntil}): _features = features,_metadata = metadata;
  factory _PaymentPlan.fromJson(Map<String, dynamic> json) => _$PaymentPlanFromJson(json);

@override final  String id;
@override final  String name;
@override final  String description;
@override final  double price;
@override final  String currency;
@override final  BillingCycle billingCycle;
 final  List<String> _features;
@override List<String> get features {
  if (_features is EqualUnmodifiableListView) return _features;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_features);
}

@override final  bool isActive;
@override final  bool isPopular;
@override final  String? imageUrl;
 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override final  DateTime? validFrom;
@override final  DateTime? validUntil;

/// Create a copy of PaymentPlan
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PaymentPlanCopyWith<_PaymentPlan> get copyWith => __$PaymentPlanCopyWithImpl<_PaymentPlan>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PaymentPlanToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PaymentPlan&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.description, description) || other.description == description)&&(identical(other.price, price) || other.price == price)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.billingCycle, billingCycle) || other.billingCycle == billingCycle)&&const DeepCollectionEquality().equals(other._features, _features)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.isPopular, isPopular) || other.isPopular == isPopular)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl)&&const DeepCollectionEquality().equals(other._metadata, _metadata)&&(identical(other.validFrom, validFrom) || other.validFrom == validFrom)&&(identical(other.validUntil, validUntil) || other.validUntil == validUntil));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,description,price,currency,billingCycle,const DeepCollectionEquality().hash(_features),isActive,isPopular,imageUrl,const DeepCollectionEquality().hash(_metadata),validFrom,validUntil);

@override
String toString() {
  return 'PaymentPlan(id: $id, name: $name, description: $description, price: $price, currency: $currency, billingCycle: $billingCycle, features: $features, isActive: $isActive, isPopular: $isPopular, imageUrl: $imageUrl, metadata: $metadata, validFrom: $validFrom, validUntil: $validUntil)';
}


}

/// @nodoc
abstract mixin class _$PaymentPlanCopyWith<$Res> implements $PaymentPlanCopyWith<$Res> {
  factory _$PaymentPlanCopyWith(_PaymentPlan value, $Res Function(_PaymentPlan) _then) = __$PaymentPlanCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, String description, double price, String currency, BillingCycle billingCycle, List<String> features, bool isActive, bool isPopular, String? imageUrl, Map<String, dynamic>? metadata, DateTime? validFrom, DateTime? validUntil
});




}
/// @nodoc
class __$PaymentPlanCopyWithImpl<$Res>
    implements _$PaymentPlanCopyWith<$Res> {
  __$PaymentPlanCopyWithImpl(this._self, this._then);

  final _PaymentPlan _self;
  final $Res Function(_PaymentPlan) _then;

/// Create a copy of PaymentPlan
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? description = null,Object? price = null,Object? currency = null,Object? billingCycle = null,Object? features = null,Object? isActive = null,Object? isPopular = null,Object? imageUrl = freezed,Object? metadata = freezed,Object? validFrom = freezed,Object? validUntil = freezed,}) {
  return _then(_PaymentPlan(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,billingCycle: null == billingCycle ? _self.billingCycle : billingCycle // ignore: cast_nullable_to_non_nullable
as BillingCycle,features: null == features ? _self._features : features // ignore: cast_nullable_to_non_nullable
as List<String>,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,isPopular: null == isPopular ? _self.isPopular : isPopular // ignore: cast_nullable_to_non_nullable
as bool,imageUrl: freezed == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,validFrom: freezed == validFrom ? _self.validFrom : validFrom // ignore: cast_nullable_to_non_nullable
as DateTime?,validUntil: freezed == validUntil ? _self.validUntil : validUntil // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$Invoice {

 String get id; String get userId; String get subscriptionId; double get amount; String get currency; InvoiceStatus get status; DateTime get dueDate; DateTime get createdAt; DateTime? get paidAt; String? get description; String? get invoiceUrl; String? get receiptUrl; Map<String, dynamic>? get items; Map<String, dynamic>? get metadata;
/// Create a copy of Invoice
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$InvoiceCopyWith<Invoice> get copyWith => _$InvoiceCopyWithImpl<Invoice>(this as Invoice, _$identity);

  /// Serializes this Invoice to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Invoice&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.subscriptionId, subscriptionId) || other.subscriptionId == subscriptionId)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.status, status) || other.status == status)&&(identical(other.dueDate, dueDate) || other.dueDate == dueDate)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.paidAt, paidAt) || other.paidAt == paidAt)&&(identical(other.description, description) || other.description == description)&&(identical(other.invoiceUrl, invoiceUrl) || other.invoiceUrl == invoiceUrl)&&(identical(other.receiptUrl, receiptUrl) || other.receiptUrl == receiptUrl)&&const DeepCollectionEquality().equals(other.items, items)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,subscriptionId,amount,currency,status,dueDate,createdAt,paidAt,description,invoiceUrl,receiptUrl,const DeepCollectionEquality().hash(items),const DeepCollectionEquality().hash(metadata));

@override
String toString() {
  return 'Invoice(id: $id, userId: $userId, subscriptionId: $subscriptionId, amount: $amount, currency: $currency, status: $status, dueDate: $dueDate, createdAt: $createdAt, paidAt: $paidAt, description: $description, invoiceUrl: $invoiceUrl, receiptUrl: $receiptUrl, items: $items, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $InvoiceCopyWith<$Res>  {
  factory $InvoiceCopyWith(Invoice value, $Res Function(Invoice) _then) = _$InvoiceCopyWithImpl;
@useResult
$Res call({
 String id, String userId, String subscriptionId, double amount, String currency, InvoiceStatus status, DateTime dueDate, DateTime createdAt, DateTime? paidAt, String? description, String? invoiceUrl, String? receiptUrl, Map<String, dynamic>? items, Map<String, dynamic>? metadata
});




}
/// @nodoc
class _$InvoiceCopyWithImpl<$Res>
    implements $InvoiceCopyWith<$Res> {
  _$InvoiceCopyWithImpl(this._self, this._then);

  final Invoice _self;
  final $Res Function(Invoice) _then;

/// Create a copy of Invoice
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? subscriptionId = null,Object? amount = null,Object? currency = null,Object? status = null,Object? dueDate = null,Object? createdAt = null,Object? paidAt = freezed,Object? description = freezed,Object? invoiceUrl = freezed,Object? receiptUrl = freezed,Object? items = freezed,Object? metadata = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,subscriptionId: null == subscriptionId ? _self.subscriptionId : subscriptionId // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as InvoiceStatus,dueDate: null == dueDate ? _self.dueDate : dueDate // ignore: cast_nullable_to_non_nullable
as DateTime,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,paidAt: freezed == paidAt ? _self.paidAt : paidAt // ignore: cast_nullable_to_non_nullable
as DateTime?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,invoiceUrl: freezed == invoiceUrl ? _self.invoiceUrl : invoiceUrl // ignore: cast_nullable_to_non_nullable
as String?,receiptUrl: freezed == receiptUrl ? _self.receiptUrl : receiptUrl // ignore: cast_nullable_to_non_nullable
as String?,items: freezed == items ? _self.items : items // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [Invoice].
extension InvoicePatterns on Invoice {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Invoice value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Invoice() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Invoice value)  $default,){
final _that = this;
switch (_that) {
case _Invoice():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Invoice value)?  $default,){
final _that = this;
switch (_that) {
case _Invoice() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  String subscriptionId,  double amount,  String currency,  InvoiceStatus status,  DateTime dueDate,  DateTime createdAt,  DateTime? paidAt,  String? description,  String? invoiceUrl,  String? receiptUrl,  Map<String, dynamic>? items,  Map<String, dynamic>? metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Invoice() when $default != null:
return $default(_that.id,_that.userId,_that.subscriptionId,_that.amount,_that.currency,_that.status,_that.dueDate,_that.createdAt,_that.paidAt,_that.description,_that.invoiceUrl,_that.receiptUrl,_that.items,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  String subscriptionId,  double amount,  String currency,  InvoiceStatus status,  DateTime dueDate,  DateTime createdAt,  DateTime? paidAt,  String? description,  String? invoiceUrl,  String? receiptUrl,  Map<String, dynamic>? items,  Map<String, dynamic>? metadata)  $default,) {final _that = this;
switch (_that) {
case _Invoice():
return $default(_that.id,_that.userId,_that.subscriptionId,_that.amount,_that.currency,_that.status,_that.dueDate,_that.createdAt,_that.paidAt,_that.description,_that.invoiceUrl,_that.receiptUrl,_that.items,_that.metadata);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  String subscriptionId,  double amount,  String currency,  InvoiceStatus status,  DateTime dueDate,  DateTime createdAt,  DateTime? paidAt,  String? description,  String? invoiceUrl,  String? receiptUrl,  Map<String, dynamic>? items,  Map<String, dynamic>? metadata)?  $default,) {final _that = this;
switch (_that) {
case _Invoice() when $default != null:
return $default(_that.id,_that.userId,_that.subscriptionId,_that.amount,_that.currency,_that.status,_that.dueDate,_that.createdAt,_that.paidAt,_that.description,_that.invoiceUrl,_that.receiptUrl,_that.items,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Invoice implements Invoice {
  const _Invoice({required this.id, required this.userId, required this.subscriptionId, required this.amount, required this.currency, required this.status, required this.dueDate, required this.createdAt, this.paidAt, this.description, this.invoiceUrl, this.receiptUrl, final  Map<String, dynamic>? items, final  Map<String, dynamic>? metadata}): _items = items,_metadata = metadata;
  factory _Invoice.fromJson(Map<String, dynamic> json) => _$InvoiceFromJson(json);

@override final  String id;
@override final  String userId;
@override final  String subscriptionId;
@override final  double amount;
@override final  String currency;
@override final  InvoiceStatus status;
@override final  DateTime dueDate;
@override final  DateTime createdAt;
@override final  DateTime? paidAt;
@override final  String? description;
@override final  String? invoiceUrl;
@override final  String? receiptUrl;
 final  Map<String, dynamic>? _items;
@override Map<String, dynamic>? get items {
  final value = _items;
  if (value == null) return null;
  if (_items is EqualUnmodifiableMapView) return _items;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of Invoice
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$InvoiceCopyWith<_Invoice> get copyWith => __$InvoiceCopyWithImpl<_Invoice>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$InvoiceToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Invoice&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.subscriptionId, subscriptionId) || other.subscriptionId == subscriptionId)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.status, status) || other.status == status)&&(identical(other.dueDate, dueDate) || other.dueDate == dueDate)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.paidAt, paidAt) || other.paidAt == paidAt)&&(identical(other.description, description) || other.description == description)&&(identical(other.invoiceUrl, invoiceUrl) || other.invoiceUrl == invoiceUrl)&&(identical(other.receiptUrl, receiptUrl) || other.receiptUrl == receiptUrl)&&const DeepCollectionEquality().equals(other._items, _items)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,subscriptionId,amount,currency,status,dueDate,createdAt,paidAt,description,invoiceUrl,receiptUrl,const DeepCollectionEquality().hash(_items),const DeepCollectionEquality().hash(_metadata));

@override
String toString() {
  return 'Invoice(id: $id, userId: $userId, subscriptionId: $subscriptionId, amount: $amount, currency: $currency, status: $status, dueDate: $dueDate, createdAt: $createdAt, paidAt: $paidAt, description: $description, invoiceUrl: $invoiceUrl, receiptUrl: $receiptUrl, items: $items, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$InvoiceCopyWith<$Res> implements $InvoiceCopyWith<$Res> {
  factory _$InvoiceCopyWith(_Invoice value, $Res Function(_Invoice) _then) = __$InvoiceCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, String subscriptionId, double amount, String currency, InvoiceStatus status, DateTime dueDate, DateTime createdAt, DateTime? paidAt, String? description, String? invoiceUrl, String? receiptUrl, Map<String, dynamic>? items, Map<String, dynamic>? metadata
});




}
/// @nodoc
class __$InvoiceCopyWithImpl<$Res>
    implements _$InvoiceCopyWith<$Res> {
  __$InvoiceCopyWithImpl(this._self, this._then);

  final _Invoice _self;
  final $Res Function(_Invoice) _then;

/// Create a copy of Invoice
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? subscriptionId = null,Object? amount = null,Object? currency = null,Object? status = null,Object? dueDate = null,Object? createdAt = null,Object? paidAt = freezed,Object? description = freezed,Object? invoiceUrl = freezed,Object? receiptUrl = freezed,Object? items = freezed,Object? metadata = freezed,}) {
  return _then(_Invoice(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,subscriptionId: null == subscriptionId ? _self.subscriptionId : subscriptionId // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as InvoiceStatus,dueDate: null == dueDate ? _self.dueDate : dueDate // ignore: cast_nullable_to_non_nullable
as DateTime,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,paidAt: freezed == paidAt ? _self.paidAt : paidAt // ignore: cast_nullable_to_non_nullable
as DateTime?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,invoiceUrl: freezed == invoiceUrl ? _self.invoiceUrl : invoiceUrl // ignore: cast_nullable_to_non_nullable
as String?,receiptUrl: freezed == receiptUrl ? _self.receiptUrl : receiptUrl // ignore: cast_nullable_to_non_nullable
as String?,items: freezed == items ? _self._items : items // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}


/// @nodoc
mixin _$PaymentWebhook {

 String get id; String get eventType; Map<String, dynamic> get payload; DateTime get receivedAt; bool get isProcessed; DateTime? get processedAt; String? get errorMessage;
/// Create a copy of PaymentWebhook
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PaymentWebhookCopyWith<PaymentWebhook> get copyWith => _$PaymentWebhookCopyWithImpl<PaymentWebhook>(this as PaymentWebhook, _$identity);

  /// Serializes this PaymentWebhook to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PaymentWebhook&&(identical(other.id, id) || other.id == id)&&(identical(other.eventType, eventType) || other.eventType == eventType)&&const DeepCollectionEquality().equals(other.payload, payload)&&(identical(other.receivedAt, receivedAt) || other.receivedAt == receivedAt)&&(identical(other.isProcessed, isProcessed) || other.isProcessed == isProcessed)&&(identical(other.processedAt, processedAt) || other.processedAt == processedAt)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,eventType,const DeepCollectionEquality().hash(payload),receivedAt,isProcessed,processedAt,errorMessage);

@override
String toString() {
  return 'PaymentWebhook(id: $id, eventType: $eventType, payload: $payload, receivedAt: $receivedAt, isProcessed: $isProcessed, processedAt: $processedAt, errorMessage: $errorMessage)';
}


}

/// @nodoc
abstract mixin class $PaymentWebhookCopyWith<$Res>  {
  factory $PaymentWebhookCopyWith(PaymentWebhook value, $Res Function(PaymentWebhook) _then) = _$PaymentWebhookCopyWithImpl;
@useResult
$Res call({
 String id, String eventType, Map<String, dynamic> payload, DateTime receivedAt, bool isProcessed, DateTime? processedAt, String? errorMessage
});




}
/// @nodoc
class _$PaymentWebhookCopyWithImpl<$Res>
    implements $PaymentWebhookCopyWith<$Res> {
  _$PaymentWebhookCopyWithImpl(this._self, this._then);

  final PaymentWebhook _self;
  final $Res Function(PaymentWebhook) _then;

/// Create a copy of PaymentWebhook
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? eventType = null,Object? payload = null,Object? receivedAt = null,Object? isProcessed = null,Object? processedAt = freezed,Object? errorMessage = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,eventType: null == eventType ? _self.eventType : eventType // ignore: cast_nullable_to_non_nullable
as String,payload: null == payload ? _self.payload : payload // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,receivedAt: null == receivedAt ? _self.receivedAt : receivedAt // ignore: cast_nullable_to_non_nullable
as DateTime,isProcessed: null == isProcessed ? _self.isProcessed : isProcessed // ignore: cast_nullable_to_non_nullable
as bool,processedAt: freezed == processedAt ? _self.processedAt : processedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [PaymentWebhook].
extension PaymentWebhookPatterns on PaymentWebhook {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PaymentWebhook value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PaymentWebhook() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PaymentWebhook value)  $default,){
final _that = this;
switch (_that) {
case _PaymentWebhook():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PaymentWebhook value)?  $default,){
final _that = this;
switch (_that) {
case _PaymentWebhook() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String eventType,  Map<String, dynamic> payload,  DateTime receivedAt,  bool isProcessed,  DateTime? processedAt,  String? errorMessage)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PaymentWebhook() when $default != null:
return $default(_that.id,_that.eventType,_that.payload,_that.receivedAt,_that.isProcessed,_that.processedAt,_that.errorMessage);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String eventType,  Map<String, dynamic> payload,  DateTime receivedAt,  bool isProcessed,  DateTime? processedAt,  String? errorMessage)  $default,) {final _that = this;
switch (_that) {
case _PaymentWebhook():
return $default(_that.id,_that.eventType,_that.payload,_that.receivedAt,_that.isProcessed,_that.processedAt,_that.errorMessage);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String eventType,  Map<String, dynamic> payload,  DateTime receivedAt,  bool isProcessed,  DateTime? processedAt,  String? errorMessage)?  $default,) {final _that = this;
switch (_that) {
case _PaymentWebhook() when $default != null:
return $default(_that.id,_that.eventType,_that.payload,_that.receivedAt,_that.isProcessed,_that.processedAt,_that.errorMessage);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PaymentWebhook implements PaymentWebhook {
  const _PaymentWebhook({required this.id, required this.eventType, required final  Map<String, dynamic> payload, required this.receivedAt, required this.isProcessed, this.processedAt, this.errorMessage}): _payload = payload;
  factory _PaymentWebhook.fromJson(Map<String, dynamic> json) => _$PaymentWebhookFromJson(json);

@override final  String id;
@override final  String eventType;
 final  Map<String, dynamic> _payload;
@override Map<String, dynamic> get payload {
  if (_payload is EqualUnmodifiableMapView) return _payload;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_payload);
}

@override final  DateTime receivedAt;
@override final  bool isProcessed;
@override final  DateTime? processedAt;
@override final  String? errorMessage;

/// Create a copy of PaymentWebhook
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PaymentWebhookCopyWith<_PaymentWebhook> get copyWith => __$PaymentWebhookCopyWithImpl<_PaymentWebhook>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PaymentWebhookToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PaymentWebhook&&(identical(other.id, id) || other.id == id)&&(identical(other.eventType, eventType) || other.eventType == eventType)&&const DeepCollectionEquality().equals(other._payload, _payload)&&(identical(other.receivedAt, receivedAt) || other.receivedAt == receivedAt)&&(identical(other.isProcessed, isProcessed) || other.isProcessed == isProcessed)&&(identical(other.processedAt, processedAt) || other.processedAt == processedAt)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,eventType,const DeepCollectionEquality().hash(_payload),receivedAt,isProcessed,processedAt,errorMessage);

@override
String toString() {
  return 'PaymentWebhook(id: $id, eventType: $eventType, payload: $payload, receivedAt: $receivedAt, isProcessed: $isProcessed, processedAt: $processedAt, errorMessage: $errorMessage)';
}


}

/// @nodoc
abstract mixin class _$PaymentWebhookCopyWith<$Res> implements $PaymentWebhookCopyWith<$Res> {
  factory _$PaymentWebhookCopyWith(_PaymentWebhook value, $Res Function(_PaymentWebhook) _then) = __$PaymentWebhookCopyWithImpl;
@override @useResult
$Res call({
 String id, String eventType, Map<String, dynamic> payload, DateTime receivedAt, bool isProcessed, DateTime? processedAt, String? errorMessage
});




}
/// @nodoc
class __$PaymentWebhookCopyWithImpl<$Res>
    implements _$PaymentWebhookCopyWith<$Res> {
  __$PaymentWebhookCopyWithImpl(this._self, this._then);

  final _PaymentWebhook _self;
  final $Res Function(_PaymentWebhook) _then;

/// Create a copy of PaymentWebhook
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? eventType = null,Object? payload = null,Object? receivedAt = null,Object? isProcessed = null,Object? processedAt = freezed,Object? errorMessage = freezed,}) {
  return _then(_PaymentWebhook(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,eventType: null == eventType ? _self.eventType : eventType // ignore: cast_nullable_to_non_nullable
as String,payload: null == payload ? _self._payload : payload // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,receivedAt: null == receivedAt ? _self.receivedAt : receivedAt // ignore: cast_nullable_to_non_nullable
as DateTime,isProcessed: null == isProcessed ? _self.isProcessed : isProcessed // ignore: cast_nullable_to_non_nullable
as bool,processedAt: freezed == processedAt ? _self.processedAt : processedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$PaymentRefund {

 String get id; String get transactionId; double get amount; String get currency; RefundStatus get status; DateTime get createdAt; DateTime? get processedAt; String? get reason; String? get externalRefundId; Map<String, dynamic>? get metadata;
/// Create a copy of PaymentRefund
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PaymentRefundCopyWith<PaymentRefund> get copyWith => _$PaymentRefundCopyWithImpl<PaymentRefund>(this as PaymentRefund, _$identity);

  /// Serializes this PaymentRefund to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PaymentRefund&&(identical(other.id, id) || other.id == id)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.processedAt, processedAt) || other.processedAt == processedAt)&&(identical(other.reason, reason) || other.reason == reason)&&(identical(other.externalRefundId, externalRefundId) || other.externalRefundId == externalRefundId)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,transactionId,amount,currency,status,createdAt,processedAt,reason,externalRefundId,const DeepCollectionEquality().hash(metadata));

@override
String toString() {
  return 'PaymentRefund(id: $id, transactionId: $transactionId, amount: $amount, currency: $currency, status: $status, createdAt: $createdAt, processedAt: $processedAt, reason: $reason, externalRefundId: $externalRefundId, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $PaymentRefundCopyWith<$Res>  {
  factory $PaymentRefundCopyWith(PaymentRefund value, $Res Function(PaymentRefund) _then) = _$PaymentRefundCopyWithImpl;
@useResult
$Res call({
 String id, String transactionId, double amount, String currency, RefundStatus status, DateTime createdAt, DateTime? processedAt, String? reason, String? externalRefundId, Map<String, dynamic>? metadata
});




}
/// @nodoc
class _$PaymentRefundCopyWithImpl<$Res>
    implements $PaymentRefundCopyWith<$Res> {
  _$PaymentRefundCopyWithImpl(this._self, this._then);

  final PaymentRefund _self;
  final $Res Function(PaymentRefund) _then;

/// Create a copy of PaymentRefund
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? transactionId = null,Object? amount = null,Object? currency = null,Object? status = null,Object? createdAt = null,Object? processedAt = freezed,Object? reason = freezed,Object? externalRefundId = freezed,Object? metadata = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,transactionId: null == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as RefundStatus,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,processedAt: freezed == processedAt ? _self.processedAt : processedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,reason: freezed == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String?,externalRefundId: freezed == externalRefundId ? _self.externalRefundId : externalRefundId // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [PaymentRefund].
extension PaymentRefundPatterns on PaymentRefund {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PaymentRefund value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PaymentRefund() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PaymentRefund value)  $default,){
final _that = this;
switch (_that) {
case _PaymentRefund():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PaymentRefund value)?  $default,){
final _that = this;
switch (_that) {
case _PaymentRefund() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String transactionId,  double amount,  String currency,  RefundStatus status,  DateTime createdAt,  DateTime? processedAt,  String? reason,  String? externalRefundId,  Map<String, dynamic>? metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PaymentRefund() when $default != null:
return $default(_that.id,_that.transactionId,_that.amount,_that.currency,_that.status,_that.createdAt,_that.processedAt,_that.reason,_that.externalRefundId,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String transactionId,  double amount,  String currency,  RefundStatus status,  DateTime createdAt,  DateTime? processedAt,  String? reason,  String? externalRefundId,  Map<String, dynamic>? metadata)  $default,) {final _that = this;
switch (_that) {
case _PaymentRefund():
return $default(_that.id,_that.transactionId,_that.amount,_that.currency,_that.status,_that.createdAt,_that.processedAt,_that.reason,_that.externalRefundId,_that.metadata);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String transactionId,  double amount,  String currency,  RefundStatus status,  DateTime createdAt,  DateTime? processedAt,  String? reason,  String? externalRefundId,  Map<String, dynamic>? metadata)?  $default,) {final _that = this;
switch (_that) {
case _PaymentRefund() when $default != null:
return $default(_that.id,_that.transactionId,_that.amount,_that.currency,_that.status,_that.createdAt,_that.processedAt,_that.reason,_that.externalRefundId,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PaymentRefund implements PaymentRefund {
  const _PaymentRefund({required this.id, required this.transactionId, required this.amount, required this.currency, required this.status, required this.createdAt, this.processedAt, this.reason, this.externalRefundId, final  Map<String, dynamic>? metadata}): _metadata = metadata;
  factory _PaymentRefund.fromJson(Map<String, dynamic> json) => _$PaymentRefundFromJson(json);

@override final  String id;
@override final  String transactionId;
@override final  double amount;
@override final  String currency;
@override final  RefundStatus status;
@override final  DateTime createdAt;
@override final  DateTime? processedAt;
@override final  String? reason;
@override final  String? externalRefundId;
 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of PaymentRefund
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PaymentRefundCopyWith<_PaymentRefund> get copyWith => __$PaymentRefundCopyWithImpl<_PaymentRefund>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PaymentRefundToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PaymentRefund&&(identical(other.id, id) || other.id == id)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.processedAt, processedAt) || other.processedAt == processedAt)&&(identical(other.reason, reason) || other.reason == reason)&&(identical(other.externalRefundId, externalRefundId) || other.externalRefundId == externalRefundId)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,transactionId,amount,currency,status,createdAt,processedAt,reason,externalRefundId,const DeepCollectionEquality().hash(_metadata));

@override
String toString() {
  return 'PaymentRefund(id: $id, transactionId: $transactionId, amount: $amount, currency: $currency, status: $status, createdAt: $createdAt, processedAt: $processedAt, reason: $reason, externalRefundId: $externalRefundId, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$PaymentRefundCopyWith<$Res> implements $PaymentRefundCopyWith<$Res> {
  factory _$PaymentRefundCopyWith(_PaymentRefund value, $Res Function(_PaymentRefund) _then) = __$PaymentRefundCopyWithImpl;
@override @useResult
$Res call({
 String id, String transactionId, double amount, String currency, RefundStatus status, DateTime createdAt, DateTime? processedAt, String? reason, String? externalRefundId, Map<String, dynamic>? metadata
});




}
/// @nodoc
class __$PaymentRefundCopyWithImpl<$Res>
    implements _$PaymentRefundCopyWith<$Res> {
  __$PaymentRefundCopyWithImpl(this._self, this._then);

  final _PaymentRefund _self;
  final $Res Function(_PaymentRefund) _then;

/// Create a copy of PaymentRefund
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? transactionId = null,Object? amount = null,Object? currency = null,Object? status = null,Object? createdAt = null,Object? processedAt = freezed,Object? reason = freezed,Object? externalRefundId = freezed,Object? metadata = freezed,}) {
  return _then(_PaymentRefund(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,transactionId: null == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as RefundStatus,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,processedAt: freezed == processedAt ? _self.processedAt : processedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,reason: freezed == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String?,externalRefundId: freezed == externalRefundId ? _self.externalRefundId : externalRefundId // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}


/// @nodoc
mixin _$PaymentRequest {

 String get id; String get requesterId; String get recipientId; double get amount; String get currency; String get description; PaymentRequestStatus get status; DateTime get createdAt; DateTime? get dueDate; DateTime? get paidAt; String? get paymentMethodId; Map<String, dynamic>? get metadata;
/// Create a copy of PaymentRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PaymentRequestCopyWith<PaymentRequest> get copyWith => _$PaymentRequestCopyWithImpl<PaymentRequest>(this as PaymentRequest, _$identity);

  /// Serializes this PaymentRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PaymentRequest&&(identical(other.id, id) || other.id == id)&&(identical(other.requesterId, requesterId) || other.requesterId == requesterId)&&(identical(other.recipientId, recipientId) || other.recipientId == recipientId)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.description, description) || other.description == description)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.dueDate, dueDate) || other.dueDate == dueDate)&&(identical(other.paidAt, paidAt) || other.paidAt == paidAt)&&(identical(other.paymentMethodId, paymentMethodId) || other.paymentMethodId == paymentMethodId)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,requesterId,recipientId,amount,currency,description,status,createdAt,dueDate,paidAt,paymentMethodId,const DeepCollectionEquality().hash(metadata));

@override
String toString() {
  return 'PaymentRequest(id: $id, requesterId: $requesterId, recipientId: $recipientId, amount: $amount, currency: $currency, description: $description, status: $status, createdAt: $createdAt, dueDate: $dueDate, paidAt: $paidAt, paymentMethodId: $paymentMethodId, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $PaymentRequestCopyWith<$Res>  {
  factory $PaymentRequestCopyWith(PaymentRequest value, $Res Function(PaymentRequest) _then) = _$PaymentRequestCopyWithImpl;
@useResult
$Res call({
 String id, String requesterId, String recipientId, double amount, String currency, String description, PaymentRequestStatus status, DateTime createdAt, DateTime? dueDate, DateTime? paidAt, String? paymentMethodId, Map<String, dynamic>? metadata
});




}
/// @nodoc
class _$PaymentRequestCopyWithImpl<$Res>
    implements $PaymentRequestCopyWith<$Res> {
  _$PaymentRequestCopyWithImpl(this._self, this._then);

  final PaymentRequest _self;
  final $Res Function(PaymentRequest) _then;

/// Create a copy of PaymentRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? requesterId = null,Object? recipientId = null,Object? amount = null,Object? currency = null,Object? description = null,Object? status = null,Object? createdAt = null,Object? dueDate = freezed,Object? paidAt = freezed,Object? paymentMethodId = freezed,Object? metadata = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,requesterId: null == requesterId ? _self.requesterId : requesterId // ignore: cast_nullable_to_non_nullable
as String,recipientId: null == recipientId ? _self.recipientId : recipientId // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as PaymentRequestStatus,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,dueDate: freezed == dueDate ? _self.dueDate : dueDate // ignore: cast_nullable_to_non_nullable
as DateTime?,paidAt: freezed == paidAt ? _self.paidAt : paidAt // ignore: cast_nullable_to_non_nullable
as DateTime?,paymentMethodId: freezed == paymentMethodId ? _self.paymentMethodId : paymentMethodId // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [PaymentRequest].
extension PaymentRequestPatterns on PaymentRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PaymentRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PaymentRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PaymentRequest value)  $default,){
final _that = this;
switch (_that) {
case _PaymentRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PaymentRequest value)?  $default,){
final _that = this;
switch (_that) {
case _PaymentRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String requesterId,  String recipientId,  double amount,  String currency,  String description,  PaymentRequestStatus status,  DateTime createdAt,  DateTime? dueDate,  DateTime? paidAt,  String? paymentMethodId,  Map<String, dynamic>? metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PaymentRequest() when $default != null:
return $default(_that.id,_that.requesterId,_that.recipientId,_that.amount,_that.currency,_that.description,_that.status,_that.createdAt,_that.dueDate,_that.paidAt,_that.paymentMethodId,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String requesterId,  String recipientId,  double amount,  String currency,  String description,  PaymentRequestStatus status,  DateTime createdAt,  DateTime? dueDate,  DateTime? paidAt,  String? paymentMethodId,  Map<String, dynamic>? metadata)  $default,) {final _that = this;
switch (_that) {
case _PaymentRequest():
return $default(_that.id,_that.requesterId,_that.recipientId,_that.amount,_that.currency,_that.description,_that.status,_that.createdAt,_that.dueDate,_that.paidAt,_that.paymentMethodId,_that.metadata);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String requesterId,  String recipientId,  double amount,  String currency,  String description,  PaymentRequestStatus status,  DateTime createdAt,  DateTime? dueDate,  DateTime? paidAt,  String? paymentMethodId,  Map<String, dynamic>? metadata)?  $default,) {final _that = this;
switch (_that) {
case _PaymentRequest() when $default != null:
return $default(_that.id,_that.requesterId,_that.recipientId,_that.amount,_that.currency,_that.description,_that.status,_that.createdAt,_that.dueDate,_that.paidAt,_that.paymentMethodId,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PaymentRequest implements PaymentRequest {
  const _PaymentRequest({required this.id, required this.requesterId, required this.recipientId, required this.amount, required this.currency, required this.description, required this.status, required this.createdAt, this.dueDate, this.paidAt, this.paymentMethodId, final  Map<String, dynamic>? metadata}): _metadata = metadata;
  factory _PaymentRequest.fromJson(Map<String, dynamic> json) => _$PaymentRequestFromJson(json);

@override final  String id;
@override final  String requesterId;
@override final  String recipientId;
@override final  double amount;
@override final  String currency;
@override final  String description;
@override final  PaymentRequestStatus status;
@override final  DateTime createdAt;
@override final  DateTime? dueDate;
@override final  DateTime? paidAt;
@override final  String? paymentMethodId;
 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of PaymentRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PaymentRequestCopyWith<_PaymentRequest> get copyWith => __$PaymentRequestCopyWithImpl<_PaymentRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PaymentRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PaymentRequest&&(identical(other.id, id) || other.id == id)&&(identical(other.requesterId, requesterId) || other.requesterId == requesterId)&&(identical(other.recipientId, recipientId) || other.recipientId == recipientId)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.description, description) || other.description == description)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.dueDate, dueDate) || other.dueDate == dueDate)&&(identical(other.paidAt, paidAt) || other.paidAt == paidAt)&&(identical(other.paymentMethodId, paymentMethodId) || other.paymentMethodId == paymentMethodId)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,requesterId,recipientId,amount,currency,description,status,createdAt,dueDate,paidAt,paymentMethodId,const DeepCollectionEquality().hash(_metadata));

@override
String toString() {
  return 'PaymentRequest(id: $id, requesterId: $requesterId, recipientId: $recipientId, amount: $amount, currency: $currency, description: $description, status: $status, createdAt: $createdAt, dueDate: $dueDate, paidAt: $paidAt, paymentMethodId: $paymentMethodId, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$PaymentRequestCopyWith<$Res> implements $PaymentRequestCopyWith<$Res> {
  factory _$PaymentRequestCopyWith(_PaymentRequest value, $Res Function(_PaymentRequest) _then) = __$PaymentRequestCopyWithImpl;
@override @useResult
$Res call({
 String id, String requesterId, String recipientId, double amount, String currency, String description, PaymentRequestStatus status, DateTime createdAt, DateTime? dueDate, DateTime? paidAt, String? paymentMethodId, Map<String, dynamic>? metadata
});




}
/// @nodoc
class __$PaymentRequestCopyWithImpl<$Res>
    implements _$PaymentRequestCopyWith<$Res> {
  __$PaymentRequestCopyWithImpl(this._self, this._then);

  final _PaymentRequest _self;
  final $Res Function(_PaymentRequest) _then;

/// Create a copy of PaymentRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? requesterId = null,Object? recipientId = null,Object? amount = null,Object? currency = null,Object? description = null,Object? status = null,Object? createdAt = null,Object? dueDate = freezed,Object? paidAt = freezed,Object? paymentMethodId = freezed,Object? metadata = freezed,}) {
  return _then(_PaymentRequest(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,requesterId: null == requesterId ? _self.requesterId : requesterId // ignore: cast_nullable_to_non_nullable
as String,recipientId: null == recipientId ? _self.recipientId : recipientId // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as PaymentRequestStatus,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,dueDate: freezed == dueDate ? _self.dueDate : dueDate // ignore: cast_nullable_to_non_nullable
as DateTime?,paidAt: freezed == paidAt ? _self.paidAt : paidAt // ignore: cast_nullable_to_non_nullable
as DateTime?,paymentMethodId: freezed == paymentMethodId ? _self.paymentMethodId : paymentMethodId // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}


/// @nodoc
mixin _$SplitPayment {

 String get id; String get organizerId; double get totalAmount; String get currency; SplitPaymentStatus get status; List<SplitPaymentMember> get members; DateTime get createdAt; DateTime? get completedAt; String? get description; Map<String, dynamic>? get metadata;
/// Create a copy of SplitPayment
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SplitPaymentCopyWith<SplitPayment> get copyWith => _$SplitPaymentCopyWithImpl<SplitPayment>(this as SplitPayment, _$identity);

  /// Serializes this SplitPayment to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SplitPayment&&(identical(other.id, id) || other.id == id)&&(identical(other.organizerId, organizerId) || other.organizerId == organizerId)&&(identical(other.totalAmount, totalAmount) || other.totalAmount == totalAmount)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.status, status) || other.status == status)&&const DeepCollectionEquality().equals(other.members, members)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.completedAt, completedAt) || other.completedAt == completedAt)&&(identical(other.description, description) || other.description == description)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,organizerId,totalAmount,currency,status,const DeepCollectionEquality().hash(members),createdAt,completedAt,description,const DeepCollectionEquality().hash(metadata));

@override
String toString() {
  return 'SplitPayment(id: $id, organizerId: $organizerId, totalAmount: $totalAmount, currency: $currency, status: $status, members: $members, createdAt: $createdAt, completedAt: $completedAt, description: $description, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $SplitPaymentCopyWith<$Res>  {
  factory $SplitPaymentCopyWith(SplitPayment value, $Res Function(SplitPayment) _then) = _$SplitPaymentCopyWithImpl;
@useResult
$Res call({
 String id, String organizerId, double totalAmount, String currency, SplitPaymentStatus status, List<SplitPaymentMember> members, DateTime createdAt, DateTime? completedAt, String? description, Map<String, dynamic>? metadata
});




}
/// @nodoc
class _$SplitPaymentCopyWithImpl<$Res>
    implements $SplitPaymentCopyWith<$Res> {
  _$SplitPaymentCopyWithImpl(this._self, this._then);

  final SplitPayment _self;
  final $Res Function(SplitPayment) _then;

/// Create a copy of SplitPayment
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? organizerId = null,Object? totalAmount = null,Object? currency = null,Object? status = null,Object? members = null,Object? createdAt = null,Object? completedAt = freezed,Object? description = freezed,Object? metadata = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,organizerId: null == organizerId ? _self.organizerId : organizerId // ignore: cast_nullable_to_non_nullable
as String,totalAmount: null == totalAmount ? _self.totalAmount : totalAmount // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as SplitPaymentStatus,members: null == members ? _self.members : members // ignore: cast_nullable_to_non_nullable
as List<SplitPaymentMember>,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,completedAt: freezed == completedAt ? _self.completedAt : completedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [SplitPayment].
extension SplitPaymentPatterns on SplitPayment {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SplitPayment value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SplitPayment() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SplitPayment value)  $default,){
final _that = this;
switch (_that) {
case _SplitPayment():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SplitPayment value)?  $default,){
final _that = this;
switch (_that) {
case _SplitPayment() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String organizerId,  double totalAmount,  String currency,  SplitPaymentStatus status,  List<SplitPaymentMember> members,  DateTime createdAt,  DateTime? completedAt,  String? description,  Map<String, dynamic>? metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SplitPayment() when $default != null:
return $default(_that.id,_that.organizerId,_that.totalAmount,_that.currency,_that.status,_that.members,_that.createdAt,_that.completedAt,_that.description,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String organizerId,  double totalAmount,  String currency,  SplitPaymentStatus status,  List<SplitPaymentMember> members,  DateTime createdAt,  DateTime? completedAt,  String? description,  Map<String, dynamic>? metadata)  $default,) {final _that = this;
switch (_that) {
case _SplitPayment():
return $default(_that.id,_that.organizerId,_that.totalAmount,_that.currency,_that.status,_that.members,_that.createdAt,_that.completedAt,_that.description,_that.metadata);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String organizerId,  double totalAmount,  String currency,  SplitPaymentStatus status,  List<SplitPaymentMember> members,  DateTime createdAt,  DateTime? completedAt,  String? description,  Map<String, dynamic>? metadata)?  $default,) {final _that = this;
switch (_that) {
case _SplitPayment() when $default != null:
return $default(_that.id,_that.organizerId,_that.totalAmount,_that.currency,_that.status,_that.members,_that.createdAt,_that.completedAt,_that.description,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SplitPayment implements SplitPayment {
  const _SplitPayment({required this.id, required this.organizerId, required this.totalAmount, required this.currency, required this.status, required final  List<SplitPaymentMember> members, required this.createdAt, this.completedAt, this.description, final  Map<String, dynamic>? metadata}): _members = members,_metadata = metadata;
  factory _SplitPayment.fromJson(Map<String, dynamic> json) => _$SplitPaymentFromJson(json);

@override final  String id;
@override final  String organizerId;
@override final  double totalAmount;
@override final  String currency;
@override final  SplitPaymentStatus status;
 final  List<SplitPaymentMember> _members;
@override List<SplitPaymentMember> get members {
  if (_members is EqualUnmodifiableListView) return _members;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_members);
}

@override final  DateTime createdAt;
@override final  DateTime? completedAt;
@override final  String? description;
 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of SplitPayment
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SplitPaymentCopyWith<_SplitPayment> get copyWith => __$SplitPaymentCopyWithImpl<_SplitPayment>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SplitPaymentToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SplitPayment&&(identical(other.id, id) || other.id == id)&&(identical(other.organizerId, organizerId) || other.organizerId == organizerId)&&(identical(other.totalAmount, totalAmount) || other.totalAmount == totalAmount)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.status, status) || other.status == status)&&const DeepCollectionEquality().equals(other._members, _members)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.completedAt, completedAt) || other.completedAt == completedAt)&&(identical(other.description, description) || other.description == description)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,organizerId,totalAmount,currency,status,const DeepCollectionEquality().hash(_members),createdAt,completedAt,description,const DeepCollectionEquality().hash(_metadata));

@override
String toString() {
  return 'SplitPayment(id: $id, organizerId: $organizerId, totalAmount: $totalAmount, currency: $currency, status: $status, members: $members, createdAt: $createdAt, completedAt: $completedAt, description: $description, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$SplitPaymentCopyWith<$Res> implements $SplitPaymentCopyWith<$Res> {
  factory _$SplitPaymentCopyWith(_SplitPayment value, $Res Function(_SplitPayment) _then) = __$SplitPaymentCopyWithImpl;
@override @useResult
$Res call({
 String id, String organizerId, double totalAmount, String currency, SplitPaymentStatus status, List<SplitPaymentMember> members, DateTime createdAt, DateTime? completedAt, String? description, Map<String, dynamic>? metadata
});




}
/// @nodoc
class __$SplitPaymentCopyWithImpl<$Res>
    implements _$SplitPaymentCopyWith<$Res> {
  __$SplitPaymentCopyWithImpl(this._self, this._then);

  final _SplitPayment _self;
  final $Res Function(_SplitPayment) _then;

/// Create a copy of SplitPayment
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? organizerId = null,Object? totalAmount = null,Object? currency = null,Object? status = null,Object? members = null,Object? createdAt = null,Object? completedAt = freezed,Object? description = freezed,Object? metadata = freezed,}) {
  return _then(_SplitPayment(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,organizerId: null == organizerId ? _self.organizerId : organizerId // ignore: cast_nullable_to_non_nullable
as String,totalAmount: null == totalAmount ? _self.totalAmount : totalAmount // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as SplitPaymentStatus,members: null == members ? _self._members : members // ignore: cast_nullable_to_non_nullable
as List<SplitPaymentMember>,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,completedAt: freezed == completedAt ? _self.completedAt : completedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}


/// @nodoc
mixin _$SplitPaymentMember {

 String get userId; double get amount; SplitPaymentMemberStatus get status; DateTime? get paidAt; String? get paymentMethodId;
/// Create a copy of SplitPaymentMember
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SplitPaymentMemberCopyWith<SplitPaymentMember> get copyWith => _$SplitPaymentMemberCopyWithImpl<SplitPaymentMember>(this as SplitPaymentMember, _$identity);

  /// Serializes this SplitPaymentMember to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SplitPaymentMember&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.status, status) || other.status == status)&&(identical(other.paidAt, paidAt) || other.paidAt == paidAt)&&(identical(other.paymentMethodId, paymentMethodId) || other.paymentMethodId == paymentMethodId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,userId,amount,status,paidAt,paymentMethodId);

@override
String toString() {
  return 'SplitPaymentMember(userId: $userId, amount: $amount, status: $status, paidAt: $paidAt, paymentMethodId: $paymentMethodId)';
}


}

/// @nodoc
abstract mixin class $SplitPaymentMemberCopyWith<$Res>  {
  factory $SplitPaymentMemberCopyWith(SplitPaymentMember value, $Res Function(SplitPaymentMember) _then) = _$SplitPaymentMemberCopyWithImpl;
@useResult
$Res call({
 String userId, double amount, SplitPaymentMemberStatus status, DateTime? paidAt, String? paymentMethodId
});




}
/// @nodoc
class _$SplitPaymentMemberCopyWithImpl<$Res>
    implements $SplitPaymentMemberCopyWith<$Res> {
  _$SplitPaymentMemberCopyWithImpl(this._self, this._then);

  final SplitPaymentMember _self;
  final $Res Function(SplitPaymentMember) _then;

/// Create a copy of SplitPaymentMember
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? userId = null,Object? amount = null,Object? status = null,Object? paidAt = freezed,Object? paymentMethodId = freezed,}) {
  return _then(_self.copyWith(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as SplitPaymentMemberStatus,paidAt: freezed == paidAt ? _self.paidAt : paidAt // ignore: cast_nullable_to_non_nullable
as DateTime?,paymentMethodId: freezed == paymentMethodId ? _self.paymentMethodId : paymentMethodId // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [SplitPaymentMember].
extension SplitPaymentMemberPatterns on SplitPaymentMember {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SplitPaymentMember value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SplitPaymentMember() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SplitPaymentMember value)  $default,){
final _that = this;
switch (_that) {
case _SplitPaymentMember():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SplitPaymentMember value)?  $default,){
final _that = this;
switch (_that) {
case _SplitPaymentMember() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String userId,  double amount,  SplitPaymentMemberStatus status,  DateTime? paidAt,  String? paymentMethodId)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SplitPaymentMember() when $default != null:
return $default(_that.userId,_that.amount,_that.status,_that.paidAt,_that.paymentMethodId);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String userId,  double amount,  SplitPaymentMemberStatus status,  DateTime? paidAt,  String? paymentMethodId)  $default,) {final _that = this;
switch (_that) {
case _SplitPaymentMember():
return $default(_that.userId,_that.amount,_that.status,_that.paidAt,_that.paymentMethodId);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String userId,  double amount,  SplitPaymentMemberStatus status,  DateTime? paidAt,  String? paymentMethodId)?  $default,) {final _that = this;
switch (_that) {
case _SplitPaymentMember() when $default != null:
return $default(_that.userId,_that.amount,_that.status,_that.paidAt,_that.paymentMethodId);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SplitPaymentMember implements SplitPaymentMember {
  const _SplitPaymentMember({required this.userId, required this.amount, required this.status, this.paidAt, this.paymentMethodId});
  factory _SplitPaymentMember.fromJson(Map<String, dynamic> json) => _$SplitPaymentMemberFromJson(json);

@override final  String userId;
@override final  double amount;
@override final  SplitPaymentMemberStatus status;
@override final  DateTime? paidAt;
@override final  String? paymentMethodId;

/// Create a copy of SplitPaymentMember
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SplitPaymentMemberCopyWith<_SplitPaymentMember> get copyWith => __$SplitPaymentMemberCopyWithImpl<_SplitPaymentMember>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SplitPaymentMemberToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SplitPaymentMember&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.status, status) || other.status == status)&&(identical(other.paidAt, paidAt) || other.paidAt == paidAt)&&(identical(other.paymentMethodId, paymentMethodId) || other.paymentMethodId == paymentMethodId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,userId,amount,status,paidAt,paymentMethodId);

@override
String toString() {
  return 'SplitPaymentMember(userId: $userId, amount: $amount, status: $status, paidAt: $paidAt, paymentMethodId: $paymentMethodId)';
}


}

/// @nodoc
abstract mixin class _$SplitPaymentMemberCopyWith<$Res> implements $SplitPaymentMemberCopyWith<$Res> {
  factory _$SplitPaymentMemberCopyWith(_SplitPaymentMember value, $Res Function(_SplitPaymentMember) _then) = __$SplitPaymentMemberCopyWithImpl;
@override @useResult
$Res call({
 String userId, double amount, SplitPaymentMemberStatus status, DateTime? paidAt, String? paymentMethodId
});




}
/// @nodoc
class __$SplitPaymentMemberCopyWithImpl<$Res>
    implements _$SplitPaymentMemberCopyWith<$Res> {
  __$SplitPaymentMemberCopyWithImpl(this._self, this._then);

  final _SplitPaymentMember _self;
  final $Res Function(_SplitPaymentMember) _then;

/// Create a copy of SplitPaymentMember
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? userId = null,Object? amount = null,Object? status = null,Object? paidAt = freezed,Object? paymentMethodId = freezed,}) {
  return _then(_SplitPaymentMember(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as SplitPaymentMemberStatus,paidAt: freezed == paidAt ? _self.paidAt : paidAt // ignore: cast_nullable_to_non_nullable
as DateTime?,paymentMethodId: freezed == paymentMethodId ? _self.paymentMethodId : paymentMethodId // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
