import 'package:freezed_annotation/freezed_annotation.dart';

part 'payment_models.freezed.dart';
part 'payment_models.g.dart';

@freezed
abstract class PaymentMethod with _$PaymentMethod {
  const factory PaymentMethod({
    required String id,
    required String userId,
    required PaymentMethodType type,
    required String lastFourDigits,
    required String cardBrand,
    required DateTime expiryDate,
    required bool isDefault,
    required bool isEnabled,
    String? cardholderName,
    String? billingAddress,
    String? paymentMethodId, // External payment provider ID
    required DateTime createdAt,
    DateTime? lastUsedAt,
    Map<String, dynamic>? metadata,
  }) = _PaymentMethod;

  factory PaymentMethod.fromJson(Map<String, dynamic> json) =>
      _$PaymentMethodFromJson(json);
}

@freezed
abstract class PaymentTransaction with _$PaymentTransaction {
  const factory PaymentTransaction({
    required String id,
    required String userId,
    required String merchantId,
    required double amount,
    required String currency,
    required PaymentStatus status,
    required PaymentType type,
    required DateTime createdAt,
    DateTime? processedAt,
    DateTime? failedAt,
    String? description,
    String? receiptUrl,
    String? transactionId, // External payment provider transaction ID
    String? failureReason,
    Map<String, dynamic>? metadata,
    List<String>? tags,
    String? invoiceId,
    String? subscriptionId,
  }) = _PaymentTransaction;

  factory PaymentTransaction.fromJson(Map<String, dynamic> json) =>
      _$PaymentTransactionFromJson(json);
}

@freezed
abstract class Subscription with _$Subscription {
  const factory Subscription({
    required String id,
    required String userId,
    required String planId,
    required SubscriptionStatus status,
    required DateTime startDate,
    required DateTime endDate,
    required double amount,
    required String currency,
    required BillingCycle billingCycle,
    required bool autoRenew,
    DateTime? nextBillingDate,
    DateTime? cancelledAt,
    String? cancellationReason,
    String? externalSubscriptionId,
    Map<String, dynamic>? features,
    Map<String, dynamic>? metadata,
  }) = _Subscription;

  factory Subscription.fromJson(Map<String, dynamic> json) =>
      _$SubscriptionFromJson(json);
}

@freezed
abstract class PaymentPlan with _$PaymentPlan {
  const factory PaymentPlan({
    required String id,
    required String name,
    required String description,
    required double price,
    required String currency,
    required BillingCycle billingCycle,
    required List<String> features,
    required bool isActive,
    required bool isPopular,
    String? imageUrl,
    Map<String, dynamic>? metadata,
    DateTime? validFrom,
    DateTime? validUntil,
  }) = _PaymentPlan;

  factory PaymentPlan.fromJson(Map<String, dynamic> json) =>
      _$PaymentPlanFromJson(json);
}

@freezed
abstract class Invoice with _$Invoice {
  const factory Invoice({
    required String id,
    required String userId,
    required String subscriptionId,
    required double amount,
    required String currency,
    required InvoiceStatus status,
    required DateTime dueDate,
    required DateTime createdAt,
    DateTime? paidAt,
    String? description,
    String? invoiceUrl,
    String? receiptUrl,
    Map<String, dynamic>? items,
    Map<String, dynamic>? metadata,
  }) = _Invoice;

  factory Invoice.fromJson(Map<String, dynamic> json) =>
      _$InvoiceFromJson(json);
}

@freezed
abstract class PaymentWebhook with _$PaymentWebhook {
  const factory PaymentWebhook({
    required String id,
    required String eventType,
    required Map<String, dynamic> payload,
    required DateTime receivedAt,
    required bool isProcessed,
    DateTime? processedAt,
    String? errorMessage,
  }) = _PaymentWebhook;

  factory PaymentWebhook.fromJson(Map<String, dynamic> json) =>
      _$PaymentWebhookFromJson(json);
}

@freezed
abstract class PaymentRefund with _$PaymentRefund {
  const factory PaymentRefund({
    required String id,
    required String transactionId,
    required double amount,
    required String currency,
    required RefundStatus status,
    required DateTime createdAt,
    DateTime? processedAt,
    String? reason,
    String? externalRefundId,
    Map<String, dynamic>? metadata,
  }) = _PaymentRefund;

  factory PaymentRefund.fromJson(Map<String, dynamic> json) =>
      _$PaymentRefundFromJson(json);
}

// Enums
enum PaymentMethodType {
  creditCard,
  debitCard,
  bankTransfer,
  digitalWallet,
  cryptocurrency,
  giftCard,
}

enum PaymentStatus {
  pending,
  processing,
  completed,
  failed,
  cancelled,
  refunded,
  disputed,
}

enum PaymentType {
  subscription,
  oneTime,
  refund,
  tip,
  donation,
  marketplace,
  event,
  membership,
}

enum SubscriptionStatus {
  active,
  cancelled,
  expired,
  suspended,
  pending,
  trialing,
}

enum BillingCycle { monthly, quarterly, yearly, weekly, daily }

enum InvoiceStatus { draft, sent, paid, overdue, cancelled, voided }

enum RefundStatus { pending, processing, completed, failed, cancelled }

// Payment Request Models
@freezed
abstract class PaymentRequest with _$PaymentRequest {
  const factory PaymentRequest({
    required String id,
    required String requesterId,
    required String recipientId,
    required double amount,
    required String currency,
    required String description,
    required PaymentRequestStatus status,
    required DateTime createdAt,
    DateTime? dueDate,
    DateTime? paidAt,
    String? paymentMethodId,
    Map<String, dynamic>? metadata,
  }) = _PaymentRequest;

  factory PaymentRequest.fromJson(Map<String, dynamic> json) =>
      _$PaymentRequestFromJson(json);
}

enum PaymentRequestStatus { pending, paid, cancelled, expired }

// Split Payment Models
@freezed
abstract class SplitPayment with _$SplitPayment {
  const factory SplitPayment({
    required String id,
    required String organizerId,
    required double totalAmount,
    required String currency,
    required SplitPaymentStatus status,
    required List<SplitPaymentMember> members,
    required DateTime createdAt,
    DateTime? completedAt,
    String? description,
    Map<String, dynamic>? metadata,
  }) = _SplitPayment;

  factory SplitPayment.fromJson(Map<String, dynamic> json) =>
      _$SplitPaymentFromJson(json);
}

@freezed
abstract class SplitPaymentMember with _$SplitPaymentMember {
  const factory SplitPaymentMember({
    required String userId,
    required double amount,
    required SplitPaymentMemberStatus status,
    DateTime? paidAt,
    String? paymentMethodId,
  }) = _SplitPaymentMember;

  factory SplitPaymentMember.fromJson(Map<String, dynamic> json) =>
      _$SplitPaymentMemberFromJson(json);
}

enum SplitPaymentStatus { pending, partial, completed, cancelled }

enum SplitPaymentMemberStatus { pending, paid, declined }
