// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_PaymentMethod _$PaymentMethodFromJson(Map<String, dynamic> json) =>
    _PaymentMethod(
      id: json['id'] as String,
      userId: json['userId'] as String,
      type: $enumDecode(_$PaymentMethodTypeEnumMap, json['type']),
      lastFourDigits: json['lastFourDigits'] as String,
      cardBrand: json['cardBrand'] as String,
      expiryDate: DateTime.parse(json['expiryDate'] as String),
      isDefault: json['isDefault'] as bool,
      isEnabled: json['isEnabled'] as bool,
      cardholderName: json['cardholderName'] as String?,
      billingAddress: json['billingAddress'] as String?,
      paymentMethodId: json['paymentMethodId'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastUsedAt: json['lastUsedAt'] == null
          ? null
          : DateTime.parse(json['lastUsedAt'] as String),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$PaymentMethodToJson(_PaymentMethod instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'type': _$PaymentMethodTypeEnumMap[instance.type]!,
      'lastFourDigits': instance.lastFourDigits,
      'cardBrand': instance.cardBrand,
      'expiryDate': instance.expiryDate.toIso8601String(),
      'isDefault': instance.isDefault,
      'isEnabled': instance.isEnabled,
      'cardholderName': instance.cardholderName,
      'billingAddress': instance.billingAddress,
      'paymentMethodId': instance.paymentMethodId,
      'createdAt': instance.createdAt.toIso8601String(),
      'lastUsedAt': instance.lastUsedAt?.toIso8601String(),
      'metadata': instance.metadata,
    };

const _$PaymentMethodTypeEnumMap = {
  PaymentMethodType.creditCard: 'creditCard',
  PaymentMethodType.debitCard: 'debitCard',
  PaymentMethodType.bankTransfer: 'bankTransfer',
  PaymentMethodType.digitalWallet: 'digitalWallet',
  PaymentMethodType.cryptocurrency: 'cryptocurrency',
  PaymentMethodType.giftCard: 'giftCard',
};

_PaymentTransaction _$PaymentTransactionFromJson(Map<String, dynamic> json) =>
    _PaymentTransaction(
      id: json['id'] as String,
      userId: json['userId'] as String,
      merchantId: json['merchantId'] as String,
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] as String,
      status: $enumDecode(_$PaymentStatusEnumMap, json['status']),
      type: $enumDecode(_$PaymentTypeEnumMap, json['type']),
      createdAt: DateTime.parse(json['createdAt'] as String),
      processedAt: json['processedAt'] == null
          ? null
          : DateTime.parse(json['processedAt'] as String),
      failedAt: json['failedAt'] == null
          ? null
          : DateTime.parse(json['failedAt'] as String),
      description: json['description'] as String?,
      receiptUrl: json['receiptUrl'] as String?,
      transactionId: json['transactionId'] as String?,
      failureReason: json['failureReason'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
      invoiceId: json['invoiceId'] as String?,
      subscriptionId: json['subscriptionId'] as String?,
    );

Map<String, dynamic> _$PaymentTransactionToJson(_PaymentTransaction instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'merchantId': instance.merchantId,
      'amount': instance.amount,
      'currency': instance.currency,
      'status': _$PaymentStatusEnumMap[instance.status]!,
      'type': _$PaymentTypeEnumMap[instance.type]!,
      'createdAt': instance.createdAt.toIso8601String(),
      'processedAt': instance.processedAt?.toIso8601String(),
      'failedAt': instance.failedAt?.toIso8601String(),
      'description': instance.description,
      'receiptUrl': instance.receiptUrl,
      'transactionId': instance.transactionId,
      'failureReason': instance.failureReason,
      'metadata': instance.metadata,
      'tags': instance.tags,
      'invoiceId': instance.invoiceId,
      'subscriptionId': instance.subscriptionId,
    };

const _$PaymentStatusEnumMap = {
  PaymentStatus.pending: 'pending',
  PaymentStatus.processing: 'processing',
  PaymentStatus.completed: 'completed',
  PaymentStatus.failed: 'failed',
  PaymentStatus.cancelled: 'cancelled',
  PaymentStatus.refunded: 'refunded',
  PaymentStatus.disputed: 'disputed',
};

const _$PaymentTypeEnumMap = {
  PaymentType.subscription: 'subscription',
  PaymentType.oneTime: 'oneTime',
  PaymentType.refund: 'refund',
  PaymentType.tip: 'tip',
  PaymentType.donation: 'donation',
  PaymentType.marketplace: 'marketplace',
  PaymentType.event: 'event',
  PaymentType.membership: 'membership',
};

_Subscription _$SubscriptionFromJson(Map<String, dynamic> json) =>
    _Subscription(
      id: json['id'] as String,
      userId: json['userId'] as String,
      planId: json['planId'] as String,
      status: $enumDecode(_$SubscriptionStatusEnumMap, json['status']),
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] as String,
      billingCycle: $enumDecode(_$BillingCycleEnumMap, json['billingCycle']),
      autoRenew: json['autoRenew'] as bool,
      nextBillingDate: json['nextBillingDate'] == null
          ? null
          : DateTime.parse(json['nextBillingDate'] as String),
      cancelledAt: json['cancelledAt'] == null
          ? null
          : DateTime.parse(json['cancelledAt'] as String),
      cancellationReason: json['cancellationReason'] as String?,
      externalSubscriptionId: json['externalSubscriptionId'] as String?,
      features: json['features'] as Map<String, dynamic>?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$SubscriptionToJson(_Subscription instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'planId': instance.planId,
      'status': _$SubscriptionStatusEnumMap[instance.status]!,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'amount': instance.amount,
      'currency': instance.currency,
      'billingCycle': _$BillingCycleEnumMap[instance.billingCycle]!,
      'autoRenew': instance.autoRenew,
      'nextBillingDate': instance.nextBillingDate?.toIso8601String(),
      'cancelledAt': instance.cancelledAt?.toIso8601String(),
      'cancellationReason': instance.cancellationReason,
      'externalSubscriptionId': instance.externalSubscriptionId,
      'features': instance.features,
      'metadata': instance.metadata,
    };

const _$SubscriptionStatusEnumMap = {
  SubscriptionStatus.active: 'active',
  SubscriptionStatus.cancelled: 'cancelled',
  SubscriptionStatus.expired: 'expired',
  SubscriptionStatus.suspended: 'suspended',
  SubscriptionStatus.pending: 'pending',
  SubscriptionStatus.trialing: 'trialing',
};

const _$BillingCycleEnumMap = {
  BillingCycle.monthly: 'monthly',
  BillingCycle.quarterly: 'quarterly',
  BillingCycle.yearly: 'yearly',
  BillingCycle.weekly: 'weekly',
  BillingCycle.daily: 'daily',
};

_PaymentPlan _$PaymentPlanFromJson(Map<String, dynamic> json) => _PaymentPlan(
  id: json['id'] as String,
  name: json['name'] as String,
  description: json['description'] as String,
  price: (json['price'] as num).toDouble(),
  currency: json['currency'] as String,
  billingCycle: $enumDecode(_$BillingCycleEnumMap, json['billingCycle']),
  features: (json['features'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  isActive: json['isActive'] as bool,
  isPopular: json['isPopular'] as bool,
  imageUrl: json['imageUrl'] as String?,
  metadata: json['metadata'] as Map<String, dynamic>?,
  validFrom: json['validFrom'] == null
      ? null
      : DateTime.parse(json['validFrom'] as String),
  validUntil: json['validUntil'] == null
      ? null
      : DateTime.parse(json['validUntil'] as String),
);

Map<String, dynamic> _$PaymentPlanToJson(_PaymentPlan instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'price': instance.price,
      'currency': instance.currency,
      'billingCycle': _$BillingCycleEnumMap[instance.billingCycle]!,
      'features': instance.features,
      'isActive': instance.isActive,
      'isPopular': instance.isPopular,
      'imageUrl': instance.imageUrl,
      'metadata': instance.metadata,
      'validFrom': instance.validFrom?.toIso8601String(),
      'validUntil': instance.validUntil?.toIso8601String(),
    };

_Invoice _$InvoiceFromJson(Map<String, dynamic> json) => _Invoice(
  id: json['id'] as String,
  userId: json['userId'] as String,
  subscriptionId: json['subscriptionId'] as String,
  amount: (json['amount'] as num).toDouble(),
  currency: json['currency'] as String,
  status: $enumDecode(_$InvoiceStatusEnumMap, json['status']),
  dueDate: DateTime.parse(json['dueDate'] as String),
  createdAt: DateTime.parse(json['createdAt'] as String),
  paidAt: json['paidAt'] == null
      ? null
      : DateTime.parse(json['paidAt'] as String),
  description: json['description'] as String?,
  invoiceUrl: json['invoiceUrl'] as String?,
  receiptUrl: json['receiptUrl'] as String?,
  items: json['items'] as Map<String, dynamic>?,
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$InvoiceToJson(_Invoice instance) => <String, dynamic>{
  'id': instance.id,
  'userId': instance.userId,
  'subscriptionId': instance.subscriptionId,
  'amount': instance.amount,
  'currency': instance.currency,
  'status': _$InvoiceStatusEnumMap[instance.status]!,
  'dueDate': instance.dueDate.toIso8601String(),
  'createdAt': instance.createdAt.toIso8601String(),
  'paidAt': instance.paidAt?.toIso8601String(),
  'description': instance.description,
  'invoiceUrl': instance.invoiceUrl,
  'receiptUrl': instance.receiptUrl,
  'items': instance.items,
  'metadata': instance.metadata,
};

const _$InvoiceStatusEnumMap = {
  InvoiceStatus.draft: 'draft',
  InvoiceStatus.sent: 'sent',
  InvoiceStatus.paid: 'paid',
  InvoiceStatus.overdue: 'overdue',
  InvoiceStatus.cancelled: 'cancelled',
  InvoiceStatus.voided: 'voided',
};

_PaymentWebhook _$PaymentWebhookFromJson(Map<String, dynamic> json) =>
    _PaymentWebhook(
      id: json['id'] as String,
      eventType: json['eventType'] as String,
      payload: json['payload'] as Map<String, dynamic>,
      receivedAt: DateTime.parse(json['receivedAt'] as String),
      isProcessed: json['isProcessed'] as bool,
      processedAt: json['processedAt'] == null
          ? null
          : DateTime.parse(json['processedAt'] as String),
      errorMessage: json['errorMessage'] as String?,
    );

Map<String, dynamic> _$PaymentWebhookToJson(_PaymentWebhook instance) =>
    <String, dynamic>{
      'id': instance.id,
      'eventType': instance.eventType,
      'payload': instance.payload,
      'receivedAt': instance.receivedAt.toIso8601String(),
      'isProcessed': instance.isProcessed,
      'processedAt': instance.processedAt?.toIso8601String(),
      'errorMessage': instance.errorMessage,
    };

_PaymentRefund _$PaymentRefundFromJson(Map<String, dynamic> json) =>
    _PaymentRefund(
      id: json['id'] as String,
      transactionId: json['transactionId'] as String,
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] as String,
      status: $enumDecode(_$RefundStatusEnumMap, json['status']),
      createdAt: DateTime.parse(json['createdAt'] as String),
      processedAt: json['processedAt'] == null
          ? null
          : DateTime.parse(json['processedAt'] as String),
      reason: json['reason'] as String?,
      externalRefundId: json['externalRefundId'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$PaymentRefundToJson(_PaymentRefund instance) =>
    <String, dynamic>{
      'id': instance.id,
      'transactionId': instance.transactionId,
      'amount': instance.amount,
      'currency': instance.currency,
      'status': _$RefundStatusEnumMap[instance.status]!,
      'createdAt': instance.createdAt.toIso8601String(),
      'processedAt': instance.processedAt?.toIso8601String(),
      'reason': instance.reason,
      'externalRefundId': instance.externalRefundId,
      'metadata': instance.metadata,
    };

const _$RefundStatusEnumMap = {
  RefundStatus.pending: 'pending',
  RefundStatus.processing: 'processing',
  RefundStatus.completed: 'completed',
  RefundStatus.failed: 'failed',
  RefundStatus.cancelled: 'cancelled',
};

_PaymentRequest _$PaymentRequestFromJson(Map<String, dynamic> json) =>
    _PaymentRequest(
      id: json['id'] as String,
      requesterId: json['requesterId'] as String,
      recipientId: json['recipientId'] as String,
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] as String,
      description: json['description'] as String,
      status: $enumDecode(_$PaymentRequestStatusEnumMap, json['status']),
      createdAt: DateTime.parse(json['createdAt'] as String),
      dueDate: json['dueDate'] == null
          ? null
          : DateTime.parse(json['dueDate'] as String),
      paidAt: json['paidAt'] == null
          ? null
          : DateTime.parse(json['paidAt'] as String),
      paymentMethodId: json['paymentMethodId'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$PaymentRequestToJson(_PaymentRequest instance) =>
    <String, dynamic>{
      'id': instance.id,
      'requesterId': instance.requesterId,
      'recipientId': instance.recipientId,
      'amount': instance.amount,
      'currency': instance.currency,
      'description': instance.description,
      'status': _$PaymentRequestStatusEnumMap[instance.status]!,
      'createdAt': instance.createdAt.toIso8601String(),
      'dueDate': instance.dueDate?.toIso8601String(),
      'paidAt': instance.paidAt?.toIso8601String(),
      'paymentMethodId': instance.paymentMethodId,
      'metadata': instance.metadata,
    };

const _$PaymentRequestStatusEnumMap = {
  PaymentRequestStatus.pending: 'pending',
  PaymentRequestStatus.paid: 'paid',
  PaymentRequestStatus.cancelled: 'cancelled',
  PaymentRequestStatus.expired: 'expired',
};

_SplitPayment _$SplitPaymentFromJson(Map<String, dynamic> json) =>
    _SplitPayment(
      id: json['id'] as String,
      organizerId: json['organizerId'] as String,
      totalAmount: (json['totalAmount'] as num).toDouble(),
      currency: json['currency'] as String,
      status: $enumDecode(_$SplitPaymentStatusEnumMap, json['status']),
      members: (json['members'] as List<dynamic>)
          .map((e) => SplitPaymentMember.fromJson(e as Map<String, dynamic>))
          .toList(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
      description: json['description'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$SplitPaymentToJson(_SplitPayment instance) =>
    <String, dynamic>{
      'id': instance.id,
      'organizerId': instance.organizerId,
      'totalAmount': instance.totalAmount,
      'currency': instance.currency,
      'status': _$SplitPaymentStatusEnumMap[instance.status]!,
      'members': instance.members,
      'createdAt': instance.createdAt.toIso8601String(),
      'completedAt': instance.completedAt?.toIso8601String(),
      'description': instance.description,
      'metadata': instance.metadata,
    };

const _$SplitPaymentStatusEnumMap = {
  SplitPaymentStatus.pending: 'pending',
  SplitPaymentStatus.partial: 'partial',
  SplitPaymentStatus.completed: 'completed',
  SplitPaymentStatus.cancelled: 'cancelled',
};

_SplitPaymentMember _$SplitPaymentMemberFromJson(Map<String, dynamic> json) =>
    _SplitPaymentMember(
      userId: json['userId'] as String,
      amount: (json['amount'] as num).toDouble(),
      status: $enumDecode(_$SplitPaymentMemberStatusEnumMap, json['status']),
      paidAt: json['paidAt'] == null
          ? null
          : DateTime.parse(json['paidAt'] as String),
      paymentMethodId: json['paymentMethodId'] as String?,
    );

Map<String, dynamic> _$SplitPaymentMemberToJson(_SplitPaymentMember instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'amount': instance.amount,
      'status': _$SplitPaymentMemberStatusEnumMap[instance.status]!,
      'paidAt': instance.paidAt?.toIso8601String(),
      'paymentMethodId': instance.paymentMethodId,
    };

const _$SplitPaymentMemberStatusEnumMap = {
  SplitPaymentMemberStatus.pending: 'pending',
  SplitPaymentMemberStatus.paid: 'paid',
  SplitPaymentMemberStatus.declined: 'declined',
};
