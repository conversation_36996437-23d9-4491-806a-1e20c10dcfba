import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/payment/models/payment_models.dart';
import 'package:billionaires_social/features/payment/services/payment_service.dart';

// Service Provider
final paymentServiceProvider = Provider<PaymentService>((ref) {
  return PaymentService();
});

// Payment Methods Providers
final userPaymentMethodsProvider =
    StreamProvider.family<List<PaymentMethod>, String>((ref, userId) {
      final service = ref.watch(paymentServiceProvider);
      return service.getUserPaymentMethods(userId);
    });

final paymentMethodProvider = FutureProvider.family<PaymentMethod?, String>((
  ref,
  paymentMethodId,
) async {
  final service = ref.watch(paymentServiceProvider);
  return service.getPaymentMethod(paymentMethodId);
});

// Transaction Providers
final userTransactionsProvider =
    StreamProvider.family<List<PaymentTransaction>, String>((ref, userId) {
      final service = ref.watch(paymentServiceProvider);
      return service.getUserTransactions(userId);
    });

final transactionProvider = FutureProvider.family<PaymentTransaction?, String>((
  ref,
  transactionId,
) async {
  final service = ref.watch(paymentServiceProvider);
  return service.getTransaction(transactionId);
});

// Subscription Providers
final userSubscriptionsProvider =
    StreamProvider.family<List<Subscription>, String>((ref, userId) {
      final service = ref.watch(paymentServiceProvider);
      return service.getUserSubscriptions(userId);
    });

final subscriptionProvider = FutureProvider.family<Subscription?, String>((
  ref,
  subscriptionId,
) async {
  final service = ref.watch(paymentServiceProvider);
  return service.getSubscription(subscriptionId);
});

// Payment Plans Provider
final availablePlansProvider = StreamProvider<List<PaymentPlan>>((ref) {
  final service = ref.watch(paymentServiceProvider);
  return service.getAvailablePlans();
});

final planProvider = FutureProvider.family<PaymentPlan?, String>((
  ref,
  planId,
) async {
  final service = ref.watch(paymentServiceProvider);
  return service.getPlan(planId);
});

// Invoice Providers
final userInvoicesProvider = StreamProvider.family<List<Invoice>, String>((
  ref,
  userId,
) {
  final service = ref.watch(paymentServiceProvider);
  return service.getUserInvoices(userId);
});

// Payment Request Providers
final incomingPaymentRequestsProvider =
    StreamProvider.family<List<PaymentRequest>, String>((ref, userId) {
      final service = ref.watch(paymentServiceProvider);
      return service.getIncomingPaymentRequests(userId);
    });

final outgoingPaymentRequestsProvider =
    StreamProvider.family<List<PaymentRequest>, String>((ref, userId) {
      final service = ref.watch(paymentServiceProvider);
      return service.getOutgoingPaymentRequests(userId);
    });

// Split Payment Providers
final userSplitPaymentsProvider =
    StreamProvider.family<List<SplitPayment>, String>((ref, userId) {
      final service = ref.watch(paymentServiceProvider);
      return service.getUserSplitPayments(userId);
    });

// Analytics Provider
final paymentAnalyticsProvider =
    FutureProvider.family<Map<String, dynamic>, String>((ref, userId) async {
      final service = ref.watch(paymentServiceProvider);
      return service.getPaymentAnalytics(userId);
    });

// Notifier Providers
final paymentNotifierProvider =
    StateNotifierProvider<PaymentNotifier, PaymentState>((ref) {
      final service = ref.watch(paymentServiceProvider);
      return PaymentNotifier(service);
    });

final transactionNotifierProvider =
    StateNotifierProvider<TransactionNotifier, TransactionState>((ref) {
      final service = ref.watch(paymentServiceProvider);
      return TransactionNotifier(service);
    });

final subscriptionNotifierProvider =
    StateNotifierProvider<SubscriptionNotifier, SubscriptionState>((ref) {
      final service = ref.watch(paymentServiceProvider);
      return SubscriptionNotifier(service);
    });

final paymentRequestNotifierProvider =
    StateNotifierProvider<PaymentRequestNotifier, PaymentRequestState>((ref) {
      final service = ref.watch(paymentServiceProvider);
      return PaymentRequestNotifier(service);
    });

// State Classes
class PaymentState {
  final bool isLoading;
  final String? error;
  final List<PaymentMethod> paymentMethods;
  final PaymentMethod? defaultMethod;

  const PaymentState({
    this.isLoading = false,
    this.error,
    this.paymentMethods = const [],
    this.defaultMethod,
  });

  PaymentState copyWith({
    bool? isLoading,
    String? error,
    List<PaymentMethod>? paymentMethods,
    PaymentMethod? defaultMethod,
  }) {
    return PaymentState(
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      paymentMethods: paymentMethods ?? this.paymentMethods,
      defaultMethod: defaultMethod ?? this.defaultMethod,
    );
  }
}

class TransactionState {
  final bool isLoading;
  final String? error;
  final bool isProcessing;
  final PaymentTransaction? lastTransaction;

  const TransactionState({
    this.isLoading = false,
    this.error,
    this.isProcessing = false,
    this.lastTransaction,
  });

  TransactionState copyWith({
    bool? isLoading,
    String? error,
    bool? isProcessing,
    PaymentTransaction? lastTransaction,
  }) {
    return TransactionState(
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      isProcessing: isProcessing ?? this.isProcessing,
      lastTransaction: lastTransaction ?? this.lastTransaction,
    );
  }
}

class SubscriptionState {
  final bool isLoading;
  final String? error;
  final bool isProcessing;
  final Subscription? currentSubscription;

  const SubscriptionState({
    this.isLoading = false,
    this.error,
    this.isProcessing = false,
    this.currentSubscription,
  });

  SubscriptionState copyWith({
    bool? isLoading,
    String? error,
    bool? isProcessing,
    Subscription? currentSubscription,
  }) {
    return SubscriptionState(
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      isProcessing: isProcessing ?? this.isProcessing,
      currentSubscription: currentSubscription ?? this.currentSubscription,
    );
  }
}

class PaymentRequestState {
  final bool isLoading;
  final String? error;
  final bool isSending;
  final PaymentRequest? lastRequest;

  const PaymentRequestState({
    this.isLoading = false,
    this.error,
    this.isSending = false,
    this.lastRequest,
  });

  PaymentRequestState copyWith({
    bool? isLoading,
    String? error,
    bool? isSending,
    PaymentRequest? lastRequest,
  }) {
    return PaymentRequestState(
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      isSending: isSending ?? this.isSending,
      lastRequest: lastRequest ?? this.lastRequest,
    );
  }
}

// Notifier Classes
class PaymentNotifier extends StateNotifier<PaymentState> {
  final PaymentService _service;

  PaymentNotifier(this._service) : super(const PaymentState());

  Future<void> addPaymentMethod(PaymentMethod paymentMethod) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      await _service.addPaymentMethod(paymentMethod);
      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  Future<void> updatePaymentMethod(PaymentMethod paymentMethod) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      await _service.updatePaymentMethod(paymentMethod);
      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  Future<void> deletePaymentMethod(String paymentMethodId) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      await _service.deletePaymentMethod(paymentMethodId);
      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  Future<void> setDefaultPaymentMethod(
    String userId,
    String paymentMethodId,
  ) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      await _service.setDefaultPaymentMethod(userId, paymentMethodId);
      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }
}

class TransactionNotifier extends StateNotifier<TransactionState> {
  final PaymentService _service;

  TransactionNotifier(this._service) : super(const TransactionState());

  Future<void> processPayment({
    required String userId,
    required String merchantId,
    required double amount,
    required String currency,
    required PaymentType type,
    String? description,
    String? paymentMethodId,
  }) async {
    try {
      state = state.copyWith(isProcessing: true, error: null);

      final transactionId = _service.generateTransactionId();
      final transaction = PaymentTransaction(
        id: transactionId,
        userId: userId,
        merchantId: merchantId,
        amount: amount,
        currency: currency,
        status: PaymentStatus.pending,
        type: type,
        createdAt: DateTime.now(),
        description: description,
      );

      await _service.createTransaction(transaction);

      try {
        // Simulate payment processing
        await Future.delayed(const Duration(seconds: 2));

        // Update transaction status to completed
        await _service.updateTransactionStatus(
          transactionId,
          PaymentStatus.completed,
        );

        state = state.copyWith(
          isProcessing: false,
          lastTransaction: transaction.copyWith(
            status: PaymentStatus.completed,
          ),
        );
      } catch (paymentError) {
        // Payment failed - update transaction status and rollback
        await _service.updateTransactionStatus(
          transactionId,
          PaymentStatus.failed,
          failureReason: paymentError.toString(),
        );

        // Re-throw to trigger outer catch block
        rethrow;
      }
    } catch (e) {
      state = state.copyWith(isProcessing: false, error: e.toString());
    }
  }

  Future<void> refundTransaction({
    required String transactionId,
    required double amount,
    required String reason,
  }) async {
    try {
      state = state.copyWith(isProcessing: true, error: null);

      final refund = PaymentRefund(
        id: 'ref_${DateTime.now().millisecondsSinceEpoch}',
        transactionId: transactionId,
        amount: amount,
        currency: 'USD', // This should come from the original transaction
        status: RefundStatus.pending,
        createdAt: DateTime.now(),
        reason: reason,
      );

      await _service.createRefund(refund);

      // Update original transaction status
      await _service.updateTransactionStatus(
        transactionId,
        PaymentStatus.refunded,
      );

      state = state.copyWith(isProcessing: false);
    } catch (e) {
      state = state.copyWith(isProcessing: false, error: e.toString());
    }
  }
}

class SubscriptionNotifier extends StateNotifier<SubscriptionState> {
  final PaymentService _service;

  SubscriptionNotifier(this._service) : super(const SubscriptionState());

  Future<void> createSubscription({
    required String userId,
    required String planId,
    required double amount,
    required String currency,
    required BillingCycle billingCycle,
  }) async {
    try {
      state = state.copyWith(isProcessing: true, error: null);

      final subscriptionId = _service.generateSubscriptionId();
      final now = DateTime.now();

      final subscription = Subscription(
        id: subscriptionId,
        userId: userId,
        planId: planId,
        status: SubscriptionStatus.active,
        startDate: now,
        endDate: _calculateEndDate(now, billingCycle),
        amount: amount,
        currency: currency,
        billingCycle: billingCycle,
        autoRenew: true,
        nextBillingDate: _calculateNextBillingDate(now, billingCycle),
      );

      await _service.createSubscription(subscription);

      state = state.copyWith(
        isProcessing: false,
        currentSubscription: subscription,
      );
    } catch (e) {
      state = state.copyWith(isProcessing: false, error: e.toString());
    }
  }

  Future<void> cancelSubscription(String subscriptionId, String reason) async {
    try {
      state = state.copyWith(isProcessing: true, error: null);
      await _service.cancelSubscription(subscriptionId, reason);
      state = state.copyWith(isProcessing: false);
    } catch (e) {
      state = state.copyWith(isProcessing: false, error: e.toString());
    }
  }

  DateTime _calculateEndDate(DateTime startDate, BillingCycle cycle) {
    switch (cycle) {
      case BillingCycle.monthly:
        return DateTime(startDate.year, startDate.month + 1, startDate.day);
      case BillingCycle.quarterly:
        return DateTime(startDate.year, startDate.month + 3, startDate.day);
      case BillingCycle.yearly:
        return DateTime(startDate.year + 1, startDate.month, startDate.day);
      case BillingCycle.weekly:
        return startDate.add(const Duration(days: 7));
      case BillingCycle.daily:
        return startDate.add(const Duration(days: 1));
    }
  }

  DateTime _calculateNextBillingDate(DateTime startDate, BillingCycle cycle) {
    return _calculateEndDate(startDate, cycle);
  }
}

class PaymentRequestNotifier extends StateNotifier<PaymentRequestState> {
  final PaymentService _service;

  PaymentRequestNotifier(this._service) : super(const PaymentRequestState());

  Future<void> sendPaymentRequest({
    required String requesterId,
    required String recipientId,
    required double amount,
    required String currency,
    required String description,
    DateTime? dueDate,
  }) async {
    try {
      state = state.copyWith(isSending: true, error: null);

      final request = PaymentRequest(
        id: 'req_${DateTime.now().millisecondsSinceEpoch}',
        requesterId: requesterId,
        recipientId: recipientId,
        amount: amount,
        currency: currency,
        description: description,
        status: PaymentRequestStatus.pending,
        createdAt: DateTime.now(),
        dueDate: dueDate,
      );

      await _service.createPaymentRequest(request);

      state = state.copyWith(isSending: false, lastRequest: request);
    } catch (e) {
      state = state.copyWith(isSending: false, error: e.toString());
    }
  }

  Future<void> payRequest(String requestId) async {
    try {
      state = state.copyWith(isSending: true, error: null);
      await _service.updatePaymentRequestStatus(
        requestId,
        PaymentRequestStatus.paid,
      );
      state = state.copyWith(isSending: false);
    } catch (e) {
      state = state.copyWith(isSending: false, error: e.toString());
    }
  }

  Future<void> cancelRequest(String requestId) async {
    try {
      state = state.copyWith(isSending: true, error: null);
      await _service.updatePaymentRequestStatus(
        requestId,
        PaymentRequestStatus.cancelled,
      );
      state = state.copyWith(isSending: false);
    } catch (e) {
      state = state.copyWith(isSending: false, error: e.toString());
    }
  }
}
