import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/core/app_theme.dart';

class PaymentMainScreen extends ConsumerStatefulWidget {
  const PaymentMainScreen({super.key});

  @override
  ConsumerState<PaymentMainScreen> createState() => _PaymentMainScreenState();
}

class _PaymentMainScreenState extends ConsumerState<PaymentMainScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.primaryColor,
      appBar: AppBar(
        title: const Text('Payments'),
        backgroundColor: AppTheme.primaryColor,
        elevation: 0,
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.add, color: AppTheme.accentColor),
            onPressed: () => _showAddPaymentMethodDialog(context),
          ),
        ],
      ),
      body: Column(
        children: [
          _buildQuickStats(),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: AppTheme.luxuryGrey,
              borderRadius: BorderRadius.circular(12),
            ),
            child: TabBar(
              controller: _tabController,
              indicator: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: AppTheme.accentColor,
              ),
              labelColor: Colors.white,
              unselectedLabelColor: AppTheme.secondaryAccentColor.withValues(alpha: 
                0.6,
              ),
              labelStyle: AppTheme.fontStyles.body.copyWith(
                fontWeight: FontWeight.w600,
              ),
              tabs: const [
                Tab(text: 'Methods'),
                Tab(text: 'Transactions'),
                Tab(text: 'Requests'),
                Tab(text: 'Subscriptions'),
              ],
            ),
          ),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildPaymentMethodsTab(),
                _buildTransactionsTab(),
                _buildPaymentRequestsTab(),
                _buildSubscriptionsTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showSendMoneyDialog(context),
        backgroundColor: AppTheme.accentColor,
        child: const Icon(Icons.send, color: AppTheme.luxuryBlack),
      ),
    );
  }

  Widget _buildQuickStats() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppTheme.accentColor, AppTheme.accentColor.withValues(alpha: 0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem('Balance', '\$2,450', Icons.account_balance_wallet),
          _buildStatItem('This Month', '\$1,200', Icons.trending_up),
          _buildStatItem('Pending', '\$350', Icons.pending),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.white, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: AppTheme.fontStyles.title.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: AppTheme.fontStyles.caption.copyWith(
            color: Colors.white.withValues(alpha: 0.9),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildPaymentMethodsTab() {
    return _buildEmptyState(
      'No Payment Methods',
      'Add your first payment method to start making transactions.',
      Icons.credit_card_outlined,
      actionText: 'Add Payment Method',
      onAction: () => _showAddPaymentMethodDialog(context),
    );
  }

  Widget _buildTransactionsTab() {
    return _buildEmptyState(
      'No Transactions',
      'Your payment history will appear here.',
      Icons.receipt_long_outlined,
    );
  }

  Widget _buildPaymentRequestsTab() {
    return _buildEmptyState(
      'No Payment Requests',
      'Incoming and outgoing payment requests will appear here.',
      Icons.request_page_outlined,
    );
  }

  Widget _buildSubscriptionsTab() {
    return _buildEmptyState(
      'No Subscriptions',
      'Manage your active subscriptions and billing.',
      Icons.subscriptions_outlined,
    );
  }

  Widget _buildEmptyState(
    String title,
    String message,
    IconData icon, {
    String? actionText,
    VoidCallback? onAction,
  }) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: AppTheme.secondaryAccentColor.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: AppTheme.fontStyles.title.copyWith(
              color: AppTheme.luxuryWhite,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: AppTheme.fontStyles.body.copyWith(
              color: AppTheme.secondaryAccentColor.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
          if (actionText != null && onAction != null) ...[
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: onAction,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.accentColor,
                foregroundColor: AppTheme.luxuryBlack,
              ),
              child: Text(actionText),
            ),
          ],
        ],
      ),
    );
  }

  void _showAddPaymentMethodDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.luxuryGrey,
        title: Text(
          'Add Payment Method',
          style: AppTheme.fontStyles.title.copyWith(
            color: AppTheme.luxuryWhite,
          ),
        ),
        content: const Text(
          'Payment method integration coming soon!',
          style: TextStyle(color: AppTheme.secondaryAccentColor),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showSendMoneyDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.luxuryGrey,
        title: Text(
          'Send Money',
          style: AppTheme.fontStyles.title.copyWith(
            color: AppTheme.luxuryWhite,
          ),
        ),
        content: const Text(
          'Money transfer feature coming soon!',
          style: TextStyle(color: AppTheme.secondaryAccentColor),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
