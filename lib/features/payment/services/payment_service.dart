import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:billionaires_social/features/payment/models/payment_models.dart';

class PaymentService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Payment Methods
  Stream<List<PaymentMethod>> getUserPaymentMethods(String userId) {
    return _firestore
        .collection('payment_methods')
        .where('userId', isEqualTo: userId)
        .where('isEnabled', isEqualTo: true)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map((doc) => PaymentMethod.fromJson(doc.data()))
              .toList(),
        );
  }

  Future<PaymentMethod?> getPaymentMethod(String paymentMethodId) async {
    final doc = await _firestore
        .collection('payment_methods')
        .doc(paymentMethodId)
        .get();

    if (doc.exists) {
      return PaymentMethod.fromJson(doc.data()!);
    }
    return null;
  }

  Future<void> addPaymentMethod(PaymentMethod paymentMethod) async {
    await _firestore
        .collection('payment_methods')
        .doc(paymentMethod.id)
        .set(paymentMethod.toJson());
  }

  Future<void> updatePaymentMethod(PaymentMethod paymentMethod) async {
    await _firestore
        .collection('payment_methods')
        .doc(paymentMethod.id)
        .update(paymentMethod.toJson());
  }

  Future<void> deletePaymentMethod(String paymentMethodId) async {
    await _firestore
        .collection('payment_methods')
        .doc(paymentMethodId)
        .delete();
  }

  Future<void> setDefaultPaymentMethod(
    String userId,
    String paymentMethodId,
  ) async {
    // Remove default from all other payment methods
    final batch = _firestore.batch();

    final methods = await _firestore
        .collection('payment_methods')
        .where('userId', isEqualTo: userId)
        .where('isDefault', isEqualTo: true)
        .get();

    for (final doc in methods.docs) {
      batch.update(doc.reference, {'isDefault': false});
    }

    // Set the new default
    batch.update(
      _firestore.collection('payment_methods').doc(paymentMethodId),
      {'isDefault': true},
    );

    await batch.commit();
  }

  // Transactions
  Stream<List<PaymentTransaction>> getUserTransactions(String userId) {
    return _firestore
        .collection('transactions')
        .where('userId', isEqualTo: userId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map((doc) => PaymentTransaction.fromJson(doc.data()))
              .toList(),
        );
  }

  Future<PaymentTransaction?> getTransaction(String transactionId) async {
    final doc = await _firestore
        .collection('transactions')
        .doc(transactionId)
        .get();

    if (doc.exists) {
      return PaymentTransaction.fromJson(doc.data()!);
    }
    return null;
  }

  Future<PaymentTransaction> createTransaction(
    PaymentTransaction transaction,
  ) async {
    await _firestore
        .collection('transactions')
        .doc(transaction.id)
        .set(transaction.toJson());

    return transaction;
  }

  Future<void> updateTransactionStatus(
    String transactionId,
    PaymentStatus status, {
    String? failureReason,
  }) async {
    final updates = <String, dynamic>{'status': status.name};

    if (status == PaymentStatus.completed) {
      updates['processedAt'] = FieldValue.serverTimestamp();
    } else if (status == PaymentStatus.failed) {
      updates['failedAt'] = FieldValue.serverTimestamp();
      if (failureReason != null) {
        updates['failureReason'] = failureReason;
      }
    }

    await _firestore
        .collection('transactions')
        .doc(transactionId)
        .update(updates);
  }

  // Subscriptions
  Stream<List<Subscription>> getUserSubscriptions(String userId) {
    return _firestore
        .collection('subscriptions')
        .where('userId', isEqualTo: userId)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map((doc) => Subscription.fromJson(doc.data()))
              .toList(),
        );
  }

  Future<Subscription?> getSubscription(String subscriptionId) async {
    final doc = await _firestore
        .collection('subscriptions')
        .doc(subscriptionId)
        .get();

    if (doc.exists) {
      return Subscription.fromJson(doc.data()!);
    }
    return null;
  }

  Future<Subscription> createSubscription(Subscription subscription) async {
    await _firestore
        .collection('subscriptions')
        .doc(subscription.id)
        .set(subscription.toJson());

    return subscription;
  }

  Future<void> updateSubscription(Subscription subscription) async {
    await _firestore
        .collection('subscriptions')
        .doc(subscription.id)
        .update(subscription.toJson());
  }

  Future<void> cancelSubscription(String subscriptionId, String reason) async {
    await _firestore.collection('subscriptions').doc(subscriptionId).update({
      'status': SubscriptionStatus.cancelled.name,
      'cancelledAt': FieldValue.serverTimestamp(),
      'cancellationReason': reason,
    });
  }

  // Payment Plans
  Stream<List<PaymentPlan>> getAvailablePlans() {
    return _firestore
        .collection('payment_plans')
        .where('isActive', isEqualTo: true)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map((doc) => PaymentPlan.fromJson(doc.data()))
              .toList(),
        );
  }

  Future<PaymentPlan?> getPlan(String planId) async {
    final doc = await _firestore.collection('payment_plans').doc(planId).get();

    if (doc.exists) {
      return PaymentPlan.fromJson(doc.data()!);
    }
    return null;
  }

  // Invoices
  Stream<List<Invoice>> getUserInvoices(String userId) {
    return _firestore
        .collection('invoices')
        .where('userId', isEqualTo: userId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs.map((doc) => Invoice.fromJson(doc.data())).toList(),
        );
  }

  Future<Invoice> createInvoice(Invoice invoice) async {
    await _firestore
        .collection('invoices')
        .doc(invoice.id)
        .set(invoice.toJson());

    return invoice;
  }

  Future<void> updateInvoiceStatus(
    String invoiceId,
    InvoiceStatus status,
  ) async {
    final updates = <String, dynamic>{'status': status.name};

    if (status == InvoiceStatus.paid) {
      updates['paidAt'] = FieldValue.serverTimestamp();
    }

    await _firestore.collection('invoices').doc(invoiceId).update(updates);
  }

  // Payment Requests
  Stream<List<PaymentRequest>> getIncomingPaymentRequests(String userId) {
    return _firestore
        .collection('payment_requests')
        .where('recipientId', isEqualTo: userId)
        .where('status', isEqualTo: PaymentRequestStatus.pending.name)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map((doc) => PaymentRequest.fromJson(doc.data()))
              .toList(),
        );
  }

  Stream<List<PaymentRequest>> getOutgoingPaymentRequests(String userId) {
    return _firestore
        .collection('payment_requests')
        .where('requesterId', isEqualTo: userId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map((doc) => PaymentRequest.fromJson(doc.data()))
              .toList(),
        );
  }

  Future<PaymentRequest> createPaymentRequest(PaymentRequest request) async {
    await _firestore
        .collection('payment_requests')
        .doc(request.id)
        .set(request.toJson());

    return request;
  }

  Future<void> updatePaymentRequestStatus(
    String requestId,
    PaymentRequestStatus status,
  ) async {
    final updates = <String, dynamic>{'status': status.name};

    if (status == PaymentRequestStatus.paid) {
      updates['paidAt'] = FieldValue.serverTimestamp();
    }

    await _firestore
        .collection('payment_requests')
        .doc(requestId)
        .update(updates);
  }

  // Split Payments
  Stream<List<SplitPayment>> getUserSplitPayments(String userId) {
    return _firestore
        .collection('split_payments')
        .where('members', arrayContains: userId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map((doc) => SplitPayment.fromJson(doc.data()))
              .toList(),
        );
  }

  Future<SplitPayment> createSplitPayment(SplitPayment splitPayment) async {
    await _firestore
        .collection('split_payments')
        .doc(splitPayment.id)
        .set(splitPayment.toJson());

    return splitPayment;
  }

  Future<void> updateSplitPaymentMemberStatus(
    String splitPaymentId,
    String userId,
    SplitPaymentMemberStatus status,
  ) async {
    final doc = await _firestore
        .collection('split_payments')
        .doc(splitPaymentId)
        .get();

    if (doc.exists) {
      final splitPayment = SplitPayment.fromJson(doc.data()!);
      final updatedMembers = splitPayment.members.map((member) {
        if (member.userId == userId) {
          return member.copyWith(
            status: status,
            paidAt: status == SplitPaymentMemberStatus.paid
                ? DateTime.now()
                : member.paidAt,
          );
        }
        return member;
      }).toList();

      final allPaid = updatedMembers.every(
        (member) => member.status == SplitPaymentMemberStatus.paid,
      );

      final newStatus = allPaid
          ? SplitPaymentStatus.completed
          : SplitPaymentStatus.partial;

      await _firestore.collection('split_payments').doc(splitPaymentId).update({
        'members': updatedMembers.map((m) => m.toJson()).toList(),
        'status': newStatus.name,
        if (allPaid) 'completedAt': FieldValue.serverTimestamp(),
      });
    }
  }

  // Refunds
  Future<PaymentRefund> createRefund(PaymentRefund refund) async {
    await _firestore.collection('refunds').doc(refund.id).set(refund.toJson());

    return refund;
  }

  Future<void> updateRefundStatus(String refundId, RefundStatus status) async {
    final updates = <String, dynamic>{'status': status.name};

    if (status == RefundStatus.completed) {
      updates['processedAt'] = FieldValue.serverTimestamp();
    }

    await _firestore.collection('refunds').doc(refundId).update(updates);
  }

  // Webhooks
  Future<void> processWebhook(PaymentWebhook webhook) async {
    await _firestore
        .collection('webhooks')
        .doc(webhook.id)
        .set(webhook.toJson());
  }

  Future<void> markWebhookProcessed(String webhookId) async {
    await _firestore.collection('webhooks').doc(webhookId).update({
      'isProcessed': true,
      'processedAt': FieldValue.serverTimestamp(),
    });
  }

  // Analytics and Reporting
  Future<Map<String, dynamic>> getPaymentAnalytics(String userId) async {
    final transactions = await _firestore
        .collection('transactions')
        .where('userId', isEqualTo: userId)
        .where('status', isEqualTo: PaymentStatus.completed.name)
        .get();

    final totalAmount = transactions.docs.fold<double>(
      0,
      (total, doc) => total + (doc.data()['amount'] as double),
    );

    final monthlyTransactions = transactions.docs.where((doc) {
      final createdAt = (doc.data()['createdAt'] as Timestamp).toDate();
      final now = DateTime.now();
      return createdAt.year == now.year && createdAt.month == now.month;
    }).length;

    return {
      'totalAmount': totalAmount,
      'totalTransactions': transactions.docs.length,
      'monthlyTransactions': monthlyTransactions,
    };
  }

  // Utility Methods
  String generateTransactionId() {
    return 'txn_${DateTime.now().millisecondsSinceEpoch}_${(1000 + (DateTime.now().millisecondsSinceEpoch % 9000))}';
  }

  String generateInvoiceId() {
    return 'inv_${DateTime.now().millisecondsSinceEpoch}_${(1000 + (DateTime.now().millisecondsSinceEpoch % 9000))}';
  }

  String generateSubscriptionId() {
    return 'sub_${DateTime.now().millisecondsSinceEpoch}_${(1000 + (DateTime.now().millisecondsSinceEpoch % 9000))}';
  }
}
