import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class MembershipPlansScreen extends StatelessWidget {
  const MembershipPlansScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final plans = [
      MembershipPlan(
        name: 'Elite',
        price: 4999,
        description:
            'Access to exclusive events, private networking, and luxury concierge services.',
        benefits: [
          'VIP event invitations',
          'Personal concierge',
          'Priority support',
          'Private chat with billionaires',
        ],
        icon: FontAwesomeIcons.crown,
        color: Colors.amber[700]!,
      ),
      MembershipPlan(
        name: 'Platinum',
        price: 1999,
        description:
            'Premium networking, early access to investment opportunities, and more.',
        benefits: [
          'Premium networking',
          'Early investment access',
          'Exclusive content',
        ],
        icon: FontAwesomeIcons.gem,
        color: Colors.blueAccent,
      ),
      MembershipPlan(
        name: 'Gold',
        price: 499,
        description:
            'Essential features for luxury socializing and business growth.',
        benefits: ['Business growth tools', 'Luxury social features'],
        icon: FontAwesomeIcons.medal,
        color: Colors.deepOrangeAccent,
      ),
    ];

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        title: const Text(
          'Membership Plans',
          style: TextStyle(color: Colors.white),
        ),
      ),
      body: ListView.separated(
        padding: const EdgeInsets.all(24),
        itemCount: plans.length,
        separatorBuilder: (_, _) => const SizedBox(height: 32),
        itemBuilder: (context, index) {
          final plan = plans[index];
          return _buildPlanCard(context, plan);
        },
      ),
    );
  }

  Widget _buildPlanCard(BuildContext context, MembershipPlan plan) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(24),
        border: Border.all(color: plan.color.withValues(alpha: 0.3), width: 2),
        boxShadow: [
          BoxShadow(
            color: plan.color.withValues(alpha: 0.08),
            blurRadius: 16,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: plan.color.withValues(alpha: 0.15),
                  shape: BoxShape.circle,
                ),
                child: Icon(plan.icon, color: plan.color, size: 32),
              ),
              const SizedBox(width: 20),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    plan.name,
                    style: TextStyle(
                      color: plan.color,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '\$${plan.price.toStringAsFixed(0)}/year',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 20),
          Text(
            plan.description,
            style: TextStyle(color: Colors.grey[300], fontSize: 16),
          ),
          const SizedBox(height: 16),
          ...plan.benefits.map(
            (b) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: plan.color, size: 18),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Text(
                      b,
                      style: const TextStyle(color: Colors.white, fontSize: 15),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: plan.color,
                foregroundColor: Colors.black,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
                textStyle: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
              onPressed: () {},
              child: const Text('Subscribe'),
            ),
          ),
        ],
      ),
    );
  }
}

class MembershipPlan {
  final String name;
  final double price;
  final String description;
  final List<String> benefits;
  final IconData icon;
  final Color color;

  const MembershipPlan({
    required this.name,
    required this.price,
    required this.description,
    required this.benefits,
    required this.icon,
    required this.color,
  });
}
