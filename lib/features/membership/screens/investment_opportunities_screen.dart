import 'package:flutter/material.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/features/explore/widgets/feature_list_widget.dart';

class InvestmentOpportunitiesScreen extends StatelessWidget {
  const InvestmentOpportunitiesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final investmentItems = [
      FeatureListItem(
        title: 'Private Equity Fund',
        subtitle: 'High-growth global investments',
        imageUrl:
            'https://images.unsplash.com/photo-1465101046530-73398c7f28ca',
        onTap: () {},
        icon: Icons.trending_up,
      ),
      FeatureListItem(
        title: 'Luxury Real Estate',
        subtitle: 'Prime properties in top locations',
        imageUrl:
            'https://images.unsplash.com/photo-1507089947368-19c1da9775ae',
        onTap: () {},
        icon: Icons.apartment,
      ),
      FeatureListItem(
        title: 'Venture Capital',
        subtitle: 'Early-stage tech disruptors',
        imageUrl:
            'https://images.unsplash.com/photo-1519125323398-675f0ddb6308',
        onTap: () {},
        icon: Icons.lightbulb,
      ),
    ];

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Investment Opportunities',
          style: AppTheme.fontStyles.title.copyWith(
            color: AppTheme.accentColor,
          ),
        ),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: FeatureListWidget(
          sectionTitle: 'Opportunities',
          items: investmentItems,
        ),
      ),
    );
  }
}
