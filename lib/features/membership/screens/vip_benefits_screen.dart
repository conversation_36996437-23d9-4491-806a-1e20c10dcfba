import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/core/services/analytics_service.dart';
import 'package:billionaires_social/core/app_theme.dart';

class VipBenefitsScreen extends ConsumerStatefulWidget {
  const VipBenefitsScreen({super.key});

  @override
  ConsumerState<VipBenefitsScreen> createState() => _VipBenefitsScreenState();
}

class _VipBenefitsScreenState extends ConsumerState<VipBenefitsScreen> {
  final AnalyticsService _analyticsService = getIt<AnalyticsService>();

  @override
  void initState() {
    super.initState();
    // Track screen view
    _analyticsService.logScreenView(
      screenName: 'VIP Benefits',
      screenClass: 'VipBenefitsScreen',
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('VIP Benefits'),
        backgroundColor: AppTheme.luxuryBlack,
        foregroundColor: AppTheme.primaryGold,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppTheme.luxuryBlack,
              AppTheme.luxuryGrey,
              AppTheme.luxuryBlack,
            ],
          ),
        ),
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            _buildMembershipTier(
              'Elite',
              '\$999/month',
              [
                'Exclusive networking events',
                'Priority booking for luxury experiences',
                'Personal concierge service',
                'Access to private investment opportunities',
              ],
              AppTheme.primaryGold,
              () => _handleMembershipUpgrade('elite', 999.0),
            ),
            const SizedBox(height: 16),
            _buildMembershipTier(
              'Platinum',
              '\$2,499/month',
              [
                'All Elite benefits',
                'Private jet booking discounts',
                'Yacht charter privileges',
                'Exclusive art gallery access',
                'VIP event invitations',
              ],
              Colors.blue,
              () => _handleMembershipUpgrade('platinum', 2499.0),
            ),
            const SizedBox(height: 16),
            _buildMembershipTier(
              'Diamond',
              '\$4,999/month',
              [
                'All Platinum benefits',
                'Personal investment advisor',
                'Private island access',
                'Exclusive business networking',
                'Luxury brand partnerships',
                'Philanthropy opportunities',
              ],
              Colors.purple,
              () => _handleMembershipUpgrade('diamond', 4999.0),
            ),
            const SizedBox(height: 16),
            _buildMembershipTier(
              'Black Card',
              '\$9,999/month',
              [
                'All Diamond benefits',
                'Unlimited luxury experiences',
                'Personal security detail',
                'Exclusive investment opportunities',
                'Private jet ownership options',
                'Yacht ownership opportunities',
                'Global networking access',
              ],
              Colors.black,
              () => _handleMembershipUpgrade('black_card', 9999.0),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMembershipTier(
    String name,
    String price,
    List<String> benefits,
    Color color,
    VoidCallback onUpgrade,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color, width: 2),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  name,
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  price,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: AppTheme.primaryGold,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...benefits.map(
              (benefit) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    Icon(Icons.check_circle, color: color, size: 20),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        benefit,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.luxuryWhite,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: onUpgrade,
                style: ElevatedButton.styleFrom(
                  backgroundColor: color,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  'Upgrade to $name',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _handleMembershipUpgrade(
    String membershipType,
    double price,
  ) async {
    try {
      // Track VIP membership upgrade event
      await _analyticsService.logVIPMembershipUpgrade(
        userId: 'current_user_id', // This would come from auth
        membershipType: membershipType,
        price: price,
        currency: 'USD',
      );

      // Track conversion event
      await _analyticsService.logConversion(
        conversionType: 'vip_membership_upgrade',
        value: price,
        currency: 'USD',
      );

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Successfully upgraded to $membershipType membership!',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      // Track error
      await _analyticsService.logError(
        errorType: 'membership_upgrade_failed',
        errorMessage: e.toString(),
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to upgrade membership: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
