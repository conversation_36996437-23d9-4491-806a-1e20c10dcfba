import 'package:billionaires_social/features/messaging/models/chat_settings_model.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:local_auth/local_auth.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';

class ChatSettingsService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final LocalAuthentication _localAuth = LocalAuthentication();

  Future<ChatSettings> loadGlobalSettings() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      final doc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .collection('settings')
          .doc('chat_settings')
          .get();

      if (doc.exists) {
        return ChatSettings.fromJson(doc.data()!);
      }
      return const ChatSettings();
    } catch (e) {
      throw Exception('Failed to load chat settings: ${e.toString()}');
    }
  }

  Future<void> saveGlobalSettings(ChatSettings settings) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .collection('settings')
          .doc('chat_settings')
          .set(settings.toJson());
    } catch (e) {
      throw Exception('Failed to save chat settings: ${e.toString()}');
    }
  }

  Future<IndividualChatSettings> loadIndividualSettings(String chatId) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      final doc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .collection('chat_settings')
          .doc(chatId)
          .get();

      if (doc.exists) {
        return IndividualChatSettings.fromJson(doc.data()!);
      }
      return IndividualChatSettings(chatId: chatId);
    } catch (e) {
      throw Exception(
        'Failed to load individual chat settings: ${e.toString()}',
      );
    }
  }

  Future<void> saveIndividualSettings(IndividualChatSettings settings) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .collection('chat_settings')
          .doc(settings.chatId)
          .set(settings.toJson());
    } catch (e) {
      throw Exception(
        'Failed to save individual chat settings: ${e.toString()}',
      );
    }
  }

  Future<void> clearMyMessagesInAllChats() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Get all chats where user is a participant
      final chatsSnapshot = await _firestore
          .collection('chats')
          .where('participants', arrayContains: currentUser.uid)
          .get();

      // Delete all messages sent by the current user in each chat
      for (final chatDoc in chatsSnapshot.docs) {
        final messagesSnapshot = await chatDoc.reference
            .collection('messages')
            .where('senderId', isEqualTo: currentUser.uid)
            .get();

        for (final messageDoc in messagesSnapshot.docs) {
          await messageDoc.reference.update({
            'isDeleted': true,
            'deletedAt': FieldValue.serverTimestamp(),
          });
        }
      }
    } catch (e) {
      throw Exception('Failed to clear messages: ${e.toString()}');
    }
  }

  Future<void> clearFullConversation(String chatId) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Check if user is a participant in this chat
      final chatDoc = await _firestore.collection('chats').doc(chatId).get();
      if (!chatDoc.exists) {
        throw Exception('Chat not found');
      }

      final chatData = chatDoc.data()!;
      final participants = List<String>.from(chatData['participants'] ?? []);

      if (!participants.contains(currentUser.uid)) {
        throw Exception('You are not a participant in this chat');
      }

      // Delete all messages in the chat
      final messagesSnapshot = await _firestore
          .collection('chats')
          .doc(chatId)
          .collection('messages')
          .get();

      for (final messageDoc in messagesSnapshot.docs) {
        await messageDoc.reference.delete();
      }

      // Update chat document to reflect cleared conversation
      await _firestore.collection('chats').doc(chatId).update({
        'lastMessage': '',
        'lastMessageTime': FieldValue.serverTimestamp(),
        'lastMessageStatus': 'cleared',
      });
    } catch (e) {
      throw Exception('Failed to clear conversation: ${e.toString()}');
    }
  }

  Future<void> exportChatHistory(String chatId) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Get all messages from the chat
      final messagesSnapshot = await _firestore
          .collection('chats')
          .doc(chatId)
          .collection('messages')
          .orderBy('timestamp', descending: true)
          .get();

      // Convert messages to exportable format
      final messages = messagesSnapshot.docs.map((doc) {
        final data = doc.data();
        return {
          'id': doc.id,
          'senderId': data['senderId'],
          'content': data['content'],
          'timestamp': data['timestamp'],
          'type': data['type'],
        };
      }).toList();

      // In a real app, this would generate and save a PDF file
      // For now, we'll just log the export data
      debugPrint(
        'Chat history export for chat $chatId: ${messages.length} messages',
      );

      // Store export record in Firestore
      await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .collection('exports')
          .add({
            'chatId': chatId,
            'exportedAt': FieldValue.serverTimestamp(),
            'messageCount': messages.length,
            'type': 'chat_history',
          });
    } catch (e) {
      throw Exception('Failed to export chat history: ${e.toString()}');
    }
  }

  Future<List<String>> getHiddenChats() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      final hiddenChatsSnapshot = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .collection('hidden_chats')
          .get();

      return hiddenChatsSnapshot.docs.map((doc) => doc.id).toList();
    } catch (e) {
      throw Exception('Failed to get hidden chats: ${e.toString()}');
    }
  }

  Future<bool> authenticateForHiddenChats(String password) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // First try biometric authentication
      try {
        final isAvailable = await _localAuth.canCheckBiometrics;
        if (isAvailable) {
          final isAuthenticated = await _localAuth.authenticate(
            localizedReason: 'Authenticate to access hidden chats',
            options: const AuthenticationOptions(
              stickyAuth: true,
              biometricOnly: true,
            ),
          );
          if (isAuthenticated) {
            return true;
          }
        }
      } on PlatformException catch (e) {
        debugPrint('Biometric authentication failed: ${e.message}');
      }

      // Fallback to password authentication
      // In a real app, this would verify against a secure password hash
      // For now, we'll check against a user-specific password stored in Firestore
      final userDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .get();
      if (userDoc.exists) {
        final userData = userDoc.data()!;
        final storedPassword = userData['hiddenChatsPassword'] as String?;

        if (storedPassword != null && storedPassword == password) {
          return true;
        }
      }

      return false;
    } catch (e) {
      throw Exception('Authentication failed: ${e.toString()}');
    }
  }

  Future<void> setHiddenChatsPassword(String password) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // In a real app, this would hash the password before storing
      await _firestore.collection('users').doc(currentUser.uid).update({
        'hiddenChatsPassword': password,
        'hiddenChatsPasswordSetAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('Failed to set hidden chats password: ${e.toString()}');
    }
  }
}
