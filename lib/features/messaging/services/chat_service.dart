import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:billionaires_social/features/messaging/models/chat_model.dart';
import 'package:billionaires_social/core/services/firebase_service.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:uuid/uuid.dart';
import 'package:billionaires_social/features/messaging/models/voice_message_model.dart';
import 'package:billionaires_social/features/messaging/models/message_encryption_model.dart';

class ChatService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseService _firebaseService = getIt<FirebaseService>();
  final Uuid _uuid = const Uuid();

  // Get all chats for current user
  Stream<List<ChatModel>> getChatsStream() {
    final currentUser = _firebaseService.currentUser;
    if (currentUser == null) return Stream.value([]);

    return _firestore
        .collection('chats')
        .where('participants', arrayContains: currentUser.uid)
        .orderBy('lastMessageTime', descending: true)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs.map((doc) {
            final data = doc.data();
            return ChatModel(
              id: doc.id,
              name: data['name'] ?? '',
              avatarUrl: data['avatarUrl'] ?? '',
              lastMessage: data['lastMessage'] ?? '',
              lastMessageTime: (data['lastMessageTime'] as Timestamp).toDate(),
              isGroup: data['isGroup'] ?? false,
              participants: List<String>.from(data['participants'] ?? []),
              isHidden: data['isHidden'] ?? false,
              isBlocked: data['isBlocked'] ?? false,
              privacySettings: ChatPrivacySettings(
                blockScreenshots:
                    data['privacySettings']?['blockScreenshots'] ?? false,
                screenshotAlerts:
                    data['privacySettings']?['screenshotAlerts'] ?? true,
                readReceipts: data['privacySettings']?['readReceipts'] ?? true,
                typingIndicators:
                    data['privacySettings']?['typingIndicators'] ?? true,
                messageReactions:
                    data['privacySettings']?['messageReactions'] ?? true,
                selfDestructEnabled:
                    data['privacySettings']?['selfDestructEnabled'] ?? false,
                defaultSelfDestructSeconds:
                    data['privacySettings']?['defaultSelfDestructSeconds'] ??
                    10,
              ),
              unreadCount: data['unreadCount'] ?? 0,
              lastMessageStatus: MessageStatus.values.firstWhere(
                (e) => e.name == (data['lastMessageStatus'] ?? 'sent'),
                orElse: () => MessageStatus.sent,
              ),
            );
          }).toList(),
        );
  }

  // Get messages for a specific chat
  Stream<List<MessageModel>> getMessagesStream(String chatId) {
    return _firestore
        .collection('chats')
        .doc(chatId)
        .collection('messages')
        .orderBy('timestamp', descending: true)
        .limit(50)
        .snapshots()
        .asyncMap((snapshot) async {
          final messages = <MessageModel>[];
          for (final doc in snapshot.docs) {
            final data = doc.data();
            final encryptedContent = data['content'] ?? '';
            final decryptedContent = await _decryptContent(encryptedContent);

            messages.add(
              MessageModel(
                id: doc.id,
                chatId: chatId,
                senderId: data['senderId'] ?? '',
                content: decryptedContent,
                timestamp: (data['timestamp'] as Timestamp).toDate(),
                type: MessageType.values.firstWhere(
                  (e) => e.name == (data['type'] ?? 'text'),
                  orElse: () => MessageType.text,
                ),
                status: MessageStatus.values.firstWhere(
                  (e) => e.name == (data['status'] ?? 'sent'),
                  orElse: () => MessageStatus.sent,
                ),
                isEdited: data['isEdited'] ?? false,
                editedAt: data['editedAt'] != null
                    ? (data['editedAt'] as Timestamp).toDate()
                    : null,
                reactions: (data['reactions'] as List<dynamic>? ?? [])
                    .map(
                      (reaction) => MessageReaction(
                        userId: reaction['userId'] ?? '',
                        emoji: reaction['emoji'] ?? '',
                        timestamp: (reaction['timestamp'] as Timestamp)
                            .toDate(),
                      ),
                    )
                    .toList(),
                selfDestructSettings: data['selfDestructSettings'] != null
                    ? SelfDestructSettings(
                        type: SelfDestructType.values.firstWhere(
                          (e) =>
                              e.name ==
                              (data['selfDestructSettings']['type'] ??
                                  'afterRead'),
                          orElse: () => SelfDestructType.afterRead,
                        ),
                        durationInSeconds:
                            data['selfDestructSettings']['durationInSeconds'] ??
                            10,
                        expiresAt:
                            data['selfDestructSettings']['expiresAt'] != null
                            ? (data['selfDestructSettings']['expiresAt']
                                      as Timestamp)
                                  .toDate()
                            : null,
                      )
                    : null,
                seenBy: List<String>.from(data['seenBy'] ?? []),
                isDeleted: data['isDeleted'] ?? false,
                deletedAt: data['deletedAt'] != null
                    ? (data['deletedAt'] as Timestamp).toDate()
                    : null,
              ),
            );
          }
          return messages;
        });
  }

  // Encrypt content before sending
  Future<String> _encryptContent(
    String content, {
    EncryptionSettings? settings,
  }) async {
    // TODO: Implement actual encryption logic
    // For now, return the content as-is
    return content;
  }

  // Decrypt content after retrieval
  Future<String> _decryptContent(String encryptedContent) async {
    // TODO: Implement actual decryption logic
    // For now, return the content as-is
    return encryptedContent;
  }

  // Send a message (with optional encryption)
  Future<MessageModel> sendMessage({
    required String chatId,
    required String content,
    required MessageType type,
    SelfDestructSettings? selfDestructSettings,
    EncryptionSettings? encryptionSettings,
  }) async {
    final currentUser = _firebaseService.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    final messageId = _uuid.v4();
    final encryptedContent = await _encryptContent(
      content,
      settings: encryptionSettings,
    );
    final message = MessageModel(
      id: messageId,
      chatId: chatId,
      senderId: currentUser.uid,
      content: encryptedContent,
      timestamp: DateTime.now(),
      type: type,
      status: MessageStatus.sending,
      isEdited: false,
      editedAt: null,
      reactions: [],
      selfDestructSettings: selfDestructSettings,
      seenBy: [currentUser.uid],
      isDeleted: false,
      deletedAt: null,
    );

    // Add message to Firestore
    await _firestore
        .collection('chats')
        .doc(chatId)
        .collection('messages')
        .doc(messageId)
        .set({
          'senderId': message.senderId,
          'content': message.content,
          'timestamp': Timestamp.fromDate(message.timestamp),
          'type': message.type.name,
          'status': message.status.name,
          'isEdited': message.isEdited,
          'editedAt': message.editedAt != null
              ? Timestamp.fromDate(message.editedAt!)
              : null,
          'reactions': message.reactions
              .map(
                (r) => {
                  'userId': r.userId,
                  'emoji': r.emoji,
                  'timestamp': Timestamp.fromDate(r.timestamp),
                },
              )
              .toList(),
          'selfDestructSettings': message.selfDestructSettings != null
              ? {
                  'type': message.selfDestructSettings!.type.name,
                  'durationInSeconds':
                      message.selfDestructSettings!.durationInSeconds,
                  'expiresAt': message.selfDestructSettings!.expiresAt != null
                      ? Timestamp.fromDate(
                          message.selfDestructSettings!.expiresAt!,
                        )
                      : null,
                }
              : null,
          'seenBy': message.seenBy,
          'isDeleted': message.isDeleted,
          'deletedAt': message.deletedAt != null
              ? Timestamp.fromDate(message.deletedAt!)
              : null,
          'encryption': encryptionSettings?.toJson(),
        });

    // Update chat's last message
    await _firestore.collection('chats').doc(chatId).update({
      'lastMessage': '[Encrypted]',
      'lastMessageTime': Timestamp.fromDate(message.timestamp),
      'lastMessageStatus': message.status.name,
    });

    // Update message status to sent
    await _firestore
        .collection('chats')
        .doc(chatId)
        .collection('messages')
        .doc(messageId)
        .update({'status': MessageStatus.sent.name});

    return message.copyWith(status: MessageStatus.sent);
  }

  // Create a new chat
  Future<String> createChat({
    required List<String> participantIds,
    required String name,
    String? avatarUrl,
    bool isGroup = false,
  }) async {
    final currentUser = _firebaseService.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    final chatId = _uuid.v4();
    final allParticipants = [...participantIds, currentUser.uid];

    await _firestore.collection('chats').doc(chatId).set({
      'name': name,
      'avatarUrl': avatarUrl ?? '',
      'participants': allParticipants,
      'isGroup': isGroup,
      'isHidden': false,
      'isBlocked': false,
      'lastMessage': '',
      'lastMessageTime': Timestamp.fromDate(DateTime.now()),
      'lastMessageStatus': MessageStatus.sent.name,
      'unreadCount': 0,
      'privacySettings': {
        'blockScreenshots': false,
        'screenshotAlerts': true,
        'readReceipts': true,
        'typingIndicators': true,
        'messageReactions': true,
        'selfDestructEnabled': false,
        'defaultSelfDestructSeconds': 10,
      },
      'createdAt': Timestamp.fromDate(DateTime.now()),
    });

    return chatId;
  }

  // Mark message as read
  Future<void> markMessageAsRead(String messageId, String chatId) async {
    final currentUser = _firebaseService.currentUser;
    if (currentUser == null) return;

    await _firestore
        .collection('chats')
        .doc(chatId)
        .collection('messages')
        .doc(messageId)
        .update({
          'seenBy': FieldValue.arrayUnion([currentUser.uid]),
          'status': MessageStatus.read.name,
        });
  }

  // Edit a message
  Future<bool> editMessage({
    required String messageId,
    required String chatId,
    required String newContent,
  }) async {
    final currentUser = _firebaseService.currentUser;
    if (currentUser == null) return false;

    try {
      await _firestore
          .collection('chats')
          .doc(chatId)
          .collection('messages')
          .doc(messageId)
          .update({
            'content': newContent,
            'isEdited': true,
            'editedAt': Timestamp.fromDate(DateTime.now()),
          });
      return true;
    } catch (e) {
      return false;
    }
  }

  // Delete a message
  Future<bool> deleteMessage(String messageId, String chatId) async {
    final currentUser = _firebaseService.currentUser;
    if (currentUser == null) return false;

    try {
      await _firestore
          .collection('chats')
          .doc(chatId)
          .collection('messages')
          .doc(messageId)
          .update({
            'isDeleted': true,
            'deletedAt': Timestamp.fromDate(DateTime.now()),
          });
      return true;
    } catch (e) {
      return false;
    }
  }

  // Add reaction to message
  Future<bool> addReaction({
    required String messageId,
    required String chatId,
    required String emoji,
  }) async {
    final currentUser = _firebaseService.currentUser;
    if (currentUser == null) return false;

    try {
      final reaction = {
        'userId': currentUser.uid,
        'emoji': emoji,
        'timestamp': Timestamp.fromDate(DateTime.now()),
      };

      await _firestore
          .collection('chats')
          .doc(chatId)
          .collection('messages')
          .doc(messageId)
          .update({
            'reactions': FieldValue.arrayUnion([reaction]),
          });
      return true;
    } catch (e) {
      return false;
    }
  }

  // Remove reaction from message
  Future<bool> removeReaction({
    required String messageId,
    required String chatId,
    required String emoji,
  }) async {
    final currentUser = _firebaseService.currentUser;
    if (currentUser == null) return false;

    try {
      final reaction = {
        'userId': currentUser.uid,
        'emoji': emoji,
        'timestamp': Timestamp.fromDate(DateTime.now()),
      };

      await _firestore
          .collection('chats')
          .doc(chatId)
          .collection('messages')
          .doc(messageId)
          .update({
            'reactions': FieldValue.arrayRemove([reaction]),
          });
      return true;
    } catch (e) {
      return false;
    }
  }

  // Get chat by ID
  Future<ChatModel?> getChatById(String chatId) async {
    try {
      final doc = await _firestore.collection('chats').doc(chatId).get();
      if (!doc.exists) return null;

      final data = doc.data()!;
      return ChatModel(
        id: doc.id,
        name: data['name'] ?? '',
        avatarUrl: data['avatarUrl'] ?? '',
        lastMessage: data['lastMessage'] ?? '',
        lastMessageTime: (data['lastMessageTime'] as Timestamp).toDate(),
        isGroup: data['isGroup'] ?? false,
        participants: List<String>.from(data['participants'] ?? []),
        isHidden: data['isHidden'] ?? false,
        isBlocked: data['isBlocked'] ?? false,
        privacySettings: ChatPrivacySettings(
          blockScreenshots:
              data['privacySettings']?['blockScreenshots'] ?? false,
          screenshotAlerts:
              data['privacySettings']?['screenshotAlerts'] ?? true,
          readReceipts: data['privacySettings']?['readReceipts'] ?? true,
          typingIndicators:
              data['privacySettings']?['typingIndicators'] ?? true,
          messageReactions:
              data['privacySettings']?['messageReactions'] ?? true,
          selfDestructEnabled:
              data['privacySettings']?['selfDestructEnabled'] ?? false,
          defaultSelfDestructSeconds:
              data['privacySettings']?['defaultSelfDestructSeconds'] ?? 10,
        ),
        unreadCount: data['unreadCount'] ?? 0,
        lastMessageStatus: MessageStatus.values.firstWhere(
          (e) => e.name == (data['lastMessageStatus'] ?? 'sent'),
          orElse: () => MessageStatus.sent,
        ),
      );
    } catch (e) {
      return null;
    }
  }

  // Update chat privacy settings
  Future<bool> updateChatPrivacySettings({
    required String chatId,
    required ChatPrivacySettings settings,
  }) async {
    try {
      await _firestore.collection('chats').doc(chatId).update({
        'privacySettings': {
          'blockScreenshots': settings.blockScreenshots,
          'screenshotAlerts': settings.screenshotAlerts,
          'readReceipts': settings.readReceipts,
          'typingIndicators': settings.typingIndicators,
          'messageReactions': settings.messageReactions,
          'selfDestructEnabled': settings.selfDestructEnabled,
          'defaultSelfDestructSeconds': settings.defaultSelfDestructSeconds,
        },
      });
      return true;
    } catch (e) {
      return false;
    }
  }

  // Search messages
  Future<List<MessageModel>> searchMessages({
    required String query,
    String? chatId,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    final currentUser = _firebaseService.currentUser;
    if (currentUser == null) return [];

    try {
      Query messagesQuery = _firestore.collectionGroup('messages');

      if (chatId != null) {
        messagesQuery = messagesQuery.where('chatId', isEqualTo: chatId);
      }

      if (fromDate != null) {
        messagesQuery = messagesQuery.where(
          'timestamp',
          isGreaterThanOrEqualTo: Timestamp.fromDate(fromDate),
        );
      }

      if (toDate != null) {
        messagesQuery = messagesQuery.where(
          'timestamp',
          isLessThanOrEqualTo: Timestamp.fromDate(toDate),
        );
      }

      final snapshot = await messagesQuery.get();
      return snapshot.docs
          .where((doc) {
            final data = doc.data() as Map<String, dynamic>;
            final content = data['content']?.toString().toLowerCase() ?? '';
            return content.contains(query.toLowerCase());
          })
          .map((doc) {
            final data = doc.data() as Map<String, dynamic>;
            return MessageModel(
              id: doc.id,
              chatId: data['chatId'] ?? '',
              senderId: data['senderId'] ?? '',
              content: data['content'] ?? '',
              timestamp: (data['timestamp'] as Timestamp).toDate(),
              type: MessageType.values.firstWhere(
                (e) => e.name == (data['type'] ?? 'text'),
                orElse: () => MessageType.text,
              ),
              status: MessageStatus.values.firstWhere(
                (e) => e.name == (data['status'] ?? 'sent'),
                orElse: () => MessageStatus.sent,
              ),
              isEdited: data['isEdited'] ?? false,
              editedAt: data['editedAt'] != null
                  ? (data['editedAt'] as Timestamp).toDate()
                  : null,
              reactions: [],
              selfDestructSettings: null,
              seenBy: List<String>.from(data['seenBy'] ?? []),
              isDeleted: data['isDeleted'] ?? false,
              deletedAt: null,
            );
          })
          .toList();
    } catch (e) {
      return [];
    }
  }

  // Hide a chat
  Future<bool> hideChat(String chatId) async {
    try {
      await _firestore.collection('chats').doc(chatId).update({
        'isHidden': true,
        'updatedAt': FieldValue.serverTimestamp(),
      });
      return true;
    } catch (e) {
      return false;
    }
  }

  // Unhide a chat
  Future<bool> unhideChat(String chatId) async {
    try {
      await _firestore.collection('chats').doc(chatId).update({
        'isHidden': false,
        'updatedAt': FieldValue.serverTimestamp(),
      });
      return true;
    } catch (e) {
      return false;
    }
  }

  // Delete a chat
  Future<bool> deleteChat(String chatId) async {
    try {
      // Delete all messages in the chat first
      final messagesSnapshot = await _firestore
          .collection('chats')
          .doc(chatId)
          .collection('messages')
          .get();

      for (final doc in messagesSnapshot.docs) {
        await doc.reference.delete();
      }

      // Delete the chat document
      await _firestore.collection('chats').doc(chatId).delete();
      return true;
    } catch (e) {
      return false;
    }
  }

  // Clear my messages in a specific chat
  Future<bool> clearMyMessages(String chatId) async {
    final currentUser = _firebaseService.currentUser;
    if (currentUser == null) return false;

    try {
      final messagesSnapshot = await _firestore
          .collection('chats')
          .doc(chatId)
          .collection('messages')
          .where('senderId', isEqualTo: currentUser.uid)
          .get();

      for (final doc in messagesSnapshot.docs) {
        await doc.reference.update({
          'isDeleted': true,
          'deletedAt': FieldValue.serverTimestamp(),
        });
      }
      return true;
    } catch (e) {
      return false;
    }
  }

  // Check if a message can be edited (within 10 minutes of sending)
  bool canEditMessage(MessageModel message) {
    final currentUser = _firebaseService.currentUser;
    if (currentUser == null) return false;

    // Only the sender can edit their own messages
    if (message.senderId != currentUser.uid) return false;

    // Check if message was sent within the last 10 minutes
    final timeSinceSent = DateTime.now().difference(message.timestamp);
    return timeSinceSent.inMinutes < 10;
  }

  // Send a shared post to a chat
  Future<MessageModel> sendSharedPost({
    required String chatId,
    required String postId,
    required String postUsername,
    required String postCaption,
    required String postMediaUrl,
    required String postUserAvatarUrl,
    String? postLocation,
  }) async {
    final currentUser = _firebaseService.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    final messageId = _uuid.v4();
    final sharedPostData = {
      'postId': postId,
      'postUsername': postUsername,
      'postCaption': postCaption,
      'postMediaUrl': postMediaUrl,
      'postUserAvatarUrl': postUserAvatarUrl,
      'postLocation': postLocation,
      'sharedAt': DateTime.now().toIso8601String(),
    };

    final message = MessageModel(
      id: messageId,
      chatId: chatId,
      senderId: currentUser.uid,
      content: 'Shared a post from $postUsername',
      timestamp: DateTime.now(),
      type:
          MessageType.text, // We'll use text type but include shared post data
      status: MessageStatus.sending,
      isEdited: false,
      editedAt: null,
      reactions: [],
      selfDestructSettings: null,
      seenBy: [currentUser.uid],
      isDeleted: false,
      deletedAt: null,
    );

    // Add message to Firestore with shared post data
    await _firestore
        .collection('chats')
        .doc(chatId)
        .collection('messages')
        .doc(messageId)
        .set({
          'senderId': message.senderId,
          'content': message.content,
          'timestamp': Timestamp.fromDate(message.timestamp),
          'type': message.type.name,
          'status': message.status.name,
          'isEdited': message.isEdited,
          'editedAt': message.editedAt != null
              ? Timestamp.fromDate(message.editedAt!)
              : null,
          'reactions': message.reactions
              .map(
                (r) => {
                  'userId': r.userId,
                  'emoji': r.emoji,
                  'timestamp': Timestamp.fromDate(r.timestamp),
                },
              )
              .toList(),
          'selfDestructSettings': message.selfDestructSettings != null
              ? {
                  'type': message.selfDestructSettings!.type.name,
                  'durationInSeconds':
                      message.selfDestructSettings!.durationInSeconds,
                  'expiresAt': message.selfDestructSettings!.expiresAt != null
                      ? Timestamp.fromDate(
                          message.selfDestructSettings!.expiresAt!,
                        )
                      : null,
                }
              : null,
          'seenBy': message.seenBy,
          'isDeleted': message.isDeleted,
          'deletedAt': message.deletedAt != null
              ? Timestamp.fromDate(message.deletedAt!)
              : null,
          'sharedPostData': sharedPostData, // Add shared post data
        });

    // Update chat's last message
    await _firestore.collection('chats').doc(chatId).update({
      'lastMessage': message.content,
      'lastMessageTime': Timestamp.fromDate(message.timestamp),
      'lastMessageStatus': message.status.name,
    });

    // Update message status to sent
    await _firestore
        .collection('chats')
        .doc(chatId)
        .collection('messages')
        .doc(messageId)
        .update({'status': MessageStatus.sent.name});

    return message.copyWith(status: MessageStatus.sent);
  }

  // Get or create a direct chat with a user
  Future<String> getOrCreateDirectChat(
    String otherUserId,
    String otherUserName,
    String otherUserAvatar,
  ) async {
    final currentUser = _firebaseService.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    // Check if a direct chat already exists
    final existingChats = await _firestore
        .collection('chats')
        .where('participants', arrayContains: currentUser.uid)
        .where('isGroup', isEqualTo: false)
        .get();

    for (final doc in existingChats.docs) {
      final data = doc.data();
      final participants = List<String>.from(data['participants'] ?? []);
      if (participants.contains(otherUserId) && participants.length == 2) {
        return doc.id; // Return existing chat ID
      }
    }

    // Create new direct chat
    return await createChat(
      participantIds: [otherUserId],
      name: otherUserName,
      avatarUrl: otherUserAvatar,
      isGroup: false,
    );
  }

  // Send a voice message (with optional encryption)
  Future<MessageModel> sendVoiceMessage({
    required String chatId,
    required String audioUrl,
    required int durationInSeconds,
    String? transcription,
    VoiceMessageMetadata? metadata,
    EncryptionSettings? encryptionSettings,
  }) async {
    final currentUser = _firebaseService.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    final messageId = _uuid.v4();
    final encryptedAudioUrl = await _encryptContent(
      audioUrl,
      settings: encryptionSettings,
    );
    final message = MessageModel(
      id: messageId,
      chatId: chatId,
      senderId: currentUser.uid,
      content: encryptedAudioUrl,
      timestamp: DateTime.now(),
      type: MessageType.audio,
      status: MessageStatus.sending,
      isEdited: false,
      editedAt: null,
      reactions: [],
      selfDestructSettings: null,
      seenBy: [currentUser.uid],
      isDeleted: false,
      deletedAt: null,
    );

    // Add message to Firestore
    await _firestore
        .collection('chats')
        .doc(chatId)
        .collection('messages')
        .doc(messageId)
        .set({
          'senderId': message.senderId,
          'content': message.content,
          'timestamp': Timestamp.fromDate(message.timestamp),
          'type': message.type.name,
          'status': message.status.name,
          'isEdited': message.isEdited,
          'editedAt': message.editedAt != null
              ? Timestamp.fromDate(message.editedAt!)
              : null,
          'reactions': [],
          'selfDestructSettings': null,
          'seenBy': message.seenBy,
          'isDeleted': message.isDeleted,
          'deletedAt': null,
          'voiceMessage': {
            'durationInSeconds': durationInSeconds,
            'transcription': transcription,
            'metadata': metadata?.toJson(),
          },
          'encryption': encryptionSettings?.toJson(),
        });

    // Update chat's last message
    await _firestore.collection('chats').doc(chatId).update({
      'lastMessage': '[Voice message]',
      'lastMessageTime': Timestamp.fromDate(message.timestamp),
      'lastMessageStatus': message.status.name,
    });

    // Update message status to sent
    await _firestore
        .collection('chats')
        .doc(chatId)
        .collection('messages')
        .doc(messageId)
        .update({'status': MessageStatus.sent.name});

    return message.copyWith(status: MessageStatus.sent);
  }
}
