import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:billionaires_social/core/services/firebase_service.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/features/messaging/models/call_model.dart';
import 'package:uuid/uuid.dart';

class CallService {
  final FirebaseFirestore _firestore;
  final FirebaseService _firebaseService;
  final Uuid _uuid;

  CallService()
    : _firestore = FirebaseFirestore.instance,
      _firebaseService = getIt<FirebaseService>(),
      _uuid = Uuid();

  // Initialize a new call
  Future<String> initiateCall({
    required String chatId,
    required List<String> participantIds,
    required CallType type,
  }) async {
    final currentUser = _firebaseService.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    final callId = _uuid.v4();
    final call = CallModel(
      id: callId,
      chatId: chatId,
      initiatorId: currentUser.uid,
      participantIds: [currentUser.uid, ...participantIds],
      type: type,
      status: CallStatus.ringing,
      startTime: DateTime.now(),
    );

    // Save call to Firestore
    await _firestore.collection('calls').doc(callId).set(call.toJson());

    return callId;
  }

  // Accept an incoming call
  Future<void> acceptCall(String callId) async {
    final currentUser = _firebaseService.currentUser;
    if (currentUser == null) return;

    await _firestore.collection('calls').doc(callId).update({
      'status': CallStatus.active.name,
      'participantIds': FieldValue.arrayUnion([currentUser.uid]),
    });
  }

  // Reject an incoming call
  Future<void> rejectCall(String callId) async {
    final currentUser = _firebaseService.currentUser;
    if (currentUser == null) return;

    await _firestore.collection('calls').doc(callId).update({
      'status': CallStatus.rejected.name,
      'endTime': Timestamp.fromDate(DateTime.now()),
    });
  }

  // End an active call
  Future<void> endCall(String callId) async {
    final currentUser = _firebaseService.currentUser;
    if (currentUser == null) return;

    final callDoc = await _firestore.collection('calls').doc(callId).get();
    if (!callDoc.exists) return;

    final callData = callDoc.data()!;
    final startTime = (callData['startTime'] as Timestamp).toDate();
    final endTime = DateTime.now();
    final duration = endTime.difference(startTime);

    await _firestore.collection('calls').doc(callId).update({
      'status': CallStatus.ended.name,
      'endTime': Timestamp.fromDate(endTime),
      'duration': duration.inSeconds,
    });
  }

  // Get call history for a chat
  Future<List<CallModel>> getCallHistory(String chatId) async {
    final currentUser = _firebaseService.currentUser;
    if (currentUser == null) return [];

    final querySnapshot = await _firestore
        .collection('calls')
        .where('chatId', isEqualTo: chatId)
        .where('participantIds', arrayContains: currentUser.uid)
        .orderBy('startTime', descending: true)
        .limit(50)
        .get();

    return querySnapshot.docs
        .map((doc) => CallModel.fromJson({...doc.data(), 'id': doc.id}))
        .toList();
  }

  // Get active calls for current user
  Stream<List<CallModel>> getActiveCalls() {
    final currentUser = _firebaseService.currentUser;
    if (currentUser == null) return Stream.value([]);

    return _firestore
        .collection('calls')
        .where('participantIds', arrayContains: currentUser.uid)
        .where(
          'status',
          whereIn: [
            CallStatus.ringing.name,
            CallStatus.connecting.name,
            CallStatus.active.name,
          ],
        )
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map((doc) => CallModel.fromJson({...doc.data(), 'id': doc.id}))
              .toList(),
        );
  }

  // Update call participant status
  Future<void> updateParticipantStatus({
    required String callId,
    required String userId,
    bool? isMuted,
    bool? isVideoEnabled,
  }) async {
    final updates = <String, dynamic>{};
    if (isMuted != null) updates['isMuted'] = isMuted;
    if (isVideoEnabled != null) updates['isVideoEnabled'] = isVideoEnabled;

    if (updates.isNotEmpty) {
      await _firestore
          .collection('calls')
          .doc(callId)
          .collection('participants')
          .doc(userId)
          .set(updates, SetOptions(merge: true));
    }
  }

  // Get call statistics
  Future<Map<String, dynamic>> getCallStats(String chatId) async {
    final currentUser = _firebaseService.currentUser;
    if (currentUser == null) return {};

    final querySnapshot = await _firestore
        .collection('calls')
        .where('chatId', isEqualTo: chatId)
        .where('participantIds', arrayContains: currentUser.uid)
        .get();

    int totalCalls = 0;
    int totalDuration = 0;
    int missedCalls = 0;
    int voiceCalls = 0;
    int videoCalls = 0;

    for (final doc in querySnapshot.docs) {
      final data = doc.data();
      totalCalls++;

      final status = CallStatus.values.firstWhere(
        (e) => e.name == data['status'],
        orElse: () => CallStatus.ended,
      );

      if (status == CallStatus.missed) {
        missedCalls++;
      }

      final type = CallType.values.firstWhere(
        (e) => e.name == data['type'],
        orElse: () => CallType.voice,
      );

      if (type == CallType.voice) {
        voiceCalls++;
      } else {
        videoCalls++;
      }

      if (data['duration'] != null) {
        totalDuration += (data['duration'] as num).toInt();
      }
    }

    return {
      'totalCalls': totalCalls,
      'totalDuration': totalDuration,
      'missedCalls': missedCalls,
      'voiceCalls': voiceCalls,
      'videoCalls': videoCalls,
      'averageDuration': totalCalls > 0 ? totalDuration / totalCalls : 0,
    };
  }

  // Delete call history
  Future<void> deleteCallHistory(String chatId) async {
    final currentUser = _firebaseService.currentUser;
    if (currentUser == null) return;

    final querySnapshot = await _firestore
        .collection('calls')
        .where('chatId', isEqualTo: chatId)
        .where('participantIds', arrayContains: currentUser.uid)
        .get();

    final batch = _firestore.batch();
    for (final doc in querySnapshot.docs) {
      batch.delete(doc.reference);
    }
    await batch.commit();
  }
}
