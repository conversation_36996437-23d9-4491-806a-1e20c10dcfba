import 'package:freezed_annotation/freezed_annotation.dart';

part 'chat_model.freezed.dart';
part 'chat_model.g.dart';

@freezed
abstract class ChatModel with _$ChatModel {
  const factory ChatModel({
    required String id,
    required String name,
    required String avatarUrl,
    required String lastMessage,
    required DateTime lastMessageTime,
    required bool isGroup,
    required List<String> participants,
    required bool isHidden,
    required bool isBlocked,
    required ChatPrivacySettings privacySettings,
    required int unreadCount,
    required MessageStatus lastMessageStatus,
  }) = _ChatModel;

  factory ChatModel.fromJson(Map<String, dynamic> json) =>
      _$ChatModelFromJson(json);
}

@freezed
abstract class MessageModel with _$MessageModel {
  const factory MessageModel({
    required String id,
    required String chatId,
    required String senderId,
    required String content,
    required DateTime timestamp,
    required MessageType type,
    required MessageStatus status,
    required bool isEdited,
    required DateTime? editedAt,
    required List<MessageReaction> reactions,
    required SelfDestructSettings? selfDestructSettings,
    required List<String> seenBy,
    required bool isDeleted,
    required DateTime? deletedAt,
  }) = _MessageModel;

  factory MessageModel.fromJson(Map<String, dynamic> json) =>
      _$MessageModelFromJson(json);
}

@freezed
abstract class MessageReaction with _$MessageReaction {
  const factory MessageReaction({
    required String userId,
    required String emoji,
    required DateTime timestamp,
  }) = _MessageReaction;

  factory MessageReaction.fromJson(Map<String, dynamic> json) =>
      _$MessageReactionFromJson(json);
}

@freezed
abstract class SelfDestructSettings with _$SelfDestructSettings {
  const factory SelfDestructSettings({
    required SelfDestructType type,
    required int durationInSeconds,
    required DateTime? expiresAt,
  }) = _SelfDestructSettings;

  factory SelfDestructSettings.fromJson(Map<String, dynamic> json) =>
      _$SelfDestructSettingsFromJson(json);
}

@freezed
abstract class ChatPrivacySettings with _$ChatPrivacySettings {
  const factory ChatPrivacySettings({
    required bool blockScreenshots,
    required bool screenshotAlerts,
    required bool readReceipts,
    required bool typingIndicators,
    required bool messageReactions,
    required bool selfDestructEnabled,
    required int defaultSelfDestructSeconds,
  }) = _ChatPrivacySettings;

  factory ChatPrivacySettings.fromJson(Map<String, dynamic> json) =>
      _$ChatPrivacySettingsFromJson(json);
}

enum MessageType { text, image, video, audio, file, location, contact }

enum MessageStatus { sending, sent, delivered, read, failed }

enum SelfDestructType { afterRead, afterTime, afterReply }

enum MessageAction { edit, delete, react, reply, forward, copy }
