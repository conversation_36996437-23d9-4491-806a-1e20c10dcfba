import 'package:freezed_annotation/freezed_annotation.dart';

part 'message_encryption_model.freezed.dart';
part 'message_encryption_model.g.dart';

@freezed
abstract class MessageEncryptionModel with _$MessageEncryptionModel {
  const factory MessageEncryptionModel({
    required String id,
    required String messageId,
    required EncryptionStatus status,
    required EncryptionType type,
    required String encryptedContent,
    required String? decryptedContent,
    required String senderPublicKey,
    required String? recipientPublicKey,
    required DateTime createdAt,
    required DateTime? decryptedAt,
    required MessageEncryptionMetadata metadata,
  }) = _MessageEncryptionModel;

  factory MessageEncryptionModel.fromJson(Map<String, dynamic> json) =>
      _$MessageEncryptionModelFromJson(json);
}

@freezed
abstract class MessageEncryptionMetadata with _$MessageEncryptionMetadata {
  const factory MessageEncryptionMetadata({
    required String algorithm,
    required String keySize,
    required String? sessionKey,
    required bool isForwardSecrecy,
    required DateTime? keyExpiry,
    required Map<String, dynamic>? additionalData,
  }) = _MessageEncryptionMetadata;

  factory MessageEncryptionMetadata.fromJson(Map<String, dynamic> json) =>
      _$MessageEncryptionMetadataFromJson(json);
}

@freezed
abstract class EncryptionKeyModel with _$EncryptionKeyModel {
  const factory EncryptionKeyModel({
    required String id,
    required String userId,
    required String publicKey,
    required String? privateKey, // Only stored locally
    required KeyType type,
    required DateTime createdAt,
    required DateTime? expiresAt,
    required bool isActive,
    required List<String> authorizedUsers,
  }) = _EncryptionKeyModel;

  factory EncryptionKeyModel.fromJson(Map<String, dynamic> json) =>
      _$EncryptionKeyModelFromJson(json);
}

@freezed
abstract class EncryptionSettings with _$EncryptionSettings {
  const factory EncryptionSettings({
    @Default(true) bool enableEndToEndEncryption,
    @Default(true) bool requireEncryption,
    @Default(EncryptionAlgorithm.aes256) EncryptionAlgorithm algorithm,
    @Default(KeyExchangeProtocol.diffieHellman) KeyExchangeProtocol keyExchange,
    @Default(true) bool enableForwardSecrecy,
    @Default(30) int keyRotationDays,
    @Default(true) bool showEncryptionStatus,
    @Default(true) bool verifyKeyFingerprints,
    @Default(false) bool allowUnencryptedMessages,
    @Default(true) bool autoEncryptMedia,
  }) = _EncryptionSettings;

  factory EncryptionSettings.fromJson(Map<String, dynamic> json) =>
      _$EncryptionSettingsFromJson(json);
}

@freezed
abstract class KeyExchangeSession with _$KeyExchangeSession {
  const factory KeyExchangeSession({
    required String id,
    required String initiatorId,
    required String recipientId,
    required KeyExchangeStatus status,
    required String initiatorPublicKey,
    required String? recipientPublicKey,
    required String? sharedSecret,
    required DateTime createdAt,
    required DateTime? completedAt,
    required DateTime? expiresAt,
  }) = _KeyExchangeSession;

  factory KeyExchangeSession.fromJson(Map<String, dynamic> json) =>
      _$KeyExchangeSessionFromJson(json);
}

enum EncryptionStatus {
  notEncrypted,
  encrypting,
  encrypted,
  decrypting,
  decrypted,
  failed,
  keyExchangeRequired,
}

enum EncryptionType {
  none,
  transport, // TLS/SSL
  endToEnd,
  group,
  custom,
}

enum KeyType { rsa, ecc, aes, custom }

enum EncryptionAlgorithm { aes128, aes256, chacha20, custom }

enum KeyExchangeProtocol { diffieHellman, rsa, ecc, custom }

enum KeyExchangeStatus { initiated, pending, completed, failed, expired }
