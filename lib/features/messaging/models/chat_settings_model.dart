import 'package:freezed_annotation/freezed_annotation.dart';

part 'chat_settings_model.freezed.dart';
part 'chat_settings_model.g.dart';

enum SelfDestructTimer { afterReading, after24h, after7days, custom }

enum EditWindowDuration {
  fiveMinutes,
  tenMinutes,
  thirtyMinutes,
  oneHour,
  custom,
}

@freezed
abstract class ChatSettings with _$ChatSettings {
  const factory ChatSettings({
    // Screenshot Control
    @Default(false) bool allowScreenshots,
    @Default(true) bool notifyOnScreenshot,

    // Message Editing
    @Default(true) bool allowMessageEditing,
    @Default(EditWindowDuration.tenMinutes)
    EditWindowDuration editWindowDuration,
    @Default(10) int customEditWindowMinutes,

    // Mute Settings
    @Default(false) bool muteAllChats,

    // Message Requests
    @Default(true) bool allowMessagesFromNonFollowers,
    @Default(true) bool autoBlockSpamAccounts,

    // Self-Destruct Messages
    @Default(false) bool enableSelfDestruct,
    @Default(SelfDestructTimer.afterReading)
    SelfDestructTimer selfDestructTimer,
    @Default(10) int customSelfDestructSeconds,

    // Seen & Typing Status
    @Default(true) bool showSeenStatus,
    @Default(true) bool showTypingIndicators,

    // Sync Settings
    @Default(true) bool syncAcrossDevices,

    // Privacy
    @Default(false) bool requirePasswordForHiddenChats,
    @Default(false) bool useFaceIDForHiddenChats,
  }) = _ChatSettings;

  factory ChatSettings.fromJson(Map<String, dynamic> json) =>
      _$ChatSettingsFromJson(json);
}

@freezed
abstract class IndividualChatSettings with _$IndividualChatSettings {
  const factory IndividualChatSettings({
    required String chatId,
    // Override global settings
    bool? allowScreenshots,
    bool? notifyOnScreenshot,
    bool? allowMessageEditing,
    bool? muteChat,
    bool? showSeenStatus,
    bool? showTypingIndicators,
    bool? enableSelfDestruct,
    SelfDestructTimer? selfDestructTimer,
    int? customSelfDestructSeconds,
    // Chat-specific settings
    @Default(false) bool isHidden,
    @Default(false) bool isPinned,
    @Default(false) bool isArchived,
    @Default(false) bool isStarred,
  }) = _IndividualChatSettings;

  factory IndividualChatSettings.fromJson(Map<String, dynamic> json) =>
      _$IndividualChatSettingsFromJson(json);
}
