import 'package:freezed_annotation/freezed_annotation.dart';

part 'voice_message_model.freezed.dart';
part 'voice_message_model.g.dart';

@freezed
abstract class VoiceMessageModel with _$VoiceMessageModel {
  const factory VoiceMessageModel({
    required String id,
    required String messageId,
    required String audioUrl,
    required int durationInSeconds,
    required String? transcription,
    required VoiceMessageStatus status,
    required DateTime createdAt,
    required DateTime? playedAt,
    required List<VoiceMessageReaction> reactions,
    required VoiceMessageMetadata metadata,
  }) = _VoiceMessageModel;

  factory VoiceMessageModel.fromJson(Map<String, dynamic> json) =>
      _$VoiceMessageModelFromJson(json);
}

@freezed
abstract class VoiceMessageReaction with _$VoiceMessageReaction {
  const factory VoiceMessageReaction({
    required String userId,
    required VoiceReactionType type,
    required DateTime timestamp,
  }) = _VoiceMessageReaction;

  factory VoiceMessageReaction.fromJson(Map<String, dynamic> json) =>
      _$VoiceMessageReactionFromJson(json);
}

@freezed
abstract class VoiceMessageMetadata with _$VoiceMessageMetadata {
  const factory VoiceMessageMetadata({
    required String fileSize,
    required String audioFormat,
    required int sampleRate,
    required int bitRate,
    required bool isCompressed,
    required String? recordingDevice,
    required Map<String, dynamic>? additionalData,
  }) = _VoiceMessageMetadata;

  factory VoiceMessageMetadata.fromJson(Map<String, dynamic> json) =>
      _$VoiceMessageMetadataFromJson(json);
}

@freezed
abstract class VoiceMessageSettings with _$VoiceMessageSettings {
  const factory VoiceMessageSettings({
    @Default(true) bool autoTranscribe,
    @Default(true) bool allowReactions,
    @Default(300) int maxDurationSeconds, // 5 minutes
    @Default(10) int minDurationSeconds, // 10 seconds
    @Default(true) bool showWaveform,
    @Default(true) bool allowPlaybackSpeed,
    @Default(['0.5x', '1x', '1.5x', '2x']) List<String> playbackSpeeds,
    @Default(true) bool saveToGallery,
    @Default(false) bool requireConfirmation,
  }) = _VoiceMessageSettings;

  factory VoiceMessageSettings.fromJson(Map<String, dynamic> json) =>
      _$VoiceMessageSettingsFromJson(json);
}

enum VoiceMessageStatus {
  recording,
  processing,
  ready,
  playing,
  paused,
  completed,
  failed,
  deleted,
}

enum VoiceReactionType { like, love, laugh, wow, sad, angry, custom }

enum VoiceMessageQuality {
  low, // 64kbps
  medium, // 128kbps
  high, // 256kbps
  ultra, // 320kbps
}
