import 'package:cloud_firestore/cloud_firestore.dart';

enum CallType { voice, video }

enum CallStatus { ringing, connecting, active, ended, missed, rejected }

class CallModel {
  final String id;
  final String chatId;
  final String initiatorId;
  final List<String> participantIds;
  final CallType type;
  final CallStatus status;
  final DateTime startTime;
  final DateTime? endTime;
  final Duration? duration;

  CallModel({
    required this.id,
    required this.chatId,
    required this.initiatorId,
    required this.participantIds,
    required this.type,
    required this.status,
    required this.startTime,
    this.endTime,
    this.duration,
  });

  CallModel copyWith({
    String? id,
    String? chatId,
    String? initiatorId,
    List<String>? participantIds,
    CallType? type,
    CallStatus? status,
    DateTime? startTime,
    DateTime? endTime,
    Duration? duration,
  }) {
    return CallModel(
      id: id ?? this.id,
      chatId: chatId ?? this.chatId,
      initiatorId: initiatorId ?? this.initiatorId,
      participantIds: participantIds ?? this.participantIds,
      type: type ?? this.type,
      status: status ?? this.status,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      duration: duration ?? this.duration,
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'chatId': chatId,
    'initiatorId': initiatorId,
    'participantIds': participantIds,
    'type': type.name,
    'status': status.name,
    'startTime': Timestamp.fromDate(startTime),
    'endTime': endTime != null ? Timestamp.fromDate(endTime!) : null,
    'duration': duration?.inSeconds,
  };

  factory CallModel.fromJson(Map<String, dynamic> json) => CallModel(
    id: json['id'],
    chatId: json['chatId'],
    initiatorId: json['initiatorId'],
    participantIds: List<String>.from(json['participantIds']),
    type: CallType.values.firstWhere(
      (e) => e.name == json['type'],
      orElse: () => CallType.voice,
    ),
    status: CallStatus.values.firstWhere(
      (e) => e.name == json['status'],
      orElse: () => CallStatus.ended,
    ),
    startTime: (json['startTime'] as Timestamp).toDate(),
    endTime: json['endTime'] != null
        ? (json['endTime'] as Timestamp).toDate()
        : null,
    duration: json['duration'] != null
        ? Duration(seconds: json['duration'])
        : null,
  );
}
