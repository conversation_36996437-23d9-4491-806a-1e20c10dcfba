// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'message_encryption_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_MessageEncryptionModel _$MessageEncryptionModelFromJson(
  Map<String, dynamic> json,
) => _MessageEncryptionModel(
  id: json['id'] as String,
  messageId: json['messageId'] as String,
  status: $enumDecode(_$EncryptionStatusEnumMap, json['status']),
  type: $enumDecode(_$EncryptionTypeEnumMap, json['type']),
  encryptedContent: json['encryptedContent'] as String,
  decryptedContent: json['decryptedContent'] as String?,
  senderPublicKey: json['senderPublicKey'] as String,
  recipientPublicKey: json['recipientPublicKey'] as String?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  decryptedAt: json['decryptedAt'] == null
      ? null
      : DateTime.parse(json['decryptedAt'] as String),
  metadata: MessageEncryptionMetadata.fromJson(
    json['metadata'] as Map<String, dynamic>,
  ),
);

Map<String, dynamic> _$MessageEncryptionModelToJson(
  _MessageEncryptionModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'messageId': instance.messageId,
  'status': _$EncryptionStatusEnumMap[instance.status]!,
  'type': _$EncryptionTypeEnumMap[instance.type]!,
  'encryptedContent': instance.encryptedContent,
  'decryptedContent': instance.decryptedContent,
  'senderPublicKey': instance.senderPublicKey,
  'recipientPublicKey': instance.recipientPublicKey,
  'createdAt': instance.createdAt.toIso8601String(),
  'decryptedAt': instance.decryptedAt?.toIso8601String(),
  'metadata': instance.metadata,
};

const _$EncryptionStatusEnumMap = {
  EncryptionStatus.notEncrypted: 'notEncrypted',
  EncryptionStatus.encrypting: 'encrypting',
  EncryptionStatus.encrypted: 'encrypted',
  EncryptionStatus.decrypting: 'decrypting',
  EncryptionStatus.decrypted: 'decrypted',
  EncryptionStatus.failed: 'failed',
  EncryptionStatus.keyExchangeRequired: 'keyExchangeRequired',
};

const _$EncryptionTypeEnumMap = {
  EncryptionType.none: 'none',
  EncryptionType.transport: 'transport',
  EncryptionType.endToEnd: 'endToEnd',
  EncryptionType.group: 'group',
  EncryptionType.custom: 'custom',
};

_MessageEncryptionMetadata _$MessageEncryptionMetadataFromJson(
  Map<String, dynamic> json,
) => _MessageEncryptionMetadata(
  algorithm: json['algorithm'] as String,
  keySize: json['keySize'] as String,
  sessionKey: json['sessionKey'] as String?,
  isForwardSecrecy: json['isForwardSecrecy'] as bool,
  keyExpiry: json['keyExpiry'] == null
      ? null
      : DateTime.parse(json['keyExpiry'] as String),
  additionalData: json['additionalData'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$MessageEncryptionMetadataToJson(
  _MessageEncryptionMetadata instance,
) => <String, dynamic>{
  'algorithm': instance.algorithm,
  'keySize': instance.keySize,
  'sessionKey': instance.sessionKey,
  'isForwardSecrecy': instance.isForwardSecrecy,
  'keyExpiry': instance.keyExpiry?.toIso8601String(),
  'additionalData': instance.additionalData,
};

_EncryptionKeyModel _$EncryptionKeyModelFromJson(Map<String, dynamic> json) =>
    _EncryptionKeyModel(
      id: json['id'] as String,
      userId: json['userId'] as String,
      publicKey: json['publicKey'] as String,
      privateKey: json['privateKey'] as String?,
      type: $enumDecode(_$KeyTypeEnumMap, json['type']),
      createdAt: DateTime.parse(json['createdAt'] as String),
      expiresAt: json['expiresAt'] == null
          ? null
          : DateTime.parse(json['expiresAt'] as String),
      isActive: json['isActive'] as bool,
      authorizedUsers: (json['authorizedUsers'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$EncryptionKeyModelToJson(_EncryptionKeyModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'publicKey': instance.publicKey,
      'privateKey': instance.privateKey,
      'type': _$KeyTypeEnumMap[instance.type]!,
      'createdAt': instance.createdAt.toIso8601String(),
      'expiresAt': instance.expiresAt?.toIso8601String(),
      'isActive': instance.isActive,
      'authorizedUsers': instance.authorizedUsers,
    };

const _$KeyTypeEnumMap = {
  KeyType.rsa: 'rsa',
  KeyType.ecc: 'ecc',
  KeyType.aes: 'aes',
  KeyType.custom: 'custom',
};

_EncryptionSettings _$EncryptionSettingsFromJson(
  Map<String, dynamic> json,
) => _EncryptionSettings(
  enableEndToEndEncryption: json['enableEndToEndEncryption'] as bool? ?? true,
  requireEncryption: json['requireEncryption'] as bool? ?? true,
  algorithm:
      $enumDecodeNullable(_$EncryptionAlgorithmEnumMap, json['algorithm']) ??
      EncryptionAlgorithm.aes256,
  keyExchange:
      $enumDecodeNullable(_$KeyExchangeProtocolEnumMap, json['keyExchange']) ??
      KeyExchangeProtocol.diffieHellman,
  enableForwardSecrecy: json['enableForwardSecrecy'] as bool? ?? true,
  keyRotationDays: (json['keyRotationDays'] as num?)?.toInt() ?? 30,
  showEncryptionStatus: json['showEncryptionStatus'] as bool? ?? true,
  verifyKeyFingerprints: json['verifyKeyFingerprints'] as bool? ?? true,
  allowUnencryptedMessages: json['allowUnencryptedMessages'] as bool? ?? false,
  autoEncryptMedia: json['autoEncryptMedia'] as bool? ?? true,
);

Map<String, dynamic> _$EncryptionSettingsToJson(_EncryptionSettings instance) =>
    <String, dynamic>{
      'enableEndToEndEncryption': instance.enableEndToEndEncryption,
      'requireEncryption': instance.requireEncryption,
      'algorithm': _$EncryptionAlgorithmEnumMap[instance.algorithm]!,
      'keyExchange': _$KeyExchangeProtocolEnumMap[instance.keyExchange]!,
      'enableForwardSecrecy': instance.enableForwardSecrecy,
      'keyRotationDays': instance.keyRotationDays,
      'showEncryptionStatus': instance.showEncryptionStatus,
      'verifyKeyFingerprints': instance.verifyKeyFingerprints,
      'allowUnencryptedMessages': instance.allowUnencryptedMessages,
      'autoEncryptMedia': instance.autoEncryptMedia,
    };

const _$EncryptionAlgorithmEnumMap = {
  EncryptionAlgorithm.aes128: 'aes128',
  EncryptionAlgorithm.aes256: 'aes256',
  EncryptionAlgorithm.chacha20: 'chacha20',
  EncryptionAlgorithm.custom: 'custom',
};

const _$KeyExchangeProtocolEnumMap = {
  KeyExchangeProtocol.diffieHellman: 'diffieHellman',
  KeyExchangeProtocol.rsa: 'rsa',
  KeyExchangeProtocol.ecc: 'ecc',
  KeyExchangeProtocol.custom: 'custom',
};

_KeyExchangeSession _$KeyExchangeSessionFromJson(Map<String, dynamic> json) =>
    _KeyExchangeSession(
      id: json['id'] as String,
      initiatorId: json['initiatorId'] as String,
      recipientId: json['recipientId'] as String,
      status: $enumDecode(_$KeyExchangeStatusEnumMap, json['status']),
      initiatorPublicKey: json['initiatorPublicKey'] as String,
      recipientPublicKey: json['recipientPublicKey'] as String?,
      sharedSecret: json['sharedSecret'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
      expiresAt: json['expiresAt'] == null
          ? null
          : DateTime.parse(json['expiresAt'] as String),
    );

Map<String, dynamic> _$KeyExchangeSessionToJson(_KeyExchangeSession instance) =>
    <String, dynamic>{
      'id': instance.id,
      'initiatorId': instance.initiatorId,
      'recipientId': instance.recipientId,
      'status': _$KeyExchangeStatusEnumMap[instance.status]!,
      'initiatorPublicKey': instance.initiatorPublicKey,
      'recipientPublicKey': instance.recipientPublicKey,
      'sharedSecret': instance.sharedSecret,
      'createdAt': instance.createdAt.toIso8601String(),
      'completedAt': instance.completedAt?.toIso8601String(),
      'expiresAt': instance.expiresAt?.toIso8601String(),
    };

const _$KeyExchangeStatusEnumMap = {
  KeyExchangeStatus.initiated: 'initiated',
  KeyExchangeStatus.pending: 'pending',
  KeyExchangeStatus.completed: 'completed',
  KeyExchangeStatus.failed: 'failed',
  KeyExchangeStatus.expired: 'expired',
};
