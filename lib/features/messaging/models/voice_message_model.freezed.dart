// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'voice_message_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$VoiceMessageModel {

 String get id; String get messageId; String get audioUrl; int get durationInSeconds; String? get transcription; VoiceMessageStatus get status; DateTime get createdAt; DateTime? get playedAt; List<VoiceMessageReaction> get reactions; VoiceMessageMetadata get metadata;
/// Create a copy of VoiceMessageModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VoiceMessageModelCopyWith<VoiceMessageModel> get copyWith => _$VoiceMessageModelCopyWithImpl<VoiceMessageModel>(this as VoiceMessageModel, _$identity);

  /// Serializes this VoiceMessageModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VoiceMessageModel&&(identical(other.id, id) || other.id == id)&&(identical(other.messageId, messageId) || other.messageId == messageId)&&(identical(other.audioUrl, audioUrl) || other.audioUrl == audioUrl)&&(identical(other.durationInSeconds, durationInSeconds) || other.durationInSeconds == durationInSeconds)&&(identical(other.transcription, transcription) || other.transcription == transcription)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.playedAt, playedAt) || other.playedAt == playedAt)&&const DeepCollectionEquality().equals(other.reactions, reactions)&&(identical(other.metadata, metadata) || other.metadata == metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,messageId,audioUrl,durationInSeconds,transcription,status,createdAt,playedAt,const DeepCollectionEquality().hash(reactions),metadata);

@override
String toString() {
  return 'VoiceMessageModel(id: $id, messageId: $messageId, audioUrl: $audioUrl, durationInSeconds: $durationInSeconds, transcription: $transcription, status: $status, createdAt: $createdAt, playedAt: $playedAt, reactions: $reactions, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $VoiceMessageModelCopyWith<$Res>  {
  factory $VoiceMessageModelCopyWith(VoiceMessageModel value, $Res Function(VoiceMessageModel) _then) = _$VoiceMessageModelCopyWithImpl;
@useResult
$Res call({
 String id, String messageId, String audioUrl, int durationInSeconds, String? transcription, VoiceMessageStatus status, DateTime createdAt, DateTime? playedAt, List<VoiceMessageReaction> reactions, VoiceMessageMetadata metadata
});


$VoiceMessageMetadataCopyWith<$Res> get metadata;

}
/// @nodoc
class _$VoiceMessageModelCopyWithImpl<$Res>
    implements $VoiceMessageModelCopyWith<$Res> {
  _$VoiceMessageModelCopyWithImpl(this._self, this._then);

  final VoiceMessageModel _self;
  final $Res Function(VoiceMessageModel) _then;

/// Create a copy of VoiceMessageModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? messageId = null,Object? audioUrl = null,Object? durationInSeconds = null,Object? transcription = freezed,Object? status = null,Object? createdAt = null,Object? playedAt = freezed,Object? reactions = null,Object? metadata = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,messageId: null == messageId ? _self.messageId : messageId // ignore: cast_nullable_to_non_nullable
as String,audioUrl: null == audioUrl ? _self.audioUrl : audioUrl // ignore: cast_nullable_to_non_nullable
as String,durationInSeconds: null == durationInSeconds ? _self.durationInSeconds : durationInSeconds // ignore: cast_nullable_to_non_nullable
as int,transcription: freezed == transcription ? _self.transcription : transcription // ignore: cast_nullable_to_non_nullable
as String?,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as VoiceMessageStatus,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,playedAt: freezed == playedAt ? _self.playedAt : playedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,reactions: null == reactions ? _self.reactions : reactions // ignore: cast_nullable_to_non_nullable
as List<VoiceMessageReaction>,metadata: null == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as VoiceMessageMetadata,
  ));
}
/// Create a copy of VoiceMessageModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$VoiceMessageMetadataCopyWith<$Res> get metadata {
  
  return $VoiceMessageMetadataCopyWith<$Res>(_self.metadata, (value) {
    return _then(_self.copyWith(metadata: value));
  });
}
}


/// Adds pattern-matching-related methods to [VoiceMessageModel].
extension VoiceMessageModelPatterns on VoiceMessageModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VoiceMessageModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VoiceMessageModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VoiceMessageModel value)  $default,){
final _that = this;
switch (_that) {
case _VoiceMessageModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VoiceMessageModel value)?  $default,){
final _that = this;
switch (_that) {
case _VoiceMessageModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String messageId,  String audioUrl,  int durationInSeconds,  String? transcription,  VoiceMessageStatus status,  DateTime createdAt,  DateTime? playedAt,  List<VoiceMessageReaction> reactions,  VoiceMessageMetadata metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VoiceMessageModel() when $default != null:
return $default(_that.id,_that.messageId,_that.audioUrl,_that.durationInSeconds,_that.transcription,_that.status,_that.createdAt,_that.playedAt,_that.reactions,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String messageId,  String audioUrl,  int durationInSeconds,  String? transcription,  VoiceMessageStatus status,  DateTime createdAt,  DateTime? playedAt,  List<VoiceMessageReaction> reactions,  VoiceMessageMetadata metadata)  $default,) {final _that = this;
switch (_that) {
case _VoiceMessageModel():
return $default(_that.id,_that.messageId,_that.audioUrl,_that.durationInSeconds,_that.transcription,_that.status,_that.createdAt,_that.playedAt,_that.reactions,_that.metadata);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String messageId,  String audioUrl,  int durationInSeconds,  String? transcription,  VoiceMessageStatus status,  DateTime createdAt,  DateTime? playedAt,  List<VoiceMessageReaction> reactions,  VoiceMessageMetadata metadata)?  $default,) {final _that = this;
switch (_that) {
case _VoiceMessageModel() when $default != null:
return $default(_that.id,_that.messageId,_that.audioUrl,_that.durationInSeconds,_that.transcription,_that.status,_that.createdAt,_that.playedAt,_that.reactions,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VoiceMessageModel implements VoiceMessageModel {
  const _VoiceMessageModel({required this.id, required this.messageId, required this.audioUrl, required this.durationInSeconds, required this.transcription, required this.status, required this.createdAt, required this.playedAt, required final  List<VoiceMessageReaction> reactions, required this.metadata}): _reactions = reactions;
  factory _VoiceMessageModel.fromJson(Map<String, dynamic> json) => _$VoiceMessageModelFromJson(json);

@override final  String id;
@override final  String messageId;
@override final  String audioUrl;
@override final  int durationInSeconds;
@override final  String? transcription;
@override final  VoiceMessageStatus status;
@override final  DateTime createdAt;
@override final  DateTime? playedAt;
 final  List<VoiceMessageReaction> _reactions;
@override List<VoiceMessageReaction> get reactions {
  if (_reactions is EqualUnmodifiableListView) return _reactions;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_reactions);
}

@override final  VoiceMessageMetadata metadata;

/// Create a copy of VoiceMessageModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VoiceMessageModelCopyWith<_VoiceMessageModel> get copyWith => __$VoiceMessageModelCopyWithImpl<_VoiceMessageModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VoiceMessageModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VoiceMessageModel&&(identical(other.id, id) || other.id == id)&&(identical(other.messageId, messageId) || other.messageId == messageId)&&(identical(other.audioUrl, audioUrl) || other.audioUrl == audioUrl)&&(identical(other.durationInSeconds, durationInSeconds) || other.durationInSeconds == durationInSeconds)&&(identical(other.transcription, transcription) || other.transcription == transcription)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.playedAt, playedAt) || other.playedAt == playedAt)&&const DeepCollectionEquality().equals(other._reactions, _reactions)&&(identical(other.metadata, metadata) || other.metadata == metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,messageId,audioUrl,durationInSeconds,transcription,status,createdAt,playedAt,const DeepCollectionEquality().hash(_reactions),metadata);

@override
String toString() {
  return 'VoiceMessageModel(id: $id, messageId: $messageId, audioUrl: $audioUrl, durationInSeconds: $durationInSeconds, transcription: $transcription, status: $status, createdAt: $createdAt, playedAt: $playedAt, reactions: $reactions, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$VoiceMessageModelCopyWith<$Res> implements $VoiceMessageModelCopyWith<$Res> {
  factory _$VoiceMessageModelCopyWith(_VoiceMessageModel value, $Res Function(_VoiceMessageModel) _then) = __$VoiceMessageModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String messageId, String audioUrl, int durationInSeconds, String? transcription, VoiceMessageStatus status, DateTime createdAt, DateTime? playedAt, List<VoiceMessageReaction> reactions, VoiceMessageMetadata metadata
});


@override $VoiceMessageMetadataCopyWith<$Res> get metadata;

}
/// @nodoc
class __$VoiceMessageModelCopyWithImpl<$Res>
    implements _$VoiceMessageModelCopyWith<$Res> {
  __$VoiceMessageModelCopyWithImpl(this._self, this._then);

  final _VoiceMessageModel _self;
  final $Res Function(_VoiceMessageModel) _then;

/// Create a copy of VoiceMessageModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? messageId = null,Object? audioUrl = null,Object? durationInSeconds = null,Object? transcription = freezed,Object? status = null,Object? createdAt = null,Object? playedAt = freezed,Object? reactions = null,Object? metadata = null,}) {
  return _then(_VoiceMessageModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,messageId: null == messageId ? _self.messageId : messageId // ignore: cast_nullable_to_non_nullable
as String,audioUrl: null == audioUrl ? _self.audioUrl : audioUrl // ignore: cast_nullable_to_non_nullable
as String,durationInSeconds: null == durationInSeconds ? _self.durationInSeconds : durationInSeconds // ignore: cast_nullable_to_non_nullable
as int,transcription: freezed == transcription ? _self.transcription : transcription // ignore: cast_nullable_to_non_nullable
as String?,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as VoiceMessageStatus,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,playedAt: freezed == playedAt ? _self.playedAt : playedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,reactions: null == reactions ? _self._reactions : reactions // ignore: cast_nullable_to_non_nullable
as List<VoiceMessageReaction>,metadata: null == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as VoiceMessageMetadata,
  ));
}

/// Create a copy of VoiceMessageModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$VoiceMessageMetadataCopyWith<$Res> get metadata {
  
  return $VoiceMessageMetadataCopyWith<$Res>(_self.metadata, (value) {
    return _then(_self.copyWith(metadata: value));
  });
}
}


/// @nodoc
mixin _$VoiceMessageReaction {

 String get userId; VoiceReactionType get type; DateTime get timestamp;
/// Create a copy of VoiceMessageReaction
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VoiceMessageReactionCopyWith<VoiceMessageReaction> get copyWith => _$VoiceMessageReactionCopyWithImpl<VoiceMessageReaction>(this as VoiceMessageReaction, _$identity);

  /// Serializes this VoiceMessageReaction to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VoiceMessageReaction&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.type, type) || other.type == type)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,userId,type,timestamp);

@override
String toString() {
  return 'VoiceMessageReaction(userId: $userId, type: $type, timestamp: $timestamp)';
}


}

/// @nodoc
abstract mixin class $VoiceMessageReactionCopyWith<$Res>  {
  factory $VoiceMessageReactionCopyWith(VoiceMessageReaction value, $Res Function(VoiceMessageReaction) _then) = _$VoiceMessageReactionCopyWithImpl;
@useResult
$Res call({
 String userId, VoiceReactionType type, DateTime timestamp
});




}
/// @nodoc
class _$VoiceMessageReactionCopyWithImpl<$Res>
    implements $VoiceMessageReactionCopyWith<$Res> {
  _$VoiceMessageReactionCopyWithImpl(this._self, this._then);

  final VoiceMessageReaction _self;
  final $Res Function(VoiceMessageReaction) _then;

/// Create a copy of VoiceMessageReaction
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? userId = null,Object? type = null,Object? timestamp = null,}) {
  return _then(_self.copyWith(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as VoiceReactionType,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// Adds pattern-matching-related methods to [VoiceMessageReaction].
extension VoiceMessageReactionPatterns on VoiceMessageReaction {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VoiceMessageReaction value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VoiceMessageReaction() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VoiceMessageReaction value)  $default,){
final _that = this;
switch (_that) {
case _VoiceMessageReaction():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VoiceMessageReaction value)?  $default,){
final _that = this;
switch (_that) {
case _VoiceMessageReaction() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String userId,  VoiceReactionType type,  DateTime timestamp)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VoiceMessageReaction() when $default != null:
return $default(_that.userId,_that.type,_that.timestamp);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String userId,  VoiceReactionType type,  DateTime timestamp)  $default,) {final _that = this;
switch (_that) {
case _VoiceMessageReaction():
return $default(_that.userId,_that.type,_that.timestamp);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String userId,  VoiceReactionType type,  DateTime timestamp)?  $default,) {final _that = this;
switch (_that) {
case _VoiceMessageReaction() when $default != null:
return $default(_that.userId,_that.type,_that.timestamp);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VoiceMessageReaction implements VoiceMessageReaction {
  const _VoiceMessageReaction({required this.userId, required this.type, required this.timestamp});
  factory _VoiceMessageReaction.fromJson(Map<String, dynamic> json) => _$VoiceMessageReactionFromJson(json);

@override final  String userId;
@override final  VoiceReactionType type;
@override final  DateTime timestamp;

/// Create a copy of VoiceMessageReaction
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VoiceMessageReactionCopyWith<_VoiceMessageReaction> get copyWith => __$VoiceMessageReactionCopyWithImpl<_VoiceMessageReaction>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VoiceMessageReactionToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VoiceMessageReaction&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.type, type) || other.type == type)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,userId,type,timestamp);

@override
String toString() {
  return 'VoiceMessageReaction(userId: $userId, type: $type, timestamp: $timestamp)';
}


}

/// @nodoc
abstract mixin class _$VoiceMessageReactionCopyWith<$Res> implements $VoiceMessageReactionCopyWith<$Res> {
  factory _$VoiceMessageReactionCopyWith(_VoiceMessageReaction value, $Res Function(_VoiceMessageReaction) _then) = __$VoiceMessageReactionCopyWithImpl;
@override @useResult
$Res call({
 String userId, VoiceReactionType type, DateTime timestamp
});




}
/// @nodoc
class __$VoiceMessageReactionCopyWithImpl<$Res>
    implements _$VoiceMessageReactionCopyWith<$Res> {
  __$VoiceMessageReactionCopyWithImpl(this._self, this._then);

  final _VoiceMessageReaction _self;
  final $Res Function(_VoiceMessageReaction) _then;

/// Create a copy of VoiceMessageReaction
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? userId = null,Object? type = null,Object? timestamp = null,}) {
  return _then(_VoiceMessageReaction(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as VoiceReactionType,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}


/// @nodoc
mixin _$VoiceMessageMetadata {

 String get fileSize; String get audioFormat; int get sampleRate; int get bitRate; bool get isCompressed; String? get recordingDevice; Map<String, dynamic>? get additionalData;
/// Create a copy of VoiceMessageMetadata
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VoiceMessageMetadataCopyWith<VoiceMessageMetadata> get copyWith => _$VoiceMessageMetadataCopyWithImpl<VoiceMessageMetadata>(this as VoiceMessageMetadata, _$identity);

  /// Serializes this VoiceMessageMetadata to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VoiceMessageMetadata&&(identical(other.fileSize, fileSize) || other.fileSize == fileSize)&&(identical(other.audioFormat, audioFormat) || other.audioFormat == audioFormat)&&(identical(other.sampleRate, sampleRate) || other.sampleRate == sampleRate)&&(identical(other.bitRate, bitRate) || other.bitRate == bitRate)&&(identical(other.isCompressed, isCompressed) || other.isCompressed == isCompressed)&&(identical(other.recordingDevice, recordingDevice) || other.recordingDevice == recordingDevice)&&const DeepCollectionEquality().equals(other.additionalData, additionalData));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,fileSize,audioFormat,sampleRate,bitRate,isCompressed,recordingDevice,const DeepCollectionEquality().hash(additionalData));

@override
String toString() {
  return 'VoiceMessageMetadata(fileSize: $fileSize, audioFormat: $audioFormat, sampleRate: $sampleRate, bitRate: $bitRate, isCompressed: $isCompressed, recordingDevice: $recordingDevice, additionalData: $additionalData)';
}


}

/// @nodoc
abstract mixin class $VoiceMessageMetadataCopyWith<$Res>  {
  factory $VoiceMessageMetadataCopyWith(VoiceMessageMetadata value, $Res Function(VoiceMessageMetadata) _then) = _$VoiceMessageMetadataCopyWithImpl;
@useResult
$Res call({
 String fileSize, String audioFormat, int sampleRate, int bitRate, bool isCompressed, String? recordingDevice, Map<String, dynamic>? additionalData
});




}
/// @nodoc
class _$VoiceMessageMetadataCopyWithImpl<$Res>
    implements $VoiceMessageMetadataCopyWith<$Res> {
  _$VoiceMessageMetadataCopyWithImpl(this._self, this._then);

  final VoiceMessageMetadata _self;
  final $Res Function(VoiceMessageMetadata) _then;

/// Create a copy of VoiceMessageMetadata
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? fileSize = null,Object? audioFormat = null,Object? sampleRate = null,Object? bitRate = null,Object? isCompressed = null,Object? recordingDevice = freezed,Object? additionalData = freezed,}) {
  return _then(_self.copyWith(
fileSize: null == fileSize ? _self.fileSize : fileSize // ignore: cast_nullable_to_non_nullable
as String,audioFormat: null == audioFormat ? _self.audioFormat : audioFormat // ignore: cast_nullable_to_non_nullable
as String,sampleRate: null == sampleRate ? _self.sampleRate : sampleRate // ignore: cast_nullable_to_non_nullable
as int,bitRate: null == bitRate ? _self.bitRate : bitRate // ignore: cast_nullable_to_non_nullable
as int,isCompressed: null == isCompressed ? _self.isCompressed : isCompressed // ignore: cast_nullable_to_non_nullable
as bool,recordingDevice: freezed == recordingDevice ? _self.recordingDevice : recordingDevice // ignore: cast_nullable_to_non_nullable
as String?,additionalData: freezed == additionalData ? _self.additionalData : additionalData // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [VoiceMessageMetadata].
extension VoiceMessageMetadataPatterns on VoiceMessageMetadata {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VoiceMessageMetadata value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VoiceMessageMetadata() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VoiceMessageMetadata value)  $default,){
final _that = this;
switch (_that) {
case _VoiceMessageMetadata():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VoiceMessageMetadata value)?  $default,){
final _that = this;
switch (_that) {
case _VoiceMessageMetadata() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String fileSize,  String audioFormat,  int sampleRate,  int bitRate,  bool isCompressed,  String? recordingDevice,  Map<String, dynamic>? additionalData)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VoiceMessageMetadata() when $default != null:
return $default(_that.fileSize,_that.audioFormat,_that.sampleRate,_that.bitRate,_that.isCompressed,_that.recordingDevice,_that.additionalData);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String fileSize,  String audioFormat,  int sampleRate,  int bitRate,  bool isCompressed,  String? recordingDevice,  Map<String, dynamic>? additionalData)  $default,) {final _that = this;
switch (_that) {
case _VoiceMessageMetadata():
return $default(_that.fileSize,_that.audioFormat,_that.sampleRate,_that.bitRate,_that.isCompressed,_that.recordingDevice,_that.additionalData);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String fileSize,  String audioFormat,  int sampleRate,  int bitRate,  bool isCompressed,  String? recordingDevice,  Map<String, dynamic>? additionalData)?  $default,) {final _that = this;
switch (_that) {
case _VoiceMessageMetadata() when $default != null:
return $default(_that.fileSize,_that.audioFormat,_that.sampleRate,_that.bitRate,_that.isCompressed,_that.recordingDevice,_that.additionalData);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VoiceMessageMetadata implements VoiceMessageMetadata {
  const _VoiceMessageMetadata({required this.fileSize, required this.audioFormat, required this.sampleRate, required this.bitRate, required this.isCompressed, required this.recordingDevice, required final  Map<String, dynamic>? additionalData}): _additionalData = additionalData;
  factory _VoiceMessageMetadata.fromJson(Map<String, dynamic> json) => _$VoiceMessageMetadataFromJson(json);

@override final  String fileSize;
@override final  String audioFormat;
@override final  int sampleRate;
@override final  int bitRate;
@override final  bool isCompressed;
@override final  String? recordingDevice;
 final  Map<String, dynamic>? _additionalData;
@override Map<String, dynamic>? get additionalData {
  final value = _additionalData;
  if (value == null) return null;
  if (_additionalData is EqualUnmodifiableMapView) return _additionalData;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of VoiceMessageMetadata
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VoiceMessageMetadataCopyWith<_VoiceMessageMetadata> get copyWith => __$VoiceMessageMetadataCopyWithImpl<_VoiceMessageMetadata>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VoiceMessageMetadataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VoiceMessageMetadata&&(identical(other.fileSize, fileSize) || other.fileSize == fileSize)&&(identical(other.audioFormat, audioFormat) || other.audioFormat == audioFormat)&&(identical(other.sampleRate, sampleRate) || other.sampleRate == sampleRate)&&(identical(other.bitRate, bitRate) || other.bitRate == bitRate)&&(identical(other.isCompressed, isCompressed) || other.isCompressed == isCompressed)&&(identical(other.recordingDevice, recordingDevice) || other.recordingDevice == recordingDevice)&&const DeepCollectionEquality().equals(other._additionalData, _additionalData));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,fileSize,audioFormat,sampleRate,bitRate,isCompressed,recordingDevice,const DeepCollectionEquality().hash(_additionalData));

@override
String toString() {
  return 'VoiceMessageMetadata(fileSize: $fileSize, audioFormat: $audioFormat, sampleRate: $sampleRate, bitRate: $bitRate, isCompressed: $isCompressed, recordingDevice: $recordingDevice, additionalData: $additionalData)';
}


}

/// @nodoc
abstract mixin class _$VoiceMessageMetadataCopyWith<$Res> implements $VoiceMessageMetadataCopyWith<$Res> {
  factory _$VoiceMessageMetadataCopyWith(_VoiceMessageMetadata value, $Res Function(_VoiceMessageMetadata) _then) = __$VoiceMessageMetadataCopyWithImpl;
@override @useResult
$Res call({
 String fileSize, String audioFormat, int sampleRate, int bitRate, bool isCompressed, String? recordingDevice, Map<String, dynamic>? additionalData
});




}
/// @nodoc
class __$VoiceMessageMetadataCopyWithImpl<$Res>
    implements _$VoiceMessageMetadataCopyWith<$Res> {
  __$VoiceMessageMetadataCopyWithImpl(this._self, this._then);

  final _VoiceMessageMetadata _self;
  final $Res Function(_VoiceMessageMetadata) _then;

/// Create a copy of VoiceMessageMetadata
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? fileSize = null,Object? audioFormat = null,Object? sampleRate = null,Object? bitRate = null,Object? isCompressed = null,Object? recordingDevice = freezed,Object? additionalData = freezed,}) {
  return _then(_VoiceMessageMetadata(
fileSize: null == fileSize ? _self.fileSize : fileSize // ignore: cast_nullable_to_non_nullable
as String,audioFormat: null == audioFormat ? _self.audioFormat : audioFormat // ignore: cast_nullable_to_non_nullable
as String,sampleRate: null == sampleRate ? _self.sampleRate : sampleRate // ignore: cast_nullable_to_non_nullable
as int,bitRate: null == bitRate ? _self.bitRate : bitRate // ignore: cast_nullable_to_non_nullable
as int,isCompressed: null == isCompressed ? _self.isCompressed : isCompressed // ignore: cast_nullable_to_non_nullable
as bool,recordingDevice: freezed == recordingDevice ? _self.recordingDevice : recordingDevice // ignore: cast_nullable_to_non_nullable
as String?,additionalData: freezed == additionalData ? _self._additionalData : additionalData // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}


/// @nodoc
mixin _$VoiceMessageSettings {

 bool get autoTranscribe; bool get allowReactions; int get maxDurationSeconds;// 5 minutes
 int get minDurationSeconds;// 10 seconds
 bool get showWaveform; bool get allowPlaybackSpeed; List<String> get playbackSpeeds; bool get saveToGallery; bool get requireConfirmation;
/// Create a copy of VoiceMessageSettings
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VoiceMessageSettingsCopyWith<VoiceMessageSettings> get copyWith => _$VoiceMessageSettingsCopyWithImpl<VoiceMessageSettings>(this as VoiceMessageSettings, _$identity);

  /// Serializes this VoiceMessageSettings to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VoiceMessageSettings&&(identical(other.autoTranscribe, autoTranscribe) || other.autoTranscribe == autoTranscribe)&&(identical(other.allowReactions, allowReactions) || other.allowReactions == allowReactions)&&(identical(other.maxDurationSeconds, maxDurationSeconds) || other.maxDurationSeconds == maxDurationSeconds)&&(identical(other.minDurationSeconds, minDurationSeconds) || other.minDurationSeconds == minDurationSeconds)&&(identical(other.showWaveform, showWaveform) || other.showWaveform == showWaveform)&&(identical(other.allowPlaybackSpeed, allowPlaybackSpeed) || other.allowPlaybackSpeed == allowPlaybackSpeed)&&const DeepCollectionEquality().equals(other.playbackSpeeds, playbackSpeeds)&&(identical(other.saveToGallery, saveToGallery) || other.saveToGallery == saveToGallery)&&(identical(other.requireConfirmation, requireConfirmation) || other.requireConfirmation == requireConfirmation));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,autoTranscribe,allowReactions,maxDurationSeconds,minDurationSeconds,showWaveform,allowPlaybackSpeed,const DeepCollectionEquality().hash(playbackSpeeds),saveToGallery,requireConfirmation);

@override
String toString() {
  return 'VoiceMessageSettings(autoTranscribe: $autoTranscribe, allowReactions: $allowReactions, maxDurationSeconds: $maxDurationSeconds, minDurationSeconds: $minDurationSeconds, showWaveform: $showWaveform, allowPlaybackSpeed: $allowPlaybackSpeed, playbackSpeeds: $playbackSpeeds, saveToGallery: $saveToGallery, requireConfirmation: $requireConfirmation)';
}


}

/// @nodoc
abstract mixin class $VoiceMessageSettingsCopyWith<$Res>  {
  factory $VoiceMessageSettingsCopyWith(VoiceMessageSettings value, $Res Function(VoiceMessageSettings) _then) = _$VoiceMessageSettingsCopyWithImpl;
@useResult
$Res call({
 bool autoTranscribe, bool allowReactions, int maxDurationSeconds, int minDurationSeconds, bool showWaveform, bool allowPlaybackSpeed, List<String> playbackSpeeds, bool saveToGallery, bool requireConfirmation
});




}
/// @nodoc
class _$VoiceMessageSettingsCopyWithImpl<$Res>
    implements $VoiceMessageSettingsCopyWith<$Res> {
  _$VoiceMessageSettingsCopyWithImpl(this._self, this._then);

  final VoiceMessageSettings _self;
  final $Res Function(VoiceMessageSettings) _then;

/// Create a copy of VoiceMessageSettings
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? autoTranscribe = null,Object? allowReactions = null,Object? maxDurationSeconds = null,Object? minDurationSeconds = null,Object? showWaveform = null,Object? allowPlaybackSpeed = null,Object? playbackSpeeds = null,Object? saveToGallery = null,Object? requireConfirmation = null,}) {
  return _then(_self.copyWith(
autoTranscribe: null == autoTranscribe ? _self.autoTranscribe : autoTranscribe // ignore: cast_nullable_to_non_nullable
as bool,allowReactions: null == allowReactions ? _self.allowReactions : allowReactions // ignore: cast_nullable_to_non_nullable
as bool,maxDurationSeconds: null == maxDurationSeconds ? _self.maxDurationSeconds : maxDurationSeconds // ignore: cast_nullable_to_non_nullable
as int,minDurationSeconds: null == minDurationSeconds ? _self.minDurationSeconds : minDurationSeconds // ignore: cast_nullable_to_non_nullable
as int,showWaveform: null == showWaveform ? _self.showWaveform : showWaveform // ignore: cast_nullable_to_non_nullable
as bool,allowPlaybackSpeed: null == allowPlaybackSpeed ? _self.allowPlaybackSpeed : allowPlaybackSpeed // ignore: cast_nullable_to_non_nullable
as bool,playbackSpeeds: null == playbackSpeeds ? _self.playbackSpeeds : playbackSpeeds // ignore: cast_nullable_to_non_nullable
as List<String>,saveToGallery: null == saveToGallery ? _self.saveToGallery : saveToGallery // ignore: cast_nullable_to_non_nullable
as bool,requireConfirmation: null == requireConfirmation ? _self.requireConfirmation : requireConfirmation // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [VoiceMessageSettings].
extension VoiceMessageSettingsPatterns on VoiceMessageSettings {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VoiceMessageSettings value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VoiceMessageSettings() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VoiceMessageSettings value)  $default,){
final _that = this;
switch (_that) {
case _VoiceMessageSettings():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VoiceMessageSettings value)?  $default,){
final _that = this;
switch (_that) {
case _VoiceMessageSettings() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( bool autoTranscribe,  bool allowReactions,  int maxDurationSeconds,  int minDurationSeconds,  bool showWaveform,  bool allowPlaybackSpeed,  List<String> playbackSpeeds,  bool saveToGallery,  bool requireConfirmation)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VoiceMessageSettings() when $default != null:
return $default(_that.autoTranscribe,_that.allowReactions,_that.maxDurationSeconds,_that.minDurationSeconds,_that.showWaveform,_that.allowPlaybackSpeed,_that.playbackSpeeds,_that.saveToGallery,_that.requireConfirmation);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( bool autoTranscribe,  bool allowReactions,  int maxDurationSeconds,  int minDurationSeconds,  bool showWaveform,  bool allowPlaybackSpeed,  List<String> playbackSpeeds,  bool saveToGallery,  bool requireConfirmation)  $default,) {final _that = this;
switch (_that) {
case _VoiceMessageSettings():
return $default(_that.autoTranscribe,_that.allowReactions,_that.maxDurationSeconds,_that.minDurationSeconds,_that.showWaveform,_that.allowPlaybackSpeed,_that.playbackSpeeds,_that.saveToGallery,_that.requireConfirmation);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( bool autoTranscribe,  bool allowReactions,  int maxDurationSeconds,  int minDurationSeconds,  bool showWaveform,  bool allowPlaybackSpeed,  List<String> playbackSpeeds,  bool saveToGallery,  bool requireConfirmation)?  $default,) {final _that = this;
switch (_that) {
case _VoiceMessageSettings() when $default != null:
return $default(_that.autoTranscribe,_that.allowReactions,_that.maxDurationSeconds,_that.minDurationSeconds,_that.showWaveform,_that.allowPlaybackSpeed,_that.playbackSpeeds,_that.saveToGallery,_that.requireConfirmation);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VoiceMessageSettings implements VoiceMessageSettings {
  const _VoiceMessageSettings({this.autoTranscribe = true, this.allowReactions = true, this.maxDurationSeconds = 300, this.minDurationSeconds = 10, this.showWaveform = true, this.allowPlaybackSpeed = true, final  List<String> playbackSpeeds = const ['0.5x', '1x', '1.5x', '2x'], this.saveToGallery = true, this.requireConfirmation = false}): _playbackSpeeds = playbackSpeeds;
  factory _VoiceMessageSettings.fromJson(Map<String, dynamic> json) => _$VoiceMessageSettingsFromJson(json);

@override@JsonKey() final  bool autoTranscribe;
@override@JsonKey() final  bool allowReactions;
@override@JsonKey() final  int maxDurationSeconds;
// 5 minutes
@override@JsonKey() final  int minDurationSeconds;
// 10 seconds
@override@JsonKey() final  bool showWaveform;
@override@JsonKey() final  bool allowPlaybackSpeed;
 final  List<String> _playbackSpeeds;
@override@JsonKey() List<String> get playbackSpeeds {
  if (_playbackSpeeds is EqualUnmodifiableListView) return _playbackSpeeds;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_playbackSpeeds);
}

@override@JsonKey() final  bool saveToGallery;
@override@JsonKey() final  bool requireConfirmation;

/// Create a copy of VoiceMessageSettings
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VoiceMessageSettingsCopyWith<_VoiceMessageSettings> get copyWith => __$VoiceMessageSettingsCopyWithImpl<_VoiceMessageSettings>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VoiceMessageSettingsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VoiceMessageSettings&&(identical(other.autoTranscribe, autoTranscribe) || other.autoTranscribe == autoTranscribe)&&(identical(other.allowReactions, allowReactions) || other.allowReactions == allowReactions)&&(identical(other.maxDurationSeconds, maxDurationSeconds) || other.maxDurationSeconds == maxDurationSeconds)&&(identical(other.minDurationSeconds, minDurationSeconds) || other.minDurationSeconds == minDurationSeconds)&&(identical(other.showWaveform, showWaveform) || other.showWaveform == showWaveform)&&(identical(other.allowPlaybackSpeed, allowPlaybackSpeed) || other.allowPlaybackSpeed == allowPlaybackSpeed)&&const DeepCollectionEquality().equals(other._playbackSpeeds, _playbackSpeeds)&&(identical(other.saveToGallery, saveToGallery) || other.saveToGallery == saveToGallery)&&(identical(other.requireConfirmation, requireConfirmation) || other.requireConfirmation == requireConfirmation));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,autoTranscribe,allowReactions,maxDurationSeconds,minDurationSeconds,showWaveform,allowPlaybackSpeed,const DeepCollectionEquality().hash(_playbackSpeeds),saveToGallery,requireConfirmation);

@override
String toString() {
  return 'VoiceMessageSettings(autoTranscribe: $autoTranscribe, allowReactions: $allowReactions, maxDurationSeconds: $maxDurationSeconds, minDurationSeconds: $minDurationSeconds, showWaveform: $showWaveform, allowPlaybackSpeed: $allowPlaybackSpeed, playbackSpeeds: $playbackSpeeds, saveToGallery: $saveToGallery, requireConfirmation: $requireConfirmation)';
}


}

/// @nodoc
abstract mixin class _$VoiceMessageSettingsCopyWith<$Res> implements $VoiceMessageSettingsCopyWith<$Res> {
  factory _$VoiceMessageSettingsCopyWith(_VoiceMessageSettings value, $Res Function(_VoiceMessageSettings) _then) = __$VoiceMessageSettingsCopyWithImpl;
@override @useResult
$Res call({
 bool autoTranscribe, bool allowReactions, int maxDurationSeconds, int minDurationSeconds, bool showWaveform, bool allowPlaybackSpeed, List<String> playbackSpeeds, bool saveToGallery, bool requireConfirmation
});




}
/// @nodoc
class __$VoiceMessageSettingsCopyWithImpl<$Res>
    implements _$VoiceMessageSettingsCopyWith<$Res> {
  __$VoiceMessageSettingsCopyWithImpl(this._self, this._then);

  final _VoiceMessageSettings _self;
  final $Res Function(_VoiceMessageSettings) _then;

/// Create a copy of VoiceMessageSettings
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? autoTranscribe = null,Object? allowReactions = null,Object? maxDurationSeconds = null,Object? minDurationSeconds = null,Object? showWaveform = null,Object? allowPlaybackSpeed = null,Object? playbackSpeeds = null,Object? saveToGallery = null,Object? requireConfirmation = null,}) {
  return _then(_VoiceMessageSettings(
autoTranscribe: null == autoTranscribe ? _self.autoTranscribe : autoTranscribe // ignore: cast_nullable_to_non_nullable
as bool,allowReactions: null == allowReactions ? _self.allowReactions : allowReactions // ignore: cast_nullable_to_non_nullable
as bool,maxDurationSeconds: null == maxDurationSeconds ? _self.maxDurationSeconds : maxDurationSeconds // ignore: cast_nullable_to_non_nullable
as int,minDurationSeconds: null == minDurationSeconds ? _self.minDurationSeconds : minDurationSeconds // ignore: cast_nullable_to_non_nullable
as int,showWaveform: null == showWaveform ? _self.showWaveform : showWaveform // ignore: cast_nullable_to_non_nullable
as bool,allowPlaybackSpeed: null == allowPlaybackSpeed ? _self.allowPlaybackSpeed : allowPlaybackSpeed // ignore: cast_nullable_to_non_nullable
as bool,playbackSpeeds: null == playbackSpeeds ? _self._playbackSpeeds : playbackSpeeds // ignore: cast_nullable_to_non_nullable
as List<String>,saveToGallery: null == saveToGallery ? _self.saveToGallery : saveToGallery // ignore: cast_nullable_to_non_nullable
as bool,requireConfirmation: null == requireConfirmation ? _self.requireConfirmation : requireConfirmation // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
