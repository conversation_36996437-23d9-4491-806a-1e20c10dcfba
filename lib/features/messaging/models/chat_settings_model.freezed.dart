// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'chat_settings_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ChatSettings {

// Screenshot Control
 bool get allowScreenshots; bool get notifyOnScreenshot;// Message Editing
 bool get allowMessageEditing; EditWindowDuration get editWindowDuration; int get customEditWindowMinutes;// Mute Settings
 bool get muteAllChats;// Message Requests
 bool get allowMessagesFromNonFollowers; bool get autoBlockSpamAccounts;// Self-Destruct Messages
 bool get enableSelfDestruct; SelfDestructTimer get selfDestructTimer; int get customSelfDestructSeconds;// Seen & Typing Status
 bool get showSeenStatus; bool get showTypingIndicators;// Sync Settings
 bool get syncAcrossDevices;// Privacy
 bool get requirePasswordForHiddenChats; bool get useFaceIDForHiddenChats;
/// Create a copy of ChatSettings
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ChatSettingsCopyWith<ChatSettings> get copyWith => _$ChatSettingsCopyWithImpl<ChatSettings>(this as ChatSettings, _$identity);

  /// Serializes this ChatSettings to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChatSettings&&(identical(other.allowScreenshots, allowScreenshots) || other.allowScreenshots == allowScreenshots)&&(identical(other.notifyOnScreenshot, notifyOnScreenshot) || other.notifyOnScreenshot == notifyOnScreenshot)&&(identical(other.allowMessageEditing, allowMessageEditing) || other.allowMessageEditing == allowMessageEditing)&&(identical(other.editWindowDuration, editWindowDuration) || other.editWindowDuration == editWindowDuration)&&(identical(other.customEditWindowMinutes, customEditWindowMinutes) || other.customEditWindowMinutes == customEditWindowMinutes)&&(identical(other.muteAllChats, muteAllChats) || other.muteAllChats == muteAllChats)&&(identical(other.allowMessagesFromNonFollowers, allowMessagesFromNonFollowers) || other.allowMessagesFromNonFollowers == allowMessagesFromNonFollowers)&&(identical(other.autoBlockSpamAccounts, autoBlockSpamAccounts) || other.autoBlockSpamAccounts == autoBlockSpamAccounts)&&(identical(other.enableSelfDestruct, enableSelfDestruct) || other.enableSelfDestruct == enableSelfDestruct)&&(identical(other.selfDestructTimer, selfDestructTimer) || other.selfDestructTimer == selfDestructTimer)&&(identical(other.customSelfDestructSeconds, customSelfDestructSeconds) || other.customSelfDestructSeconds == customSelfDestructSeconds)&&(identical(other.showSeenStatus, showSeenStatus) || other.showSeenStatus == showSeenStatus)&&(identical(other.showTypingIndicators, showTypingIndicators) || other.showTypingIndicators == showTypingIndicators)&&(identical(other.syncAcrossDevices, syncAcrossDevices) || other.syncAcrossDevices == syncAcrossDevices)&&(identical(other.requirePasswordForHiddenChats, requirePasswordForHiddenChats) || other.requirePasswordForHiddenChats == requirePasswordForHiddenChats)&&(identical(other.useFaceIDForHiddenChats, useFaceIDForHiddenChats) || other.useFaceIDForHiddenChats == useFaceIDForHiddenChats));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,allowScreenshots,notifyOnScreenshot,allowMessageEditing,editWindowDuration,customEditWindowMinutes,muteAllChats,allowMessagesFromNonFollowers,autoBlockSpamAccounts,enableSelfDestruct,selfDestructTimer,customSelfDestructSeconds,showSeenStatus,showTypingIndicators,syncAcrossDevices,requirePasswordForHiddenChats,useFaceIDForHiddenChats);

@override
String toString() {
  return 'ChatSettings(allowScreenshots: $allowScreenshots, notifyOnScreenshot: $notifyOnScreenshot, allowMessageEditing: $allowMessageEditing, editWindowDuration: $editWindowDuration, customEditWindowMinutes: $customEditWindowMinutes, muteAllChats: $muteAllChats, allowMessagesFromNonFollowers: $allowMessagesFromNonFollowers, autoBlockSpamAccounts: $autoBlockSpamAccounts, enableSelfDestruct: $enableSelfDestruct, selfDestructTimer: $selfDestructTimer, customSelfDestructSeconds: $customSelfDestructSeconds, showSeenStatus: $showSeenStatus, showTypingIndicators: $showTypingIndicators, syncAcrossDevices: $syncAcrossDevices, requirePasswordForHiddenChats: $requirePasswordForHiddenChats, useFaceIDForHiddenChats: $useFaceIDForHiddenChats)';
}


}

/// @nodoc
abstract mixin class $ChatSettingsCopyWith<$Res>  {
  factory $ChatSettingsCopyWith(ChatSettings value, $Res Function(ChatSettings) _then) = _$ChatSettingsCopyWithImpl;
@useResult
$Res call({
 bool allowScreenshots, bool notifyOnScreenshot, bool allowMessageEditing, EditWindowDuration editWindowDuration, int customEditWindowMinutes, bool muteAllChats, bool allowMessagesFromNonFollowers, bool autoBlockSpamAccounts, bool enableSelfDestruct, SelfDestructTimer selfDestructTimer, int customSelfDestructSeconds, bool showSeenStatus, bool showTypingIndicators, bool syncAcrossDevices, bool requirePasswordForHiddenChats, bool useFaceIDForHiddenChats
});




}
/// @nodoc
class _$ChatSettingsCopyWithImpl<$Res>
    implements $ChatSettingsCopyWith<$Res> {
  _$ChatSettingsCopyWithImpl(this._self, this._then);

  final ChatSettings _self;
  final $Res Function(ChatSettings) _then;

/// Create a copy of ChatSettings
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? allowScreenshots = null,Object? notifyOnScreenshot = null,Object? allowMessageEditing = null,Object? editWindowDuration = null,Object? customEditWindowMinutes = null,Object? muteAllChats = null,Object? allowMessagesFromNonFollowers = null,Object? autoBlockSpamAccounts = null,Object? enableSelfDestruct = null,Object? selfDestructTimer = null,Object? customSelfDestructSeconds = null,Object? showSeenStatus = null,Object? showTypingIndicators = null,Object? syncAcrossDevices = null,Object? requirePasswordForHiddenChats = null,Object? useFaceIDForHiddenChats = null,}) {
  return _then(_self.copyWith(
allowScreenshots: null == allowScreenshots ? _self.allowScreenshots : allowScreenshots // ignore: cast_nullable_to_non_nullable
as bool,notifyOnScreenshot: null == notifyOnScreenshot ? _self.notifyOnScreenshot : notifyOnScreenshot // ignore: cast_nullable_to_non_nullable
as bool,allowMessageEditing: null == allowMessageEditing ? _self.allowMessageEditing : allowMessageEditing // ignore: cast_nullable_to_non_nullable
as bool,editWindowDuration: null == editWindowDuration ? _self.editWindowDuration : editWindowDuration // ignore: cast_nullable_to_non_nullable
as EditWindowDuration,customEditWindowMinutes: null == customEditWindowMinutes ? _self.customEditWindowMinutes : customEditWindowMinutes // ignore: cast_nullable_to_non_nullable
as int,muteAllChats: null == muteAllChats ? _self.muteAllChats : muteAllChats // ignore: cast_nullable_to_non_nullable
as bool,allowMessagesFromNonFollowers: null == allowMessagesFromNonFollowers ? _self.allowMessagesFromNonFollowers : allowMessagesFromNonFollowers // ignore: cast_nullable_to_non_nullable
as bool,autoBlockSpamAccounts: null == autoBlockSpamAccounts ? _self.autoBlockSpamAccounts : autoBlockSpamAccounts // ignore: cast_nullable_to_non_nullable
as bool,enableSelfDestruct: null == enableSelfDestruct ? _self.enableSelfDestruct : enableSelfDestruct // ignore: cast_nullable_to_non_nullable
as bool,selfDestructTimer: null == selfDestructTimer ? _self.selfDestructTimer : selfDestructTimer // ignore: cast_nullable_to_non_nullable
as SelfDestructTimer,customSelfDestructSeconds: null == customSelfDestructSeconds ? _self.customSelfDestructSeconds : customSelfDestructSeconds // ignore: cast_nullable_to_non_nullable
as int,showSeenStatus: null == showSeenStatus ? _self.showSeenStatus : showSeenStatus // ignore: cast_nullable_to_non_nullable
as bool,showTypingIndicators: null == showTypingIndicators ? _self.showTypingIndicators : showTypingIndicators // ignore: cast_nullable_to_non_nullable
as bool,syncAcrossDevices: null == syncAcrossDevices ? _self.syncAcrossDevices : syncAcrossDevices // ignore: cast_nullable_to_non_nullable
as bool,requirePasswordForHiddenChats: null == requirePasswordForHiddenChats ? _self.requirePasswordForHiddenChats : requirePasswordForHiddenChats // ignore: cast_nullable_to_non_nullable
as bool,useFaceIDForHiddenChats: null == useFaceIDForHiddenChats ? _self.useFaceIDForHiddenChats : useFaceIDForHiddenChats // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [ChatSettings].
extension ChatSettingsPatterns on ChatSettings {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ChatSettings value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ChatSettings() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ChatSettings value)  $default,){
final _that = this;
switch (_that) {
case _ChatSettings():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ChatSettings value)?  $default,){
final _that = this;
switch (_that) {
case _ChatSettings() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( bool allowScreenshots,  bool notifyOnScreenshot,  bool allowMessageEditing,  EditWindowDuration editWindowDuration,  int customEditWindowMinutes,  bool muteAllChats,  bool allowMessagesFromNonFollowers,  bool autoBlockSpamAccounts,  bool enableSelfDestruct,  SelfDestructTimer selfDestructTimer,  int customSelfDestructSeconds,  bool showSeenStatus,  bool showTypingIndicators,  bool syncAcrossDevices,  bool requirePasswordForHiddenChats,  bool useFaceIDForHiddenChats)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ChatSettings() when $default != null:
return $default(_that.allowScreenshots,_that.notifyOnScreenshot,_that.allowMessageEditing,_that.editWindowDuration,_that.customEditWindowMinutes,_that.muteAllChats,_that.allowMessagesFromNonFollowers,_that.autoBlockSpamAccounts,_that.enableSelfDestruct,_that.selfDestructTimer,_that.customSelfDestructSeconds,_that.showSeenStatus,_that.showTypingIndicators,_that.syncAcrossDevices,_that.requirePasswordForHiddenChats,_that.useFaceIDForHiddenChats);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( bool allowScreenshots,  bool notifyOnScreenshot,  bool allowMessageEditing,  EditWindowDuration editWindowDuration,  int customEditWindowMinutes,  bool muteAllChats,  bool allowMessagesFromNonFollowers,  bool autoBlockSpamAccounts,  bool enableSelfDestruct,  SelfDestructTimer selfDestructTimer,  int customSelfDestructSeconds,  bool showSeenStatus,  bool showTypingIndicators,  bool syncAcrossDevices,  bool requirePasswordForHiddenChats,  bool useFaceIDForHiddenChats)  $default,) {final _that = this;
switch (_that) {
case _ChatSettings():
return $default(_that.allowScreenshots,_that.notifyOnScreenshot,_that.allowMessageEditing,_that.editWindowDuration,_that.customEditWindowMinutes,_that.muteAllChats,_that.allowMessagesFromNonFollowers,_that.autoBlockSpamAccounts,_that.enableSelfDestruct,_that.selfDestructTimer,_that.customSelfDestructSeconds,_that.showSeenStatus,_that.showTypingIndicators,_that.syncAcrossDevices,_that.requirePasswordForHiddenChats,_that.useFaceIDForHiddenChats);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( bool allowScreenshots,  bool notifyOnScreenshot,  bool allowMessageEditing,  EditWindowDuration editWindowDuration,  int customEditWindowMinutes,  bool muteAllChats,  bool allowMessagesFromNonFollowers,  bool autoBlockSpamAccounts,  bool enableSelfDestruct,  SelfDestructTimer selfDestructTimer,  int customSelfDestructSeconds,  bool showSeenStatus,  bool showTypingIndicators,  bool syncAcrossDevices,  bool requirePasswordForHiddenChats,  bool useFaceIDForHiddenChats)?  $default,) {final _that = this;
switch (_that) {
case _ChatSettings() when $default != null:
return $default(_that.allowScreenshots,_that.notifyOnScreenshot,_that.allowMessageEditing,_that.editWindowDuration,_that.customEditWindowMinutes,_that.muteAllChats,_that.allowMessagesFromNonFollowers,_that.autoBlockSpamAccounts,_that.enableSelfDestruct,_that.selfDestructTimer,_that.customSelfDestructSeconds,_that.showSeenStatus,_that.showTypingIndicators,_that.syncAcrossDevices,_that.requirePasswordForHiddenChats,_that.useFaceIDForHiddenChats);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ChatSettings implements ChatSettings {
  const _ChatSettings({this.allowScreenshots = false, this.notifyOnScreenshot = true, this.allowMessageEditing = true, this.editWindowDuration = EditWindowDuration.tenMinutes, this.customEditWindowMinutes = 10, this.muteAllChats = false, this.allowMessagesFromNonFollowers = true, this.autoBlockSpamAccounts = true, this.enableSelfDestruct = false, this.selfDestructTimer = SelfDestructTimer.afterReading, this.customSelfDestructSeconds = 10, this.showSeenStatus = true, this.showTypingIndicators = true, this.syncAcrossDevices = true, this.requirePasswordForHiddenChats = false, this.useFaceIDForHiddenChats = false});
  factory _ChatSettings.fromJson(Map<String, dynamic> json) => _$ChatSettingsFromJson(json);

// Screenshot Control
@override@JsonKey() final  bool allowScreenshots;
@override@JsonKey() final  bool notifyOnScreenshot;
// Message Editing
@override@JsonKey() final  bool allowMessageEditing;
@override@JsonKey() final  EditWindowDuration editWindowDuration;
@override@JsonKey() final  int customEditWindowMinutes;
// Mute Settings
@override@JsonKey() final  bool muteAllChats;
// Message Requests
@override@JsonKey() final  bool allowMessagesFromNonFollowers;
@override@JsonKey() final  bool autoBlockSpamAccounts;
// Self-Destruct Messages
@override@JsonKey() final  bool enableSelfDestruct;
@override@JsonKey() final  SelfDestructTimer selfDestructTimer;
@override@JsonKey() final  int customSelfDestructSeconds;
// Seen & Typing Status
@override@JsonKey() final  bool showSeenStatus;
@override@JsonKey() final  bool showTypingIndicators;
// Sync Settings
@override@JsonKey() final  bool syncAcrossDevices;
// Privacy
@override@JsonKey() final  bool requirePasswordForHiddenChats;
@override@JsonKey() final  bool useFaceIDForHiddenChats;

/// Create a copy of ChatSettings
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChatSettingsCopyWith<_ChatSettings> get copyWith => __$ChatSettingsCopyWithImpl<_ChatSettings>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ChatSettingsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChatSettings&&(identical(other.allowScreenshots, allowScreenshots) || other.allowScreenshots == allowScreenshots)&&(identical(other.notifyOnScreenshot, notifyOnScreenshot) || other.notifyOnScreenshot == notifyOnScreenshot)&&(identical(other.allowMessageEditing, allowMessageEditing) || other.allowMessageEditing == allowMessageEditing)&&(identical(other.editWindowDuration, editWindowDuration) || other.editWindowDuration == editWindowDuration)&&(identical(other.customEditWindowMinutes, customEditWindowMinutes) || other.customEditWindowMinutes == customEditWindowMinutes)&&(identical(other.muteAllChats, muteAllChats) || other.muteAllChats == muteAllChats)&&(identical(other.allowMessagesFromNonFollowers, allowMessagesFromNonFollowers) || other.allowMessagesFromNonFollowers == allowMessagesFromNonFollowers)&&(identical(other.autoBlockSpamAccounts, autoBlockSpamAccounts) || other.autoBlockSpamAccounts == autoBlockSpamAccounts)&&(identical(other.enableSelfDestruct, enableSelfDestruct) || other.enableSelfDestruct == enableSelfDestruct)&&(identical(other.selfDestructTimer, selfDestructTimer) || other.selfDestructTimer == selfDestructTimer)&&(identical(other.customSelfDestructSeconds, customSelfDestructSeconds) || other.customSelfDestructSeconds == customSelfDestructSeconds)&&(identical(other.showSeenStatus, showSeenStatus) || other.showSeenStatus == showSeenStatus)&&(identical(other.showTypingIndicators, showTypingIndicators) || other.showTypingIndicators == showTypingIndicators)&&(identical(other.syncAcrossDevices, syncAcrossDevices) || other.syncAcrossDevices == syncAcrossDevices)&&(identical(other.requirePasswordForHiddenChats, requirePasswordForHiddenChats) || other.requirePasswordForHiddenChats == requirePasswordForHiddenChats)&&(identical(other.useFaceIDForHiddenChats, useFaceIDForHiddenChats) || other.useFaceIDForHiddenChats == useFaceIDForHiddenChats));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,allowScreenshots,notifyOnScreenshot,allowMessageEditing,editWindowDuration,customEditWindowMinutes,muteAllChats,allowMessagesFromNonFollowers,autoBlockSpamAccounts,enableSelfDestruct,selfDestructTimer,customSelfDestructSeconds,showSeenStatus,showTypingIndicators,syncAcrossDevices,requirePasswordForHiddenChats,useFaceIDForHiddenChats);

@override
String toString() {
  return 'ChatSettings(allowScreenshots: $allowScreenshots, notifyOnScreenshot: $notifyOnScreenshot, allowMessageEditing: $allowMessageEditing, editWindowDuration: $editWindowDuration, customEditWindowMinutes: $customEditWindowMinutes, muteAllChats: $muteAllChats, allowMessagesFromNonFollowers: $allowMessagesFromNonFollowers, autoBlockSpamAccounts: $autoBlockSpamAccounts, enableSelfDestruct: $enableSelfDestruct, selfDestructTimer: $selfDestructTimer, customSelfDestructSeconds: $customSelfDestructSeconds, showSeenStatus: $showSeenStatus, showTypingIndicators: $showTypingIndicators, syncAcrossDevices: $syncAcrossDevices, requirePasswordForHiddenChats: $requirePasswordForHiddenChats, useFaceIDForHiddenChats: $useFaceIDForHiddenChats)';
}


}

/// @nodoc
abstract mixin class _$ChatSettingsCopyWith<$Res> implements $ChatSettingsCopyWith<$Res> {
  factory _$ChatSettingsCopyWith(_ChatSettings value, $Res Function(_ChatSettings) _then) = __$ChatSettingsCopyWithImpl;
@override @useResult
$Res call({
 bool allowScreenshots, bool notifyOnScreenshot, bool allowMessageEditing, EditWindowDuration editWindowDuration, int customEditWindowMinutes, bool muteAllChats, bool allowMessagesFromNonFollowers, bool autoBlockSpamAccounts, bool enableSelfDestruct, SelfDestructTimer selfDestructTimer, int customSelfDestructSeconds, bool showSeenStatus, bool showTypingIndicators, bool syncAcrossDevices, bool requirePasswordForHiddenChats, bool useFaceIDForHiddenChats
});




}
/// @nodoc
class __$ChatSettingsCopyWithImpl<$Res>
    implements _$ChatSettingsCopyWith<$Res> {
  __$ChatSettingsCopyWithImpl(this._self, this._then);

  final _ChatSettings _self;
  final $Res Function(_ChatSettings) _then;

/// Create a copy of ChatSettings
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? allowScreenshots = null,Object? notifyOnScreenshot = null,Object? allowMessageEditing = null,Object? editWindowDuration = null,Object? customEditWindowMinutes = null,Object? muteAllChats = null,Object? allowMessagesFromNonFollowers = null,Object? autoBlockSpamAccounts = null,Object? enableSelfDestruct = null,Object? selfDestructTimer = null,Object? customSelfDestructSeconds = null,Object? showSeenStatus = null,Object? showTypingIndicators = null,Object? syncAcrossDevices = null,Object? requirePasswordForHiddenChats = null,Object? useFaceIDForHiddenChats = null,}) {
  return _then(_ChatSettings(
allowScreenshots: null == allowScreenshots ? _self.allowScreenshots : allowScreenshots // ignore: cast_nullable_to_non_nullable
as bool,notifyOnScreenshot: null == notifyOnScreenshot ? _self.notifyOnScreenshot : notifyOnScreenshot // ignore: cast_nullable_to_non_nullable
as bool,allowMessageEditing: null == allowMessageEditing ? _self.allowMessageEditing : allowMessageEditing // ignore: cast_nullable_to_non_nullable
as bool,editWindowDuration: null == editWindowDuration ? _self.editWindowDuration : editWindowDuration // ignore: cast_nullable_to_non_nullable
as EditWindowDuration,customEditWindowMinutes: null == customEditWindowMinutes ? _self.customEditWindowMinutes : customEditWindowMinutes // ignore: cast_nullable_to_non_nullable
as int,muteAllChats: null == muteAllChats ? _self.muteAllChats : muteAllChats // ignore: cast_nullable_to_non_nullable
as bool,allowMessagesFromNonFollowers: null == allowMessagesFromNonFollowers ? _self.allowMessagesFromNonFollowers : allowMessagesFromNonFollowers // ignore: cast_nullable_to_non_nullable
as bool,autoBlockSpamAccounts: null == autoBlockSpamAccounts ? _self.autoBlockSpamAccounts : autoBlockSpamAccounts // ignore: cast_nullable_to_non_nullable
as bool,enableSelfDestruct: null == enableSelfDestruct ? _self.enableSelfDestruct : enableSelfDestruct // ignore: cast_nullable_to_non_nullable
as bool,selfDestructTimer: null == selfDestructTimer ? _self.selfDestructTimer : selfDestructTimer // ignore: cast_nullable_to_non_nullable
as SelfDestructTimer,customSelfDestructSeconds: null == customSelfDestructSeconds ? _self.customSelfDestructSeconds : customSelfDestructSeconds // ignore: cast_nullable_to_non_nullable
as int,showSeenStatus: null == showSeenStatus ? _self.showSeenStatus : showSeenStatus // ignore: cast_nullable_to_non_nullable
as bool,showTypingIndicators: null == showTypingIndicators ? _self.showTypingIndicators : showTypingIndicators // ignore: cast_nullable_to_non_nullable
as bool,syncAcrossDevices: null == syncAcrossDevices ? _self.syncAcrossDevices : syncAcrossDevices // ignore: cast_nullable_to_non_nullable
as bool,requirePasswordForHiddenChats: null == requirePasswordForHiddenChats ? _self.requirePasswordForHiddenChats : requirePasswordForHiddenChats // ignore: cast_nullable_to_non_nullable
as bool,useFaceIDForHiddenChats: null == useFaceIDForHiddenChats ? _self.useFaceIDForHiddenChats : useFaceIDForHiddenChats // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$IndividualChatSettings {

 String get chatId;// Override global settings
 bool? get allowScreenshots; bool? get notifyOnScreenshot; bool? get allowMessageEditing; bool? get muteChat; bool? get showSeenStatus; bool? get showTypingIndicators; bool? get enableSelfDestruct; SelfDestructTimer? get selfDestructTimer; int? get customSelfDestructSeconds;// Chat-specific settings
 bool get isHidden; bool get isPinned; bool get isArchived; bool get isStarred;
/// Create a copy of IndividualChatSettings
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$IndividualChatSettingsCopyWith<IndividualChatSettings> get copyWith => _$IndividualChatSettingsCopyWithImpl<IndividualChatSettings>(this as IndividualChatSettings, _$identity);

  /// Serializes this IndividualChatSettings to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is IndividualChatSettings&&(identical(other.chatId, chatId) || other.chatId == chatId)&&(identical(other.allowScreenshots, allowScreenshots) || other.allowScreenshots == allowScreenshots)&&(identical(other.notifyOnScreenshot, notifyOnScreenshot) || other.notifyOnScreenshot == notifyOnScreenshot)&&(identical(other.allowMessageEditing, allowMessageEditing) || other.allowMessageEditing == allowMessageEditing)&&(identical(other.muteChat, muteChat) || other.muteChat == muteChat)&&(identical(other.showSeenStatus, showSeenStatus) || other.showSeenStatus == showSeenStatus)&&(identical(other.showTypingIndicators, showTypingIndicators) || other.showTypingIndicators == showTypingIndicators)&&(identical(other.enableSelfDestruct, enableSelfDestruct) || other.enableSelfDestruct == enableSelfDestruct)&&(identical(other.selfDestructTimer, selfDestructTimer) || other.selfDestructTimer == selfDestructTimer)&&(identical(other.customSelfDestructSeconds, customSelfDestructSeconds) || other.customSelfDestructSeconds == customSelfDestructSeconds)&&(identical(other.isHidden, isHidden) || other.isHidden == isHidden)&&(identical(other.isPinned, isPinned) || other.isPinned == isPinned)&&(identical(other.isArchived, isArchived) || other.isArchived == isArchived)&&(identical(other.isStarred, isStarred) || other.isStarred == isStarred));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,chatId,allowScreenshots,notifyOnScreenshot,allowMessageEditing,muteChat,showSeenStatus,showTypingIndicators,enableSelfDestruct,selfDestructTimer,customSelfDestructSeconds,isHidden,isPinned,isArchived,isStarred);

@override
String toString() {
  return 'IndividualChatSettings(chatId: $chatId, allowScreenshots: $allowScreenshots, notifyOnScreenshot: $notifyOnScreenshot, allowMessageEditing: $allowMessageEditing, muteChat: $muteChat, showSeenStatus: $showSeenStatus, showTypingIndicators: $showTypingIndicators, enableSelfDestruct: $enableSelfDestruct, selfDestructTimer: $selfDestructTimer, customSelfDestructSeconds: $customSelfDestructSeconds, isHidden: $isHidden, isPinned: $isPinned, isArchived: $isArchived, isStarred: $isStarred)';
}


}

/// @nodoc
abstract mixin class $IndividualChatSettingsCopyWith<$Res>  {
  factory $IndividualChatSettingsCopyWith(IndividualChatSettings value, $Res Function(IndividualChatSettings) _then) = _$IndividualChatSettingsCopyWithImpl;
@useResult
$Res call({
 String chatId, bool? allowScreenshots, bool? notifyOnScreenshot, bool? allowMessageEditing, bool? muteChat, bool? showSeenStatus, bool? showTypingIndicators, bool? enableSelfDestruct, SelfDestructTimer? selfDestructTimer, int? customSelfDestructSeconds, bool isHidden, bool isPinned, bool isArchived, bool isStarred
});




}
/// @nodoc
class _$IndividualChatSettingsCopyWithImpl<$Res>
    implements $IndividualChatSettingsCopyWith<$Res> {
  _$IndividualChatSettingsCopyWithImpl(this._self, this._then);

  final IndividualChatSettings _self;
  final $Res Function(IndividualChatSettings) _then;

/// Create a copy of IndividualChatSettings
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? chatId = null,Object? allowScreenshots = freezed,Object? notifyOnScreenshot = freezed,Object? allowMessageEditing = freezed,Object? muteChat = freezed,Object? showSeenStatus = freezed,Object? showTypingIndicators = freezed,Object? enableSelfDestruct = freezed,Object? selfDestructTimer = freezed,Object? customSelfDestructSeconds = freezed,Object? isHidden = null,Object? isPinned = null,Object? isArchived = null,Object? isStarred = null,}) {
  return _then(_self.copyWith(
chatId: null == chatId ? _self.chatId : chatId // ignore: cast_nullable_to_non_nullable
as String,allowScreenshots: freezed == allowScreenshots ? _self.allowScreenshots : allowScreenshots // ignore: cast_nullable_to_non_nullable
as bool?,notifyOnScreenshot: freezed == notifyOnScreenshot ? _self.notifyOnScreenshot : notifyOnScreenshot // ignore: cast_nullable_to_non_nullable
as bool?,allowMessageEditing: freezed == allowMessageEditing ? _self.allowMessageEditing : allowMessageEditing // ignore: cast_nullable_to_non_nullable
as bool?,muteChat: freezed == muteChat ? _self.muteChat : muteChat // ignore: cast_nullable_to_non_nullable
as bool?,showSeenStatus: freezed == showSeenStatus ? _self.showSeenStatus : showSeenStatus // ignore: cast_nullable_to_non_nullable
as bool?,showTypingIndicators: freezed == showTypingIndicators ? _self.showTypingIndicators : showTypingIndicators // ignore: cast_nullable_to_non_nullable
as bool?,enableSelfDestruct: freezed == enableSelfDestruct ? _self.enableSelfDestruct : enableSelfDestruct // ignore: cast_nullable_to_non_nullable
as bool?,selfDestructTimer: freezed == selfDestructTimer ? _self.selfDestructTimer : selfDestructTimer // ignore: cast_nullable_to_non_nullable
as SelfDestructTimer?,customSelfDestructSeconds: freezed == customSelfDestructSeconds ? _self.customSelfDestructSeconds : customSelfDestructSeconds // ignore: cast_nullable_to_non_nullable
as int?,isHidden: null == isHidden ? _self.isHidden : isHidden // ignore: cast_nullable_to_non_nullable
as bool,isPinned: null == isPinned ? _self.isPinned : isPinned // ignore: cast_nullable_to_non_nullable
as bool,isArchived: null == isArchived ? _self.isArchived : isArchived // ignore: cast_nullable_to_non_nullable
as bool,isStarred: null == isStarred ? _self.isStarred : isStarred // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [IndividualChatSettings].
extension IndividualChatSettingsPatterns on IndividualChatSettings {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _IndividualChatSettings value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _IndividualChatSettings() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _IndividualChatSettings value)  $default,){
final _that = this;
switch (_that) {
case _IndividualChatSettings():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _IndividualChatSettings value)?  $default,){
final _that = this;
switch (_that) {
case _IndividualChatSettings() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String chatId,  bool? allowScreenshots,  bool? notifyOnScreenshot,  bool? allowMessageEditing,  bool? muteChat,  bool? showSeenStatus,  bool? showTypingIndicators,  bool? enableSelfDestruct,  SelfDestructTimer? selfDestructTimer,  int? customSelfDestructSeconds,  bool isHidden,  bool isPinned,  bool isArchived,  bool isStarred)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _IndividualChatSettings() when $default != null:
return $default(_that.chatId,_that.allowScreenshots,_that.notifyOnScreenshot,_that.allowMessageEditing,_that.muteChat,_that.showSeenStatus,_that.showTypingIndicators,_that.enableSelfDestruct,_that.selfDestructTimer,_that.customSelfDestructSeconds,_that.isHidden,_that.isPinned,_that.isArchived,_that.isStarred);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String chatId,  bool? allowScreenshots,  bool? notifyOnScreenshot,  bool? allowMessageEditing,  bool? muteChat,  bool? showSeenStatus,  bool? showTypingIndicators,  bool? enableSelfDestruct,  SelfDestructTimer? selfDestructTimer,  int? customSelfDestructSeconds,  bool isHidden,  bool isPinned,  bool isArchived,  bool isStarred)  $default,) {final _that = this;
switch (_that) {
case _IndividualChatSettings():
return $default(_that.chatId,_that.allowScreenshots,_that.notifyOnScreenshot,_that.allowMessageEditing,_that.muteChat,_that.showSeenStatus,_that.showTypingIndicators,_that.enableSelfDestruct,_that.selfDestructTimer,_that.customSelfDestructSeconds,_that.isHidden,_that.isPinned,_that.isArchived,_that.isStarred);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String chatId,  bool? allowScreenshots,  bool? notifyOnScreenshot,  bool? allowMessageEditing,  bool? muteChat,  bool? showSeenStatus,  bool? showTypingIndicators,  bool? enableSelfDestruct,  SelfDestructTimer? selfDestructTimer,  int? customSelfDestructSeconds,  bool isHidden,  bool isPinned,  bool isArchived,  bool isStarred)?  $default,) {final _that = this;
switch (_that) {
case _IndividualChatSettings() when $default != null:
return $default(_that.chatId,_that.allowScreenshots,_that.notifyOnScreenshot,_that.allowMessageEditing,_that.muteChat,_that.showSeenStatus,_that.showTypingIndicators,_that.enableSelfDestruct,_that.selfDestructTimer,_that.customSelfDestructSeconds,_that.isHidden,_that.isPinned,_that.isArchived,_that.isStarred);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _IndividualChatSettings implements IndividualChatSettings {
  const _IndividualChatSettings({required this.chatId, this.allowScreenshots, this.notifyOnScreenshot, this.allowMessageEditing, this.muteChat, this.showSeenStatus, this.showTypingIndicators, this.enableSelfDestruct, this.selfDestructTimer, this.customSelfDestructSeconds, this.isHidden = false, this.isPinned = false, this.isArchived = false, this.isStarred = false});
  factory _IndividualChatSettings.fromJson(Map<String, dynamic> json) => _$IndividualChatSettingsFromJson(json);

@override final  String chatId;
// Override global settings
@override final  bool? allowScreenshots;
@override final  bool? notifyOnScreenshot;
@override final  bool? allowMessageEditing;
@override final  bool? muteChat;
@override final  bool? showSeenStatus;
@override final  bool? showTypingIndicators;
@override final  bool? enableSelfDestruct;
@override final  SelfDestructTimer? selfDestructTimer;
@override final  int? customSelfDestructSeconds;
// Chat-specific settings
@override@JsonKey() final  bool isHidden;
@override@JsonKey() final  bool isPinned;
@override@JsonKey() final  bool isArchived;
@override@JsonKey() final  bool isStarred;

/// Create a copy of IndividualChatSettings
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$IndividualChatSettingsCopyWith<_IndividualChatSettings> get copyWith => __$IndividualChatSettingsCopyWithImpl<_IndividualChatSettings>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$IndividualChatSettingsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _IndividualChatSettings&&(identical(other.chatId, chatId) || other.chatId == chatId)&&(identical(other.allowScreenshots, allowScreenshots) || other.allowScreenshots == allowScreenshots)&&(identical(other.notifyOnScreenshot, notifyOnScreenshot) || other.notifyOnScreenshot == notifyOnScreenshot)&&(identical(other.allowMessageEditing, allowMessageEditing) || other.allowMessageEditing == allowMessageEditing)&&(identical(other.muteChat, muteChat) || other.muteChat == muteChat)&&(identical(other.showSeenStatus, showSeenStatus) || other.showSeenStatus == showSeenStatus)&&(identical(other.showTypingIndicators, showTypingIndicators) || other.showTypingIndicators == showTypingIndicators)&&(identical(other.enableSelfDestruct, enableSelfDestruct) || other.enableSelfDestruct == enableSelfDestruct)&&(identical(other.selfDestructTimer, selfDestructTimer) || other.selfDestructTimer == selfDestructTimer)&&(identical(other.customSelfDestructSeconds, customSelfDestructSeconds) || other.customSelfDestructSeconds == customSelfDestructSeconds)&&(identical(other.isHidden, isHidden) || other.isHidden == isHidden)&&(identical(other.isPinned, isPinned) || other.isPinned == isPinned)&&(identical(other.isArchived, isArchived) || other.isArchived == isArchived)&&(identical(other.isStarred, isStarred) || other.isStarred == isStarred));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,chatId,allowScreenshots,notifyOnScreenshot,allowMessageEditing,muteChat,showSeenStatus,showTypingIndicators,enableSelfDestruct,selfDestructTimer,customSelfDestructSeconds,isHidden,isPinned,isArchived,isStarred);

@override
String toString() {
  return 'IndividualChatSettings(chatId: $chatId, allowScreenshots: $allowScreenshots, notifyOnScreenshot: $notifyOnScreenshot, allowMessageEditing: $allowMessageEditing, muteChat: $muteChat, showSeenStatus: $showSeenStatus, showTypingIndicators: $showTypingIndicators, enableSelfDestruct: $enableSelfDestruct, selfDestructTimer: $selfDestructTimer, customSelfDestructSeconds: $customSelfDestructSeconds, isHidden: $isHidden, isPinned: $isPinned, isArchived: $isArchived, isStarred: $isStarred)';
}


}

/// @nodoc
abstract mixin class _$IndividualChatSettingsCopyWith<$Res> implements $IndividualChatSettingsCopyWith<$Res> {
  factory _$IndividualChatSettingsCopyWith(_IndividualChatSettings value, $Res Function(_IndividualChatSettings) _then) = __$IndividualChatSettingsCopyWithImpl;
@override @useResult
$Res call({
 String chatId, bool? allowScreenshots, bool? notifyOnScreenshot, bool? allowMessageEditing, bool? muteChat, bool? showSeenStatus, bool? showTypingIndicators, bool? enableSelfDestruct, SelfDestructTimer? selfDestructTimer, int? customSelfDestructSeconds, bool isHidden, bool isPinned, bool isArchived, bool isStarred
});




}
/// @nodoc
class __$IndividualChatSettingsCopyWithImpl<$Res>
    implements _$IndividualChatSettingsCopyWith<$Res> {
  __$IndividualChatSettingsCopyWithImpl(this._self, this._then);

  final _IndividualChatSettings _self;
  final $Res Function(_IndividualChatSettings) _then;

/// Create a copy of IndividualChatSettings
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? chatId = null,Object? allowScreenshots = freezed,Object? notifyOnScreenshot = freezed,Object? allowMessageEditing = freezed,Object? muteChat = freezed,Object? showSeenStatus = freezed,Object? showTypingIndicators = freezed,Object? enableSelfDestruct = freezed,Object? selfDestructTimer = freezed,Object? customSelfDestructSeconds = freezed,Object? isHidden = null,Object? isPinned = null,Object? isArchived = null,Object? isStarred = null,}) {
  return _then(_IndividualChatSettings(
chatId: null == chatId ? _self.chatId : chatId // ignore: cast_nullable_to_non_nullable
as String,allowScreenshots: freezed == allowScreenshots ? _self.allowScreenshots : allowScreenshots // ignore: cast_nullable_to_non_nullable
as bool?,notifyOnScreenshot: freezed == notifyOnScreenshot ? _self.notifyOnScreenshot : notifyOnScreenshot // ignore: cast_nullable_to_non_nullable
as bool?,allowMessageEditing: freezed == allowMessageEditing ? _self.allowMessageEditing : allowMessageEditing // ignore: cast_nullable_to_non_nullable
as bool?,muteChat: freezed == muteChat ? _self.muteChat : muteChat // ignore: cast_nullable_to_non_nullable
as bool?,showSeenStatus: freezed == showSeenStatus ? _self.showSeenStatus : showSeenStatus // ignore: cast_nullable_to_non_nullable
as bool?,showTypingIndicators: freezed == showTypingIndicators ? _self.showTypingIndicators : showTypingIndicators // ignore: cast_nullable_to_non_nullable
as bool?,enableSelfDestruct: freezed == enableSelfDestruct ? _self.enableSelfDestruct : enableSelfDestruct // ignore: cast_nullable_to_non_nullable
as bool?,selfDestructTimer: freezed == selfDestructTimer ? _self.selfDestructTimer : selfDestructTimer // ignore: cast_nullable_to_non_nullable
as SelfDestructTimer?,customSelfDestructSeconds: freezed == customSelfDestructSeconds ? _self.customSelfDestructSeconds : customSelfDestructSeconds // ignore: cast_nullable_to_non_nullable
as int?,isHidden: null == isHidden ? _self.isHidden : isHidden // ignore: cast_nullable_to_non_nullable
as bool,isPinned: null == isPinned ? _self.isPinned : isPinned // ignore: cast_nullable_to_non_nullable
as bool,isArchived: null == isArchived ? _self.isArchived : isArchived // ignore: cast_nullable_to_non_nullable
as bool,isStarred: null == isStarred ? _self.isStarred : isStarred // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
