// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'message_encryption_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MessageEncryptionModel {

 String get id; String get messageId; EncryptionStatus get status; EncryptionType get type; String get encryptedContent; String? get decryptedContent; String get senderPublicKey; String? get recipientPublicKey; DateTime get createdAt; DateTime? get decryptedAt; MessageEncryptionMetadata get metadata;
/// Create a copy of MessageEncryptionModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MessageEncryptionModelCopyWith<MessageEncryptionModel> get copyWith => _$MessageEncryptionModelCopyWithImpl<MessageEncryptionModel>(this as MessageEncryptionModel, _$identity);

  /// Serializes this MessageEncryptionModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MessageEncryptionModel&&(identical(other.id, id) || other.id == id)&&(identical(other.messageId, messageId) || other.messageId == messageId)&&(identical(other.status, status) || other.status == status)&&(identical(other.type, type) || other.type == type)&&(identical(other.encryptedContent, encryptedContent) || other.encryptedContent == encryptedContent)&&(identical(other.decryptedContent, decryptedContent) || other.decryptedContent == decryptedContent)&&(identical(other.senderPublicKey, senderPublicKey) || other.senderPublicKey == senderPublicKey)&&(identical(other.recipientPublicKey, recipientPublicKey) || other.recipientPublicKey == recipientPublicKey)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.decryptedAt, decryptedAt) || other.decryptedAt == decryptedAt)&&(identical(other.metadata, metadata) || other.metadata == metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,messageId,status,type,encryptedContent,decryptedContent,senderPublicKey,recipientPublicKey,createdAt,decryptedAt,metadata);

@override
String toString() {
  return 'MessageEncryptionModel(id: $id, messageId: $messageId, status: $status, type: $type, encryptedContent: $encryptedContent, decryptedContent: $decryptedContent, senderPublicKey: $senderPublicKey, recipientPublicKey: $recipientPublicKey, createdAt: $createdAt, decryptedAt: $decryptedAt, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $MessageEncryptionModelCopyWith<$Res>  {
  factory $MessageEncryptionModelCopyWith(MessageEncryptionModel value, $Res Function(MessageEncryptionModel) _then) = _$MessageEncryptionModelCopyWithImpl;
@useResult
$Res call({
 String id, String messageId, EncryptionStatus status, EncryptionType type, String encryptedContent, String? decryptedContent, String senderPublicKey, String? recipientPublicKey, DateTime createdAt, DateTime? decryptedAt, MessageEncryptionMetadata metadata
});


$MessageEncryptionMetadataCopyWith<$Res> get metadata;

}
/// @nodoc
class _$MessageEncryptionModelCopyWithImpl<$Res>
    implements $MessageEncryptionModelCopyWith<$Res> {
  _$MessageEncryptionModelCopyWithImpl(this._self, this._then);

  final MessageEncryptionModel _self;
  final $Res Function(MessageEncryptionModel) _then;

/// Create a copy of MessageEncryptionModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? messageId = null,Object? status = null,Object? type = null,Object? encryptedContent = null,Object? decryptedContent = freezed,Object? senderPublicKey = null,Object? recipientPublicKey = freezed,Object? createdAt = null,Object? decryptedAt = freezed,Object? metadata = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,messageId: null == messageId ? _self.messageId : messageId // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as EncryptionStatus,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as EncryptionType,encryptedContent: null == encryptedContent ? _self.encryptedContent : encryptedContent // ignore: cast_nullable_to_non_nullable
as String,decryptedContent: freezed == decryptedContent ? _self.decryptedContent : decryptedContent // ignore: cast_nullable_to_non_nullable
as String?,senderPublicKey: null == senderPublicKey ? _self.senderPublicKey : senderPublicKey // ignore: cast_nullable_to_non_nullable
as String,recipientPublicKey: freezed == recipientPublicKey ? _self.recipientPublicKey : recipientPublicKey // ignore: cast_nullable_to_non_nullable
as String?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,decryptedAt: freezed == decryptedAt ? _self.decryptedAt : decryptedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,metadata: null == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as MessageEncryptionMetadata,
  ));
}
/// Create a copy of MessageEncryptionModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MessageEncryptionMetadataCopyWith<$Res> get metadata {
  
  return $MessageEncryptionMetadataCopyWith<$Res>(_self.metadata, (value) {
    return _then(_self.copyWith(metadata: value));
  });
}
}


/// Adds pattern-matching-related methods to [MessageEncryptionModel].
extension MessageEncryptionModelPatterns on MessageEncryptionModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _MessageEncryptionModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _MessageEncryptionModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _MessageEncryptionModel value)  $default,){
final _that = this;
switch (_that) {
case _MessageEncryptionModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _MessageEncryptionModel value)?  $default,){
final _that = this;
switch (_that) {
case _MessageEncryptionModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String messageId,  EncryptionStatus status,  EncryptionType type,  String encryptedContent,  String? decryptedContent,  String senderPublicKey,  String? recipientPublicKey,  DateTime createdAt,  DateTime? decryptedAt,  MessageEncryptionMetadata metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _MessageEncryptionModel() when $default != null:
return $default(_that.id,_that.messageId,_that.status,_that.type,_that.encryptedContent,_that.decryptedContent,_that.senderPublicKey,_that.recipientPublicKey,_that.createdAt,_that.decryptedAt,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String messageId,  EncryptionStatus status,  EncryptionType type,  String encryptedContent,  String? decryptedContent,  String senderPublicKey,  String? recipientPublicKey,  DateTime createdAt,  DateTime? decryptedAt,  MessageEncryptionMetadata metadata)  $default,) {final _that = this;
switch (_that) {
case _MessageEncryptionModel():
return $default(_that.id,_that.messageId,_that.status,_that.type,_that.encryptedContent,_that.decryptedContent,_that.senderPublicKey,_that.recipientPublicKey,_that.createdAt,_that.decryptedAt,_that.metadata);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String messageId,  EncryptionStatus status,  EncryptionType type,  String encryptedContent,  String? decryptedContent,  String senderPublicKey,  String? recipientPublicKey,  DateTime createdAt,  DateTime? decryptedAt,  MessageEncryptionMetadata metadata)?  $default,) {final _that = this;
switch (_that) {
case _MessageEncryptionModel() when $default != null:
return $default(_that.id,_that.messageId,_that.status,_that.type,_that.encryptedContent,_that.decryptedContent,_that.senderPublicKey,_that.recipientPublicKey,_that.createdAt,_that.decryptedAt,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _MessageEncryptionModel implements MessageEncryptionModel {
  const _MessageEncryptionModel({required this.id, required this.messageId, required this.status, required this.type, required this.encryptedContent, required this.decryptedContent, required this.senderPublicKey, required this.recipientPublicKey, required this.createdAt, required this.decryptedAt, required this.metadata});
  factory _MessageEncryptionModel.fromJson(Map<String, dynamic> json) => _$MessageEncryptionModelFromJson(json);

@override final  String id;
@override final  String messageId;
@override final  EncryptionStatus status;
@override final  EncryptionType type;
@override final  String encryptedContent;
@override final  String? decryptedContent;
@override final  String senderPublicKey;
@override final  String? recipientPublicKey;
@override final  DateTime createdAt;
@override final  DateTime? decryptedAt;
@override final  MessageEncryptionMetadata metadata;

/// Create a copy of MessageEncryptionModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MessageEncryptionModelCopyWith<_MessageEncryptionModel> get copyWith => __$MessageEncryptionModelCopyWithImpl<_MessageEncryptionModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$MessageEncryptionModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _MessageEncryptionModel&&(identical(other.id, id) || other.id == id)&&(identical(other.messageId, messageId) || other.messageId == messageId)&&(identical(other.status, status) || other.status == status)&&(identical(other.type, type) || other.type == type)&&(identical(other.encryptedContent, encryptedContent) || other.encryptedContent == encryptedContent)&&(identical(other.decryptedContent, decryptedContent) || other.decryptedContent == decryptedContent)&&(identical(other.senderPublicKey, senderPublicKey) || other.senderPublicKey == senderPublicKey)&&(identical(other.recipientPublicKey, recipientPublicKey) || other.recipientPublicKey == recipientPublicKey)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.decryptedAt, decryptedAt) || other.decryptedAt == decryptedAt)&&(identical(other.metadata, metadata) || other.metadata == metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,messageId,status,type,encryptedContent,decryptedContent,senderPublicKey,recipientPublicKey,createdAt,decryptedAt,metadata);

@override
String toString() {
  return 'MessageEncryptionModel(id: $id, messageId: $messageId, status: $status, type: $type, encryptedContent: $encryptedContent, decryptedContent: $decryptedContent, senderPublicKey: $senderPublicKey, recipientPublicKey: $recipientPublicKey, createdAt: $createdAt, decryptedAt: $decryptedAt, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$MessageEncryptionModelCopyWith<$Res> implements $MessageEncryptionModelCopyWith<$Res> {
  factory _$MessageEncryptionModelCopyWith(_MessageEncryptionModel value, $Res Function(_MessageEncryptionModel) _then) = __$MessageEncryptionModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String messageId, EncryptionStatus status, EncryptionType type, String encryptedContent, String? decryptedContent, String senderPublicKey, String? recipientPublicKey, DateTime createdAt, DateTime? decryptedAt, MessageEncryptionMetadata metadata
});


@override $MessageEncryptionMetadataCopyWith<$Res> get metadata;

}
/// @nodoc
class __$MessageEncryptionModelCopyWithImpl<$Res>
    implements _$MessageEncryptionModelCopyWith<$Res> {
  __$MessageEncryptionModelCopyWithImpl(this._self, this._then);

  final _MessageEncryptionModel _self;
  final $Res Function(_MessageEncryptionModel) _then;

/// Create a copy of MessageEncryptionModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? messageId = null,Object? status = null,Object? type = null,Object? encryptedContent = null,Object? decryptedContent = freezed,Object? senderPublicKey = null,Object? recipientPublicKey = freezed,Object? createdAt = null,Object? decryptedAt = freezed,Object? metadata = null,}) {
  return _then(_MessageEncryptionModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,messageId: null == messageId ? _self.messageId : messageId // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as EncryptionStatus,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as EncryptionType,encryptedContent: null == encryptedContent ? _self.encryptedContent : encryptedContent // ignore: cast_nullable_to_non_nullable
as String,decryptedContent: freezed == decryptedContent ? _self.decryptedContent : decryptedContent // ignore: cast_nullable_to_non_nullable
as String?,senderPublicKey: null == senderPublicKey ? _self.senderPublicKey : senderPublicKey // ignore: cast_nullable_to_non_nullable
as String,recipientPublicKey: freezed == recipientPublicKey ? _self.recipientPublicKey : recipientPublicKey // ignore: cast_nullable_to_non_nullable
as String?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,decryptedAt: freezed == decryptedAt ? _self.decryptedAt : decryptedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,metadata: null == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as MessageEncryptionMetadata,
  ));
}

/// Create a copy of MessageEncryptionModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MessageEncryptionMetadataCopyWith<$Res> get metadata {
  
  return $MessageEncryptionMetadataCopyWith<$Res>(_self.metadata, (value) {
    return _then(_self.copyWith(metadata: value));
  });
}
}


/// @nodoc
mixin _$MessageEncryptionMetadata {

 String get algorithm; String get keySize; String? get sessionKey; bool get isForwardSecrecy; DateTime? get keyExpiry; Map<String, dynamic>? get additionalData;
/// Create a copy of MessageEncryptionMetadata
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MessageEncryptionMetadataCopyWith<MessageEncryptionMetadata> get copyWith => _$MessageEncryptionMetadataCopyWithImpl<MessageEncryptionMetadata>(this as MessageEncryptionMetadata, _$identity);

  /// Serializes this MessageEncryptionMetadata to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MessageEncryptionMetadata&&(identical(other.algorithm, algorithm) || other.algorithm == algorithm)&&(identical(other.keySize, keySize) || other.keySize == keySize)&&(identical(other.sessionKey, sessionKey) || other.sessionKey == sessionKey)&&(identical(other.isForwardSecrecy, isForwardSecrecy) || other.isForwardSecrecy == isForwardSecrecy)&&(identical(other.keyExpiry, keyExpiry) || other.keyExpiry == keyExpiry)&&const DeepCollectionEquality().equals(other.additionalData, additionalData));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,algorithm,keySize,sessionKey,isForwardSecrecy,keyExpiry,const DeepCollectionEquality().hash(additionalData));

@override
String toString() {
  return 'MessageEncryptionMetadata(algorithm: $algorithm, keySize: $keySize, sessionKey: $sessionKey, isForwardSecrecy: $isForwardSecrecy, keyExpiry: $keyExpiry, additionalData: $additionalData)';
}


}

/// @nodoc
abstract mixin class $MessageEncryptionMetadataCopyWith<$Res>  {
  factory $MessageEncryptionMetadataCopyWith(MessageEncryptionMetadata value, $Res Function(MessageEncryptionMetadata) _then) = _$MessageEncryptionMetadataCopyWithImpl;
@useResult
$Res call({
 String algorithm, String keySize, String? sessionKey, bool isForwardSecrecy, DateTime? keyExpiry, Map<String, dynamic>? additionalData
});




}
/// @nodoc
class _$MessageEncryptionMetadataCopyWithImpl<$Res>
    implements $MessageEncryptionMetadataCopyWith<$Res> {
  _$MessageEncryptionMetadataCopyWithImpl(this._self, this._then);

  final MessageEncryptionMetadata _self;
  final $Res Function(MessageEncryptionMetadata) _then;

/// Create a copy of MessageEncryptionMetadata
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? algorithm = null,Object? keySize = null,Object? sessionKey = freezed,Object? isForwardSecrecy = null,Object? keyExpiry = freezed,Object? additionalData = freezed,}) {
  return _then(_self.copyWith(
algorithm: null == algorithm ? _self.algorithm : algorithm // ignore: cast_nullable_to_non_nullable
as String,keySize: null == keySize ? _self.keySize : keySize // ignore: cast_nullable_to_non_nullable
as String,sessionKey: freezed == sessionKey ? _self.sessionKey : sessionKey // ignore: cast_nullable_to_non_nullable
as String?,isForwardSecrecy: null == isForwardSecrecy ? _self.isForwardSecrecy : isForwardSecrecy // ignore: cast_nullable_to_non_nullable
as bool,keyExpiry: freezed == keyExpiry ? _self.keyExpiry : keyExpiry // ignore: cast_nullable_to_non_nullable
as DateTime?,additionalData: freezed == additionalData ? _self.additionalData : additionalData // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [MessageEncryptionMetadata].
extension MessageEncryptionMetadataPatterns on MessageEncryptionMetadata {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _MessageEncryptionMetadata value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _MessageEncryptionMetadata() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _MessageEncryptionMetadata value)  $default,){
final _that = this;
switch (_that) {
case _MessageEncryptionMetadata():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _MessageEncryptionMetadata value)?  $default,){
final _that = this;
switch (_that) {
case _MessageEncryptionMetadata() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String algorithm,  String keySize,  String? sessionKey,  bool isForwardSecrecy,  DateTime? keyExpiry,  Map<String, dynamic>? additionalData)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _MessageEncryptionMetadata() when $default != null:
return $default(_that.algorithm,_that.keySize,_that.sessionKey,_that.isForwardSecrecy,_that.keyExpiry,_that.additionalData);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String algorithm,  String keySize,  String? sessionKey,  bool isForwardSecrecy,  DateTime? keyExpiry,  Map<String, dynamic>? additionalData)  $default,) {final _that = this;
switch (_that) {
case _MessageEncryptionMetadata():
return $default(_that.algorithm,_that.keySize,_that.sessionKey,_that.isForwardSecrecy,_that.keyExpiry,_that.additionalData);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String algorithm,  String keySize,  String? sessionKey,  bool isForwardSecrecy,  DateTime? keyExpiry,  Map<String, dynamic>? additionalData)?  $default,) {final _that = this;
switch (_that) {
case _MessageEncryptionMetadata() when $default != null:
return $default(_that.algorithm,_that.keySize,_that.sessionKey,_that.isForwardSecrecy,_that.keyExpiry,_that.additionalData);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _MessageEncryptionMetadata implements MessageEncryptionMetadata {
  const _MessageEncryptionMetadata({required this.algorithm, required this.keySize, required this.sessionKey, required this.isForwardSecrecy, required this.keyExpiry, required final  Map<String, dynamic>? additionalData}): _additionalData = additionalData;
  factory _MessageEncryptionMetadata.fromJson(Map<String, dynamic> json) => _$MessageEncryptionMetadataFromJson(json);

@override final  String algorithm;
@override final  String keySize;
@override final  String? sessionKey;
@override final  bool isForwardSecrecy;
@override final  DateTime? keyExpiry;
 final  Map<String, dynamic>? _additionalData;
@override Map<String, dynamic>? get additionalData {
  final value = _additionalData;
  if (value == null) return null;
  if (_additionalData is EqualUnmodifiableMapView) return _additionalData;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of MessageEncryptionMetadata
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MessageEncryptionMetadataCopyWith<_MessageEncryptionMetadata> get copyWith => __$MessageEncryptionMetadataCopyWithImpl<_MessageEncryptionMetadata>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$MessageEncryptionMetadataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _MessageEncryptionMetadata&&(identical(other.algorithm, algorithm) || other.algorithm == algorithm)&&(identical(other.keySize, keySize) || other.keySize == keySize)&&(identical(other.sessionKey, sessionKey) || other.sessionKey == sessionKey)&&(identical(other.isForwardSecrecy, isForwardSecrecy) || other.isForwardSecrecy == isForwardSecrecy)&&(identical(other.keyExpiry, keyExpiry) || other.keyExpiry == keyExpiry)&&const DeepCollectionEquality().equals(other._additionalData, _additionalData));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,algorithm,keySize,sessionKey,isForwardSecrecy,keyExpiry,const DeepCollectionEquality().hash(_additionalData));

@override
String toString() {
  return 'MessageEncryptionMetadata(algorithm: $algorithm, keySize: $keySize, sessionKey: $sessionKey, isForwardSecrecy: $isForwardSecrecy, keyExpiry: $keyExpiry, additionalData: $additionalData)';
}


}

/// @nodoc
abstract mixin class _$MessageEncryptionMetadataCopyWith<$Res> implements $MessageEncryptionMetadataCopyWith<$Res> {
  factory _$MessageEncryptionMetadataCopyWith(_MessageEncryptionMetadata value, $Res Function(_MessageEncryptionMetadata) _then) = __$MessageEncryptionMetadataCopyWithImpl;
@override @useResult
$Res call({
 String algorithm, String keySize, String? sessionKey, bool isForwardSecrecy, DateTime? keyExpiry, Map<String, dynamic>? additionalData
});




}
/// @nodoc
class __$MessageEncryptionMetadataCopyWithImpl<$Res>
    implements _$MessageEncryptionMetadataCopyWith<$Res> {
  __$MessageEncryptionMetadataCopyWithImpl(this._self, this._then);

  final _MessageEncryptionMetadata _self;
  final $Res Function(_MessageEncryptionMetadata) _then;

/// Create a copy of MessageEncryptionMetadata
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? algorithm = null,Object? keySize = null,Object? sessionKey = freezed,Object? isForwardSecrecy = null,Object? keyExpiry = freezed,Object? additionalData = freezed,}) {
  return _then(_MessageEncryptionMetadata(
algorithm: null == algorithm ? _self.algorithm : algorithm // ignore: cast_nullable_to_non_nullable
as String,keySize: null == keySize ? _self.keySize : keySize // ignore: cast_nullable_to_non_nullable
as String,sessionKey: freezed == sessionKey ? _self.sessionKey : sessionKey // ignore: cast_nullable_to_non_nullable
as String?,isForwardSecrecy: null == isForwardSecrecy ? _self.isForwardSecrecy : isForwardSecrecy // ignore: cast_nullable_to_non_nullable
as bool,keyExpiry: freezed == keyExpiry ? _self.keyExpiry : keyExpiry // ignore: cast_nullable_to_non_nullable
as DateTime?,additionalData: freezed == additionalData ? _self._additionalData : additionalData // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}


/// @nodoc
mixin _$EncryptionKeyModel {

 String get id; String get userId; String get publicKey; String? get privateKey;// Only stored locally
 KeyType get type; DateTime get createdAt; DateTime? get expiresAt; bool get isActive; List<String> get authorizedUsers;
/// Create a copy of EncryptionKeyModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$EncryptionKeyModelCopyWith<EncryptionKeyModel> get copyWith => _$EncryptionKeyModelCopyWithImpl<EncryptionKeyModel>(this as EncryptionKeyModel, _$identity);

  /// Serializes this EncryptionKeyModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EncryptionKeyModel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.publicKey, publicKey) || other.publicKey == publicKey)&&(identical(other.privateKey, privateKey) || other.privateKey == privateKey)&&(identical(other.type, type) || other.type == type)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.expiresAt, expiresAt) || other.expiresAt == expiresAt)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&const DeepCollectionEquality().equals(other.authorizedUsers, authorizedUsers));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,publicKey,privateKey,type,createdAt,expiresAt,isActive,const DeepCollectionEquality().hash(authorizedUsers));

@override
String toString() {
  return 'EncryptionKeyModel(id: $id, userId: $userId, publicKey: $publicKey, privateKey: $privateKey, type: $type, createdAt: $createdAt, expiresAt: $expiresAt, isActive: $isActive, authorizedUsers: $authorizedUsers)';
}


}

/// @nodoc
abstract mixin class $EncryptionKeyModelCopyWith<$Res>  {
  factory $EncryptionKeyModelCopyWith(EncryptionKeyModel value, $Res Function(EncryptionKeyModel) _then) = _$EncryptionKeyModelCopyWithImpl;
@useResult
$Res call({
 String id, String userId, String publicKey, String? privateKey, KeyType type, DateTime createdAt, DateTime? expiresAt, bool isActive, List<String> authorizedUsers
});




}
/// @nodoc
class _$EncryptionKeyModelCopyWithImpl<$Res>
    implements $EncryptionKeyModelCopyWith<$Res> {
  _$EncryptionKeyModelCopyWithImpl(this._self, this._then);

  final EncryptionKeyModel _self;
  final $Res Function(EncryptionKeyModel) _then;

/// Create a copy of EncryptionKeyModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? publicKey = null,Object? privateKey = freezed,Object? type = null,Object? createdAt = null,Object? expiresAt = freezed,Object? isActive = null,Object? authorizedUsers = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,publicKey: null == publicKey ? _self.publicKey : publicKey // ignore: cast_nullable_to_non_nullable
as String,privateKey: freezed == privateKey ? _self.privateKey : privateKey // ignore: cast_nullable_to_non_nullable
as String?,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as KeyType,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,expiresAt: freezed == expiresAt ? _self.expiresAt : expiresAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,authorizedUsers: null == authorizedUsers ? _self.authorizedUsers : authorizedUsers // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}

}


/// Adds pattern-matching-related methods to [EncryptionKeyModel].
extension EncryptionKeyModelPatterns on EncryptionKeyModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _EncryptionKeyModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _EncryptionKeyModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _EncryptionKeyModel value)  $default,){
final _that = this;
switch (_that) {
case _EncryptionKeyModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _EncryptionKeyModel value)?  $default,){
final _that = this;
switch (_that) {
case _EncryptionKeyModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  String publicKey,  String? privateKey,  KeyType type,  DateTime createdAt,  DateTime? expiresAt,  bool isActive,  List<String> authorizedUsers)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _EncryptionKeyModel() when $default != null:
return $default(_that.id,_that.userId,_that.publicKey,_that.privateKey,_that.type,_that.createdAt,_that.expiresAt,_that.isActive,_that.authorizedUsers);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  String publicKey,  String? privateKey,  KeyType type,  DateTime createdAt,  DateTime? expiresAt,  bool isActive,  List<String> authorizedUsers)  $default,) {final _that = this;
switch (_that) {
case _EncryptionKeyModel():
return $default(_that.id,_that.userId,_that.publicKey,_that.privateKey,_that.type,_that.createdAt,_that.expiresAt,_that.isActive,_that.authorizedUsers);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  String publicKey,  String? privateKey,  KeyType type,  DateTime createdAt,  DateTime? expiresAt,  bool isActive,  List<String> authorizedUsers)?  $default,) {final _that = this;
switch (_that) {
case _EncryptionKeyModel() when $default != null:
return $default(_that.id,_that.userId,_that.publicKey,_that.privateKey,_that.type,_that.createdAt,_that.expiresAt,_that.isActive,_that.authorizedUsers);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _EncryptionKeyModel implements EncryptionKeyModel {
  const _EncryptionKeyModel({required this.id, required this.userId, required this.publicKey, required this.privateKey, required this.type, required this.createdAt, required this.expiresAt, required this.isActive, required final  List<String> authorizedUsers}): _authorizedUsers = authorizedUsers;
  factory _EncryptionKeyModel.fromJson(Map<String, dynamic> json) => _$EncryptionKeyModelFromJson(json);

@override final  String id;
@override final  String userId;
@override final  String publicKey;
@override final  String? privateKey;
// Only stored locally
@override final  KeyType type;
@override final  DateTime createdAt;
@override final  DateTime? expiresAt;
@override final  bool isActive;
 final  List<String> _authorizedUsers;
@override List<String> get authorizedUsers {
  if (_authorizedUsers is EqualUnmodifiableListView) return _authorizedUsers;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_authorizedUsers);
}


/// Create a copy of EncryptionKeyModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$EncryptionKeyModelCopyWith<_EncryptionKeyModel> get copyWith => __$EncryptionKeyModelCopyWithImpl<_EncryptionKeyModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$EncryptionKeyModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _EncryptionKeyModel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.publicKey, publicKey) || other.publicKey == publicKey)&&(identical(other.privateKey, privateKey) || other.privateKey == privateKey)&&(identical(other.type, type) || other.type == type)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.expiresAt, expiresAt) || other.expiresAt == expiresAt)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&const DeepCollectionEquality().equals(other._authorizedUsers, _authorizedUsers));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,publicKey,privateKey,type,createdAt,expiresAt,isActive,const DeepCollectionEquality().hash(_authorizedUsers));

@override
String toString() {
  return 'EncryptionKeyModel(id: $id, userId: $userId, publicKey: $publicKey, privateKey: $privateKey, type: $type, createdAt: $createdAt, expiresAt: $expiresAt, isActive: $isActive, authorizedUsers: $authorizedUsers)';
}


}

/// @nodoc
abstract mixin class _$EncryptionKeyModelCopyWith<$Res> implements $EncryptionKeyModelCopyWith<$Res> {
  factory _$EncryptionKeyModelCopyWith(_EncryptionKeyModel value, $Res Function(_EncryptionKeyModel) _then) = __$EncryptionKeyModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, String publicKey, String? privateKey, KeyType type, DateTime createdAt, DateTime? expiresAt, bool isActive, List<String> authorizedUsers
});




}
/// @nodoc
class __$EncryptionKeyModelCopyWithImpl<$Res>
    implements _$EncryptionKeyModelCopyWith<$Res> {
  __$EncryptionKeyModelCopyWithImpl(this._self, this._then);

  final _EncryptionKeyModel _self;
  final $Res Function(_EncryptionKeyModel) _then;

/// Create a copy of EncryptionKeyModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? publicKey = null,Object? privateKey = freezed,Object? type = null,Object? createdAt = null,Object? expiresAt = freezed,Object? isActive = null,Object? authorizedUsers = null,}) {
  return _then(_EncryptionKeyModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,publicKey: null == publicKey ? _self.publicKey : publicKey // ignore: cast_nullable_to_non_nullable
as String,privateKey: freezed == privateKey ? _self.privateKey : privateKey // ignore: cast_nullable_to_non_nullable
as String?,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as KeyType,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,expiresAt: freezed == expiresAt ? _self.expiresAt : expiresAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,authorizedUsers: null == authorizedUsers ? _self._authorizedUsers : authorizedUsers // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}


}


/// @nodoc
mixin _$EncryptionSettings {

 bool get enableEndToEndEncryption; bool get requireEncryption; EncryptionAlgorithm get algorithm; KeyExchangeProtocol get keyExchange; bool get enableForwardSecrecy; int get keyRotationDays; bool get showEncryptionStatus; bool get verifyKeyFingerprints; bool get allowUnencryptedMessages; bool get autoEncryptMedia;
/// Create a copy of EncryptionSettings
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$EncryptionSettingsCopyWith<EncryptionSettings> get copyWith => _$EncryptionSettingsCopyWithImpl<EncryptionSettings>(this as EncryptionSettings, _$identity);

  /// Serializes this EncryptionSettings to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EncryptionSettings&&(identical(other.enableEndToEndEncryption, enableEndToEndEncryption) || other.enableEndToEndEncryption == enableEndToEndEncryption)&&(identical(other.requireEncryption, requireEncryption) || other.requireEncryption == requireEncryption)&&(identical(other.algorithm, algorithm) || other.algorithm == algorithm)&&(identical(other.keyExchange, keyExchange) || other.keyExchange == keyExchange)&&(identical(other.enableForwardSecrecy, enableForwardSecrecy) || other.enableForwardSecrecy == enableForwardSecrecy)&&(identical(other.keyRotationDays, keyRotationDays) || other.keyRotationDays == keyRotationDays)&&(identical(other.showEncryptionStatus, showEncryptionStatus) || other.showEncryptionStatus == showEncryptionStatus)&&(identical(other.verifyKeyFingerprints, verifyKeyFingerprints) || other.verifyKeyFingerprints == verifyKeyFingerprints)&&(identical(other.allowUnencryptedMessages, allowUnencryptedMessages) || other.allowUnencryptedMessages == allowUnencryptedMessages)&&(identical(other.autoEncryptMedia, autoEncryptMedia) || other.autoEncryptMedia == autoEncryptMedia));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,enableEndToEndEncryption,requireEncryption,algorithm,keyExchange,enableForwardSecrecy,keyRotationDays,showEncryptionStatus,verifyKeyFingerprints,allowUnencryptedMessages,autoEncryptMedia);

@override
String toString() {
  return 'EncryptionSettings(enableEndToEndEncryption: $enableEndToEndEncryption, requireEncryption: $requireEncryption, algorithm: $algorithm, keyExchange: $keyExchange, enableForwardSecrecy: $enableForwardSecrecy, keyRotationDays: $keyRotationDays, showEncryptionStatus: $showEncryptionStatus, verifyKeyFingerprints: $verifyKeyFingerprints, allowUnencryptedMessages: $allowUnencryptedMessages, autoEncryptMedia: $autoEncryptMedia)';
}


}

/// @nodoc
abstract mixin class $EncryptionSettingsCopyWith<$Res>  {
  factory $EncryptionSettingsCopyWith(EncryptionSettings value, $Res Function(EncryptionSettings) _then) = _$EncryptionSettingsCopyWithImpl;
@useResult
$Res call({
 bool enableEndToEndEncryption, bool requireEncryption, EncryptionAlgorithm algorithm, KeyExchangeProtocol keyExchange, bool enableForwardSecrecy, int keyRotationDays, bool showEncryptionStatus, bool verifyKeyFingerprints, bool allowUnencryptedMessages, bool autoEncryptMedia
});




}
/// @nodoc
class _$EncryptionSettingsCopyWithImpl<$Res>
    implements $EncryptionSettingsCopyWith<$Res> {
  _$EncryptionSettingsCopyWithImpl(this._self, this._then);

  final EncryptionSettings _self;
  final $Res Function(EncryptionSettings) _then;

/// Create a copy of EncryptionSettings
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? enableEndToEndEncryption = null,Object? requireEncryption = null,Object? algorithm = null,Object? keyExchange = null,Object? enableForwardSecrecy = null,Object? keyRotationDays = null,Object? showEncryptionStatus = null,Object? verifyKeyFingerprints = null,Object? allowUnencryptedMessages = null,Object? autoEncryptMedia = null,}) {
  return _then(_self.copyWith(
enableEndToEndEncryption: null == enableEndToEndEncryption ? _self.enableEndToEndEncryption : enableEndToEndEncryption // ignore: cast_nullable_to_non_nullable
as bool,requireEncryption: null == requireEncryption ? _self.requireEncryption : requireEncryption // ignore: cast_nullable_to_non_nullable
as bool,algorithm: null == algorithm ? _self.algorithm : algorithm // ignore: cast_nullable_to_non_nullable
as EncryptionAlgorithm,keyExchange: null == keyExchange ? _self.keyExchange : keyExchange // ignore: cast_nullable_to_non_nullable
as KeyExchangeProtocol,enableForwardSecrecy: null == enableForwardSecrecy ? _self.enableForwardSecrecy : enableForwardSecrecy // ignore: cast_nullable_to_non_nullable
as bool,keyRotationDays: null == keyRotationDays ? _self.keyRotationDays : keyRotationDays // ignore: cast_nullable_to_non_nullable
as int,showEncryptionStatus: null == showEncryptionStatus ? _self.showEncryptionStatus : showEncryptionStatus // ignore: cast_nullable_to_non_nullable
as bool,verifyKeyFingerprints: null == verifyKeyFingerprints ? _self.verifyKeyFingerprints : verifyKeyFingerprints // ignore: cast_nullable_to_non_nullable
as bool,allowUnencryptedMessages: null == allowUnencryptedMessages ? _self.allowUnencryptedMessages : allowUnencryptedMessages // ignore: cast_nullable_to_non_nullable
as bool,autoEncryptMedia: null == autoEncryptMedia ? _self.autoEncryptMedia : autoEncryptMedia // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [EncryptionSettings].
extension EncryptionSettingsPatterns on EncryptionSettings {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _EncryptionSettings value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _EncryptionSettings() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _EncryptionSettings value)  $default,){
final _that = this;
switch (_that) {
case _EncryptionSettings():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _EncryptionSettings value)?  $default,){
final _that = this;
switch (_that) {
case _EncryptionSettings() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( bool enableEndToEndEncryption,  bool requireEncryption,  EncryptionAlgorithm algorithm,  KeyExchangeProtocol keyExchange,  bool enableForwardSecrecy,  int keyRotationDays,  bool showEncryptionStatus,  bool verifyKeyFingerprints,  bool allowUnencryptedMessages,  bool autoEncryptMedia)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _EncryptionSettings() when $default != null:
return $default(_that.enableEndToEndEncryption,_that.requireEncryption,_that.algorithm,_that.keyExchange,_that.enableForwardSecrecy,_that.keyRotationDays,_that.showEncryptionStatus,_that.verifyKeyFingerprints,_that.allowUnencryptedMessages,_that.autoEncryptMedia);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( bool enableEndToEndEncryption,  bool requireEncryption,  EncryptionAlgorithm algorithm,  KeyExchangeProtocol keyExchange,  bool enableForwardSecrecy,  int keyRotationDays,  bool showEncryptionStatus,  bool verifyKeyFingerprints,  bool allowUnencryptedMessages,  bool autoEncryptMedia)  $default,) {final _that = this;
switch (_that) {
case _EncryptionSettings():
return $default(_that.enableEndToEndEncryption,_that.requireEncryption,_that.algorithm,_that.keyExchange,_that.enableForwardSecrecy,_that.keyRotationDays,_that.showEncryptionStatus,_that.verifyKeyFingerprints,_that.allowUnencryptedMessages,_that.autoEncryptMedia);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( bool enableEndToEndEncryption,  bool requireEncryption,  EncryptionAlgorithm algorithm,  KeyExchangeProtocol keyExchange,  bool enableForwardSecrecy,  int keyRotationDays,  bool showEncryptionStatus,  bool verifyKeyFingerprints,  bool allowUnencryptedMessages,  bool autoEncryptMedia)?  $default,) {final _that = this;
switch (_that) {
case _EncryptionSettings() when $default != null:
return $default(_that.enableEndToEndEncryption,_that.requireEncryption,_that.algorithm,_that.keyExchange,_that.enableForwardSecrecy,_that.keyRotationDays,_that.showEncryptionStatus,_that.verifyKeyFingerprints,_that.allowUnencryptedMessages,_that.autoEncryptMedia);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _EncryptionSettings implements EncryptionSettings {
  const _EncryptionSettings({this.enableEndToEndEncryption = true, this.requireEncryption = true, this.algorithm = EncryptionAlgorithm.aes256, this.keyExchange = KeyExchangeProtocol.diffieHellman, this.enableForwardSecrecy = true, this.keyRotationDays = 30, this.showEncryptionStatus = true, this.verifyKeyFingerprints = true, this.allowUnencryptedMessages = false, this.autoEncryptMedia = true});
  factory _EncryptionSettings.fromJson(Map<String, dynamic> json) => _$EncryptionSettingsFromJson(json);

@override@JsonKey() final  bool enableEndToEndEncryption;
@override@JsonKey() final  bool requireEncryption;
@override@JsonKey() final  EncryptionAlgorithm algorithm;
@override@JsonKey() final  KeyExchangeProtocol keyExchange;
@override@JsonKey() final  bool enableForwardSecrecy;
@override@JsonKey() final  int keyRotationDays;
@override@JsonKey() final  bool showEncryptionStatus;
@override@JsonKey() final  bool verifyKeyFingerprints;
@override@JsonKey() final  bool allowUnencryptedMessages;
@override@JsonKey() final  bool autoEncryptMedia;

/// Create a copy of EncryptionSettings
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$EncryptionSettingsCopyWith<_EncryptionSettings> get copyWith => __$EncryptionSettingsCopyWithImpl<_EncryptionSettings>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$EncryptionSettingsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _EncryptionSettings&&(identical(other.enableEndToEndEncryption, enableEndToEndEncryption) || other.enableEndToEndEncryption == enableEndToEndEncryption)&&(identical(other.requireEncryption, requireEncryption) || other.requireEncryption == requireEncryption)&&(identical(other.algorithm, algorithm) || other.algorithm == algorithm)&&(identical(other.keyExchange, keyExchange) || other.keyExchange == keyExchange)&&(identical(other.enableForwardSecrecy, enableForwardSecrecy) || other.enableForwardSecrecy == enableForwardSecrecy)&&(identical(other.keyRotationDays, keyRotationDays) || other.keyRotationDays == keyRotationDays)&&(identical(other.showEncryptionStatus, showEncryptionStatus) || other.showEncryptionStatus == showEncryptionStatus)&&(identical(other.verifyKeyFingerprints, verifyKeyFingerprints) || other.verifyKeyFingerprints == verifyKeyFingerprints)&&(identical(other.allowUnencryptedMessages, allowUnencryptedMessages) || other.allowUnencryptedMessages == allowUnencryptedMessages)&&(identical(other.autoEncryptMedia, autoEncryptMedia) || other.autoEncryptMedia == autoEncryptMedia));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,enableEndToEndEncryption,requireEncryption,algorithm,keyExchange,enableForwardSecrecy,keyRotationDays,showEncryptionStatus,verifyKeyFingerprints,allowUnencryptedMessages,autoEncryptMedia);

@override
String toString() {
  return 'EncryptionSettings(enableEndToEndEncryption: $enableEndToEndEncryption, requireEncryption: $requireEncryption, algorithm: $algorithm, keyExchange: $keyExchange, enableForwardSecrecy: $enableForwardSecrecy, keyRotationDays: $keyRotationDays, showEncryptionStatus: $showEncryptionStatus, verifyKeyFingerprints: $verifyKeyFingerprints, allowUnencryptedMessages: $allowUnencryptedMessages, autoEncryptMedia: $autoEncryptMedia)';
}


}

/// @nodoc
abstract mixin class _$EncryptionSettingsCopyWith<$Res> implements $EncryptionSettingsCopyWith<$Res> {
  factory _$EncryptionSettingsCopyWith(_EncryptionSettings value, $Res Function(_EncryptionSettings) _then) = __$EncryptionSettingsCopyWithImpl;
@override @useResult
$Res call({
 bool enableEndToEndEncryption, bool requireEncryption, EncryptionAlgorithm algorithm, KeyExchangeProtocol keyExchange, bool enableForwardSecrecy, int keyRotationDays, bool showEncryptionStatus, bool verifyKeyFingerprints, bool allowUnencryptedMessages, bool autoEncryptMedia
});




}
/// @nodoc
class __$EncryptionSettingsCopyWithImpl<$Res>
    implements _$EncryptionSettingsCopyWith<$Res> {
  __$EncryptionSettingsCopyWithImpl(this._self, this._then);

  final _EncryptionSettings _self;
  final $Res Function(_EncryptionSettings) _then;

/// Create a copy of EncryptionSettings
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? enableEndToEndEncryption = null,Object? requireEncryption = null,Object? algorithm = null,Object? keyExchange = null,Object? enableForwardSecrecy = null,Object? keyRotationDays = null,Object? showEncryptionStatus = null,Object? verifyKeyFingerprints = null,Object? allowUnencryptedMessages = null,Object? autoEncryptMedia = null,}) {
  return _then(_EncryptionSettings(
enableEndToEndEncryption: null == enableEndToEndEncryption ? _self.enableEndToEndEncryption : enableEndToEndEncryption // ignore: cast_nullable_to_non_nullable
as bool,requireEncryption: null == requireEncryption ? _self.requireEncryption : requireEncryption // ignore: cast_nullable_to_non_nullable
as bool,algorithm: null == algorithm ? _self.algorithm : algorithm // ignore: cast_nullable_to_non_nullable
as EncryptionAlgorithm,keyExchange: null == keyExchange ? _self.keyExchange : keyExchange // ignore: cast_nullable_to_non_nullable
as KeyExchangeProtocol,enableForwardSecrecy: null == enableForwardSecrecy ? _self.enableForwardSecrecy : enableForwardSecrecy // ignore: cast_nullable_to_non_nullable
as bool,keyRotationDays: null == keyRotationDays ? _self.keyRotationDays : keyRotationDays // ignore: cast_nullable_to_non_nullable
as int,showEncryptionStatus: null == showEncryptionStatus ? _self.showEncryptionStatus : showEncryptionStatus // ignore: cast_nullable_to_non_nullable
as bool,verifyKeyFingerprints: null == verifyKeyFingerprints ? _self.verifyKeyFingerprints : verifyKeyFingerprints // ignore: cast_nullable_to_non_nullable
as bool,allowUnencryptedMessages: null == allowUnencryptedMessages ? _self.allowUnencryptedMessages : allowUnencryptedMessages // ignore: cast_nullable_to_non_nullable
as bool,autoEncryptMedia: null == autoEncryptMedia ? _self.autoEncryptMedia : autoEncryptMedia // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$KeyExchangeSession {

 String get id; String get initiatorId; String get recipientId; KeyExchangeStatus get status; String get initiatorPublicKey; String? get recipientPublicKey; String? get sharedSecret; DateTime get createdAt; DateTime? get completedAt; DateTime? get expiresAt;
/// Create a copy of KeyExchangeSession
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$KeyExchangeSessionCopyWith<KeyExchangeSession> get copyWith => _$KeyExchangeSessionCopyWithImpl<KeyExchangeSession>(this as KeyExchangeSession, _$identity);

  /// Serializes this KeyExchangeSession to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is KeyExchangeSession&&(identical(other.id, id) || other.id == id)&&(identical(other.initiatorId, initiatorId) || other.initiatorId == initiatorId)&&(identical(other.recipientId, recipientId) || other.recipientId == recipientId)&&(identical(other.status, status) || other.status == status)&&(identical(other.initiatorPublicKey, initiatorPublicKey) || other.initiatorPublicKey == initiatorPublicKey)&&(identical(other.recipientPublicKey, recipientPublicKey) || other.recipientPublicKey == recipientPublicKey)&&(identical(other.sharedSecret, sharedSecret) || other.sharedSecret == sharedSecret)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.completedAt, completedAt) || other.completedAt == completedAt)&&(identical(other.expiresAt, expiresAt) || other.expiresAt == expiresAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,initiatorId,recipientId,status,initiatorPublicKey,recipientPublicKey,sharedSecret,createdAt,completedAt,expiresAt);

@override
String toString() {
  return 'KeyExchangeSession(id: $id, initiatorId: $initiatorId, recipientId: $recipientId, status: $status, initiatorPublicKey: $initiatorPublicKey, recipientPublicKey: $recipientPublicKey, sharedSecret: $sharedSecret, createdAt: $createdAt, completedAt: $completedAt, expiresAt: $expiresAt)';
}


}

/// @nodoc
abstract mixin class $KeyExchangeSessionCopyWith<$Res>  {
  factory $KeyExchangeSessionCopyWith(KeyExchangeSession value, $Res Function(KeyExchangeSession) _then) = _$KeyExchangeSessionCopyWithImpl;
@useResult
$Res call({
 String id, String initiatorId, String recipientId, KeyExchangeStatus status, String initiatorPublicKey, String? recipientPublicKey, String? sharedSecret, DateTime createdAt, DateTime? completedAt, DateTime? expiresAt
});




}
/// @nodoc
class _$KeyExchangeSessionCopyWithImpl<$Res>
    implements $KeyExchangeSessionCopyWith<$Res> {
  _$KeyExchangeSessionCopyWithImpl(this._self, this._then);

  final KeyExchangeSession _self;
  final $Res Function(KeyExchangeSession) _then;

/// Create a copy of KeyExchangeSession
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? initiatorId = null,Object? recipientId = null,Object? status = null,Object? initiatorPublicKey = null,Object? recipientPublicKey = freezed,Object? sharedSecret = freezed,Object? createdAt = null,Object? completedAt = freezed,Object? expiresAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,initiatorId: null == initiatorId ? _self.initiatorId : initiatorId // ignore: cast_nullable_to_non_nullable
as String,recipientId: null == recipientId ? _self.recipientId : recipientId // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as KeyExchangeStatus,initiatorPublicKey: null == initiatorPublicKey ? _self.initiatorPublicKey : initiatorPublicKey // ignore: cast_nullable_to_non_nullable
as String,recipientPublicKey: freezed == recipientPublicKey ? _self.recipientPublicKey : recipientPublicKey // ignore: cast_nullable_to_non_nullable
as String?,sharedSecret: freezed == sharedSecret ? _self.sharedSecret : sharedSecret // ignore: cast_nullable_to_non_nullable
as String?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,completedAt: freezed == completedAt ? _self.completedAt : completedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,expiresAt: freezed == expiresAt ? _self.expiresAt : expiresAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [KeyExchangeSession].
extension KeyExchangeSessionPatterns on KeyExchangeSession {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _KeyExchangeSession value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _KeyExchangeSession() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _KeyExchangeSession value)  $default,){
final _that = this;
switch (_that) {
case _KeyExchangeSession():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _KeyExchangeSession value)?  $default,){
final _that = this;
switch (_that) {
case _KeyExchangeSession() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String initiatorId,  String recipientId,  KeyExchangeStatus status,  String initiatorPublicKey,  String? recipientPublicKey,  String? sharedSecret,  DateTime createdAt,  DateTime? completedAt,  DateTime? expiresAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _KeyExchangeSession() when $default != null:
return $default(_that.id,_that.initiatorId,_that.recipientId,_that.status,_that.initiatorPublicKey,_that.recipientPublicKey,_that.sharedSecret,_that.createdAt,_that.completedAt,_that.expiresAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String initiatorId,  String recipientId,  KeyExchangeStatus status,  String initiatorPublicKey,  String? recipientPublicKey,  String? sharedSecret,  DateTime createdAt,  DateTime? completedAt,  DateTime? expiresAt)  $default,) {final _that = this;
switch (_that) {
case _KeyExchangeSession():
return $default(_that.id,_that.initiatorId,_that.recipientId,_that.status,_that.initiatorPublicKey,_that.recipientPublicKey,_that.sharedSecret,_that.createdAt,_that.completedAt,_that.expiresAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String initiatorId,  String recipientId,  KeyExchangeStatus status,  String initiatorPublicKey,  String? recipientPublicKey,  String? sharedSecret,  DateTime createdAt,  DateTime? completedAt,  DateTime? expiresAt)?  $default,) {final _that = this;
switch (_that) {
case _KeyExchangeSession() when $default != null:
return $default(_that.id,_that.initiatorId,_that.recipientId,_that.status,_that.initiatorPublicKey,_that.recipientPublicKey,_that.sharedSecret,_that.createdAt,_that.completedAt,_that.expiresAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _KeyExchangeSession implements KeyExchangeSession {
  const _KeyExchangeSession({required this.id, required this.initiatorId, required this.recipientId, required this.status, required this.initiatorPublicKey, required this.recipientPublicKey, required this.sharedSecret, required this.createdAt, required this.completedAt, required this.expiresAt});
  factory _KeyExchangeSession.fromJson(Map<String, dynamic> json) => _$KeyExchangeSessionFromJson(json);

@override final  String id;
@override final  String initiatorId;
@override final  String recipientId;
@override final  KeyExchangeStatus status;
@override final  String initiatorPublicKey;
@override final  String? recipientPublicKey;
@override final  String? sharedSecret;
@override final  DateTime createdAt;
@override final  DateTime? completedAt;
@override final  DateTime? expiresAt;

/// Create a copy of KeyExchangeSession
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$KeyExchangeSessionCopyWith<_KeyExchangeSession> get copyWith => __$KeyExchangeSessionCopyWithImpl<_KeyExchangeSession>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$KeyExchangeSessionToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _KeyExchangeSession&&(identical(other.id, id) || other.id == id)&&(identical(other.initiatorId, initiatorId) || other.initiatorId == initiatorId)&&(identical(other.recipientId, recipientId) || other.recipientId == recipientId)&&(identical(other.status, status) || other.status == status)&&(identical(other.initiatorPublicKey, initiatorPublicKey) || other.initiatorPublicKey == initiatorPublicKey)&&(identical(other.recipientPublicKey, recipientPublicKey) || other.recipientPublicKey == recipientPublicKey)&&(identical(other.sharedSecret, sharedSecret) || other.sharedSecret == sharedSecret)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.completedAt, completedAt) || other.completedAt == completedAt)&&(identical(other.expiresAt, expiresAt) || other.expiresAt == expiresAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,initiatorId,recipientId,status,initiatorPublicKey,recipientPublicKey,sharedSecret,createdAt,completedAt,expiresAt);

@override
String toString() {
  return 'KeyExchangeSession(id: $id, initiatorId: $initiatorId, recipientId: $recipientId, status: $status, initiatorPublicKey: $initiatorPublicKey, recipientPublicKey: $recipientPublicKey, sharedSecret: $sharedSecret, createdAt: $createdAt, completedAt: $completedAt, expiresAt: $expiresAt)';
}


}

/// @nodoc
abstract mixin class _$KeyExchangeSessionCopyWith<$Res> implements $KeyExchangeSessionCopyWith<$Res> {
  factory _$KeyExchangeSessionCopyWith(_KeyExchangeSession value, $Res Function(_KeyExchangeSession) _then) = __$KeyExchangeSessionCopyWithImpl;
@override @useResult
$Res call({
 String id, String initiatorId, String recipientId, KeyExchangeStatus status, String initiatorPublicKey, String? recipientPublicKey, String? sharedSecret, DateTime createdAt, DateTime? completedAt, DateTime? expiresAt
});




}
/// @nodoc
class __$KeyExchangeSessionCopyWithImpl<$Res>
    implements _$KeyExchangeSessionCopyWith<$Res> {
  __$KeyExchangeSessionCopyWithImpl(this._self, this._then);

  final _KeyExchangeSession _self;
  final $Res Function(_KeyExchangeSession) _then;

/// Create a copy of KeyExchangeSession
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? initiatorId = null,Object? recipientId = null,Object? status = null,Object? initiatorPublicKey = null,Object? recipientPublicKey = freezed,Object? sharedSecret = freezed,Object? createdAt = null,Object? completedAt = freezed,Object? expiresAt = freezed,}) {
  return _then(_KeyExchangeSession(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,initiatorId: null == initiatorId ? _self.initiatorId : initiatorId // ignore: cast_nullable_to_non_nullable
as String,recipientId: null == recipientId ? _self.recipientId : recipientId // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as KeyExchangeStatus,initiatorPublicKey: null == initiatorPublicKey ? _self.initiatorPublicKey : initiatorPublicKey // ignore: cast_nullable_to_non_nullable
as String,recipientPublicKey: freezed == recipientPublicKey ? _self.recipientPublicKey : recipientPublicKey // ignore: cast_nullable_to_non_nullable
as String?,sharedSecret: freezed == sharedSecret ? _self.sharedSecret : sharedSecret // ignore: cast_nullable_to_non_nullable
as String?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,completedAt: freezed == completedAt ? _self.completedAt : completedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,expiresAt: freezed == expiresAt ? _self.expiresAt : expiresAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}

// dart format on
