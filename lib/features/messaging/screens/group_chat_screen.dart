import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:image_picker/image_picker.dart';
import 'package:timeago/timeago.dart' as timeago;

class GroupChatScreen extends StatefulWidget {
  final String? groupId;
  final String? groupName;
  final List<GroupMember>? initialMembers;

  const GroupChatScreen({
    super.key,
    this.groupId,
    this.groupName,
    this.initialMembers,
  });

  @override
  State<GroupChatScreen> createState() => _GroupChatScreenState();
}

class _GroupChatScreenState extends State<GroupChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final ImagePicker _imagePicker = ImagePicker();

  List<GroupMessage> _messages = [];
  List<GroupMember> _members = [];
  final bool _isLoading = false;
  final bool _isTyping = false;
  final String _typingUser = '';

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    _loadMessages();
  }

  void _loadInitialData() {
    // Initialize with sample data for demo
    _members =
        widget.initialMembers ??
        [
          GroupMember(
            id: '1',
            name: 'Elon Musk',
            avatar: 'https://example.com/elon.jpg',
            isOnline: true,
            isAdmin: true,
          ),
          GroupMember(
            id: '2',
            name: 'Jeff Bezos',
            avatar: 'https://example.com/jeff.jpg',
            isOnline: true,
            isAdmin: false,
          ),
          GroupMember(
            id: '3',
            name: 'Mark Zuckerberg',
            avatar: 'https://example.com/mark.jpg',
            isOnline: false,
            isAdmin: false,
          ),
          GroupMember(
            id: '4',
            name: 'Bill Gates',
            avatar: 'https://example.com/bill.jpg',
            isOnline: true,
            isAdmin: false,
          ),
        ];
  }

  void _loadMessages() {
    // Load sample messages for demo
    _messages = [
      GroupMessage(
        id: '1',
        senderId: '1',
        senderName: 'Elon Musk',
        senderAvatar: 'https://example.com/elon.jpg',
        content: 'Hey everyone! How\'s the market looking today?',
        timestamp: DateTime.now().subtract(const Duration(minutes: 5)),
        type: MessageType.text,
      ),
      GroupMessage(
        id: '2',
        senderId: '2',
        senderName: 'Jeff Bezos',
        senderAvatar: 'https://example.com/jeff.jpg',
        content:
            'Pretty volatile. I\'m seeing some interesting opportunities in tech.',
        timestamp: DateTime.now().subtract(const Duration(minutes: 3)),
        type: MessageType.text,
      ),
      GroupMessage(
        id: '3',
        senderId: '3',
        senderName: 'Mark Zuckerberg',
        senderAvatar: 'https://example.com/mark.jpg',
        content: 'Anyone up for a virtual meeting later?',
        timestamp: DateTime.now().subtract(const Duration(minutes: 1)),
        type: MessageType.text,
      ),
    ];
  }

  void _sendMessage() {
    if (_messageController.text.trim().isEmpty) return;

    final message = GroupMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      senderId: 'current_user',
      senderName: 'You',
      senderAvatar: 'https://example.com/current_user.jpg',
      content: _messageController.text.trim(),
      timestamp: DateTime.now(),
      type: MessageType.text,
    );

    setState(() {
      _messages.add(message);
    });

    _messageController.clear();
    _scrollToBottom();
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  Future<void> _pickImage() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
      );

      if (image != null) {
        // TODO: Upload image and send message
        final message = GroupMessage(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          senderId: 'current_user',
          senderName: 'You',
          senderAvatar: 'https://example.com/current_user.jpg',
          content: image.path,
          timestamp: DateTime.now(),
          type: MessageType.image,
        );

        setState(() {
          _messages.add(message);
        });

        _scrollToBottom();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error picking image: $e')));
      }
    }
  }

  void _showGroupInfo() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => _buildGroupInfoModal(),
    );
  }

  Widget _buildGroupInfoModal() {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[600],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          const SizedBox(height: 20),

          // Group info
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              children: [
                // Group avatar
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: Colors.grey[800],
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white24, width: 2),
                  ),
                  child: const Icon(
                    Icons.group,
                    size: 40,
                    color: Colors.white54,
                  ),
                ),

                const SizedBox(height: 16),

                // Group name
                Text(
                  widget.groupName ?? 'Billionaires Club',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),

                const SizedBox(height: 8),

                // Member count
                Text(
                  '${_members.length} members',
                  style: TextStyle(color: Colors.grey[400], fontSize: 16),
                ),

                const SizedBox(height: 24),

                // Action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildActionButton(
                      icon: FontAwesomeIcons.video,
                      label: 'Video Call',
                      onTap: () {
                        Navigator.pop(context);
                        // TODO: Start video call
                      },
                    ),
                    _buildActionButton(
                      icon: FontAwesomeIcons.phone,
                      label: 'Voice Call',
                      onTap: () {
                        Navigator.pop(context);
                        _startVoiceCall();
                      },
                    ),
                    _buildActionButton(
                      icon: FontAwesomeIcons.share,
                      label: 'Share',
                      onTap: () {
                        Navigator.pop(context);
                        // TODO: Share group
                      },
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // Members list
                const Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    'Members',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Members
                Expanded(
                  child: ListView.builder(
                    itemCount: _members.length,
                    itemBuilder: (context, index) {
                      final member = _members[index];
                      return _buildMemberTile(member);
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: Colors.grey[900],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: Colors.white, size: 20),
          ),
          const SizedBox(height: 8),
          Text(label, style: TextStyle(color: Colors.grey[400], fontSize: 12)),
        ],
      ),
    );
  }

  Widget _buildMemberTile(GroupMember member) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          // Avatar
          Stack(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundImage: CachedNetworkImageProvider(member.avatar),
                onBackgroundImageError: (exception, stackTrace) {
                  // Handle error
                },
              ),
              if (member.isOnline)
                Positioned(
                  right: 0,
                  bottom: 0,
                  child: Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: Colors.green,
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.black, width: 2),
                    ),
                  ),
                ),
            ],
          ),

          const SizedBox(width: 12),

          // Member info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      member.name,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    if (member.isAdmin) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.amber,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Text(
                          'Admin',
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                Text(
                  member.isOnline ? 'Online' : 'Offline',
                  style: TextStyle(
                    color: member.isOnline ? Colors.green : Colors.grey[400],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),

          // Actions
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert, color: Colors.white54),
            onSelected: (value) {
              // Handle member actions
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'profile',
                child: Text('View Profile'),
              ),
              const PopupMenuItem(
                value: 'message',
                child: Text('Send Message'),
              ),
              if (member.isAdmin)
                const PopupMenuItem(
                  value: 'remove_admin',
                  child: Text('Remove Admin'),
                )
              else
                const PopupMenuItem(
                  value: 'make_admin',
                  child: Text('Make Admin'),
                ),
              const PopupMenuItem(
                value: 'remove',
                child: Text('Remove from Group'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarColor: Colors.black,
        systemNavigationBarIconBrightness: Brightness.light,
      ),
      child: Scaffold(
        backgroundColor: Colors.black,
        appBar: AppBar(
          backgroundColor: Colors.black,
          elevation: 0,
          leading: IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.arrow_back, color: Colors.white),
          ),
          title: GestureDetector(
            onTap: _showGroupInfo,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.groupName ?? 'Billionaires Club',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${_members.length} members',
                  style: TextStyle(color: Colors.grey[400], fontSize: 12),
                ),
              ],
            ),
          ),
          actions: [
            IconButton(
              onPressed: () {
                // TODO: Start video call
              },
              icon: const Icon(FontAwesomeIcons.video, color: Colors.white),
            ),
            IconButton(
              onPressed: _startVoiceCall,
              icon: const Icon(FontAwesomeIcons.phone, color: Colors.white),
            ),
            IconButton(
              onPressed: _showGroupInfo,
              icon: const Icon(Icons.info_outline, color: Colors.white),
            ),
          ],
        ),
        body: Column(
          children: [
            // Messages
            Expanded(
              child: _isLoading
                  ? const Center(
                      child: CircularProgressIndicator(color: Colors.white),
                    )
                  : ListView.builder(
                      controller: _scrollController,
                      padding: const EdgeInsets.all(16),
                      itemCount: _messages.length,
                      itemBuilder: (context, index) {
                        final message = _messages[index];
                        return _buildMessageTile(message);
                      },
                    ),
            ),

            // Typing indicator
            if (_isTyping)
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                child: Row(
                  children: [
                    Text(
                      '$_typingUser is typing...',
                      style: TextStyle(color: Colors.grey[400], fontSize: 12),
                    ),
                  ],
                ),
              ),

            // Message input
            _buildMessageInput(),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageTile(GroupMessage message) {
    final isCurrentUser = message.senderId == 'current_user';
    final showAvatar = !isCurrentUser;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (showAvatar) ...[
            CircleAvatar(
              radius: 16,
              backgroundImage: _isValidImageUrl(message.senderAvatar)
                  ? CachedNetworkImageProvider(message.senderAvatar)
                  : null,
              backgroundColor: Colors.grey[300],
              child: !_isValidImageUrl(message.senderAvatar)
                  ? const Icon(Icons.person, size: 16, color: Colors.grey)
                  : null,
              onBackgroundImageError: (exception, stackTrace) {
                // Handle error
              },
            ),
            const SizedBox(width: 8),
          ] else
            const SizedBox(width: 40),

          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (showAvatar) ...[
                  Text(
                    message.senderName,
                    style: TextStyle(
                      color: Colors.grey[400],
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                ],

                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: isCurrentUser ? Colors.blue : Colors.grey[900],
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: message.type == MessageType.text
                      ? Text(
                          message.content,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                          ),
                        )
                      : ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.network(
                            message.content,
                            width: 200,
                            height: 150,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                width: 200,
                                height: 150,
                                color: Colors.grey[800],
                                child: const Icon(
                                  Icons.image_not_supported,
                                  color: Colors.white54,
                                ),
                              );
                            },
                          ),
                        ),
                ),

                const SizedBox(height: 4),

                Text(
                  timeago.format(message.timestamp),
                  style: TextStyle(color: Colors.grey[500], fontSize: 10),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        border: Border(top: BorderSide(color: Colors.grey[800]!)),
      ),
      child: Row(
        children: [
          // Attachment button
          IconButton(
            onPressed: _pickImage,
            icon: const Icon(Icons.attach_file, color: Colors.white54),
          ),

          // Message input
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.grey[800],
                borderRadius: BorderRadius.circular(24),
              ),
              child: TextField(
                controller: _messageController,
                style: const TextStyle(color: Colors.white),
                decoration: const InputDecoration(
                  hintText: 'Type a message...',
                  hintStyle: TextStyle(color: Colors.grey),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
                maxLines: null,
                textCapitalization: TextCapitalization.sentences,
              ),
            ),
          ),

          const SizedBox(width: 8),

          // Send button
          Container(
            decoration: const BoxDecoration(
              color: Colors.blue,
              shape: BoxShape.circle,
            ),
            child: IconButton(
              onPressed: _sendMessage,
              icon: const Icon(Icons.send, color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  void _startVoiceCall() {
    // Show voice call initiation dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Start Voice Call'),
        content: Text('Starting voice call with ${widget.groupName}...'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Integrate with actual voice calling service (e.g., Agora, WebRTC)
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Voice calling feature coming soon!'),
                ),
              );
            },
            child: const Text('Start Call'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  bool _isValidImageUrl(String? url) {
    if (url == null || url.isEmpty) return false;
    return url.startsWith('http://') || url.startsWith('https://');
  }
}

// Models
class GroupMember {
  final String id;
  final String name;
  final String avatar;
  final bool isOnline;
  final bool isAdmin;

  const GroupMember({
    required this.id,
    required this.name,
    required this.avatar,
    required this.isOnline,
    required this.isAdmin,
  });
}

class GroupMessage {
  final String id;
  final String senderId;
  final String senderName;
  final String senderAvatar;
  final String content;
  final DateTime timestamp;
  final MessageType type;

  const GroupMessage({
    required this.id,
    required this.senderId,
    required this.senderName,
    required this.senderAvatar,
    required this.content,
    required this.timestamp,
    required this.type,
  });
}

enum MessageType { text, image, video, audio, file }
