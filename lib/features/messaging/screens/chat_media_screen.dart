import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/core/widgets/back_button_widget.dart';
import 'package:billionaires_social/features/messaging/models/chat_model.dart';
import 'package:billionaires_social/features/messaging/providers/chat_provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:share_plus/share_plus.dart';

class ChatMediaScreen extends ConsumerStatefulWidget {
  final ChatModel chat;

  const ChatMediaScreen({super.key, required this.chat});

  @override
  ConsumerState<ChatMediaScreen> createState() => _ChatMediaScreenState();
}

class _ChatMediaScreenState extends ConsumerState<ChatMediaScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  String _searchQuery = '';
  bool _isSearching = false;
  List<MessageModel> _filteredMedia = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.primaryColor,
      appBar: AppBar(
        backgroundColor: AppTheme.primaryColor,
        elevation: 0,
        leading: const CustomBackButton(size: 36),
        title: _isSearching
            ? TextField(
                autofocus: true,
                style: AppTheme.fontStyles.body.copyWith(
                  color: AppTheme.accentColor,
                ),
                decoration: InputDecoration(
                  hintText: 'Search media...',
                  hintStyle: AppTheme.fontStyles.body.copyWith(
                    color: AppTheme.secondaryAccentColor,
                  ),
                  border: InputBorder.none,
                ),
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value;
                  });
                  _filterMedia();
                },
              )
            : Text(
                'Media',
                style: AppTheme.fontStyles.title.copyWith(
                  color: AppTheme.accentColor,
                ),
              ),
        actions: [
          IconButton(
            onPressed: () {
              setState(() {
                _isSearching = !_isSearching;
                if (!_isSearching) {
                  _searchQuery = '';
                  _filteredMedia.clear();
                }
              });
            },
            icon: FaIcon(
              _isSearching
                  ? FontAwesomeIcons.xmark
                  : FontAwesomeIcons.magnifyingGlass,
              color: AppTheme.accentColor,
            ),
          ),
          IconButton(
            onPressed: () {
              _showMediaOptions();
            },
            icon: FaIcon(
              FontAwesomeIcons.ellipsis,
              color: AppTheme.accentColor,
            ),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: AppTheme.accentColor,
          labelColor: AppTheme.accentColor,
          unselectedLabelColor: AppTheme.secondaryAccentColor,
          tabs: const [
            Tab(text: 'All'),
            Tab(text: 'Photos'),
            Tab(text: 'Videos'),
            Tab(text: 'Files'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildMediaGrid('all'),
          _buildMediaGrid('photos'),
          _buildMediaGrid('videos'),
          _buildMediaGrid('files'),
        ],
      ),
    );
  }

  Widget _buildMediaGrid(String type) {
    return Consumer(
      builder: (context, ref, child) {
        final messagesAsync = ref.watch(
          messagesNotifierProvider(widget.chat.id),
        );

        return messagesAsync.when(
          data: (messages) {
            final mediaMessages = _getMediaMessages(messages, type);
            final displayMessages = _isSearching
                ? _filteredMedia
                : mediaMessages;

            if (displayMessages.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    FaIcon(
                      _getEmptyIcon(type),
                      size: 64,
                      color: AppTheme.secondaryAccentColor,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      _getEmptyText(type),
                      style: AppTheme.fontStyles.subtitle.copyWith(
                        color: AppTheme.secondaryAccentColor,
                      ),
                    ),
                  ],
                ),
              );
            }

            return GridView.builder(
              padding: const EdgeInsets.all(16),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
              ),
              itemCount: displayMessages.length,
              itemBuilder: (context, index) {
                final message = displayMessages[index];
                return _buildMediaItem(message);
              },
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Center(child: Text('Error: $error')),
        );
      },
    );
  }

  List<MessageModel> _getMediaMessages(
    List<MessageModel> messages,
    String type,
  ) {
    return messages.where((message) {
      switch (type) {
        case 'photos':
          return message.type == MessageType.image;
        case 'videos':
          return message.type == MessageType.video;
        case 'files':
          return message.type == MessageType.file;
        case 'all':
        default:
          return message.type == MessageType.image ||
              message.type == MessageType.video ||
              message.type == MessageType.file;
      }
    }).toList();
  }

  void _filterMedia() {
    final messagesAsync = ref.read(messagesNotifierProvider(widget.chat.id));
    messagesAsync.whenData((messages) {
      final allMedia = _getMediaMessages(messages, 'all');
      setState(() {
        _filteredMedia = allMedia.where((message) {
          final content = message.content.toLowerCase();
          final query = _searchQuery.toLowerCase();
          return content.contains(query) ||
              message.senderId.toLowerCase().contains(query);
        }).toList();
      });
    });
  }

  Widget _buildMediaItem(MessageModel message) {
    return GestureDetector(
      onTap: () => _showMediaDetail(message),
      onLongPress: () => _showMediaOptions(message),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: AppTheme.accentColor.withValues(alpha: 0.3),
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Stack(
            children: [
              _buildMediaContent(message),
              if (message.type == MessageType.video)
                Positioned.fill(
                  child: Container(
                    color: Colors.black.withValues(alpha: 0.3),
                    child: const Center(
                      child: Icon(
                        Icons.play_arrow,
                        color: Colors.white,
                        size: 32,
                      ),
                    ),
                  ),
                ),
              Positioned(
                bottom: 4,
                right: 4,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    DateFormat.MMMd().format(message.timestamp),
                    style: AppTheme.fontStyles.caption.copyWith(
                      color: Colors.white,
                      fontSize: 10,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMediaContent(MessageModel message) {
    switch (message.type) {
      case MessageType.image:
        return CachedNetworkImage(
          imageUrl: message.content,
          fit: BoxFit.cover,
          placeholder: (context, url) => Container(
            color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
            child: const Center(child: CircularProgressIndicator()),
          ),
          errorWidget: (context, url, error) => Container(
            color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
            child: const Icon(Icons.error),
          ),
        );
      case MessageType.video:
        return CachedNetworkImage(
          imageUrl: message.content,
          fit: BoxFit.cover,
          placeholder: (context, url) => Container(
            color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
            child: const Center(child: CircularProgressIndicator()),
          ),
          errorWidget: (context, url, error) => Container(
            color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
            child: const Icon(Icons.video_file),
          ),
        );
      case MessageType.file:
        return Container(
          color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
          child: const Center(child: Icon(Icons.insert_drive_file, size: 32)),
        );
      default:
        return Container(
          color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
          child: const Center(child: Icon(Icons.error)),
        );
    }
  }

  IconData _getEmptyIcon(String type) {
    switch (type) {
      case 'photos':
        return FontAwesomeIcons.image;
      case 'videos':
        return FontAwesomeIcons.video;
      case 'files':
        return FontAwesomeIcons.file;
      default:
        return FontAwesomeIcons.images;
    }
  }

  String _getEmptyText(String type) {
    switch (type) {
      case 'photos':
        return 'No photos shared yet';
      case 'videos':
        return 'No videos shared yet';
      case 'files':
        return 'No files shared yet';
      default:
        return 'No media shared yet';
    }
  }

  void _showMediaDetail(MessageModel message) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Stack(
          children: [
            // Media content
            Center(child: _buildMediaContent(message)),
            // Close button
            Positioned(
              top: 40,
              right: 20,
              child: IconButton(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(Icons.close, color: Colors.white, size: 32),
                style: IconButton.styleFrom(
                  backgroundColor: Colors.black.withValues(alpha: 0.5),
                ),
              ),
            ),
            // Action buttons
            Positioned(
              bottom: 40,
              right: 20,
              child: Column(
                children: [
                  IconButton(
                    onPressed: () => _shareMedia(message),
                    icon: const Icon(Icons.share, color: Colors.white),
                    style: IconButton.styleFrom(
                      backgroundColor: Colors.black.withValues(alpha: 0.5),
                    ),
                  ),
                  const SizedBox(height: 8),
                  IconButton(
                    onPressed: () => _downloadMedia(message),
                    icon: const Icon(Icons.download, color: Colors.white),
                    style: IconButton.styleFrom(
                      backgroundColor: Colors.black.withValues(alpha: 0.5),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showMediaOptions([MessageModel? message]) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: AppTheme.primaryColor,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppTheme.luxuryGrey,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'Media Options',
              style: AppTheme.fontStyles.title.copyWith(
                color: AppTheme.accentColor,
              ),
            ),
            const SizedBox(height: 20),
            if (message != null) ...[
              _buildOptionTile(
                icon: FontAwesomeIcons.share,
                title: 'Share',
                subtitle: 'Share this media',
                onTap: () {
                  Navigator.pop(context);
                  _shareMedia(message);
                },
              ),
              _buildOptionTile(
                icon: FontAwesomeIcons.download,
                title: 'Download',
                subtitle: 'Save to device',
                onTap: () {
                  Navigator.pop(context);
                  _downloadMedia(message);
                },
              ),
              _buildOptionTile(
                icon: FontAwesomeIcons.trash,
                title: 'Delete',
                subtitle: 'Remove from chat',
                isDestructive: true,
                onTap: () {
                  Navigator.pop(context);
                  _deleteMedia(message);
                },
              ),
            ] else ...[
              _buildOptionTile(
                icon: FontAwesomeIcons.download,
                title: 'Download All',
                subtitle: 'Save all media to device',
                onTap: () {
                  Navigator.pop(context);
                  _downloadAllMedia();
                },
              ),
              _buildOptionTile(
                icon: FontAwesomeIcons.share,
                title: 'Share All',
                subtitle: 'Share all media',
                onTap: () {
                  Navigator.pop(context);
                  _shareAllMedia();
                },
              ),
            ],
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildOptionTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: FaIcon(
        icon,
        color: isDestructive ? Colors.red : AppTheme.accentColor,
      ),
      title: Text(
        title,
        style: AppTheme.fontStyles.bodyBold.copyWith(
          color: isDestructive ? Colors.red : AppTheme.accentColor,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: AppTheme.fontStyles.caption.copyWith(
          color: AppTheme.secondaryAccentColor,
        ),
      ),
      onTap: onTap,
    );
  }

  void _shareMedia(MessageModel message) {
    try {
      SharePlus.instance.share(
        ShareParams(
          text: message.content,
          subject: 'Shared from ${widget.chat.name}',
        ),
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error sharing media: ${e.toString()}')),
        );
      }
    }
  }

  void _downloadMedia(MessageModel message) {
    try {
      // TODO: Implement actual download functionality
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('Download started...')));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error downloading media: ${e.toString()}')),
        );
      }
    }
  }

  void _deleteMedia(MessageModel message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.primaryColor,
        title: Text(
          'Delete Media',
          style: AppTheme.fontStyles.subtitle.copyWith(
            color: AppTheme.accentColor,
          ),
        ),
        content: Text(
          'Are you sure you want to delete this media?',
          style: AppTheme.fontStyles.body.copyWith(
            color: AppTheme.secondaryAccentColor,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: AppTheme.fontStyles.body.copyWith(
                color: AppTheme.secondaryAccentColor,
              ),
            ),
          ),
          TextButton(
            onPressed: () async {
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              Navigator.pop(context);
              try {
                await ref
                    .read(messagesNotifierProvider(widget.chat.id).notifier)
                    .deleteMessage(message.id);
                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    const SnackBar(content: Text('Media deleted')),
                  );
                }
              } catch (e) {
                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text('Error deleting media: ${e.toString()}'),
                    ),
                  );
                }
              }
            },
            child: Text(
              'Delete',
              style: AppTheme.fontStyles.bodyBold.copyWith(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  void _downloadAllMedia() {
    try {
      // TODO: Implement bulk download functionality
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Downloading all media...')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error downloading media: ${e.toString()}')),
        );
      }
    }
  }

  void _shareAllMedia() {
    try {
      // TODO: Implement bulk share functionality
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('Sharing all media...')));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error sharing media: ${e.toString()}')),
        );
      }
    }
  }
}
