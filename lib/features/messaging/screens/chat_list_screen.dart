import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/features/messaging/models/chat_model.dart';
import 'package:billionaires_social/features/messaging/providers/chat_provider.dart';
import 'package:billionaires_social/features/messaging/screens/chat_screen.dart';
import 'package:billionaires_social/features/messaging/screens/chat_settings_screen.dart';
import 'package:billionaires_social/features/stories/widgets/story_aware_profile_image.dart';
import 'package:billionaires_social/core/widgets/billionaire_badge.dart';
import 'package:billionaires_social/core/mock_data.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart';

class ChatListScreen extends ConsumerStatefulWidget {
  const ChatListScreen({super.key});

  @override
  ConsumerState<ChatListScreen> createState() => _ChatListScreenState();
}

class _ChatListScreenState extends ConsumerState<ChatListScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.primaryColor,
      appBar: AppBar(
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
        title: Text(
          'Messages',
          style: AppTheme.fontStyles.title.copyWith(
            color: AppTheme.accentColor,
          ),
        ),
        actions: [
          IconButton(
            onPressed: () {
              // TODO: Add any additional actions if needed
            },
            icon: FaIcon(
              FontAwesomeIcons.ellipsis,
              color: AppTheme.accentColor,
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              onChanged: (query) {
                setState(() {
                  _searchQuery = query;
                });
              },
              decoration: InputDecoration(
                hintText: 'Search or start a new chat',
                prefixIcon: const Icon(Icons.search),
                filled: true,
                fillColor: Theme.of(context).inputDecorationTheme.fillColor,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(30.0),
                  borderSide: BorderSide.none,
                ),
              ),
            ),
          ),
          _buildFilterChips(context),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildChatList(ChatFilter.all),
                _buildChatList(ChatFilter.unread),
                _buildChatList(ChatFilter.groups),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChips(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        children: [
          ChoiceChip(
            label: const Text('All'),
            selected: true,
            onSelected: (bool selected) {},
            backgroundColor: Theme.of(context).chipTheme.backgroundColor,
            selectedColor: Theme.of(context).chipTheme.selectedColor,
            labelStyle: Theme.of(context).chipTheme.labelStyle,
          ),
          const SizedBox(width: 8),
          ChoiceChip(
            label: const Text('Unread'),
            selected: false,
            onSelected: (bool selected) {},
            backgroundColor: Theme.of(context).chipTheme.backgroundColor,
            labelStyle: Theme.of(context).chipTheme.labelStyle,
          ),
        ],
      ),
    );
  }

  Widget _buildChatList(ChatFilter filter) {
    final chatsAsync = ref.watch(chatListNotifierProvider);

    return chatsAsync.when(
      data: (chats) {
        final filteredChats = chats.where((chat) {
          final matchesSearch = chat.name.toLowerCase().contains(
            _searchQuery.toLowerCase(),
          );
          if (!matchesSearch) return false;

          switch (filter) {
            case ChatFilter.unread:
              return chat.unreadCount > 0;
            case ChatFilter.groups:
              return chat.isGroup;
            case ChatFilter.all:
              return true;
          }
        }).toList();

        if (filteredChats.isEmpty) {
          return _buildEmptyState(filter);
        }

        return ListView.builder(
          itemCount: filteredChats.length,
          itemBuilder: (context, index) {
            final chat = filteredChats[index];
            return _buildChatTile(chat);
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stackTrace) => _buildErrorState(error, stackTrace),
    );
  }

  Widget _buildEmptyState(ChatFilter filter) {
    String message;
    IconData icon;

    switch (filter) {
      case ChatFilter.unread:
        message = 'No unread messages';
        icon = Icons.mark_email_read;
        break;
      case ChatFilter.groups:
        message = 'No group chats';
        icon = Icons.group;
        break;
      case ChatFilter.all:
        message = _searchQuery.isNotEmpty
            ? 'No chats found for "$_searchQuery"'
            : 'No conversations yet';
        icon = Icons.chat_bubble_outline;
        break;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            message,
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(color: Colors.grey[600]),
          ),
          if (filter == ChatFilter.all && _searchQuery.isEmpty) ...[
            const SizedBox(height: 8),
            Text(
              'Start a conversation by searching for users',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildErrorState(Object error, StackTrace stackTrace) {
    // Check if it's a Firestore index error
    final errorMessage = error.toString();
    final isIndexError =
        errorMessage.contains('index') ||
        errorMessage.contains('composite') ||
        errorMessage.contains('failed-precondition');

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              isIndexError ? Icons.build : Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              isIndexError ? 'Database Setup Required' : 'Something went wrong',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Colors.red[700],
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              isIndexError
                  ? 'The app needs to set up database indexes. This is a one-time setup that will be completed automatically.'
                  : 'Unable to load your conversations. Please try again.',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                ref.invalidate(chatListNotifierProvider);
              },
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
            if (isIndexError) ...[
              const SizedBox(height: 12),
              TextButton(
                onPressed: () {
                  // Show detailed error information
                  _showIndexErrorDialog(errorMessage);
                },
                child: const Text('Show Details'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _showIndexErrorDialog(String errorMessage) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Database Index Information'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'This error occurs when the app needs database indexes to be created. This is normal for new installations.',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              const Text('What to do:'),
              const SizedBox(height: 8),
              const Text('1. The indexes will be created automatically'),
              const Text('2. This may take a few minutes'),
              const Text('3. Try refreshing the app periodically'),
              const SizedBox(height: 16),
              const Text(
                'Technical Details:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  errorMessage,
                  style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildChatTile(ChatModel chat) {
    return ListTile(
      leading: StoryAwareProfileImage(
        profileImageUrl: chat.avatarUrl,
        userId: chat.isGroup ? '' : chat.participants.first,
        size: 50,
      ),
      title: Row(
        children: [
          Expanded(
            child: Text(
              chat.name,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: chat.unreadCount > 0
                    ? FontWeight.bold
                    : FontWeight.normal,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (chat.participants.any(
            (id) =>
                mockUsers.any((user) => user.id == id && user.isBillionaire),
          ))
            const BillionaireBadge(size: 16),
        ],
      ),
      subtitle: Text(
        chat.lastMessage,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: chat.unreadCount > 0
              ? Theme.of(context).textTheme.bodyMedium?.color
              : Colors.grey[600],
          fontWeight: chat.unreadCount > 0
              ? FontWeight.w500
              : FontWeight.normal,
        ),
        overflow: TextOverflow.ellipsis,
      ),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            _formatTime(chat.lastMessageTime),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: chat.unreadCount > 0
                  ? Theme.of(context).primaryColor
                  : Colors.grey[500],
              fontWeight: chat.unreadCount > 0
                  ? FontWeight.bold
                  : FontWeight.normal,
            ),
          ),
          if (chat.unreadCount > 0) ...[
            const SizedBox(height: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                chat.unreadCount > 99 ? '99+' : chat.unreadCount.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ],
      ),
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => ChatScreen(chat: chat)),
        );
      },
      onLongPress: () => _showChatOptions(chat),
    );
  }

  void _showChatOptions(ChatModel chat) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.visibility_off),
              title: const Text('Hide Chat'),
              onTap: () {
                Navigator.pop(context);
                ref.read(chatListNotifierProvider.notifier).hideChat(chat.id);
              },
            ),
            ListTile(
              leading: const Icon(Icons.volume_off),
              title: const Text('Mute Chat'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Implement mute functionality
              },
            ),
            ListTile(
              leading: const Icon(Icons.settings),
              title: const Text('Chat Settings'),
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => ChatSettingsScreen(
                      chatId: chat.id,
                      chatName: chat.name,
                    ),
                  ),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text(
                'Delete Chat',
                style: TextStyle(color: Colors.red),
              ),
              onTap: () {
                Navigator.pop(context);
                _confirmDeleteChat(chat);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _confirmDeleteChat(ChatModel chat) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Chat'),
        content: Text(
          'Are you sure you want to delete your conversation with ${chat.name}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ref.read(chatListNotifierProvider.notifier).deleteChat(chat.id);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inDays > 0) {
      return DateFormat('MMM d').format(time);
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m';
    } else {
      return 'now';
    }
  }
}

enum ChatFilter { all, unread, groups }
