import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/features/messaging/models/chat_model.dart';
import 'package:billionaires_social/features/messaging/providers/chat_provider.dart';
import 'package:billionaires_social/features/messaging/screens/chat_settings_screen.dart';
import 'package:billionaires_social/features/messaging/widgets/shared_post_message.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:billionaires_social/core/utils/dialog_utils.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'dart:io';
import 'package:giphy_picker/giphy_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:billionaires_social/features/messaging/screens/video_call_screen.dart';
import 'package:billionaires_social/features/messaging/screens/chat_media_screen.dart';
import 'package:billionaires_social/features/messaging/screens/voice_call_screen.dart';
import 'package:billionaires_social/features/messaging/screens/call_history_screen.dart';

class ChatScreen extends ConsumerStatefulWidget {
  final ChatModel chat;

  const ChatScreen({super.key, required this.chat});

  @override
  ConsumerState<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends ConsumerState<ChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final bool _isTyping = false;
  String? _editingMessageId;
  String? _replyingToMessageId;
  FlutterSoundRecorder? _audioRecorder;
  String? _recordedFilePath;
  bool _isRecording = false;

  @override
  void initState() {
    super.initState();
    _audioRecorder = FlutterSoundRecorder();
    _initRecorder();
  }

  Future<void> _initRecorder() async {
    await _audioRecorder?.openRecorder();
  }

  @override
  void dispose() {
    _audioRecorder?.closeRecorder();
    _audioRecorder = null;
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final messagesAsync = ref.watch(messagesNotifierProvider(widget.chat.id));
    final privacySettingsAsync = ref.watch(
      chatPrivacyNotifierProvider(widget.chat.id),
    );

    return Scaffold(
      backgroundColor: AppTheme.primaryColor,
      appBar: AppBar(
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(Icons.arrow_back, color: AppTheme.accentColor),
        ),
        title: Row(
          children: [
            CircleAvatar(
              radius: 18,
              backgroundImage: CachedNetworkImageProvider(
                widget.chat.avatarUrl,
              ),
              backgroundColor: AppTheme.luxuryGrey,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.chat.name,
                    style: AppTheme.fontStyles.subtitle.copyWith(
                      color: Theme.of(context).appBarTheme.foregroundColor,
                    ),
                  ),
                  if (widget.chat.isGroup)
                    Text(
                      '${widget.chat.participants.length} members',
                      style: AppTheme.fontStyles.caption.copyWith(
                        color: AppTheme.secondaryAccentColor,
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            onPressed: () => _startVideoCall(),
            icon: FaIcon(
              FontAwesomeIcons.video,
              color: AppTheme.accentColor,
              size: 20,
            ),
            tooltip: 'Video Call',
          ),
          IconButton(
            onPressed: () => _startVoiceCall(),
            icon: FaIcon(
              FontAwesomeIcons.phone,
              color: AppTheme.accentColor,
              size: 20,
            ),
            tooltip: 'Voice Call',
          ),
          PopupMenuButton<String>(
            icon: FaIcon(
              FontAwesomeIcons.ellipsisVertical,
              color: Theme.of(context).appBarTheme.foregroundColor,
              size: 20,
            ),
            onSelected: (value) {
              switch (value) {
                case 'media':
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => ChatMediaScreen(chat: widget.chat),
                    ),
                  );
                  break;
                case 'settings':
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => ChatSettingsScreen(
                        chatId: widget.chat.id,
                        chatName: widget.chat.name,
                      ),
                    ),
                  );
                  break;
                case 'privacy':
                  _showPrivacySettings(privacySettingsAsync.value);
                  break;
                case 'info':
                  _showChatInfo();
                  break;
                case 'callHistory':
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => CallHistoryScreen(
                        chatId: widget.chat.id,
                        chatName: widget.chat.name,
                      ),
                    ),
                  );
                  break;
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'media',
                child: Row(
                  children: [
                    FaIcon(
                      FontAwesomeIcons.images,
                      color: AppTheme.accentColor,
                    ),
                    const SizedBox(width: 12),
                    Text('Media', style: AppTheme.fontStyles.body),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    FaIcon(FontAwesomeIcons.gear, color: AppTheme.accentColor),
                    const SizedBox(width: 12),
                    Text('Settings', style: AppTheme.fontStyles.body),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'privacy',
                child: Row(
                  children: [
                    FaIcon(
                      FontAwesomeIcons.shieldHalved,
                      color: AppTheme.accentColor,
                    ),
                    const SizedBox(width: 12),
                    Text('Privacy', style: AppTheme.fontStyles.body),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'info',
                child: Row(
                  children: [
                    FaIcon(FontAwesomeIcons.info, color: AppTheme.accentColor),
                    const SizedBox(width: 12),
                    Text('Chat Info', style: AppTheme.fontStyles.body),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'callHistory',
                child: Row(
                  children: [
                    FaIcon(
                      FontAwesomeIcons.clockRotateLeft,
                      color: AppTheme.accentColor,
                    ),
                    const SizedBox(width: 12),
                    Text('Call History', style: AppTheme.fontStyles.body),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Reply preview
          if (_replyingToMessageId != null) _buildReplyPreview(),

          // Messages list
          Expanded(
            child: messagesAsync.when(
              data: (messages) {
                if (messages.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        FaIcon(
                          FontAwesomeIcons.comments,
                          size: 64,
                          color: AppTheme.secondaryAccentColor.withValues(
                            alpha: 0.5,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'No messages yet',
                          style: AppTheme.fontStyles.subtitle.copyWith(
                            color: Theme.of(
                              context,
                            ).appBarTheme.foregroundColor,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Start the conversation!',
                          style: AppTheme.fontStyles.body.copyWith(
                            color: AppTheme.secondaryAccentColor.withValues(
                              alpha: 0.7,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  controller: _scrollController,
                  reverse: true,
                  padding: const EdgeInsets.all(16),
                  itemCount: messages.length,
                  itemBuilder: (context, index) {
                    final message = messages[index];
                    final isMyMessage = message.senderId == 'user1';

                    return GestureDetector(
                      onLongPress: () {
                        _showMessageOptions(message);
                      },
                      child: Container(
                        margin: const EdgeInsets.only(bottom: 8),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Check if this is a shared post message
                            if (_hasSharedPostData(message))
                              SharedPostMessage(
                                sharedPostData: _getSharedPostData(message),
                                isMyMessage: isMyMessage,
                              )
                            else if (message.type == MessageType.audio)
                              Row(
                                children: [
                                  _buildEncryptionStatusIcon(message),
                                  const SizedBox(width: 4),
                                  Expanded(
                                    child: _buildVoiceMessagePlayer(message),
                                  ),
                                ],
                              )
                            else
                              Row(
                                children: [
                                  _buildEncryptionStatusIcon(message),
                                  const SizedBox(width: 4),
                                  Expanded(
                                    child: Container(
                                      padding: const EdgeInsets.all(12),
                                      decoration: BoxDecoration(
                                        color: isMyMessage
                                            ? Theme.of(
                                                context,
                                              ).colorScheme.primary
                                            : Theme.of(
                                                context,
                                              ).colorScheme.surface,
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            message.content,
                                            style: TextStyle(
                                              color: isMyMessage
                                                  ? Theme.of(
                                                      context,
                                                    ).colorScheme.onPrimary
                                                  : Theme.of(
                                                      context,
                                                    ).colorScheme.onSurface,
                                            ),
                                          ),
                                          const SizedBox(height: 4),
                                          Text(
                                            DateFormat.jm().format(
                                              message.timestamp,
                                            ),
                                            style: AppTheme.fontStyles.caption
                                                .copyWith(
                                                  color: isMyMessage
                                                      ? Theme.of(context)
                                                            .colorScheme
                                                            .onPrimary
                                                            .withValues(
                                                              alpha: 0.7,
                                                            )
                                                      : AppTheme
                                                            .secondaryAccentColor,
                                                  fontSize: 10,
                                                ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                          ],
                        ),
                      ),
                    );
                  },
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Center(child: Text('Error: $error')),
            ),
          ),

          // Typing indicator
          if (_isTyping) _buildTypingIndicator(),

          // Message input
          _buildMessageInput(),
        ],
      ),
    );
  }

  Widget _buildReplyPreview() {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.accentColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 40,
            decoration: BoxDecoration(
              color: AppTheme.accentColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Replying to message',
                  style: AppTheme.fontStyles.caption.copyWith(
                    color: AppTheme.accentColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Tap to cancel reply',
                  style: AppTheme.fontStyles.caption.copyWith(
                    color: AppTheme.secondaryAccentColor,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              setState(() {
                _replyingToMessageId = null;
              });
            },
            icon: const Icon(
              Icons.close,
              color: AppTheme.secondaryAccentColor,
              size: 20,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTypingIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Text(
            '${widget.chat.name} is typing',
            style: AppTheme.fontStyles.caption.copyWith(
              color: AppTheme.secondaryAccentColor,
            ),
          ),
          const SizedBox(width: 8),
          const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(AppTheme.accentColor),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        border: Border(
          top: BorderSide(
            color: AppTheme.luxuryGrey.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: () {
              _showSelfDestructOptions();
            },
            icon: FaIcon(
              FontAwesomeIcons.clock,
              color: Theme.of(context).iconTheme.color,
            ),
          ),
          IconButton(
            onPressed: _pickGif,
            icon: FaIcon(FontAwesomeIcons.image, color: AppTheme.accentColor),
            tooltip: 'Send GIF',
          ),
          IconButton(
            onPressed: _pickSticker,
            icon: FaIcon(
              FontAwesomeIcons.faceSmile,
              color: AppTheme.accentColor,
            ),
            tooltip: 'Send Sticker',
          ),
          IconButton(
            onPressed: _pickDocument,
            icon: FaIcon(FontAwesomeIcons.file, color: AppTheme.accentColor),
            tooltip: 'Send Document',
          ),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(24),
                border: Border.all(
                  color: AppTheme.accentColor.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _messageController,
                      decoration: InputDecoration(
                        hintText: _editingMessageId != null
                            ? 'Edit message...'
                            : 'Type a message...',
                        hintStyle: AppTheme.fontStyles.body.copyWith(
                          color: AppTheme.secondaryAccentColor,
                        ),
                        border: InputBorder.none,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                      ),
                      style: AppTheme.fontStyles.body.copyWith(
                        color: Theme.of(context).iconTheme.color,
                      ),
                      maxLines: null,
                      textCapitalization: TextCapitalization.sentences,
                      onChanged: (_) => setState(() {}),
                    ),
                  ),
                  if (_editingMessageId != null)
                    IconButton(
                      onPressed: () {
                        setState(() {
                          _editingMessageId = null;
                          _messageController.clear();
                        });
                      },
                      icon: const Icon(
                        Icons.close,
                        color: AppTheme.secondaryAccentColor,
                      ),
                    ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 8),
          _messageController.text.trim().isEmpty
              ? GestureDetector(
                  onLongPressStart: (_) => _startVoiceRecording(),
                  onLongPressEnd: (_) => _stopVoiceRecording(),
                  child: Container(
                    decoration: const BoxDecoration(
                      color: AppTheme.accentColor,
                      shape: BoxShape.circle,
                    ),
                    padding: const EdgeInsets.all(12),
                    child: Icon(
                      _isRecording ? Icons.mic : Icons.mic_none,
                      color: Colors.white,
                    ),
                  ),
                )
              : Container(
                  decoration: const BoxDecoration(
                    color: AppTheme.accentColor,
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    onPressed: _sendMessage,
                    icon: const Icon(Icons.send, color: Colors.white),
                  ),
                ),
        ],
      ),
    );
  }

  void _sendMessage() async {
    final content = _messageController.text.trim();
    if (content.isEmpty) return;

    try {
      if (_editingMessageId != null) {
        // Edit existing message
        await ref
            .read(messagesNotifierProvider(widget.chat.id).notifier)
            .editMessage(messageId: _editingMessageId!, newContent: content);
        setState(() {
          _editingMessageId = null;
          _messageController.clear();
        });
      } else {
        // Send new message
        await ref
            .read(messagesNotifierProvider(widget.chat.id).notifier)
            .sendMessage(
              content: content,
              type: MessageType.text,
              selfDestructSettings: null, // TODO: Add self-destruct settings
            );
        _messageController.clear();
      }

      // Scroll to bottom
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            0,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to send message: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showMessageOptions(MessageModel message) {
    final isMyMessage = message.senderId == 'user1';
    final canEdit =
        isMyMessage && ref.read(chatServiceProvider).canEditMessage(message);

    showAppDialog(
      context: context,
      title: Text('Message Options'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppTheme.luxuryGrey,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 20),
          if (canEdit)
            _buildOptionTile(
              icon: FontAwesomeIcons.penToSquare,
              title: 'Edit Message',
              subtitle: 'Edit this message (within 10 minutes)',
              onTap: () {
                Navigator.pop(context);
                _editMessage(message);
              },
            ),
          _buildOptionTile(
            icon: FontAwesomeIcons.reply,
            title: 'Reply',
            subtitle: 'Reply to this message',
            onTap: () {
              Navigator.pop(context);
              _setReplyMessage(message.id);
            },
          ),
          _buildOptionTile(
            icon: FontAwesomeIcons.heart,
            title: 'React',
            subtitle: 'Add a reaction',
            onTap: () {
              Navigator.pop(context);
              _showReactionPicker(message);
            },
          ),
          if (isMyMessage)
            _buildOptionTile(
              icon: FontAwesomeIcons.trash,
              title: 'Delete',
              subtitle: 'Delete this message',
              isDestructive: true,
              onTap: () {
                Navigator.pop(context);
                _deleteMessage(message);
              },
            ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildOptionTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: FaIcon(
        icon,
        color: isDestructive ? Colors.red : Theme.of(context).iconTheme.color,
      ),
      title: Text(
        title,
        style: AppTheme.fontStyles.bodyBold.copyWith(
          color: isDestructive ? Colors.red : Theme.of(context).iconTheme.color,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: AppTheme.fontStyles.caption.copyWith(
          color: AppTheme.secondaryAccentColor,
        ),
      ),
      onTap: onTap,
    );
  }

  void _editMessage(MessageModel message) {
    setState(() {
      _editingMessageId = message.id;
      _messageController.text = message.content;
    });
    _messageController.selection = TextSelection.fromPosition(
      TextPosition(offset: _messageController.text.length),
    );
  }

  void _setReplyMessage(String messageId) {
    setState(() {
      _replyingToMessageId = messageId;
    });
  }

  void _deleteMessage(MessageModel message) {
    showAppDialog(
      context: context,
      title: Text('Delete Message'),
      content: Text(
        'Are you sure you want to delete this message?',
        style: AppTheme.fontStyles.body.copyWith(
          color: AppTheme.secondaryAccentColor,
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(
            'Cancel',
            style: AppTheme.fontStyles.body.copyWith(
              color: AppTheme.secondaryAccentColor,
            ),
          ),
        ),
        TextButton(
          onPressed: () async {
            Navigator.pop(context);
            try {
              await ref
                  .read(messagesNotifierProvider(widget.chat.id).notifier)
                  .deleteMessage(message.id);
            } catch (e) {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'Failed to delete message: \\${e.toString()}',
                    ),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            }
          },
          child: Text(
            'Delete',
            style: AppTheme.fontStyles.bodyBold.copyWith(color: Colors.red),
          ),
        ),
      ],
    );
  }

  void _showReactionPicker(MessageModel message) {
    showAppDialog(
      context: context,
      title: Text('Add Reaction'),
      content: Wrap(
        spacing: 16,
        runSpacing: 16,
        children: ['❤️', '😂', '🔥', '👍', '😢']
            .map(
              (emoji) => GestureDetector(
                onTap: () {
                  Navigator.pop(context);
                  _handleReaction(message, emoji);
                },
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(25),
                    border: Border.all(
                      color: AppTheme.accentColor.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Center(
                    child: Text(emoji, style: const TextStyle(fontSize: 24)),
                  ),
                ),
              ),
            )
            .toList(),
      ),
    );
  }

  void _handleReaction(MessageModel message, String emoji) async {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('You must be logged in to react to messages'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }
    final hasReaction = message.reactions.any(
      (r) => r.userId == currentUser.uid && r.emoji == emoji,
    );
    try {
      if (hasReaction) {
        await ref
            .read(messagesNotifierProvider(widget.chat.id).notifier)
            .removeReaction(messageId: message.id, emoji: emoji);
      } else {
        await ref
            .read(messagesNotifierProvider(widget.chat.id).notifier)
            .addReaction(messageId: message.id, emoji: emoji);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update reaction: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showSelfDestructOptions() {
    showAppDialog(
      context: context,
      title: Text('Self-Destruct Timer'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppTheme.luxuryGrey,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 20),
          _buildSelfDestructOption('No Timer', null),
          _buildSelfDestructOption('30 seconds', 30),
          _buildSelfDestructOption('1 minute', 60),
          _buildSelfDestructOption('5 minutes', 300),
          _buildSelfDestructOption('1 hour', 3600),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildSelfDestructOption(String label, int? seconds) {
    return ListTile(
      leading: FaIcon(
        seconds != null ? FontAwesomeIcons.clock : FontAwesomeIcons.infinity,
        color: AppTheme.accentColor,
      ),
      title: Text(
        label,
        style: AppTheme.fontStyles.body.copyWith(color: AppTheme.accentColor),
      ),
      onTap: () {
        Navigator.pop(context);
        // TODO: Set self-destruct timer for next message
      },
    );
  }

  void _showPrivacySettings(ChatPrivacySettings? settings) {
    if (settings == null) return;

    showAppDialog(
      context: context,
      title: Text('Privacy Settings'),
      content: SizedBox(
        height: MediaQuery.of(context).size.height * 0.7,
        child: ListView(
          children: [
            _buildPrivacySwitch(
              'Block Screenshots',
              'Prevent screenshots in this chat',
              settings.blockScreenshots,
              (value) {
                ref
                    .read(chatPrivacyNotifierProvider(widget.chat.id).notifier)
                    .updatePrivacySettings(
                      settings.copyWith(blockScreenshots: value),
                    );
              },
            ),
            _buildPrivacySwitch(
              'Screenshot Alerts',
              'Get notified when someone takes a screenshot',
              settings.screenshotAlerts,
              (value) {
                ref
                    .read(chatPrivacyNotifierProvider(widget.chat.id).notifier)
                    .updatePrivacySettings(
                      settings.copyWith(screenshotAlerts: value),
                    );
              },
            ),
            _buildPrivacySwitch(
              'Read Receipts',
              'Show when messages are read',
              settings.readReceipts,
              (value) {
                ref
                    .read(chatPrivacyNotifierProvider(widget.chat.id).notifier)
                    .updatePrivacySettings(
                      settings.copyWith(readReceipts: value),
                    );
              },
            ),
            _buildPrivacySwitch(
              'Typing Indicators',
              'Show when someone is typing',
              settings.typingIndicators,
              (value) {
                ref
                    .read(chatPrivacyNotifierProvider(widget.chat.id).notifier)
                    .updatePrivacySettings(
                      settings.copyWith(typingIndicators: value),
                    );
              },
            ),
            _buildPrivacySwitch(
              'Message Reactions',
              'Allow reactions to messages',
              settings.messageReactions,
              (value) {
                ref
                    .read(chatPrivacyNotifierProvider(widget.chat.id).notifier)
                    .updatePrivacySettings(
                      settings.copyWith(messageReactions: value),
                    );
              },
            ),
            _buildPrivacySwitch(
              'Self-Destruct Messages',
              'Enable self-destructing messages',
              settings.selfDestructEnabled,
              (value) {
                ref
                    .read(chatPrivacyNotifierProvider(widget.chat.id).notifier)
                    .updatePrivacySettings(
                      settings.copyWith(selfDestructEnabled: value),
                    );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrivacySwitch(
    String title,
    String subtitle,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.accentColor.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTheme.fontStyles.bodyBold.copyWith(
                    color: Theme.of(context).appBarTheme.foregroundColor,
                  ),
                ),
                Text(
                  subtitle,
                  style: AppTheme.fontStyles.caption.copyWith(
                    color: AppTheme.secondaryAccentColor,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppTheme.accentColor,
          ),
        ],
      ),
    );
  }

  void _showChatInfo() {
    showAppDialog(
      context: context,
      title: Text('Chat Info'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppTheme.luxuryGrey,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 20),
          CircleAvatar(
            radius: 40,
            backgroundImage: CachedNetworkImageProvider(widget.chat.avatarUrl),
            backgroundColor: AppTheme.luxuryGrey,
          ),
          const SizedBox(height: 16),
          Text(
            widget.chat.name,
            style: AppTheme.fontStyles.title.copyWith(
              color: Theme.of(context).appBarTheme.foregroundColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            widget.chat.isGroup
                ? '${widget.chat.participants.length} members'
                : 'Direct message',
            style: AppTheme.fontStyles.body.copyWith(
              color: AppTheme.secondaryAccentColor,
            ),
          ),
          const SizedBox(height: 20),
          _buildInfoTile(
            icon: FontAwesomeIcons.calendar,
            title: 'Created',
            subtitle: DateFormat.yMMMd().format(widget.chat.lastMessageTime),
          ),
          if (widget.chat.isGroup) ...[
            _buildInfoTile(
              icon: FontAwesomeIcons.users,
              title: 'Participants',
              subtitle: widget.chat.participants.join(', '),
            ),
          ],
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildInfoTile({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return ListTile(
      leading: FaIcon(
        icon,
        color: Theme.of(context).appBarTheme.foregroundColor,
      ),
      title: Text(
        title,
        style: AppTheme.fontStyles.bodyBold.copyWith(
          color: Theme.of(context).appBarTheme.foregroundColor,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: AppTheme.fontStyles.caption.copyWith(
          color: AppTheme.secondaryAccentColor,
        ),
      ),
    );
  }

  // Helper methods for shared post data
  bool _hasSharedPostData(MessageModel message) {
    // This would check if the message contains shared post data
    // For now, we'll check if the content indicates it's a shared post
    return message.content.startsWith('Shared a post from');
  }

  Map<String, dynamic> _getSharedPostData(MessageModel message) {
    // This would extract shared post data from the message
    // For now, returning mock data - in a real implementation,
    // this would come from the message's sharedPostData field
    return {
      'postId': 'mock_post_id',
      'postUsername': 'mock_user',
      'postCaption': 'This is a shared post caption',
      'postMediaUrl': 'https://example.com/image.jpg',
      'postUserAvatarUrl': 'https://example.com/avatar.jpg',
      'postLocation': 'New York, NY',
    };
  }

  Widget _buildVoiceMessagePlayer(MessageModel message) {
    // TODO: Implement actual audio playback UI using flutter_sound
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppTheme.luxuryGrey.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Icon(Icons.play_arrow, color: AppTheme.accentColor),
          const SizedBox(width: 8),
          Text('Voice message'),
        ],
      ),
    );
  }

  void _startVoiceRecording() async {
    setState(() {
      _isRecording = true;
    });
    final tempDir = Directory.systemTemp;
    final filePath =
        '${tempDir.path}/voice_${DateTime.now().millisecondsSinceEpoch}.aac';
    _recordedFilePath = filePath;
    await _audioRecorder?.startRecorder(toFile: filePath, codec: Codec.aacADTS);
  }

  void _stopVoiceRecording() async {
    setState(() {
      _isRecording = false;
    });
    await _audioRecorder?.stopRecorder();
    if (_recordedFilePath != null) {
      // TODO: Upload audio file and get URL
      final audioUrl = await _uploadAudioFile(_recordedFilePath!);
      // TODO: Get duration
      final duration = await _getAudioDuration(_recordedFilePath!);
      // Send as voice message
      await ref
          .read(chatServiceProvider)
          .sendVoiceMessage(
            chatId: widget.chat.id,
            audioUrl: audioUrl,
            durationInSeconds: duration,
            transcription: null, // TODO: Add transcription if available
          );
      _recordedFilePath = null;
    }
  }

  Future<String> _uploadAudioFile(String filePath) async {
    // TODO: Implement audio file upload and return the URL
    // For now, just return the local file path as a placeholder
    return filePath;
  }

  Future<int> _getAudioDuration(String filePath) async {
    // TODO: Implement audio duration extraction
    // For now, return a placeholder value
    return 5;
  }

  Widget _buildEncryptionStatusIcon(MessageModel message) {
    // Implement the logic to determine the encryption status icon based on the message
    // For now, returning a placeholder icon
    return FaIcon(FontAwesomeIcons.lock, color: AppTheme.accentColor, size: 16);
  }

  Future<void> _pickGif() async {
    final gif = await GiphyPicker.pickGif(
      context: context,
      apiKey: 'dc6zaTOxFJmzC', // TODO: Replace with your Giphy API key
      fullScreenDialog: true,
      showPreviewPage: true,
    );
    if (gif != null) {
      // Send GIF as a message
      await ref
          .read(chatServiceProvider)
          .sendMessage(
            chatId: widget.chat.id,
            content:
                gif.images.original?.url ?? gif.images.fixedHeight?.url ?? '',
            type: MessageType.image,
          );
    }
  }

  void _pickSticker() async {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) {
        return Container(
          height: 200,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: GridView.count(
            crossAxisCount: 4,
            padding: const EdgeInsets.all(16),
            children: [
              GestureDetector(
                onTap: () async {
                  Navigator.pop(context);
                  await ref
                      .read(chatServiceProvider)
                      .sendMessage(
                        chatId: widget.chat.id,
                        content:
                            'assets/icons8-comment.svg', // Use selected sticker asset path
                        type: MessageType.image,
                      );
                },
                child: Image.asset(
                  'assets/icons8-comment.svg',
                  height: 64,
                  width: 64,
                ),
              ),
              // Add more stickers here as needed
            ],
          ),
        );
      },
    );
  }

  Future<void> _pickDocument() async {
    final result = await FilePicker.platform.pickFiles(
      type: FileType.any,
      allowMultiple: false,
    );

    if (result != null && result.files.single.path != null) {
      final file = result.files.single;
      final filePath = file.path!;
      final fileName = file.name;
      final fileSize = file.size;
      final fileExtension = file.extension;

      // Show file preview
      final shouldSend = await _showFilePreview(
        fileName,
        fileSize,
        fileExtension,
      );
      if (!shouldSend) return;

      try {
        // Upload file to Firebase Storage
        final fileUrl = await _uploadFileToStorage(filePath, fileName);

        // Send file message with metadata
        await ref
            .read(chatServiceProvider)
            .sendMessage(
              chatId: widget.chat.id,
              content: fileUrl,
              type: MessageType.file,
            );
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to send file: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<String> _uploadFileToStorage(String filePath, String fileName) async {
    final file = File(filePath);
    final ref = FirebaseStorage.instance
        .ref()
        .child('chat_files')
        .child('${DateTime.now().millisecondsSinceEpoch}_$fileName');

    final uploadTask = ref.putFile(file);
    final snapshot = await uploadTask;
    return await snapshot.ref.getDownloadURL();
  }

  Future<bool> _showFilePreview(
    String fileName,
    int fileSize,
    String? fileExtension,
  ) async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            backgroundColor: AppTheme.primaryColor,
            title: Text(
              'Send File',
              style: AppTheme.fontStyles.subtitle.copyWith(
                color: AppTheme.accentColor,
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      _getFileIcon(fileExtension),
                      color: AppTheme.accentColor,
                      size: 32,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            fileName,
                            style: AppTheme.fontStyles.bodyBold.copyWith(
                              color: AppTheme.accentColor,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text(
                            _formatFileSize(fileSize),
                            style: AppTheme.fontStyles.caption.copyWith(
                              color: AppTheme.secondaryAccentColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  'Send this file to the chat?',
                  style: AppTheme.fontStyles.body.copyWith(
                    color: AppTheme.secondaryAccentColor,
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: Text(
                  'Cancel',
                  style: AppTheme.fontStyles.body.copyWith(
                    color: AppTheme.secondaryAccentColor,
                  ),
                ),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context, true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.accentColor,
                ),
                child: Text(
                  'Send',
                  style: AppTheme.fontStyles.bodyBold.copyWith(
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ) ??
        false;
  }

  IconData _getFileIcon(String? extension) {
    switch (extension?.toLowerCase()) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'ppt':
      case 'pptx':
        return Icons.slideshow;
      case 'txt':
        return Icons.text_snippet;
      case 'zip':
      case 'rar':
        return Icons.archive;
      case 'mp3':
      case 'wav':
        return Icons.audiotrack;
      case 'mp4':
      case 'avi':
        return Icons.video_file;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return Icons.image;
      default:
        return Icons.insert_drive_file;
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  void _startVideoCall() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VideoCallScreen(
          participantId: widget.chat.participants.firstWhere(
            (id) => id != FirebaseAuth.instance.currentUser?.uid,
            orElse: () => '',
          ),
          participantName: widget.chat.name,
          isGroupCall: widget.chat.isGroup,
        ),
      ),
    );
  }

  void _startVoiceCall() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VoiceCallScreen(
          participantId: widget.chat.participants.firstWhere(
            (id) => id != FirebaseAuth.instance.currentUser?.uid,
            orElse: () => '',
          ),
          participantName: widget.chat.name,
          isGroupCall: widget.chat.isGroup,
        ),
      ),
    );
  }
}
