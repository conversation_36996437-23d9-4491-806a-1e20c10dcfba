import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:permission_handler/permission_handler.dart';

class VoiceCallScreen extends StatefulWidget {
  final String? callId;
  final String? participantId;
  final String? participantName;
  final bool isIncoming;
  final bool isGroupCall;

  const VoiceCallScreen({
    super.key,
    this.callId,
    this.participantId,
    this.participantName,
    this.isIncoming = false,
    this.isGroupCall = false,
  });

  @override
  State<VoiceCallScreen> createState() => _VoiceCallScreenState();
}

class _VoiceCallScreenState extends State<VoiceCallScreen>
    with TickerProviderStateMixin {
  bool _isMuted = false;
  bool _isSpeakerOn = false;
  bool _isCallActive = false;
  bool _isMinimized = false;
  Duration _callDuration = Duration.zero;
  late AnimationController _pulseAnimationController;
  late AnimationController _slideAnimationController;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _requestPermissions();
    if (widget.isIncoming) {
      _startIncomingCallAnimation();
    } else {
      _startCall();
    }
  }

  void _setupAnimations() {
    _pulseAnimationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);

    _slideAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  Future<void> _requestPermissions() async {
    await Permission.microphone.request();
  }

  void _startIncomingCallAnimation() {
    _pulseAnimationController.repeat(reverse: true);
  }

  void _startCall() {
    setState(() {
      _isCallActive = true;
    });
    _startCallTimer();
  }

  void _startCallTimer() {
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted && _isCallActive) {
        setState(() {
          _callDuration += const Duration(seconds: 1);
        });
        _startCallTimer();
      }
    });
  }

  void _toggleMute() {
    setState(() {
      _isMuted = !_isMuted;
    });
    // TODO: Implement actual mute functionality
  }

  void _toggleSpeaker() {
    setState(() {
      _isSpeakerOn = !_isSpeakerOn;
    });
    // TODO: Implement actual speaker toggle functionality
  }

  void _endCall() {
    setState(() {
      _isCallActive = false;
    });
    Navigator.of(context).pop();
  }

  void _acceptCall() {
    _pulseAnimationController.stop();
    _startCall();
  }

  void _rejectCall() {
    Navigator.of(context).pop();
  }

  void _toggleMinimize() {
    setState(() {
      _isMinimized = !_isMinimized;
    });
    if (_isMinimized) {
      _slideAnimationController.forward();
    } else {
      _slideAnimationController.reverse();
    }
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    String hours = twoDigits(duration.inHours);
    String minutes = twoDigits(duration.inMinutes.remainder(60));
    String seconds = twoDigits(duration.inSeconds.remainder(60));
    return duration.inHours > 0
        ? '$hours:$minutes:$seconds'
        : '$minutes:$seconds';
  }

  @override
  void dispose() {
    _pulseAnimationController.dispose();
    _slideAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarColor: Colors.black,
        systemNavigationBarIconBrightness: Brightness.light,
      ),
      child: Scaffold(
        backgroundColor: Colors.black,
        body: Stack(
          children: [
            // Background gradient
            Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [Color(0xFF1a1a1a), Color(0xFF000000)],
                ),
              ),
            ),

            // Main content
            SafeArea(
              child: Column(
                children: [
                  // Top section with participant info
                  _buildTopSection(),

                  // Call area
                  Expanded(child: _buildCallArea()),

                  // Bottom controls
                  _buildBottomControls(),
                ],
              ),
            ),

            // Incoming call overlay
            if (widget.isIncoming && !_isCallActive)
              _buildIncomingCallOverlay(),
          ],
        ),
      ),
    );
  }

  Widget _buildTopSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          // Back button
          IconButton(
            onPressed: _endCall,
            icon: const Icon(Icons.arrow_back, color: Colors.white, size: 24),
          ),

          const SizedBox(width: 16),

          // Participant info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.participantName ?? 'Unknown',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (_isCallActive)
                  Text(
                    _formatDuration(_callDuration),
                    style: TextStyle(color: Colors.grey[400], fontSize: 14),
                  )
                else
                  Text(
                    widget.isIncoming ? 'Incoming call...' : 'Calling...',
                    style: TextStyle(color: Colors.grey[400], fontSize: 14),
                  ),
              ],
            ),
          ),

          // Minimize button
          IconButton(
            onPressed: _toggleMinimize,
            icon: Icon(
              _isMinimized ? Icons.fullscreen : Icons.fullscreen_exit,
              color: Colors.white,
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCallArea() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Stack(
        children: [
          // Main call area
          Container(
            width: double.infinity,
            height: double.infinity,
            decoration: BoxDecoration(
              color: Colors.grey[900],
              borderRadius: BorderRadius.circular(20),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: Container(
                color: Colors.grey[800],
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Participant avatar
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: AppTheme.accentColor.withValues(alpha: 0.2),
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: AppTheme.accentColor,
                          width: 3,
                        ),
                      ),
                      child: Icon(
                        Icons.person,
                        size: 60,
                        color: AppTheme.accentColor,
                      ),
                    ),
                    const SizedBox(height: 24),
                    Text(
                      widget.participantName ?? 'Unknown',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      widget.isGroupCall ? 'Group Voice Call' : 'Voice Call',
                      style: TextStyle(color: Colors.grey[400], fontSize: 16),
                    ),
                    if (_isCallActive) ...[
                      const SizedBox(height: 16),
                      Text(
                        _formatDuration(_callDuration),
                        style: TextStyle(color: Colors.grey[400], fontSize: 18),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),

          // Call status overlay
          if (!_isCallActive && !widget.isIncoming)
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.call, size: 60, color: Colors.white54),
                      SizedBox(height: 16),
                      Text(
                        'Connecting...',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildBottomControls() {
    return Container(
      padding: const EdgeInsets.all(30),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Mute button
          _buildControlButton(
            icon: _isMuted ? Icons.mic_off : Icons.mic,
            isActive: _isMuted,
            onPressed: _toggleMute,
          ),

          // Speaker button
          _buildControlButton(
            icon: _isSpeakerOn ? Icons.volume_up : Icons.volume_down,
            isActive: _isSpeakerOn,
            onPressed: _toggleSpeaker,
          ),

          // End call button
          _buildEndCallButton(),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required bool isActive,
    required VoidCallback onPressed,
  }) {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        color: isActive ? Colors.red : Colors.white24,
        shape: BoxShape.circle,
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(
          icon,
          color: isActive ? Colors.white : Colors.white,
          size: 24,
        ),
      ),
    );
  }

  Widget _buildEndCallButton() {
    return Container(
      width: 70,
      height: 70,
      decoration: const BoxDecoration(
        color: Colors.red,
        shape: BoxShape.circle,
      ),
      child: IconButton(
        onPressed: _endCall,
        icon: const Icon(Icons.call_end, color: Colors.white, size: 30),
      ),
    );
  }

  Widget _buildIncomingCallOverlay() {
    return Container(
      color: Colors.black87,
      child: SafeArea(
        child: Column(
          children: [
            const Spacer(),

            // Participant avatar
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: AppTheme.accentColor.withValues(alpha: 0.2),
                shape: BoxShape.circle,
                border: Border.all(color: AppTheme.accentColor, width: 3),
              ),
              child: Icon(Icons.person, size: 60, color: AppTheme.accentColor),
            ),

            const SizedBox(height: 24),

            // Participant name
            Text(
              widget.participantName ?? 'Unknown',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 28,
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: 8),

            // Call type
            Text(
              widget.isGroupCall ? 'Group Voice Call' : 'Voice Call',
              style: TextStyle(color: Colors.grey[400], fontSize: 16),
            ),

            const SizedBox(height: 40),

            // Animated call icon
            AnimatedBuilder(
              animation: _pulseAnimationController,
              builder: (context, child) {
                return Transform.scale(
                  scale: 1.0 + (_pulseAnimationController.value * 0.2),
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: Colors.green,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.call,
                      color: Colors.white,
                      size: 40,
                    ),
                  ),
                );
              },
            ),

            const SizedBox(height: 60),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // Reject button
                Container(
                  width: 70,
                  height: 70,
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    onPressed: _rejectCall,
                    icon: const Icon(
                      Icons.call_end,
                      color: Colors.white,
                      size: 30,
                    ),
                  ),
                ),

                // Accept button
                Container(
                  width: 70,
                  height: 70,
                  decoration: const BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    onPressed: _acceptCall,
                    icon: const Icon(Icons.call, color: Colors.white, size: 30),
                  ),
                ),
              ],
            ),

            const Spacer(),
          ],
        ),
      ),
    );
  }
}
