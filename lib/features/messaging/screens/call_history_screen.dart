import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/features/messaging/providers/call_provider.dart';
import 'package:billionaires_social/features/messaging/models/call_model.dart';

class CallHistoryScreen extends ConsumerStatefulWidget {
  final String chatId;
  final String chatName;

  const CallHistoryScreen({
    super.key,
    required this.chatId,
    required this.chatName,
  });

  @override
  ConsumerState<CallHistoryScreen> createState() => _CallHistoryScreenState();
}

class _CallHistoryScreenState extends ConsumerState<CallHistoryScreen> {
  CallType? _selectedFilter;

  @override
  Widget build(BuildContext context) {
    final callHistoryAsync = ref.watch(callHistoryProvider(widget.chatId));

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.arrow_back, color: Colors.white),
        ),
        title: Text(
          'Call History',
          style: AppTheme.fontStyles.title.copyWith(
            color: Theme.of(context).appBarTheme.foregroundColor,
          ),
        ),
        actions: [
          IconButton(
            onPressed: _showFilterOptions,
            icon: FaIcon(
              FontAwesomeIcons.filter,
              color: AppTheme.accentColor,
              size: 20,
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Filter chips
          if (_selectedFilter != null)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  Chip(
                    label: Text(
                      _selectedFilter == CallType.voice ? 'Voice' : 'Video',
                      style: AppTheme.fontStyles.caption.copyWith(
                        color: Colors.white,
                      ),
                    ),
                    backgroundColor: AppTheme.accentColor,
                    deleteIcon: const Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 16,
                    ),
                    onDeleted: () => setState(() => _selectedFilter = null),
                  ),
                ],
              ),
            ),

          // Call history list
          Expanded(
            child: callHistoryAsync.when(
              data: (calls) {
                final validCalls = calls;
                final filteredCalls = _selectedFilter != null
                    ? validCalls
                          .where((call) => call.type == _selectedFilter!)
                          .toList()
                    : validCalls;

                if (filteredCalls.isEmpty) {
                  return _buildEmptyState();
                }

                return ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: filteredCalls.length,
                  itemBuilder: (context, index) {
                    final call = filteredCalls[index];
                    return _buildCallItem(call);
                  },
                );
              },
              loading: () => const Center(
                child: CircularProgressIndicator(color: AppTheme.accentColor),
              ),
              error: (error, stack) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    FaIcon(
                      FontAwesomeIcons.triangleExclamation,
                      color: Colors.red,
                      size: 48,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Error loading call history',
                      style: AppTheme.fontStyles.body.copyWith(
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      error.toString(),
                      style: AppTheme.fontStyles.caption.copyWith(
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          FaIcon(
            FontAwesomeIcons.phone,
            color: AppTheme.accentColor.withValues(alpha: 0.5),
            size: 64,
          ),
          const SizedBox(height: 16),
          Text(
            'No calls yet',
            style: AppTheme.fontStyles.title.copyWith(color: Colors.white),
          ),
          const SizedBox(height: 8),
          Text(
            'Start a call to see it here',
            style: AppTheme.fontStyles.body.copyWith(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildCallItem(CallModel call) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.accentColor.withValues(alpha: 0.2)),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: _getCallTypeColor(call.type).withValues(alpha: 0.2),
            shape: BoxShape.circle,
          ),
          child: Icon(
            _getCallTypeIcon(call.type),
            color: _getCallTypeColor(call.type),
            size: 24,
          ),
        ),
        title: Row(
          children: [
            Text(
              _getCallTypeLabel(call.type),
              style: AppTheme.fontStyles.subtitle.copyWith(color: Colors.white),
            ),
            const SizedBox(width: 8),
            _buildStatusChip(call.status),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              DateFormat('MMM dd, yyyy - HH:mm').format(call.startTime),
              style: AppTheme.fontStyles.caption.copyWith(color: Colors.grey),
            ),
            if (call.duration != null) ...[
              const SizedBox(height: 4),
              Text(
                'Duration: ${_formatDuration(call.duration!)}',
                style: AppTheme.fontStyles.caption.copyWith(color: Colors.grey),
              ),
            ],
          ],
        ),
        trailing: IconButton(
          onPressed: () => _showCallDetails(call),
          icon: FaIcon(
            FontAwesomeIcons.circleInfo,
            color: AppTheme.accentColor,
            size: 20,
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(CallStatus status) {
    Color color;
    String label;

    switch (status) {
      case CallStatus.ended:
        color = Colors.green;
        label = 'Completed';
        break;
      case CallStatus.missed:
        color = Colors.red;
        label = 'Missed';
        break;
      case CallStatus.rejected:
        color = Colors.orange;
        label = 'Rejected';
        break;
      case CallStatus.active:
        color = Colors.blue;
        label = 'Active';
        break;
      case CallStatus.ringing:
        color = Colors.yellow;
        label = 'Ringing';
        break;
      case CallStatus.connecting:
        color = Colors.purple;
        label = 'Connecting';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        label,
        style: AppTheme.fontStyles.caption.copyWith(color: color, fontSize: 10),
      ),
    );
  }

  IconData _getCallTypeIcon(CallType type) {
    switch (type) {
      case CallType.voice:
        return FontAwesomeIcons.phone;
      case CallType.video:
        return FontAwesomeIcons.video;
    }
  }

  Color _getCallTypeColor(CallType type) {
    switch (type) {
      case CallType.voice:
        return Colors.green;
      case CallType.video:
        return Colors.blue;
    }
  }

  String _getCallTypeLabel(CallType type) {
    switch (type) {
      case CallType.voice:
        return 'Voice Call';
      case CallType.video:
        return 'Video Call';
    }
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    String hours = twoDigits(duration.inHours);
    String minutes = twoDigits(duration.inMinutes.remainder(60));
    String seconds = twoDigits(duration.inSeconds.remainder(60));
    return duration.inHours > 0
        ? '$hours:$minutes:$seconds'
        : '$minutes:$seconds';
  }

  void _showFilterOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12),
              decoration: BoxDecoration(
                color: Colors.grey[600],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'Filter Calls',
              style: AppTheme.fontStyles.title.copyWith(color: Colors.white),
            ),
            const SizedBox(height: 20),
            ListTile(
              leading: FaIcon(FontAwesomeIcons.phone, color: Colors.green),
              title: Text(
                'Voice Calls',
                style: AppTheme.fontStyles.body.copyWith(color: Colors.white),
              ),
              onTap: () {
                setState(() => _selectedFilter = CallType.voice);
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: FaIcon(FontAwesomeIcons.video, color: Colors.blue),
              title: Text(
                'Video Calls',
                style: AppTheme.fontStyles.body.copyWith(color: Colors.white),
              ),
              onTap: () {
                setState(() => _selectedFilter = CallType.video);
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.clear, color: Colors.grey),
              title: Text(
                'Clear Filter',
                style: AppTheme.fontStyles.body.copyWith(color: Colors.white),
              ),
              onTap: () {
                setState(() => _selectedFilter = null);
                Navigator.pop(context);
              },
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  void _showCallDetails(CallModel call) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: Text(
          'Call Details',
          style: AppTheme.fontStyles.title.copyWith(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('Type', _getCallTypeLabel(call.type)),
            _buildDetailRow('Status', _getStatusLabel(call.status)),
            _buildDetailRow(
              'Started',
              DateFormat('MMM dd, yyyy HH:mm').format(call.startTime),
            ),
            if (call.endTime != null)
              _buildDetailRow(
                'Ended',
                DateFormat('MMM dd, yyyy HH:mm').format(call.endTime!),
              ),
            if (call.duration != null)
              _buildDetailRow('Duration', _formatDuration(call.duration!)),
            _buildDetailRow('Participants', '${call.participantIds.length}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Close',
              style: AppTheme.fontStyles.body.copyWith(
                color: AppTheme.accentColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: AppTheme.fontStyles.caption.copyWith(
                color: Colors.grey,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTheme.fontStyles.caption.copyWith(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  String _getStatusLabel(CallStatus status) {
    switch (status) {
      case CallStatus.ended:
        return 'Completed';
      case CallStatus.missed:
        return 'Missed';
      case CallStatus.rejected:
        return 'Rejected';
      case CallStatus.active:
        return 'Active';
      case CallStatus.ringing:
        return 'Ringing';
      case CallStatus.connecting:
        return 'Connecting';
    }
  }
}
