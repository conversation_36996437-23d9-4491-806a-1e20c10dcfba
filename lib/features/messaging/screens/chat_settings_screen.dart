import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/core/widgets/back_button_widget.dart';
import 'package:billionaires_social/features/messaging/models/chat_settings_model.dart';
import 'package:billionaires_social/features/messaging/providers/chat_settings_provider.dart';
import 'package:billionaires_social/features/messaging/services/chat_settings_service.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/core/utils/dialog_utils.dart';
import 'package:billionaires_social/features/messaging/widgets/enhanced_privacy_modal.dart';

class ChatSettingsScreen extends ConsumerStatefulWidget {
  final String?
  chatId; // null for global settings, chatId for individual settings
  final String? chatName;

  const ChatSettingsScreen({super.key, this.chatId, this.chatName});

  @override
  ConsumerState<ChatSettingsScreen> createState() => _ChatSettingsScreenState();
}

class _ChatSettingsScreenState extends ConsumerState<ChatSettingsScreen> {
  final _passwordController = TextEditingController();

  @override
  void dispose() {
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isIndividualChat = widget.chatId != null;
    final ChatSettings? globalSettings = !isIndividualChat
        ? ref.watch(chatSettingsProvider)
        : null;
    final IndividualChatSettings? individualSettings = isIndividualChat
        ? ref.watch(individualChatSettingsProvider(widget.chatId!))
        : null;
    final settings = isIndividualChat ? individualSettings : globalSettings;

    return Scaffold(
      backgroundColor: AppTheme.primaryColor,
      appBar: AppBar(
        leading: const CustomBackButton(size: 36),
        title: Text(
          isIndividualChat ? 'Chat Settings' : 'Chat Settings',
          style: AppTheme.fontStyles.title,
        ),
        backgroundColor: AppTheme.primaryColor,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (isIndividualChat && widget.chatName != null) _buildChatHeader(),

            // Screenshot Control Section
            _buildSectionHeader('📷 Screenshot Control'),
            _buildScreenshotSettings(settings, isIndividualChat),

            // Message Clearing Section
            _buildSectionHeader('🧼 Message Clearing'),
            _buildMessageClearingSection(),

            // Message Editing Section
            _buildSectionHeader('✏️ Message Editing'),
            _buildMessageEditingSettings(settings, isIndividualChat),

            // Mute Settings Section
            _buildSectionHeader('🔕 Mute Settings'),
            _buildMuteSettings(settings, isIndividualChat),

            // Message Requests Section
            if (!isIndividualChat) ...[
              _buildSectionHeader('🛑 Message Requests'),
              _buildMessageRequestSettings(globalSettings!),
            ],

            // Self-Destruct Messages Section
            _buildSectionHeader('⏳ Self-Destruct Messages'),
            _buildSelfDestructSettings(settings, isIndividualChat),

            // Seen & Typing Status Section
            _buildSectionHeader('👁️ Seen & Typing Status'),
            _buildStatusSettings(settings, isIndividualChat),

            // Hide/Unhide Chats Section
            if (!isIndividualChat) ...[
              _buildSectionHeader('🔐 Hide/Unhide Chats'),
              _buildHiddenChatsSection(),
            ],

            // Sync Settings Section
            if (!isIndividualChat) ...[
              _buildSectionHeader('🔄 Sync Settings'),
              _buildSyncSettings(globalSettings!),
            ],

            // Privacy Settings Section
            if (!isIndividualChat) ...[
              _buildSectionHeader('🔐 Privacy'),
              _buildPrivacySettings(globalSettings!),
            ],

            // Chat Actions Section (for individual chats)
            if (isIndividualChat) ...[
              _buildSectionHeader('💬 Chat Actions'),
              _buildChatActions(),
            ],

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildChatHeader() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.accentColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.accentColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.chat_bubble_outline,
            color: AppTheme.accentColor,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.chatName!,
                  style: AppTheme.fontStyles.bodyBold.copyWith(
                    color: AppTheme.accentColor,
                  ),
                ),
                Text(
                  'Individual chat settings',
                  style: AppTheme.fontStyles.caption.copyWith(
                    color: AppTheme.secondaryAccentColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
      child: Text(
        title,
        style: AppTheme.fontStyles.subtitle.copyWith(
          color: AppTheme.accentColor,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildScreenshotSettings(dynamic settings, bool isIndividualChat) {
    return _buildSettingsCard([
      _buildSwitchTile(
        'Allow taking screenshots',
        'Let users take screenshots of this chat',
        isIndividualChat
            ? settings.allowScreenshots ?? false
            : settings.allowScreenshots,
        (value) {
          if (isIndividualChat) {
            ref
                .read(individualChatSettingsProvider(widget.chatId!).notifier)
                .toggleScreenshots();
          } else {
            ref.read(chatSettingsProvider.notifier).toggleScreenshots();
          }
        },
        icon: FontAwesomeIcons.camera,
      ),
      _buildSwitchTile(
        'Notify on screenshot',
        'Get notified when someone takes a screenshot',
        isIndividualChat
            ? settings.notifyOnScreenshot ?? true
            : settings.notifyOnScreenshot,
        (value) {
          if (isIndividualChat) {
            ref
                .read(individualChatSettingsProvider(widget.chatId!).notifier)
                .toggleScreenshotNotifications();
          } else {
            ref
                .read(chatSettingsProvider.notifier)
                .toggleScreenshotNotifications();
          }
        },
        icon: FontAwesomeIcons.bell,
      ),
      _buildActionTile(
        'Advanced Privacy Settings',
        'Configure detailed privacy and security options',
        () => _showEnhancedPrivacyModal(),
        icon: FontAwesomeIcons.shield,
      ),
    ]);
  }

  Widget _buildMessageClearingSection() {
    return _buildSettingsCard([
      _buildActionTile(
        'Clear my messages only',
        'Delete only messages you sent in all conversations',
        () => _showClearMessagesDialog(),
        icon: FontAwesomeIcons.trash,
        isDestructive: true,
      ),
      _buildActionTile(
        'Clear full conversation',
        'Delete entire conversation (if allowed)',
        () => _showClearConversationDialog(),
        icon: FontAwesomeIcons.trashCan,
        isDestructive: true,
      ),
    ]);
  }

  Widget _buildMessageEditingSettings(dynamic settings, bool isIndividualChat) {
    return _buildSettingsCard([
      _buildSwitchTile(
        'Allow editing sent messages',
        'Enable editing messages after sending',
        isIndividualChat
            ? settings.allowMessageEditing ?? true
            : settings.allowMessageEditing,
        (value) {
          if (isIndividualChat) {
            ref
                .read(individualChatSettingsProvider(widget.chatId!).notifier)
                .toggleMessageEditing();
          } else {
            ref.read(chatSettingsProvider.notifier).toggleMessageEditing();
          }
        },
        icon: FontAwesomeIcons.penToSquare,
      ),
      if (!isIndividualChat) ...[
        _buildDropdownTile(
          'Edit window duration',
          'How long you can edit messages after sending',
          _getEditWindowText(settings.editWindowDuration),
          () => _showEditWindowDialog(settings.editWindowDuration),
          icon: FontAwesomeIcons.clock,
        ),
      ],
    ]);
  }

  Widget _buildMuteSettings(dynamic settings, bool isIndividualChat) {
    return _buildSettingsCard([
      _buildSwitchTile(
        isIndividualChat ? 'Mute this chat' : 'Mute all chats',
        isIndividualChat
            ? 'Stop notifications from this chat'
            : 'Stop notifications from all chats',
        isIndividualChat ? settings.muteChat ?? false : settings.muteAllChats,
        (value) {
          if (isIndividualChat) {
            ref
                .read(individualChatSettingsProvider(widget.chatId!).notifier)
                .toggleMuteChat();
          } else {
            ref.read(chatSettingsProvider.notifier).toggleMuteAllChats();
          }
        },
        icon: FontAwesomeIcons.volumeXmark,
      ),
    ]);
  }

  Widget _buildMessageRequestSettings(ChatSettings settings) {
    return _buildSettingsCard([
      _buildSwitchTile(
        'Allow messages from non-followers',
        'Let people you don\'t follow send you messages',
        settings.allowMessagesFromNonFollowers,
        (value) => ref
            .read(chatSettingsProvider.notifier)
            .toggleMessagesFromNonFollowers(),
        icon: FontAwesomeIcons.userPlus,
      ),
      _buildSwitchTile(
        'Auto-block spam accounts',
        'Automatically block suspicious or new accounts',
        settings.autoBlockSpamAccounts,
        (value) =>
            ref.read(chatSettingsProvider.notifier).toggleAutoBlockSpam(),
        icon: FontAwesomeIcons.shieldHalved,
      ),
    ]);
  }

  Widget _buildSelfDestructSettings(dynamic settings, bool isIndividualChat) {
    return _buildSettingsCard([
      _buildSwitchTile(
        'Enable self-destruct timer',
        'Messages will automatically disappear',
        isIndividualChat
            ? settings.enableSelfDestruct ?? false
            : settings.enableSelfDestruct,
        (value) {
          if (isIndividualChat) {
            ref
                .read(individualChatSettingsProvider(widget.chatId!).notifier)
                .toggleSelfDestruct();
          } else {
            ref.read(chatSettingsProvider.notifier).toggleSelfDestruct();
          }
        },
        icon: FontAwesomeIcons.bomb,
      ),
      if (!isIndividualChat ||
          (isIndividualChat && (settings.enableSelfDestruct ?? false))) ...[
        _buildDropdownTile(
          'Self-destruct timer',
          'When messages should disappear',
          _getSelfDestructText(settings.selfDestructTimer),
          () => _showSelfDestructDialog(settings.selfDestructTimer),
          icon: FontAwesomeIcons.stopwatch,
        ),
      ],
    ]);
  }

  Widget _buildStatusSettings(dynamic settings, bool isIndividualChat) {
    return _buildSettingsCard([
      _buildSwitchTile(
        'Show seen status',
        'Let others know when you\'ve read their messages',
        isIndividualChat
            ? settings.showSeenStatus ?? true
            : settings.showSeenStatus,
        (value) {
          if (isIndividualChat) {
            ref
                .read(individualChatSettingsProvider(widget.chatId!).notifier)
                .toggleSeenStatus();
          } else {
            ref.read(chatSettingsProvider.notifier).toggleSeenStatus();
          }
        },
        icon: FontAwesomeIcons.eye,
      ),
      _buildSwitchTile(
        'Show typing indicators',
        'Show when others are typing',
        isIndividualChat
            ? settings.showTypingIndicators ?? true
            : settings.showTypingIndicators,
        (value) {
          if (isIndividualChat) {
            ref
                .read(individualChatSettingsProvider(widget.chatId!).notifier)
                .toggleTypingIndicators();
          } else {
            ref.read(chatSettingsProvider.notifier).toggleTypingIndicators();
          }
        },
        icon: FontAwesomeIcons.keyboard,
      ),
    ]);
  }

  Widget _buildHiddenChatsSection() {
    return _buildSettingsCard([
      _buildActionTile(
        'View Hidden Chats',
        'Access chats you\'ve hidden',
        () => _showHiddenChatsDialog(),
        icon: FontAwesomeIcons.eyeSlash,
      ),
    ]);
  }

  Widget _buildSyncSettings(ChatSettings settings) {
    return _buildSettingsCard([
      _buildSwitchTile(
        'Sync across devices',
        'Keep chat settings synchronized on all your devices',
        settings.syncAcrossDevices,
        (value) =>
            ref.read(chatSettingsProvider.notifier).toggleSyncAcrossDevices(),
        icon: FontAwesomeIcons.arrowsRotate,
      ),
    ]);
  }

  Widget _buildPrivacySettings(ChatSettings settings) {
    return _buildSettingsCard([
      _buildSwitchTile(
        'Require password for hidden chats',
        'Use password to access hidden chats',
        settings.requirePasswordForHiddenChats,
        (value) => ref
            .read(chatSettingsProvider.notifier)
            .togglePasswordForHiddenChats(),
        icon: FontAwesomeIcons.lock,
      ),
      _buildSwitchTile(
        'Use Face ID for hidden chats',
        'Use Face ID to access hidden chats',
        settings.useFaceIDForHiddenChats,
        (value) => ref
            .read(chatSettingsProvider.notifier)
            .toggleFaceIDForHiddenChats(),
        icon: FontAwesomeIcons.faceSmile,
      ),
    ]);
  }

  Widget _buildChatActions() {
    return _buildSettingsCard([
      _buildActionTile(
        'Pin this chat',
        'Keep this chat at the top of your list',
        () => ref
            .read(individualChatSettingsProvider(widget.chatId!).notifier)
            .togglePinned(),
        icon: FontAwesomeIcons.thumbtack,
      ),
      _buildActionTile(
        'Archive this chat',
        'Move this chat to archived conversations',
        () => ref
            .read(individualChatSettingsProvider(widget.chatId!).notifier)
            .toggleArchived(),
        icon: FontAwesomeIcons.boxArchive,
      ),
      _buildActionTile(
        'Export chat history',
        'Download chat history as PDF',
        () => _exportChatHistory(),
        icon: FontAwesomeIcons.download,
      ),
    ]);
  }

  Widget _buildSettingsCard(List<Widget> children) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.luxuryGrey.withValues(alpha: 0.3)),
      ),
      child: Column(children: children),
    );
  }

  Widget _buildSwitchTile(
    String title,
    String subtitle,
    bool value,
    ValueChanged<bool> onChanged, {
    IconData? icon,
  }) {
    return ListTile(
      leading: icon != null ? FaIcon(icon, color: AppTheme.accentColor) : null,
      title: Text(title, style: AppTheme.fontStyles.bodyBold),
      subtitle: Text(subtitle, style: AppTheme.fontStyles.caption),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: AppTheme.accentColor,
      ),
    );
  }

  Widget _buildActionTile(
    String title,
    String subtitle,
    VoidCallback onTap, {
    IconData? icon,
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: icon != null
          ? FaIcon(
              icon,
              color: isDestructive ? Colors.red : AppTheme.accentColor,
            )
          : null,
      title: Text(
        title,
        style: AppTheme.fontStyles.bodyBold.copyWith(
          color: isDestructive ? Colors.red : null,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: AppTheme.fontStyles.caption.copyWith(
          color: isDestructive ? Colors.red.withValues(alpha: 0.7) : null,
        ),
      ),
      trailing: const FaIcon(FontAwesomeIcons.chevronRight, size: 12),
      onTap: onTap,
    );
  }

  Widget _buildDropdownTile(
    String title,
    String subtitle,
    String currentValue,
    VoidCallback onTap, {
    IconData? icon,
  }) {
    return ListTile(
      leading: icon != null ? FaIcon(icon, color: AppTheme.accentColor) : null,
      title: Text(title, style: AppTheme.fontStyles.bodyBold),
      subtitle: Text(subtitle, style: AppTheme.fontStyles.caption),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            currentValue,
            style: AppTheme.fontStyles.caption.copyWith(
              color: AppTheme.accentColor,
            ),
          ),
          const SizedBox(width: 8),
          const FaIcon(FontAwesomeIcons.chevronRight, size: 12),
        ],
      ),
      onTap: onTap,
    );
  }

  String _getEditWindowText(EditWindowDuration duration) {
    switch (duration) {
      case EditWindowDuration.fiveMinutes:
        return '5 minutes';
      case EditWindowDuration.tenMinutes:
        return '10 minutes';
      case EditWindowDuration.thirtyMinutes:
        return '30 minutes';
      case EditWindowDuration.oneHour:
        return '1 hour';
      case EditWindowDuration.custom:
        return 'Custom';
    }
  }

  String _getSelfDestructText(SelfDestructTimer timer) {
    switch (timer) {
      case SelfDestructTimer.afterReading:
        return 'After reading';
      case SelfDestructTimer.after24h:
        return 'After 24h';
      case SelfDestructTimer.after7days:
        return 'After 7 days';
      case SelfDestructTimer.custom:
        return 'Custom';
    }
  }

  void _showClearMessagesDialog() {
    showAppDialog(
      context: context,
      title: const Text('Clear Your Messages'),
      content: const Text(
        'This will delete all messages you sent in all conversations. This action cannot be undone.',
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () {
            Navigator.pop(context);
            _clearMyMessages();
          },
          style: TextButton.styleFrom(foregroundColor: Colors.red),
          child: const Text('Clear'),
        ),
      ],
    );
  }

  void _showClearConversationDialog() {
    showAppDialog(
      context: context,
      title: const Text('Clear Conversation'),
      content: const Text(
        'This will delete the entire conversation. This action cannot be undone.',
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () {
            Navigator.pop(context);
            _clearConversation();
          },
          style: TextButton.styleFrom(foregroundColor: Colors.red),
          child: const Text('Clear'),
        ),
      ],
    );
  }

  void _showEditWindowDialog(EditWindowDuration currentDuration) {
    showAppDialog(
      context: context,
      title: const Text('Edit Window Duration'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: EditWindowDuration.values.map((duration) {
          return ListTile(
            title: Text(_getEditWindowText(duration)),
            trailing: currentDuration == duration
                ? const Icon(Icons.check, color: Colors.green)
                : null,
            onTap: () {
              Navigator.pop(context);
              ref
                  .read(chatSettingsProvider.notifier)
                  .setEditWindowDuration(duration);
            },
          );
        }).toList(),
      ),
    );
  }

  void _showSelfDestructDialog(SelfDestructTimer currentTimer) {
    showAppDialog(
      context: context,
      title: const Text('Self-Destruct Timer'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: SelfDestructTimer.values.map((timer) {
          return ListTile(
            title: Text(_getSelfDestructText(timer)),
            trailing: currentTimer == timer
                ? const Icon(Icons.check, color: Colors.green)
                : null,
            onTap: () {
              Navigator.pop(context);
              ref
                  .read(chatSettingsProvider.notifier)
                  .setSelfDestructTimer(timer);
            },
          );
        }).toList(),
      ),
    );
  }

  void _showHiddenChatsDialog() {
    showAppDialog(
      context: context,
      title: const Text('Hidden Chats'),
      content: const Text('Enter your password to view hidden chats:'),
      actions: [
        TextField(
          controller: _passwordController,
          obscureText: true,
          decoration: const InputDecoration(hintText: 'Password'),
        ),
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () {
            Navigator.pop(context);
            _authenticateHiddenChats();
          },
          child: const Text('Unlock'),
        ),
      ],
    );
  }

  Future<void> _clearMyMessages() async {
    try {
      final service = getIt<ChatSettingsService>();
      await service.clearMyMessagesInAllChats();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Your messages have been cleared'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to clear messages: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _clearConversation() async {
    if (widget.chatId != null) {
      try {
        final service = getIt<ChatSettingsService>();
        await service.clearFullConversation(widget.chatId!);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Conversation cleared'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context);
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to clear conversation: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _exportChatHistory() async {
    if (widget.chatId != null) {
      final service = getIt<ChatSettingsService>();
      await service.exportChatHistory(widget.chatId!);
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('Chat history exported')));
      }
    }
  }

  Future<void> _authenticateHiddenChats() async {
    final service = getIt<ChatSettingsService>();
    final success = await service.authenticateForHiddenChats(
      _passwordController.text,
    );
    if (mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Authentication successful')),
        );
        // Navigate to hidden chats screen
      } else {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('Authentication failed')));
      }
    }
  }

  void _showEnhancedPrivacyModal() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => EnhancedPrivacyModal(
        chatId: widget.chatId ?? 'global',
        chatName: widget.chatName ?? 'Global Settings',
        isGroup: false, // You can determine this based on chat type
      ),
    );
  }
}
