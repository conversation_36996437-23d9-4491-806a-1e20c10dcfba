import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:billionaires_social/features/profile/screens/main_profile_screen.dart';
import 'package:billionaires_social/core/app_theme.dart';

class SharedPostMessage extends StatelessWidget {
  final Map<String, dynamic> sharedPostData;
  final bool isMyMessage;
  final VoidCallback? onTap;

  const SharedPostMessage({
    super.key,
    required this.sharedPostData,
    required this.isMyMessage,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap ?? () => _navigateToOriginalPost(context),
      child: Container(
        margin: const EdgeInsets.only(top: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isMyMessage
              ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
              : Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with user info
            Row(
              children: [
                CircleAvatar(
                  radius: 16,
                  backgroundImage:
                      sharedPostData['postUserAvatarUrl']?.isNotEmpty == true
                      ? CachedNetworkImageProvider(
                          sharedPostData['postUserAvatarUrl'],
                        )
                      : null,
                  child: sharedPostData['postUserAvatarUrl']?.isEmpty != false
                      ? const Icon(Icons.person, size: 16)
                      : null,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        sharedPostData['postUsername'] ?? 'Unknown User',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                      if (sharedPostData['postLocation'] != null)
                        Text(
                          sharedPostData['postLocation']!,
                          style: TextStyle(
                            color: Theme.of(
                              context,
                            ).colorScheme.onSurface.withValues(alpha: 0.6),
                            fontSize: 12,
                          ),
                        ),
                    ],
                  ),
                ),
                Icon(
                  Icons.share,
                  size: 16,
                  color: Theme.of(
                    context,
                  ).colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Post media
            if (sharedPostData['postMediaUrl']?.isNotEmpty == true)
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: AspectRatio(
                  aspectRatio: 1,
                  child: CachedNetworkImage(
                    imageUrl: sharedPostData['postMediaUrl']!,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: Colors.grey[300],
                      child: const Center(
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: Colors.grey[300],
                      child: const Icon(Icons.error, size: 24),
                    ),
                  ),
                ),
              ),

            // Post caption
            if (sharedPostData['postCaption']?.isNotEmpty == true) ...[
              const SizedBox(height: 8),
              Text(
                sharedPostData['postCaption']!,
                style: TextStyle(
                  fontSize: 13,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ],

            // Tap to view indicator
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  Icons.open_in_new,
                  size: 14,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 4),
                Text(
                  'Tap to view post',
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToOriginalPost(BuildContext context) {
    // Navigate to the original post
    // This would typically open the post in the feed or a dedicated post view
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(
            title: const Text('Original Post'),
            backgroundColor: AppTheme.primaryColor,
          ),
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.post_add, size: 64, color: Colors.grey),
                const SizedBox(height: 16),
                Text(
                  'Post ID: ${sharedPostData['postId']}',
                  style: const TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 8),
                Text(
                  'By: ${sharedPostData['postUsername']}',
                  style: const TextStyle(fontSize: 14, color: Colors.grey),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => _navigateToUserProfile(context),
                  child: const Text('View Profile'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _navigateToUserProfile(BuildContext context) {
    // Navigate to the user's profile
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => MainProfileScreen(
          userId:
              sharedPostData['postId'] ??
              '', // This should be the user ID, not post ID
        ),
      ),
    );
  }
}
