import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/features/messaging/models/chat_model.dart';

// Simple provider for chat privacy settings
final chatPrivacyProvider = FutureProvider.family<ChatPrivacySettings, String>((
  ref,
  chatId,
) async {
  // Return default settings for now - in a real app this would fetch from ChatService
  return const ChatPrivacySettings(
    blockScreenshots: false,
    screenshotAlerts: true,
    readReceipts: true,
    typingIndicators: true,
    messageReactions: true,
    selfDestructEnabled: false,
    defaultSelfDestructSeconds: 10,
  );
});

class EnhancedPrivacyModal extends ConsumerStatefulWidget {
  final String chatId;
  final String chatName;
  final bool isGroup;

  const EnhancedPrivacyModal({
    super.key,
    required this.chatId,
    required this.chatName,
    this.isGroup = false,
  });

  @override
  ConsumerState<EnhancedPrivacyModal> createState() =>
      _EnhancedPrivacyModalState();
}

class _EnhancedPrivacyModalState extends ConsumerState<EnhancedPrivacyModal> {
  @override
  Widget build(BuildContext context) {
    final privacySettingsAsync = ref.watch(chatPrivacyProvider(widget.chatId));

    return Container(
      height: MediaQuery.of(context).size.height * 0.85,
      decoration: const BoxDecoration(
        color: AppTheme.primaryColor,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppTheme.luxuryGrey,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                const FaIcon(
                  FontAwesomeIcons.shield,
                  color: AppTheme.accentColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Privacy & Security',
                        style: AppTheme.fontStyles.title.copyWith(
                          color: AppTheme.accentColor,
                        ),
                      ),
                      Text(
                        'Manage privacy settings for ${widget.chatName}',
                        style: AppTheme.fontStyles.body.copyWith(
                          color: AppTheme.secondaryAccentColor,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const FaIcon(
                    FontAwesomeIcons.xmark,
                    color: AppTheme.secondaryAccentColor,
                    size: 20,
                  ),
                ),
              ],
            ),
          ),

          // Content
          Expanded(
            child: privacySettingsAsync.when(
              data: (settings) => _buildPrivacySettings(settings),
              loading: () => const Center(
                child: CircularProgressIndicator(color: AppTheme.accentColor),
              ),
              error: (error, stack) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const FaIcon(
                      FontAwesomeIcons.triangleExclamation,
                      color: Colors.red,
                      size: 48,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Failed to load privacy settings',
                      style: AppTheme.fontStyles.body.copyWith(
                        color: Colors.red,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextButton(
                      onPressed: () {
                        ref.invalidate(chatPrivacyProvider(widget.chatId));
                      },
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPrivacySettings(ChatPrivacySettings settings) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('🔒 Message Security'),
          _buildPrivacyTile(
            icon: FontAwesomeIcons.camera,
            title: 'Block Screenshots',
            subtitle: 'Prevent screenshots of this conversation',
            value: settings.blockScreenshots,
            onChanged: (value) =>
                _updateSettings(settings.copyWith(blockScreenshots: value)),
          ),
          _buildPrivacyTile(
            icon: FontAwesomeIcons.bell,
            title: 'Screenshot Alerts',
            subtitle: 'Get notified when someone takes a screenshot',
            value: settings.screenshotAlerts,
            onChanged: (value) =>
                _updateSettings(settings.copyWith(screenshotAlerts: value)),
          ),

          const SizedBox(height: 24),
          _buildSectionHeader('👁️ Read Status'),
          _buildPrivacyTile(
            icon: FontAwesomeIcons.eye,
            title: 'Read Receipts',
            subtitle: 'Show when you\'ve read messages',
            value: settings.readReceipts,
            onChanged: (value) =>
                _updateSettings(settings.copyWith(readReceipts: value)),
          ),
          _buildPrivacyTile(
            icon: FontAwesomeIcons.keyboard,
            title: 'Typing Indicators',
            subtitle: 'Show when you\'re typing',
            value: settings.typingIndicators,
            onChanged: (value) =>
                _updateSettings(settings.copyWith(typingIndicators: value)),
          ),

          const SizedBox(height: 24),
          _buildSectionHeader('⏳ Self-Destruct Messages'),
          _buildPrivacyTile(
            icon: FontAwesomeIcons.clock,
            title: 'Enable Self-Destruct',
            subtitle: 'Messages disappear after being read',
            value: settings.selfDestructEnabled,
            onChanged: (value) =>
                _updateSettings(settings.copyWith(selfDestructEnabled: value)),
          ),
          if (settings.selfDestructEnabled) ...[
            const SizedBox(height: 12),
            _buildSelfDestructTimer(settings),
          ],

          const SizedBox(height: 24),
          _buildSectionHeader('🎭 Message Features'),
          _buildPrivacyTile(
            icon: FontAwesomeIcons.faceSmile,
            title: 'Message Reactions',
            subtitle: 'Allow reactions to messages',
            value: settings.messageReactions,
            onChanged: (value) =>
                _updateSettings(settings.copyWith(messageReactions: value)),
          ),

          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        title,
        style: AppTheme.fontStyles.bodyBold.copyWith(
          color: AppTheme.accentColor,
          fontSize: 16,
        ),
      ),
    );
  }

  Widget _buildPrivacyTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.luxuryGrey.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          FaIcon(icon, color: AppTheme.accentColor, size: 20),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTheme.fontStyles.bodyBold.copyWith(
                    color: AppTheme.accentColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: AppTheme.fontStyles.caption.copyWith(
                    color: AppTheme.secondaryAccentColor,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppTheme.accentColor,
            activeTrackColor: AppTheme.accentColor.withValues(alpha: 0.3),
            inactiveThumbColor: AppTheme.luxuryGrey,
            inactiveTrackColor: AppTheme.luxuryGrey.withValues(alpha: 0.3),
          ),
        ],
      ),
    );
  }

  Widget _buildSelfDestructTimer(ChatPrivacySettings settings) {
    final timers = [5, 10, 30, 60, 300]; // seconds
    final labels = ['5s', '10s', '30s', '1m', '5m'];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.luxuryGrey.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Self-Destruct Timer',
            style: AppTheme.fontStyles.bodyBold.copyWith(
              color: AppTheme.accentColor,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: timers.asMap().entries.map((entry) {
              final index = entry.key;
              final seconds = entry.value;
              final label = labels[index];
              final isSelected = settings.defaultSelfDestructSeconds == seconds;

              return GestureDetector(
                onTap: () => _updateSettings(
                  settings.copyWith(defaultSelfDestructSeconds: seconds),
                ),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? AppTheme.accentColor
                        : AppTheme.luxuryGrey.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: isSelected
                          ? AppTheme.accentColor
                          : AppTheme.luxuryGrey.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    label,
                    style: AppTheme.fontStyles.body.copyWith(
                      color: isSelected
                          ? AppTheme.primaryColor
                          : AppTheme.accentColor,
                      fontWeight: isSelected
                          ? FontWeight.bold
                          : FontWeight.normal,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  void _updateSettings(ChatPrivacySettings newSettings) {
    // For now, just invalidate the provider to refresh
    // In a real app, this would update the chat settings via ChatService
    ref.invalidate(chatPrivacyProvider(widget.chatId));

    // Show a success message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Privacy settings updated'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
