import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/messaging/services/call_service.dart';
import 'package:billionaires_social/features/messaging/models/call_model.dart';

final callServiceProvider = Provider<CallService>((ref) => CallService());

final activeCallsProvider = StreamProvider<List<CallModel>>((ref) {
  final callService = ref.watch(callServiceProvider);
  return callService.getActiveCalls();
});

final callHistoryProvider = FutureProvider.family<List<CallModel>, String>((
  ref,
  chatId,
) async {
  final callService = ref.watch(callServiceProvider);
  return callService.getCallHistory(chatId);
});

final currentCallProvider = StateProvider<CallModel?>((ref) => null);

final callStateProvider = StateNotifierProvider<CallStateNotifier, CallState>(
  (ref) => CallStateNotifier(ref),
);

class CallState {
  final bool isInCall;
  final CallModel? currentCall;
  final bool isMuted;
  final bool isVideoEnabled;
  final bool isSpeakerOn;

  CallState({
    this.isInCall = false,
    this.currentCall,
    this.isMuted = false,
    this.isVideoEnabled = true,
    this.isSpeakerOn = false,
  });

  CallState copyWith({
    bool? isInCall,
    CallModel? currentCall,
    bool? isMuted,
    bool? isVideoEnabled,
    bool? isSpeakerOn,
  }) {
    return CallState(
      isInCall: isInCall ?? this.isInCall,
      currentCall: currentCall ?? this.currentCall,
      isMuted: isMuted ?? this.isMuted,
      isVideoEnabled: isVideoEnabled ?? this.isVideoEnabled,
      isSpeakerOn: isSpeakerOn ?? this.isSpeakerOn,
    );
  }
}

class CallStateNotifier extends StateNotifier<CallState> {
  final Ref _ref;

  CallStateNotifier(this._ref) : super(CallState());

  void startCall({
    required String chatId,
    required List<String> participantIds,
    required CallType type,
  }) async {
    try {
      final callService = _ref.read(callServiceProvider);
      final callId = await callService.initiateCall(
        chatId: chatId,
        participantIds: participantIds,
        type: type,
      );

      // Create a temporary call model for UI
      final call = CallModel(
        id: callId,
        chatId: chatId,
        initiatorId: '', // Will be set by the service
        participantIds: participantIds,
        type: type,
        status: CallStatus.ringing,
        startTime: DateTime.now(),
      );

      state = state.copyWith(isInCall: true, currentCall: call);
    } catch (e) {
      // Handle error
      debugPrint('Error starting call: $e');
    }
  }

  void acceptCall(CallModel call) async {
    try {
      final callService = _ref.read(callServiceProvider);
      await callService.acceptCall(call.id);

      state = state.copyWith(
        isInCall: true,
        currentCall: call.copyWith(status: CallStatus.active),
      );
    } catch (e) {
      debugPrint('Error accepting call: $e');
    }
  }

  void rejectCall(CallModel call) async {
    try {
      final callService = _ref.read(callServiceProvider);
      await callService.rejectCall(call.id);

      state = state.copyWith(isInCall: false, currentCall: null);
    } catch (e) {
      debugPrint('Error rejecting call: $e');
    }
  }

  void endCall() async {
    final currentCall = state.currentCall;
    if (currentCall == null) return;

    try {
      final callService = _ref.read(callServiceProvider);
      await callService.endCall(currentCall.id);

      state = state.copyWith(
        isInCall: false,
        currentCall: null,
        isMuted: false,
        isVideoEnabled: true,
        isSpeakerOn: false,
      );
    } catch (e) {
      debugPrint('Error ending call: $e');
    }
  }

  void toggleMute() {
    state = state.copyWith(isMuted: !state.isMuted);
  }

  void toggleVideo() {
    state = state.copyWith(isVideoEnabled: !state.isVideoEnabled);
  }

  void toggleSpeaker() {
    state = state.copyWith(isSpeakerOn: !state.isSpeakerOn);
  }

  void setCurrentCall(CallModel? call) {
    state = state.copyWith(isInCall: call != null, currentCall: call);
  }
}
