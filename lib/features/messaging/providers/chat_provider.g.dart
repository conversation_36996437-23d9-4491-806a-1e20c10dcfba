// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$chatServiceHash() => r'b9998fa2d42956e2e80a1f29370868db8f5e1c22';

/// See also [chatService].
@ProviderFor(chatService)
final chatServiceProvider = AutoDisposeProvider<ChatService>.internal(
  chatService,
  name: r'chatServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$chatServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ChatServiceRef = AutoDisposeProviderRef<ChatService>;
String _$chatListNotifierHash() => r'6f591a95eaeb1121a53ff9aaf457c5e4b0e4a1f6';

/// See also [ChatListNotifier].
@ProviderFor(ChatListNotifier)
final chatListNotifierProvider =
    AutoDisposeAsyncNotifierProvider<
      ChatListNotifier,
      List<ChatModel>
    >.internal(
      ChatListNotifier.new,
      name: r'chatListNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$chatListNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$ChatListNotifier = AutoDisposeAsyncNotifier<List<ChatModel>>;
String _$messagesNotifierHash() => r'5a10ea97786e9a7b02237c91d1cca4d3dcde9d11';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$MessagesNotifier
    extends BuildlessAutoDisposeAsyncNotifier<List<MessageModel>> {
  late final String chatId;

  FutureOr<List<MessageModel>> build(String chatId);
}

/// See also [MessagesNotifier].
@ProviderFor(MessagesNotifier)
const messagesNotifierProvider = MessagesNotifierFamily();

/// See also [MessagesNotifier].
class MessagesNotifierFamily extends Family<AsyncValue<List<MessageModel>>> {
  /// See also [MessagesNotifier].
  const MessagesNotifierFamily();

  /// See also [MessagesNotifier].
  MessagesNotifierProvider call(String chatId) {
    return MessagesNotifierProvider(chatId);
  }

  @override
  MessagesNotifierProvider getProviderOverride(
    covariant MessagesNotifierProvider provider,
  ) {
    return call(provider.chatId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'messagesNotifierProvider';
}

/// See also [MessagesNotifier].
class MessagesNotifierProvider
    extends
        AutoDisposeAsyncNotifierProviderImpl<
          MessagesNotifier,
          List<MessageModel>
        > {
  /// See also [MessagesNotifier].
  MessagesNotifierProvider(String chatId)
    : this._internal(
        () => MessagesNotifier()..chatId = chatId,
        from: messagesNotifierProvider,
        name: r'messagesNotifierProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$messagesNotifierHash,
        dependencies: MessagesNotifierFamily._dependencies,
        allTransitiveDependencies:
            MessagesNotifierFamily._allTransitiveDependencies,
        chatId: chatId,
      );

  MessagesNotifierProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.chatId,
  }) : super.internal();

  final String chatId;

  @override
  FutureOr<List<MessageModel>> runNotifierBuild(
    covariant MessagesNotifier notifier,
  ) {
    return notifier.build(chatId);
  }

  @override
  Override overrideWith(MessagesNotifier Function() create) {
    return ProviderOverride(
      origin: this,
      override: MessagesNotifierProvider._internal(
        () => create()..chatId = chatId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        chatId: chatId,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<MessagesNotifier, List<MessageModel>>
  createElement() {
    return _MessagesNotifierProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is MessagesNotifierProvider && other.chatId == chatId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, chatId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin MessagesNotifierRef
    on AutoDisposeAsyncNotifierProviderRef<List<MessageModel>> {
  /// The parameter `chatId` of this provider.
  String get chatId;
}

class _MessagesNotifierProviderElement
    extends
        AutoDisposeAsyncNotifierProviderElement<
          MessagesNotifier,
          List<MessageModel>
        >
    with MessagesNotifierRef {
  _MessagesNotifierProviderElement(super.provider);

  @override
  String get chatId => (origin as MessagesNotifierProvider).chatId;
}

String _$chatPrivacyNotifierHash() =>
    r'8f6f2a806366472c3c4de88130e0aee0e7922ad5';

abstract class _$ChatPrivacyNotifier
    extends BuildlessAutoDisposeAsyncNotifier<ChatPrivacySettings?> {
  late final String chatId;

  FutureOr<ChatPrivacySettings?> build(String chatId);
}

/// See also [ChatPrivacyNotifier].
@ProviderFor(ChatPrivacyNotifier)
const chatPrivacyNotifierProvider = ChatPrivacyNotifierFamily();

/// See also [ChatPrivacyNotifier].
class ChatPrivacyNotifierFamily
    extends Family<AsyncValue<ChatPrivacySettings?>> {
  /// See also [ChatPrivacyNotifier].
  const ChatPrivacyNotifierFamily();

  /// See also [ChatPrivacyNotifier].
  ChatPrivacyNotifierProvider call(String chatId) {
    return ChatPrivacyNotifierProvider(chatId);
  }

  @override
  ChatPrivacyNotifierProvider getProviderOverride(
    covariant ChatPrivacyNotifierProvider provider,
  ) {
    return call(provider.chatId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'chatPrivacyNotifierProvider';
}

/// See also [ChatPrivacyNotifier].
class ChatPrivacyNotifierProvider
    extends
        AutoDisposeAsyncNotifierProviderImpl<
          ChatPrivacyNotifier,
          ChatPrivacySettings?
        > {
  /// See also [ChatPrivacyNotifier].
  ChatPrivacyNotifierProvider(String chatId)
    : this._internal(
        () => ChatPrivacyNotifier()..chatId = chatId,
        from: chatPrivacyNotifierProvider,
        name: r'chatPrivacyNotifierProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$chatPrivacyNotifierHash,
        dependencies: ChatPrivacyNotifierFamily._dependencies,
        allTransitiveDependencies:
            ChatPrivacyNotifierFamily._allTransitiveDependencies,
        chatId: chatId,
      );

  ChatPrivacyNotifierProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.chatId,
  }) : super.internal();

  final String chatId;

  @override
  FutureOr<ChatPrivacySettings?> runNotifierBuild(
    covariant ChatPrivacyNotifier notifier,
  ) {
    return notifier.build(chatId);
  }

  @override
  Override overrideWith(ChatPrivacyNotifier Function() create) {
    return ProviderOverride(
      origin: this,
      override: ChatPrivacyNotifierProvider._internal(
        () => create()..chatId = chatId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        chatId: chatId,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<
    ChatPrivacyNotifier,
    ChatPrivacySettings?
  >
  createElement() {
    return _ChatPrivacyNotifierProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ChatPrivacyNotifierProvider && other.chatId == chatId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, chatId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ChatPrivacyNotifierRef
    on AutoDisposeAsyncNotifierProviderRef<ChatPrivacySettings?> {
  /// The parameter `chatId` of this provider.
  String get chatId;
}

class _ChatPrivacyNotifierProviderElement
    extends
        AutoDisposeAsyncNotifierProviderElement<
          ChatPrivacyNotifier,
          ChatPrivacySettings?
        >
    with ChatPrivacyNotifierRef {
  _ChatPrivacyNotifierProviderElement(super.provider);

  @override
  String get chatId => (origin as ChatPrivacyNotifierProvider).chatId;
}

String _$messageSearchNotifierHash() =>
    r'e361ed6397065106b38f50cf6a318dd14edb3af1';

/// See also [MessageSearchNotifier].
@ProviderFor(MessageSearchNotifier)
final messageSearchNotifierProvider =
    AutoDisposeAsyncNotifierProvider<
      MessageSearchNotifier,
      List<MessageModel>
    >.internal(
      MessageSearchNotifier.new,
      name: r'messageSearchNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$messageSearchNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$MessageSearchNotifier = AutoDisposeAsyncNotifier<List<MessageModel>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
