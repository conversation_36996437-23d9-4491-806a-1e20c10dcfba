import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/messaging/models/chat_settings_model.dart';
// import 'package:billionaires_social/features/messaging/services/chat_settings_service.dart';

final chatSettingsProvider =
    StateNotifierProvider<ChatSettingsNotifier, ChatSettings>((ref) {
      return ChatSettingsNotifier();
    });

final individualChatSettingsProvider =
    StateNotifierProvider.family<
      IndividualChatSettingsNotifier,
      IndividualChatSettings,
      String
    >((ref, chatId) {
      return IndividualChatSettingsNotifier(chatId);
    });

class ChatSettingsNotifier extends StateNotifier<ChatSettings> {
  ChatSettingsNotifier() : super(const ChatSettings());

  void updateSettings(ChatSettings newSettings) {
    state = newSettings;
  }

  void toggleScreenshots() {
    state = state.copyWith(allowScreenshots: !state.allowScreenshots);
  }

  void toggleScreenshotNotifications() {
    state = state.copyWith(notifyOnScreenshot: !state.notifyOnScreenshot);
  }

  void toggleMessageEditing() {
    state = state.copyWith(allowMessageEditing: !state.allowMessageEditing);
  }

  void setEditWindowDuration(EditWindowDuration duration) {
    state = state.copyWith(editWindowDuration: duration);
  }

  void setCustomEditWindowMinutes(int minutes) {
    state = state.copyWith(customEditWindowMinutes: minutes);
  }

  void toggleMuteAllChats() {
    state = state.copyWith(muteAllChats: !state.muteAllChats);
  }

  void toggleMessagesFromNonFollowers() {
    state = state.copyWith(
      allowMessagesFromNonFollowers: !state.allowMessagesFromNonFollowers,
    );
  }

  void toggleAutoBlockSpam() {
    state = state.copyWith(autoBlockSpamAccounts: !state.autoBlockSpamAccounts);
  }

  void toggleSelfDestruct() {
    state = state.copyWith(enableSelfDestruct: !state.enableSelfDestruct);
  }

  void setSelfDestructTimer(SelfDestructTimer timer) {
    state = state.copyWith(selfDestructTimer: timer);
  }

  void setCustomSelfDestructSeconds(int seconds) {
    state = state.copyWith(customSelfDestructSeconds: seconds);
  }

  void toggleSeenStatus() {
    state = state.copyWith(showSeenStatus: !state.showSeenStatus);
  }

  void toggleTypingIndicators() {
    state = state.copyWith(showTypingIndicators: !state.showTypingIndicators);
  }

  void toggleSyncAcrossDevices() {
    state = state.copyWith(syncAcrossDevices: !state.syncAcrossDevices);
  }

  void togglePasswordForHiddenChats() {
    state = state.copyWith(
      requirePasswordForHiddenChats: !state.requirePasswordForHiddenChats,
    );
  }

  void toggleFaceIDForHiddenChats() {
    state = state.copyWith(
      useFaceIDForHiddenChats: !state.useFaceIDForHiddenChats,
    );
  }
}

class IndividualChatSettingsNotifier
    extends StateNotifier<IndividualChatSettings> {
  final String chatId;

  IndividualChatSettingsNotifier(this.chatId)
    : super(IndividualChatSettings(chatId: chatId));

  void updateSettings(IndividualChatSettings newSettings) {
    state = newSettings;
  }

  void toggleScreenshots() {
    state = state.copyWith(
      allowScreenshots: !(state.allowScreenshots ?? false),
    );
  }

  void toggleScreenshotNotifications() {
    state = state.copyWith(
      notifyOnScreenshot: !(state.notifyOnScreenshot ?? false),
    );
  }

  void toggleMessageEditing() {
    state = state.copyWith(
      allowMessageEditing: !(state.allowMessageEditing ?? false),
    );
  }

  void toggleMuteChat() {
    state = state.copyWith(muteChat: !(state.muteChat ?? false));
  }

  void toggleSeenStatus() {
    state = state.copyWith(showSeenStatus: !(state.showSeenStatus ?? false));
  }

  void toggleTypingIndicators() {
    state = state.copyWith(
      showTypingIndicators: !(state.showTypingIndicators ?? false),
    );
  }

  void toggleSelfDestruct() {
    state = state.copyWith(
      enableSelfDestruct: !(state.enableSelfDestruct ?? false),
    );
  }

  void setSelfDestructTimer(SelfDestructTimer timer) {
    state = state.copyWith(selfDestructTimer: timer);
  }

  void setCustomSelfDestructSeconds(int seconds) {
    state = state.copyWith(customSelfDestructSeconds: seconds);
  }

  void toggleHidden() {
    state = state.copyWith(isHidden: !state.isHidden);
  }

  void togglePinned() {
    state = state.copyWith(isPinned: !state.isPinned);
  }

  void toggleArchived() {
    state = state.copyWith(isArchived: !state.isArchived);
  }

  void toggleStarred() {
    state = state.copyWith(isStarred: !state.isStarred);
  }
}
