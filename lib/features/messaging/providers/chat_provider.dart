import 'package:billionaires_social/features/messaging/models/chat_model.dart';
import 'package:billionaires_social/features/messaging/services/chat_service.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

part 'chat_provider.g.dart';

@riverpod
ChatService chatService(Ref ref) {
  return ChatService();
}

@riverpod
class ChatListNotifier extends _$ChatListNotifier {
  @override
  Future<List<ChatModel>> build() async {
    final chatService = getIt<ChatService>();
    return chatService.getChatsStream().first;
  }

  Future<void> hideChat(String chatId) async {
    final chatService = getIt<ChatService>();
    try {
      await chatService.hideChat(chatId);
      await refresh();
    } catch (e) {
      throw Exception('Failed to hide chat: ${e.toString()}');
    }
  }

  Future<void> unhideChat(String chatId) async {
    final chatService = getIt<ChatService>();
    try {
      await chatService.unhideChat(chatId);
      await refresh();
    } catch (e) {
      throw Exception('Failed to unhide chat: ${e.toString()}');
    }
  }

  Future<void> deleteChat(String chatId) async {
    final chatService = getIt<ChatService>();
    try {
      await chatService.deleteChat(chatId);
      await refresh();
    } catch (e) {
      throw Exception('Failed to delete chat: ${e.toString()}');
    }
  }

  Future<void> clearMyMessages(String chatId) async {
    final chatService = getIt<ChatService>();
    try {
      await chatService.clearMyMessages(chatId);
      await refresh();
    } catch (e) {
      throw Exception('Failed to clear messages: ${e.toString()}');
    }
  }

  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(
      () => getIt<ChatService>().getChatsStream().first,
    );
  }
}

@riverpod
class MessagesNotifier extends _$MessagesNotifier {
  @override
  Future<List<MessageModel>> build(String chatId) async {
    final chatService = getIt<ChatService>();
    return chatService.getMessagesStream(chatId).first;
  }

  Future<void> sendMessage({
    required String content,
    required MessageType type,
    SelfDestructSettings? selfDestructSettings,
  }) async {
    final currentChatId = chatId;
    final chatService = getIt<ChatService>();
    try {
      await chatService.sendMessage(
        chatId: currentChatId,
        content: content,
        type: type,
        selfDestructSettings: selfDestructSettings,
      );
    } catch (e) {
      throw Exception('Failed to send message: ${e.toString()}');
    }
  }

  Future<void> editMessage({
    required String messageId,
    required String newContent,
  }) async {
    final chatService = getIt<ChatService>();
    try {
      await chatService.editMessage(
        messageId: messageId,
        chatId: chatId,
        newContent: newContent,
      );
      await refresh();
    } catch (e) {
      throw Exception('Failed to edit message: ${e.toString()}');
    }
  }

  Future<void> deleteMessage(String messageId) async {
    final chatService = getIt<ChatService>();
    try {
      await chatService.deleteMessage(messageId, chatId);
      await refresh();
    } catch (e) {
      throw Exception('Failed to delete message: ${e.toString()}');
    }
  }

  Future<void> addReaction({
    required String messageId,
    required String emoji,
  }) async {
    final chatService = getIt<ChatService>();
    try {
      await chatService.addReaction(
        messageId: messageId,
        chatId: chatId,
        emoji: emoji,
      );
      await refresh();
    } catch (e) {
      throw Exception('Failed to add reaction: ${e.toString()}');
    }
  }

  Future<void> removeReaction({
    required String messageId,
    required String emoji,
  }) async {
    final chatService = getIt<ChatService>();
    try {
      await chatService.removeReaction(
        messageId: messageId,
        chatId: chatId,
        emoji: emoji,
      );
      await refresh();
    } catch (e) {
      throw Exception('Failed to remove reaction: ${e.toString()}');
    }
  }

  Future<void> markMessageAsRead(String messageId) async {
    final chatService = getIt<ChatService>();
    await chatService.markMessageAsRead(messageId, chatId);
    await refresh();
  }

  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(
      () => getIt<ChatService>().getMessagesStream(chatId).first,
    );
  }
}

@riverpod
class ChatPrivacyNotifier extends _$ChatPrivacyNotifier {
  @override
  Future<ChatPrivacySettings?> build(String chatId) async {
    final chatService = getIt<ChatService>();
    final chat = await chatService.getChatById(chatId);
    return chat?.privacySettings;
  }

  Future<void> updatePrivacySettings(ChatPrivacySettings settings) async {
    final chatService = getIt<ChatService>();
    await chatService.updateChatPrivacySettings(
      chatId: chatId,
      settings: settings,
    );
    await refresh();
  }

  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final chatService = getIt<ChatService>();
      final chat = await chatService.getChatById(chatId);
      return chat?.privacySettings;
    });
  }
}

@riverpod
class MessageSearchNotifier extends _$MessageSearchNotifier {
  @override
  Future<List<MessageModel>> build() async {
    return [];
  }

  Future<void> searchMessages({
    required String query,
    String? chatId,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(
      () => getIt<ChatService>().searchMessages(
        query: query,
        chatId: chatId,
        fromDate: fromDate,
        toDate: toDate,
      ),
    );
  }

  void clearSearch() {
    state = const AsyncValue.data([]);
  }
}
