import 'package:billionaires_social/features/notifications/screens/notification_settings_screen.dart';
import 'package:billionaires_social/features/profile/screens/close_friends_screen.dart';
import 'package:billionaires_social/features/profile/screens/saved_content_screen.dart';
import 'package:billionaires_social/features/settings/screens/privacy_settings_screen.dart';
import 'package:billionaires_social/features/settings/screens/security_settings_screen.dart';
import 'package:billionaires_social/features/stories/screens/story_settings_screen.dart';
import 'package:billionaires_social/features/settings/screens/chat_privacy_screen.dart';
import 'package:billionaires_social/features/messaging/screens/chat_settings_screen.dart';
import 'package:billionaires_social/features/settings/screens/app_appearance_screen.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class MainSettingsScreen extends StatelessWidget {
  const MainSettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings and Privacy'),
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
      ),
      body: ListView(
        children: [
          _buildSectionHeader('Account'),
          _SettingsTile(
            icon: FontAwesomeIcons.userShield,
            title: 'Account Privacy',
            onTap: () => _navigateTo(context, const PrivacySettingsScreen()),
          ),
          _SettingsTile(
            icon: FontAwesomeIcons.lock,
            title: 'Security',
            onTap: () => _navigateTo(context, const SecuritySettingsScreen()),
          ),
          _SettingsTile(
            icon: FontAwesomeIcons.comments,
            title: 'Chat Privacy',
            onTap: () => _navigateTo(context, const ChatPrivacyScreen()),
          ),
          _SettingsTile(
            icon: FontAwesomeIcons.gear,
            title: 'Chat Settings',
            onTap: () => _navigateTo(context, const ChatSettingsScreen()),
          ),
          _buildSectionHeader('Content & Display'),
          _SettingsTile(
            icon: FontAwesomeIcons.solidBookmark,
            title: 'Saved',
            onTap: () => _navigateTo(context, const SavedContentScreen()),
          ),
          _SettingsTile(
            icon: FontAwesomeIcons.solidStar,
            title: 'Close Friends',
            onTap: () => _navigateTo(context, const CloseFriendsScreen()),
          ),
          _SettingsTile(
            icon: FontAwesomeIcons.solidCirclePlay,
            title: 'Story Settings',
            onTap: () => _navigateTo(context, const StorySettingsScreen()),
          ),
          _SettingsTile(
            icon: FontAwesomeIcons.solidBell,
            title: 'Notifications',
            onTap: () =>
                _navigateTo(context, const NotificationSettingsScreen()),
          ),
          _SettingsTile(
            icon: FontAwesomeIcons.palette,
            title: 'App Appearance',
            onTap: () => _navigateTo(context, const AppAppearanceScreen()),
          ),
          _buildSectionHeader('Login'),
          _SettingsTile(
            icon: FontAwesomeIcons.arrowRightFromBracket,
            title: 'Log Out',
            onTap: () {
              // Implement logout
            },
            isDestructive: true,
          ),
        ],
      ),
    );
  }

  void _navigateTo(BuildContext context, Widget screen) {
    Navigator.of(context).push(MaterialPageRoute(builder: (_) => screen));
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
      child: Text(
        title.toUpperCase(),
        style: TextStyle(
          color: Colors.grey.shade600,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }
}

class _SettingsTile extends StatelessWidget {
  final IconData icon;
  final String title;
  final VoidCallback onTap;
  final bool isDestructive;

  const _SettingsTile({
    required this.icon,
    required this.title,
    required this.onTap,
    this.isDestructive = false,
  });

  @override
  Widget build(BuildContext context) {
    final color = isDestructive
        ? Colors.red
        : Theme.of(context).textTheme.bodyLarge?.color;
    return ListTile(
      leading: FaIcon(icon, color: color, size: 20),
      title: Text(title, style: TextStyle(color: color)),
      trailing: FaIcon(
        FontAwesomeIcons.chevronRight,
        size: 16,
        color: color?.withValues(alpha: 0.5),
      ),
      onTap: onTap,
    );
  }
}
