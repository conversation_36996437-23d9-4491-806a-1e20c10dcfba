import 'package:flutter/material.dart';

class AccountSettingsScreen extends StatelessWidget {
  const AccountSettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Account Center')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: <PERSON>um<PERSON>(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Manage your connected experiences and account settings across Billionaire\'s Social.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
            const ListTile(
              leading: Icon(Icons.person_outline),
              title: Text('Personal Details'),
            ),
            const ListTile(
              leading: Icon(Icons.lock_outline),
              title: Text('Password and Security'),
            ),
            const ListTile(
              leading: Icon(Icons.monetization_on_outlined),
              title: Text('Ad Preferences'),
            ),
          ],
        ),
      ),
    );
  }
}
