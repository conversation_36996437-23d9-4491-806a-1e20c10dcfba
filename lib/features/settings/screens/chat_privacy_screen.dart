import 'package:billionaires_social/features/settings/models/chat_privacy_model.dart';
import 'package:billionaires_social/features/settings/providers/chat_privacy_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ChatPrivacyScreen extends ConsumerWidget {
  const ChatPrivacyScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settingsAsync = ref.watch(chatPrivacyNotifierProvider);

    return Scaffold(
      appBar: AppBar(title: const Text('Chat Privacy')),
      body: settingsAsync.when(
        data: (settings) => ListView(
          children: [
            _buildSectionHeader(context, 'Message Controls'),
            SwitchListTile(
              title: const Text('Block messages from non-followers'),
              subtitle: const Text('Only followers can send you messages'),
              value: settings.blockNonFollowers,
              onChanged: (value) =>
                  _update(ref, settings.copyWith(blockNonFollowers: value)),
            ),
            SwitchListTile(
              title: const Text('Auto-delete messages after reading'),
              subtitle: const Text('Messages disappear after being read'),
              value: settings.autoDeleteAfterReading,
              onChanged: (value) => _update(
                ref,
                settings.copyWith(autoDeleteAfterReading: value),
              ),
            ),
            const Divider(),
            _buildSectionHeader(context, 'Conversation Management'),
            ListTile(
              title: const Text('Muted conversations'),
              subtitle: Text(
                '${settings.mutedConversationIds.length} conversations muted',
              ),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                // TODO: Show muted conversations list
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text(
                      'Muted conversations management coming soon!',
                    ),
                  ),
                );
              },
            ),
            ListTile(
              title: const Text('Blocked users'),
              subtitle: Text('${settings.blockedUserIds.length} users blocked'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                // TODO: Show blocked users list
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Blocked users management coming soon!'),
                  ),
                );
              },
            ),
            const Divider(),
            _buildSectionHeader(context, 'Chat Features'),
            SwitchListTile(
              title: const Text('Allow group chats'),
              subtitle: const Text(
                'Receive invitations to group conversations',
              ),
              value: settings.allowGroupChats,
              onChanged: (value) =>
                  _update(ref, settings.copyWith(allowGroupChats: value)),
            ),
            SwitchListTile(
              title: const Text('Allow voice messages'),
              subtitle: const Text('Receive voice messages from others'),
              value: settings.allowVoiceMessages,
              onChanged: (value) =>
                  _update(ref, settings.copyWith(allowVoiceMessages: value)),
            ),
            SwitchListTile(
              title: const Text('Allow video calls'),
              subtitle: const Text('Receive video call requests'),
              value: settings.allowVideoCalls,
              onChanged: (value) =>
                  _update(ref, settings.copyWith(allowVideoCalls: value)),
            ),
          ],
        ),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (err, stack) => Center(child: Text('Error: $err')),
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
      child: Text(
        title.toUpperCase(),
        style: Theme.of(context).textTheme.labelMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }

  void _update(WidgetRef ref, ChatPrivacySettings newSettings) {
    ref.read(chatPrivacyNotifierProvider.notifier).updateSettings(newSettings);
  }
}
