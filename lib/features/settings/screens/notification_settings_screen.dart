import 'package:flutter/material.dart';

class NotificationSettingsScreen extends StatelessWidget {
  const NotificationSettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Notifications')),
      body: ListView(
        children: [
          _buildSectionHeader(context, 'Push Notifications'),
          SwitchListTile(
            title: const Text('Pause All'),
            value: false,
            onChanged: (value) {},
          ),
          _buildNotificationSwitch(
            title: 'Posts, Stories, and Comments',
            subtitle: 'Likes, comments, and new posts.',
            value: true,
          ),
          _buildNotificationSwitch(
            title: 'Following & Followers',
            subtitle: 'New followers and accepted requests.',
            value: true,
          ),
          _buildNotificationSwitch(
            title: 'Direct Messages',
            subtitle: 'New messages from individuals and groups.',
            value: true,
          ),
          _buildNotificationSwitch(
            title: 'Live & Events',
            subtitle: 'Notifications about live streams and upcoming events.',
            value: false,
          ),
          const Divider(),
          _buildSectionHeader(context, 'Other Notification Types'),
          _buildNotificationSwitch(
            title: 'Email Notifications',
            subtitle: 'Product and feature updates.',
            value: true,
          ),
          _buildNotificationSwitch(
            title: 'SMS Notifications',
            subtitle: 'Only for critical security alerts.',
            value: false,
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Text(
        title.toUpperCase(),
        style: Theme.of(context).textTheme.labelMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }

  Widget _buildNotificationSwitch({
    required String title,
    required String subtitle,
    required bool value,
  }) {
    return SwitchListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      value: value,
      onChanged: (val) {
        // In a real app, this would be connected to a provider
      },
    );
  }
}
