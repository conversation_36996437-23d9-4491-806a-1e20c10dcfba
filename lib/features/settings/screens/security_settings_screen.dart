import 'package:billionaires_social/features/settings/providers/security_settings_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class SecuritySettingsScreen extends ConsumerWidget {
  const SecuritySettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final securitySettings = ref.watch(securitySettingsNotifierProvider);
    final notifier = ref.read(securitySettingsNotifierProvider.notifier);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Security'),
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
      ),
      body: securitySettings.when(
        data: (settings) => ListView(
          children: [
            SwitchListTile(
              title: Text(
                'Two-Factor Authentication',
                style: Theme.of(context).textTheme.bodyLarge,
              ),
              subtitle: Text(
                'Add an extra layer of security',
                style: Theme.of(context).textTheme.bodySmall,
              ),
              value: settings.isTwoFactorEnabled,
              onChanged: (value) => notifier.setTwoFactor(value),
              activeColor: Theme.of(context).colorScheme.secondary,
            ),
            const Divider(),
            SwitchListTile(
              title: Text(
                'Biometric Login',
                style: Theme.of(context).textTheme.bodyLarge,
              ),
              subtitle: Text(
                'Use Face ID or Touch ID to log in',
                style: Theme.of(context).textTheme.bodySmall,
              ),
              value: settings.isBiometricLoginEnabled,
              onChanged: (value) => notifier.setBiometricLogin(value),
              activeColor: Theme.of(context).colorScheme.secondary,
            ),
          ],
        ),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (err, stack) => Center(child: Text('Error: $err')),
      ),
    );
  }
}
