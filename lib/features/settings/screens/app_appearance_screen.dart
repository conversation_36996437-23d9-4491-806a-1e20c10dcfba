import 'package:billionaires_social/core/app_themes.dart';
import 'package:billionaires_social/features/settings/providers/theme_provider.dart';
import 'package:billionaires_social/core/widgets/back_button_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class AppAppearanceScreen extends ConsumerWidget {
  const AppAppearanceScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentThemeType = ref.watch(themeProvider);

    return Scaffold(
      appBar: AppBar(
        leading: const CustomBackButton(size: 36),
        title: const Text('App Appearance'),
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16.0),
        itemCount: AppThemeType.values.length,
        itemBuilder: (context, index) {
          final themeType = AppThemeType.values[index];
          final themeData = AppThemes.getTheme(themeType);
          final isSelected = currentThemeType == themeType;

          return GestureDetector(
            onTap: () {
              ref.read(themeProvider.notifier).setTheme(themeType);
            },
            child: Container(
              margin: const EdgeInsets.only(bottom: 12),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: themeData.primaryColor,
                borderRadius: BorderRadius.circular(12),
                border: isSelected
                    ? Border.all(
                        color: themeData.colorScheme.secondary,
                        width: 3,
                      )
                    : Border.all(color: Colors.black.withValues(alpha: 0.2)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    themeType.name[0].toUpperCase() +
                        themeType.name.substring(1),
                    style: TextStyle(
                      color: themeData.colorScheme.onPrimary,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (isSelected)
                    Icon(
                      Icons.check_circle,
                      color: themeData.colorScheme.secondary,
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
