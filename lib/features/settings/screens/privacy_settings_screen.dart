import 'package:billionaires_social/features/settings/providers/privacy_settings_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class PrivacySettingsScreen extends ConsumerWidget {
  const PrivacySettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settingsState = ref.watch(privacySettingsNotifierProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Account Privacy'),
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
      ),
      body: settingsState.when(
        data: (settings) {
          return ListView(
            children: [
              const SizedBox(height: 16),
              _SwitchTile(
                title: 'Private Account',
                subtitle:
                    'When your account is private, only people you approve can see your photos and videos. Your existing followers won\'t be affected.',
                value: settings.isPrivate,
                onChanged: (value) {
                  ref
                      .read(privacySettingsNotifierProvider.notifier)
                      .setAccountPrivate(value);
                },
              ),
              const Divider(height: 32),
              _buildSectionHeader(context, 'Account Visibility'),
              _SwitchTile(
                title: 'Hide Followers List',
                subtitle: 'Other users won\'t be able to see who follows you.',
                value: settings.hideFollowersList,
                onChanged: (value) {
                  ref
                      .read(privacySettingsNotifierProvider.notifier)
                      .setHideFollowersList(value);
                },
              ),
              _SwitchTile(
                title: 'Hide Following List',
                subtitle: 'Other users won\'t be able to see who you follow.',
                value: settings.hideFollowingList,
                onChanged: (value) {
                  ref
                      .read(privacySettingsNotifierProvider.notifier)
                      .setHideFollowingList(value);
                },
              ),
              const Divider(height: 32),
              _buildSectionHeader(context, 'Advanced Privacy'),
              _PrivacyLevelDropdown(settings.privacyLevel, (value) {
                if (value != null) {
                  ref
                      .read(privacySettingsNotifierProvider.notifier)
                      .setPrivacyLevel(value);
                }
              }),
              _SwitchTile(
                title: 'Temporarily Hide Profile',
                subtitle: 'Hide your profile from everyone for a limited time.',
                value: settings.temporarilyHidden,
                onChanged: (value) {
                  ref
                      .read(privacySettingsNotifierProvider.notifier)
                      .setTemporarilyHidden(value);
                },
              ),
              _SwitchTile(
                title: 'Enable Profile View History',
                subtitle:
                    'See who viewed your profile (if enabled for both parties).',
                value: settings.enableProfileViewHistory,
                onChanged: (value) {
                  ref
                      .read(privacySettingsNotifierProvider.notifier)
                      .setEnableProfileViewHistory(value);
                },
              ),
              _buildNavigationTile(
                context,
                'Blocked Users',
                'View and manage blocked accounts.',
                () {
                  // TODO: Navigate to Blocked Users Screen
                },
              ),
              const Divider(height: 32),
              _buildSectionHeader(context, 'Discovery & Search'),
              _SwitchTile(
                title: 'Hide from Search',
                subtitle: 'Your account won\'t appear in search results.',
                value: settings.hideFromSearch,
                onChanged: (value) {
                  ref
                      .read(privacySettingsNotifierProvider.notifier)
                      .setHideFromSearch(value);
                },
              ),
              _SwitchTile(
                title: 'Hide from Suggestions',
                subtitle: 'Your account won\'t be suggested to other users.',
                value: settings.hideFromSuggestions,
                onChanged: (value) {
                  ref
                      .read(privacySettingsNotifierProvider.notifier)
                      .setHideFromSuggestions(value);
                },
              ),
              _SwitchTile(
                title: 'Hide from Explore',
                subtitle: 'Your content won\'t appear in the Explore tab.',
                value: settings.hideFromExplore,
                onChanged: (value) {
                  ref
                      .read(privacySettingsNotifierProvider.notifier)
                      .setHideFromExplore(value);
                },
              ),
              _SwitchTile(
                title: 'Allow others to find me by photo',
                subtitle:
                    'Let people search for your profile using your photos. Turn off to prevent photo-based search.',
                value: settings.discoverableByPhoto,
                onChanged: (value) {
                  ref
                      .read(privacySettingsNotifierProvider.notifier)
                      .setDiscoverableByPhoto(value);
                },
              ),
              _SwitchTile(
                title: 'Allow Existing Followers to See Profile',
                subtitle:
                    'Existing followers can still see your profile even when hidden.',
                value: settings.allowExistingFollowersToSeeProfile,
                onChanged: (value) {
                  ref
                      .read(privacySettingsNotifierProvider.notifier)
                      .setAllowExistingFollowersToSeeProfile(value);
                },
              ),
              const Divider(height: 32),
              _buildSectionHeader(context, 'Interactions'),
              _buildNavigationTile(
                context,
                'Blocked Accounts',
                'View and manage the accounts you have blocked.',
                () {
                  /* Navigate to Blocked Accounts Screen */
                },
              ),
              _buildNavigationTile(
                context,
                'Muted Accounts',
                'View and manage the accounts you have muted.',
                () {
                  /* Navigate to Muted Accounts Screen */
                },
              ),
            ],
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (e, s) => Center(child: Text('Failed to load settings: $e')),
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
      child: Text(
        title.toUpperCase(),
        style: Theme.of(context).textTheme.labelMedium?.copyWith(
          color: Colors.black.withValues(alpha: 0.5),
          fontWeight: FontWeight.bold,
          letterSpacing: 1.2,
        ),
      ),
    );
  }

  Widget _buildNavigationTile(
    BuildContext context,
    String title,
    String subtitle,
    VoidCallback onTap,
  ) {
    return ListTile(
      title: Text(title, style: Theme.of(context).textTheme.bodyLarge),
      subtitle: Text(subtitle, style: Theme.of(context).textTheme.bodySmall),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: Theme.of(context).iconTheme.color,
      ),
      onTap: onTap,
    );
  }
}

class _SwitchTile extends StatelessWidget {
  final String title;
  final String subtitle;
  final bool value;
  final ValueChanged<bool> onChanged;

  const _SwitchTile({
    required this.title,
    required this.subtitle,
    required this.value,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return SwitchListTile(
      title: Text(title, style: Theme.of(context).textTheme.bodyLarge),
      subtitle: Padding(
        padding: const EdgeInsets.only(top: 4.0),
        child: Text(subtitle, style: Theme.of(context).textTheme.bodySmall),
      ),
      value: value,
      onChanged: onChanged,
      activeColor: Theme.of(context).colorScheme.secondary,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16.0),
    );
  }
}

class _PrivacyLevelDropdown extends StatelessWidget {
  final String value;
  final ValueChanged<String?>? onChanged;
  const _PrivacyLevelDropdown(this.value, this.onChanged);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: DropdownButtonFormField<String>(
        value: value,
        decoration: const InputDecoration(
          labelText: 'Profile Privacy Level',
          border: OutlineInputBorder(),
        ),
        items: const [
          DropdownMenuItem(value: 'public', child: Text('Public')),
          DropdownMenuItem(value: 'private', child: Text('Private')),
          DropdownMenuItem(value: 'friends', child: Text('Friends Only')),
          DropdownMenuItem(value: 'custom', child: Text('Custom List')),
        ],
        onChanged: (val) {
          if (val != null && onChanged != null) onChanged!(val);
        },
      ),
    );
  }
}
