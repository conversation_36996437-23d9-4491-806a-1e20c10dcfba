import 'package:freezed_annotation/freezed_annotation.dart';

part 'chat_privacy_model.freezed.dart';
part 'chat_privacy_model.g.dart';

@freezed
abstract class ChatPrivacySettings with _$ChatPrivacySettings {
  const factory ChatPrivacySettings({
    required String id,
    required bool blockNonFollowers,
    required bool autoDeleteAfterReading,
    required List<String> mutedConversationIds,
    required List<String> blockedUserIds,
    required bool allowGroupChats,
    required bool allowVoiceMessages,
    required bool allowVideoCalls,
  }) = _ChatPrivacySettings;

  factory ChatPrivacySettings.fromJson(Map<String, dynamic> json) =>
      _$ChatPrivacySettingsFromJson(json);
}
