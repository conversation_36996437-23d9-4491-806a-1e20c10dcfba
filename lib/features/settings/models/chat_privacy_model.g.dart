// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_privacy_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ChatPrivacySettings _$ChatPrivacySettingsFromJson(Map<String, dynamic> json) =>
    _ChatPrivacySettings(
      id: json['id'] as String,
      blockNonFollowers: json['blockNonFollowers'] as bool,
      autoDeleteAfterReading: json['autoDeleteAfterReading'] as bool,
      mutedConversationIds: (json['mutedConversationIds'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      blockedUserIds: (json['blockedUserIds'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      allowGroupChats: json['allowGroupChats'] as bool,
      allowVoiceMessages: json['allowVoiceMessages'] as bool,
      allowVideoCalls: json['allowVideoCalls'] as bool,
    );

Map<String, dynamic> _$ChatPrivacySettingsToJson(
  _ChatPrivacySettings instance,
) => <String, dynamic>{
  'id': instance.id,
  'blockNonFollowers': instance.blockNonFollowers,
  'autoDeleteAfterReading': instance.autoDeleteAfterReading,
  'mutedConversationIds': instance.mutedConversationIds,
  'blockedUserIds': instance.blockedUserIds,
  'allowGroupChats': instance.allowGroupChats,
  'allowVoiceMessages': instance.allowVoiceMessages,
  'allowVideoCalls': instance.allowVideoCalls,
};
