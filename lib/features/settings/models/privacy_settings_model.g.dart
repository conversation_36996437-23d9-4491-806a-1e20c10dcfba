// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'privacy_settings_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_PrivacySettings _$PrivacySettingsFromJson(Map<String, dynamic> json) =>
    _PrivacySettings(
      isPrivate: json['isPrivate'] as bool,
      blockedAccountIds: (json['blockedAccountIds'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      hideFollowersList: json['hideFollowersList'] as bool,
      hideFollowingList: json['hideFollowingList'] as bool,
      hideFromSearch: json['hideFromSearch'] as bool,
      hideFromSuggestions: json['hideFromSuggestions'] as bool,
      hideFromExplore: json['hideFromExplore'] as bool,
      allowExistingFollowersToSeeProfile:
          json['allowExistingFollowersToSeeProfile'] as bool,
      discoverableByPhoto: json['discoverableByPhoto'] as bool,
      privacyLevel: json['privacyLevel'] as String? ?? 'public',
      temporarilyHidden: json['temporarilyHidden'] as bool? ?? false,
      enableProfileViewHistory:
          json['enableProfileViewHistory'] as bool? ?? false,
    );

Map<String, dynamic> _$PrivacySettingsToJson(_PrivacySettings instance) =>
    <String, dynamic>{
      'isPrivate': instance.isPrivate,
      'blockedAccountIds': instance.blockedAccountIds,
      'hideFollowersList': instance.hideFollowersList,
      'hideFollowingList': instance.hideFollowingList,
      'hideFromSearch': instance.hideFromSearch,
      'hideFromSuggestions': instance.hideFromSuggestions,
      'hideFromExplore': instance.hideFromExplore,
      'allowExistingFollowersToSeeProfile':
          instance.allowExistingFollowersToSeeProfile,
      'discoverableByPhoto': instance.discoverableByPhoto,
      'privacyLevel': instance.privacyLevel,
      'temporarilyHidden': instance.temporarilyHidden,
      'enableProfileViewHistory': instance.enableProfileViewHistory,
    };
