import 'package:freezed_annotation/freezed_annotation.dart';

part 'privacy_settings_model.freezed.dart';
part 'privacy_settings_model.g.dart';

@freezed
abstract class PrivacySettings with _$PrivacySettings {
  const factory PrivacySettings({
    required bool isPrivate,
    required List<String> blockedAccountIds,
    // Account Visibility Features
    required bool hideFollowersList,
    required bool hideFollowingList,
    required bool hideFromSearch,
    required bool hideFromSuggestions,
    required bool hideFromExplore,
    required bool allowExistingFollowersToSeeProfile,
    // Controls whether the user can be found by photo search
    required bool discoverableByPhoto,
    // Advanced privacy controls
    @Default('public') String privacyLevel,
    @Default(false) bool temporarilyHidden,
    @Default(false) bool enableProfileViewHistory,
  }) = _PrivacySettings;

  factory PrivacySettings.fromJson(Map<String, dynamic> json) =>
      _$PrivacySettingsFromJson(json);
}
