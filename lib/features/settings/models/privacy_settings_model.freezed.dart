// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'privacy_settings_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PrivacySettings {

 bool get isPrivate; List<String> get blockedAccountIds;// Account Visibility Features
 bool get hideFollowersList; bool get hideFollowingList; bool get hideFromSearch; bool get hideFromSuggestions; bool get hideFromExplore; bool get allowExistingFollowersToSeeProfile;// Controls whether the user can be found by photo search
 bool get discoverableByPhoto;// Advanced privacy controls
 String get privacyLevel; bool get temporarilyHidden; bool get enableProfileViewHistory;
/// Create a copy of PrivacySettings
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PrivacySettingsCopyWith<PrivacySettings> get copyWith => _$PrivacySettingsCopyWithImpl<PrivacySettings>(this as PrivacySettings, _$identity);

  /// Serializes this PrivacySettings to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PrivacySettings&&(identical(other.isPrivate, isPrivate) || other.isPrivate == isPrivate)&&const DeepCollectionEquality().equals(other.blockedAccountIds, blockedAccountIds)&&(identical(other.hideFollowersList, hideFollowersList) || other.hideFollowersList == hideFollowersList)&&(identical(other.hideFollowingList, hideFollowingList) || other.hideFollowingList == hideFollowingList)&&(identical(other.hideFromSearch, hideFromSearch) || other.hideFromSearch == hideFromSearch)&&(identical(other.hideFromSuggestions, hideFromSuggestions) || other.hideFromSuggestions == hideFromSuggestions)&&(identical(other.hideFromExplore, hideFromExplore) || other.hideFromExplore == hideFromExplore)&&(identical(other.allowExistingFollowersToSeeProfile, allowExistingFollowersToSeeProfile) || other.allowExistingFollowersToSeeProfile == allowExistingFollowersToSeeProfile)&&(identical(other.discoverableByPhoto, discoverableByPhoto) || other.discoverableByPhoto == discoverableByPhoto)&&(identical(other.privacyLevel, privacyLevel) || other.privacyLevel == privacyLevel)&&(identical(other.temporarilyHidden, temporarilyHidden) || other.temporarilyHidden == temporarilyHidden)&&(identical(other.enableProfileViewHistory, enableProfileViewHistory) || other.enableProfileViewHistory == enableProfileViewHistory));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,isPrivate,const DeepCollectionEquality().hash(blockedAccountIds),hideFollowersList,hideFollowingList,hideFromSearch,hideFromSuggestions,hideFromExplore,allowExistingFollowersToSeeProfile,discoverableByPhoto,privacyLevel,temporarilyHidden,enableProfileViewHistory);

@override
String toString() {
  return 'PrivacySettings(isPrivate: $isPrivate, blockedAccountIds: $blockedAccountIds, hideFollowersList: $hideFollowersList, hideFollowingList: $hideFollowingList, hideFromSearch: $hideFromSearch, hideFromSuggestions: $hideFromSuggestions, hideFromExplore: $hideFromExplore, allowExistingFollowersToSeeProfile: $allowExistingFollowersToSeeProfile, discoverableByPhoto: $discoverableByPhoto, privacyLevel: $privacyLevel, temporarilyHidden: $temporarilyHidden, enableProfileViewHistory: $enableProfileViewHistory)';
}


}

/// @nodoc
abstract mixin class $PrivacySettingsCopyWith<$Res>  {
  factory $PrivacySettingsCopyWith(PrivacySettings value, $Res Function(PrivacySettings) _then) = _$PrivacySettingsCopyWithImpl;
@useResult
$Res call({
 bool isPrivate, List<String> blockedAccountIds, bool hideFollowersList, bool hideFollowingList, bool hideFromSearch, bool hideFromSuggestions, bool hideFromExplore, bool allowExistingFollowersToSeeProfile, bool discoverableByPhoto, String privacyLevel, bool temporarilyHidden, bool enableProfileViewHistory
});




}
/// @nodoc
class _$PrivacySettingsCopyWithImpl<$Res>
    implements $PrivacySettingsCopyWith<$Res> {
  _$PrivacySettingsCopyWithImpl(this._self, this._then);

  final PrivacySettings _self;
  final $Res Function(PrivacySettings) _then;

/// Create a copy of PrivacySettings
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? isPrivate = null,Object? blockedAccountIds = null,Object? hideFollowersList = null,Object? hideFollowingList = null,Object? hideFromSearch = null,Object? hideFromSuggestions = null,Object? hideFromExplore = null,Object? allowExistingFollowersToSeeProfile = null,Object? discoverableByPhoto = null,Object? privacyLevel = null,Object? temporarilyHidden = null,Object? enableProfileViewHistory = null,}) {
  return _then(_self.copyWith(
isPrivate: null == isPrivate ? _self.isPrivate : isPrivate // ignore: cast_nullable_to_non_nullable
as bool,blockedAccountIds: null == blockedAccountIds ? _self.blockedAccountIds : blockedAccountIds // ignore: cast_nullable_to_non_nullable
as List<String>,hideFollowersList: null == hideFollowersList ? _self.hideFollowersList : hideFollowersList // ignore: cast_nullable_to_non_nullable
as bool,hideFollowingList: null == hideFollowingList ? _self.hideFollowingList : hideFollowingList // ignore: cast_nullable_to_non_nullable
as bool,hideFromSearch: null == hideFromSearch ? _self.hideFromSearch : hideFromSearch // ignore: cast_nullable_to_non_nullable
as bool,hideFromSuggestions: null == hideFromSuggestions ? _self.hideFromSuggestions : hideFromSuggestions // ignore: cast_nullable_to_non_nullable
as bool,hideFromExplore: null == hideFromExplore ? _self.hideFromExplore : hideFromExplore // ignore: cast_nullable_to_non_nullable
as bool,allowExistingFollowersToSeeProfile: null == allowExistingFollowersToSeeProfile ? _self.allowExistingFollowersToSeeProfile : allowExistingFollowersToSeeProfile // ignore: cast_nullable_to_non_nullable
as bool,discoverableByPhoto: null == discoverableByPhoto ? _self.discoverableByPhoto : discoverableByPhoto // ignore: cast_nullable_to_non_nullable
as bool,privacyLevel: null == privacyLevel ? _self.privacyLevel : privacyLevel // ignore: cast_nullable_to_non_nullable
as String,temporarilyHidden: null == temporarilyHidden ? _self.temporarilyHidden : temporarilyHidden // ignore: cast_nullable_to_non_nullable
as bool,enableProfileViewHistory: null == enableProfileViewHistory ? _self.enableProfileViewHistory : enableProfileViewHistory // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [PrivacySettings].
extension PrivacySettingsPatterns on PrivacySettings {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PrivacySettings value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PrivacySettings() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PrivacySettings value)  $default,){
final _that = this;
switch (_that) {
case _PrivacySettings():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PrivacySettings value)?  $default,){
final _that = this;
switch (_that) {
case _PrivacySettings() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( bool isPrivate,  List<String> blockedAccountIds,  bool hideFollowersList,  bool hideFollowingList,  bool hideFromSearch,  bool hideFromSuggestions,  bool hideFromExplore,  bool allowExistingFollowersToSeeProfile,  bool discoverableByPhoto,  String privacyLevel,  bool temporarilyHidden,  bool enableProfileViewHistory)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PrivacySettings() when $default != null:
return $default(_that.isPrivate,_that.blockedAccountIds,_that.hideFollowersList,_that.hideFollowingList,_that.hideFromSearch,_that.hideFromSuggestions,_that.hideFromExplore,_that.allowExistingFollowersToSeeProfile,_that.discoverableByPhoto,_that.privacyLevel,_that.temporarilyHidden,_that.enableProfileViewHistory);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( bool isPrivate,  List<String> blockedAccountIds,  bool hideFollowersList,  bool hideFollowingList,  bool hideFromSearch,  bool hideFromSuggestions,  bool hideFromExplore,  bool allowExistingFollowersToSeeProfile,  bool discoverableByPhoto,  String privacyLevel,  bool temporarilyHidden,  bool enableProfileViewHistory)  $default,) {final _that = this;
switch (_that) {
case _PrivacySettings():
return $default(_that.isPrivate,_that.blockedAccountIds,_that.hideFollowersList,_that.hideFollowingList,_that.hideFromSearch,_that.hideFromSuggestions,_that.hideFromExplore,_that.allowExistingFollowersToSeeProfile,_that.discoverableByPhoto,_that.privacyLevel,_that.temporarilyHidden,_that.enableProfileViewHistory);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( bool isPrivate,  List<String> blockedAccountIds,  bool hideFollowersList,  bool hideFollowingList,  bool hideFromSearch,  bool hideFromSuggestions,  bool hideFromExplore,  bool allowExistingFollowersToSeeProfile,  bool discoverableByPhoto,  String privacyLevel,  bool temporarilyHidden,  bool enableProfileViewHistory)?  $default,) {final _that = this;
switch (_that) {
case _PrivacySettings() when $default != null:
return $default(_that.isPrivate,_that.blockedAccountIds,_that.hideFollowersList,_that.hideFollowingList,_that.hideFromSearch,_that.hideFromSuggestions,_that.hideFromExplore,_that.allowExistingFollowersToSeeProfile,_that.discoverableByPhoto,_that.privacyLevel,_that.temporarilyHidden,_that.enableProfileViewHistory);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PrivacySettings implements PrivacySettings {
  const _PrivacySettings({required this.isPrivate, required final  List<String> blockedAccountIds, required this.hideFollowersList, required this.hideFollowingList, required this.hideFromSearch, required this.hideFromSuggestions, required this.hideFromExplore, required this.allowExistingFollowersToSeeProfile, required this.discoverableByPhoto, this.privacyLevel = 'public', this.temporarilyHidden = false, this.enableProfileViewHistory = false}): _blockedAccountIds = blockedAccountIds;
  factory _PrivacySettings.fromJson(Map<String, dynamic> json) => _$PrivacySettingsFromJson(json);

@override final  bool isPrivate;
 final  List<String> _blockedAccountIds;
@override List<String> get blockedAccountIds {
  if (_blockedAccountIds is EqualUnmodifiableListView) return _blockedAccountIds;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_blockedAccountIds);
}

// Account Visibility Features
@override final  bool hideFollowersList;
@override final  bool hideFollowingList;
@override final  bool hideFromSearch;
@override final  bool hideFromSuggestions;
@override final  bool hideFromExplore;
@override final  bool allowExistingFollowersToSeeProfile;
// Controls whether the user can be found by photo search
@override final  bool discoverableByPhoto;
// Advanced privacy controls
@override@JsonKey() final  String privacyLevel;
@override@JsonKey() final  bool temporarilyHidden;
@override@JsonKey() final  bool enableProfileViewHistory;

/// Create a copy of PrivacySettings
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PrivacySettingsCopyWith<_PrivacySettings> get copyWith => __$PrivacySettingsCopyWithImpl<_PrivacySettings>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PrivacySettingsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PrivacySettings&&(identical(other.isPrivate, isPrivate) || other.isPrivate == isPrivate)&&const DeepCollectionEquality().equals(other._blockedAccountIds, _blockedAccountIds)&&(identical(other.hideFollowersList, hideFollowersList) || other.hideFollowersList == hideFollowersList)&&(identical(other.hideFollowingList, hideFollowingList) || other.hideFollowingList == hideFollowingList)&&(identical(other.hideFromSearch, hideFromSearch) || other.hideFromSearch == hideFromSearch)&&(identical(other.hideFromSuggestions, hideFromSuggestions) || other.hideFromSuggestions == hideFromSuggestions)&&(identical(other.hideFromExplore, hideFromExplore) || other.hideFromExplore == hideFromExplore)&&(identical(other.allowExistingFollowersToSeeProfile, allowExistingFollowersToSeeProfile) || other.allowExistingFollowersToSeeProfile == allowExistingFollowersToSeeProfile)&&(identical(other.discoverableByPhoto, discoverableByPhoto) || other.discoverableByPhoto == discoverableByPhoto)&&(identical(other.privacyLevel, privacyLevel) || other.privacyLevel == privacyLevel)&&(identical(other.temporarilyHidden, temporarilyHidden) || other.temporarilyHidden == temporarilyHidden)&&(identical(other.enableProfileViewHistory, enableProfileViewHistory) || other.enableProfileViewHistory == enableProfileViewHistory));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,isPrivate,const DeepCollectionEquality().hash(_blockedAccountIds),hideFollowersList,hideFollowingList,hideFromSearch,hideFromSuggestions,hideFromExplore,allowExistingFollowersToSeeProfile,discoverableByPhoto,privacyLevel,temporarilyHidden,enableProfileViewHistory);

@override
String toString() {
  return 'PrivacySettings(isPrivate: $isPrivate, blockedAccountIds: $blockedAccountIds, hideFollowersList: $hideFollowersList, hideFollowingList: $hideFollowingList, hideFromSearch: $hideFromSearch, hideFromSuggestions: $hideFromSuggestions, hideFromExplore: $hideFromExplore, allowExistingFollowersToSeeProfile: $allowExistingFollowersToSeeProfile, discoverableByPhoto: $discoverableByPhoto, privacyLevel: $privacyLevel, temporarilyHidden: $temporarilyHidden, enableProfileViewHistory: $enableProfileViewHistory)';
}


}

/// @nodoc
abstract mixin class _$PrivacySettingsCopyWith<$Res> implements $PrivacySettingsCopyWith<$Res> {
  factory _$PrivacySettingsCopyWith(_PrivacySettings value, $Res Function(_PrivacySettings) _then) = __$PrivacySettingsCopyWithImpl;
@override @useResult
$Res call({
 bool isPrivate, List<String> blockedAccountIds, bool hideFollowersList, bool hideFollowingList, bool hideFromSearch, bool hideFromSuggestions, bool hideFromExplore, bool allowExistingFollowersToSeeProfile, bool discoverableByPhoto, String privacyLevel, bool temporarilyHidden, bool enableProfileViewHistory
});




}
/// @nodoc
class __$PrivacySettingsCopyWithImpl<$Res>
    implements _$PrivacySettingsCopyWith<$Res> {
  __$PrivacySettingsCopyWithImpl(this._self, this._then);

  final _PrivacySettings _self;
  final $Res Function(_PrivacySettings) _then;

/// Create a copy of PrivacySettings
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? isPrivate = null,Object? blockedAccountIds = null,Object? hideFollowersList = null,Object? hideFollowingList = null,Object? hideFromSearch = null,Object? hideFromSuggestions = null,Object? hideFromExplore = null,Object? allowExistingFollowersToSeeProfile = null,Object? discoverableByPhoto = null,Object? privacyLevel = null,Object? temporarilyHidden = null,Object? enableProfileViewHistory = null,}) {
  return _then(_PrivacySettings(
isPrivate: null == isPrivate ? _self.isPrivate : isPrivate // ignore: cast_nullable_to_non_nullable
as bool,blockedAccountIds: null == blockedAccountIds ? _self._blockedAccountIds : blockedAccountIds // ignore: cast_nullable_to_non_nullable
as List<String>,hideFollowersList: null == hideFollowersList ? _self.hideFollowersList : hideFollowersList // ignore: cast_nullable_to_non_nullable
as bool,hideFollowingList: null == hideFollowingList ? _self.hideFollowingList : hideFollowingList // ignore: cast_nullable_to_non_nullable
as bool,hideFromSearch: null == hideFromSearch ? _self.hideFromSearch : hideFromSearch // ignore: cast_nullable_to_non_nullable
as bool,hideFromSuggestions: null == hideFromSuggestions ? _self.hideFromSuggestions : hideFromSuggestions // ignore: cast_nullable_to_non_nullable
as bool,hideFromExplore: null == hideFromExplore ? _self.hideFromExplore : hideFromExplore // ignore: cast_nullable_to_non_nullable
as bool,allowExistingFollowersToSeeProfile: null == allowExistingFollowersToSeeProfile ? _self.allowExistingFollowersToSeeProfile : allowExistingFollowersToSeeProfile // ignore: cast_nullable_to_non_nullable
as bool,discoverableByPhoto: null == discoverableByPhoto ? _self.discoverableByPhoto : discoverableByPhoto // ignore: cast_nullable_to_non_nullable
as bool,privacyLevel: null == privacyLevel ? _self.privacyLevel : privacyLevel // ignore: cast_nullable_to_non_nullable
as String,temporarilyHidden: null == temporarilyHidden ? _self.temporarilyHidden : temporarilyHidden // ignore: cast_nullable_to_non_nullable
as bool,enableProfileViewHistory: null == enableProfileViewHistory ? _self.enableProfileViewHistory : enableProfileViewHistory // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
