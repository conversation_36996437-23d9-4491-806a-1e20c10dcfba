// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'chat_privacy_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ChatPrivacySettings {

 String get id; bool get blockNonFollowers; bool get autoDeleteAfterReading; List<String> get mutedConversationIds; List<String> get blockedUserIds; bool get allowGroupChats; bool get allowVoiceMessages; bool get allowVideoCalls;
/// Create a copy of ChatPrivacySettings
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ChatPrivacySettingsCopyWith<ChatPrivacySettings> get copyWith => _$ChatPrivacySettingsCopyWithImpl<ChatPrivacySettings>(this as ChatPrivacySettings, _$identity);

  /// Serializes this ChatPrivacySettings to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChatPrivacySettings&&(identical(other.id, id) || other.id == id)&&(identical(other.blockNonFollowers, blockNonFollowers) || other.blockNonFollowers == blockNonFollowers)&&(identical(other.autoDeleteAfterReading, autoDeleteAfterReading) || other.autoDeleteAfterReading == autoDeleteAfterReading)&&const DeepCollectionEquality().equals(other.mutedConversationIds, mutedConversationIds)&&const DeepCollectionEquality().equals(other.blockedUserIds, blockedUserIds)&&(identical(other.allowGroupChats, allowGroupChats) || other.allowGroupChats == allowGroupChats)&&(identical(other.allowVoiceMessages, allowVoiceMessages) || other.allowVoiceMessages == allowVoiceMessages)&&(identical(other.allowVideoCalls, allowVideoCalls) || other.allowVideoCalls == allowVideoCalls));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,blockNonFollowers,autoDeleteAfterReading,const DeepCollectionEquality().hash(mutedConversationIds),const DeepCollectionEquality().hash(blockedUserIds),allowGroupChats,allowVoiceMessages,allowVideoCalls);

@override
String toString() {
  return 'ChatPrivacySettings(id: $id, blockNonFollowers: $blockNonFollowers, autoDeleteAfterReading: $autoDeleteAfterReading, mutedConversationIds: $mutedConversationIds, blockedUserIds: $blockedUserIds, allowGroupChats: $allowGroupChats, allowVoiceMessages: $allowVoiceMessages, allowVideoCalls: $allowVideoCalls)';
}


}

/// @nodoc
abstract mixin class $ChatPrivacySettingsCopyWith<$Res>  {
  factory $ChatPrivacySettingsCopyWith(ChatPrivacySettings value, $Res Function(ChatPrivacySettings) _then) = _$ChatPrivacySettingsCopyWithImpl;
@useResult
$Res call({
 String id, bool blockNonFollowers, bool autoDeleteAfterReading, List<String> mutedConversationIds, List<String> blockedUserIds, bool allowGroupChats, bool allowVoiceMessages, bool allowVideoCalls
});




}
/// @nodoc
class _$ChatPrivacySettingsCopyWithImpl<$Res>
    implements $ChatPrivacySettingsCopyWith<$Res> {
  _$ChatPrivacySettingsCopyWithImpl(this._self, this._then);

  final ChatPrivacySettings _self;
  final $Res Function(ChatPrivacySettings) _then;

/// Create a copy of ChatPrivacySettings
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? blockNonFollowers = null,Object? autoDeleteAfterReading = null,Object? mutedConversationIds = null,Object? blockedUserIds = null,Object? allowGroupChats = null,Object? allowVoiceMessages = null,Object? allowVideoCalls = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,blockNonFollowers: null == blockNonFollowers ? _self.blockNonFollowers : blockNonFollowers // ignore: cast_nullable_to_non_nullable
as bool,autoDeleteAfterReading: null == autoDeleteAfterReading ? _self.autoDeleteAfterReading : autoDeleteAfterReading // ignore: cast_nullable_to_non_nullable
as bool,mutedConversationIds: null == mutedConversationIds ? _self.mutedConversationIds : mutedConversationIds // ignore: cast_nullable_to_non_nullable
as List<String>,blockedUserIds: null == blockedUserIds ? _self.blockedUserIds : blockedUserIds // ignore: cast_nullable_to_non_nullable
as List<String>,allowGroupChats: null == allowGroupChats ? _self.allowGroupChats : allowGroupChats // ignore: cast_nullable_to_non_nullable
as bool,allowVoiceMessages: null == allowVoiceMessages ? _self.allowVoiceMessages : allowVoiceMessages // ignore: cast_nullable_to_non_nullable
as bool,allowVideoCalls: null == allowVideoCalls ? _self.allowVideoCalls : allowVideoCalls // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [ChatPrivacySettings].
extension ChatPrivacySettingsPatterns on ChatPrivacySettings {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ChatPrivacySettings value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ChatPrivacySettings() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ChatPrivacySettings value)  $default,){
final _that = this;
switch (_that) {
case _ChatPrivacySettings():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ChatPrivacySettings value)?  $default,){
final _that = this;
switch (_that) {
case _ChatPrivacySettings() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  bool blockNonFollowers,  bool autoDeleteAfterReading,  List<String> mutedConversationIds,  List<String> blockedUserIds,  bool allowGroupChats,  bool allowVoiceMessages,  bool allowVideoCalls)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ChatPrivacySettings() when $default != null:
return $default(_that.id,_that.blockNonFollowers,_that.autoDeleteAfterReading,_that.mutedConversationIds,_that.blockedUserIds,_that.allowGroupChats,_that.allowVoiceMessages,_that.allowVideoCalls);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  bool blockNonFollowers,  bool autoDeleteAfterReading,  List<String> mutedConversationIds,  List<String> blockedUserIds,  bool allowGroupChats,  bool allowVoiceMessages,  bool allowVideoCalls)  $default,) {final _that = this;
switch (_that) {
case _ChatPrivacySettings():
return $default(_that.id,_that.blockNonFollowers,_that.autoDeleteAfterReading,_that.mutedConversationIds,_that.blockedUserIds,_that.allowGroupChats,_that.allowVoiceMessages,_that.allowVideoCalls);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  bool blockNonFollowers,  bool autoDeleteAfterReading,  List<String> mutedConversationIds,  List<String> blockedUserIds,  bool allowGroupChats,  bool allowVoiceMessages,  bool allowVideoCalls)?  $default,) {final _that = this;
switch (_that) {
case _ChatPrivacySettings() when $default != null:
return $default(_that.id,_that.blockNonFollowers,_that.autoDeleteAfterReading,_that.mutedConversationIds,_that.blockedUserIds,_that.allowGroupChats,_that.allowVoiceMessages,_that.allowVideoCalls);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ChatPrivacySettings implements ChatPrivacySettings {
  const _ChatPrivacySettings({required this.id, required this.blockNonFollowers, required this.autoDeleteAfterReading, required final  List<String> mutedConversationIds, required final  List<String> blockedUserIds, required this.allowGroupChats, required this.allowVoiceMessages, required this.allowVideoCalls}): _mutedConversationIds = mutedConversationIds,_blockedUserIds = blockedUserIds;
  factory _ChatPrivacySettings.fromJson(Map<String, dynamic> json) => _$ChatPrivacySettingsFromJson(json);

@override final  String id;
@override final  bool blockNonFollowers;
@override final  bool autoDeleteAfterReading;
 final  List<String> _mutedConversationIds;
@override List<String> get mutedConversationIds {
  if (_mutedConversationIds is EqualUnmodifiableListView) return _mutedConversationIds;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_mutedConversationIds);
}

 final  List<String> _blockedUserIds;
@override List<String> get blockedUserIds {
  if (_blockedUserIds is EqualUnmodifiableListView) return _blockedUserIds;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_blockedUserIds);
}

@override final  bool allowGroupChats;
@override final  bool allowVoiceMessages;
@override final  bool allowVideoCalls;

/// Create a copy of ChatPrivacySettings
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChatPrivacySettingsCopyWith<_ChatPrivacySettings> get copyWith => __$ChatPrivacySettingsCopyWithImpl<_ChatPrivacySettings>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ChatPrivacySettingsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChatPrivacySettings&&(identical(other.id, id) || other.id == id)&&(identical(other.blockNonFollowers, blockNonFollowers) || other.blockNonFollowers == blockNonFollowers)&&(identical(other.autoDeleteAfterReading, autoDeleteAfterReading) || other.autoDeleteAfterReading == autoDeleteAfterReading)&&const DeepCollectionEquality().equals(other._mutedConversationIds, _mutedConversationIds)&&const DeepCollectionEquality().equals(other._blockedUserIds, _blockedUserIds)&&(identical(other.allowGroupChats, allowGroupChats) || other.allowGroupChats == allowGroupChats)&&(identical(other.allowVoiceMessages, allowVoiceMessages) || other.allowVoiceMessages == allowVoiceMessages)&&(identical(other.allowVideoCalls, allowVideoCalls) || other.allowVideoCalls == allowVideoCalls));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,blockNonFollowers,autoDeleteAfterReading,const DeepCollectionEquality().hash(_mutedConversationIds),const DeepCollectionEquality().hash(_blockedUserIds),allowGroupChats,allowVoiceMessages,allowVideoCalls);

@override
String toString() {
  return 'ChatPrivacySettings(id: $id, blockNonFollowers: $blockNonFollowers, autoDeleteAfterReading: $autoDeleteAfterReading, mutedConversationIds: $mutedConversationIds, blockedUserIds: $blockedUserIds, allowGroupChats: $allowGroupChats, allowVoiceMessages: $allowVoiceMessages, allowVideoCalls: $allowVideoCalls)';
}


}

/// @nodoc
abstract mixin class _$ChatPrivacySettingsCopyWith<$Res> implements $ChatPrivacySettingsCopyWith<$Res> {
  factory _$ChatPrivacySettingsCopyWith(_ChatPrivacySettings value, $Res Function(_ChatPrivacySettings) _then) = __$ChatPrivacySettingsCopyWithImpl;
@override @useResult
$Res call({
 String id, bool blockNonFollowers, bool autoDeleteAfterReading, List<String> mutedConversationIds, List<String> blockedUserIds, bool allowGroupChats, bool allowVoiceMessages, bool allowVideoCalls
});




}
/// @nodoc
class __$ChatPrivacySettingsCopyWithImpl<$Res>
    implements _$ChatPrivacySettingsCopyWith<$Res> {
  __$ChatPrivacySettingsCopyWithImpl(this._self, this._then);

  final _ChatPrivacySettings _self;
  final $Res Function(_ChatPrivacySettings) _then;

/// Create a copy of ChatPrivacySettings
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? blockNonFollowers = null,Object? autoDeleteAfterReading = null,Object? mutedConversationIds = null,Object? blockedUserIds = null,Object? allowGroupChats = null,Object? allowVoiceMessages = null,Object? allowVideoCalls = null,}) {
  return _then(_ChatPrivacySettings(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,blockNonFollowers: null == blockNonFollowers ? _self.blockNonFollowers : blockNonFollowers // ignore: cast_nullable_to_non_nullable
as bool,autoDeleteAfterReading: null == autoDeleteAfterReading ? _self.autoDeleteAfterReading : autoDeleteAfterReading // ignore: cast_nullable_to_non_nullable
as bool,mutedConversationIds: null == mutedConversationIds ? _self._mutedConversationIds : mutedConversationIds // ignore: cast_nullable_to_non_nullable
as List<String>,blockedUserIds: null == blockedUserIds ? _self._blockedUserIds : blockedUserIds // ignore: cast_nullable_to_non_nullable
as List<String>,allowGroupChats: null == allowGroupChats ? _self.allowGroupChats : allowGroupChats // ignore: cast_nullable_to_non_nullable
as bool,allowVoiceMessages: null == allowVoiceMessages ? _self.allowVoiceMessages : allowVoiceMessages // ignore: cast_nullable_to_non_nullable
as bool,allowVideoCalls: null == allowVideoCalls ? _self.allowVideoCalls : allowVideoCalls // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
