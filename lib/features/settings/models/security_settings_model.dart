import 'package:freezed_annotation/freezed_annotation.dart';

part 'security_settings_model.freezed.dart';
part 'security_settings_model.g.dart';

@freezed
abstract class SecuritySettings with _$SecuritySettings {
  const factory SecuritySettings({
    required bool isTwoFactorEnabled,
    required bool isBiometricLoginEnabled,
  }) = _SecuritySettings;

  factory SecuritySettings.fromJson(Map<String, dynamic> json) =>
      _$SecuritySettingsFromJson(json);
}
