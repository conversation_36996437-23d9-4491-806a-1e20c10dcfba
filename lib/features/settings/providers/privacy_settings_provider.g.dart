// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'privacy_settings_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$privacySettingsNotifierHash() =>
    r'461e8984de74778c51cca66ff52d49e2452ba5ad';

/// See also [PrivacySettingsNotifier].
@ProviderFor(PrivacySettingsNotifier)
final privacySettingsNotifierProvider =
    AutoDisposeAsyncNotifierProvider<
      PrivacySettingsNotifier,
      PrivacySettings
    >.internal(
      PrivacySettingsNotifier.new,
      name: r'privacySettingsNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$privacySettingsNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$PrivacySettingsNotifier = AutoDisposeAsyncNotifier<PrivacySettings>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
