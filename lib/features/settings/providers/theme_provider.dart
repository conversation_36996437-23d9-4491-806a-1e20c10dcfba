import 'package:billionaires_social/core/app_themes.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/features/settings/services/theme_service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';

final themeProvider = StateNotifierProvider<ThemeNotifier, AppThemeType>((ref) {
  debugPrint('[ThemeProvider] Using GetIt instance: \\${getIt.hashCode}');
  final themeService = getIt<ThemeService>();
  return ThemeNotifier(themeService);
});

class ThemeNotifier extends StateNotifier<AppThemeType> {
  final ThemeService _themeService;

  ThemeNotifier(this._themeService) : super(AppThemeType.luxuryWhiteGold) {
    // Always use luxuryWhiteGold theme (white app bars)
    state = AppThemeType.luxuryWhiteGold;
  }

  Future<void> setTheme(AppThemeType theme) async {
    if (state == theme) return;
    state = theme;
    await _themeService.setTheme(theme);
  }
}
