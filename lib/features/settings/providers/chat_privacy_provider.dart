import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/features/settings/models/chat_privacy_model.dart';
import 'package:billionaires_social/features/settings/services/chat_privacy_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'chat_privacy_provider.g.dart';

@riverpod
class ChatPrivacyNotifier extends _$ChatPrivacyNotifier {
  late final ChatPrivacyService _service;

  @override
  Future<ChatPrivacySettings> build() async {
    _service = getIt<ChatPrivacyService>();
    return _service.getSettings();
  }

  Future<void> updateSettings(ChatPrivacySettings newSettings) async {
    state = AsyncValue.data(newSettings);
    await _service.updateSettings(newSettings);
  }
}
