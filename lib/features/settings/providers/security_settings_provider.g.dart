// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'security_settings_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$securitySettingsNotifierHash() =>
    r'08f8834a906e872b8d8237804d74cf006f1db8d5';

/// See also [SecuritySettingsNotifier].
@ProviderFor(SecuritySettingsNotifier)
final securitySettingsNotifierProvider =
    AutoDisposeAsyncNotifierProvider<
      SecuritySettingsNotifier,
      SecuritySettings
    >.internal(
      SecuritySettingsNotifier.new,
      name: r'securitySettingsNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$securitySettingsNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SecuritySettingsNotifier = AutoDisposeAsyncNotifier<SecuritySettings>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
