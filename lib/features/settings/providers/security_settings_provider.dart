import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/features/settings/models/security_settings_model.dart';
import 'package:billionaires_social/features/settings/services/settings_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'security_settings_provider.g.dart';

@riverpod
class SecuritySettingsNotifier extends _$SecuritySettingsNotifier {
  @override
  Future<SecuritySettings> build() {
    final settingsService = getIt<SettingsService>();
    return settingsService.getSecuritySettings();
  }

  Future<void> setTwoFactor(bool isEnabled) async {
    await _updateSettings(state.value!.copyWith(isTwoFactorEnabled: isEnabled));
  }

  Future<void> setBiometricLogin(bool isEnabled) async {
    await _updateSettings(
      state.value!.copyWith(isBiometricLoginEnabled: isEnabled),
    );
  }

  Future<void> _updateSettings(SecuritySettings newSettings) async {
    final settingsService = getIt<SettingsService>();
    final currentState = state.value;

    state = AsyncValue.data(newSettings);

    try {
      await settingsService.saveSecuritySettings(newSettings);
    } catch (e) {
      state = AsyncValue.data(currentState!);
      rethrow;
    }
  }
}
