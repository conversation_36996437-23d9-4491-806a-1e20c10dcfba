import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/features/settings/models/privacy_settings_model.dart';
import 'package:billionaires_social/features/settings/services/settings_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'privacy_settings_provider.g.dart';

@riverpod
class PrivacySettingsNotifier extends _$PrivacySettingsNotifier {
  @override
  Future<PrivacySettings> build() {
    final settingsService = getIt<SettingsService>();
    return settingsService.getPrivacySettings();
  }

  Future<void> setAccountPrivate(bool isPrivate) async {
    final settingsService = getIt<SettingsService>();
    final currentState = state.valueOrNull;
    if (currentState == null) return;

    // Optimistically update the UI
    final newSettings = currentState.copyWith(isPrivate: isPrivate);
    state = AsyncValue.data(newSettings);

    // Persist the change
    try {
      await settingsService.savePrivacySettings(newSettings);
    } catch (e) {
      // If saving fails, revert the state
      state = AsyncValue.data(currentState);
      // Optionally, rethrow or handle the error
      rethrow;
    }
  }

  Future<void> setHideFollowersList(bool hideFollowersList) async {
    final settingsService = getIt<SettingsService>();
    final currentState = state.valueOrNull;
    if (currentState == null) return;

    final newSettings = currentState.copyWith(
      hideFollowersList: hideFollowersList,
    );
    state = AsyncValue.data(newSettings);

    try {
      await settingsService.savePrivacySettings(newSettings);
    } catch (e) {
      state = AsyncValue.data(currentState);
      rethrow;
    }
  }

  Future<void> setHideFollowingList(bool hideFollowingList) async {
    final settingsService = getIt<SettingsService>();
    final currentState = state.valueOrNull;
    if (currentState == null) return;

    final newSettings = currentState.copyWith(
      hideFollowingList: hideFollowingList,
    );
    state = AsyncValue.data(newSettings);

    try {
      await settingsService.savePrivacySettings(newSettings);
    } catch (e) {
      state = AsyncValue.data(currentState);
      rethrow;
    }
  }

  Future<void> setHideFromSearch(bool hideFromSearch) async {
    final settingsService = getIt<SettingsService>();
    final currentState = state.valueOrNull;
    if (currentState == null) return;

    final newSettings = currentState.copyWith(hideFromSearch: hideFromSearch);
    state = AsyncValue.data(newSettings);

    try {
      await settingsService.savePrivacySettings(newSettings);
    } catch (e) {
      state = AsyncValue.data(currentState);
      rethrow;
    }
  }

  Future<void> setHideFromSuggestions(bool hideFromSuggestions) async {
    final settingsService = getIt<SettingsService>();
    final currentState = state.valueOrNull;
    if (currentState == null) return;

    final newSettings = currentState.copyWith(
      hideFromSuggestions: hideFromSuggestions,
    );
    state = AsyncValue.data(newSettings);

    try {
      await settingsService.savePrivacySettings(newSettings);
    } catch (e) {
      state = AsyncValue.data(currentState);
      rethrow;
    }
  }

  Future<void> setHideFromExplore(bool hideFromExplore) async {
    final settingsService = getIt<SettingsService>();
    final currentState = state.valueOrNull;
    if (currentState == null) return;

    final newSettings = currentState.copyWith(hideFromExplore: hideFromExplore);
    state = AsyncValue.data(newSettings);

    try {
      await settingsService.savePrivacySettings(newSettings);
    } catch (e) {
      state = AsyncValue.data(currentState);
      rethrow;
    }
  }

  Future<void> setAllowExistingFollowersToSeeProfile(
    bool allowExistingFollowersToSeeProfile,
  ) async {
    final settingsService = getIt<SettingsService>();
    final currentState = state.valueOrNull;
    if (currentState == null) return;

    final newSettings = currentState.copyWith(
      allowExistingFollowersToSeeProfile: allowExistingFollowersToSeeProfile,
    );
    state = AsyncValue.data(newSettings);

    try {
      await settingsService.savePrivacySettings(newSettings);
    } catch (e) {
      state = AsyncValue.data(currentState);
      rethrow;
    }
  }

  Future<void> setDiscoverableByPhoto(bool discoverableByPhoto) async {
    final settingsService = getIt<SettingsService>();
    final currentState = state.valueOrNull;
    if (currentState == null) return;

    final newSettings = currentState.copyWith(
      discoverableByPhoto: discoverableByPhoto,
    );
    state = AsyncValue.data(newSettings);

    try {
      await settingsService.savePrivacySettings(newSettings);
    } catch (e) {
      state = AsyncValue.data(currentState);
      rethrow;
    }
  }

  Future<void> setPrivacyLevel(String privacyLevel) async {
    final settingsService = getIt<SettingsService>();
    final currentState = state.valueOrNull;
    if (currentState == null) return;
    final newSettings = currentState.copyWith(privacyLevel: privacyLevel);
    state = AsyncValue.data(newSettings);
    try {
      await settingsService.savePrivacySettings(newSettings);
    } catch (e) {
      state = AsyncValue.data(currentState);
      rethrow;
    }
  }

  Future<void> setTemporarilyHidden(bool temporarilyHidden) async {
    final settingsService = getIt<SettingsService>();
    final currentState = state.valueOrNull;
    if (currentState == null) return;
    final newSettings = currentState.copyWith(
      temporarilyHidden: temporarilyHidden,
    );
    state = AsyncValue.data(newSettings);
    try {
      await settingsService.savePrivacySettings(newSettings);
    } catch (e) {
      state = AsyncValue.data(currentState);
      rethrow;
    }
  }

  Future<void> setEnableProfileViewHistory(
    bool enableProfileViewHistory,
  ) async {
    final settingsService = getIt<SettingsService>();
    final currentState = state.valueOrNull;
    if (currentState == null) return;
    final newSettings = currentState.copyWith(
      enableProfileViewHistory: enableProfileViewHistory,
    );
    state = AsyncValue.data(newSettings);
    try {
      await settingsService.savePrivacySettings(newSettings);
    } catch (e) {
      state = AsyncValue.data(currentState);
      rethrow;
    }
  }
}
