// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_privacy_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$chatPrivacyNotifierHash() =>
    r'39082853e2b654167c05512ef874a5b07358797e';

/// See also [ChatPrivacyNotifier].
@ProviderFor(ChatPrivacyNotifier)
final chatPrivacyNotifierProvider =
    AutoDisposeAsyncNotifierProvider<
      ChatPrivacyNotifier,
      ChatPrivacySettings
    >.internal(
      ChatPrivacyNotifier.new,
      name: r'chatPrivacyNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$chatPrivacyNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$ChatPrivacyNotifier = AutoDisposeAsyncNotifier<ChatPrivacySettings>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
