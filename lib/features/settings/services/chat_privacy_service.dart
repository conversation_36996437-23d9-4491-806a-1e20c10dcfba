import 'package:billionaires_social/features/settings/models/chat_privacy_model.dart';

class ChatPrivacyService {
  ChatPrivacySettings? _settings;

  Future<ChatPrivacySettings> getSettings() async {
    await Future.delayed(const Duration(milliseconds: 200));
    return _settings ??= const ChatPrivacySettings(
      id: 'default',
      blockNonFollowers: false,
      autoDeleteAfterReading: false,
      mutedConversationIds: [],
      blockedUserIds: [],
      allowGroupChats: true,
      allowVoiceMessages: true,
      allowVideoCalls: true,
    );
  }

  Future<void> updateSettings(ChatPrivacySettings settings) async {
    await Future.delayed(const Duration(milliseconds: 200));
    _settings = settings;
  }
}
