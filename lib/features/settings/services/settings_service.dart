import 'package:shared_preferences/shared_preferences.dart';
import 'package:billionaires_social/features/settings/models/privacy_settings_model.dart';
import 'package:billionaires_social/features/settings/models/security_settings_model.dart';
import 'dart:convert';

class SettingsService {
  static const String _privacyKey = 'privacy_settings';
  static const String _securityKey = 'security_settings';

  // Privacy Settings
  Future<PrivacySettings> getPrivacySettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final data = prefs.getString(_privacyKey);

      if (data != null) {
        final jsonData = json.decode(data) as Map<String, dynamic>;
        return PrivacySettings.fromJson(jsonData);
      }

      return const PrivacySettings(
        isPrivate: false,
        blockedAccountIds: [],
        hideFollowersList: false,
        hideFollowingList: false,
        hideFromSearch: false,
        hideFromSuggestions: false,
        hideFromExplore: false,
        allowExistingFollowersToSeeProfile: true,
        discoverableByPhoto: false,
      ); // Default settings
    } catch (e) {
      throw Exception('Failed to load privacy settings: ${e.toString()}');
    }
  }

  Future<void> savePrivacySettings(PrivacySettings settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonData = json.encode(settings.toJson());
      await prefs.setString(_privacyKey, jsonData);
    } catch (e) {
      throw Exception('Failed to save privacy settings: ${e.toString()}');
    }
  }

  // Security Settings
  Future<SecuritySettings> getSecuritySettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final data = prefs.getString(_securityKey);

      if (data != null) {
        final jsonData = json.decode(data) as Map<String, dynamic>;
        return SecuritySettings.fromJson(jsonData);
      }

      return const SecuritySettings(
        isTwoFactorEnabled: false,
        isBiometricLoginEnabled: false,
      ); // Default settings
    } catch (e) {
      throw Exception('Failed to load security settings: ${e.toString()}');
    }
  }

  Future<void> saveSecuritySettings(SecuritySettings settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonData = json.encode(settings.toJson());
      await prefs.setString(_securityKey, jsonData);
    } catch (e) {
      throw Exception('Failed to save security settings: ${e.toString()}');
    }
  }
}
