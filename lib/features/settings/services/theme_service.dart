import 'package:billionaires_social/core/app_themes.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ThemeService {
  static const String _themeKey = 'app_theme';

  Future<AppThemeType> getTheme() async {
    // Always return luxuryWhiteGold (white theme) regardless of saved preference
    return AppThemeType.luxuryWhiteGold;
  }

  Future<void> setTheme(AppThemeType theme) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_themeKey, theme.name);
  }
}
