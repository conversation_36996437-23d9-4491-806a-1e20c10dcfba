import 'dart:math';

import 'package:billionaires_social/features/explore/models/place_model.dart';
import 'package:faker/faker.dart';
import 'package:billionaires_social/core/services/analytics_service.dart';
import 'package:billionaires_social/core/service_locator.dart';

class PlacesService {
  final AnalyticsService _analyticsService = getIt<AnalyticsService>();

  // Mock database tables
  static final List<Place> _places = [];
  static final List<PlaceJoin> _joins = [];
  static final List<Booking> _bookings = [];
  static final Map<String, UserJoinStats> _userStats = {};
  static const String currentUserId = 'user1';

  // Generate mock data
  static void generateMockData() {
    if (_places.isNotEmpty) return;

    final faker = Faker();
    final now = DateTime.now();
    final random = Random();

    final mockUsers = List.generate(
      20,
      (index) => PlaceUser(
        id: 'user${index + 2}',
        name: faker.person.firstName(),
        avatarUrl: 'https://randomuser.me/api/portraits/men/${index + 2}.jpg',
      ),
    );

    // Generate luxury locations with enhanced data
    for (int i = 0; i < 50; i++) {
      final category =
          PlaceCategory.values[random.nextInt(PlaceCategory.values.length)];
      final type = PlaceType.values[random.nextInt(PlaceType.values.length)];
      final eventDate = now.add(Duration(days: random.nextInt(60) - 5));
      final attendeesCount = random.nextInt(10);
      // Ensure we don't exceed the available users count
      final safeAttendeesCount = attendeesCount > mockUsers.length ? mockUsers.length : attendeesCount;
      final attendees = safeAttendeesCount > 0 
          ? (mockUsers.toList()..shuffle()).take(safeAttendeesCount).toList()
          : <PlaceUser>[];

      // Generate multiple images for luxury locations
      final imageUrls = List.generate(
        random.nextInt(5) + 1,
        (index) => 'https://picsum.photos/seed/place${i}_img$index/400/300',
      );

      // Generate amenities for luxury locations
      final amenities = _generateAmenities(category, random);

      // Generate pricing for luxury locations
      final price =
          type == PlaceType.accommodation || type == PlaceType.experience
          ? (random.nextDouble() * 5000 + 500).roundToDouble()
          : null;

      _places.add(
        Place(
          id: 'place$i',
          name: _generateLuxuryName(category, faker),
          imageUrl: imageUrls.first,
          imageUrls: imageUrls,
          category: category,
          type: type,
          eventDate: eventDate,
          attendees: attendees,
          isTrending: attendeesCount > 5,
          isNew: now.difference(eventDate).inDays.abs() < 7,
          description: _generateDescription(category, faker),
          location: _generateLocation(faker),
          latitude: 40.7128 + (random.nextDouble() - 0.5) * 0.1, // NYC area
          longitude: -74.0060 + (random.nextDouble() - 0.5) * 0.1,
          price: price,
          currency: price != null ? 'USD' : null,
          amenities: amenities,
          isVerified: random.nextBool(),
          isExclusive: random.nextDouble() < 0.3, // 30% chance
          bookingStatus:
              BookingStatus.values[random.nextInt(BookingStatus.values.length)],
          capacity: type == PlaceType.accommodation
              ? random.nextInt(20) + 1
              : null,
          currentBookings: type == PlaceType.accommodation
              ? random.nextInt(10)
              : null,
          ownerId: 'owner$i',
          ownerName: faker.person.name(),
          ownerAvatarUrl:
              'https://randomuser.me/api/portraits/men/${i + 50}.jpg',
          rating: (random.nextDouble() * 2 + 3).roundToDouble(), // 3.0 - 5.0
          reviewCount: random.nextInt(100),
          lastUpdated: now.subtract(Duration(days: random.nextInt(30))),
        ),
      );
    }

    _userStats[currentUserId] = const UserJoinStats(userId: currentUserId);
  }

  static String _generateLuxuryName(PlaceCategory category, Faker faker) {
    final luxuryPrefixes = [
      'Elite',
      'Royal',
      'Premium',
      'Exclusive',
      'Luxury',
      'Grand',
      'Imperial',
    ];
    final prefix = luxuryPrefixes[Random().nextInt(luxuryPrefixes.length)];

    switch (category) {
      case PlaceCategory.villas:
        return '$prefix Villa ${faker.company.name()}';
      case PlaceCategory.yachts:
        return '$prefix Yacht ${faker.company.name()}';
      case PlaceCategory.privateJets:
        return '$prefix Jet ${faker.company.name()}';
      case PlaceCategory.islands:
        return '$prefix Island ${faker.company.name()}';
      case PlaceCategory.luxuryCars:
        return '$prefix Motors ${faker.company.name()}';
      case PlaceCategory.artGalleries:
        return '$prefix Gallery ${faker.company.name()}';
      case PlaceCategory.exclusiveClubs:
        return '$prefix Club ${faker.company.name()}';
      case PlaceCategory.spas:
        return '$prefix Spa ${faker.company.name()}';
      default:
        return '${faker.company.name()} ${category.name}';
    }
  }

  static String _generateDescription(PlaceCategory category, Faker faker) {
    final descriptions = {
      PlaceCategory.villas:
          'Luxurious private villa with stunning ocean views, infinity pool, and world-class amenities.',
      PlaceCategory.yachts:
          'Exclusive yacht experience with professional crew, gourmet dining, and premium entertainment.',
      PlaceCategory.privateJets:
          'Private jet charter service offering ultimate luxury and privacy for discerning travelers.',
      PlaceCategory.islands:
          'Private island paradise with pristine beaches, tropical gardens, and exclusive access.',
      PlaceCategory.luxuryCars:
          'Premium automotive experience featuring the world\'s most exclusive vehicles.',
      PlaceCategory.artGalleries:
          'Curated collection of contemporary and classical art in an elegant gallery setting.',
      PlaceCategory.exclusiveClubs:
          'Members-only club offering sophisticated dining, entertainment, and networking.',
      PlaceCategory.spas:
          'Luxury wellness retreat with world-class treatments and serene relaxation spaces.',
    };

    return descriptions[category] ?? faker.lorem.sentence();
  }

  static String _generateLocation(Faker faker) {
    final luxuryLocations = [
      'Beverly Hills, CA',
      'Miami Beach, FL',
      'Aspen, CO',
      'Monaco',
      'Dubai, UAE',
      'St. Tropez, France',
      'Maldives',
      'Swiss Alps',
      'Amalfi Coast, Italy',
      'French Riviera',
    ];

    return luxuryLocations[Random().nextInt(luxuryLocations.length)];
  }

  static List<String> _generateAmenities(
    PlaceCategory category,
    Random random,
  ) {
    final allAmenities = [
      'WiFi',
      'Pool',
      'Spa',
      'Gym',
      'Restaurant',
      'Bar',
      'Concierge',
      'Security',
      'Parking',
      'Helipad',
      'Beach Access',
      'Golf Course',
      'Tennis Court',
      'Wine Cellar',
      'Private Chef',
      'Butler Service',
      'Ocean View',
      'Mountain View',
      'City View',
      'Private Garden',
    ];

    final categorySpecificAmenities = {
      PlaceCategory.villas: [
        'Private Pool',
        'Garden',
        'Kitchen',
        'Multiple Bedrooms',
      ],
      PlaceCategory.yachts: [
        'Deck Space',
        'Water Toys',
        'Fishing Equipment',
        'Diving Gear',
      ],
      PlaceCategory.privateJets: [
        'Luxury Seating',
        'In-flight Entertainment',
        'Gourmet Catering',
      ],
      PlaceCategory.islands: [
        'Beach Access',
        'Water Sports',
        'Nature Trails',
        'Wildlife Viewing',
      ],
      PlaceCategory.luxuryCars: [
        'Professional Driver',
        'Chauffeur Service',
        'Vehicle Maintenance',
      ],
      PlaceCategory.artGalleries: [
        'Art Consultant',
        'Private Viewings',
        'Auction Services',
      ],
      PlaceCategory.exclusiveClubs: [
        'Membership Services',
        'Event Planning',
        'Networking Events',
      ],
      PlaceCategory.spas: [
        'Wellness Programs',
        'Fitness Classes',
        'Nutrition Consultation',
      ],
    };

    final specificAmenities = categorySpecificAmenities[category] ?? [];
    final selectedAmenities = <String>[];

    // Add category-specific amenities
    for (final amenity in specificAmenities) {
      if (random.nextBool()) {
        selectedAmenities.add(amenity);
      }
    }

    // Add general amenities
    for (final amenity in allAmenities) {
      if (random.nextBool() && selectedAmenities.length < 8) {
        selectedAmenities.add(amenity);
      }
    }

    return selectedAmenities;
  }

  Future<List<Place>> getPlaces({
    Set<PlaceCategory>? categories,
    Set<PlaceType>? types,
    DateTime? startDate,
    DateTime? endDate,
    double? minPrice,
    double? maxPrice,
    bool? verifiedOnly,
    bool? exclusiveOnly,
    bool? trendingOnly,
    String? searchQuery,
    String? location,
    double? latitude,
    double? longitude,
    double? radius,
    double? minRating,
  }) async {
    generateMockData();
    await Future.delayed(const Duration(milliseconds: 300));

    var filteredPlaces = _places;

    // Category filter
    if (categories != null && categories.isNotEmpty) {
      filteredPlaces = filteredPlaces
          .where((p) => categories.contains(p.category))
          .toList();
    }

    // Type filter
    if (types != null && types.isNotEmpty) {
      filteredPlaces = filteredPlaces
          .where((p) => types.contains(p.type))
          .toList();
    }

    // Date filter
    if (startDate != null && endDate != null) {
      filteredPlaces = filteredPlaces
          .where(
            (p) =>
                p.eventDate.isAfter(
                  startDate.subtract(const Duration(days: 1)),
                ) &&
                p.eventDate.isBefore(endDate.add(const Duration(days: 1))),
          )
          .toList();
    }

    // Price filter
    if (minPrice != null) {
      filteredPlaces = filteredPlaces
          .where((p) => p.price != null && p.price! >= minPrice)
          .toList();
    }

    if (maxPrice != null) {
      filteredPlaces = filteredPlaces
          .where((p) => p.price != null && p.price! <= maxPrice)
          .toList();
    }

    // Verified filter
    if (verifiedOnly == true) {
      filteredPlaces = filteredPlaces.where((p) => p.isVerified).toList();
    }

    // Exclusive filter
    if (exclusiveOnly == true) {
      filteredPlaces = filteredPlaces.where((p) => p.isExclusive).toList();
    }

    // Trending filter
    if (trendingOnly == true) {
      filteredPlaces = filteredPlaces.where((p) => p.isTrending).toList();
    }

    // Search filter
    if (searchQuery != null && searchQuery.isNotEmpty) {
      final query = searchQuery.toLowerCase();
      filteredPlaces = filteredPlaces
          .where(
            (p) =>
                p.name.toLowerCase().contains(query) ||
                p.description?.toLowerCase().contains(query) == true ||
                p.location?.toLowerCase().contains(query) == true,
          )
          .toList();
    }

    // Location filter
    if (location != null && location.isNotEmpty) {
      filteredPlaces = filteredPlaces
          .where(
            (p) =>
                p.location?.toLowerCase().contains(location.toLowerCase()) ==
                true,
          )
          .toList();
    }

    // Rating filter
    if (minRating != null && minRating > 0) {
      filteredPlaces = filteredPlaces
          .where((p) => p.rating >= minRating)
          .toList();
    }

    return filteredPlaces;
  }

  Future<void> joinPlace({
    required String placeId,
    required DateTime visitDate,
    String? notes,
  }) async {
    final userStats = _userStats[currentUserId]!;

    // Abuse prevention rule
    if (userStats.restrictionEndDate != null &&
        userStats.restrictionEndDate!.isAfter(DateTime.now())) {
      throw Exception(
        'You are restricted from joining new places for a week due to missed join-ins.',
      );
    }

    // Simulate missing a join-in
    if (Random().nextDouble() < 0.2) {
      // 20% chance to "miss"
      final newMissedJoins = userStats.missedJoins + 1;
      var newRestrictionDate = userStats.restrictionEndDate;
      if (newMissedJoins >= 4) {
        newRestrictionDate = DateTime.now().add(const Duration(days: 7));
        _userStats[currentUserId] = userStats.copyWith(
          missedJoins: newMissedJoins,
          restrictionEndDate: newRestrictionDate,
        );
        throw Exception(
          'You\'ve missed 4 join-ins. You\'ll be restricted from joining new places for a week.',
        );
      } else {
        _userStats[currentUserId] = userStats.copyWith(
          missedJoins: newMissedJoins,
        );
      }
    }

    final join = PlaceJoin(
      id: 'join${_joins.length + 1}',
      userId: currentUserId,
      placeId: placeId,
      joinDate: DateTime.now(),
      visitDate: visitDate,
      notes: notes,
    );
    _joins.add(join);

    await Future.delayed(const Duration(milliseconds: 500));
  }

  Future<void> cancelJoin(String joinId) async {
    _joins.removeWhere((j) => j.id == joinId);
    await Future.delayed(const Duration(milliseconds: 200));
  }

  Future<UserJoinStats> getUserStats() async {
    return _userStats[currentUserId]!;
  }

  // New booking functionality with analytics
  Future<List<Booking>> getUserBookings(String userId) async {
    await Future.delayed(const Duration(milliseconds: 200));
    final bookings = _bookings.where((b) => b.userId == userId).toList();

    // Log analytics for booking view
    await _analyticsService.logEngagement(
      engagementType: 'bookings_viewed',
      additionalParams: {
        'user_id': userId,
        'booking_count': bookings.length.toString(),
      },
    );

    return bookings;
  }

  Future<List<Booking>> getPlaceBookings(String placeId) async {
    await Future.delayed(const Duration(milliseconds: 200));
    return _bookings.where((b) => b.placeId == placeId).toList();
  }

  Future<Booking?> createBooking(BookingRequest request) async {
    // Get current user ID from the service (this would come from auth in real app)
    final currentUserId = 'user_${_userStats.keys.first}'; // Mock current user
    final userStats = _userStats[currentUserId]!;

    // Abuse prevention rule
    if (userStats.restrictionEndDate != null &&
        userStats.restrictionEndDate!.isAfter(DateTime.now())) {
      throw Exception(
        'You are restricted from joining new places for a week due to missed join-ins.',
      );
    }

    // Simulate missing a join-in
    if (Random().nextDouble() < 0.2) {
      // 20% chance to "miss"
      final newMissedJoins = userStats.missedJoins + 1;
      var newRestrictionDate = userStats.restrictionEndDate;
      if (newMissedJoins >= 4) {
        newRestrictionDate = DateTime.now().add(const Duration(days: 7));
        _userStats[currentUserId] = userStats.copyWith(
          missedJoins: newMissedJoins,
          restrictionEndDate: newRestrictionDate,
        );
        throw Exception(
          'You\'ve missed 4 join-ins. You\'ll be restricted from joining new places for a week.',
        );
      } else {
        _userStats[currentUserId] = userStats.copyWith(
          missedJoins: newMissedJoins,
        );
      }
    }

    final place = _places.firstWhere((p) => p.id == request.placeId);
    final booking = Booking(
      id: 'booking${_bookings.length + 1}',
      userId: currentUserId,
      placeId: request.placeId,
      bookingDate: DateTime.now(),
      visitDate: request.visitDate,
      guestCount: request.guestCount,
      status: BookingStatus.confirmed,
      specialRequests: request.specialRequests,
      contactPhone: request.contactPhone,
      contactEmail: request.contactEmail,
    );
    _bookings.add(booking);

    // Log analytics for booking creation
    await _analyticsService.logEngagement(
      engagementType: 'booking_created',
      additionalParams: {
        'user_id': currentUserId,
        'place_id': request.placeId,
        'visit_date': request.visitDate.toIso8601String(),
      },
    );

    // Log billionaire-specific analytics based on place category
    switch (place.category) {
      case PlaceCategory.privateJets:
        await _analyticsService.logPrivateJetBooking(
          userId: currentUserId,
          destination: place.location ?? 'Unknown',
          cost: place.price ?? 0.0,
          aircraftType: place.name.contains('Gulfstream')
              ? 'Gulfstream'
              : place.name.contains('Boeing')
              ? 'Boeing'
              : 'Private Jet',
        );
        break;
      case PlaceCategory.yachts:
        await _analyticsService.logYachtCharter(
          userId: currentUserId,
          destination: place.location ?? 'Unknown',
          cost: place.price ?? 0.0,
          durationDays: request.visitDate.difference(DateTime.now()).inDays,
        );
        break;
      case PlaceCategory.villas:
      case PlaceCategory.islands:
        await _analyticsService.logLuxuryPurchase(
          userId: currentUserId,
          itemType: place.category.name,
          price: place.price ?? 0.0,
          brand: place.ownerName,
          category: place.category.name,
        );
        break;
      case PlaceCategory.exclusiveClubs:
        await _analyticsService.logExclusiveEventAttendance(
          userId: currentUserId,
          eventId: place.id,
          eventTitle: place.name,
          ticketPrice: place.price ?? 0.0,
        );
        break;
      default:
        // Log general booking event
        await _analyticsService.logEngagement(
          engagementType: 'luxury_booking',
          additionalParams: {
            'user_id': currentUserId,
            'place_category': place.category.name,
            'place_name': place.name,
            'cost': (place.price ?? 0.0).toString(),
          },
        );
    }

    await Future.delayed(const Duration(milliseconds: 500));
    return booking;
  }
}
