import 'package:billionaires_social/core/app_theme.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:billionaires_social/core/widgets/back_button_widget.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:billionaires_social/features/explore/widgets/booking_modal.dart';
import 'package:billionaires_social/features/explore/models/place_model.dart';

enum MapLayer { people, places, both }

class ExploreMapScreen extends StatefulWidget {
  const ExploreMapScreen({super.key});

  @override
  State<ExploreMapScreen> createState() => _ExploreMapScreenState();
}

class _ExploreMapScreenState extends State<ExploreMapScreen> {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  MapLayer _selectedLayer = MapLayer.both;
  bool _showLivePins = true;
  bool _showMyLocation = false;
  String _dateFilter = 'All Time';

  // Sample luxury locations - in real app, these would come from Firestore
  final List<Map<String, dynamic>> _luxuryLocations = [
    {
      'id': '1',
      'name': 'Beverly Hills Hotel',
      'type': 'hotel',
      'latitude': 34.0736,
      'longitude': -118.4004,
      'isExclusive': true,
    },
    {
      'id': '2',
      'name': 'Monaco Yacht Club',
      'type': 'yacht',
      'latitude': 43.7384,
      'longitude': 7.4246,
      'isExclusive': true,
    },
    {
      'id': '3',
      'name': 'Dubai Marina',
      'type': 'location',
      'latitude': 25.0920,
      'longitude': 55.1381,
      'isExclusive': false,
    },
  ];

  // Sample user pins - in real app, these would come from Firestore
  final List<Map<String, dynamic>> _userPins = [
    {
      'id': 'user1',
      'name': 'Alex Thompson',
      'avatar': 'https://example.com/avatar1.jpg',
      'latitude': 34.0522,
      'longitude': -118.2437,
      'isLive': true,
      'status': 'At Beverly Hills Hotel',
    },
    {
      'id': 'user2',
      'name': 'Sarah Wilson',
      'avatar': 'https://example.com/avatar2.jpg',
      'latitude': 43.7384,
      'longitude': 7.4246,
      'isLive': false,
      'status': 'Last seen at Monaco Yacht Club',
    },
  ];

  @override
  void initState() {
    super.initState();
    _checkLocationPermission();
  }

  Future<void> _checkLocationPermission() async {
    final status = await Permission.location.status;
    if (status.isGranted) {
      _getCurrentLocation();
    }
  }

  Future<void> _getCurrentLocation() async {
    try {
      setState(() {});

      await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
        ),
      );

      setState(() {});
    } catch (e) {
      setState(() {});
      debugPrint('Error getting location: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.primaryColor,
      appBar: AppBar(
        backgroundColor: AppTheme.primaryColor,
        elevation: 0,
        leading: const CustomBackButton(size: 36),
        title: Text(
          'Luxury Map',
          style: AppTheme.fontStyles.title.copyWith(
            color: AppTheme.accentColor,
          ),
        ),
        actions: [
          IconButton(
            onPressed: () {
              _showFiltersModal();
            },
            icon: FaIcon(FontAwesomeIcons.sliders, color: AppTheme.accentColor),
          ),
          IconButton(
            onPressed: () {
              _showVisibilitySettings();
            },
            icon: FaIcon(FontAwesomeIcons.eye, color: AppTheme.accentColor),
          ),
        ],
      ),
      body: Column(
        children: [
          // Map layer toggle
          _buildLayerToggle(),
          // Map placeholder with luxury locations and user pins
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: AppTheme.accentColor.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Stack(
                children: [
                  // Map background placeholder
                  Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        FaIcon(
                          FontAwesomeIcons.map,
                          size: 80,
                          color: AppTheme.accentColor.withValues(alpha: 0.3),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Interactive Luxury Map',
                          style: AppTheme.fontStyles.subtitle.copyWith(
                            color: AppTheme.accentColor,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _getMapDescription(),
                          style: AppTheme.fontStyles.body.copyWith(
                            color: AppTheme.secondaryAccentColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Render pins based on selected layer
                  ..._buildMapPins(),
                ],
              ),
            ),
          ),
          // Bottom action bar
          _buildBottomActionBar(),
        ],
      ),
    );
  }

  Widget _buildLayerToggle() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: AppTheme.luxuryBlack,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.accentColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildToggleButton(
              icon: FontAwesomeIcons.user,
              label: 'People',
              isSelected:
                  _selectedLayer == MapLayer.people ||
                  _selectedLayer == MapLayer.both,
              onTap: () {
                setState(() {
                  _selectedLayer = _selectedLayer == MapLayer.people
                      ? MapLayer.both
                      : MapLayer.people;
                });
              },
            ),
          ),
          Expanded(
            child: _buildToggleButton(
              icon: FontAwesomeIcons.locationDot,
              label: 'Places',
              isSelected:
                  _selectedLayer == MapLayer.places ||
                  _selectedLayer == MapLayer.both,
              onTap: () {
                setState(() {
                  _selectedLayer = _selectedLayer == MapLayer.places
                      ? MapLayer.both
                      : MapLayer.places;
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildToggleButton({
    required IconData icon,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.accentColor : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            FaIcon(
              icon,
              size: 16,
              color: isSelected ? Colors.white : AppTheme.secondaryAccentColor,
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: AppTheme.fontStyles.caption.copyWith(
                color: isSelected
                    ? Colors.white
                    : AppTheme.secondaryAccentColor,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildMapPins() {
    List<Widget> pins = [];

    // Add luxury location pins
    if (_selectedLayer == MapLayer.places || _selectedLayer == MapLayer.both) {
      for (final location in _luxuryLocations) {
        pins.add(_buildLuxuryMarker(location));
      }
    }

    // Add user pins
    if (_selectedLayer == MapLayer.people || _selectedLayer == MapLayer.both) {
      for (final user in _userPins) {
        if (_showLivePins || !user['isLive']) {
          pins.add(_buildUserPin(user));
        }
      }
    }

    // Add my location pin if enabled
    if (_showMyLocation) {
      pins.add(_buildMyLocationPin());
    }

    return pins;
  }

  Widget _buildLuxuryMarker(Map<String, dynamic> location) {
    return Positioned(
      left: location['position'].dx * 300,
      top: location['position'].dy * 400,
      child: GestureDetector(
        onTap: () {
          _showLocationDetails(location);
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: location['color'],
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: location['color'].withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(location['icon'], color: Colors.white, size: 16),
              const SizedBox(width: 4),
              Text(
                location['name'],
                style: AppTheme.fontStyles.caption.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUserPin(Map<String, dynamic> user) {
    return Positioned(
      left: user['position'].dx * 300,
      top: user['position'].dy * 400,
      child: GestureDetector(
        onTap: () {
          _showUserContent(user);
        },
        child: Container(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: user['isLive'] ? Colors.green : AppTheme.accentColor,
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: AppTheme.accentColor.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: CircleAvatar(
            radius: 20,
            backgroundColor: AppTheme.primaryColor,
            child: CircleAvatar(
              radius: 18,
              backgroundColor: AppTheme.luxuryGrey,
              child: Text(
                user['username'][0].toUpperCase(),
                style: AppTheme.fontStyles.bodyBold.copyWith(
                  color: AppTheme.accentColor,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMyLocationPin() {
    return Positioned(
      left: 0.1 * 300,
      top: 0.1 * 400,
      child: Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(color: Colors.blue, width: 3),
          boxShadow: [
            BoxShadow(
              color: Colors.blue.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: const CircleAvatar(
          radius: 15,
          backgroundColor: Colors.blue,
          child: Icon(Icons.my_location, color: Colors.white, size: 20),
        ),
      ),
    );
  }

  Widget _buildBottomActionBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor,
        border: Border(
          top: BorderSide(
            color: AppTheme.luxuryGrey.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildActionButton(
              icon: FontAwesomeIcons.bookmark,
              label: 'Bookmarks',
              onTap: () {
                _showBookmarks();
              },
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildActionButton(
              icon: FontAwesomeIcons.calendar,
              label: 'Book Now',
              onTap: () {
                _openBookingInterface();
              },
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildActionButton(
              icon: FontAwesomeIcons.locationArrow,
              label: 'My Location',
              onTap: () {
                setState(() {
                  _showMyLocation = !_showMyLocation;
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: AppTheme.accentColor.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            FaIcon(icon, color: AppTheme.accentColor, size: 20),
            const SizedBox(height: 4),
            Text(
              label,
              style: AppTheme.fontStyles.caption.copyWith(
                color: AppTheme.accentColor,
                fontSize: 10,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getMapDescription() {
    switch (_selectedLayer) {
      case MapLayer.people:
        return 'Following • Live Users • Content';
      case MapLayer.places:
        return 'Villas • Yachts • Jets • Cars';
      case MapLayer.both:
        return 'People & Places • Social Map';
    }
  }

  void _showFiltersModal() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: AppTheme.primaryColor,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppTheme.luxuryGrey,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'Map Filters',
              style: AppTheme.fontStyles.title.copyWith(
                color: AppTheme.accentColor,
              ),
            ),
            const SizedBox(height: 20),
            _buildFilterOption(
              'Show Live Pins',
              'Display users currently active',
              _showLivePins,
              (value) {
                setState(() {
                  _showLivePins = value;
                });
                Navigator.pop(context);
              },
            ),
            const SizedBox(height: 12),
            _buildDateFilterDropdown(),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterOption(
    String title,
    String subtitle,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.accentColor.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTheme.fontStyles.bodyBold.copyWith(
                    color: AppTheme.accentColor,
                  ),
                ),
                Text(
                  subtitle,
                  style: AppTheme.fontStyles.caption.copyWith(
                    color: AppTheme.secondaryAccentColor,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppTheme.accentColor,
          ),
        ],
      ),
    );
  }

  Widget _buildDateFilterDropdown() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.accentColor.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Time Filter',
                  style: AppTheme.fontStyles.bodyBold.copyWith(
                    color: AppTheme.accentColor,
                  ),
                ),
                Text(
                  _dateFilter,
                  style: AppTheme.fontStyles.caption.copyWith(
                    color: AppTheme.secondaryAccentColor,
                  ),
                ),
              ],
            ),
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              setState(() {
                _dateFilter = value;
              });
            },
            itemBuilder: (context) =>
                ['All Time', 'Last 24 Hours', 'This Week', 'This Month']
                    .map(
                      (option) =>
                          PopupMenuItem(value: option, child: Text(option)),
                    )
                    .toList(),
            child: const FaIcon(
              FontAwesomeIcons.chevronDown,
              color: AppTheme.accentColor,
            ),
          ),
        ],
      ),
    );
  }

  void _showVisibilitySettings() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: AppTheme.primaryColor,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppTheme.luxuryGrey,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'Location Privacy',
              style: AppTheme.fontStyles.title.copyWith(
                color: AppTheme.accentColor,
              ),
            ),
            const SizedBox(height: 20),
            _buildFilterOption(
              'Show My Location',
              'Make your location visible to followers',
              _showMyLocation,
              (value) {
                setState(() {
                  _showMyLocation = value;
                });
                Navigator.pop(context);
              },
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppTheme.accentColor.withValues(alpha: 0.2),
                ),
              ),
              child: Row(
                children: [
                  const FaIcon(
                    FontAwesomeIcons.shieldHalved,
                    color: AppTheme.accentColor,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Your location is hidden by default for privacy',
                      style: AppTheme.fontStyles.caption.copyWith(
                        color: AppTheme.secondaryAccentColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  void _showLocationDetails(Map<String, dynamic> location) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: AppTheme.primaryColor,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppTheme.luxuryGrey,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: location['color'].withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: location['color'].withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(location['icon'], color: location['color'], size: 32),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          location['name'],
                          style: AppTheme.fontStyles.subtitle.copyWith(
                            color: AppTheme.accentColor,
                          ),
                        ),
                        Text(
                          location['price'],
                          style: AppTheme.fontStyles.body.copyWith(
                            color: AppTheme.secondaryAccentColor,
                          ),
                        ),
                        Row(
                          children: [
                            const FaIcon(
                              FontAwesomeIcons.star,
                              color: Colors.amber,
                              size: 14,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              location['rating'].toString(),
                              style: AppTheme.fontStyles.caption.copyWith(
                                color: AppTheme.secondaryAccentColor,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    icon: const FaIcon(FontAwesomeIcons.bookmark),
                    label: const Text('Bookmark'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.accentColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    icon: const FaIcon(FontAwesomeIcons.calendar),
                    label: const Text('Book Now'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: location['color'],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  void _showUserContent(Map<String, dynamic> user) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: AppTheme.primaryColor,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppTheme.luxuryGrey,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor: AppTheme.luxuryGrey,
                  child: Text(
                    user['username'][0].toUpperCase(),
                    style: AppTheme.fontStyles.bodyBold.copyWith(
                      color: AppTheme.accentColor,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        user['username'],
                        style: AppTheme.fontStyles.bodyBold.copyWith(
                          color: AppTheme.accentColor,
                        ),
                      ),
                      Row(
                        children: [
                          if (user['isLive'])
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.green,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                'LIVE',
                                style: AppTheme.fontStyles.caption.copyWith(
                                  color: Colors.white,
                                  fontSize: 8,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          const SizedBox(width: 8),
                          Text(
                            _getTimeAgo(user['lastActive']),
                            style: AppTheme.fontStyles.caption.copyWith(
                              color: AppTheme.secondaryAccentColor,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppTheme.luxuryGrey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppTheme.accentColor.withValues(alpha: 0.2),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: AppTheme.luxuryGrey,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      user['content']['type'] == 'post'
                          ? Icons.photo
                          : Icons.circle,
                      color: AppTheme.accentColor,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      user['content']['preview'],
                      style: AppTheme.fontStyles.body.copyWith(
                        color: AppTheme.secondaryAccentColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    icon: const FaIcon(FontAwesomeIcons.user),
                    label: const Text('View Profile'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.accentColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    icon: const FaIcon(FontAwesomeIcons.eye),
                    label: const Text('View Content'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.transparent,
                      foregroundColor: AppTheme.accentColor,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      side: BorderSide(color: AppTheme.accentColor),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  String _getTimeAgo(DateTime lastActive) {
    final now = DateTime.now();
    final difference = now.difference(lastActive);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  Future<void> _showBookmarks() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please sign in to view bookmarks')),
        );
        return;
      }

      final bookmarksSnapshot = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .collection('favorites')
          .get();

      if (bookmarksSnapshot.docs.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('No bookmarked locations found')),
          );
        }
        return;
      }

      // Show bookmarks in a modal
      if (mounted) {
        showModalBottomSheet(
          context: context,
          builder: (context) => _buildBookmarksModal(bookmarksSnapshot.docs),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading bookmarks: ${e.toString()}')),
        );
      }
    }
  }

  Widget _buildBookmarksModal(List<QueryDocumentSnapshot> bookmarks) {
    return Container(
      height: 400,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Bookmarked Locations',
            style: AppTheme.fontStyles.title.copyWith(
              color: AppTheme.accentColor,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView.builder(
              itemCount: bookmarks.length,
              itemBuilder: (context, index) {
                final bookmark =
                    bookmarks[index].data() as Map<String, dynamic>;
                return ListTile(
                  leading: CircleAvatar(
                    backgroundImage: NetworkImage(bookmark['placeImage'] ?? ''),
                    onBackgroundImageError: (e, s) => const Icon(Icons.place),
                  ),
                  title: Text(bookmark['placeName'] ?? 'Unknown Place'),
                  subtitle: Text(bookmark['placeCategory'] ?? ''),
                  trailing: IconButton(
                    icon: const Icon(Icons.navigation),
                    onPressed: () {
                      Navigator.pop(context);
                      _navigateToBookmarkedPlace(bookmark);
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _openBookingInterface() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please sign in to make bookings')),
        );
        return;
      }

      // Get nearby places for booking
      final placesSnapshot = await _firestore
          .collection('places')
          .where('type', whereIn: ['accommodation', 'experience'])
          .limit(10)
          .get();

      if (placesSnapshot.docs.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('No bookable places found nearby')),
          );
        }
        return;
      }

      // Show booking interface
      if (mounted) {
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          builder: (context) => _buildBookingInterface(placesSnapshot.docs),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading booking options: ${e.toString()}'),
          ),
        );
      }
    }
  }

  Widget _buildBookingInterface(List<QueryDocumentSnapshot> places) {
    return Container(
      height: 500,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Book Nearby Places',
            style: AppTheme.fontStyles.title.copyWith(
              color: AppTheme.accentColor,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView.builder(
              itemCount: places.length,
              itemBuilder: (context, index) {
                final place = places[index].data() as Map<String, dynamic>;
                return Card(
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundImage: NetworkImage(place['imageUrl'] ?? ''),
                      onBackgroundImageError: (e, s) => const Icon(Icons.place),
                    ),
                    title: Text(place['name'] ?? 'Unknown Place'),
                    subtitle: Text(place['type'] ?? ''),
                    trailing: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        _navigateToBookingScreen(place);
                      },
                      child: const Text('Book'),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  // Navigation Methods

  void _navigateToBookmarkedPlace(Map<String, dynamic> bookmark) {
    // Extract coordinates from bookmark
    final latitude = bookmark['latitude'] as double?;
    final longitude = bookmark['longitude'] as double?;
    final placeName = bookmark['placeName'] as String? ?? 'Unknown Place';

    if (latitude != null && longitude != null) {
      // In a real implementation with a map controller, you would:
      // 1. Animate the map camera to the coordinates
      // 2. Show a marker at the location
      // 3. Optionally show place details

      // For now, show a success message with coordinates
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Navigating to $placeName at ($latitude, $longitude)'),
          backgroundColor: Colors.green,
          action: SnackBarAction(
            label: 'View Details',
            textColor: Colors.white,
            onPressed: () {
              _showPlaceDetails(bookmark);
            },
          ),
        ),
      );
    } else {
      // Handle case where coordinates are not available
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Location coordinates not available for $placeName'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  void _navigateToBookingScreen(Map<String, dynamic> placeData) {
    try {
      // Convert Firestore document data to Place model
      final place = Place(
        id: placeData['id'] ?? '',
        name: placeData['name'] ?? 'Unknown Place',
        imageUrl: placeData['imageUrl'] ?? '',
        category: _getPlaceCategoryFromString(placeData['category'] as String?),
        eventDate: placeData['eventDate'] != null
            ? (placeData['eventDate'] as Timestamp).toDate()
            : DateTime.now(),
        description: placeData['description'],
        location: placeData['location'],
        latitude: (placeData['latitude'] as num?)?.toDouble(),
        longitude: (placeData['longitude'] as num?)?.toDouble(),
        type: _getPlaceTypeFromString(placeData['type'] as String?),
        price: (placeData['price'] as num?)?.toDouble(),
        currency: placeData['currency'],
        imageUrls: List<String>.from(placeData['imageUrls'] ?? []),
        amenities: List<String>.from(placeData['amenities'] ?? []),
        isExclusive: placeData['isExclusive'] ?? false,
        ownerId: placeData['ownerId'],
        ownerName: placeData['ownerName'],
        ownerAvatarUrl: placeData['ownerAvatarUrl'],
        rating: (placeData['rating'] as num?)?.toDouble() ?? 0.0,
        reviewCount: placeData['reviewCount'],
        capacity: placeData['capacity'],
        currentBookings: placeData['currentBookings'],
      );

      // Show booking modal
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Theme.of(context).cardColor,
        builder: (context) => BookingModal(place: place),
      );
    } catch (e) {
      // Handle error in place conversion
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error opening booking: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showPlaceDetails(Map<String, dynamic> bookmark) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(bookmark['placeName'] ?? 'Place Details'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (bookmark['placeCategory'] != null)
              Text('Category: ${bookmark['placeCategory']}'),
            const SizedBox(height: 8),
            if (bookmark['latitude'] != null && bookmark['longitude'] != null)
              Text(
                'Coordinates: ${bookmark['latitude']}, ${bookmark['longitude']}',
              ),
            const SizedBox(height: 8),
            if (bookmark['bookmarkedAt'] != null)
              Text('Bookmarked: ${_formatDate(bookmark['bookmarkedAt'])}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  PlaceCategory _getPlaceCategoryFromString(String? categoryString) {
    switch (categoryString?.toLowerCase()) {
      case 'restaurants':
        return PlaceCategory.restaurants;
      case 'events':
        return PlaceCategory.events;
      case 'nightlife':
        return PlaceCategory.nightlife;
      case 'cafes':
        return PlaceCategory.cafes;
      case 'resorts':
        return PlaceCategory.resorts;
      case 'concerts':
        return PlaceCategory.concerts;
      case 'shopping':
        return PlaceCategory.shopping;
      case 'shows':
        return PlaceCategory.shows;
      case 'villas':
        return PlaceCategory.villas;
      case 'yachts':
        return PlaceCategory.yachts;
      case 'privatejets':
      case 'private_jets':
        return PlaceCategory.privateJets;
      case 'islands':
        return PlaceCategory.islands;
      case 'luxurycars':
      case 'luxury_cars':
        return PlaceCategory.luxuryCars;
      case 'artgalleries':
      case 'art_galleries':
        return PlaceCategory.artGalleries;
      case 'exclusiveclubs':
      case 'exclusive_clubs':
        return PlaceCategory.exclusiveClubs;
      case 'spas':
        return PlaceCategory.spas;
      default:
        return PlaceCategory.restaurants; // Default fallback
    }
  }

  PlaceType _getPlaceTypeFromString(String? typeString) {
    switch (typeString?.toLowerCase()) {
      case 'accommodation':
        return PlaceType.accommodation;
      case 'experience':
        return PlaceType.experience;
      case 'location':
        return PlaceType.location;
      case 'event':
        return PlaceType.event;
      default:
        return PlaceType.location; // Default fallback
    }
  }

  String _formatDate(dynamic timestamp) {
    if (timestamp is Timestamp) {
      final date = timestamp.toDate();
      return '${date.day}/${date.month}/${date.year}';
    }
    return 'Unknown';
  }
}
