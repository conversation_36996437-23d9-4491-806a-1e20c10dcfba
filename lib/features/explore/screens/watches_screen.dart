import 'package:flutter/material.dart';
import 'package:billionaires_social/core/app_theme.dart';
import '../widgets/feature_list_widget.dart';

class WatchesScreen extends StatelessWidget {
  const WatchesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final watchItems = [
      FeatureListItem(
        title: 'Rolex Submariner',
        subtitle: 'Iconic luxury dive watch',
        imageUrl:
            'https://images.unsplash.com/photo-1516574187841-cb9cc2ca948b',
        onTap: () {},
        icon: Icons.watch,
      ),
      FeatureListItem(
        title: 'Patek <PERSON> Nautilus',
        subtitle: 'Prestige and craftsmanship',
        imageUrl:
            'https://images.unsplash.com/photo-1465101046530-73398c7f28ca',
        onTap: () {},
        icon: Icons.watch,
      ),
      FeatureListItem(
        title: 'Audemars Piguet Royal Oak',
        subtitle: 'Distinctive design, legendary status',
        imageUrl:
            'https://images.unsplash.com/photo-1503602642458-232111445657',
        onTap: () {},
        icon: Icons.watch,
      ),
    ];

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Watches',
          style: AppTheme.fontStyles.title.copyWith(
            color: AppTheme.accentColor,
          ),
        ),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: FeatureListWidget(
          sectionTitle: 'Featured Watches',
          items: watchItems,
        ),
      ),
    );
  }
}
