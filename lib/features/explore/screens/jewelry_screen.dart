import 'package:flutter/material.dart';
import 'package:billionaires_social/core/app_theme.dart';
import '../widgets/feature_list_widget.dart';

class JewelryScreen extends StatelessWidget {
  const JewelryScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final jewelryItems = [
      FeatureListItem(
        title: 'Diamond Necklace',
        subtitle: 'Brilliant-cut centerpiece',
        imageUrl:
            'https://images.unsplash.com/photo-1465101178521-c1a9136a3b99',
        onTap: () {},
        icon: Icons.diamond,
      ),
      FeatureListItem(
        title: 'Emerald Ring',
        subtitle: 'Vivid green, timeless elegance',
        imageUrl:
            'https://images.unsplash.com/photo-1519125323398-675f0ddb6308',
        onTap: () {},
        icon: Icons.diamond,
      ),
      FeatureListItem(
        title: 'Sapphire Earrings',
        subtitle: 'Deep blue, classic style',
        imageUrl:
            'https://images.unsplash.com/photo-1504196606672-aef5c9cefc92',
        onTap: () {},
        icon: Icons.diamond,
      ),
    ];

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Jewelry',
          style: AppTheme.fontStyles.title.copyWith(
            color: AppTheme.accentColor,
          ),
        ),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: FeatureListWidget(
          sectionTitle: 'Featured Jewelry',
          items: jewelryItems,
        ),
      ),
    );
  }
}
