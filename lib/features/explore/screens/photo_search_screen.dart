import 'package:billionaires_social/core/app_theme.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

class PhotoSearchScreen extends StatefulWidget {
  const PhotoSearchScreen({super.key});

  @override
  State<PhotoSearchScreen> createState() => _PhotoSearchScreenState();
}

class _PhotoSearchScreenState extends State<PhotoSearchScreen> {
  File? _selectedImage;
  bool _isSearching = false;
  List<Person> _searchResults = [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Search by Photo'),
        actions: [
          if (_selectedImage != null)
            IconButton(onPressed: _clearImage, icon: const Icon(Icons.clear)),
        ],
      ),
      body: Column(
        children: [
          _buildPhotoSection(),
          if (_isSearching) _buildSearchingIndicator(),
          if (_searchResults.isNotEmpty) _buildSearchResults(),
        ],
      ),
    );
  }

  Widget _buildPhotoSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          if (_selectedImage == null) ...[
            Container(
              width: double.infinity,
              height: 200,
              decoration: BoxDecoration(
                color: AppTheme.luxuryGrey.withAlpha(51),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppTheme.accentColor.withAlpha(100),
                  style: BorderStyle.solid,
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  FaIcon(
                    FontAwesomeIcons.camera,
                    size: 48,
                    color: AppTheme.accentColor.withAlpha(150),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Upload a photo to search',
                    style: AppTheme.fontStyles.body.copyWith(
                      color: AppTheme.secondaryAccentColor,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _pickImage(ImageSource.camera),
                    icon: const FaIcon(FontAwesomeIcons.camera),
                    label: const Text('Take Photo'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.accentColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _pickImage(ImageSource.gallery),
                    icon: const FaIcon(FontAwesomeIcons.images),
                    label: const Text('Choose from Gallery'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: AppTheme.accentColor,
                      side: BorderSide(color: AppTheme.accentColor),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
          ] else ...[
            Container(
              width: double.infinity,
              height: 300,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(50),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Image.file(_selectedImage!, fit: BoxFit.cover),
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _searchByPhoto,
                icon: const FaIcon(FontAwesomeIcons.magnifyingGlass),
                label: const Text('Search by Photo'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.accentColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSearchingIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(
            'Analyzing photo and searching for matches...',
            style: AppTheme.fontStyles.body.copyWith(
              color: AppTheme.secondaryAccentColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'Search Results (${_searchResults.length} matches)',
              style: AppTheme.fontStyles.title.copyWith(
                color: AppTheme.accentColor,
              ),
            ),
          ),
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _searchResults.length,
              itemBuilder: (context, index) {
                final person = _searchResults[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: ListTile(
                    leading: CircleAvatar(
                      radius: 30,
                      backgroundImage: CachedNetworkImageProvider(
                        person.avatarUrl,
                      ),
                    ),
                    title: Text(
                      person.name,
                      style: AppTheme.fontStyles.bodyBold,
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(person.bio),
                        const SizedBox(height: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: AppTheme.accentColor.withAlpha(50),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            '${person.matchPercentage}% match',
                            style: AppTheme.fontStyles.caption.copyWith(
                              color: AppTheme.accentColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    trailing: ElevatedButton(
                      onPressed: () {
                        // TODO: Navigate to profile
                      },
                      child: const Text('View Profile'),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _pickImage(ImageSource source) async {
    final messenger = ScaffoldMessenger.of(context);
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(
        source: source,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        setState(() {
          _selectedImage = File(pickedFile.path);
        });
      }
    } catch (e) {
      messenger.showSnackBar(
        SnackBar(content: Text('Error picking image: $e')),
      );
    }
  }

  void _clearImage() {
    setState(() {
      _selectedImage = null;
      _searchResults.clear();
    });
  }

  Future<void> _searchByPhoto() async {
    if (_selectedImage == null) return;

    setState(() {
      _isSearching = true;
      _searchResults.clear();
    });

    // Simulate API call delay
    await Future.delayed(const Duration(seconds: 2));

    // Mock search results based on the uploaded photo
    setState(() {
      _isSearching = false;
      _searchResults = _getMockSearchResults();
    });
  }

  List<Person> _getMockSearchResults() {
    return [
          Person(
            name: 'Elon Musk',
            bio: 'CEO of Tesla and SpaceX',
            avatarUrl: 'https://i.pravatar.cc/150?u=elonmusk',
            discoverableByPhoto: true,
            matchPercentage: 95,
          ),
          Person(
            name: 'Bill Gates',
            bio: 'Co-founder of Microsoft',
            avatarUrl: 'https://i.pravatar.cc/150?u=billgates',
            discoverableByPhoto: true,
            matchPercentage: 87,
          ),
          Person(
            name: 'Jeff Bezos',
            bio: 'Founder of Amazon',
            avatarUrl: 'https://i.pravatar.cc/150?u=jeffbezos',
            discoverableByPhoto:
                false, // This person won't appear due to privacy settings
            matchPercentage: 82,
          ),
        ]
        .where((person) => person.discoverableByPhoto)
        .toList(); // Filter by privacy setting
  }
}

class Person {
  final String name;
  final String bio;
  final String avatarUrl;
  final bool discoverableByPhoto;
  final int matchPercentage;

  Person({
    required this.name,
    required this.bio,
    required this.avatarUrl,
    required this.discoverableByPhoto,
    required this.matchPercentage,
  });
}
