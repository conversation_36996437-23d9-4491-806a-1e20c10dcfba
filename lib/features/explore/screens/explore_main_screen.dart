import 'package:billionaires_social/features/explore/widgets/billionaires_tab.dart';
import 'package:billionaires_social/features/explore/widgets/places_tab.dart';
import 'package:billionaires_social/features/explore/widgets/reels_tab.dart';
import 'package:billionaires_social/features/explore/widgets/trends_tab.dart';
import 'package:billionaires_social/features/marketplace/screens/marketplace_main_screen.dart';
import 'package:billionaires_social/features/events/screens/events_main_screen.dart';
import 'package:flutter/material.dart';

class ExploreMainScreen extends StatelessWidget {
  const ExploreMainScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 6,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Explore'),
          backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
          foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
          bottom: TabBar(
            indicatorColor: Theme.of(context).tabBarTheme.indicatorColor,
            labelColor: Theme.of(context).tabBarTheme.labelColor,
            unselectedLabelColor: Theme.of(
              context,
            ).tabBarTheme.unselectedLabelColor,
            tabs: const [
              Tab(text: 'Billionaires'),
              Tab(text: 'Places'),
              Tab(text: 'Trends'),
              Tab(text: 'Reels'),
              Tab(text: 'Marketplace'),
              Tab(text: 'Events'),
            ],
          ),
        ),
        body: const TabBarView(
          children: [
            BillionairesTab(),
            PlacesTab(),
            TrendsTab(),
            ReelsTab(),
            MarketplaceMainScreen(),
            EventsMainScreen(),
          ],
        ),
      ),
    );
  }
}
