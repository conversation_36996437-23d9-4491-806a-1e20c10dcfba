import 'package:flutter/material.dart';
import 'package:billionaires_social/core/app_theme.dart';
import '../widgets/feature_list_widget.dart';

class ArtScreen extends StatelessWidget {
  const ArtScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final artItems = [
      FeatureListItem(
        title: 'Starry Night',
        subtitle: '<PERSON>, 1889',
        imageUrl:
            'https://images.unsplash.com/photo-1506744038136-46273834b3fb',
        onTap: () {},
        icon: Icons.brush,
      ),
      FeatureListItem(
        title: '<PERSON>',
        subtitle: '<PERSON>, 1503',
        imageUrl:
            'https://images.unsplash.com/photo-1464983953574-0892a716854b',
        onTap: () {},
        icon: Icons.brush,
      ),
      FeatureListItem(
        title: 'The Persistence of Memory',
        subtitle: 'Salvador <PERSON>, 1931',
        imageUrl:
            'https://images.unsplash.com/photo-1517694712202-14dd9538aa97',
        onTap: () {},
        icon: Icons.brush,
      ),
    ];

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Art',
          style: AppTheme.fontStyles.title.copyWith(
            color: AppTheme.accentColor,
          ),
        ),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: FeatureListWidget(
          sectionTitle: 'Featured Artworks',
          items: artItems,
        ),
      ),
    );
  }
}
