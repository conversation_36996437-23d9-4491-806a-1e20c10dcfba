import 'package:flutter/material.dart';
import 'package:billionaires_social/core/app_theme.dart';
import '../widgets/feature_list_widget.dart';

class FashionScreen extends StatelessWidget {
  const FashionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final fashionItems = [
      FeatureListItem(
        title: 'Classic Suit',
        subtitle: 'Timeless elegance for any occasion',
        imageUrl:
            'https://images.unsplash.com/photo-1512436991641-6745cdb1723f',
        onTap: () {},
        icon: Icons.checkroom,
      ),
      FeatureListItem(
        title: 'Designer Dress',
        subtitle: 'Luxury evening wear',
        imageUrl:
            'https://images.unsplash.com/photo-1503342217505-b0a15ec3261c',
        onTap: () {},
        icon: Icons.checkroom,
      ),
      FeatureListItem(
        title: 'Luxury Handbag',
        subtitle: 'Iconic statement piece',
        imageUrl:
            'https://images.unsplash.com/photo-1517841905240-472988babdf9',
        onTap: () {},
        icon: Icons.shopping_bag,
      ),
    ];

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Fashion',
          style: AppTheme.fontStyles.title.copyWith(
            color: AppTheme.accentColor,
          ),
        ),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: FeatureListWidget(
          sectionTitle: 'Featured Fashion',
          items: fashionItems,
        ),
      ),
    );
  }
}
