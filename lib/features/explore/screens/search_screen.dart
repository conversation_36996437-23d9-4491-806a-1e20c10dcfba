import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/features/explore/screens/photo_search_screen.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../search/services/user_search_service.dart';
import '../../profile/providers/followers_provider.dart';
import '../../profile/providers/profile_provider.dart';
import '../../auth/providers/auth_provider.dart';
import '../../profile/models/profile_model.dart';
import '../../profile/screens/user_profile_screen.dart';

class SearchScreen extends ConsumerStatefulWidget {
  const SearchScreen({super.key});

  @override
  ConsumerState<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends ConsumerState<SearchScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  List<ProfileModel> _searchResults = [];
  List<ProfileModel> _searchHistory = [];
  List<ProfileModel> _suggestions = [];
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadSearchHistory();
    _loadSuggestions();
  }

  Future<void> _loadSearchHistory() async {
    try {
      final userSearchService = ref.read(userSearchServiceProvider);
      final currentUser = ref.read(authProvider).value;

      if (currentUser != null) {
        final history = await userSearchService.getRecentSearches(
          currentUserId: currentUser.uid,
          limit: 10,
        );

        if (mounted) {
          setState(() {
            _searchHistory = history;
          });
        }
      }
    } catch (e) {
      debugPrint('Error loading search history: $e');
    }
  }

  Future<void> _loadSuggestions() async {
    try {
      final userSearchService = ref.read(userSearchServiceProvider);
      final currentUser = ref.read(authProvider).value;

      // Get suggested users (search for common names to get popular users)
      final suggestions = await userSearchService.searchUsers(
        query: '', // Empty query to get general suggestions
        limit: 10,
        excludeUserIds: currentUser != null ? [currentUser.uid] : null,
        verifiedOnly: true, // Prefer verified users as suggestions
      );

      if (mounted) {
        setState(() {
          _suggestions = suggestions;
        });
      }
    } catch (e) {
      debugPrint('Error loading suggestions: $e');
      // Fallback to empty list if trending users method doesn't exist
      setState(() {
        _suggestions = [];
      });
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _performUserSearch(String query) async {
    if (query.trim().isEmpty) {
      setState(() {
        _searchResults = [];
        _isSearching = false;
      });
      return;
    }

    setState(() {
      _isSearching = true;
    });

    try {
      final userSearchService = ref.read(userSearchServiceProvider);
      final currentUser = ref.read(authProvider).value;

      final results = await userSearchService.searchUsers(
        query: query,
        limit: 20,
        excludeUserIds: currentUser != null ? [currentUser.uid] : null,
      );

      if (mounted) {
        setState(() {
          _searchResults = results;
          _isSearching = false;
        });
      }
    } catch (e) {
      debugPrint('Error searching users: $e');
      if (mounted) {
        setState(() {
          _searchResults = [];
          _isSearching = false;
        });
      }
    }
  }

  Future<void> _onUserTap(ProfileModel user) async {
    final currentUser = ref.read(authProvider).value;

    // Save to search history
    if (currentUser != null) {
      try {
        final userSearchService = ref.read(userSearchServiceProvider);
        await userSearchService.saveSearchToHistory(
          currentUserId: currentUser.uid,
          searchedUserId: user.id,
        );

        // Refresh search history
        _loadSearchHistory();
      } catch (e) {
        debugPrint('Error saving search to history: $e');
      }
    }

    // Navigate to user profile
    if (mounted) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => UserProfileScreen(userId: user.id),
        ),
      );
    }
  }

  Future<void> _clearSearchHistory() async {
    final currentUser = ref.read(authProvider).value;
    if (currentUser == null) return;

    try {
      final userSearchService = ref.read(userSearchServiceProvider);
      await userSearchService.clearSearchHistory(currentUser.uid);

      setState(() {
        _searchHistory = [];
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Search history cleared'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error clearing search history: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to clear search history'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: TextField(
          controller: _searchController,
          onChanged: (query) {
            setState(() {
              _searchQuery = query;
            });
            _performUserSearch(query);
          },
          decoration: InputDecoration(
            hintText: 'Search users...',
            border: InputBorder.none,
            hintStyle: const TextStyle(color: Colors.grey),
            suffixIcon: _searchQuery.isNotEmpty
                ? IconButton(
                    icon: const Icon(Icons.clear, color: Colors.grey),
                    onPressed: () {
                      _searchController.clear();
                      setState(() {
                        _searchQuery = '';
                        _searchResults = [];
                      });
                    },
                  )
                : null,
          ),
        ),
        actions: [
          if (_searchHistory.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.history),
              onPressed: _clearSearchHistory,
              tooltip: 'Clear search history',
            ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'People'),
            Tab(text: 'Posts'),
            Tab(text: 'Tags'),
          ],
        ),
      ),
      body: Column(
        children: [
          _buildPhotoSearchButton(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildPeopleResults(),
                _buildPostsResults(),
                _buildTagsResults(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhotoSearchButton() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton.icon(
          onPressed: () {
            Navigator.of(context).push(
              MaterialPageRoute(builder: (_) => const PhotoSearchScreen()),
            );
          },
          icon: const FaIcon(FontAwesomeIcons.camera),
          label: const Text('Search by Photo'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.accentColor,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
        ),
      ),
    );
  }

  Widget _buildPeopleResults() {
    if (_searchQuery.isEmpty) {
      return _buildSuggestionsAndHistory();
    }

    if (_isSearching) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_searchResults.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.search_off, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            Text(
              'No users found for "$_searchQuery"',
              style: const TextStyle(fontSize: 16, color: Colors.grey),
            ),
            const SizedBox(height: 8),
            const Text(
              'Try searching with a different name or username',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        final user = _searchResults[index];
        return _buildUserTile(user, isSearchResult: true);
      },
    );
  }

  Widget _buildUserTile(
    ProfileModel user, {
    bool isSearchResult = false,
    bool isHistory = false,
  }) {
    return ListTile(
      leading: CircleAvatar(
        radius: 25,
        backgroundImage: user.profilePictureUrl.isNotEmpty
            ? CachedNetworkImageProvider(user.profilePictureUrl)
            : null,
        backgroundColor: user.profilePictureUrl.isEmpty
            ? Colors.grey[300]
            : null,
        child: user.profilePictureUrl.isEmpty
            ? const Icon(Icons.person, color: Colors.grey)
            : null,
      ),
      title: Row(
        children: [
          Expanded(
            child: Text(
              user.name.isNotEmpty ? user.name : user.username,
              style: const TextStyle(fontWeight: FontWeight.w600),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (user.isVerified) ...[
            const SizedBox(width: 4),
            const Icon(Icons.verified, color: Colors.blue, size: 16),
          ],
          if (isHistory) ...[
            const SizedBox(width: 4),
            Icon(Icons.history, color: Colors.grey[400], size: 16),
          ],
        ],
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('@${user.username}'),
          if (user.bio.isNotEmpty)
            Text(
              user.bio,
              style: TextStyle(color: Colors.grey[600], fontSize: 12),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          if (user.followerCount > 0)
            Text(
              '${_formatCount(user.followerCount)} followers',
              style: TextStyle(color: Colors.grey[500], fontSize: 11),
            ),
        ],
      ),
      trailing: FutureBuilder<bool>(
        future: ref.read(followersServiceProvider).isFollowing(user.id),
        builder: (context, snapshot) {
          final isFollowing = snapshot.data ?? false;
          return _buildFollowButton(user, isFollowing);
        },
      ),
      onTap: () => _onUserTap(user),
    );
  }

  Widget _buildFollowButton(ProfileModel user, bool isFollowing) {
    return ElevatedButton(
      onPressed: () => _handleFollow(user, isFollowing),
      style: ElevatedButton.styleFrom(
        backgroundColor: isFollowing ? Colors.grey[300] : AppTheme.accentColor,
        foregroundColor: isFollowing ? Colors.black : Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),
      child: Text(isFollowing ? 'Following' : 'Follow'),
    );
  }

  Future<void> _handleFollow(
    ProfileModel user,
    bool isCurrentlyFollowing,
  ) async {
    try {
      final followersService = ref.read(followersServiceProvider);

      if (isCurrentlyFollowing) {
        // Unfollow
        await followersService.unfollowUser(user.id);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('You unfollowed ${user.name}'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      } else {
        // Follow
        await followersService.followUser(user.id);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('You are now following ${user.name}'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }

      // Invalidate count providers to refresh UI immediately
      if (mounted) {
        final currentUserId = FirebaseAuth.instance.currentUser?.uid;
        if (currentUserId != null) {
          // Invalidate follower count for the user being followed/unfollowed
          ref.invalidate(followerCountProvider(user.id));
          // Invalidate following count for the current user
          ref.invalidate(followingCountProvider(currentUserId));
          // Also invalidate profile providers to refresh cached counts
          ref.invalidate(profileProvider(user.id));
          ref.invalidate(profileProvider(currentUserId));
        }
      }

      // Trigger a rebuild to update the button state
      setState(() {});
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to ${isCurrentlyFollowing ? 'unfollow' : 'follow'} user: $e',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _formatCount(int count) {
    if (count >= 1000000) {
      return '${(count / 1000000).toStringAsFixed(1)}M';
    } else if (count >= 1000) {
      return '${(count / 1000).toStringAsFixed(1)}K';
    }
    return count.toString();
  }

  Widget _buildPostsResults() {
    if (_searchQuery.isEmpty) {
      return _buildSuggestions();
    }

    final posts = _getMockPosts()
        .where(
          (post) =>
              post.caption.toLowerCase().contains(_searchQuery.toLowerCase()),
        )
        .toList();

    return GridView.builder(
      padding: const EdgeInsets.all(8),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: posts.length,
      itemBuilder: (context, index) {
        final post = posts[index];
        return CachedNetworkImage(imageUrl: post.imageUrl, fit: BoxFit.cover);
      },
    );
  }

  Widget _buildTagsResults() {
    if (_searchQuery.isEmpty) {
      return _buildSuggestions();
    }

    final tags = _getMockTags()
        .where((tag) => tag.toLowerCase().contains(_searchQuery.toLowerCase()))
        .toList();

    return ListView.builder(
      itemCount: tags.length,
      itemBuilder: (context, index) {
        final tag = tags[index];
        return ListTile(
          leading: const FaIcon(FontAwesomeIcons.hashtag),
          title: Text('#$tag'),
          subtitle: Text('${(index + 1) * 1000} posts'),
        );
      },
    );
  }

  Widget _buildSuggestionsAndHistory() {
    return ListView(
      children: [
        // Search History Section
        if (_searchHistory.isNotEmpty) ...[
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Recent Searches',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                TextButton(
                  onPressed: _clearSearchHistory,
                  child: const Text(
                    'Clear All',
                    style: TextStyle(color: Colors.red),
                  ),
                ),
              ],
            ),
          ),
          ..._searchHistory.map(
            (user) => _buildUserTile(user, isHistory: true),
          ),
          const Divider(height: 32),
        ],

        // Suggestions Section
        if (_suggestions.isNotEmpty) ...[
          const Padding(
            padding: EdgeInsets.all(16),
            child: Text(
              'Suggested for You',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
          ),
          ..._suggestions.map((user) => _buildUserTile(user)),
        ],

        // Empty state
        if (_searchHistory.isEmpty && _suggestions.isEmpty) ...[
          const SizedBox(height: 100),
          const Center(
            child: Column(
              children: [
                Icon(Icons.search, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  'Search for people',
                  style: TextStyle(fontSize: 18, color: Colors.grey),
                ),
                SizedBox(height: 8),
                Text(
                  'Start typing to find users by name or username',
                  style: TextStyle(fontSize: 14, color: Colors.grey),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildSuggestions() {
    // Fallback method for compatibility
    return _buildSuggestionsAndHistory();
  }
}

class Person {
  final String name;
  final String bio;
  final String avatarUrl;
  final bool discoverableByPhoto;

  const Person({
    required this.name,
    required this.bio,
    required this.avatarUrl,
    required this.discoverableByPhoto,
  });
}

class Post {
  final String caption;
  final String imageUrl;

  const Post({required this.caption, required this.imageUrl});
}

List<Post> _getMockPosts() {
  return [
    Post(
      caption: 'Amazing sunset view from my yacht',
      imageUrl: 'https://picsum.photos/seed/post1/300/300',
    ),
    Post(
      caption: 'Private jet life',
      imageUrl: 'https://picsum.photos/seed/post2/300/300',
    ),
    Post(
      caption: 'Luxury vacation in Monaco',
      imageUrl: 'https://picsum.photos/seed/post3/300/300',
    ),
  ];
}

List<String> _getMockTags() {
  return ['luxury', 'billionaire', 'yacht', 'privatejet', 'monaco'];
}
