import 'package:flutter/material.dart';
import 'package:billionaires_social/core/app_theme.dart';
import '../widgets/feature_list_widget.dart';

class LuxuryCarsScreen extends StatelessWidget {
  const LuxuryCarsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final carItems = [
      FeatureListItem(
        title: 'Lamborghini Aventador',
        subtitle: 'V12 power, iconic design',
        imageUrl:
            'https://images.unsplash.com/photo-1503736334956-4c8f8e92946d',
        onTap: () {},
        icon: Icons.directions_car,
      ),
      FeatureListItem(
        title: 'Rolls-Royce Phantom',
        subtitle: 'Ultimate luxury sedan',
        imageUrl:
            'https://images.unsplash.com/photo-1511918984145-48de785d4c4e',
        onTap: () {},
        icon: Icons.directions_car,
      ),
      FeatureListItem(
        title: 'Ferrari 488 Spider',
        subtitle: 'Open-top Italian performance',
        imageUrl:
            'https://images.unsplash.com/photo-1462392246754-28dfa2df8e6b',
        onTap: () {},
        icon: Icons.directions_car,
      ),
    ];

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Luxury Cars',
          style: AppTheme.fontStyles.title.copyWith(
            color: AppTheme.accentColor,
          ),
        ),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: FeatureListWidget(
          sectionTitle: 'Featured Cars',
          items: carItems,
        ),
      ),
    );
  }
}
