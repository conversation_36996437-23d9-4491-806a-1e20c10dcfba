import 'package:flutter/material.dart';
import 'package:billionaires_social/core/app_theme.dart';
import '../widgets/feature_list_widget.dart';

class RealEstateScreen extends StatelessWidget {
  const RealEstateScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final realEstateItems = [
      FeatureListItem(
        title: 'Penthouse Suite',
        subtitle: 'Skyline views in the heart of the city',
        imageUrl:
            'https://images.unsplash.com/photo-1507089947368-19c1da9775ae',
        onTap: () {},
        icon: Icons.apartment,
      ),
      FeatureListItem(
        title: 'Beachfront Villa',
        subtitle: 'Private luxury on the coast',
        imageUrl:
            'https://images.unsplash.com/photo-1460518451285-97b6aa326961',
        onTap: () {},
        icon: Icons.house,
      ),
      FeatureListItem(
        title: 'Mountain Retreat',
        subtitle: 'Secluded estate with breathtaking views',
        imageUrl:
            'https://images.unsplash.com/photo-1506744038136-46273834b3fb',
        onTap: () {},
        icon: Icons.terrain,
      ),
    ];

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Real Estate',
          style: AppTheme.fontStyles.title.copyWith(
            color: AppTheme.accentColor,
          ),
        ),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: FeatureListWidget(
          sectionTitle: 'Featured Properties',
          items: realEstateItems,
        ),
      ),
    );
  }
}
