import 'package:freezed_annotation/freezed_annotation.dart';

part 'place_model.freezed.dart';
part 'place_model.g.dart';

@freezed
abstract class PlaceUser with _$PlaceUser {
  const factory PlaceUser({
    required String id,
    required String name,
    required String avatarUrl,
  }) = _PlaceUser;

  factory PlaceUser.fromJson(Map<String, dynamic> json) =>
      _$PlaceUserFromJson(json);
}

enum PlaceCategory {
  restaurants,
  events,
  nightlife,
  cafes,
  resorts,
  concerts,
  shopping,
  shows,
  villas,
  yachts,
  privateJets,
  islands,
  luxuryCars,
  artGalleries,
  exclusiveClubs,
  spas,
}

enum JoinStatus { joined, attended, missed, cancelled }

enum BookingStatus { available, booked, pending, confirmed, cancelled }

enum PlaceType { event, location, experience, accommodation }

@freezed
abstract class Place with _$Place {
  const factory Place({
    required String id,
    required String name,
    required String imageUrl,
    required PlaceCategory category,
    required DateTime eventDate,
    @Default([]) List<PlaceUser> attendees,
    @Default(false) bool isTrending,
    @Default(false) bool isNew,
    @Default(PlaceType.event) PlaceType type,
    String? description,
    String? location,
    double? latitude,
    double? longitude,
    double? price,
    String? currency,
    @Default([]) List<String> imageUrls,
    @Default([]) List<String> amenities,
    @Default(false) bool isVerified,
    @Default(false) bool isExclusive,
    @Default(BookingStatus.available) BookingStatus bookingStatus,
    int? capacity,
    int? currentBookings,
    String? ownerId,
    String? ownerName,
    String? ownerAvatarUrl,
    @Default(0.0) double rating,
    int? reviewCount,
    DateTime? lastUpdated,
  }) = _Place;

  factory Place.fromJson(Map<String, dynamic> json) => _$PlaceFromJson(json);
}

@freezed
abstract class PlaceJoin with _$PlaceJoin {
  const factory PlaceJoin({
    required String id,
    required String userId,
    required String placeId,
    required DateTime joinDate,
    required DateTime visitDate,
    @Default(JoinStatus.joined) JoinStatus status,
    String? notes,
  }) = _PlaceJoin;

  factory PlaceJoin.fromJson(Map<String, dynamic> json) =>
      _$PlaceJoinFromJson(json);
}

@freezed
abstract class UserJoinStats with _$UserJoinStats {
  const factory UserJoinStats({
    required String userId,
    @Default(0) int missedJoins,
    DateTime? restrictionEndDate,
  }) = _UserJoinStats;

  factory UserJoinStats.fromJson(Map<String, dynamic> json) =>
      _$UserJoinStatsFromJson(json);
}

@freezed
abstract class Booking with _$Booking {
  const factory Booking({
    required String id,
    required String userId,
    required String placeId,
    required DateTime bookingDate,
    required DateTime visitDate,
    required int guestCount,
    @Default(BookingStatus.pending) BookingStatus status,
    double? totalPrice,
    String? currency,
    String? specialRequests,
    String? contactPhone,
    String? contactEmail,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _Booking;

  factory Booking.fromJson(Map<String, dynamic> json) =>
      _$BookingFromJson(json);
}

@freezed
abstract class BookingRequest with _$BookingRequest {
  const factory BookingRequest({
    required String placeId,
    required DateTime visitDate,
    required int guestCount,
    String? specialRequests,
    String? contactPhone,
    String? contactEmail,
  }) = _BookingRequest;

  factory BookingRequest.fromJson(Map<String, dynamic> json) =>
      _$BookingRequestFromJson(json);
}

@freezed
abstract class PlacesFilter with _$PlacesFilter {
  const factory PlacesFilter({
    @Default({}) Set<PlaceCategory> categories,
    @Default({}) Set<PlaceType> types,
    DateTime? startDate,
    DateTime? endDate,
    double? minPrice,
    double? maxPrice,
    @Default(false) bool verifiedOnly,
    @Default(false) bool exclusiveOnly,
    @Default(false) bool trendingOnly,
    String? searchQuery,
    String? location,
    double? latitude,
    double? longitude,
    double? radius,
    @Default(0.0) double minRating,
  }) = _PlacesFilter;

  factory PlacesFilter.fromJson(Map<String, dynamic> json) =>
      _$PlacesFilterFromJson(json);
}
