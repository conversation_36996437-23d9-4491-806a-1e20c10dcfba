// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'place_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PlaceUser {

 String get id; String get name; String get avatarUrl;
/// Create a copy of PlaceUser
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PlaceUserCopyWith<PlaceUser> get copyWith => _$PlaceUserCopyWithImpl<PlaceUser>(this as PlaceUser, _$identity);

  /// Serializes this PlaceUser to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PlaceUser&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.avatarUrl, avatarUrl) || other.avatarUrl == avatarUrl));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,avatarUrl);

@override
String toString() {
  return 'PlaceUser(id: $id, name: $name, avatarUrl: $avatarUrl)';
}


}

/// @nodoc
abstract mixin class $PlaceUserCopyWith<$Res>  {
  factory $PlaceUserCopyWith(PlaceUser value, $Res Function(PlaceUser) _then) = _$PlaceUserCopyWithImpl;
@useResult
$Res call({
 String id, String name, String avatarUrl
});




}
/// @nodoc
class _$PlaceUserCopyWithImpl<$Res>
    implements $PlaceUserCopyWith<$Res> {
  _$PlaceUserCopyWithImpl(this._self, this._then);

  final PlaceUser _self;
  final $Res Function(PlaceUser) _then;

/// Create a copy of PlaceUser
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? avatarUrl = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,avatarUrl: null == avatarUrl ? _self.avatarUrl : avatarUrl // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [PlaceUser].
extension PlaceUserPatterns on PlaceUser {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PlaceUser value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PlaceUser() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PlaceUser value)  $default,){
final _that = this;
switch (_that) {
case _PlaceUser():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PlaceUser value)?  $default,){
final _that = this;
switch (_that) {
case _PlaceUser() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  String avatarUrl)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PlaceUser() when $default != null:
return $default(_that.id,_that.name,_that.avatarUrl);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  String avatarUrl)  $default,) {final _that = this;
switch (_that) {
case _PlaceUser():
return $default(_that.id,_that.name,_that.avatarUrl);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  String avatarUrl)?  $default,) {final _that = this;
switch (_that) {
case _PlaceUser() when $default != null:
return $default(_that.id,_that.name,_that.avatarUrl);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PlaceUser implements PlaceUser {
  const _PlaceUser({required this.id, required this.name, required this.avatarUrl});
  factory _PlaceUser.fromJson(Map<String, dynamic> json) => _$PlaceUserFromJson(json);

@override final  String id;
@override final  String name;
@override final  String avatarUrl;

/// Create a copy of PlaceUser
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PlaceUserCopyWith<_PlaceUser> get copyWith => __$PlaceUserCopyWithImpl<_PlaceUser>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PlaceUserToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PlaceUser&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.avatarUrl, avatarUrl) || other.avatarUrl == avatarUrl));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,avatarUrl);

@override
String toString() {
  return 'PlaceUser(id: $id, name: $name, avatarUrl: $avatarUrl)';
}


}

/// @nodoc
abstract mixin class _$PlaceUserCopyWith<$Res> implements $PlaceUserCopyWith<$Res> {
  factory _$PlaceUserCopyWith(_PlaceUser value, $Res Function(_PlaceUser) _then) = __$PlaceUserCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, String avatarUrl
});




}
/// @nodoc
class __$PlaceUserCopyWithImpl<$Res>
    implements _$PlaceUserCopyWith<$Res> {
  __$PlaceUserCopyWithImpl(this._self, this._then);

  final _PlaceUser _self;
  final $Res Function(_PlaceUser) _then;

/// Create a copy of PlaceUser
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? avatarUrl = null,}) {
  return _then(_PlaceUser(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,avatarUrl: null == avatarUrl ? _self.avatarUrl : avatarUrl // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$Place {

 String get id; String get name; String get imageUrl; PlaceCategory get category; DateTime get eventDate; List<PlaceUser> get attendees; bool get isTrending; bool get isNew; PlaceType get type; String? get description; String? get location; double? get latitude; double? get longitude; double? get price; String? get currency; List<String> get imageUrls; List<String> get amenities; bool get isVerified; bool get isExclusive; BookingStatus get bookingStatus; int? get capacity; int? get currentBookings; String? get ownerId; String? get ownerName; String? get ownerAvatarUrl; double get rating; int? get reviewCount; DateTime? get lastUpdated;
/// Create a copy of Place
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PlaceCopyWith<Place> get copyWith => _$PlaceCopyWithImpl<Place>(this as Place, _$identity);

  /// Serializes this Place to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Place&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl)&&(identical(other.category, category) || other.category == category)&&(identical(other.eventDate, eventDate) || other.eventDate == eventDate)&&const DeepCollectionEquality().equals(other.attendees, attendees)&&(identical(other.isTrending, isTrending) || other.isTrending == isTrending)&&(identical(other.isNew, isNew) || other.isNew == isNew)&&(identical(other.type, type) || other.type == type)&&(identical(other.description, description) || other.description == description)&&(identical(other.location, location) || other.location == location)&&(identical(other.latitude, latitude) || other.latitude == latitude)&&(identical(other.longitude, longitude) || other.longitude == longitude)&&(identical(other.price, price) || other.price == price)&&(identical(other.currency, currency) || other.currency == currency)&&const DeepCollectionEquality().equals(other.imageUrls, imageUrls)&&const DeepCollectionEquality().equals(other.amenities, amenities)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.isExclusive, isExclusive) || other.isExclusive == isExclusive)&&(identical(other.bookingStatus, bookingStatus) || other.bookingStatus == bookingStatus)&&(identical(other.capacity, capacity) || other.capacity == capacity)&&(identical(other.currentBookings, currentBookings) || other.currentBookings == currentBookings)&&(identical(other.ownerId, ownerId) || other.ownerId == ownerId)&&(identical(other.ownerName, ownerName) || other.ownerName == ownerName)&&(identical(other.ownerAvatarUrl, ownerAvatarUrl) || other.ownerAvatarUrl == ownerAvatarUrl)&&(identical(other.rating, rating) || other.rating == rating)&&(identical(other.reviewCount, reviewCount) || other.reviewCount == reviewCount)&&(identical(other.lastUpdated, lastUpdated) || other.lastUpdated == lastUpdated));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,name,imageUrl,category,eventDate,const DeepCollectionEquality().hash(attendees),isTrending,isNew,type,description,location,latitude,longitude,price,currency,const DeepCollectionEquality().hash(imageUrls),const DeepCollectionEquality().hash(amenities),isVerified,isExclusive,bookingStatus,capacity,currentBookings,ownerId,ownerName,ownerAvatarUrl,rating,reviewCount,lastUpdated]);

@override
String toString() {
  return 'Place(id: $id, name: $name, imageUrl: $imageUrl, category: $category, eventDate: $eventDate, attendees: $attendees, isTrending: $isTrending, isNew: $isNew, type: $type, description: $description, location: $location, latitude: $latitude, longitude: $longitude, price: $price, currency: $currency, imageUrls: $imageUrls, amenities: $amenities, isVerified: $isVerified, isExclusive: $isExclusive, bookingStatus: $bookingStatus, capacity: $capacity, currentBookings: $currentBookings, ownerId: $ownerId, ownerName: $ownerName, ownerAvatarUrl: $ownerAvatarUrl, rating: $rating, reviewCount: $reviewCount, lastUpdated: $lastUpdated)';
}


}

/// @nodoc
abstract mixin class $PlaceCopyWith<$Res>  {
  factory $PlaceCopyWith(Place value, $Res Function(Place) _then) = _$PlaceCopyWithImpl;
@useResult
$Res call({
 String id, String name, String imageUrl, PlaceCategory category, DateTime eventDate, List<PlaceUser> attendees, bool isTrending, bool isNew, PlaceType type, String? description, String? location, double? latitude, double? longitude, double? price, String? currency, List<String> imageUrls, List<String> amenities, bool isVerified, bool isExclusive, BookingStatus bookingStatus, int? capacity, int? currentBookings, String? ownerId, String? ownerName, String? ownerAvatarUrl, double rating, int? reviewCount, DateTime? lastUpdated
});




}
/// @nodoc
class _$PlaceCopyWithImpl<$Res>
    implements $PlaceCopyWith<$Res> {
  _$PlaceCopyWithImpl(this._self, this._then);

  final Place _self;
  final $Res Function(Place) _then;

/// Create a copy of Place
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? imageUrl = null,Object? category = null,Object? eventDate = null,Object? attendees = null,Object? isTrending = null,Object? isNew = null,Object? type = null,Object? description = freezed,Object? location = freezed,Object? latitude = freezed,Object? longitude = freezed,Object? price = freezed,Object? currency = freezed,Object? imageUrls = null,Object? amenities = null,Object? isVerified = null,Object? isExclusive = null,Object? bookingStatus = null,Object? capacity = freezed,Object? currentBookings = freezed,Object? ownerId = freezed,Object? ownerName = freezed,Object? ownerAvatarUrl = freezed,Object? rating = null,Object? reviewCount = freezed,Object? lastUpdated = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,imageUrl: null == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String,category: null == category ? _self.category : category // ignore: cast_nullable_to_non_nullable
as PlaceCategory,eventDate: null == eventDate ? _self.eventDate : eventDate // ignore: cast_nullable_to_non_nullable
as DateTime,attendees: null == attendees ? _self.attendees : attendees // ignore: cast_nullable_to_non_nullable
as List<PlaceUser>,isTrending: null == isTrending ? _self.isTrending : isTrending // ignore: cast_nullable_to_non_nullable
as bool,isNew: null == isNew ? _self.isNew : isNew // ignore: cast_nullable_to_non_nullable
as bool,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as PlaceType,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,location: freezed == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String?,latitude: freezed == latitude ? _self.latitude : latitude // ignore: cast_nullable_to_non_nullable
as double?,longitude: freezed == longitude ? _self.longitude : longitude // ignore: cast_nullable_to_non_nullable
as double?,price: freezed == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double?,currency: freezed == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String?,imageUrls: null == imageUrls ? _self.imageUrls : imageUrls // ignore: cast_nullable_to_non_nullable
as List<String>,amenities: null == amenities ? _self.amenities : amenities // ignore: cast_nullable_to_non_nullable
as List<String>,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,isExclusive: null == isExclusive ? _self.isExclusive : isExclusive // ignore: cast_nullable_to_non_nullable
as bool,bookingStatus: null == bookingStatus ? _self.bookingStatus : bookingStatus // ignore: cast_nullable_to_non_nullable
as BookingStatus,capacity: freezed == capacity ? _self.capacity : capacity // ignore: cast_nullable_to_non_nullable
as int?,currentBookings: freezed == currentBookings ? _self.currentBookings : currentBookings // ignore: cast_nullable_to_non_nullable
as int?,ownerId: freezed == ownerId ? _self.ownerId : ownerId // ignore: cast_nullable_to_non_nullable
as String?,ownerName: freezed == ownerName ? _self.ownerName : ownerName // ignore: cast_nullable_to_non_nullable
as String?,ownerAvatarUrl: freezed == ownerAvatarUrl ? _self.ownerAvatarUrl : ownerAvatarUrl // ignore: cast_nullable_to_non_nullable
as String?,rating: null == rating ? _self.rating : rating // ignore: cast_nullable_to_non_nullable
as double,reviewCount: freezed == reviewCount ? _self.reviewCount : reviewCount // ignore: cast_nullable_to_non_nullable
as int?,lastUpdated: freezed == lastUpdated ? _self.lastUpdated : lastUpdated // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [Place].
extension PlacePatterns on Place {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Place value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Place() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Place value)  $default,){
final _that = this;
switch (_that) {
case _Place():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Place value)?  $default,){
final _that = this;
switch (_that) {
case _Place() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  String imageUrl,  PlaceCategory category,  DateTime eventDate,  List<PlaceUser> attendees,  bool isTrending,  bool isNew,  PlaceType type,  String? description,  String? location,  double? latitude,  double? longitude,  double? price,  String? currency,  List<String> imageUrls,  List<String> amenities,  bool isVerified,  bool isExclusive,  BookingStatus bookingStatus,  int? capacity,  int? currentBookings,  String? ownerId,  String? ownerName,  String? ownerAvatarUrl,  double rating,  int? reviewCount,  DateTime? lastUpdated)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Place() when $default != null:
return $default(_that.id,_that.name,_that.imageUrl,_that.category,_that.eventDate,_that.attendees,_that.isTrending,_that.isNew,_that.type,_that.description,_that.location,_that.latitude,_that.longitude,_that.price,_that.currency,_that.imageUrls,_that.amenities,_that.isVerified,_that.isExclusive,_that.bookingStatus,_that.capacity,_that.currentBookings,_that.ownerId,_that.ownerName,_that.ownerAvatarUrl,_that.rating,_that.reviewCount,_that.lastUpdated);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  String imageUrl,  PlaceCategory category,  DateTime eventDate,  List<PlaceUser> attendees,  bool isTrending,  bool isNew,  PlaceType type,  String? description,  String? location,  double? latitude,  double? longitude,  double? price,  String? currency,  List<String> imageUrls,  List<String> amenities,  bool isVerified,  bool isExclusive,  BookingStatus bookingStatus,  int? capacity,  int? currentBookings,  String? ownerId,  String? ownerName,  String? ownerAvatarUrl,  double rating,  int? reviewCount,  DateTime? lastUpdated)  $default,) {final _that = this;
switch (_that) {
case _Place():
return $default(_that.id,_that.name,_that.imageUrl,_that.category,_that.eventDate,_that.attendees,_that.isTrending,_that.isNew,_that.type,_that.description,_that.location,_that.latitude,_that.longitude,_that.price,_that.currency,_that.imageUrls,_that.amenities,_that.isVerified,_that.isExclusive,_that.bookingStatus,_that.capacity,_that.currentBookings,_that.ownerId,_that.ownerName,_that.ownerAvatarUrl,_that.rating,_that.reviewCount,_that.lastUpdated);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  String imageUrl,  PlaceCategory category,  DateTime eventDate,  List<PlaceUser> attendees,  bool isTrending,  bool isNew,  PlaceType type,  String? description,  String? location,  double? latitude,  double? longitude,  double? price,  String? currency,  List<String> imageUrls,  List<String> amenities,  bool isVerified,  bool isExclusive,  BookingStatus bookingStatus,  int? capacity,  int? currentBookings,  String? ownerId,  String? ownerName,  String? ownerAvatarUrl,  double rating,  int? reviewCount,  DateTime? lastUpdated)?  $default,) {final _that = this;
switch (_that) {
case _Place() when $default != null:
return $default(_that.id,_that.name,_that.imageUrl,_that.category,_that.eventDate,_that.attendees,_that.isTrending,_that.isNew,_that.type,_that.description,_that.location,_that.latitude,_that.longitude,_that.price,_that.currency,_that.imageUrls,_that.amenities,_that.isVerified,_that.isExclusive,_that.bookingStatus,_that.capacity,_that.currentBookings,_that.ownerId,_that.ownerName,_that.ownerAvatarUrl,_that.rating,_that.reviewCount,_that.lastUpdated);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Place implements Place {
  const _Place({required this.id, required this.name, required this.imageUrl, required this.category, required this.eventDate, final  List<PlaceUser> attendees = const [], this.isTrending = false, this.isNew = false, this.type = PlaceType.event, this.description, this.location, this.latitude, this.longitude, this.price, this.currency, final  List<String> imageUrls = const [], final  List<String> amenities = const [], this.isVerified = false, this.isExclusive = false, this.bookingStatus = BookingStatus.available, this.capacity, this.currentBookings, this.ownerId, this.ownerName, this.ownerAvatarUrl, this.rating = 0.0, this.reviewCount, this.lastUpdated}): _attendees = attendees,_imageUrls = imageUrls,_amenities = amenities;
  factory _Place.fromJson(Map<String, dynamic> json) => _$PlaceFromJson(json);

@override final  String id;
@override final  String name;
@override final  String imageUrl;
@override final  PlaceCategory category;
@override final  DateTime eventDate;
 final  List<PlaceUser> _attendees;
@override@JsonKey() List<PlaceUser> get attendees {
  if (_attendees is EqualUnmodifiableListView) return _attendees;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_attendees);
}

@override@JsonKey() final  bool isTrending;
@override@JsonKey() final  bool isNew;
@override@JsonKey() final  PlaceType type;
@override final  String? description;
@override final  String? location;
@override final  double? latitude;
@override final  double? longitude;
@override final  double? price;
@override final  String? currency;
 final  List<String> _imageUrls;
@override@JsonKey() List<String> get imageUrls {
  if (_imageUrls is EqualUnmodifiableListView) return _imageUrls;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_imageUrls);
}

 final  List<String> _amenities;
@override@JsonKey() List<String> get amenities {
  if (_amenities is EqualUnmodifiableListView) return _amenities;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_amenities);
}

@override@JsonKey() final  bool isVerified;
@override@JsonKey() final  bool isExclusive;
@override@JsonKey() final  BookingStatus bookingStatus;
@override final  int? capacity;
@override final  int? currentBookings;
@override final  String? ownerId;
@override final  String? ownerName;
@override final  String? ownerAvatarUrl;
@override@JsonKey() final  double rating;
@override final  int? reviewCount;
@override final  DateTime? lastUpdated;

/// Create a copy of Place
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PlaceCopyWith<_Place> get copyWith => __$PlaceCopyWithImpl<_Place>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PlaceToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Place&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl)&&(identical(other.category, category) || other.category == category)&&(identical(other.eventDate, eventDate) || other.eventDate == eventDate)&&const DeepCollectionEquality().equals(other._attendees, _attendees)&&(identical(other.isTrending, isTrending) || other.isTrending == isTrending)&&(identical(other.isNew, isNew) || other.isNew == isNew)&&(identical(other.type, type) || other.type == type)&&(identical(other.description, description) || other.description == description)&&(identical(other.location, location) || other.location == location)&&(identical(other.latitude, latitude) || other.latitude == latitude)&&(identical(other.longitude, longitude) || other.longitude == longitude)&&(identical(other.price, price) || other.price == price)&&(identical(other.currency, currency) || other.currency == currency)&&const DeepCollectionEquality().equals(other._imageUrls, _imageUrls)&&const DeepCollectionEquality().equals(other._amenities, _amenities)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.isExclusive, isExclusive) || other.isExclusive == isExclusive)&&(identical(other.bookingStatus, bookingStatus) || other.bookingStatus == bookingStatus)&&(identical(other.capacity, capacity) || other.capacity == capacity)&&(identical(other.currentBookings, currentBookings) || other.currentBookings == currentBookings)&&(identical(other.ownerId, ownerId) || other.ownerId == ownerId)&&(identical(other.ownerName, ownerName) || other.ownerName == ownerName)&&(identical(other.ownerAvatarUrl, ownerAvatarUrl) || other.ownerAvatarUrl == ownerAvatarUrl)&&(identical(other.rating, rating) || other.rating == rating)&&(identical(other.reviewCount, reviewCount) || other.reviewCount == reviewCount)&&(identical(other.lastUpdated, lastUpdated) || other.lastUpdated == lastUpdated));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,name,imageUrl,category,eventDate,const DeepCollectionEquality().hash(_attendees),isTrending,isNew,type,description,location,latitude,longitude,price,currency,const DeepCollectionEquality().hash(_imageUrls),const DeepCollectionEquality().hash(_amenities),isVerified,isExclusive,bookingStatus,capacity,currentBookings,ownerId,ownerName,ownerAvatarUrl,rating,reviewCount,lastUpdated]);

@override
String toString() {
  return 'Place(id: $id, name: $name, imageUrl: $imageUrl, category: $category, eventDate: $eventDate, attendees: $attendees, isTrending: $isTrending, isNew: $isNew, type: $type, description: $description, location: $location, latitude: $latitude, longitude: $longitude, price: $price, currency: $currency, imageUrls: $imageUrls, amenities: $amenities, isVerified: $isVerified, isExclusive: $isExclusive, bookingStatus: $bookingStatus, capacity: $capacity, currentBookings: $currentBookings, ownerId: $ownerId, ownerName: $ownerName, ownerAvatarUrl: $ownerAvatarUrl, rating: $rating, reviewCount: $reviewCount, lastUpdated: $lastUpdated)';
}


}

/// @nodoc
abstract mixin class _$PlaceCopyWith<$Res> implements $PlaceCopyWith<$Res> {
  factory _$PlaceCopyWith(_Place value, $Res Function(_Place) _then) = __$PlaceCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, String imageUrl, PlaceCategory category, DateTime eventDate, List<PlaceUser> attendees, bool isTrending, bool isNew, PlaceType type, String? description, String? location, double? latitude, double? longitude, double? price, String? currency, List<String> imageUrls, List<String> amenities, bool isVerified, bool isExclusive, BookingStatus bookingStatus, int? capacity, int? currentBookings, String? ownerId, String? ownerName, String? ownerAvatarUrl, double rating, int? reviewCount, DateTime? lastUpdated
});




}
/// @nodoc
class __$PlaceCopyWithImpl<$Res>
    implements _$PlaceCopyWith<$Res> {
  __$PlaceCopyWithImpl(this._self, this._then);

  final _Place _self;
  final $Res Function(_Place) _then;

/// Create a copy of Place
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? imageUrl = null,Object? category = null,Object? eventDate = null,Object? attendees = null,Object? isTrending = null,Object? isNew = null,Object? type = null,Object? description = freezed,Object? location = freezed,Object? latitude = freezed,Object? longitude = freezed,Object? price = freezed,Object? currency = freezed,Object? imageUrls = null,Object? amenities = null,Object? isVerified = null,Object? isExclusive = null,Object? bookingStatus = null,Object? capacity = freezed,Object? currentBookings = freezed,Object? ownerId = freezed,Object? ownerName = freezed,Object? ownerAvatarUrl = freezed,Object? rating = null,Object? reviewCount = freezed,Object? lastUpdated = freezed,}) {
  return _then(_Place(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,imageUrl: null == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String,category: null == category ? _self.category : category // ignore: cast_nullable_to_non_nullable
as PlaceCategory,eventDate: null == eventDate ? _self.eventDate : eventDate // ignore: cast_nullable_to_non_nullable
as DateTime,attendees: null == attendees ? _self._attendees : attendees // ignore: cast_nullable_to_non_nullable
as List<PlaceUser>,isTrending: null == isTrending ? _self.isTrending : isTrending // ignore: cast_nullable_to_non_nullable
as bool,isNew: null == isNew ? _self.isNew : isNew // ignore: cast_nullable_to_non_nullable
as bool,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as PlaceType,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,location: freezed == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String?,latitude: freezed == latitude ? _self.latitude : latitude // ignore: cast_nullable_to_non_nullable
as double?,longitude: freezed == longitude ? _self.longitude : longitude // ignore: cast_nullable_to_non_nullable
as double?,price: freezed == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double?,currency: freezed == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String?,imageUrls: null == imageUrls ? _self._imageUrls : imageUrls // ignore: cast_nullable_to_non_nullable
as List<String>,amenities: null == amenities ? _self._amenities : amenities // ignore: cast_nullable_to_non_nullable
as List<String>,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,isExclusive: null == isExclusive ? _self.isExclusive : isExclusive // ignore: cast_nullable_to_non_nullable
as bool,bookingStatus: null == bookingStatus ? _self.bookingStatus : bookingStatus // ignore: cast_nullable_to_non_nullable
as BookingStatus,capacity: freezed == capacity ? _self.capacity : capacity // ignore: cast_nullable_to_non_nullable
as int?,currentBookings: freezed == currentBookings ? _self.currentBookings : currentBookings // ignore: cast_nullable_to_non_nullable
as int?,ownerId: freezed == ownerId ? _self.ownerId : ownerId // ignore: cast_nullable_to_non_nullable
as String?,ownerName: freezed == ownerName ? _self.ownerName : ownerName // ignore: cast_nullable_to_non_nullable
as String?,ownerAvatarUrl: freezed == ownerAvatarUrl ? _self.ownerAvatarUrl : ownerAvatarUrl // ignore: cast_nullable_to_non_nullable
as String?,rating: null == rating ? _self.rating : rating // ignore: cast_nullable_to_non_nullable
as double,reviewCount: freezed == reviewCount ? _self.reviewCount : reviewCount // ignore: cast_nullable_to_non_nullable
as int?,lastUpdated: freezed == lastUpdated ? _self.lastUpdated : lastUpdated // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$PlaceJoin {

 String get id; String get userId; String get placeId; DateTime get joinDate; DateTime get visitDate; JoinStatus get status; String? get notes;
/// Create a copy of PlaceJoin
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PlaceJoinCopyWith<PlaceJoin> get copyWith => _$PlaceJoinCopyWithImpl<PlaceJoin>(this as PlaceJoin, _$identity);

  /// Serializes this PlaceJoin to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PlaceJoin&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.placeId, placeId) || other.placeId == placeId)&&(identical(other.joinDate, joinDate) || other.joinDate == joinDate)&&(identical(other.visitDate, visitDate) || other.visitDate == visitDate)&&(identical(other.status, status) || other.status == status)&&(identical(other.notes, notes) || other.notes == notes));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,placeId,joinDate,visitDate,status,notes);

@override
String toString() {
  return 'PlaceJoin(id: $id, userId: $userId, placeId: $placeId, joinDate: $joinDate, visitDate: $visitDate, status: $status, notes: $notes)';
}


}

/// @nodoc
abstract mixin class $PlaceJoinCopyWith<$Res>  {
  factory $PlaceJoinCopyWith(PlaceJoin value, $Res Function(PlaceJoin) _then) = _$PlaceJoinCopyWithImpl;
@useResult
$Res call({
 String id, String userId, String placeId, DateTime joinDate, DateTime visitDate, JoinStatus status, String? notes
});




}
/// @nodoc
class _$PlaceJoinCopyWithImpl<$Res>
    implements $PlaceJoinCopyWith<$Res> {
  _$PlaceJoinCopyWithImpl(this._self, this._then);

  final PlaceJoin _self;
  final $Res Function(PlaceJoin) _then;

/// Create a copy of PlaceJoin
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? placeId = null,Object? joinDate = null,Object? visitDate = null,Object? status = null,Object? notes = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,placeId: null == placeId ? _self.placeId : placeId // ignore: cast_nullable_to_non_nullable
as String,joinDate: null == joinDate ? _self.joinDate : joinDate // ignore: cast_nullable_to_non_nullable
as DateTime,visitDate: null == visitDate ? _self.visitDate : visitDate // ignore: cast_nullable_to_non_nullable
as DateTime,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as JoinStatus,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [PlaceJoin].
extension PlaceJoinPatterns on PlaceJoin {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PlaceJoin value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PlaceJoin() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PlaceJoin value)  $default,){
final _that = this;
switch (_that) {
case _PlaceJoin():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PlaceJoin value)?  $default,){
final _that = this;
switch (_that) {
case _PlaceJoin() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  String placeId,  DateTime joinDate,  DateTime visitDate,  JoinStatus status,  String? notes)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PlaceJoin() when $default != null:
return $default(_that.id,_that.userId,_that.placeId,_that.joinDate,_that.visitDate,_that.status,_that.notes);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  String placeId,  DateTime joinDate,  DateTime visitDate,  JoinStatus status,  String? notes)  $default,) {final _that = this;
switch (_that) {
case _PlaceJoin():
return $default(_that.id,_that.userId,_that.placeId,_that.joinDate,_that.visitDate,_that.status,_that.notes);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  String placeId,  DateTime joinDate,  DateTime visitDate,  JoinStatus status,  String? notes)?  $default,) {final _that = this;
switch (_that) {
case _PlaceJoin() when $default != null:
return $default(_that.id,_that.userId,_that.placeId,_that.joinDate,_that.visitDate,_that.status,_that.notes);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PlaceJoin implements PlaceJoin {
  const _PlaceJoin({required this.id, required this.userId, required this.placeId, required this.joinDate, required this.visitDate, this.status = JoinStatus.joined, this.notes});
  factory _PlaceJoin.fromJson(Map<String, dynamic> json) => _$PlaceJoinFromJson(json);

@override final  String id;
@override final  String userId;
@override final  String placeId;
@override final  DateTime joinDate;
@override final  DateTime visitDate;
@override@JsonKey() final  JoinStatus status;
@override final  String? notes;

/// Create a copy of PlaceJoin
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PlaceJoinCopyWith<_PlaceJoin> get copyWith => __$PlaceJoinCopyWithImpl<_PlaceJoin>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PlaceJoinToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PlaceJoin&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.placeId, placeId) || other.placeId == placeId)&&(identical(other.joinDate, joinDate) || other.joinDate == joinDate)&&(identical(other.visitDate, visitDate) || other.visitDate == visitDate)&&(identical(other.status, status) || other.status == status)&&(identical(other.notes, notes) || other.notes == notes));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,placeId,joinDate,visitDate,status,notes);

@override
String toString() {
  return 'PlaceJoin(id: $id, userId: $userId, placeId: $placeId, joinDate: $joinDate, visitDate: $visitDate, status: $status, notes: $notes)';
}


}

/// @nodoc
abstract mixin class _$PlaceJoinCopyWith<$Res> implements $PlaceJoinCopyWith<$Res> {
  factory _$PlaceJoinCopyWith(_PlaceJoin value, $Res Function(_PlaceJoin) _then) = __$PlaceJoinCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, String placeId, DateTime joinDate, DateTime visitDate, JoinStatus status, String? notes
});




}
/// @nodoc
class __$PlaceJoinCopyWithImpl<$Res>
    implements _$PlaceJoinCopyWith<$Res> {
  __$PlaceJoinCopyWithImpl(this._self, this._then);

  final _PlaceJoin _self;
  final $Res Function(_PlaceJoin) _then;

/// Create a copy of PlaceJoin
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? placeId = null,Object? joinDate = null,Object? visitDate = null,Object? status = null,Object? notes = freezed,}) {
  return _then(_PlaceJoin(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,placeId: null == placeId ? _self.placeId : placeId // ignore: cast_nullable_to_non_nullable
as String,joinDate: null == joinDate ? _self.joinDate : joinDate // ignore: cast_nullable_to_non_nullable
as DateTime,visitDate: null == visitDate ? _self.visitDate : visitDate // ignore: cast_nullable_to_non_nullable
as DateTime,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as JoinStatus,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$UserJoinStats {

 String get userId; int get missedJoins; DateTime? get restrictionEndDate;
/// Create a copy of UserJoinStats
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UserJoinStatsCopyWith<UserJoinStats> get copyWith => _$UserJoinStatsCopyWithImpl<UserJoinStats>(this as UserJoinStats, _$identity);

  /// Serializes this UserJoinStats to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UserJoinStats&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.missedJoins, missedJoins) || other.missedJoins == missedJoins)&&(identical(other.restrictionEndDate, restrictionEndDate) || other.restrictionEndDate == restrictionEndDate));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,userId,missedJoins,restrictionEndDate);

@override
String toString() {
  return 'UserJoinStats(userId: $userId, missedJoins: $missedJoins, restrictionEndDate: $restrictionEndDate)';
}


}

/// @nodoc
abstract mixin class $UserJoinStatsCopyWith<$Res>  {
  factory $UserJoinStatsCopyWith(UserJoinStats value, $Res Function(UserJoinStats) _then) = _$UserJoinStatsCopyWithImpl;
@useResult
$Res call({
 String userId, int missedJoins, DateTime? restrictionEndDate
});




}
/// @nodoc
class _$UserJoinStatsCopyWithImpl<$Res>
    implements $UserJoinStatsCopyWith<$Res> {
  _$UserJoinStatsCopyWithImpl(this._self, this._then);

  final UserJoinStats _self;
  final $Res Function(UserJoinStats) _then;

/// Create a copy of UserJoinStats
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? userId = null,Object? missedJoins = null,Object? restrictionEndDate = freezed,}) {
  return _then(_self.copyWith(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,missedJoins: null == missedJoins ? _self.missedJoins : missedJoins // ignore: cast_nullable_to_non_nullable
as int,restrictionEndDate: freezed == restrictionEndDate ? _self.restrictionEndDate : restrictionEndDate // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [UserJoinStats].
extension UserJoinStatsPatterns on UserJoinStats {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _UserJoinStats value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _UserJoinStats() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _UserJoinStats value)  $default,){
final _that = this;
switch (_that) {
case _UserJoinStats():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _UserJoinStats value)?  $default,){
final _that = this;
switch (_that) {
case _UserJoinStats() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String userId,  int missedJoins,  DateTime? restrictionEndDate)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _UserJoinStats() when $default != null:
return $default(_that.userId,_that.missedJoins,_that.restrictionEndDate);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String userId,  int missedJoins,  DateTime? restrictionEndDate)  $default,) {final _that = this;
switch (_that) {
case _UserJoinStats():
return $default(_that.userId,_that.missedJoins,_that.restrictionEndDate);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String userId,  int missedJoins,  DateTime? restrictionEndDate)?  $default,) {final _that = this;
switch (_that) {
case _UserJoinStats() when $default != null:
return $default(_that.userId,_that.missedJoins,_that.restrictionEndDate);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _UserJoinStats implements UserJoinStats {
  const _UserJoinStats({required this.userId, this.missedJoins = 0, this.restrictionEndDate});
  factory _UserJoinStats.fromJson(Map<String, dynamic> json) => _$UserJoinStatsFromJson(json);

@override final  String userId;
@override@JsonKey() final  int missedJoins;
@override final  DateTime? restrictionEndDate;

/// Create a copy of UserJoinStats
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UserJoinStatsCopyWith<_UserJoinStats> get copyWith => __$UserJoinStatsCopyWithImpl<_UserJoinStats>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UserJoinStatsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UserJoinStats&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.missedJoins, missedJoins) || other.missedJoins == missedJoins)&&(identical(other.restrictionEndDate, restrictionEndDate) || other.restrictionEndDate == restrictionEndDate));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,userId,missedJoins,restrictionEndDate);

@override
String toString() {
  return 'UserJoinStats(userId: $userId, missedJoins: $missedJoins, restrictionEndDate: $restrictionEndDate)';
}


}

/// @nodoc
abstract mixin class _$UserJoinStatsCopyWith<$Res> implements $UserJoinStatsCopyWith<$Res> {
  factory _$UserJoinStatsCopyWith(_UserJoinStats value, $Res Function(_UserJoinStats) _then) = __$UserJoinStatsCopyWithImpl;
@override @useResult
$Res call({
 String userId, int missedJoins, DateTime? restrictionEndDate
});




}
/// @nodoc
class __$UserJoinStatsCopyWithImpl<$Res>
    implements _$UserJoinStatsCopyWith<$Res> {
  __$UserJoinStatsCopyWithImpl(this._self, this._then);

  final _UserJoinStats _self;
  final $Res Function(_UserJoinStats) _then;

/// Create a copy of UserJoinStats
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? userId = null,Object? missedJoins = null,Object? restrictionEndDate = freezed,}) {
  return _then(_UserJoinStats(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,missedJoins: null == missedJoins ? _self.missedJoins : missedJoins // ignore: cast_nullable_to_non_nullable
as int,restrictionEndDate: freezed == restrictionEndDate ? _self.restrictionEndDate : restrictionEndDate // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$Booking {

 String get id; String get userId; String get placeId; DateTime get bookingDate; DateTime get visitDate; int get guestCount; BookingStatus get status; double? get totalPrice; String? get currency; String? get specialRequests; String? get contactPhone; String? get contactEmail; DateTime? get createdAt; DateTime? get updatedAt;
/// Create a copy of Booking
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BookingCopyWith<Booking> get copyWith => _$BookingCopyWithImpl<Booking>(this as Booking, _$identity);

  /// Serializes this Booking to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Booking&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.placeId, placeId) || other.placeId == placeId)&&(identical(other.bookingDate, bookingDate) || other.bookingDate == bookingDate)&&(identical(other.visitDate, visitDate) || other.visitDate == visitDate)&&(identical(other.guestCount, guestCount) || other.guestCount == guestCount)&&(identical(other.status, status) || other.status == status)&&(identical(other.totalPrice, totalPrice) || other.totalPrice == totalPrice)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.specialRequests, specialRequests) || other.specialRequests == specialRequests)&&(identical(other.contactPhone, contactPhone) || other.contactPhone == contactPhone)&&(identical(other.contactEmail, contactEmail) || other.contactEmail == contactEmail)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,placeId,bookingDate,visitDate,guestCount,status,totalPrice,currency,specialRequests,contactPhone,contactEmail,createdAt,updatedAt);

@override
String toString() {
  return 'Booking(id: $id, userId: $userId, placeId: $placeId, bookingDate: $bookingDate, visitDate: $visitDate, guestCount: $guestCount, status: $status, totalPrice: $totalPrice, currency: $currency, specialRequests: $specialRequests, contactPhone: $contactPhone, contactEmail: $contactEmail, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $BookingCopyWith<$Res>  {
  factory $BookingCopyWith(Booking value, $Res Function(Booking) _then) = _$BookingCopyWithImpl;
@useResult
$Res call({
 String id, String userId, String placeId, DateTime bookingDate, DateTime visitDate, int guestCount, BookingStatus status, double? totalPrice, String? currency, String? specialRequests, String? contactPhone, String? contactEmail, DateTime? createdAt, DateTime? updatedAt
});




}
/// @nodoc
class _$BookingCopyWithImpl<$Res>
    implements $BookingCopyWith<$Res> {
  _$BookingCopyWithImpl(this._self, this._then);

  final Booking _self;
  final $Res Function(Booking) _then;

/// Create a copy of Booking
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? placeId = null,Object? bookingDate = null,Object? visitDate = null,Object? guestCount = null,Object? status = null,Object? totalPrice = freezed,Object? currency = freezed,Object? specialRequests = freezed,Object? contactPhone = freezed,Object? contactEmail = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,placeId: null == placeId ? _self.placeId : placeId // ignore: cast_nullable_to_non_nullable
as String,bookingDate: null == bookingDate ? _self.bookingDate : bookingDate // ignore: cast_nullable_to_non_nullable
as DateTime,visitDate: null == visitDate ? _self.visitDate : visitDate // ignore: cast_nullable_to_non_nullable
as DateTime,guestCount: null == guestCount ? _self.guestCount : guestCount // ignore: cast_nullable_to_non_nullable
as int,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as BookingStatus,totalPrice: freezed == totalPrice ? _self.totalPrice : totalPrice // ignore: cast_nullable_to_non_nullable
as double?,currency: freezed == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String?,specialRequests: freezed == specialRequests ? _self.specialRequests : specialRequests // ignore: cast_nullable_to_non_nullable
as String?,contactPhone: freezed == contactPhone ? _self.contactPhone : contactPhone // ignore: cast_nullable_to_non_nullable
as String?,contactEmail: freezed == contactEmail ? _self.contactEmail : contactEmail // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [Booking].
extension BookingPatterns on Booking {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Booking value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Booking() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Booking value)  $default,){
final _that = this;
switch (_that) {
case _Booking():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Booking value)?  $default,){
final _that = this;
switch (_that) {
case _Booking() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  String placeId,  DateTime bookingDate,  DateTime visitDate,  int guestCount,  BookingStatus status,  double? totalPrice,  String? currency,  String? specialRequests,  String? contactPhone,  String? contactEmail,  DateTime? createdAt,  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Booking() when $default != null:
return $default(_that.id,_that.userId,_that.placeId,_that.bookingDate,_that.visitDate,_that.guestCount,_that.status,_that.totalPrice,_that.currency,_that.specialRequests,_that.contactPhone,_that.contactEmail,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  String placeId,  DateTime bookingDate,  DateTime visitDate,  int guestCount,  BookingStatus status,  double? totalPrice,  String? currency,  String? specialRequests,  String? contactPhone,  String? contactEmail,  DateTime? createdAt,  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _Booking():
return $default(_that.id,_that.userId,_that.placeId,_that.bookingDate,_that.visitDate,_that.guestCount,_that.status,_that.totalPrice,_that.currency,_that.specialRequests,_that.contactPhone,_that.contactEmail,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  String placeId,  DateTime bookingDate,  DateTime visitDate,  int guestCount,  BookingStatus status,  double? totalPrice,  String? currency,  String? specialRequests,  String? contactPhone,  String? contactEmail,  DateTime? createdAt,  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _Booking() when $default != null:
return $default(_that.id,_that.userId,_that.placeId,_that.bookingDate,_that.visitDate,_that.guestCount,_that.status,_that.totalPrice,_that.currency,_that.specialRequests,_that.contactPhone,_that.contactEmail,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Booking implements Booking {
  const _Booking({required this.id, required this.userId, required this.placeId, required this.bookingDate, required this.visitDate, required this.guestCount, this.status = BookingStatus.pending, this.totalPrice, this.currency, this.specialRequests, this.contactPhone, this.contactEmail, this.createdAt, this.updatedAt});
  factory _Booking.fromJson(Map<String, dynamic> json) => _$BookingFromJson(json);

@override final  String id;
@override final  String userId;
@override final  String placeId;
@override final  DateTime bookingDate;
@override final  DateTime visitDate;
@override final  int guestCount;
@override@JsonKey() final  BookingStatus status;
@override final  double? totalPrice;
@override final  String? currency;
@override final  String? specialRequests;
@override final  String? contactPhone;
@override final  String? contactEmail;
@override final  DateTime? createdAt;
@override final  DateTime? updatedAt;

/// Create a copy of Booking
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BookingCopyWith<_Booking> get copyWith => __$BookingCopyWithImpl<_Booking>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BookingToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Booking&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.placeId, placeId) || other.placeId == placeId)&&(identical(other.bookingDate, bookingDate) || other.bookingDate == bookingDate)&&(identical(other.visitDate, visitDate) || other.visitDate == visitDate)&&(identical(other.guestCount, guestCount) || other.guestCount == guestCount)&&(identical(other.status, status) || other.status == status)&&(identical(other.totalPrice, totalPrice) || other.totalPrice == totalPrice)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.specialRequests, specialRequests) || other.specialRequests == specialRequests)&&(identical(other.contactPhone, contactPhone) || other.contactPhone == contactPhone)&&(identical(other.contactEmail, contactEmail) || other.contactEmail == contactEmail)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,placeId,bookingDate,visitDate,guestCount,status,totalPrice,currency,specialRequests,contactPhone,contactEmail,createdAt,updatedAt);

@override
String toString() {
  return 'Booking(id: $id, userId: $userId, placeId: $placeId, bookingDate: $bookingDate, visitDate: $visitDate, guestCount: $guestCount, status: $status, totalPrice: $totalPrice, currency: $currency, specialRequests: $specialRequests, contactPhone: $contactPhone, contactEmail: $contactEmail, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$BookingCopyWith<$Res> implements $BookingCopyWith<$Res> {
  factory _$BookingCopyWith(_Booking value, $Res Function(_Booking) _then) = __$BookingCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, String placeId, DateTime bookingDate, DateTime visitDate, int guestCount, BookingStatus status, double? totalPrice, String? currency, String? specialRequests, String? contactPhone, String? contactEmail, DateTime? createdAt, DateTime? updatedAt
});




}
/// @nodoc
class __$BookingCopyWithImpl<$Res>
    implements _$BookingCopyWith<$Res> {
  __$BookingCopyWithImpl(this._self, this._then);

  final _Booking _self;
  final $Res Function(_Booking) _then;

/// Create a copy of Booking
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? placeId = null,Object? bookingDate = null,Object? visitDate = null,Object? guestCount = null,Object? status = null,Object? totalPrice = freezed,Object? currency = freezed,Object? specialRequests = freezed,Object? contactPhone = freezed,Object? contactEmail = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_Booking(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,placeId: null == placeId ? _self.placeId : placeId // ignore: cast_nullable_to_non_nullable
as String,bookingDate: null == bookingDate ? _self.bookingDate : bookingDate // ignore: cast_nullable_to_non_nullable
as DateTime,visitDate: null == visitDate ? _self.visitDate : visitDate // ignore: cast_nullable_to_non_nullable
as DateTime,guestCount: null == guestCount ? _self.guestCount : guestCount // ignore: cast_nullable_to_non_nullable
as int,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as BookingStatus,totalPrice: freezed == totalPrice ? _self.totalPrice : totalPrice // ignore: cast_nullable_to_non_nullable
as double?,currency: freezed == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String?,specialRequests: freezed == specialRequests ? _self.specialRequests : specialRequests // ignore: cast_nullable_to_non_nullable
as String?,contactPhone: freezed == contactPhone ? _self.contactPhone : contactPhone // ignore: cast_nullable_to_non_nullable
as String?,contactEmail: freezed == contactEmail ? _self.contactEmail : contactEmail // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$BookingRequest {

 String get placeId; DateTime get visitDate; int get guestCount; String? get specialRequests; String? get contactPhone; String? get contactEmail;
/// Create a copy of BookingRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BookingRequestCopyWith<BookingRequest> get copyWith => _$BookingRequestCopyWithImpl<BookingRequest>(this as BookingRequest, _$identity);

  /// Serializes this BookingRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BookingRequest&&(identical(other.placeId, placeId) || other.placeId == placeId)&&(identical(other.visitDate, visitDate) || other.visitDate == visitDate)&&(identical(other.guestCount, guestCount) || other.guestCount == guestCount)&&(identical(other.specialRequests, specialRequests) || other.specialRequests == specialRequests)&&(identical(other.contactPhone, contactPhone) || other.contactPhone == contactPhone)&&(identical(other.contactEmail, contactEmail) || other.contactEmail == contactEmail));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,placeId,visitDate,guestCount,specialRequests,contactPhone,contactEmail);

@override
String toString() {
  return 'BookingRequest(placeId: $placeId, visitDate: $visitDate, guestCount: $guestCount, specialRequests: $specialRequests, contactPhone: $contactPhone, contactEmail: $contactEmail)';
}


}

/// @nodoc
abstract mixin class $BookingRequestCopyWith<$Res>  {
  factory $BookingRequestCopyWith(BookingRequest value, $Res Function(BookingRequest) _then) = _$BookingRequestCopyWithImpl;
@useResult
$Res call({
 String placeId, DateTime visitDate, int guestCount, String? specialRequests, String? contactPhone, String? contactEmail
});




}
/// @nodoc
class _$BookingRequestCopyWithImpl<$Res>
    implements $BookingRequestCopyWith<$Res> {
  _$BookingRequestCopyWithImpl(this._self, this._then);

  final BookingRequest _self;
  final $Res Function(BookingRequest) _then;

/// Create a copy of BookingRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? placeId = null,Object? visitDate = null,Object? guestCount = null,Object? specialRequests = freezed,Object? contactPhone = freezed,Object? contactEmail = freezed,}) {
  return _then(_self.copyWith(
placeId: null == placeId ? _self.placeId : placeId // ignore: cast_nullable_to_non_nullable
as String,visitDate: null == visitDate ? _self.visitDate : visitDate // ignore: cast_nullable_to_non_nullable
as DateTime,guestCount: null == guestCount ? _self.guestCount : guestCount // ignore: cast_nullable_to_non_nullable
as int,specialRequests: freezed == specialRequests ? _self.specialRequests : specialRequests // ignore: cast_nullable_to_non_nullable
as String?,contactPhone: freezed == contactPhone ? _self.contactPhone : contactPhone // ignore: cast_nullable_to_non_nullable
as String?,contactEmail: freezed == contactEmail ? _self.contactEmail : contactEmail // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [BookingRequest].
extension BookingRequestPatterns on BookingRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _BookingRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _BookingRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _BookingRequest value)  $default,){
final _that = this;
switch (_that) {
case _BookingRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _BookingRequest value)?  $default,){
final _that = this;
switch (_that) {
case _BookingRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String placeId,  DateTime visitDate,  int guestCount,  String? specialRequests,  String? contactPhone,  String? contactEmail)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _BookingRequest() when $default != null:
return $default(_that.placeId,_that.visitDate,_that.guestCount,_that.specialRequests,_that.contactPhone,_that.contactEmail);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String placeId,  DateTime visitDate,  int guestCount,  String? specialRequests,  String? contactPhone,  String? contactEmail)  $default,) {final _that = this;
switch (_that) {
case _BookingRequest():
return $default(_that.placeId,_that.visitDate,_that.guestCount,_that.specialRequests,_that.contactPhone,_that.contactEmail);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String placeId,  DateTime visitDate,  int guestCount,  String? specialRequests,  String? contactPhone,  String? contactEmail)?  $default,) {final _that = this;
switch (_that) {
case _BookingRequest() when $default != null:
return $default(_that.placeId,_that.visitDate,_that.guestCount,_that.specialRequests,_that.contactPhone,_that.contactEmail);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _BookingRequest implements BookingRequest {
  const _BookingRequest({required this.placeId, required this.visitDate, required this.guestCount, this.specialRequests, this.contactPhone, this.contactEmail});
  factory _BookingRequest.fromJson(Map<String, dynamic> json) => _$BookingRequestFromJson(json);

@override final  String placeId;
@override final  DateTime visitDate;
@override final  int guestCount;
@override final  String? specialRequests;
@override final  String? contactPhone;
@override final  String? contactEmail;

/// Create a copy of BookingRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BookingRequestCopyWith<_BookingRequest> get copyWith => __$BookingRequestCopyWithImpl<_BookingRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BookingRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BookingRequest&&(identical(other.placeId, placeId) || other.placeId == placeId)&&(identical(other.visitDate, visitDate) || other.visitDate == visitDate)&&(identical(other.guestCount, guestCount) || other.guestCount == guestCount)&&(identical(other.specialRequests, specialRequests) || other.specialRequests == specialRequests)&&(identical(other.contactPhone, contactPhone) || other.contactPhone == contactPhone)&&(identical(other.contactEmail, contactEmail) || other.contactEmail == contactEmail));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,placeId,visitDate,guestCount,specialRequests,contactPhone,contactEmail);

@override
String toString() {
  return 'BookingRequest(placeId: $placeId, visitDate: $visitDate, guestCount: $guestCount, specialRequests: $specialRequests, contactPhone: $contactPhone, contactEmail: $contactEmail)';
}


}

/// @nodoc
abstract mixin class _$BookingRequestCopyWith<$Res> implements $BookingRequestCopyWith<$Res> {
  factory _$BookingRequestCopyWith(_BookingRequest value, $Res Function(_BookingRequest) _then) = __$BookingRequestCopyWithImpl;
@override @useResult
$Res call({
 String placeId, DateTime visitDate, int guestCount, String? specialRequests, String? contactPhone, String? contactEmail
});




}
/// @nodoc
class __$BookingRequestCopyWithImpl<$Res>
    implements _$BookingRequestCopyWith<$Res> {
  __$BookingRequestCopyWithImpl(this._self, this._then);

  final _BookingRequest _self;
  final $Res Function(_BookingRequest) _then;

/// Create a copy of BookingRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? placeId = null,Object? visitDate = null,Object? guestCount = null,Object? specialRequests = freezed,Object? contactPhone = freezed,Object? contactEmail = freezed,}) {
  return _then(_BookingRequest(
placeId: null == placeId ? _self.placeId : placeId // ignore: cast_nullable_to_non_nullable
as String,visitDate: null == visitDate ? _self.visitDate : visitDate // ignore: cast_nullable_to_non_nullable
as DateTime,guestCount: null == guestCount ? _self.guestCount : guestCount // ignore: cast_nullable_to_non_nullable
as int,specialRequests: freezed == specialRequests ? _self.specialRequests : specialRequests // ignore: cast_nullable_to_non_nullable
as String?,contactPhone: freezed == contactPhone ? _self.contactPhone : contactPhone // ignore: cast_nullable_to_non_nullable
as String?,contactEmail: freezed == contactEmail ? _self.contactEmail : contactEmail // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$PlacesFilter {

 Set<PlaceCategory> get categories; Set<PlaceType> get types; DateTime? get startDate; DateTime? get endDate; double? get minPrice; double? get maxPrice; bool get verifiedOnly; bool get exclusiveOnly; bool get trendingOnly; String? get searchQuery; String? get location; double? get latitude; double? get longitude; double? get radius; double get minRating;
/// Create a copy of PlacesFilter
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PlacesFilterCopyWith<PlacesFilter> get copyWith => _$PlacesFilterCopyWithImpl<PlacesFilter>(this as PlacesFilter, _$identity);

  /// Serializes this PlacesFilter to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PlacesFilter&&const DeepCollectionEquality().equals(other.categories, categories)&&const DeepCollectionEquality().equals(other.types, types)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.minPrice, minPrice) || other.minPrice == minPrice)&&(identical(other.maxPrice, maxPrice) || other.maxPrice == maxPrice)&&(identical(other.verifiedOnly, verifiedOnly) || other.verifiedOnly == verifiedOnly)&&(identical(other.exclusiveOnly, exclusiveOnly) || other.exclusiveOnly == exclusiveOnly)&&(identical(other.trendingOnly, trendingOnly) || other.trendingOnly == trendingOnly)&&(identical(other.searchQuery, searchQuery) || other.searchQuery == searchQuery)&&(identical(other.location, location) || other.location == location)&&(identical(other.latitude, latitude) || other.latitude == latitude)&&(identical(other.longitude, longitude) || other.longitude == longitude)&&(identical(other.radius, radius) || other.radius == radius)&&(identical(other.minRating, minRating) || other.minRating == minRating));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(categories),const DeepCollectionEquality().hash(types),startDate,endDate,minPrice,maxPrice,verifiedOnly,exclusiveOnly,trendingOnly,searchQuery,location,latitude,longitude,radius,minRating);

@override
String toString() {
  return 'PlacesFilter(categories: $categories, types: $types, startDate: $startDate, endDate: $endDate, minPrice: $minPrice, maxPrice: $maxPrice, verifiedOnly: $verifiedOnly, exclusiveOnly: $exclusiveOnly, trendingOnly: $trendingOnly, searchQuery: $searchQuery, location: $location, latitude: $latitude, longitude: $longitude, radius: $radius, minRating: $minRating)';
}


}

/// @nodoc
abstract mixin class $PlacesFilterCopyWith<$Res>  {
  factory $PlacesFilterCopyWith(PlacesFilter value, $Res Function(PlacesFilter) _then) = _$PlacesFilterCopyWithImpl;
@useResult
$Res call({
 Set<PlaceCategory> categories, Set<PlaceType> types, DateTime? startDate, DateTime? endDate, double? minPrice, double? maxPrice, bool verifiedOnly, bool exclusiveOnly, bool trendingOnly, String? searchQuery, String? location, double? latitude, double? longitude, double? radius, double minRating
});




}
/// @nodoc
class _$PlacesFilterCopyWithImpl<$Res>
    implements $PlacesFilterCopyWith<$Res> {
  _$PlacesFilterCopyWithImpl(this._self, this._then);

  final PlacesFilter _self;
  final $Res Function(PlacesFilter) _then;

/// Create a copy of PlacesFilter
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? categories = null,Object? types = null,Object? startDate = freezed,Object? endDate = freezed,Object? minPrice = freezed,Object? maxPrice = freezed,Object? verifiedOnly = null,Object? exclusiveOnly = null,Object? trendingOnly = null,Object? searchQuery = freezed,Object? location = freezed,Object? latitude = freezed,Object? longitude = freezed,Object? radius = freezed,Object? minRating = null,}) {
  return _then(_self.copyWith(
categories: null == categories ? _self.categories : categories // ignore: cast_nullable_to_non_nullable
as Set<PlaceCategory>,types: null == types ? _self.types : types // ignore: cast_nullable_to_non_nullable
as Set<PlaceType>,startDate: freezed == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime?,endDate: freezed == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime?,minPrice: freezed == minPrice ? _self.minPrice : minPrice // ignore: cast_nullable_to_non_nullable
as double?,maxPrice: freezed == maxPrice ? _self.maxPrice : maxPrice // ignore: cast_nullable_to_non_nullable
as double?,verifiedOnly: null == verifiedOnly ? _self.verifiedOnly : verifiedOnly // ignore: cast_nullable_to_non_nullable
as bool,exclusiveOnly: null == exclusiveOnly ? _self.exclusiveOnly : exclusiveOnly // ignore: cast_nullable_to_non_nullable
as bool,trendingOnly: null == trendingOnly ? _self.trendingOnly : trendingOnly // ignore: cast_nullable_to_non_nullable
as bool,searchQuery: freezed == searchQuery ? _self.searchQuery : searchQuery // ignore: cast_nullable_to_non_nullable
as String?,location: freezed == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String?,latitude: freezed == latitude ? _self.latitude : latitude // ignore: cast_nullable_to_non_nullable
as double?,longitude: freezed == longitude ? _self.longitude : longitude // ignore: cast_nullable_to_non_nullable
as double?,radius: freezed == radius ? _self.radius : radius // ignore: cast_nullable_to_non_nullable
as double?,minRating: null == minRating ? _self.minRating : minRating // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [PlacesFilter].
extension PlacesFilterPatterns on PlacesFilter {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PlacesFilter value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PlacesFilter() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PlacesFilter value)  $default,){
final _that = this;
switch (_that) {
case _PlacesFilter():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PlacesFilter value)?  $default,){
final _that = this;
switch (_that) {
case _PlacesFilter() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( Set<PlaceCategory> categories,  Set<PlaceType> types,  DateTime? startDate,  DateTime? endDate,  double? minPrice,  double? maxPrice,  bool verifiedOnly,  bool exclusiveOnly,  bool trendingOnly,  String? searchQuery,  String? location,  double? latitude,  double? longitude,  double? radius,  double minRating)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PlacesFilter() when $default != null:
return $default(_that.categories,_that.types,_that.startDate,_that.endDate,_that.minPrice,_that.maxPrice,_that.verifiedOnly,_that.exclusiveOnly,_that.trendingOnly,_that.searchQuery,_that.location,_that.latitude,_that.longitude,_that.radius,_that.minRating);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( Set<PlaceCategory> categories,  Set<PlaceType> types,  DateTime? startDate,  DateTime? endDate,  double? minPrice,  double? maxPrice,  bool verifiedOnly,  bool exclusiveOnly,  bool trendingOnly,  String? searchQuery,  String? location,  double? latitude,  double? longitude,  double? radius,  double minRating)  $default,) {final _that = this;
switch (_that) {
case _PlacesFilter():
return $default(_that.categories,_that.types,_that.startDate,_that.endDate,_that.minPrice,_that.maxPrice,_that.verifiedOnly,_that.exclusiveOnly,_that.trendingOnly,_that.searchQuery,_that.location,_that.latitude,_that.longitude,_that.radius,_that.minRating);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( Set<PlaceCategory> categories,  Set<PlaceType> types,  DateTime? startDate,  DateTime? endDate,  double? minPrice,  double? maxPrice,  bool verifiedOnly,  bool exclusiveOnly,  bool trendingOnly,  String? searchQuery,  String? location,  double? latitude,  double? longitude,  double? radius,  double minRating)?  $default,) {final _that = this;
switch (_that) {
case _PlacesFilter() when $default != null:
return $default(_that.categories,_that.types,_that.startDate,_that.endDate,_that.minPrice,_that.maxPrice,_that.verifiedOnly,_that.exclusiveOnly,_that.trendingOnly,_that.searchQuery,_that.location,_that.latitude,_that.longitude,_that.radius,_that.minRating);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PlacesFilter implements PlacesFilter {
  const _PlacesFilter({final  Set<PlaceCategory> categories = const {}, final  Set<PlaceType> types = const {}, this.startDate, this.endDate, this.minPrice, this.maxPrice, this.verifiedOnly = false, this.exclusiveOnly = false, this.trendingOnly = false, this.searchQuery, this.location, this.latitude, this.longitude, this.radius, this.minRating = 0.0}): _categories = categories,_types = types;
  factory _PlacesFilter.fromJson(Map<String, dynamic> json) => _$PlacesFilterFromJson(json);

 final  Set<PlaceCategory> _categories;
@override@JsonKey() Set<PlaceCategory> get categories {
  if (_categories is EqualUnmodifiableSetView) return _categories;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableSetView(_categories);
}

 final  Set<PlaceType> _types;
@override@JsonKey() Set<PlaceType> get types {
  if (_types is EqualUnmodifiableSetView) return _types;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableSetView(_types);
}

@override final  DateTime? startDate;
@override final  DateTime? endDate;
@override final  double? minPrice;
@override final  double? maxPrice;
@override@JsonKey() final  bool verifiedOnly;
@override@JsonKey() final  bool exclusiveOnly;
@override@JsonKey() final  bool trendingOnly;
@override final  String? searchQuery;
@override final  String? location;
@override final  double? latitude;
@override final  double? longitude;
@override final  double? radius;
@override@JsonKey() final  double minRating;

/// Create a copy of PlacesFilter
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PlacesFilterCopyWith<_PlacesFilter> get copyWith => __$PlacesFilterCopyWithImpl<_PlacesFilter>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PlacesFilterToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PlacesFilter&&const DeepCollectionEquality().equals(other._categories, _categories)&&const DeepCollectionEquality().equals(other._types, _types)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.minPrice, minPrice) || other.minPrice == minPrice)&&(identical(other.maxPrice, maxPrice) || other.maxPrice == maxPrice)&&(identical(other.verifiedOnly, verifiedOnly) || other.verifiedOnly == verifiedOnly)&&(identical(other.exclusiveOnly, exclusiveOnly) || other.exclusiveOnly == exclusiveOnly)&&(identical(other.trendingOnly, trendingOnly) || other.trendingOnly == trendingOnly)&&(identical(other.searchQuery, searchQuery) || other.searchQuery == searchQuery)&&(identical(other.location, location) || other.location == location)&&(identical(other.latitude, latitude) || other.latitude == latitude)&&(identical(other.longitude, longitude) || other.longitude == longitude)&&(identical(other.radius, radius) || other.radius == radius)&&(identical(other.minRating, minRating) || other.minRating == minRating));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_categories),const DeepCollectionEquality().hash(_types),startDate,endDate,minPrice,maxPrice,verifiedOnly,exclusiveOnly,trendingOnly,searchQuery,location,latitude,longitude,radius,minRating);

@override
String toString() {
  return 'PlacesFilter(categories: $categories, types: $types, startDate: $startDate, endDate: $endDate, minPrice: $minPrice, maxPrice: $maxPrice, verifiedOnly: $verifiedOnly, exclusiveOnly: $exclusiveOnly, trendingOnly: $trendingOnly, searchQuery: $searchQuery, location: $location, latitude: $latitude, longitude: $longitude, radius: $radius, minRating: $minRating)';
}


}

/// @nodoc
abstract mixin class _$PlacesFilterCopyWith<$Res> implements $PlacesFilterCopyWith<$Res> {
  factory _$PlacesFilterCopyWith(_PlacesFilter value, $Res Function(_PlacesFilter) _then) = __$PlacesFilterCopyWithImpl;
@override @useResult
$Res call({
 Set<PlaceCategory> categories, Set<PlaceType> types, DateTime? startDate, DateTime? endDate, double? minPrice, double? maxPrice, bool verifiedOnly, bool exclusiveOnly, bool trendingOnly, String? searchQuery, String? location, double? latitude, double? longitude, double? radius, double minRating
});




}
/// @nodoc
class __$PlacesFilterCopyWithImpl<$Res>
    implements _$PlacesFilterCopyWith<$Res> {
  __$PlacesFilterCopyWithImpl(this._self, this._then);

  final _PlacesFilter _self;
  final $Res Function(_PlacesFilter) _then;

/// Create a copy of PlacesFilter
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? categories = null,Object? types = null,Object? startDate = freezed,Object? endDate = freezed,Object? minPrice = freezed,Object? maxPrice = freezed,Object? verifiedOnly = null,Object? exclusiveOnly = null,Object? trendingOnly = null,Object? searchQuery = freezed,Object? location = freezed,Object? latitude = freezed,Object? longitude = freezed,Object? radius = freezed,Object? minRating = null,}) {
  return _then(_PlacesFilter(
categories: null == categories ? _self._categories : categories // ignore: cast_nullable_to_non_nullable
as Set<PlaceCategory>,types: null == types ? _self._types : types // ignore: cast_nullable_to_non_nullable
as Set<PlaceType>,startDate: freezed == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime?,endDate: freezed == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime?,minPrice: freezed == minPrice ? _self.minPrice : minPrice // ignore: cast_nullable_to_non_nullable
as double?,maxPrice: freezed == maxPrice ? _self.maxPrice : maxPrice // ignore: cast_nullable_to_non_nullable
as double?,verifiedOnly: null == verifiedOnly ? _self.verifiedOnly : verifiedOnly // ignore: cast_nullable_to_non_nullable
as bool,exclusiveOnly: null == exclusiveOnly ? _self.exclusiveOnly : exclusiveOnly // ignore: cast_nullable_to_non_nullable
as bool,trendingOnly: null == trendingOnly ? _self.trendingOnly : trendingOnly // ignore: cast_nullable_to_non_nullable
as bool,searchQuery: freezed == searchQuery ? _self.searchQuery : searchQuery // ignore: cast_nullable_to_non_nullable
as String?,location: freezed == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String?,latitude: freezed == latitude ? _self.latitude : latitude // ignore: cast_nullable_to_non_nullable
as double?,longitude: freezed == longitude ? _self.longitude : longitude // ignore: cast_nullable_to_non_nullable
as double?,radius: freezed == radius ? _self.radius : radius // ignore: cast_nullable_to_non_nullable
as double?,minRating: null == minRating ? _self.minRating : minRating // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

// dart format on
