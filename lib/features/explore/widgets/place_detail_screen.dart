import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/explore/models/place_model.dart';
import 'package:billionaires_social/features/explore/widgets/booking_modal.dart';
import 'package:intl/intl.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:share_plus/share_plus.dart' as share_plus;
import 'package:url_launcher/url_launcher.dart';

class PlaceDetailScreen extends ConsumerStatefulWidget {
  final Place place;

  const PlaceDetailScreen({super.key, required this.place});

  @override
  ConsumerState<PlaceDetailScreen> createState() => _PlaceDetailScreenState();
}

class _PlaceDetailScreenState extends ConsumerState<PlaceDetailScreen>
    with TickerProviderStateMixin {
  late PageController _imageController;
  int _currentImageIndex = 0;
  late TabController _tabController;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  bool _isFavorite = false;

  @override
  void initState() {
    super.initState();
    _imageController = PageController();
    _tabController = TabController(length: 3, vsync: this);
    _checkFavoriteStatus();
  }

  @override
  void dispose() {
    _imageController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _checkFavoriteStatus() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return;

      final doc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .collection('favorites')
          .doc(widget.place.id)
          .get();

      if (mounted) {
        setState(() {
          _isFavorite = doc.exists;
        });
      }
    } catch (e) {
      debugPrint('Error checking favorite status: $e');
    }
  }

  Future<void> _toggleFavorite() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please sign in to save favorites')),
        );
        return;
      }

      final favoriteRef = _firestore
          .collection('users')
          .doc(currentUser.uid)
          .collection('favorites')
          .doc(widget.place.id);

      if (_isFavorite) {
        // Remove from favorites
        await favoriteRef.delete();
        setState(() {
          _isFavorite = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Removed ${widget.place.name} from favorites'),
            ),
          );
        }
      } else {
        // Add to favorites
        await favoriteRef.set({
          'placeId': widget.place.id,
          'placeName': widget.place.name,
          'placeImage': widget.place.imageUrl,
          'placeCategory': widget.place.category.name,
          'addedAt': FieldValue.serverTimestamp(),
        });

        setState(() {
          _isFavorite = true;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Added ${widget.place.name} to favorites')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error: ${e.toString()}')));
      }
    }
  }

  void _sharePlace() {
    final placeUrl = 'https://billionaires.social/places/${widget.place.id}';
    final shareText =
        'Check out this amazing place: ${widget.place.name}!\n\n${widget.place.description ?? ''}\n\n$placeUrl';

    share_plus.SharePlus.instance.share(
      share_plus.ShareParams(text: shareText),
    );
  }

  Future<void> _contactPlace() async {
    try {
      // Try to contact via phone if available
      if (widget.place.ownerId != null) {
        // Get owner contact info from Firestore
        final ownerDoc = await _firestore
            .collection('users')
            .doc(widget.place.ownerId)
            .get();

        if (ownerDoc.exists) {
          final ownerData = ownerDoc.data()!;
          final phone = ownerData['phone'] as String?;
          final email = ownerData['email'] as String?;

          if (phone != null) {
            final phoneUri = Uri(scheme: 'tel', path: phone);
            if (await canLaunchUrl(phoneUri)) {
              await launchUrl(phoneUri);
              return;
            }
          }

          if (email != null) {
            final emailUri = Uri(
              scheme: 'mailto',
              path: email,
              queryParameters: {
                'subject': 'Inquiry about ${widget.place.name}',
                'body':
                    'Hi, I\'m interested in ${widget.place.name}. Please provide more information.',
              },
            );
            if (await canLaunchUrl(emailUri)) {
              await launchUrl(emailUri);
              return;
            }
          }
        }
      }

      // Fallback to general contact
      final emailUri = Uri(
        scheme: 'mailto',
        path: '<EMAIL>',
        queryParameters: {
          'subject': 'Inquiry about ${widget.place.name}',
          'body':
              'Hi, I\'m interested in ${widget.place.name}. Please provide more information.',
        },
      );

      if (await canLaunchUrl(emailUri)) {
        await launchUrl(emailUri);
      } else {
        throw Exception('Could not open email app');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error contacting place: ${e.toString()}')),
        );
      }
    }
  }

  Future<void> _joinPlace() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please sign in to join events')),
        );
        return;
      }

      // Create join record
      await _firestore.collection('place_joins').add({
        'userId': currentUser.uid,
        'placeId': widget.place.id,
        'joinDate': FieldValue.serverTimestamp(),
        'visitDate': widget.place.eventDate,
        'status': 'joined',
        'notes': '',
      });

      // Update place attendees count
      await _firestore.collection('places').doc(widget.place.id).update({
        'currentBookings': FieldValue.increment(1),
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Successfully joined ${widget.place.name}!')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error joining place: ${e.toString()}')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: CustomScrollView(
        slivers: [
          _buildSliverAppBar(),
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildHeader(),
                  const SizedBox(height: 16),
                  _buildTabs(),
                  const SizedBox(height: 16),
                  _buildTabContent(),
                ],
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 300,
      pinned: true,
      backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
      foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
      flexibleSpace: FlexibleSpaceBar(
        background: Stack(
          children: [
            _buildImageGallery(),
            _buildGradientOverlay(),
            _buildImageIndicators(),
          ],
        ),
      ),
      actions: [
        IconButton(
          icon: Icon(
            _isFavorite ? Icons.favorite : Icons.favorite_border,
            color: _isFavorite ? Colors.red : null,
          ),
          onPressed: _toggleFavorite,
        ),
        IconButton(icon: const Icon(Icons.share), onPressed: _sharePlace),
      ],
    );
  }

  Widget _buildImageGallery() {
    final images = widget.place.imageUrls.isNotEmpty
        ? widget.place.imageUrls
        : [widget.place.imageUrl];

    return PageView.builder(
      controller: _imageController,
      onPageChanged: (index) {
        setState(() {
          _currentImageIndex = index;
        });
      },
      itemCount: images.length,
      itemBuilder: (context, index) {
        return CachedNetworkImage(
          imageUrl: images[index],
          fit: BoxFit.cover,
          placeholder: (context, url) => Container(
            color: Colors.grey[300],
            child: const Center(child: CircularProgressIndicator()),
          ),
          errorWidget: (context, url, error) => Container(
            color: Colors.grey[300],
            child: const Icon(Icons.error),
          ),
        );
      },
    );
  }

  Widget _buildGradientOverlay() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        height: 100,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.transparent, Colors.black.withValues(alpha: 0.5)],
          ),
        ),
      ),
    );
  }

  Widget _buildImageIndicators() {
    final images = widget.place.imageUrls.isNotEmpty
        ? widget.place.imageUrls
        : [widget.place.imageUrl];

    if (images.length <= 1) return const SizedBox.shrink();

    return Positioned(
      bottom: 16,
      left: 0,
      right: 0,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(images.length, (index) {
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 4),
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: _currentImageIndex == index
                  ? Colors.white
                  : Colors.white.withAlpha(128),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                widget.place.name,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            if (widget.place.isVerified)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.blue,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.verified, color: Colors.white, size: 16),
                    const SizedBox(width: 4),
                    Text(
                      'Verified',
                      style: Theme.of(context).textTheme.labelSmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Icon(Icons.star, size: 16, color: Colors.amber),
            const SizedBox(width: 4),
            Text(
              widget.place.rating.toString(),
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            if (widget.place.reviewCount != null) ...[
              Text(
                ' (${widget.place.reviewCount} reviews)',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.secondary,
                ),
              ),
            ],
            const Spacer(),
            if (widget.place.isExclusive)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.amber,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'EXCLUSIVE',
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          '${widget.place.category.name} • ${widget.place.type.name}',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.secondary,
          ),
        ),
        if (widget.place.location != null) ...[
          const SizedBox(height: 4),
          Row(
            children: [
              Icon(
                Icons.location_on,
                size: 16,
                color: Theme.of(context).colorScheme.secondary,
              ),
              const SizedBox(width: 4),
              Text(
                widget.place.location!,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        ],
        if (widget.place.price != null) ...[
          const SizedBox(height: 8),
          Text(
            'From \$${widget.place.price!.toInt()}',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.secondary,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildTabs() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Theme.of(context).dividerColor),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: Theme.of(context).colorScheme.secondary,
        ),
        labelColor: Theme.of(context).colorScheme.onSecondary,
        unselectedLabelColor: Theme.of(context).textTheme.labelLarge?.color,
        tabs: const [
          Tab(text: 'Overview'),
          Tab(text: 'Amenities'),
          Tab(text: 'Reviews'),
        ],
      ),
    );
  }

  Widget _buildTabContent() {
    return SizedBox(
      height: 400,
      child: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(),
          _buildAmenitiesTab(),
          _buildReviewsTab(),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.place.description != null) ...[
            Text(
              'Description',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              widget.place.description!,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
          ],
          if (widget.place.ownerName != null) ...[
            Text(
              'Hosted by',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                if (widget.place.ownerAvatarUrl != null)
                  CircleAvatar(
                    radius: 20,
                    backgroundImage: NetworkImage(widget.place.ownerAvatarUrl!),
                  ),
                const SizedBox(width: 12),
                Text(
                  widget.place.ownerName!,
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
          ],
          if (widget.place.capacity != null) ...[
            Text(
              'Capacity',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              'Up to ${widget.place.capacity} guests',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
          ],
          Text(
            'Event Date',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            DateFormat.yMMMd().add_jm().format(widget.place.eventDate),
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildAmenitiesTab() {
    if (widget.place.amenities.isEmpty) {
      return const Center(child: Text('No amenities listed'));
    }

    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 3,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: widget.place.amenities.length,
      itemBuilder: (context, index) {
        final amenity = widget.place.amenities[index];
        return Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Theme.of(context).dividerColor),
          ),
          child: Row(
            children: [
              Icon(
                _getAmenityIcon(amenity),
                size: 20,
                color: Theme.of(context).colorScheme.secondary,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  amenity,
                  style: Theme.of(context).textTheme.bodyMedium,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildReviewsTab() {
    return const Center(child: Text('Reviews coming soon'));
  }

  IconData _getAmenityIcon(String amenity) {
    final amenityIcons = {
      'WiFi': Icons.wifi,
      'Pool': Icons.pool,
      'Spa': Icons.spa,
      'Gym': Icons.fitness_center,
      'Restaurant': Icons.restaurant,
      'Bar': Icons.local_bar,
      'Concierge': Icons.person,
      'Security': Icons.security,
      'Parking': Icons.local_parking,
      'Helipad': Icons.flight,
      'Beach Access': Icons.beach_access,
      'Golf Course': Icons.sports_golf,
      'Tennis Court': Icons.sports_tennis,
      'Wine Cellar': Icons.wine_bar,
      'Private Chef': Icons.restaurant_menu,
      'Butler Service': Icons.person_outline,
      'Ocean View': Icons.visibility,
      'Mountain View': Icons.landscape,
      'City View': Icons.location_city,
      'Private Garden': Icons.eco,
    };

    return amenityIcons[amenity] ?? Icons.check;
  }

  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).bottomAppBarTheme.color,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(26),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          if (widget.place.type == PlaceType.accommodation ||
              widget.place.type == PlaceType.experience)
            Expanded(
              child: ElevatedButton(
                onPressed: () {
                  showModalBottomSheet(
                    context: context,
                    isScrollControlled: true,
                    backgroundColor: Theme.of(context).cardColor,
                    builder: (_) => BookingModal(place: widget.place),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.secondary,
                  foregroundColor: Theme.of(context).colorScheme.onSecondary,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text('Book Now'),
              ),
            )
          else
            Expanded(
              child: ElevatedButton(
                onPressed: _joinPlace,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.secondary,
                  foregroundColor: Theme.of(context).colorScheme.onSecondary,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text('Join Event'),
              ),
            ),
          const SizedBox(width: 16),
          IconButton(
            onPressed: _contactPlace,
            icon: const Icon(Icons.message),
            style: IconButton.styleFrom(
              backgroundColor: Colors.black.withValues(alpha: 0.1),
            ),
          ),
        ],
      ),
    );
  }
}
