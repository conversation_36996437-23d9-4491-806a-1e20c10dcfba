import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/explore/models/place_model.dart';
import 'package:billionaires_social/features/explore/providers/places_provider.dart';
import 'package:billionaires_social/features/notifications/services/notification_service.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:intl/intl.dart';

class BookingModal extends ConsumerStatefulWidget {
  final Place place;

  const BookingModal({super.key, required this.place});

  @override
  ConsumerState<BookingModal> createState() => _BookingModalState();
}

class _BookingModalState extends ConsumerState<BookingModal> {
  DateTime? _selectedDate;
  TimeOfDay? _selectedTime;
  String _selectedMealType = 'Dinner';
  int _guestCount = 1;
  final _specialRequestsController = TextEditingController();
  final _contactPhoneController = TextEditingController();
  final _contactEmailController = TextEditingController();
  bool _isLoading = false;
  final NotificationService _notificationService = getIt<NotificationService>();

  final List<String> _mealTypes = [
    'Breakfast',
    'Brunch',
    'Lunch',
    'Dinner',
    'Late Night',
    'Drinks Only',
    'Special Event',
  ];

  @override
  void dispose() {
    _specialRequestsController.dispose();
    _contactPhoneController.dispose();
    _contactEmailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.9,
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(16),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Book ${widget.place.name}',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
          ),
          // Scrollable content
          Expanded(
            child: Container(
              color: Theme.of(context).cardColor,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildPlaceSummary(),
                    const SizedBox(height: 24),
                    _buildBookingForm(),
                    const SizedBox(height: 24),
                    _buildPriceSummary(),
                    const SizedBox(height: 24),
                    _buildActionButtons(),
                    const SizedBox(height: 20), // Extra padding at bottom
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlaceSummary() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.secondary.withAlpha(26),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              widget.place.imageUrl,
              width: 60,
              height: 60,
              fit: BoxFit.cover,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.place.name,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  widget.place.location ?? 'Location not specified',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                if (widget.place.price != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    '\$${widget.place.price!.toInt()} per guest',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.secondary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBookingForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Booking Details',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        _buildDatePicker(),
        const SizedBox(height: 16),
        _buildMealTypeSelector(),
        const SizedBox(height: 16),
        _buildTimePicker(),
        const SizedBox(height: 16),
        _buildGuestCounter(),
        const SizedBox(height: 16),
        _buildContactFields(),
        const SizedBox(height: 16),
        _buildSpecialRequests(),
      ],
    );
  }

  Widget _buildDatePicker() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Visit Date', style: Theme.of(context).textTheme.labelLarge),
        const SizedBox(height: 8),
        InkWell(
          onTap: () async {
            final tomorrow = DateTime.now().add(const Duration(days: 1));
            final date = await showDatePicker(
              context: context,
              initialDate: _selectedDate ?? tomorrow,
              firstDate: tomorrow,
              lastDate: DateTime.now().add(const Duration(days: 365)),
            );
            if (date != null) {
              setState(() {
                _selectedDate = date;
              });
            }
          },
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              border: Border.all(color: Theme.of(context).dividerColor),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  color: Theme.of(context).colorScheme.secondary,
                ),
                const SizedBox(width: 8),
                Text(
                  _selectedDate != null
                      ? DateFormat.yMMMd().format(_selectedDate!)
                      : 'Select a date',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: _selectedDate != null
                        ? Theme.of(context).textTheme.bodyMedium?.color
                        : Theme.of(context).hintColor,
                  ),
                ),
                const Spacer(),
                const Icon(Icons.arrow_drop_down),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMealTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Meal Type', style: Theme.of(context).textTheme.labelLarge),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
          decoration: BoxDecoration(
            border: Border.all(color: Theme.of(context).dividerColor),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: _selectedMealType,
              isExpanded: true,
              icon: const Icon(Icons.arrow_drop_down),
              items: _mealTypes.map((String mealType) {
                return DropdownMenuItem<String>(
                  value: mealType,
                  child: Row(
                    children: [
                      Icon(_getMealTypeIcon(mealType), size: 20),
                      const SizedBox(width: 8),
                      Text(mealType),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (String? newValue) {
                if (newValue != null) {
                  setState(() {
                    _selectedMealType = newValue;
                  });
                }
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTimePicker() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Time', style: Theme.of(context).textTheme.labelLarge),
        const SizedBox(height: 8),
        InkWell(
          onTap: () async {
            final time = await showTimePicker(
              context: context,
              initialTime:
                  _selectedTime ?? const TimeOfDay(hour: 12, minute: 0),
            );
            if (time != null) {
              setState(() {
                _selectedTime = time;
              });
            }
          },
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              border: Border.all(color: Theme.of(context).dividerColor),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.access_time,
                  color: Theme.of(context).colorScheme.secondary,
                ),
                const SizedBox(width: 8),
                Text(
                  _selectedTime != null
                      ? _selectedTime!.format(context)
                      : 'Select time',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: _selectedTime != null
                        ? Theme.of(context).textTheme.bodyMedium?.color
                        : Theme.of(context).hintColor,
                  ),
                ),
                const Spacer(),
                const Icon(Icons.arrow_drop_down),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildGuestCounter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Number of Guests', style: Theme.of(context).textTheme.labelLarge),
        const SizedBox(height: 8),
        Row(
          children: [
            IconButton(
              onPressed: () {
                if (_guestCount > 1) {
                  setState(() {
                    _guestCount--;
                  });
                }
              },
              icon: const Icon(Icons.remove_circle_outline),
            ),
            Expanded(
              child: Text(
                _guestCount.toString(),
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.titleLarge,
              ),
            ),
            IconButton(
              onPressed: () {
                if (widget.place.capacity == null ||
                    _guestCount < widget.place.capacity!) {
                  setState(() {
                    _guestCount++;
                  });
                }
              },
              icon: const Icon(Icons.add_circle_outline),
            ),
          ],
        ),
        if (widget.place.capacity != null)
          Text(
            'Maximum capacity: ${widget.place.capacity} guests',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.secondary,
            ),
          ),
      ],
    );
  }

  Widget _buildContactFields() {
    return Column(
      children: [
        TextField(
          controller: _contactPhoneController,
          keyboardType: TextInputType.phone,
          decoration: const InputDecoration(
            labelText: 'Contact Phone',
            prefixIcon: Icon(Icons.phone),
          ),
        ),
        const SizedBox(height: 12),
        TextField(
          controller: _contactEmailController,
          keyboardType: TextInputType.emailAddress,
          decoration: const InputDecoration(
            labelText: 'Contact Email',
            prefixIcon: Icon(Icons.email),
          ),
        ),
      ],
    );
  }

  Widget _buildSpecialRequests() {
    return TextField(
      controller: _specialRequestsController,
      maxLines: 3,
      decoration: const InputDecoration(
        labelText: 'Special Requests (Optional)',
        hintText: 'Any special requirements or preferences...',
        alignLabelWithHint: true,
      ),
    );
  }

  Widget _buildPriceSummary() {
    final basePrice = widget.place.price ?? 0;
    final totalPrice = basePrice * _guestCount;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.secondary.withAlpha(26),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Theme.of(context).dividerColor),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Price per guest',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              Text(
                '\$${basePrice.toInt()}',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Number of guests',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              Text(
                _guestCount.toString(),
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Meal Type', style: Theme.of(context).textTheme.bodyMedium),
              Row(
                children: [
                  Icon(_getMealTypeIcon(_selectedMealType), size: 16),
                  const SizedBox(width: 4),
                  Text(
                    _selectedMealType,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Time', style: Theme.of(context).textTheme.bodyMedium),
              Text(
                _selectedTime != null
                    ? _selectedTime!.format(context)
                    : 'Not selected',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: _selectedTime != null
                      ? Theme.of(context).textTheme.bodyMedium?.color
                      : Theme.of(context).hintColor,
                ),
              ),
            ],
          ),
          const Divider(),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
              Text(
                '\$${totalPrice.toInt()}',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.secondary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _handleBooking,
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.secondary,
              foregroundColor: Theme.of(context).colorScheme.onSecondary,
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Confirm Booking'),
          ),
        ),
      ],
    );
  }

  Future<void> _handleBooking() async {
    final messenger = ScaffoldMessenger.of(context);
    final navigator = Navigator.of(context);

    if (_selectedDate == null) {
      messenger.showSnackBar(
        const SnackBar(
          content: Text('Please select a visit date'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (_selectedTime == null) {
      messenger.showSnackBar(
        const SnackBar(
          content: Text('Please select a time'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (_contactPhoneController.text.isEmpty ||
        _contactEmailController.text.isEmpty) {
      messenger.showSnackBar(
        const SnackBar(
          content: Text('Please provide contact information'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Combine time and meal type with special requests
      final timeAndMealInfo =
          'Meal Type: $_selectedMealType\nTime: ${_selectedTime!.format(context)}'; // Safe to use ! since we validated above
      final combinedRequests = _specialRequestsController.text.isNotEmpty
          ? '$timeAndMealInfo\n\nSpecial Requests:\n${_specialRequestsController.text}'
          : timeAndMealInfo;

      final request = BookingRequest(
        placeId: widget.place.id,
        visitDate: _selectedDate!, // Safe to use ! since we validated above
        guestCount: _guestCount,
        specialRequests: combinedRequests,
        contactPhone: _contactPhoneController.text,
        contactEmail: _contactEmailController.text,
      );

      final booking = await ref.read(createBookingProvider(request).future);

      if (booking != null) {
        // Send booking notification to place owner
        if (widget.place.ownerId != null) {
          await _notificationService.createBookingNotification(
            widget.place.ownerId!,
            booking.id,
          );
        }

        navigator.pop();
        messenger.showSnackBar(
          SnackBar(
            content: Text('Booking confirmed! Booking ID: ${booking.id}'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        throw Exception('Failed to create booking');
      }
    } catch (e) {
      messenger.showSnackBar(
        SnackBar(
          content: Text('Booking failed: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  IconData _getMealTypeIcon(String mealType) {
    switch (mealType) {
      case 'Breakfast':
        return Icons.free_breakfast;
      case 'Brunch':
        return Icons.brunch_dining;
      case 'Lunch':
        return Icons.lunch_dining;
      case 'Dinner':
        return Icons.dinner_dining;
      case 'Late Night':
        return Icons.nightlife;
      case 'Drinks Only':
        return Icons.local_bar;
      case 'Special Event':
        return Icons.celebration;
      default:
        return Icons.restaurant;
    }
  }
}
