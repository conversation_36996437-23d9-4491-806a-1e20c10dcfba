import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/explore/models/place_model.dart';
import 'package:billionaires_social/features/explore/providers/places_provider.dart';
import 'package:intl/intl.dart';
import 'package:billionaires_social/features/explore/widgets/category_filter_modal.dart';
import 'package:billionaires_social/features/explore/widgets/date_filter_modal.dart';
import 'package:billionaires_social/features/explore/widgets/price_filter_modal.dart';
import 'package:billionaires_social/features/explore/widgets/place_detail_screen.dart';
import 'package:billionaires_social/features/explore/widgets/join_place_modal.dart';

class PlacesTab extends ConsumerStatefulWidget {
  const PlacesTab({super.key});

  @override
  ConsumerState<PlacesTab> createState() => _PlacesTabState();
}

class _PlacesTabState extends ConsumerState<PlacesTab>
    with TickerProviderStateMixin {
  late TabController _viewController;
  bool _isMapView = false;

  String _getCategoryDisplayName(PlaceCategory category) {
    switch (category) {
      case PlaceCategory.restaurants:
        return 'Restaurants';
      case PlaceCategory.events:
        return 'Events';
      case PlaceCategory.nightlife:
        return 'Nightlife';
      case PlaceCategory.cafes:
        return 'Cafes';
      case PlaceCategory.resorts:
        return 'Resorts';
      case PlaceCategory.concerts:
        return 'Concerts';
      case PlaceCategory.shopping:
        return 'Shopping';
      case PlaceCategory.shows:
        return 'Shows';
      case PlaceCategory.villas:
        return 'Villas';
      case PlaceCategory.yachts:
        return 'Yachts';
      case PlaceCategory.privateJets:
        return 'Private Jets';
      case PlaceCategory.islands:
        return 'Islands';
      case PlaceCategory.luxuryCars:
        return 'Luxury Cars';
      case PlaceCategory.artGalleries:
        return 'Art Galleries';
      case PlaceCategory.exclusiveClubs:
        return 'Exclusive Clubs';
      case PlaceCategory.spas:
        return 'Spas';
    }
  }

  String _getDateRangeText(DateTime startDate, DateTime? endDate) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));

    // Check for common date ranges
    if (startDate.isAtSameMomentAs(today) &&
        (endDate == null || endDate.isAtSameMomentAs(tomorrow))) {
      return 'Today';
    }

    if (startDate.isAtSameMomentAs(tomorrow) &&
        (endDate == null ||
            endDate.isAtSameMomentAs(tomorrow.add(const Duration(days: 1))))) {
      return 'Tomorrow';
    }

    // Check for this week
    final weekStart = today.subtract(Duration(days: today.weekday - 1));
    final weekEnd = weekStart.add(const Duration(days: 7));
    if (startDate.isAtSameMomentAs(today) &&
        endDate != null &&
        endDate.isAtSameMomentAs(weekEnd)) {
      return 'This Week';
    }

    // Check for this month
    final monthEnd = DateTime(now.year, now.month + 1, 1);
    if (startDate.isAtSameMomentAs(today) &&
        endDate != null &&
        endDate.isAtSameMomentAs(monthEnd)) {
      return 'This Month';
    }

    // Format custom date ranges
    if (endDate == null || startDate.isAtSameMomentAs(endDate)) {
      return DateFormat.MMMd().format(startDate);
    } else {
      final start = DateFormat.MMMd().format(startDate);
      final end = DateFormat.MMMd().format(endDate);
      return '$start - $end';
    }
  }

  bool _isPlacePopular(Place place) {
    // A place is popular if:
    // 1. It has many attendees today (>= 5)
    // 2. It has high rating (>= 4.5)
    // 3. It's trending or exclusive
    int todayAttendees = place.attendees.length;

    return todayAttendees >= 5 ||
        place.rating >= 4.5 ||
        place.isExclusive ||
        place.isTrending;
  }

  IconData _getCategoryIcon(PlaceCategory category) {
    switch (category) {
      case PlaceCategory.restaurants:
        return Icons.restaurant;
      case PlaceCategory.events:
        return Icons.event;
      case PlaceCategory.nightlife:
        return Icons.nightlife;
      case PlaceCategory.cafes:
        return Icons.local_cafe;
      case PlaceCategory.resorts:
        return Icons.hotel;
      case PlaceCategory.concerts:
        return Icons.music_note;
      case PlaceCategory.shopping:
        return Icons.shopping_bag;
      case PlaceCategory.shows:
        return Icons.theater_comedy;
      case PlaceCategory.villas:
        return Icons.villa;
      case PlaceCategory.yachts:
        return Icons.sailing;
      case PlaceCategory.privateJets:
        return Icons.flight;
      case PlaceCategory.islands:
        return Icons.landscape;
      case PlaceCategory.luxuryCars:
        return Icons.directions_car;
      case PlaceCategory.artGalleries:
        return Icons.palette;
      case PlaceCategory.exclusiveClubs:
        return Icons.group;
      case PlaceCategory.spas:
        return Icons.spa;
    }
  }

  List<PlaceUser> _getFollowersAtPlace(Place place) {
    // In a real app, this would filter attendees to show only people you follow
    // For now, we'll show some of the attendees as mock followers
    return place.attendees.take(5).toList();
  }

  void _joinPlace(Place place) {
    // Show join place modal
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => JoinPlaceModal(place: place),
    );
  }

  @override
  void initState() {
    super.initState();
    _viewController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _viewController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final placesAsync = ref.watch(placesProvider);
    final activeFilters = ref.watch(placesFilterProvider);

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: Column(
        children: [
          _buildFilterButtons(context, ref),
          _buildActiveFilterChips(context, ref, activeFilters),
          _buildViewToggle(context),
          Expanded(
            child: RefreshIndicator(
              onRefresh: () async {
                // Invalidate the places provider to trigger a refresh
                ref.invalidate(placesProvider);
                // Wait for the new data to load
                await ref.read(placesProvider.future);
              },
              child: placesAsync.when(
                data: (places) => _buildPlacesContent(places),
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (err, stack) => Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.grey,
                      ),
                      const SizedBox(height: 16),
                      Text('Error: $err'),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () => ref.invalidate(placesProvider),
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterButtons(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              icon: const Icon(Icons.category),
              label: const Text(
                'Category',
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
              onPressed: () => _showCategoryFilterModal(context, ref),
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: Theme.of(context).dividerColor),
                foregroundColor: Theme.of(context).textTheme.labelLarge?.color,
                padding: const EdgeInsets.symmetric(
                  horizontal: 8,
                  vertical: 12,
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: OutlinedButton.icon(
              icon: const Icon(Icons.calendar_today),
              label: const Text('Date'),
              onPressed: () => _showDateFilterModal(context, ref),
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: Theme.of(context).dividerColor),
                foregroundColor: Theme.of(context).textTheme.labelLarge?.color,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: OutlinedButton.icon(
              icon: const Icon(Icons.attach_money),
              label: const Text('Price'),
              onPressed: () => _showPriceFilterModal(context, ref),
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: Theme.of(context).dividerColor),
                foregroundColor: Theme.of(context).textTheme.labelLarge?.color,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActiveFilterChips(
    BuildContext context,
    WidgetRef ref,
    PlacesFilter activeFilters,
  ) {
    final List<Widget> chips = [];

    // Add category chips
    for (final category in activeFilters.categories) {
      chips.add(
        Chip(
          label: Text(_getCategoryDisplayName(category)),
          backgroundColor: Theme.of(context).chipTheme.backgroundColor,
          labelStyle: Theme.of(context).chipTheme.labelStyle,
          onDeleted: () {
            final newCategories = activeFilters.categories.toSet()
              ..remove(category);
            ref
                .read(placesFilterProvider.notifier)
                .update((state) => state.copyWith(categories: newCategories));
          },
        ),
      );
    }

    // Add date chip
    if (activeFilters.startDate != null) {
      String dateText = _getDateRangeText(
        activeFilters.startDate!,
        activeFilters.endDate,
      );
      chips.add(
        Chip(
          label: Text(dateText),
          backgroundColor: Theme.of(context).chipTheme.backgroundColor,
          labelStyle: Theme.of(context).chipTheme.labelStyle,
          onDeleted: () {
            ref
                .read(placesFilterProvider.notifier)
                .update(
                  (state) => state.copyWith(startDate: null, endDate: null),
                );
          },
        ),
      );
    }

    // Add price chip
    if (activeFilters.minPrice != null || activeFilters.maxPrice != null) {
      final min = activeFilters.minPrice != null
          ? '\$${activeFilters.minPrice!.toInt()}'
          : '';
      final max = activeFilters.maxPrice != null
          ? '\$${activeFilters.maxPrice!.toInt()}'
          : '';
      final priceText = min.isNotEmpty && max.isNotEmpty
          ? '$min - $max'
          : min.isNotEmpty
          ? 'From $min'
          : 'Up to $max';

      chips.add(
        Chip(
          label: Text(priceText),
          backgroundColor: Theme.of(context).chipTheme.backgroundColor,
          labelStyle: Theme.of(context).chipTheme.labelStyle,
          onDeleted: () {
            ref
                .read(placesFilterProvider.notifier)
                .update(
                  (state) => state.copyWith(minPrice: null, maxPrice: null),
                );
          },
        ),
      );
    }

    if (chips.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Wrap(spacing: 8.0, runSpacing: 4.0, children: chips),
    );
  }

  Widget _buildViewToggle(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(25),
        border: Border.all(color: Theme.of(context).dividerColor),
      ),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _isMapView = false),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: !_isMapView
                      ? Theme.of(context).colorScheme.secondary
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.view_list,
                      color: !_isMapView
                          ? Theme.of(context).colorScheme.onSecondary
                          : Theme.of(context).textTheme.bodyMedium?.color,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'List',
                      style: TextStyle(
                        color: !_isMapView
                            ? Theme.of(context).colorScheme.onSecondary
                            : Theme.of(context).textTheme.bodyMedium?.color,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _isMapView = true),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: _isMapView
                      ? Theme.of(context).colorScheme.secondary
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.map,
                      color: _isMapView
                          ? Theme.of(context).colorScheme.onSecondary
                          : Theme.of(context).textTheme.bodyMedium?.color,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Map',
                      style: TextStyle(
                        color: _isMapView
                            ? Theme.of(context).colorScheme.onSecondary
                            : Theme.of(context).textTheme.bodyMedium?.color,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlacesContent(List<Place> places) {
    if (places.isEmpty) {
      return const Center(
        child: Text('No places found for the selected filters.'),
      );
    }

    if (_isMapView) {
      return _buildMapView(places);
    } else {
      return _buildCircularPlacesView(places);
    }
  }

  Widget _buildCircularPlacesView(List<Place> places) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Create circular grid layout
            for (int i = 0; i < places.length; i += 3)
              Padding(
                padding: const EdgeInsets.only(bottom: 30.0),
                child: _buildCircularRow(places, i),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildCircularRow(List<Place> places, int startIndex) {
    final rowPlaces = <Place>[];
    for (int i = startIndex; i < startIndex + 3 && i < places.length; i++) {
      rowPlaces.add(places[i]);
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: rowPlaces
          .map((place) => _buildCircularPlaceItem(place))
          .toList(),
    );
  }

  Widget _buildCircularPlaceItem(Place place) {
    final isPopular = _isPlacePopular(place);
    final followersAtPlace = _getFollowersAtPlace(place);

    return Column(
      children: [
        // Main circular place container
        GestureDetector(
          onTap: () {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => PlaceDetailScreen(place: place),
              ),
            );
          },
          child: SizedBox(
            width: 100,
            height: 100,
            child: Stack(
              children: [
                // Circular container
                Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(
                          context,
                        ).colorScheme.secondary.withValues(alpha: 0.8),
                        Theme.of(context).colorScheme.secondary,
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        _getCategoryIcon(place.category),
                        color: Colors.white,
                        size: 24,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        place.name,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                // Fire icon for popular places
                if (isPopular)
                  Positioned(
                    top: 0,
                    left: 0,
                    child: Container(
                      width: 24,
                      height: 24,
                      decoration: const BoxDecoration(
                        color: Colors.orange,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.local_fire_department,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                  ),
                // Join button (+) on the right
                Positioned(
                  top: 0,
                  right: 0,
                  child: GestureDetector(
                    onTap: () => _joinPlace(place),
                    child: Container(
                      width: 28,
                      height: 28,
                      decoration: BoxDecoration(
                        color: Colors.green,
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 2),
                      ),
                      child: const Icon(
                        Icons.add,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 8),
        // Small circles showing followers at this place
        if (followersAtPlace.isNotEmpty)
          SizedBox(
            height: 20,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ...followersAtPlace
                    .take(3)
                    .map(
                      (follower) => Container(
                        width: 20,
                        height: 20,
                        margin: const EdgeInsets.symmetric(horizontal: 1),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 1),
                          image: DecorationImage(
                            image: NetworkImage(
                              follower.avatarUrl.isNotEmpty
                                  ? follower.avatarUrl
                                  : 'https://i.pravatar.cc/150?u=${follower.id}',
                            ),
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    ),
                if (followersAtPlace.length > 3)
                  Container(
                    width: 20,
                    height: 20,
                    margin: const EdgeInsets.symmetric(horizontal: 1),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.grey.withValues(alpha: 0.8),
                      border: Border.all(color: Colors.white, width: 1),
                    ),
                    child: Text(
                      '+${followersAtPlace.length - 3}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 8,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildMapView(List<Place> places) {
    // For now, show a placeholder for map view
    // In a real implementation, this would integrate with Google Maps or similar
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Theme.of(context).dividerColor),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.map,
            size: 64,
            color: Theme.of(context).colorScheme.secondary,
          ),
          const SizedBox(height: 16),
          Text(
            'Map View Coming Soon',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            'Interactive map with luxury location pins',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _showCategoryFilterModal(BuildContext context, WidgetRef ref) {
    final currentFilter = ref.read(placesFilterProvider);
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CategoryFilterModal(
        initialSelectedCategories: currentFilter.categories,
      ),
    ).then((selectedCategories) {
      if (selectedCategories != null) {
        ref
            .read(placesFilterProvider.notifier)
            .update((state) => state.copyWith(categories: selectedCategories));
      }
    });
  }

  void _showDateFilterModal(BuildContext context, WidgetRef ref) {
    final currentFilter = ref.read(placesFilterProvider);
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DateFilterModal(
        initialStartDate: currentFilter.startDate,
        initialEndDate: currentFilter.endDate,
      ),
    ).then((result) {
      if (result != null) {
        ref
            .read(placesFilterProvider.notifier)
            .update(
              (state) => state.copyWith(
                startDate: result['startDate'],
                endDate: result['endDate'],
              ),
            );
      }
    });
  }

  void _showPriceFilterModal(BuildContext context, WidgetRef ref) {
    final currentFilter = ref.read(placesFilterProvider);
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => PriceFilterModal(
        initialMinPrice: currentFilter.minPrice,
        initialMaxPrice: currentFilter.maxPrice,
      ),
    ).then((result) {
      if (result != null) {
        ref
            .read(placesFilterProvider.notifier)
            .update(
              (state) => state.copyWith(
                minPrice: result['minPrice'],
                maxPrice: result['maxPrice'],
              ),
            );
      }
    });
  }
}
