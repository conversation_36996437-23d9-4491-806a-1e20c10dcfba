import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/core/mock_data.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/core/widgets/billionaire_badge.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:billionaires_social/features/profile/screens/user_profile_screen.dart';
import 'package:billionaires_social/features/feed/providers/feed_filter_provider.dart';
import 'package:billionaires_social/features/feed/providers/feed_provider.dart';

class BillionairesTab extends ConsumerWidget {
  const BillionairesTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final billionaireUsers = mockUsers
        .where((user) => user.userType == UserType.billionaire)
        .toList();

    return RefreshIndicator(
      onRefresh: () async {
        // Simulate refresh delay for mock data
        await Future.delayed(const Duration(seconds: 1));
        // In a real app, you would refresh the data source here
        // For now, we'll just show a success message
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Billionaires list refreshed'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );
        }
      },
      child: ListView.builder(
        itemCount: billionaireUsers.length,
        itemBuilder: (context, index) {
          final user = billionaireUsers[index];
          return ListTile(
            leading: CircleAvatar(
              backgroundImage: _isValidImageUrl(user.avatarUrl)
                  ? CachedNetworkImageProvider(user.avatarUrl)
                  : null,
              backgroundColor: AppTheme.luxuryGrey,
              child: !_isValidImageUrl(user.avatarUrl)
                  ? const Icon(Icons.person, color: Colors.grey)
                  : null,
            ),
            title: Row(
              children: [
                Expanded(
                  child: Text(
                    user.name,
                    style: AppTheme.fontStyles.bodyBold,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(width: 8),
                if (user.isVerified) ...[
                  Icon(Icons.verified, color: AppTheme.accentColor, size: 16),
                  const SizedBox(width: 4),
                ],
                if (user.isBillionaire) const BillionaireBadge(size: 16),
              ],
            ),
            subtitle: Text(
              user.bio,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            trailing: ElevatedButton(
              onPressed: () {
                // Set filter to billionaires and navigate back to feed
                ref
                    .read(feedFilterProvider.notifier)
                    .setFilter(FeedFilterType.billionaires);
                ref
                    .read(feedProvider.notifier)
                    .fetchFilteredPosts(FeedFilterType.billionaires);
                Navigator.of(context).pop(); // Go back to feed
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.accentColor,
                foregroundColor: AppTheme.primaryColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
              child: const Text('View Posts'),
            ),
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => UserProfileScreen(userId: user.id),
                ),
              );
            },
          );
        },
      ),
    );
  }

  bool _isValidImageUrl(String? url) {
    if (url == null || url.isEmpty) return false;
    return url.startsWith('http://') || url.startsWith('https://');
  }
}
