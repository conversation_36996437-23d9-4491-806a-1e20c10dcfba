import 'package:flutter/material.dart';

class DateFilterModal extends StatefulWidget {
  final DateTime? initialStartDate;
  final DateTime? initialEndDate;

  const DateFilterModal({
    super.key,
    this.initialStartDate,
    this.initialEndDate,
  });

  @override
  State<DateFilterModal> createState() => _DateFilterModalState();
}

class _DateFilterModalState extends State<DateFilterModal> {
  DateTime? _startDate;
  DateTime? _endDate;

  @override
  void initState() {
    super.initState();
    _startDate = widget.initialStartDate;
    _endDate = widget.initialEndDate;
  }

  void _setDateRange(DateTime start, DateTime end) {
    setState(() {
      _startDate = start;
      _endDate = end;
    });
  }

  Future<void> _selectCustomDateRange(BuildContext context) async {
    final now = DateTime.now();
    final picked = await showDateRangePicker(
      context: context,
      firstDate: now.subtract(const Duration(days: 365)),
      lastDate: now.add(const Duration(days: 365)),
      initialDateRange: _startDate != null && _endDate != null
          ? DateTimeRange(start: _startDate!, end: _endDate!)
          : null,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: Theme.of(context).colorScheme.secondary,
              onPrimary: Theme.of(context).colorScheme.onSecondary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Filter by Date',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 16),
          _buildDateOption('Today', today, today.add(const Duration(days: 1))),
          _buildDateOption(
            'Tomorrow',
            today.add(const Duration(days: 1)),
            today.add(const Duration(days: 2)),
          ),
          _buildDateOption(
            'This Week',
            today,
            today.add(const Duration(days: 7)),
          ),
          _buildDateOption(
            'This Month',
            today,
            DateTime(now.year, now.month + 1, 1),
          ),
          const Divider(height: 24),
          ListTile(
            leading: Icon(
              Icons.calendar_today,
              color: Theme.of(context).iconTheme.color,
            ),
            title: Text(
              'Select specific date(s)',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            onTap: () => _selectCustomDateRange(context),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              Navigator.of(
                context,
              ).pop({'startDate': _startDate, 'endDate': _endDate});
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.secondary,
              foregroundColor: Theme.of(context).colorScheme.onSecondary,
              minimumSize: const Size(double.infinity, 50),
            ),
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }

  Widget _buildDateOption(String title, DateTime start, DateTime end) {
    return ListTile(
      title: Text(title, style: Theme.of(context).textTheme.bodyLarge),
      onTap: () => _setDateRange(start, end),
      trailing: _isDateRangeSelected(start, end)
          ? Icon(
              Icons.check_circle,
              color: Theme.of(context).colorScheme.secondary,
            )
          : const Icon(Icons.circle_outlined),
    );
  }

  bool _isDateRangeSelected(DateTime start, DateTime end) {
    return _startDate != null &&
        _endDate != null &&
        _startDate!.isAtSameMomentAs(start) &&
        _endDate!.isAtSameMomentAs(end);
  }
}
