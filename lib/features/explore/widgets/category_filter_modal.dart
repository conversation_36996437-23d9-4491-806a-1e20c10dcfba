import 'package:billionaires_social/features/explore/models/place_model.dart';
import 'package:flutter/material.dart';

class CategoryFilterModal extends StatefulWidget {
  final Set<PlaceCategory> initialSelectedCategories;

  const CategoryFilterModal({
    super.key,
    required this.initialSelectedCategories,
  });

  @override
  State<CategoryFilterModal> createState() => _CategoryFilterModalState();
}

class _CategoryFilterModalState extends State<CategoryFilterModal> {
  late Set<PlaceCategory> _selectedCategories;

  @override
  void initState() {
    super.initState();
    _selectedCategories = Set.from(widget.initialSelectedCategories);
  }

  String _getCategoryDisplayName(PlaceCategory category) {
    switch (category) {
      case PlaceCategory.restaurants:
        return 'Restaurants';
      case PlaceCategory.events:
        return 'Events';
      case PlaceCategory.nightlife:
        return 'Nightlife';
      case PlaceCategory.cafes:
        return 'Cafes';
      case PlaceCategory.resorts:
        return 'Resorts';
      case PlaceCategory.concerts:
        return 'Concerts';
      case PlaceCategory.shopping:
        return 'Shopping';
      case PlaceCategory.shows:
        return 'Shows';
      case PlaceCategory.villas:
        return 'Villas';
      case PlaceCategory.yachts:
        return 'Yachts';
      case PlaceCategory.privateJets:
        return 'Private Jets';
      case PlaceCategory.islands:
        return 'Islands';
      case PlaceCategory.luxuryCars:
        return 'Luxury Cars';
      case PlaceCategory.artGalleries:
        return 'Art Galleries';
      case PlaceCategory.exclusiveClubs:
        return 'Exclusive Clubs';
      case PlaceCategory.spas:
        return 'Spas';
    }
  }

  void _onCategorySelected(bool? selected, PlaceCategory category) {
    if (selected == true) {
      setState(() {
        _selectedCategories.add(category);
      });
    } else {
      setState(() {
        _selectedCategories.remove(category);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Filter by Category',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView(
              children: PlaceCategory.values.map((category) {
                return CheckboxListTile(
                  title: Text(
                    _getCategoryDisplayName(category),
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                  value: _selectedCategories.contains(category),
                  onChanged: (bool? selected) {
                    _onCategorySelected(selected, category);
                  },
                  activeColor: Theme.of(context).colorScheme.secondary,
                  checkColor: Theme.of(context).colorScheme.onSecondary,
                );
              }).toList(),
            ),
          ),
          const SizedBox(height: 24),
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    setState(() => _selectedCategories.clear());
                  },
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: Theme.of(context).dividerColor),
                    foregroundColor: Theme.of(
                      context,
                    ).textTheme.labelLarge?.color,
                  ),
                  child: const Text('Clear'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop(_selectedCategories);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.secondary,
                    foregroundColor: Theme.of(context).colorScheme.onSecondary,
                  ),
                  child: const Text('Apply'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
