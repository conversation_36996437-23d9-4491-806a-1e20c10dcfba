import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class PriceFilterModal extends ConsumerStatefulWidget {
  final double? initialMinPrice;
  final double? initialMaxPrice;

  const PriceFilterModal({
    super.key,
    this.initialMinPrice,
    this.initialMaxPrice,
  });

  @override
  ConsumerState<PriceFilterModal> createState() => _PriceFilterModalState();
}

class _PriceFilterModalState extends ConsumerState<PriceFilterModal> {
  late RangeValues _priceRange;
  final _minController = TextEditingController();
  final _maxController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _priceRange = RangeValues(
      widget.initialMinPrice ?? 0,
      widget.initialMaxPrice ?? 10000,
    );
    _minController.text = widget.initialMinPrice?.toString() ?? '';
    _maxController.text = widget.initialMaxPrice?.toString() ?? '';
  }

  @override
  void dispose() {
    _minController.dispose();
    _maxController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(16),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Price Range',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  'Set your budget range',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 24),
                RangeSlider(
                  values: _priceRange,
                  min: 0,
                  max: 10000,
                  divisions: 100,
                  labels: RangeLabels(
                    '\$${_priceRange.start.toInt()}',
                    '\$${_priceRange.end.toInt()}',
                  ),
                  onChanged: (values) {
                    setState(() {
                      _priceRange = values;
                      _minController.text = values.start.toInt().toString();
                      _maxController.text = values.end.toInt().toString();
                    });
                  },
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _minController,
                        keyboardType: TextInputType.number,
                        decoration: const InputDecoration(
                          labelText: 'Min Price',
                          prefixText: '\$',
                        ),
                        onChanged: (value) {
                          final minPrice = double.tryParse(value);
                          if (minPrice != null && minPrice <= _priceRange.end) {
                            setState(() {
                              _priceRange = RangeValues(
                                minPrice,
                                _priceRange.end,
                              );
                            });
                          }
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextField(
                        controller: _maxController,
                        keyboardType: TextInputType.number,
                        decoration: const InputDecoration(
                          labelText: 'Max Price',
                          prefixText: '\$',
                        ),
                        onChanged: (value) {
                          final maxPrice = double.tryParse(value);
                          if (maxPrice != null &&
                              maxPrice >= _priceRange.start) {
                            setState(() {
                              _priceRange = RangeValues(
                                _priceRange.start,
                                maxPrice,
                              );
                            });
                          }
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () {
                          setState(() {
                            _priceRange = const RangeValues(0, 10000);
                            _minController.clear();
                            _maxController.clear();
                          });
                        },
                        child: const Text('Clear'),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          final result = <String, double?>{};
                          if (_priceRange.start > 0) {
                            result['minPrice'] = _priceRange.start;
                          }
                          if (_priceRange.end < 10000) {
                            result['maxPrice'] = _priceRange.end;
                          }
                          Navigator.pop(context, result);
                        },
                        child: const Text('Apply'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
