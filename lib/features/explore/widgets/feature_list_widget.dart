import 'package:flutter/material.dart';
import 'package:billionaires_social/core/app_theme.dart';

class FeatureListItem {
  final String title;
  final String subtitle;
  final String imageUrl;
  final VoidCallback? onTap;
  final IconData? icon;

  const FeatureListItem({
    required this.title,
    required this.subtitle,
    required this.imageUrl,
    this.onTap,
    this.icon,
  });
}

class FeatureListWidget extends StatelessWidget {
  final String sectionTitle;
  final List<FeatureListItem> items;
  final Widget? headerAction;

  const FeatureListWidget({
    super.key,
    required this.sectionTitle,
    required this.items,
    this.headerAction,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                sectionTitle,
                style: AppTheme.fontStyles.title.copyWith(
                  color: AppTheme.accentColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (headerAction != null) headerAction!,
            ],
          ),
        ),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: items.length,
          separatorBuilder: (_, _) => const SizedBox(height: 12),
          itemBuilder: (context, index) {
            final item = items[index];
            return GestureDetector(
              onTap: item.onTap,
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                decoration: BoxDecoration(
                  color: Theme.of(context).cardColor,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(20),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(16),
                        bottomLeft: Radius.circular(16),
                      ),
                      child: Image.network(
                        item.imageUrl,
                        width: 80,
                        height: 80,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) => Container(
                          width: 80,
                          height: 80,
                          color: AppTheme.luxuryGrey,
                          child: const Icon(Icons.image, color: Colors.white38),
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 16,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                if (item.icon != null)
                                  Padding(
                                    padding: const EdgeInsets.only(right: 8.0),
                                    child: Icon(
                                      item.icon,
                                      color: AppTheme.accentColor,
                                      size: 20,
                                    ),
                                  ),
                                Expanded(
                                  child: Text(
                                    item.title,
                                    style: AppTheme.fontStyles.subtitle
                                        .copyWith(
                                          color: AppTheme.luxuryWhite,
                                          fontWeight: FontWeight.bold,
                                        ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 6),
                            Text(
                              item.subtitle,
                              style: AppTheme.fontStyles.body.copyWith(
                                color: AppTheme.secondaryAccentColor,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Icon(
                      Icons.chevron_right,
                      color: AppTheme.secondaryAccentColor,
                    ),
                    const SizedBox(width: 8),
                  ],
                ),
              ),
            );
          },
        ),
      ],
    );
  }
}
