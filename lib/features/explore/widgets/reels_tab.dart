import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/features/feed/providers/feed_filter_provider.dart';
import 'package:billionaires_social/features/feed/providers/feed_provider.dart';

class ReelsTab extends ConsumerWidget {
  const ReelsTab({super.key});

  int _getReelsCountByFilter(FeedFilterType filterType) {
    switch (filterType) {
      case FeedFilterType.billionaires:
        return 8; // Fewer billionaire reels
      case FeedFilterType.verified:
        return 12; // More verified reels
      case FeedFilterType.celebrities:
        return 15; // Many celebrity reels
      case FeedFilterType.followed:
        return 6; // Fewer followed reels
      case FeedFilterType.closeFriends:
        return 4; // Private close friends reels
      case FeedFilterType.allUsers:
      case FeedFilterType.all:
        return 21; // All reels
    }
  }

  String _getFilterDescription(FeedFilterType filterType) {
    switch (filterType) {
      case FeedFilterType.billionaires:
        return 'Luxury lifestyle reels from billionaires';
      case FeedFilterType.verified:
        return 'Authentic reels from verified accounts';
      case FeedFilterType.celebrities:
        return 'Celebrity and influencer reels';
      case FeedFilterType.followed:
        return 'Reels from people you follow';
      case FeedFilterType.closeFriends:
        return 'Private reels from your close friends';
      case FeedFilterType.allUsers:
      case FeedFilterType.all:
        return 'All trending reels';
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentFilter = ref.watch(feedFilterProvider);
    final reelsCount = _getReelsCountByFilter(currentFilter);
    final filterDescription = _getFilterDescription(currentFilter);
    final filterIcon = ref.read(feedFilterProvider.notifier).getFilterIcon();

    return Column(
      children: [
        // Active filter indicator
        Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppTheme.accentColor.withAlpha(100),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppTheme.accentColor.withAlpha(200)),
          ),
          child: Row(
            children: [
              Text(filterIcon, style: const TextStyle(fontSize: 20)),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Active Filter: ${ref.read(feedFilterProvider.notifier).getFilterTitle()}',
                      style: AppTheme.fontStyles.bodyBold.copyWith(
                        color: AppTheme.accentColor,
                      ),
                    ),
                    Text(
                      filterDescription,
                      style: AppTheme.fontStyles.body.copyWith(
                        color: AppTheme.secondaryAccentColor,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.play_circle_outline,
                color: AppTheme.accentColor,
                size: 20,
              ),
            ],
          ),
        ),

        // Reels grid
        Expanded(
          child: RefreshIndicator(
            onRefresh: () async {
              // Simulate refresh delay for reels data
              await Future.delayed(const Duration(seconds: 1));
              // In a real app, you would refresh the reels data here
              if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Reels refreshed'),
                    backgroundColor: Colors.green,
                    duration: Duration(seconds: 2),
                  ),
                );
              }
            },
            child: GridView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: 4,
                mainAxisSpacing: 4,
              ),
              itemCount: reelsCount,
              itemBuilder: (context, index) {
                return GestureDetector(
                  onTap: () {
                    // Set filter to followed users and navigate back to feed
                    ref
                        .read(feedFilterProvider.notifier)
                        .setFilter(FeedFilterType.followed);
                    ref
                        .read(feedProvider.notifier)
                        .fetchFilteredPosts(FeedFilterType.followed);
                    Navigator.of(context).pop(); // Go back to feed
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(30),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Stack(
                        children: [
                          CachedNetworkImage(
                            imageUrl:
                                'https://source.unsplash.com/random/300x500?sig=${currentFilter.name}_$index',
                            fit: BoxFit.cover,
                            width: double.infinity,
                            height: double.infinity,
                          ),
                          Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [Colors.transparent, Colors.black54],
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                              ),
                            ),
                          ),
                          Positioned(
                            bottom: 4,
                            left: 4,
                            child: Icon(
                              Icons.play_arrow,
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}
