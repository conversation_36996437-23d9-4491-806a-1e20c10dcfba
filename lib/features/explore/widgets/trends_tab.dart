import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/features/feed/providers/feed_filter_provider.dart';
import 'package:billionaires_social/features/feed/providers/feed_provider.dart';

class TrendsTab extends ConsumerWidget {
  const TrendsTab({super.key});

  List<Map<String, String>> _getTrendsByFilter(FeedFilterType filterType) {
    switch (filterType) {
      case FeedFilterType.billionaires:
        return [
          {
            'title': '#LuxuryInvestments',
            'description': 'Billionaire investment strategies',
          },
          {'title': '#PrivateJets', 'description': 'Luxury air travel trends'},
          {'title': '#YachtLife', 'description': 'Superyacht lifestyle'},
          {'title': '#FineArt', 'description': 'Art collecting and auctions'},
        ];

      case FeedFilterType.verified:
        return [
          {
            'title': '#VerifiedLuxury',
            'description': 'Authentic luxury experiences',
          },
          {
            'title': '#TrustedReviews',
            'description': 'Verified user recommendations',
          },
          {
            'title': '#PremiumContent',
            'description': 'High-quality verified posts',
          },
          {
            'title': '#EliteNetworking',
            'description': 'Verified community connections',
          },
        ];

      case FeedFilterType.celebrities:
        return [
          {
            'title': '#CelebrityLifestyle',
            'description': 'Celebrity fashion and trends',
          },
          {'title': '#RedCarpet', 'description': 'Award shows and events'},
          {
            'title': '#CelebrityTravel',
            'description': 'Celebrity vacation spots',
          },
          {
            'title': '#HollywoodGlamour',
            'description': 'Entertainment industry trends',
          },
        ];

      case FeedFilterType.followed:
        return [
          {
            'title': '#FollowingTrends',
            'description': 'Trends from people you follow',
          },
          {
            'title': '#PersonalRecommendations',
            'description': 'Curated content for you',
          },
          {'title': '#CloseCircle', 'description': 'Your network\'s favorites'},
          {
            'title': '#TrustedSources',
            'description': 'Content from trusted connections',
          },
        ];

      case FeedFilterType.allUsers:
      case FeedFilterType.all:
        return [
          {
            'title': '#LuxuryTravel',
            'description': 'Exploring the world in style',
          },
          {'title': '#YachtLife', 'description': 'Sun, sea, and superyachts'},
          {
            'title': '#FineDining',
            'description': 'Discovering the best restaurants',
          },
          {
            'title': '#ArtCollecting',
            'description': 'The latest in the art world',
          },
        ];

      case FeedFilterType.closeFriends:
        return [
          {
            'title': '#CloseFriendsOnly',
            'description': 'Private moments with close friends',
          },
          {
            'title': '#InnerCircle',
            'description': 'Exclusive updates from your circle',
          },
          {
            'title': '#PrivateLife',
            'description': 'Personal stories and moments',
          },
          {
            'title': '#TrustedFriends',
            'description': 'Content shared with trusted friends',
          },
        ];
    }
  }

  String _getFilterDescription(FeedFilterType filterType) {
    switch (filterType) {
      case FeedFilterType.billionaires:
        return 'Trending topics among billionaires';
      case FeedFilterType.verified:
        return 'Trends from verified accounts';
      case FeedFilterType.celebrities:
        return 'Celebrity and influencer trends';
      case FeedFilterType.followed:
        return 'Trends from people you follow';
      case FeedFilterType.closeFriends:
        return 'Private trends from your close friends';
      case FeedFilterType.allUsers:
      case FeedFilterType.all:
        return 'Popular trends across the platform';
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentFilter = ref.watch(feedFilterProvider);
    final trends = _getTrendsByFilter(currentFilter);
    final filterDescription = _getFilterDescription(currentFilter);
    final filterIcon = ref.read(feedFilterProvider.notifier).getFilterIcon();

    return Column(
      children: [
        // Active filter indicator
        Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppTheme.accentColor.withAlpha(100),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppTheme.accentColor.withAlpha(200)),
          ),
          child: Row(
            children: [
              Text(filterIcon, style: const TextStyle(fontSize: 20)),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Active Filter: ${ref.read(feedFilterProvider.notifier).getFilterTitle()}',
                      style: AppTheme.fontStyles.bodyBold.copyWith(
                        color: AppTheme.accentColor,
                      ),
                    ),
                    Text(
                      filterDescription,
                      style: AppTheme.fontStyles.body.copyWith(
                        color: AppTheme.secondaryAccentColor,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(Icons.trending_up, color: AppTheme.accentColor, size: 20),
            ],
          ),
        ),

        // Trends list
        Expanded(
          child: RefreshIndicator(
            onRefresh: () async {
              // Simulate refresh delay for trends data
              await Future.delayed(const Duration(seconds: 1));
              // In a real app, you would refresh the trends data here
              if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Trends refreshed'),
                    backgroundColor: Colors.green,
                    duration: Duration(seconds: 2),
                  ),
                );
              }
            },
            child: ListView.builder(
              itemCount: trends.length,
              itemBuilder: (context, index) {
                final trend = trends[index];
                return Card(
                  margin: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          trend['title']!,
                          style: AppTheme.fontStyles.subtitle,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          trend['description']!,
                          style: AppTheme.fontStyles.body,
                        ),
                        const SizedBox(height: 8),
                        Align(
                          alignment: Alignment.centerRight,
                          child: TextButton(
                            onPressed: () {
                              // Set filter to verified users and navigate back to feed
                              ref
                                  .read(feedFilterProvider.notifier)
                                  .setFilter(FeedFilterType.verified);
                              ref
                                  .read(feedProvider.notifier)
                                  .fetchFilteredPosts(FeedFilterType.verified);
                              Navigator.of(context).pop(); // Go back to feed
                            },
                            child: const Text('View'),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}
