import 'package:billionaires_social/features/explore/models/place_model.dart';
import 'package:billionaires_social/features/explore/services/places_service.dart';
import 'package:billionaires_social/core/services/cache_service.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Provider for the PlacesService
final placesServiceProvider = Provider<PlacesService>((ref) {
  return PlacesService();
});

// Provider for the CacheService
final cacheServiceProvider = Provider<CacheService>((ref) {
  return getIt<CacheService>();
});

// State notifier for filters - using the PlacesFilter from models
final placesFilterProvider = StateProvider<PlacesFilter>((ref) {
  final now = DateTime.now();
  final startOfToday = DateTime(now.year, now.month, now.day);
  final endOfToday = startOfToday.add(const Duration(days: 1));
  return PlacesFilter(
    categories: {},
    types: {},
    startDate: startOfToday,
    endDate: endOfToday,
    minPrice: null,
    maxPrice: null,
    verifiedOnly: false,
    exclusiveOnly: false,
    trendingOnly: false,
    searchQuery: null,
    location: null,
    latitude: null,
    longitude: null,
    radius: null,
    minRating: 0.0,
  );
});

// Provider to fetch places based on the current filter with caching
final placesProvider = FutureProvider<List<Place>>((ref) async {
  final placesService = ref.watch(placesServiceProvider);
  final cacheService = ref.watch(cacheServiceProvider);
  final filter = ref.watch(placesFilterProvider);

  // Check cache first
  final cachedPlaces = await cacheService.getCachedEvents();
  if (cachedPlaces != null) {
    try {
      final places = cachedPlaces.map((json) => Place.fromJson(json)).toList();
      // Return cached data immediately, then refresh in background
      _refreshPlacesInBackground(placesService, cacheService, filter);
      return places;
    } catch (e) {
      // If cached data is invalid, remove it and continue with fresh fetch
      await cacheService.removeData('events');
    }
  }

  // Fetch fresh data from service
  final places = await placesService.getPlaces(
    categories: filter.categories,
    types: filter.types,
    startDate: filter.startDate,
    endDate: filter.endDate,
    minPrice: filter.minPrice,
    maxPrice: filter.maxPrice,
    verifiedOnly: filter.verifiedOnly,
    exclusiveOnly: filter.exclusiveOnly,
    trendingOnly: filter.trendingOnly,
    searchQuery: filter.searchQuery,
    location: filter.location,
    latitude: filter.latitude,
    longitude: filter.longitude,
    radius: filter.radius,
    minRating: filter.minRating,
  );

  // Cache the fresh data
  final placesJson = places.map((place) => place.toJson()).toList();
  await cacheService.cacheEvents(placesJson);

  return places;
});

Future<void> _refreshPlacesInBackground(
  PlacesService placesService,
  CacheService cacheService,
  PlacesFilter filter,
) async {
  try {
    final freshPlaces = await placesService.getPlaces(
      categories: filter.categories,
      types: filter.types,
      startDate: filter.startDate,
      endDate: filter.endDate,
      minPrice: filter.minPrice,
      maxPrice: filter.maxPrice,
      verifiedOnly: filter.verifiedOnly,
      exclusiveOnly: filter.exclusiveOnly,
      trendingOnly: filter.trendingOnly,
      searchQuery: filter.searchQuery,
      location: filter.location,
      latitude: filter.latitude,
      longitude: filter.longitude,
      radius: filter.radius,
      minRating: filter.minRating,
    );

    final placesJson = freshPlaces.map((place) => place.toJson()).toList();
    await cacheService.cacheEvents(placesJson);
  } catch (e) {
    // Silently fail background refresh
  }
}

// Provider for user join stats
final userJoinStatsProvider = FutureProvider<UserJoinStats>((ref) async {
  final placesService = ref.watch(placesServiceProvider);
  return placesService.getUserStats();
});

// New providers for booking functionality
final bookingProvider = FutureProvider.family<List<Booking>, String>((
  ref,
  userId,
) async {
  final placesService = ref.watch(placesServiceProvider);
  return placesService.getUserBookings(userId);
});

final placeBookingsProvider = FutureProvider.family<List<Booking>, String>((
  ref,
  placeId,
) async {
  final placesService = ref.watch(placesServiceProvider);
  return placesService.getPlaceBookings(placeId);
});

// Provider for creating bookings
final createBookingProvider = FutureProvider.family<Booking?, BookingRequest>((
  ref,
  request,
) async {
  final placesService = ref.watch(placesServiceProvider);
  return placesService.createBooking(request);
});
