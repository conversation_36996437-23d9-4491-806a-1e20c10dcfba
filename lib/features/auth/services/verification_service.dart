import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:billionaires_social/core/services/analytics_service.dart';
import 'package:get_it/get_it.dart';
import 'dart:math';

enum VerificationType { email, phone, twoFactor }

enum VerificationStatus { pending, verified, failed, expired }

class VerificationAttempt {
  final String userId;
  final String email;
  final String? phone;
  final VerificationType type;
  final String code;
  final DateTime createdAt;
  final DateTime expiresAt;
  final VerificationStatus status;
  final String? ipAddress;
  final String? deviceId;
  final int attemptCount;

  VerificationAttempt({
    required this.userId,
    required this.email,
    this.phone,
    required this.type,
    required this.code,
    required this.createdAt,
    required this.expiresAt,
    this.status = VerificationStatus.pending,
    this.ipAddress,
    this.deviceId,
    this.attemptCount = 0,
  });

  Map<String, dynamic> toJson() => {
    'userId': userId,
    'email': email,
    'phone': phone,
    'type': type.name,
    'code': code,
    'createdAt': Timestamp.fromDate(createdAt),
    'expiresAt': Timestamp.fromDate(expiresAt),
    'status': status.name,
    'ipAddress': ipAddress,
    'deviceId': deviceId,
    'attemptCount': attemptCount,
  };

  factory VerificationAttempt.fromJson(Map<String, dynamic> json) =>
      VerificationAttempt(
        userId: json['userId'],
        email: json['email'],
        phone: json['phone'],
        type: VerificationType.values.firstWhere((e) => e.name == json['type']),
        code: json['code'],
        createdAt: (json['createdAt'] as Timestamp).toDate(),
        expiresAt: (json['expiresAt'] as Timestamp).toDate(),
        status: VerificationStatus.values.firstWhere(
          (e) => e.name == json['status'],
        ),
        ipAddress: json['ipAddress'],
        deviceId: json['deviceId'],
        attemptCount: json['attemptCount'] ?? 0,
      );
}

class VerificationService {
  static final VerificationService _instance = VerificationService._internal();
  factory VerificationService() => _instance;
  VerificationService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final AnalyticsService _analyticsService = GetIt.I<AnalyticsService>();

  // Verification settings
  static const int _codeLength = 6;
  static const Duration _codeExpiry = Duration(minutes: 10);
  static const int _maxAttempts = 5;
  static const Duration _lockoutDuration = Duration(minutes: 30);
  static const Duration _resendCooldown = Duration(minutes: 2);

  // In-memory cache for rate limiting
  final Map<String, List<DateTime>> _attemptCache = {};
  final Map<String, DateTime> _lockoutCache = {};
  final Map<String, DateTime> _resendCache = {};

  /// Send email verification code
  Future<void> sendEmailVerificationCode({
    required String userId,
    required String email,
    String? ipAddress,
    String? deviceId,
  }) async {
    await _checkRateLimits(userId, 'email');
    await _checkResendCooldown(userId, 'email');

    final code = _generateVerificationCode();
    final now = DateTime.now();
    final expiresAt = now.add(_codeExpiry);

    final attempt = VerificationAttempt(
      userId: userId,
      email: email,
      type: VerificationType.email,
      code: code,
      createdAt: now,
      expiresAt: expiresAt,
      ipAddress: ipAddress,
      deviceId: deviceId,
    );

    // Store verification attempt
    await _storeVerificationAttempt(attempt);

    // Send email (in real implementation, use email service)
    await _sendEmailCode(email, code);

    // Update resend cache
    _resendCache['${userId}_email'] = now.add(_resendCooldown);

    // Log analytics
    await _analyticsService.logEventSafely(
      eventName: 'email_verification_sent',
      parameters: {
        'user_id': userId,
        'email': email,
        if (ipAddress != null) 'ip_address': ipAddress,
      },
    );

    debugPrint('✅ Email verification code sent to $email');
  }

  /// Send phone verification code
  Future<void> sendPhoneVerificationCode({
    required String userId,
    required String email,
    required String phone,
    String? ipAddress,
    String? deviceId,
  }) async {
    await _checkRateLimits(userId, 'phone');
    await _checkResendCooldown(userId, 'phone');

    final code = _generateVerificationCode();
    final now = DateTime.now();
    final expiresAt = now.add(_codeExpiry);

    final attempt = VerificationAttempt(
      userId: userId,
      email: email,
      phone: phone,
      type: VerificationType.phone,
      code: code,
      createdAt: now,
      expiresAt: expiresAt,
      ipAddress: ipAddress,
      deviceId: deviceId,
    );

    // Store verification attempt
    await _storeVerificationAttempt(attempt);

    // Send SMS (in real implementation, use SMS service)
    await _sendSmsCode(phone, code);

    // Update resend cache
    _resendCache['${userId}_phone'] = now.add(_resendCooldown);

    // Log analytics
    await _analyticsService.logEventSafely(
      eventName: 'phone_verification_sent',
      parameters: {
        'user_id': userId,
        'phone': phone,
        if (ipAddress != null) 'ip_address': ipAddress,
      },
    );

    debugPrint('✅ Phone verification code sent to $phone');
  }

  /// Send 2FA code
  Future<void> send2FACode({
    required String userId,
    required String email,
    String? phone,
    String? ipAddress,
    String? deviceId,
  }) async {
    await _checkRateLimits(userId, '2fa');
    await _checkResendCooldown(userId, '2fa');

    final code = _generateVerificationCode();
    final now = DateTime.now();
    final expiresAt = now.add(_codeExpiry);

    final attempt = VerificationAttempt(
      userId: userId,
      email: email,
      phone: phone,
      type: VerificationType.twoFactor,
      code: code,
      createdAt: now,
      expiresAt: expiresAt,
      ipAddress: ipAddress,
      deviceId: deviceId,
    );

    // Store verification attempt
    await _storeVerificationAttempt(attempt);

    // Send 2FA code (email or SMS based on user preference)
    if (phone != null) {
      await _sendSmsCode(phone, code);
    } else {
      await _sendEmailCode(email, code);
    }

    // Update resend cache
    _resendCache['${userId}_2fa'] = now.add(_resendCooldown);

    // Log analytics
    await _analyticsService.logEventSafely(
      eventName: '2fa_code_sent',
      parameters: {
        'user_id': userId,
        'method': phone != null ? 'sms' : 'email',
        if (ipAddress != null) 'ip_address': ipAddress,
      },
    );

    debugPrint('✅ 2FA code sent to ${phone ?? email}');
  }

  /// Verify code
  Future<bool> verifyCode({
    required String userId,
    required String code,
    required VerificationType type,
    String? ipAddress,
    String? deviceId,
  }) async {
    await _checkRateLimits(userId, 'verify');

    try {
      // Get latest verification attempt
      final attempt = await _getLatestVerificationAttempt(userId, type);
      if (attempt == null) {
        throw Exception('No verification attempt found');
      }

      // Check if code is expired
      if (DateTime.now().isAfter(attempt.expiresAt)) {
        await _updateAttemptStatus(attempt, VerificationStatus.expired);
        throw Exception('Verification code has expired');
      }

      // Check if code matches
      if (attempt.code != code) {
        await _incrementAttemptCount(attempt);
        throw Exception('Invalid verification code');
      }

      // Mark as verified
      await _updateAttemptStatus(attempt, VerificationStatus.verified);

      // Update user verification status
      await _updateUserVerificationStatus(userId, type);

      // Clear rate limiting for this user
      _clearRateLimits(userId);

      // Log analytics
      await _analyticsService.logEventSafely(
        eventName: 'verification_successful',
        parameters: {
          'user_id': userId,
          'type': type.name,
          if (ipAddress != null) 'ip_address': ipAddress,
        },
      );

      debugPrint('✅ Verification successful for user $userId');
      return true;
    } catch (e) {
      // Log analytics
      await _analyticsService.logEventSafely(
        eventName: 'verification_failed',
        parameters: {
          'user_id': userId,
          'type': type.name,
          'error': e.toString(),
          if (ipAddress != null) 'ip_address': ipAddress,
        },
      );

      debugPrint('❌ Verification failed: $e');
      rethrow;
    }
  }

  /// Check if user requires 2FA based on role
  Future<bool> requires2FA(String userId) async {
    try {
      final doc = await _firestore.collection('users').doc(userId).get();
      if (!doc.exists) return false;

      final data = doc.data()!;
      final isAdmin = data['isAdmin'] ?? false;
      final requires2FA = data['requires2FA'] ?? false;

      // Admins always require 2FA
      if (isAdmin) return true;

      return requires2FA;
    } catch (e) {
      debugPrint('❌ Error checking 2FA requirement: $e');
      return false;
    }
  }

  /// Check if user is verified
  Future<bool> isUserVerified(String userId) async {
    try {
      final doc = await _firestore.collection('users').doc(userId).get();
      if (!doc.exists) return false;

      final data = doc.data()!;
      return data['isVerified'] ?? false;
    } catch (e) {
      debugPrint('❌ Error checking user verification: $e');
      return false;
    }
  }

  /// Get verification attempts for user
  Future<List<VerificationAttempt>> getVerificationAttempts(
    String userId,
    VerificationType type,
  ) async {
    try {
      final query = await _firestore
          .collection('verification_attempts')
          .where('userId', isEqualTo: userId)
          .where('type', isEqualTo: type.name)
          .orderBy('createdAt', descending: true)
          .limit(10)
          .get();

      return query.docs
          .map((doc) => VerificationAttempt.fromJson(doc.data()))
          .toList();
    } catch (e) {
      debugPrint('❌ Error getting verification attempts: $e');
      return [];
    }
  }

  /// Generate verification code
  String _generateVerificationCode() {
    final random = Random();
    final code = StringBuffer();
    for (int i = 0; i < _codeLength; i++) {
      code.write(random.nextInt(10));
    }
    return code.toString();
  }

  /// Store verification attempt
  Future<void> _storeVerificationAttempt(VerificationAttempt attempt) async {
    await _firestore.collection('verification_attempts').add(attempt.toJson());
  }

  /// Get latest verification attempt
  Future<VerificationAttempt?> _getLatestVerificationAttempt(
    String userId,
    VerificationType type,
  ) async {
    final query = await _firestore
        .collection('verification_attempts')
        .where('userId', isEqualTo: userId)
        .where('type', isEqualTo: type.name)
        .orderBy('createdAt', descending: true)
        .limit(1)
        .get();

    if (query.docs.isEmpty) return null;

    return VerificationAttempt.fromJson(query.docs.first.data());
  }

  /// Update attempt status
  Future<void> _updateAttemptStatus(
    VerificationAttempt attempt,
    VerificationStatus status,
  ) async {
    // Find the document and update it
    final query = await _firestore
        .collection('verification_attempts')
        .where('userId', isEqualTo: attempt.userId)
        .where('type', isEqualTo: attempt.type.name)
        .where('createdAt', isEqualTo: Timestamp.fromDate(attempt.createdAt))
        .get();

    if (query.docs.isNotEmpty) {
      await query.docs.first.reference.update({'status': status.name});
    }
  }

  /// Increment attempt count
  Future<void> _incrementAttemptCount(VerificationAttempt attempt) async {
    final query = await _firestore
        .collection('verification_attempts')
        .where('userId', isEqualTo: attempt.userId)
        .where('type', isEqualTo: attempt.type.name)
        .where('createdAt', isEqualTo: Timestamp.fromDate(attempt.createdAt))
        .get();

    if (query.docs.isNotEmpty) {
      await query.docs.first.reference.update({
        'attemptCount': FieldValue.increment(1),
      });
    }
  }

  /// Update user verification status
  Future<void> _updateUserVerificationStatus(
    String userId,
    VerificationType type,
  ) async {
    final updates = <String, dynamic>{};

    switch (type) {
      case VerificationType.email:
        updates['emailVerified'] = true;
        updates['emailVerifiedAt'] = FieldValue.serverTimestamp();
        break;
      case VerificationType.phone:
        updates['phoneVerified'] = true;
        updates['phoneVerifiedAt'] = FieldValue.serverTimestamp();
        break;
      case VerificationType.twoFactor:
        updates['twoFactorVerified'] = true;
        updates['twoFactorVerifiedAt'] = FieldValue.serverTimestamp();
        updates['last2FA'] = FieldValue.serverTimestamp();
        break;
    }

    // Mark user as verified if both email and phone are verified
    if (type == VerificationType.email || type == VerificationType.phone) {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (userDoc.exists) {
        final data = userDoc.data()!;
        final emailVerified = data['emailVerified'] ?? false;
        final phoneVerified = data['phoneVerified'] ?? false;

        if (emailVerified && phoneVerified) {
          updates['isVerified'] = true;
          updates['verifiedAt'] = FieldValue.serverTimestamp();
        }
      }
    }

    await _firestore.collection('users').doc(userId).update(updates);
  }

  /// Check rate limits
  Future<void> _checkRateLimits(String userId, String action) async {
    final key = '${userId}_$action';
    final now = DateTime.now();

    // Check lockout
    if (_lockoutCache.containsKey(key)) {
      final lockoutTime = _lockoutCache[key]!;
      if (now.isBefore(lockoutTime)) {
        throw Exception(
          'Too many attempts. Please try again in ${lockoutTime.difference(now).inMinutes} minutes.',
        );
      } else {
        _lockoutCache.remove(key);
        _attemptCache.remove(key);
      }
    }

    // Check attempts
    _attemptCache.putIfAbsent(key, () => []);
    _attemptCache[key]!.add(now);

    // Clean old attempts
    final cutoff = now.subtract(const Duration(minutes: 15));
    _attemptCache[key]!.removeWhere((timestamp) => timestamp.isBefore(cutoff));

    // Check if limit exceeded
    if (_attemptCache[key]!.length >= _maxAttempts) {
      final lockoutUntil = now.add(_lockoutDuration);
      _lockoutCache[key] = lockoutUntil;
      throw Exception(
        'Too many attempts. Account locked for ${_lockoutDuration.inMinutes} minutes.',
      );
    }
  }

  /// Check resend cooldown
  Future<void> _checkResendCooldown(String userId, String type) async {
    final key = '${userId}_$type';
    final now = DateTime.now();

    if (_resendCache.containsKey(key)) {
      final cooldownTime = _resendCache[key]!;
      if (now.isBefore(cooldownTime)) {
        final remaining = cooldownTime.difference(now);
        throw Exception(
          'Please wait ${remaining.inMinutes} minutes before requesting another code.',
        );
      }
    }
  }

  /// Clear rate limits for user
  void _clearRateLimits(String userId) {
    final keysToRemove = <String>[];
    for (final key in _attemptCache.keys) {
      if (key.startsWith('${userId}_')) {
        keysToRemove.add(key);
      }
    }
    for (final key in keysToRemove) {
      _attemptCache.remove(key);
      _lockoutCache.remove(key);
    }
  }

  /// Send email code (placeholder implementation)
  Future<void> _sendEmailCode(String email, String code) async {
    // In real implementation, use email service like SendGrid, Mailgun, etc.
    debugPrint('📧 Sending email verification code $code to $email');
    await Future.delayed(
      const Duration(milliseconds: 500),
    ); // Simulate API call
  }

  /// Send SMS code (placeholder implementation)
  Future<void> _sendSmsCode(String phone, String code) async {
    // In real implementation, use SMS service like Twilio, AWS SNS, etc.
    debugPrint('📱 Sending SMS verification code $code to $phone');
    await Future.delayed(
      const Duration(milliseconds: 500),
    ); // Simulate API call
  }

  /// Get current authenticated user
  User? getCurrentUser() {
    return _auth.currentUser;
  }

  /// Send email verification for current user
  Future<void> sendCurrentUserEmailVerification({
    String? ipAddress,
    String? deviceId,
  }) async {
    final currentUser = getCurrentUser();
    if (currentUser == null) {
      throw Exception('No authenticated user found');
    }
    if (currentUser.email == null) {
      throw Exception('Current user has no email address');
    }

    await sendEmailVerificationCode(
      userId: currentUser.uid,
      email: currentUser.email!,
      ipAddress: ipAddress,
      deviceId: deviceId,
    );
  }

  /// Send phone verification for current user
  Future<void> sendCurrentUserPhoneVerification({
    required String phone,
    String? ipAddress,
    String? deviceId,
  }) async {
    final currentUser = getCurrentUser();
    if (currentUser == null) {
      throw Exception('No authenticated user found');
    }
    if (currentUser.email == null) {
      throw Exception('Current user has no email address');
    }

    await sendPhoneVerificationCode(
      userId: currentUser.uid,
      email: currentUser.email!,
      phone: phone,
      ipAddress: ipAddress,
      deviceId: deviceId,
    );
  }

  /// Send 2FA code for current user
  Future<void> sendCurrent2FACode({
    String? phone,
    String? ipAddress,
    String? deviceId,
  }) async {
    final currentUser = getCurrentUser();
    if (currentUser == null) {
      throw Exception('No authenticated user found');
    }
    if (currentUser.email == null) {
      throw Exception('Current user has no email address');
    }

    await send2FACode(
      userId: currentUser.uid,
      email: currentUser.email!,
      phone: phone,
      ipAddress: ipAddress,
      deviceId: deviceId,
    );
  }

  /// Verify code for current user
  Future<bool> verifyCurrentUserCode({
    required String code,
    required VerificationType type,
    String? ipAddress,
    String? deviceId,
  }) async {
    final currentUser = getCurrentUser();
    if (currentUser == null) {
      throw Exception('No authenticated user found');
    }

    return await verifyCode(
      userId: currentUser.uid,
      code: code,
      type: type,
      ipAddress: ipAddress,
      deviceId: deviceId,
    );
  }

  /// Check if current user requires 2FA
  Future<bool> currentUserRequires2FA() async {
    final currentUser = getCurrentUser();
    if (currentUser == null) return false;
    return await requires2FA(currentUser.uid);
  }

  /// Check if current user is verified
  Future<bool> isCurrentUserVerified() async {
    final currentUser = getCurrentUser();
    if (currentUser == null) return false;
    return await isUserVerified(currentUser.uid);
  }

  /// Get verification attempts for current user
  Future<List<VerificationAttempt>> getCurrentUserVerificationAttempts(
    VerificationType type,
  ) async {
    final currentUser = getCurrentUser();
    if (currentUser == null) return [];
    return await getVerificationAttempts(currentUser.uid, type);
  }

  /// Check if current user is signed in
  bool isUserSignedIn() {
    return _auth.currentUser != null;
  }

  /// Get current user's email
  String? getCurrentUserEmail() {
    return _auth.currentUser?.email;
  }

  /// Check if current user's email is verified in Firebase Auth
  bool isCurrentUserEmailVerifiedInAuth() {
    final currentUser = getCurrentUser();
    return currentUser?.emailVerified ?? false;
  }
}
