import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/app_theme.dart';
import '../providers/verification_provider.dart';
import '../services/verification_service.dart';
import '../../../core/services/analytics_service.dart';
import 'package:get_it/get_it.dart';
import 'dart:async';

class VerificationScreen extends ConsumerStatefulWidget {
  final VerificationType verificationType;
  final String? phone;
  final VoidCallback? onSuccess;

  const VerificationScreen({
    super.key,
    this.verificationType = VerificationType.email,
    this.phone,
    this.onSuccess,
  });

  @override
  ConsumerState<VerificationScreen> createState() => _VerificationScreenState();
}

class _VerificationScreenState extends ConsumerState<VerificationScreen>
    with TickerProviderStateMixin {
  final _codeController = TextEditingController();
  final _phoneController = TextEditingController();
  String? _error;
  int _resendCountdown = 0;
  bool _isLoading = false;
  bool _isResending = false;
  bool _codeSent = false;
  Timer? _countdownTimer;

  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _countdownController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _countdownAnimation;

  @override
  void initState() {
    super.initState();
    _phoneController.text = widget.phone ?? '';

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _countdownController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.2), end: Offset.zero).animate(
          CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
        );

    _countdownAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(parent: _countdownController, curve: Curves.linear),
    );

    _fadeController.forward();
    _slideController.forward();

    // Auto-send code for email verification
    if (widget.verificationType == VerificationType.email) {
      _sendCode();
    }
  }

  @override
  void dispose() {
    _codeController.dispose();
    _phoneController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    _countdownController.dispose();
    _countdownTimer?.cancel();
    super.dispose();
  }

  void _startCountdown() {
    _resendCountdown = 120; // 2 minutes
    _countdownTimer?.cancel();
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          _resendCountdown--;
        });
        if (_resendCountdown <= 0) {
          timer.cancel();
        }
      }
    });
  }

  Future<void> _sendCode() async {
    if (_isResending) return;

    setState(() {
      _isResending = true;
      _error = null;
    });

    try {
      switch (widget.verificationType) {
        case VerificationType.email:
          await ref
              .read(verificationProvider.notifier)
              .sendEmailVerificationCode(
                ipAddress: _getIpAddress(),
                deviceId: _getDeviceId(),
              );
          break;
        case VerificationType.phone:
          if (_phoneController.text.trim().isEmpty) {
            throw Exception('Please enter a valid phone number');
          }
          await ref
              .read(verificationProvider.notifier)
              .sendPhoneVerificationCode(
                phone: _phoneController.text.trim(),
                ipAddress: _getIpAddress(),
                deviceId: _getDeviceId(),
              );
          break;
        case VerificationType.twoFactor:
          await ref
              .read(verificationProvider.notifier)
              .send2FACode(
                phone: widget.phone,
                ipAddress: _getIpAddress(),
                deviceId: _getDeviceId(),
              );
          break;
      }

      setState(() {
        _codeSent = true;
        _isResending = false;
      });

      _startCountdown();

      // Log analytics
      final analyticsService = GetIt.I<AnalyticsService>();
      await analyticsService.logEventSafely(
        eventName: 'verification_code_sent',
        parameters: {
          'type': widget.verificationType.name,
          'method': widget.verificationType == VerificationType.phone
              ? 'sms'
              : 'email',
        },
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Verification code sent to ${_getVerificationTarget()}',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _error = _getErrorMessage(e.toString());
        _isResending = false;
      });

      // Log analytics
      final analyticsService = GetIt.I<AnalyticsService>();
      await analyticsService.logError(
        errorType: 'verification_code_send_failed',
        errorMessage: e.toString(),
      );
    }
  }

  Future<void> _verifyCode() async {
    if (_codeController.text.isEmpty) {
      setState(() {
        _error = 'Please enter the verification code';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      bool success = false;

      switch (widget.verificationType) {
        case VerificationType.email:
          success = await ref
              .read(verificationProvider.notifier)
              .verifyEmailCode(
                code: _codeController.text.trim(),
                ipAddress: _getIpAddress(),
                deviceId: _getDeviceId(),
              );
          break;
        case VerificationType.phone:
          success = await ref
              .read(verificationProvider.notifier)
              .verifyPhoneCode(
                code: _codeController.text.trim(),
                ipAddress: _getIpAddress(),
                deviceId: _getDeviceId(),
              );
          break;
        case VerificationType.twoFactor:
          success = await ref
              .read(verificationProvider.notifier)
              .verify2FACode(
                code: _codeController.text.trim(),
                ipAddress: _getIpAddress(),
                deviceId: _getDeviceId(),
              );
          break;
      }

      if (success) {
        // Log analytics
        final analyticsService = GetIt.I<AnalyticsService>();
        await analyticsService.logEventSafely(
          eventName: 'verification_successful',
          parameters: {'type': widget.verificationType.name},
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Verification successful!'),
              backgroundColor: Colors.green,
            ),
          );

          // Call success callback or navigate
          if (widget.onSuccess != null) {
            widget.onSuccess!();
          } else {
            Navigator.of(context).pop(true);
          }
        }
      }
    } catch (e) {
      setState(() {
        _error = _getErrorMessage(e.toString());
        _isLoading = false;
      });

      // Log analytics
      final analyticsService = GetIt.I<AnalyticsService>();
      await analyticsService.logError(
        errorType: 'verification_failed',
        errorMessage: e.toString(),
      );
    }
  }

  String _getVerificationTarget() {
    switch (widget.verificationType) {
      case VerificationType.email:
        return 'your email';
      case VerificationType.phone:
        return _phoneController.text.trim();
      case VerificationType.twoFactor:
        return widget.phone ?? 'your email';
    }
  }

  String _getErrorMessage(String error) {
    if (error.contains('invalid-code')) {
      return 'Invalid verification code. Please try again.';
    } else if (error.contains('expired-code')) {
      return 'Verification code has expired. Please request a new one.';
    } else if (error.contains('too-many-requests')) {
      return 'Too many attempts. Please try again later.';
    } else if (error.contains('network')) {
      return 'Network error. Please check your connection and try again.';
    } else if (error.contains('No verification attempt found')) {
      return 'Please request a verification code first.';
    } else {
      return 'Verification failed. Please try again.';
    }
  }

  String _getIpAddress() {
    // In real implementation, get actual IP address
    return 'ip_${DateTime.now().millisecondsSinceEpoch}';
  }

  String _getDeviceId() {
    // In real implementation, get actual device ID
    return 'device_${DateTime.now().millisecondsSinceEpoch}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppTheme.luxuryBlack,
              AppTheme.luxuryGrey,
              AppTheme.luxuryBlack,
            ],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(32),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header section
                    Center(
                      child: Column(
                        children: [
                          // Back button and title
                          Row(
                            children: [
                              IconButton(
                                onPressed: () => Navigator.pop(context),
                                icon: Icon(
                                  Icons.arrow_back_ios,
                                  color: AppTheme.luxuryWhite,
                                ),
                              ),
                              const Spacer(),
                              Text(
                                _getTitle(),
                                style: Theme.of(context).textTheme.headlineLarge
                                    ?.copyWith(
                                      color: AppTheme.primaryGold,
                                      fontWeight: FontWeight.bold,
                                      letterSpacing: 2,
                                    ),
                              ),
                              const Spacer(),
                              const SizedBox(width: 48),
                            ],
                          ),
                          const SizedBox(height: 40),
                          // Icon
                          Container(
                            width: 80,
                            height: 80,
                            decoration: BoxDecoration(
                              color: AppTheme.primaryGold.withValues(
                                alpha: 0.1,
                              ),
                              borderRadius: BorderRadius.circular(40),
                              border: Border.all(
                                color: AppTheme.primaryGold.withValues(
                                  alpha: 0.3,
                                ),
                                width: 2,
                              ),
                            ),
                            child: Icon(
                              _getIcon(),
                              size: 40,
                              color: AppTheme.primaryGold,
                            ),
                          ),
                          const SizedBox(height: 24),
                          // Title
                          Text(
                            _getScreenTitle(),
                            style: Theme.of(context).textTheme.displaySmall
                                ?.copyWith(
                                  color: AppTheme.luxuryWhite,
                                  fontWeight: FontWeight.w300,
                                  letterSpacing: 1,
                                ),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            _getDescription(),
                            style: Theme.of(context).textTheme.bodyLarge
                                ?.copyWith(
                                  color: AppTheme.luxuryWhite.withValues(
                                    alpha: 0.7,
                                  ),
                                  fontWeight: FontWeight.w300,
                                  height: 1.5,
                                ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 48),
                    if (!_codeSent &&
                        widget.verificationType == VerificationType.phone) ...[
                      Text(
                        'Phone Number',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              color: AppTheme.luxuryWhite,
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                      const SizedBox(height: 8),
                      TextField(
                        controller: _phoneController,
                        keyboardType: TextInputType.phone,
                        style: TextStyle(color: AppTheme.luxuryWhite),
                        decoration: InputDecoration(
                          hintText: '****** 567 8900',
                          hintStyle: TextStyle(
                            color: AppTheme.luxuryWhite.withAlpha(120),
                          ),
                          filled: true,
                          fillColor: AppTheme.luxuryGrey.withAlpha(80),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide.none,
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),
                      SizedBox(
                        width: double.infinity,
                        height: 56,
                        child: ElevatedButton(
                          onPressed: _isResending ? null : _sendCode,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.primaryGold,
                            foregroundColor: AppTheme.luxuryBlack,
                            elevation: 8,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                          ),
                          child: _isResending
                              ? const CircularProgressIndicator()
                              : const Text('SEND CODE'),
                        ),
                      ),
                    ] else ...[
                      Text(
                        'Verification Code',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              color: AppTheme.luxuryWhite,
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                      const SizedBox(height: 8),
                      TextField(
                        controller: _codeController,
                        keyboardType: TextInputType.number,
                        style: TextStyle(
                          color: AppTheme.luxuryWhite,
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          letterSpacing: 2,
                        ),
                        decoration: InputDecoration(
                          hintText: '000000',
                          hintStyle: TextStyle(
                            color: AppTheme.luxuryWhite.withValues(alpha: 0.3),
                            fontSize: 18,
                            letterSpacing: 2,
                          ),
                          filled: true,
                          fillColor: AppTheme.luxuryGrey.withValues(alpha: 0.3),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide.none,
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: AppTheme.primaryGold,
                              width: 2,
                            ),
                          ),
                          prefixIcon: Icon(
                            Icons.lock_outline,
                            color: AppTheme.luxuryWhite.withValues(alpha: 0.7),
                          ),
                        ),
                        textAlign: TextAlign.center,
                        maxLength: 6,
                      ),
                      const SizedBox(height: 24),
                      SizedBox(
                        width: double.infinity,
                        height: 56,
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _verifyCode,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.primaryGold,
                            foregroundColor: AppTheme.luxuryBlack,
                            elevation: 8,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                          ),
                          child: _isLoading
                              ? const CircularProgressIndicator()
                              : const Text('VERIFY'),
                        ),
                      ),
                      const SizedBox(height: 16),
                      // Resend button with countdown
                      Center(
                        child: TextButton(
                          onPressed: (_resendCountdown == 0 && !_isResending)
                              ? _sendCode
                              : null,
                          child: _resendCountdown == 0
                              ? Text(
                                  'Resend Code',
                                  style: TextStyle(
                                    color: AppTheme.primaryGold,
                                    fontWeight: FontWeight.w500,
                                  ),
                                )
                              : Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    // Animated countdown progress indicator
                                    AnimatedBuilder(
                                      animation: _countdownAnimation,
                                      builder: (context, child) {
                                        return SizedBox(
                                          width: 20,
                                          height: 20,
                                          child: CircularProgressIndicator(
                                            value: _resendCountdown / 120.0,
                                            strokeWidth: 2,
                                            backgroundColor: AppTheme
                                                .luxuryWhite
                                                .withValues(alpha: 0.2),
                                            valueColor:
                                                AlwaysStoppedAnimation<Color>(
                                                  AppTheme.primaryGold
                                                      .withValues(alpha: 0.8),
                                                ),
                                          ),
                                        );
                                      },
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'Resend in ',
                                      style: TextStyle(
                                        color: AppTheme.luxuryWhite.withValues(
                                          alpha: 0.5,
                                        ),
                                      ),
                                    ),
                                    Text(
                                      '${_resendCountdown ~/ 60}:${(_resendCountdown % 60).toString().padLeft(2, '0')}',
                                      style: TextStyle(
                                        color: AppTheme.primaryGold,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                        ),
                      ),
                    ],
                    if (_error != null) ...[
                      const SizedBox(height: 16),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.red.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Colors.red.withValues(alpha: 0.3),
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.error_outline,
                              color: Colors.red,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _error!,
                                style: TextStyle(
                                  color: Colors.red,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  String _getTitle() {
    switch (widget.verificationType) {
      case VerificationType.email:
        return 'EMAIL VERIFICATION';
      case VerificationType.phone:
        return 'PHONE VERIFICATION';
      case VerificationType.twoFactor:
        return '2FA VERIFICATION';
    }
  }

  String _getScreenTitle() {
    switch (widget.verificationType) {
      case VerificationType.email:
        return 'Verify Your Email';
      case VerificationType.phone:
        return 'Verify Your Phone';
      case VerificationType.twoFactor:
        return 'Two-Factor Authentication';
    }
  }

  String _getDescription() {
    switch (widget.verificationType) {
      case VerificationType.email:
        return 'We\'ve sent a 6-digit verification code to your email address.';
      case VerificationType.phone:
        return 'We\'ve sent a 6-digit verification code to your phone number.';
      case VerificationType.twoFactor:
        return 'Enter the 6-digit verification code sent to your email or phone.';
    }
  }

  IconData _getIcon() {
    switch (widget.verificationType) {
      case VerificationType.email:
        return Icons.email;
      case VerificationType.phone:
        return Icons.phone;
      case VerificationType.twoFactor:
        return Icons.security;
    }
  }
}
