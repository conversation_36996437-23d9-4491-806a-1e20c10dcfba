import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:billionaires_social/core/utils/dialog_utils.dart';

class PreferencesScreen extends StatefulWidget {
  const PreferencesScreen({super.key});

  @override
  State<PreferencesScreen> createState() => _PreferencesScreenState();
}

class _PreferencesScreenState extends State<PreferencesScreen> {
  bool _darkMode = true;
  bool _emailNotifications = true;
  bool _pushNotifications = true;
  bool _locationServices = false;
  bool _analytics = true;
  String _language = 'English';
  String _currency = 'USD';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
        elevation: 0,
        title: Text(
          'Preferences',
          style: Theme.of(context).appBarTheme.titleTextStyle,
        ),
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: Theme.of(context).appBarTheme.foregroundColor,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: ListView(
        padding: const EdgeInsets.all(24),
        children: [
          _buildSection(
            title: 'Appearance',
            icon: FontAwesomeIcons.palette,
            children: [
              _buildSwitchTile(
                title: 'Dark Mode',
                subtitle: 'Use dark theme',
                value: _darkMode,
                onChanged: (value) => setState(() => _darkMode = value),
              ),
            ],
          ),
          const SizedBox(height: 32),
          _buildSection(
            title: 'Notifications',
            icon: FontAwesomeIcons.bell,
            children: [
              _buildSwitchTile(
                title: 'Push Notifications',
                subtitle: 'Receive push notifications',
                value: _pushNotifications,
                onChanged: (value) =>
                    setState(() => _pushNotifications = value),
              ),
              _buildSwitchTile(
                title: 'Email Notifications',
                subtitle: 'Receive email updates',
                value: _emailNotifications,
                onChanged: (value) =>
                    setState(() => _emailNotifications = value),
              ),
            ],
          ),
          const SizedBox(height: 32),
          _buildSection(
            title: 'Privacy & Security',
            icon: FontAwesomeIcons.shield,
            children: [
              _buildSwitchTile(
                title: 'Location Services',
                subtitle: 'Allow location access',
                value: _locationServices,
                onChanged: (value) => setState(() => _locationServices = value),
              ),
              _buildSwitchTile(
                title: 'Analytics',
                subtitle: 'Help improve the app',
                value: _analytics,
                onChanged: (value) => setState(() => _analytics = value),
              ),
              _buildListTile(
                title: 'Privacy Policy',
                subtitle: 'Read our privacy policy',
                icon: FontAwesomeIcons.fileLines,
                onTap: () {},
              ),
              _buildListTile(
                title: 'Terms of Service',
                subtitle: 'Read our terms of service',
                icon: FontAwesomeIcons.fileContract,
                onTap: () {},
              ),
            ],
          ),
          const SizedBox(height: 32),
          _buildSection(
            title: 'Regional',
            icon: FontAwesomeIcons.globe,
            children: [
              _buildDropdownTile(
                title: 'Language',
                subtitle: 'Choose your language',
                value: _language,
                options: ['English', 'Spanish', 'French', 'German', 'Italian'],
                onChanged: (value) => setState(() => _language = value!),
              ),
              _buildDropdownTile(
                title: 'Currency',
                subtitle: 'Choose your currency',
                value: _currency,
                options: ['USD', 'EUR', 'GBP', 'JPY', 'CAD'],
                onChanged: (value) => setState(() => _currency = value!),
              ),
            ],
          ),
          const SizedBox(height: 32),
          _buildSection(
            title: 'Account',
            icon: FontAwesomeIcons.user,
            children: [
              _buildListTile(
                title: 'Change Password',
                subtitle: 'Update your password',
                icon: FontAwesomeIcons.key,
                onTap: () {},
              ),
              _buildListTile(
                title: 'Two-Factor Authentication',
                subtitle: 'Secure your account',
                icon: FontAwesomeIcons.lock,
                onTap: () {},
              ),
              _buildListTile(
                title: 'Delete Account',
                subtitle: 'Permanently delete your account',
                icon: FontAwesomeIcons.trash,
                onTap: () => _showDeleteAccountDialog(),
                isDestructive: true,
              ),
            ],
          ),
          const SizedBox(height: 32),
          _buildSection(
            title: 'Support',
            icon: FontAwesomeIcons.headset,
            children: [
              _buildListTile(
                title: 'Help Center',
                subtitle: 'Get help and support',
                icon: FontAwesomeIcons.circleQuestion,
                onTap: () {},
              ),
              _buildListTile(
                title: 'Contact Support',
                subtitle: 'Reach out to our team',
                icon: FontAwesomeIcons.envelope,
                onTap: () {},
              ),
              _buildListTile(
                title: 'About',
                subtitle: 'App version and info',
                icon: FontAwesomeIcons.circleInfo,
                onTap: () {},
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: Colors.blue, size: 20),
            ),
            const SizedBox(width: 12),
            Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: Colors.grey[900],
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.white12),
          ),
          child: Column(children: children),
        ),
      ],
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.white12, width: 0.5)),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(color: Colors.grey[400], fontSize: 14),
                ),
              ],
            ),
          ),
          Switch(value: value, onChanged: onChanged, activeColor: Colors.blue),
        ],
      ),
    );
  }

  Widget _buildListTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return Container(
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.white12, width: 0.5)),
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: isDestructive
                ? Colors.red.withValues(alpha: 0.15)
                : Colors.grey.withValues(alpha: 0.15),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: isDestructive ? Colors.red : Colors.grey,
            size: 20,
          ),
        ),
        title: Text(
          title,
          style: TextStyle(
            color: isDestructive ? Colors.red : Colors.white,
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(color: Colors.grey[400], fontSize: 14),
        ),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          color: Colors.white54,
          size: 16,
        ),
        onTap: onTap,
      ),
    );
  }

  Widget _buildDropdownTile({
    required String title,
    required String subtitle,
    required String value,
    required List<String> options,
    required ValueChanged<String?> onChanged,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.white12, width: 0.5)),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(color: Colors.grey[400], fontSize: 14),
                ),
              ],
            ),
          ),
          DropdownButton<String>(
            value: value,
            onChanged: onChanged,
            dropdownColor: Colors.grey[900],
            style: const TextStyle(color: Colors.white),
            underline: Container(),
            items: options.map((String option) {
              return DropdownMenuItem<String>(
                value: option,
                child: Text(option),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  void _showDeleteAccountDialog() {
    showAppDialog(
      context: context,
      title: const Text('Delete Account?'),
      content: const Text(
        'This action cannot be undone. All your data will be permanently deleted.',
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel', style: TextStyle(color: Colors.blue)),
        ),
        ElevatedButton(
          style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
          onPressed: () {
            Navigator.of(context).pop();
            // Handle account deletion
          },
          child: const Text('Delete Account'),
        ),
      ],
    );
  }
}
