import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/app_theme.dart';
import '../providers/auth_provider.dart';
import '../services/verification_service.dart';
import 'verification_screen.dart';
import '../../../core/services/analytics_service.dart';
import '../../../core/services/registration_security_service.dart';
import 'package:get_it/get_it.dart';

class RegisterScreen extends ConsumerStatefulWidget {
  final Function(String email, String name)? onRegister;
  const RegisterScreen({super.key, this.onRegister});

  @override
  ConsumerState<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends ConsumerState<RegisterScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  String? _error;

  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  bool _termsAccepted = false;
  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  AccountType _selectedAccountType = AccountType.user;

  final RegistrationSecurityService _registrationSecurityService =
      RegistrationSecurityService();

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.2), end: Offset.zero).animate(
          CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
        );

    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  void _submit() async {
    if (!_formKey.currentState!.validate()) {
      setState(() {
        _error = 'Please fill in all fields correctly.';
      });
      return;
    }

    if (!_termsAccepted) {
      setState(() {
        _error = 'You must accept the Terms of Service and Privacy Policy.';
      });
      return;
    }

    setState(() {
      _error = null;
      _isLoading = true;
    });

    try {
      // Enhanced registration with role-based account creation
      final result = await ref
          .read(authProvider.notifier)
          .signUp(
            email: _emailController.text.trim(),
            password: _passwordController.text,
            name: _nameController.text.trim(),
            accountType: _selectedAccountType,
          );

      // Log successful registration
      final analyticsService = GetIt.I<AnalyticsService>();
      await analyticsService.logEventSafely(
        eventName: 'signup_attempt',
        parameters: {
          'account_type': _selectedAccountType.name,
          'success': true,
          'device_id': await _registrationSecurityService.getDeviceId(),
        },
      );

      // If successful, call the callback if provided
      if (widget.onRegister != null) {
        widget.onRegister!(
          _emailController.text.trim(),
          _nameController.text.trim(),
        );
      }

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Account created successfully! Please verify your email.',
            ),
            backgroundColor: Colors.green,
          ),
        );

        // Handle role-based navigation
        final navigationRoute = result['navigationRoute'] as String;
        final requiresEmailVerification =
            result['requiresEmailVerification'] as bool;

        if (requiresEmailVerification) {
          // Navigate to email verification screen
          _navigateToEmailVerification(navigationRoute);
        } else {
          // Navigate directly to role-based route
          _navigateToRoleBasedRoute(navigationRoute);
        }
      }
    } catch (e) {
      // Log registration error
      final analyticsService = GetIt.I<AnalyticsService>();
      await analyticsService.logEventSafely(
        eventName: 'signup_attempt',
        parameters: {
          'account_type': _selectedAccountType.name,
          'success': false,
          'error': e.toString(),
          'device_id': await _registrationSecurityService.getDeviceId(),
        },
      );

      setState(() {
        _error = _getErrorMessage(e.toString());
        _isLoading = false;
      });
    }
  }

  void _navigateToEmailVerification(String navigationRoute) {
    // Navigate to email verification screen
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => VerificationScreen(
          verificationType: VerificationType.email,
          onSuccess: () => _navigateToRoleBasedRoute(navigationRoute),
        ),
      ),
    );
  }

  void _navigateToRoleBasedRoute(String route) {
    Navigator.of(context).pushNamedAndRemoveUntil(route, (route) => false);
  }

  String _getErrorMessage(String error) {
    if (error.contains('email-already-in-use')) {
      return 'Email is already registered. Please use a different email or sign in.';
    } else if (error.contains('weak-password')) {
      return 'Password is too weak. Please choose a stronger password.';
    } else if (error.contains('invalid-email')) {
      return 'Please enter a valid email address.';
    } else if (error.contains('network')) {
      return 'Network error. Please check your connection and try again.';
    } else if (error.contains('registration limit')) {
      return 'You\'ve reached the registration limit for today. Please try again later or contact support.';
    } else if (error.contains('Password must be at least 8 characters')) {
      return 'Password must be at least 8 characters with 1 number and 1 special character.';
    } else {
      return 'Registration failed. Please try again.';
    }
  }

  void _handleBackButton() {
    Navigator.pop(context);
  }

  Widget _buildAccountTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'What type of account are you creating?',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: AppTheme.luxuryWhite,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 16),
        // Personal Account Option
        GestureDetector(
          onTap: () {
            setState(() {
              _selectedAccountType = AccountType.user;
            });
          },
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: _selectedAccountType == AccountType.user
                  ? AppTheme.primaryGold.withValues(alpha: 0.2)
                  : AppTheme.luxuryGrey.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: _selectedAccountType == AccountType.user
                    ? AppTheme.primaryGold
                    : AppTheme.luxuryWhite.withValues(alpha: 0.3),
                width: 2,
              ),
            ),
            child: Row(
              children: [
                Radio<AccountType>(
                  value: AccountType.user,
                  groupValue: _selectedAccountType,
                  onChanged: (value) {
                    setState(() {
                      _selectedAccountType = value!;
                    });
                  },
                  activeColor: AppTheme.primaryGold,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Personal Account',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              color: AppTheme.luxuryWhite,
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Connect with friends, share content, and explore the community',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.luxuryWhite.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 12),
        // Business Account Option
        GestureDetector(
          onTap: () {
            setState(() {
              _selectedAccountType = AccountType.business;
            });
          },
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: _selectedAccountType == AccountType.business
                  ? AppTheme.primaryGold.withValues(alpha: 0.2)
                  : AppTheme.luxuryGrey.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: _selectedAccountType == AccountType.business
                    ? AppTheme.primaryGold
                    : AppTheme.luxuryWhite.withValues(alpha: 0.3),
                width: 2,
              ),
            ),
            child: Row(
              children: [
                Radio<AccountType>(
                  value: AccountType.business,
                  groupValue: _selectedAccountType,
                  onChanged: (value) {
                    setState(() {
                      _selectedAccountType = value!;
                    });
                  },
                  activeColor: AppTheme.primaryGold,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Business Account',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              color: AppTheme.luxuryWhite,
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Sell products, manage your store, and access business insights',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.luxuryWhite.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppTheme.luxuryBlack,
              AppTheme.luxuryGrey,
              AppTheme.luxuryBlack,
            ],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(32),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header section
                      Center(
                        child: Column(
                          children: [
                            // Back button and title
                            Row(
                              children: [
                                IconButton(
                                  onPressed: _handleBackButton,
                                  icon: Icon(
                                    Icons.arrow_back_ios,
                                    color: AppTheme.luxuryWhite,
                                  ),
                                ),
                                const Spacer(),
                                Text(
                                  'REGISTER',
                                  style: Theme.of(context)
                                      .textTheme
                                      .headlineLarge
                                      ?.copyWith(
                                        color: AppTheme.primaryGold,
                                        fontWeight: FontWeight.bold,
                                        letterSpacing: 2,
                                      ),
                                ),
                                const Spacer(),
                                const SizedBox(
                                  width: 48,
                                ), // Balance the back button
                              ],
                            ),
                            const SizedBox(height: 40),
                            // Welcome text
                            Text(
                              'Join the Elite',
                              style: Theme.of(context).textTheme.displaySmall
                                  ?.copyWith(
                                    color: AppTheme.luxuryWhite,
                                    fontWeight: FontWeight.w300,
                                    letterSpacing: 1,
                                  ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Create your account',
                              style: Theme.of(context).textTheme.bodyLarge
                                  ?.copyWith(
                                    color: AppTheme.luxuryWhite.withValues(
                                      alpha: 0.7,
                                    ),
                                    fontWeight: FontWeight.w300,
                                  ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 48),
                      // Account Type Selection
                      _buildAccountTypeSelector(),
                      const SizedBox(height: 32),
                      // Name field
                      Text(
                        'Full Name',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              color: AppTheme.luxuryWhite,
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                      const SizedBox(height: 8),
                      Semantics(
                        label: 'Full name field',
                        child: TextFormField(
                          controller: _nameController,
                          style: TextStyle(
                            color: AppTheme.luxuryWhite,
                            fontSize: 16,
                          ),
                          decoration: InputDecoration(
                            hintText: 'Enter your full name',
                            hintStyle: TextStyle(
                              color: AppTheme.luxuryWhite.withValues(
                                alpha: 0.5,
                              ),
                            ),
                            filled: true,
                            fillColor: AppTheme.luxuryGrey.withValues(
                              alpha: 0.3,
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide.none,
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: AppTheme.primaryGold,
                                width: 2,
                              ),
                            ),
                            prefixIcon: Icon(
                              Icons.person_outline,
                              color: AppTheme.luxuryWhite.withValues(
                                alpha: 0.7,
                              ),
                            ),
                          ),
                          textCapitalization: TextCapitalization.words,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter your full name';
                            }
                            if (value.length < 2) {
                              return 'Name must be at least 2 characters';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(height: 24),
                      // Email field
                      Text(
                        'Email',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              color: AppTheme.luxuryWhite,
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                      const SizedBox(height: 8),
                      Semantics(
                        label: 'Email address field',
                        child: TextFormField(
                          controller: _emailController,
                          style: TextStyle(
                            color: AppTheme.luxuryWhite,
                            fontSize: 16,
                          ),
                          decoration: InputDecoration(
                            hintText: 'Enter your email',
                            hintStyle: TextStyle(
                              color: AppTheme.luxuryWhite.withValues(
                                alpha: 0.5,
                              ),
                            ),
                            filled: true,
                            fillColor: AppTheme.luxuryGrey.withValues(
                              alpha: 0.3,
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide.none,
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: AppTheme.primaryGold,
                                width: 2,
                              ),
                            ),
                            prefixIcon: Icon(
                              Icons.email_outlined,
                              color: AppTheme.luxuryWhite.withValues(
                                alpha: 0.7,
                              ),
                            ),
                          ),
                          keyboardType: TextInputType.emailAddress,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter your email';
                            }
                            if (!RegExp(
                              r'^[^@\s]+@[^@\s]+\.[^@\s]+$',
                            ).hasMatch(value)) {
                              return 'Please enter a valid email';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(height: 24),
                      // Password field
                      Text(
                        'Password',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              color: AppTheme.luxuryWhite,
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                      const SizedBox(height: 8),
                      Semantics(
                        label: 'Password field',
                        child: TextFormField(
                          controller: _passwordController,
                          style: TextStyle(
                            color: AppTheme.luxuryWhite,
                            fontSize: 16,
                          ),
                          decoration: InputDecoration(
                            hintText: 'Create a password',
                            hintStyle: TextStyle(
                              color: AppTheme.luxuryWhite.withValues(
                                alpha: 0.5,
                              ),
                            ),
                            filled: true,
                            fillColor: AppTheme.luxuryGrey.withValues(
                              alpha: 0.3,
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide.none,
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: AppTheme.primaryGold,
                                width: 2,
                              ),
                            ),
                            prefixIcon: Icon(
                              Icons.lock_outline,
                              color: AppTheme.luxuryWhite.withValues(
                                alpha: 0.7,
                              ),
                            ),
                            suffixIcon: IconButton(
                              icon: Icon(
                                _obscurePassword
                                    ? Icons.visibility_off
                                    : Icons.visibility,
                                color: AppTheme.luxuryWhite.withValues(
                                  alpha: 0.7,
                                ),
                              ),
                              onPressed: () {
                                setState(() {
                                  _obscurePassword = !_obscurePassword;
                                });
                              },
                              tooltip: _obscurePassword
                                  ? 'Show password'
                                  : 'Hide password',
                            ),
                          ),
                          obscureText: _obscurePassword,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please create a password';
                            }
                            if (value.length < 8) {
                              return 'Password must be at least 8 characters';
                            }
                            if (!RegExp(r'[0-9]').hasMatch(value)) {
                              return 'Password must contain at least 1 number';
                            }
                            if (!RegExp(
                              r'[!@#$%^&*(),.?":{}|<>]',
                            ).hasMatch(value)) {
                              return 'Password must contain at least 1 special character';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(height: 24),
                      // Confirm Password field
                      Text(
                        'Confirm Password',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              color: AppTheme.luxuryWhite,
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                      const SizedBox(height: 8),
                      Semantics(
                        label: 'Confirm password field',
                        child: TextFormField(
                          controller: _confirmPasswordController,
                          style: TextStyle(
                            color: AppTheme.luxuryWhite,
                            fontSize: 16,
                          ),
                          decoration: InputDecoration(
                            hintText: 'Confirm your password',
                            hintStyle: TextStyle(
                              color: AppTheme.luxuryWhite.withValues(
                                alpha: 0.5,
                              ),
                            ),
                            filled: true,
                            fillColor: AppTheme.luxuryGrey.withValues(
                              alpha: 0.3,
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide.none,
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: AppTheme.primaryGold,
                                width: 2,
                              ),
                            ),
                            prefixIcon: Icon(
                              Icons.lock_outline,
                              color: AppTheme.luxuryWhite.withValues(
                                alpha: 0.7,
                              ),
                            ),
                            suffixIcon: IconButton(
                              icon: Icon(
                                _obscureConfirmPassword
                                    ? Icons.visibility_off
                                    : Icons.visibility,
                                color: AppTheme.luxuryWhite.withValues(
                                  alpha: 0.7,
                                ),
                              ),
                              onPressed: () {
                                setState(() {
                                  _obscureConfirmPassword =
                                      !_obscureConfirmPassword;
                                });
                              },
                              tooltip: _obscureConfirmPassword
                                  ? 'Show password'
                                  : 'Hide password',
                            ),
                          ),
                          obscureText: _obscureConfirmPassword,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please confirm your password';
                            }
                            if (value != _passwordController.text) {
                              return 'Passwords do not match';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(height: 32),
                      // Terms and conditions
                      Row(
                        children: [
                          Semantics(
                            label: 'Terms and conditions checkbox',
                            child: Checkbox(
                              value: _termsAccepted,
                              onChanged: (value) {
                                setState(() {
                                  _termsAccepted = value ?? false;
                                  // Clear error when user accepts terms
                                  if (_termsAccepted) {
                                    _error = null;
                                  }
                                });
                              },
                              activeColor: AppTheme.primaryGold,
                              checkColor: AppTheme.luxuryBlack,
                            ),
                          ),
                          Expanded(
                            child: RichText(
                              text: TextSpan(
                                style: TextStyle(
                                  color: AppTheme.luxuryWhite.withValues(
                                    alpha: 0.7,
                                  ),
                                  fontSize: 14,
                                ),
                                children: [
                                  const TextSpan(text: 'I agree to the '),
                                  TextSpan(
                                    text: 'Terms of Service',
                                    style: TextStyle(
                                      color: AppTheme.primaryGold,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  const TextSpan(text: ' and '),
                                  TextSpan(
                                    text: 'Privacy Policy',
                                    style: TextStyle(
                                      color: AppTheme.primaryGold,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 32),
                      // Error message
                      if (_error != null)
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.red.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Colors.red.withValues(alpha: 0.3),
                            ),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.error_outline,
                                color: Colors.red,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  _error!,
                                  style: TextStyle(
                                    color: Colors.red,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      if (_error != null) const SizedBox(height: 24),
                      // Register button
                      SizedBox(
                        width: double.infinity,
                        height: 56,
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _submit,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.primaryGold,
                            foregroundColor: AppTheme.luxuryBlack,
                            elevation: 8,
                            shadowColor: AppTheme.primaryGold.withValues(
                              alpha: 0.3,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                          ),
                          child: _isLoading
                              ? SizedBox(
                                  width: 24,
                                  height: 24,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      AppTheme.luxuryBlack,
                                    ),
                                  ),
                                )
                              : Text(
                                  'CREATE ACCOUNT',
                                  style: Theme.of(context).textTheme.titleLarge
                                      ?.copyWith(
                                        fontWeight: FontWeight.bold,
                                        letterSpacing: 2,
                                      ),
                                ),
                        ),
                      ),
                      const SizedBox(height: 24),
                      // Login link
                      Center(
                        child: RichText(
                          text: TextSpan(
                            style: TextStyle(
                              color: AppTheme.luxuryWhite.withValues(
                                alpha: 0.7,
                              ),
                              fontSize: 16,
                            ),
                            children: [
                              const TextSpan(text: 'Already have an account? '),
                              TextSpan(
                                text: 'Sign In',
                                style: TextStyle(
                                  color: AppTheme.primaryGold,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
