import 'package:flutter/material.dart';
import '../../../core/app_theme.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/verification_provider.dart';
import '../../../core/services/analytics_service.dart';
import 'package:get_it/get_it.dart';
import 'dart:async';

class TwoFactorScreen extends ConsumerStatefulWidget {
  final VoidCallback onSuccess;
  final String? phone;

  const TwoFactorScreen({super.key, required this.onSuccess, this.phone});

  @override
  ConsumerState<TwoFactorScreen> createState() => _TwoFactorScreenState();
}

class _TwoFactorScreenState extends ConsumerState<TwoFactorScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _codeController = TextEditingController();
  String? _error;
  int _resendCountdown = 0;
  bool _isLoading = false;
  bool _isResending = false;
  Timer? _countdownTimer;

  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _countdownController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _countdownAnimation;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _countdownController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.2), end: Offset.zero).animate(
          CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
        );

    _countdownAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(parent: _countdownController, curve: Curves.linear),
    );

    _fadeController.forward();
    _slideController.forward();

    // Auto-send 2FA code
    _send2FACode();
  }

  @override
  void dispose() {
    _codeController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    _countdownController.dispose();
    _countdownTimer?.cancel();
    super.dispose();
  }

  void _startCountdown() {
    _resendCountdown = 120; // 2 minutes
    _countdownTimer?.cancel();
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          _resendCountdown--;
        });
        if (_resendCountdown <= 0) {
          timer.cancel();
        }
      }
    });
  }

  Future<void> _send2FACode() async {
    if (_isResending) return;

    setState(() {
      _isResending = true;
      _error = null;
    });

    try {
      await ref
          .read(verificationProvider.notifier)
          .send2FACode(
            phone: widget.phone,
            ipAddress: _getIpAddress(),
            deviceId: _getDeviceId(),
          );

      setState(() {
        _isResending = false;
      });

      _startCountdown();

      // Log analytics
      final analyticsService = GetIt.I<AnalyticsService>();
      await analyticsService.logEventSafely(
        eventName: '2fa_code_sent',
        parameters: {'method': widget.phone != null ? 'sms' : 'email'},
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('2FA code sent to ${widget.phone ?? 'your email'}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _error = _getErrorMessage(e.toString());
        _isResending = false;
      });

      // Log analytics
      final analyticsService = GetIt.I<AnalyticsService>();
      await analyticsService.logError(
        errorType: '2fa_code_send_failed',
        errorMessage: e.toString(),
      );
    }
  }

  void _verifyCode() async {
    if (!_formKey.currentState!.validate()) {
      setState(() {
        _error = 'Please enter the verification code.';
      });
      return;
    }

    setState(() {
      _error = null;
      _isLoading = true;
    });

    try {
      // Verify the 2FA code
      final success = await ref
          .read(verificationProvider.notifier)
          .verify2FACode(
            code: _codeController.text.trim(),
            ipAddress: _getIpAddress(),
            deviceId: _getDeviceId(),
          );

      if (success) {
        // Log successful 2FA verification
        final analyticsService = GetIt.I<AnalyticsService>();
        await analyticsService.logEventSafely(
          eventName: 'two_factor_verified',
          parameters: {'method': 'verification_code'},
        );

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Two-factor authentication successful!'),
              backgroundColor: Colors.green,
            ),
          );

          // Call the success callback
          widget.onSuccess();
        }
      }
    } catch (e) {
      // Log 2FA error
      final analyticsService = GetIt.I<AnalyticsService>();
      await analyticsService.logError(
        errorType: 'two_factor_failed',
        errorMessage: e.toString(),
      );

      setState(() {
        _error = _getErrorMessage(e.toString());
        _isLoading = false;
      });
    }
  }

  String _getErrorMessage(String error) {
    if (error.contains('invalid-code')) {
      return 'Invalid verification code. Please try again.';
    } else if (error.contains('expired-code')) {
      return 'Verification code has expired. Please request a new one.';
    } else if (error.contains('too-many-requests')) {
      return 'Too many attempts. Please try again later.';
    } else if (error.contains('network')) {
      return 'Network error. Please check your connection and try again.';
    } else if (error.contains('No verification attempt found')) {
      return 'Please request a verification code first.';
    } else {
      return 'Verification failed. Please try again.';
    }
  }

  String _getIpAddress() {
    // In real implementation, get actual IP address
    return 'ip_${DateTime.now().millisecondsSinceEpoch}';
  }

  String _getDeviceId() {
    // In real implementation, get actual device ID
    return 'device_${DateTime.now().millisecondsSinceEpoch}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppTheme.luxuryBlack,
              AppTheme.luxuryGrey,
              AppTheme.luxuryBlack,
            ],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(32),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header section
                      Center(
                        child: Column(
                          children: [
                            // Back button and title
                            Row(
                              children: [
                                IconButton(
                                  onPressed: () => Navigator.pop(context),
                                  icon: Icon(
                                    Icons.arrow_back_ios,
                                    color: AppTheme.luxuryWhite,
                                  ),
                                ),
                                const Spacer(),
                                Text(
                                  '2FA VERIFICATION',
                                  style: Theme.of(context)
                                      .textTheme
                                      .headlineLarge
                                      ?.copyWith(
                                        color: AppTheme.primaryGold,
                                        fontWeight: FontWeight.bold,
                                        letterSpacing: 2,
                                      ),
                                ),
                                const Spacer(),
                                const SizedBox(width: 48),
                              ],
                            ),
                            const SizedBox(height: 40),
                            // Security icon
                            Container(
                              width: 80,
                              height: 80,
                              decoration: BoxDecoration(
                                color: AppTheme.primaryGold.withValues(
                                  alpha: 0.2,
                                ),
                                borderRadius: BorderRadius.circular(40),
                              ),
                              child: Icon(
                                Icons.security,
                                size: 40,
                                color: AppTheme.primaryGold,
                              ),
                            ),
                            const SizedBox(height: 24),
                            // Title
                            Text(
                              'Two-Factor Authentication',
                              style: Theme.of(context).textTheme.displaySmall
                                  ?.copyWith(
                                    color: AppTheme.luxuryWhite,
                                    fontWeight: FontWeight.w300,
                                    letterSpacing: 1,
                                  ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Enter your verification code',
                              style: Theme.of(context).textTheme.bodyLarge
                                  ?.copyWith(
                                    color: AppTheme.luxuryWhite.withValues(
                                      alpha: 0.7,
                                    ),
                                    fontWeight: FontWeight.w300,
                                  ),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'We\'ve sent a verification code to ${widget.phone ?? 'your email'}.',
                              textAlign: TextAlign.center,
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(
                                    color: AppTheme.luxuryWhite.withValues(
                                      alpha: 0.6,
                                    ),
                                    fontWeight: FontWeight.w300,
                                  ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 48),
                      // Verification code field
                      Text(
                        'Verification Code',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              color: AppTheme.luxuryWhite,
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                      const SizedBox(height: 8),
                      Semantics(
                        label: 'Verification code field',
                        child: TextFormField(
                          controller: _codeController,
                          style: TextStyle(
                            color: AppTheme.luxuryWhite,
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            letterSpacing: 2,
                          ),
                          decoration: InputDecoration(
                            hintText: '000000',
                            hintStyle: TextStyle(
                              color: AppTheme.luxuryWhite.withValues(
                                alpha: 0.3,
                              ),
                              fontSize: 18,
                              letterSpacing: 2,
                            ),
                            filled: true,
                            fillColor: AppTheme.luxuryGrey.withValues(
                              alpha: 0.3,
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide.none,
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: AppTheme.primaryGold,
                                width: 2,
                              ),
                            ),
                            prefixIcon: Icon(
                              Icons.lock_outline,
                              color: AppTheme.luxuryWhite.withValues(
                                alpha: 0.7,
                              ),
                            ),
                          ),
                          keyboardType: TextInputType.number,
                          textAlign: TextAlign.center,
                          maxLength: 6,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter the verification code';
                            }
                            if (value.length != 6) {
                              return 'Please enter a 6-digit code';
                            }
                            if (!RegExp(r'^[0-9]{6}$').hasMatch(value)) {
                              return 'Please enter only numbers';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(height: 24),
                      // Error message
                      if (_error != null)
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.red.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Colors.red.withValues(alpha: 0.3),
                            ),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.error_outline,
                                color: Colors.red,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  _error!,
                                  style: TextStyle(
                                    color: Colors.red,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      if (_error != null) const SizedBox(height: 24),
                      // Verify button
                      SizedBox(
                        width: double.infinity,
                        height: 56,
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _verifyCode,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.primaryGold,
                            foregroundColor: AppTheme.luxuryBlack,
                            elevation: 8,
                            shadowColor: AppTheme.primaryGold.withValues(
                              alpha: 0.3,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                          ),
                          child: _isLoading
                              ? SizedBox(
                                  width: 24,
                                  height: 24,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      AppTheme.luxuryBlack,
                                    ),
                                  ),
                                )
                              : Text(
                                  'VERIFY',
                                  style: Theme.of(context).textTheme.titleLarge
                                      ?.copyWith(
                                        fontWeight: FontWeight.bold,
                                        letterSpacing: 2,
                                      ),
                                ),
                        ),
                      ),
                      const SizedBox(height: 24),
                      // Resend code link with countdown
                      Center(
                        child: TextButton(
                          onPressed: (_resendCountdown == 0 && !_isResending)
                              ? _send2FACode
                              : null,
                          child: _resendCountdown == 0
                              ? _isResending
                                    ? SizedBox(
                                        width: 16,
                                        height: 16,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                                AppTheme.primaryGold,
                                              ),
                                        ),
                                      )
                                    : Text(
                                        'Resend Code',
                                        style: TextStyle(
                                          color: AppTheme.primaryGold,
                                          fontWeight: FontWeight.w500,
                                          fontSize: 14,
                                        ),
                                      )
                              : Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    // Animated countdown progress indicator
                                    AnimatedBuilder(
                                      animation: _countdownAnimation,
                                      builder: (context, child) {
                                        return SizedBox(
                                          width: 20,
                                          height: 20,
                                          child: CircularProgressIndicator(
                                            value: _resendCountdown / 120.0,
                                            strokeWidth: 2,
                                            backgroundColor: AppTheme
                                                .luxuryWhite
                                                .withValues(alpha: 0.2),
                                            valueColor:
                                                AlwaysStoppedAnimation<Color>(
                                                  AppTheme.primaryGold
                                                      .withValues(alpha: 0.8),
                                                ),
                                          ),
                                        );
                                      },
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'Resend in ',
                                      style: TextStyle(
                                        color: AppTheme.luxuryWhite.withValues(
                                          alpha: 0.5,
                                        ),
                                      ),
                                    ),
                                    Text(
                                      '${_resendCountdown ~/ 60}:${(_resendCountdown % 60).toString().padLeft(2, '0')}',
                                      style: TextStyle(
                                        color: AppTheme.primaryGold,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                        ),
                      ),
                      const SizedBox(height: 32),
                      // Help text
                      Center(
                        child: Text(
                          'Having trouble? Contact support',
                          style: TextStyle(
                            color: AppTheme.luxuryWhite.withValues(alpha: 0.5),
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
