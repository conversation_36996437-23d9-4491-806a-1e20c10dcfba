import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/app_theme.dart';
import '../../../core/widgets/validated_text_field.dart';
import '../../../core/utils/input_validation.dart';
import '../providers/auth_provider.dart';
import 'forgot_password_screen.dart';
import 'register_screen.dart';
import 'two_factor_screen.dart';
import '../../../core/services/analytics_service.dart';
import 'package:get_it/get_it.dart';

class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({super.key});

  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  String? _error;

  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _showSocialLogin = true; // Will be determined by role

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.2), end: Offset.zero).animate(
          CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
        );

    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  void _submit() async {
    if (!_formKey.currentState!.validate()) {
      setState(() {
        _error = 'Please fill in all fields correctly.';
      });
      return;
    }

    setState(() {
      _error = null;
      _isLoading = true;
    });

    try {
      // Enhanced sign in with role detection
      final result = await ref
          .read(authProvider.notifier)
          .signIn(
            email: _emailController.text.trim(),
            password: _passwordController.text,
          );

      // Log successful login
      final analyticsService = GetIt.I<AnalyticsService>();
      await analyticsService.logLogin(method: 'email');

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Signed in successfully!'),
            backgroundColor: Colors.green,
          ),
        );

        // Handle role-based navigation
        final requires2FA = result['requires2FA'] as bool;
        final navigationRoute = result['navigationRoute'] as String;

        if (requires2FA) {
          // Navigate to 2FA screen
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) => TwoFactorScreen(
                onSuccess: () => _navigateToRoleBasedRoute(navigationRoute),
                phone: null, // Will be determined by user preference
              ),
            ),
          );
        } else {
          // Navigate directly to role-based route
          _navigateToRoleBasedRoute(navigationRoute);
        }
      }
    } catch (e) {
      // Log login error
      final analyticsService = GetIt.I<AnalyticsService>();
      await analyticsService.logError(
        errorType: 'login_failed',
        errorMessage: e.toString(),
      );

      setState(() {
        _error = _getErrorMessage(e.toString());
        _isLoading = false;
      });
    }
  }

  void _navigateToRoleBasedRoute(String route) {
    Navigator.of(context).pushNamedAndRemoveUntil(route, (route) => false);
  }

  String _getErrorMessage(String error) {
    if (error.contains('user-not-found')) {
      return 'No account found with this email address.';
    } else if (error.contains('wrong-password')) {
      return 'Incorrect password. Please try again.';
    } else if (error.contains('invalid-email')) {
      return 'Please enter a valid email address.';
    } else if (error.contains('user-disabled')) {
      return 'This account has been disabled.';
    } else if (error.contains('too-many-requests')) {
      return 'Too many failed attempts. Please try again later.';
    } else if (error.contains('network')) {
      return 'Network error. Please check your connection and try again.';
    } else if (error.contains('Account temporarily locked')) {
      return error; // Use the exact error message from security service
    } else if (error.contains('Too many failed attempts')) {
      return error; // Use the exact error message from security service
    } else {
      return 'Sign in failed. Please try again.';
    }
  }

  // Check if social login should be shown
  // Note: Social login visibility should be determined server-side for security
  Future<void> _checkSocialLoginVisibility() async {
    // Always show social login - server-side validation will handle restrictions
    // Admin restrictions should be enforced on the backend, not client-side
    setState(() {
      _showSocialLogin = true;
    });
  }

  // Social Login Handlers

  Future<void> _handleAppleSignIn() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final authNotifier = ref.read(authProvider.notifier);
      final result = await authNotifier.signInWithApple();

      if (mounted) {
        // Log analytics
        final analyticsService = GetIt.instance<AnalyticsService>();
        await analyticsService.logEventSafely(
          eventName: 'apple_signin_attempt',
          parameters: {'success': true, 'provider': 'apple'},
        );

        // Navigate based on result
        final navigationRoute = result['navigationRoute'] as String;
        final requires2FA = result['requires2FA'] as bool;

        if (requires2FA) {
          if (mounted) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TwoFactorScreen(
                  onSuccess: () => _navigateToRoleBasedRoute(navigationRoute),
                ),
              ),
            );
          }
        } else {
          _navigateToRoleBasedRoute(navigationRoute);
        }
      }
    } catch (e) {
      if (mounted) {
        // Log analytics
        final analyticsService = GetIt.instance<AnalyticsService>();
        await analyticsService.logEventSafely(
          eventName: 'apple_signin_attempt',
          parameters: {
            'success': false,
            'error': e.toString(),
            'provider': 'apple',
          },
        );

        setState(() {
          _error = _getSocialLoginErrorMessage(e.toString());
          _isLoading = false;
        });
      }
    }

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _handleFacebookSignIn() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final authNotifier = ref.read(authProvider.notifier);
      final result = await authNotifier.signInWithFacebook();

      if (mounted) {
        // Log analytics
        final analyticsService = GetIt.instance<AnalyticsService>();
        await analyticsService.logEventSafely(
          eventName: 'facebook_signin_attempt',
          parameters: {'success': true, 'provider': 'facebook'},
        );

        // Navigate based on result
        final navigationRoute = result['navigationRoute'] as String;
        final requires2FA = result['requires2FA'] as bool;

        if (requires2FA) {
          if (mounted) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TwoFactorScreen(
                  onSuccess: () => _navigateToRoleBasedRoute(navigationRoute),
                ),
              ),
            );
          }
        } else {
          _navigateToRoleBasedRoute(navigationRoute);
        }
      }
    } catch (e) {
      if (mounted) {
        // Log analytics
        final analyticsService = GetIt.instance<AnalyticsService>();
        await analyticsService.logEventSafely(
          eventName: 'facebook_signin_attempt',
          parameters: {
            'success': false,
            'error': e.toString(),
            'provider': 'facebook',
          },
        );

        setState(() {
          _error = _getSocialLoginErrorMessage(e.toString());
          _isLoading = false;
        });
      }
    }

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  String _getSocialLoginErrorMessage(String error) {
    if (error.contains('not yet configured')) {
      return error; // Return the specific configuration message
    } else if (error.contains('cancelled')) {
      return 'Sign in was cancelled. Please try again.';
    } else if (error.contains('network')) {
      return 'Network error. Please check your connection and try again.';
    } else if (error.contains('Account temporarily locked')) {
      return error; // Return the specific lockout message
    } else if (error.contains('Too many failed attempts')) {
      return error; // Return the specific lockout message
    } else {
      return 'Social sign in failed. Please try email/password login or contact support.';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppTheme.luxuryBlack,
              AppTheme.luxuryGrey,
              AppTheme.luxuryBlack,
            ],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(32),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header section
                      Center(
                        child: Column(
                          children: [
                            // Back button and title
                            Row(
                              children: [
                                IconButton(
                                  onPressed: () => Navigator.pop(context),
                                  icon: Icon(
                                    Icons.arrow_back_ios,
                                    color: AppTheme.luxuryWhite,
                                  ),
                                ),
                                const Spacer(),
                                Text(
                                  'SIGN IN',
                                  style: Theme.of(context)
                                      .textTheme
                                      .headlineLarge
                                      ?.copyWith(
                                        color: AppTheme.primaryGold,
                                        fontWeight: FontWeight.bold,
                                        letterSpacing: 2,
                                      ),
                                ),
                                const Spacer(),
                                const SizedBox(
                                  width: 48,
                                ), // Balance the back button
                              ],
                            ),
                            const SizedBox(height: 40),
                            // Welcome text
                            Text(
                              'Welcome Back',
                              style: Theme.of(context).textTheme.displaySmall
                                  ?.copyWith(
                                    color: AppTheme.luxuryWhite,
                                    fontWeight: FontWeight.w300,
                                    letterSpacing: 1,
                                  ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Sign in to your account',
                              style: Theme.of(context).textTheme.bodyLarge
                                  ?.copyWith(
                                    color: AppTheme.luxuryWhite.withValues(
                                      alpha: 0.7,
                                    ),
                                    fontWeight: FontWeight.w300,
                                  ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 48),
                      // Email field
                      Text(
                        'Email',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              color: AppTheme.luxuryWhite,
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                      const SizedBox(height: 8),
                      Semantics(
                        label: 'Email address field',
                        child: ValidatedTextField(
                          controller: _emailController,
                          hint: 'Enter your email',
                          keyboardType: TextInputType.emailAddress,
                          textInputAction: TextInputAction.next,
                          validator: InputValidation.validateEmail,
                          onChanged: (value) => _checkSocialLoginVisibility(),
                          prefixIcon: Icon(
                            Icons.email_outlined,
                            color: AppTheme.luxuryWhite.withValues(alpha: 0.7),
                          ),
                          style: TextStyle(
                            color: AppTheme.luxuryWhite,
                            fontSize: 16,
                          ),
                          hintStyle: TextStyle(
                            color: AppTheme.luxuryWhite.withValues(alpha: 0.5),
                          ),
                          fillColor: AppTheme.luxuryGrey.withValues(alpha: 0.3),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide.none,
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: AppTheme.primaryGold,
                              width: 2,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),
                      // Password field
                      Text(
                        'Password',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              color: AppTheme.luxuryWhite,
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                      const SizedBox(height: 8),
                      Semantics(
                        label: 'Password field',
                        child: ValidatedTextField(
                          controller: _passwordController,
                          hint: 'Enter your password',
                          obscureText: _obscurePassword,
                          keyboardType: TextInputType.visiblePassword,
                          textInputAction: TextInputAction.done,
                          validator: (value) {
                            // Use simpler validation for login (not registration)
                            if (value == null || value.isEmpty) {
                              return ValidationResult(
                                isValid: false,
                                errorMessage: 'Please enter your password',
                              );
                            }
                            return ValidationResult(isValid: true);
                          },
                          prefixIcon: Icon(
                            Icons.lock_outline,
                            color: AppTheme.luxuryWhite.withValues(alpha: 0.7),
                          ),
                          suffixIcon: IconButton(
                            icon: Icon(
                              _obscurePassword
                                  ? Icons.visibility_off
                                  : Icons.visibility,
                              color: AppTheme.luxuryWhite.withValues(
                                alpha: 0.7,
                              ),
                            ),
                            onPressed: () {
                              setState(() {
                                _obscurePassword = !_obscurePassword;
                              });
                            },
                            tooltip: _obscurePassword
                                ? 'Show password'
                                : 'Hide password',
                          ),
                          style: TextStyle(
                            color: AppTheme.luxuryWhite,
                            fontSize: 16,
                          ),
                          hintStyle: TextStyle(
                            color: AppTheme.luxuryWhite.withValues(alpha: 0.5),
                          ),
                          fillColor: AppTheme.luxuryGrey.withValues(alpha: 0.3),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide.none,
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: AppTheme.primaryGold,
                              width: 2,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      // Forgot password link
                      Align(
                        alignment: Alignment.centerRight,
                        child: TextButton(
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) =>
                                    const ForgotPasswordScreen(),
                              ),
                            );
                          },
                          child: Text(
                            'Forgot Password?',
                            style: TextStyle(
                              color: AppTheme.primaryGold,
                              fontWeight: FontWeight.w500,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 32),
                      // Error message
                      if (_error != null)
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.red.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Colors.red.withValues(alpha: 0.3),
                            ),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.error_outline,
                                color: Colors.red,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  _error!,
                                  style: TextStyle(
                                    color: Colors.red,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      if (_error != null) const SizedBox(height: 24),
                      // Sign in button
                      SizedBox(
                        width: double.infinity,
                        height: 56,
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _submit,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.primaryGold,
                            foregroundColor: AppTheme.luxuryBlack,
                            elevation: 8,
                            shadowColor: AppTheme.primaryGold.withValues(
                              alpha: 0.3,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                          ),
                          child: _isLoading
                              ? SizedBox(
                                  width: 24,
                                  height: 24,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      AppTheme.luxuryBlack,
                                    ),
                                  ),
                                )
                              : Text(
                                  'SIGN IN',
                                  style: Theme.of(context).textTheme.titleLarge
                                      ?.copyWith(
                                        fontWeight: FontWeight.bold,
                                        letterSpacing: 2,
                                      ),
                                ),
                        ),
                      ),
                      const SizedBox(height: 24),
                      // Social login section (conditionally shown)
                      if (_showSocialLogin) ...[
                        // Divider
                        Row(
                          children: [
                            Expanded(
                              child: Divider(
                                color: AppTheme.luxuryWhite.withValues(
                                  alpha: 0.3,
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                              ),
                              child: Text(
                                'OR',
                                style: TextStyle(
                                  color: AppTheme.luxuryWhite.withValues(
                                    alpha: 0.5,
                                  ),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            Expanded(
                              child: Divider(
                                color: AppTheme.luxuryWhite.withValues(
                                  alpha: 0.3,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 24),
                        // Social login buttons
                        Row(
                          children: [
                            Expanded(
                              child: OutlinedButton.icon(
                                onPressed: () => _handleAppleSignIn(),
                                icon: Icon(
                                  Icons.apple,
                                  color: AppTheme.luxuryWhite,
                                ),
                                label: Text(
                                  'Apple',
                                  style: TextStyle(
                                    color: AppTheme.luxuryWhite,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                style: OutlinedButton.styleFrom(
                                  side: BorderSide(
                                    color: AppTheme.luxuryWhite.withValues(
                                      alpha: 0.3,
                                    ),
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 16,
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: OutlinedButton.icon(
                                onPressed: () => _handleFacebookSignIn(),
                                icon: Icon(
                                  Icons.facebook,
                                  color: AppTheme.luxuryWhite,
                                ),
                                label: Text(
                                  'Facebook',
                                  style: TextStyle(
                                    color: AppTheme.luxuryWhite,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                style: OutlinedButton.styleFrom(
                                  side: BorderSide(
                                    color: AppTheme.luxuryWhite.withValues(
                                      alpha: 0.3,
                                    ),
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 16,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 32),
                      ],
                      // Register link
                      Center(
                        child: GestureDetector(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const RegisterScreen(),
                              ),
                            );
                          },
                          child: RichText(
                            text: TextSpan(
                              style: TextStyle(
                                color: AppTheme.luxuryWhite.withValues(
                                  alpha: 0.7,
                                ),
                                fontSize: 16,
                              ),
                              children: [
                                const TextSpan(text: "Don't have an account? "),
                                TextSpan(
                                  text: 'Sign Up',
                                  style: TextStyle(
                                    color: AppTheme.primaryGold,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
