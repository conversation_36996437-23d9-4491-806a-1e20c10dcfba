import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../services/verification_service.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/core/services/firebase_service.dart';

part 'verification_provider.g.dart';

@riverpod
class Verification extends _$Verification {
  final VerificationService _verificationService = VerificationService();
  final FirebaseService _firebaseService = getIt<FirebaseService>();

  @override
  Future<Map<String, dynamic>> build() async {
    final user = _firebaseService.currentUser;
    if (user == null) {
      return {
        'isVerified': false,
        'requires2FA': false,
        'emailVerified': false,
        'phoneVerified': false,
        'twoFactorVerified': false,
      };
    }

    final isVerified = await _verificationService.isUserVerified(user.uid);
    final requires2FA = await _verificationService.requires2FA(user.uid);

    return {
      'isVerified': isVerified,
      'requires2FA': requires2FA,
      'emailVerified': user.emailVerified,
      'phoneVerified': false, // Will be updated from Firestore
      'twoFactorVerified': false, // Will be updated from Firestore
    };
  }

  /// Send email verification code
  Future<void> sendEmailVerificationCode({
    String? ipAddress,
    String? deviceId,
  }) async {
    final user = _firebaseService.currentUser;
    if (user == null) throw Exception('No authenticated user');

    await _verificationService.sendEmailVerificationCode(
      userId: user.uid,
      email: user.email!,
      ipAddress: ipAddress,
      deviceId: deviceId,
    );

    // Refresh state
    ref.invalidateSelf();
  }

  /// Send phone verification code
  Future<void> sendPhoneVerificationCode({
    required String phone,
    String? ipAddress,
    String? deviceId,
  }) async {
    final user = _firebaseService.currentUser;
    if (user == null) throw Exception('No authenticated user');

    await _verificationService.sendPhoneVerificationCode(
      userId: user.uid,
      email: user.email!,
      phone: phone,
      ipAddress: ipAddress,
      deviceId: deviceId,
    );

    // Refresh state
    ref.invalidateSelf();
  }

  /// Send 2FA code
  Future<void> send2FACode({
    String? phone,
    String? ipAddress,
    String? deviceId,
  }) async {
    final user = _firebaseService.currentUser;
    if (user == null) throw Exception('No authenticated user');

    await _verificationService.send2FACode(
      userId: user.uid,
      email: user.email!,
      phone: phone,
      ipAddress: ipAddress,
      deviceId: deviceId,
    );

    // Refresh state
    ref.invalidateSelf();
  }

  /// Verify email code
  Future<bool> verifyEmailCode({
    required String code,
    String? ipAddress,
    String? deviceId,
  }) async {
    final user = _firebaseService.currentUser;
    if (user == null) throw Exception('No authenticated user');

    final success = await _verificationService.verifyCode(
      userId: user.uid,
      code: code,
      type: VerificationType.email,
      ipAddress: ipAddress,
      deviceId: deviceId,
    );

    if (success) {
      // Refresh state
      ref.invalidateSelf();
    }

    return success;
  }

  /// Verify phone code
  Future<bool> verifyPhoneCode({
    required String code,
    String? ipAddress,
    String? deviceId,
  }) async {
    final user = _firebaseService.currentUser;
    if (user == null) throw Exception('No authenticated user');

    final success = await _verificationService.verifyCode(
      userId: user.uid,
      code: code,
      type: VerificationType.phone,
      ipAddress: ipAddress,
      deviceId: deviceId,
    );

    if (success) {
      // Refresh state
      ref.invalidateSelf();
    }

    return success;
  }

  /// Verify 2FA code
  Future<bool> verify2FACode({
    required String code,
    String? ipAddress,
    String? deviceId,
  }) async {
    final user = _firebaseService.currentUser;
    if (user == null) throw Exception('No authenticated user');

    final success = await _verificationService.verifyCode(
      userId: user.uid,
      code: code,
      type: VerificationType.twoFactor,
      ipAddress: ipAddress,
      deviceId: deviceId,
    );

    if (success) {
      // Refresh state
      ref.invalidateSelf();
    }

    return success;
  }

  /// Check if user requires 2FA
  Future<bool> checkRequires2FA() async {
    final user = _firebaseService.currentUser;
    if (user == null) return false;

    return await _verificationService.requires2FA(user.uid);
  }

  /// Check if user is verified
  Future<bool> checkIsVerified() async {
    final user = _firebaseService.currentUser;
    if (user == null) return false;

    return await _verificationService.isUserVerified(user.uid);
  }

  /// Get verification attempts
  Future<List<VerificationAttempt>> getVerificationAttempts(
    VerificationType type,
  ) async {
    final user = _firebaseService.currentUser;
    if (user == null) return [];

    return await _verificationService.getVerificationAttempts(user.uid, type);
  }

  /// Refresh verification state
  void refresh() {
    ref.invalidateSelf();
  }
}
