import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/core/services/firebase_service.dart';
import 'package:billionaires_social/core/services/role_service.dart';
import 'package:billionaires_social/core/services/login_security_service.dart';
import 'package:billionaires_social/core/services/registration_security_service.dart';
import 'package:billionaires_social/core/services/universal_account_service.dart';
import '../services/verification_service.dart';

part 'auth_provider.g.dart';

enum AccountType { user, business }

@riverpod
class Auth extends _$Auth {
  @override
  Stream<User?> build() {
    final firebaseService = getIt<FirebaseService>();
    return firebaseService.authStateChanges;
  }

  // Enhanced sign up with role-based account creation
  Future<Map<String, dynamic>> signUp({
    required String email,
    required String password,
    required String name,
    required AccountType accountType,
  }) async {
    final firebaseService = getIt<FirebaseService>();
    final registrationSecurityService = RegistrationSecurityService();

    // Get device ID and IP address
    final deviceId = await registrationSecurityService.getDeviceId();
    final ipAddress = registrationSecurityService.getIpAddress();

    // Check if registration is allowed
    final isAllowed = await registrationSecurityService.isRegistrationAllowed(
      deviceId,
      ipAddress,
    );
    if (!isAllowed) {
      throw Exception(
        'You\'ve reached the registration limit for today. Please try again later or contact support.',
      );
    }

    // Check if email is already registered
    final isEmailRegistered = await registrationSecurityService
        .isEmailRegistered(email);
    if (isEmailRegistered) {
      throw Exception(
        'Email is already registered. Please use a different email or sign in.',
      );
    }

    // Validate password strength
    if (!registrationSecurityService.validatePasswordStrength(password)) {
      throw Exception(
        'Password must be at least 8 characters with 1 number and 1 special character.',
      );
    }

    try {
      // Create Firebase user account
      await firebaseService.signUpWithEmailAndPassword(
        email: email,
        password: password,
        name: name,
      );

      // Get current user
      final user = firebaseService.currentUser;
      if (user == null) {
        throw Exception('Account creation failed - no user returned');
      }

      // Create user document in Firestore with role information
      await _createUserDocument(user, name, email, accountType);

      // Record successful registration attempt
      await registrationSecurityService.recordRegistrationAttempt(
        email: email,
        accountType: accountType.name,
        success: true,
        ipAddress: ipAddress,
        deviceId: deviceId,
      );

      // Get navigation route based on account type
      final navigationRoute = _getRegistrationNavigationRoute(accountType);

      return {
        'user': user,
        'accountType': accountType,
        'navigationRoute': navigationRoute,
        'requiresEmailVerification': true,
        'requiresPhoneVerification': accountType == AccountType.business,
      };
    } catch (e) {
      // Record failed registration attempt
      await registrationSecurityService.recordRegistrationAttempt(
        email: email,
        accountType: accountType.name,
        success: false,
        ipAddress: ipAddress,
        deviceId: deviceId,
        errorMessage: e.toString(),
      );

      // Re-throw the original error
      rethrow;
    }
  }

  // Create user document in Firestore
  Future<void> _createUserDocument(
    User user,
    String name,
    String email,
    AccountType accountType,
  ) async {
    final firestore = FirebaseFirestore.instance;

    // Use universal account template for consistency
    final userData = UniversalAccountService.getUniversalAccountTemplate(
      uid: user.uid,
      email: email,
      name: name,
      username: email.split('@')[0],
      isBusinessAccount: accountType == AccountType.business,
    );

    // Add account-type specific fields
    userData['role'] = accountType.name;
    if (accountType == AccountType.business) {
      userData['businessName'] = '';
      userData['businessCategory'] = '';
    }

    await firestore.collection('users').doc(user.uid).set(userData);

    // Apply universal account defaults
    await UniversalAccountService.initializeNewUser(user.uid);
  }

  // Get navigation route for registration
  String _getRegistrationNavigationRoute(AccountType accountType) {
    switch (accountType) {
      case AccountType.user:
        return '/main';
      case AccountType.business:
        return '/business/setup';
    }
  }

  // Sign in with enhanced security and role detection
  Future<Map<String, dynamic>> signIn({
    required String email,
    required String password,
  }) async {
    final firebaseService = getIt<FirebaseService>();
    final loginSecurityService = LoginSecurityService();
    final roleService = RoleService();

    // Check if login is allowed
    final isAllowed = await loginSecurityService.isLoginAllowed(email);
    if (!isAllowed) {
      final lockoutTime = loginSecurityService.getLockoutTimeRemaining(email);
      throw Exception(
        'Account temporarily locked. Please try again in ${lockoutTime?.inMinutes ?? 10} minutes.',
      );
    }

    try {
      // Attempt Firebase authentication
      await firebaseService.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Get current user
      final user = firebaseService.currentUser;
      if (user == null) {
        throw Exception('Authentication failed - no user returned');
      }

      // Record successful login attempt
      await loginSecurityService.recordLoginAttempt(
        email: email,
        success: true,
        ipAddress: loginSecurityService.getIpAddress(),
        deviceId: loginSecurityService.getDeviceId(),
      );

      // Get user role
      final role = await roleService.getUserRole(user.uid);

      // Check if 2FA is required
      final requires2FA = await roleService.requires2FA(user.uid);

      // Get navigation route
      final navigationRoute = roleService.getNavigationRoute(role);

      return {
        'user': user,
        'role': role,
        'requires2FA': requires2FA,
        'navigationRoute': navigationRoute,
        'shouldShowSocialLogin': roleService.shouldShowSocialLogin(role),
      };
    } catch (e) {
      // Record failed login attempt
      await loginSecurityService.recordLoginAttempt(
        email: email,
        success: false,
        ipAddress: loginSecurityService.getIpAddress(),
        deviceId: loginSecurityService.getDeviceId(),
        errorMessage: e.toString(),
      );

      // Check remaining attempts
      final remainingAttempts = loginSecurityService.getRemainingAttempts(
        email,
      );
      if (remainingAttempts <= 0) {
        throw Exception(
          'Too many failed attempts. Account locked for 10 minutes.',
        );
      }

      // Re-throw the original error
      rethrow;
    }
  }

  // Sign out
  Future<void> signOut() async {
    final firebaseService = getIt<FirebaseService>();
    await firebaseService.signOut();
  }

  // Reset password
  Future<void> resetPassword(String email) async {
    final firebaseService = getIt<FirebaseService>();
    await firebaseService.resetPassword(email);
  }

  // Verify backup code for 2FA recovery
  Future<void> verifyBackupCode(String backupCode) async {
    final firebaseService = getIt<FirebaseService>();
    await firebaseService.verifyBackupCode(backupCode);
  }

  // Social Login Methods

  // Sign in with Apple
  Future<Map<String, dynamic>> signInWithApple() async {
    final loginSecurityService = LoginSecurityService();

    try {
      // TODO: Replace with actual Apple Sign In implementation
      // For now, this is a placeholder that simulates Apple Sign In

      // In a real implementation, you would:
      // 1. Use sign_in_with_apple package
      // 2. Get Apple ID credential
      // 3. Create Firebase credential from Apple credential
      // 4. Sign in with Firebase using the credential

      // Placeholder implementation
      throw Exception(
        'Apple Sign In not yet configured. Please use email/password login or contact support.',
      );

      // Real implementation would look like:
      /*
      final appleCredential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      final oauthCredential = OAuthProvider("apple.com").credential(
        idToken: appleCredential.identityToken,
        accessToken: appleCredential.authorizationCode,
      );

      final userCredential = await FirebaseAuth.instance.signInWithCredential(oauthCredential);
      final user = userCredential.user;

      if (user != null) {
        // Create or update user document
        await _createOrUpdateSocialUser(
          user: user,
          name: '${appleCredential.givenName ?? ''} ${appleCredential.familyName ?? ''}'.trim(),
          email: appleCredential.email ?? user.email ?? '',
          provider: 'apple',
        );

        // Get user role and navigation
        final role = await roleService.getUserRole(user.uid);
        final navigationRoute = roleService.getNavigationRoute(role);

        return {
          'user': user,
          'role': role,
          'requires2FA': false, // Social login typically doesn't require 2FA initially
          'navigationRoute': navigationRoute,
          'provider': 'apple',
        };
      }
      */
    } catch (e) {
      // Record failed login attempt
      await loginSecurityService.recordLoginAttempt(
        email: 'apple_signin',
        success: false,
        ipAddress: loginSecurityService.getIpAddress(),
        deviceId: loginSecurityService.getDeviceId(),
        errorMessage: e.toString(),
      );

      rethrow;
    }
  }

  // Sign in with Facebook
  Future<Map<String, dynamic>> signInWithFacebook() async {
    final loginSecurityService = LoginSecurityService();

    try {
      // TODO: Replace with actual Facebook Sign In implementation
      // For now, this is a placeholder that simulates Facebook Sign In

      // In a real implementation, you would:
      // 1. Use flutter_facebook_auth package
      // 2. Get Facebook access token
      // 3. Create Firebase credential from Facebook token
      // 4. Sign in with Firebase using the credential

      // Placeholder implementation
      throw Exception(
        'Facebook Sign In not yet configured. Please use email/password login or contact support.',
      );

      // Real implementation would look like:
      /*
      final LoginResult result = await FacebookAuth.instance.login();

      if (result.status == LoginStatus.success) {
        final AccessToken accessToken = result.accessToken!;

        final OAuthCredential facebookAuthCredential =
            FacebookAuthProvider.credential(accessToken.token);

        final userCredential = await FirebaseAuth.instance.signInWithCredential(facebookAuthCredential);
        final user = userCredential.user;

        if (user != null) {
          // Get Facebook user data
          final userData = await FacebookAuth.instance.getUserData();

          // Create or update user document
          await _createOrUpdateSocialUser(
            user: user,
            name: userData['name'] ?? '',
            email: userData['email'] ?? user.email ?? '',
            provider: 'facebook',
            profilePictureUrl: userData['picture']?['data']?['url'],
          );

          // Get user role and navigation
          final role = await roleService.getUserRole(user.uid);
          final navigationRoute = roleService.getNavigationRoute(role);

          return {
            'user': user,
            'role': role,
            'requires2FA': false, // Social login typically doesn't require 2FA initially
            'navigationRoute': navigationRoute,
            'provider': 'facebook',
          };
        }
      }
      */
    } catch (e) {
      // Record failed login attempt
      await loginSecurityService.recordLoginAttempt(
        email: 'facebook_signin',
        success: false,
        ipAddress: loginSecurityService.getIpAddress(),
        deviceId: loginSecurityService.getDeviceId(),
        errorMessage: e.toString(),
      );

      rethrow;
    }
  }

  // Send email verification code
  Future<void> sendEmailVerificationCode({
    String? ipAddress,
    String? deviceId,
  }) async {
    final user = currentUser;
    if (user == null) throw Exception('No authenticated user');

    final verificationService = getIt<VerificationService>();
    await verificationService.sendEmailVerificationCode(
      userId: user.uid,
      email: user.email!,
      ipAddress: ipAddress,
      deviceId: deviceId,
    );
  }

  // Send phone verification code
  Future<void> sendPhoneVerificationCode({
    required String phone,
    String? ipAddress,
    String? deviceId,
  }) async {
    final user = currentUser;
    if (user == null) throw Exception('No authenticated user');

    final verificationService = getIt<VerificationService>();
    await verificationService.sendPhoneVerificationCode(
      userId: user.uid,
      email: user.email!,
      phone: phone,
      ipAddress: ipAddress,
      deviceId: deviceId,
    );
  }

  // Send 2FA code
  Future<void> send2FACode({
    String? phone,
    String? ipAddress,
    String? deviceId,
  }) async {
    final user = currentUser;
    if (user == null) throw Exception('No authenticated user');

    final verificationService = getIt<VerificationService>();
    await verificationService.send2FACode(
      userId: user.uid,
      email: user.email!,
      phone: phone,
      ipAddress: ipAddress,
      deviceId: deviceId,
    );
  }

  // Verify email code
  Future<bool> verifyEmailCode({
    required String code,
    String? ipAddress,
    String? deviceId,
  }) async {
    final user = currentUser;
    if (user == null) throw Exception('No authenticated user');

    final verificationService = getIt<VerificationService>();
    return await verificationService.verifyCode(
      userId: user.uid,
      code: code,
      type: VerificationType.email,
      ipAddress: ipAddress,
      deviceId: deviceId,
    );
  }

  // Verify phone code
  Future<bool> verifyPhoneCode({
    required String code,
    String? ipAddress,
    String? deviceId,
  }) async {
    final user = currentUser;
    if (user == null) throw Exception('No authenticated user');

    final verificationService = getIt<VerificationService>();
    return await verificationService.verifyCode(
      userId: user.uid,
      code: code,
      type: VerificationType.phone,
      ipAddress: ipAddress,
      deviceId: deviceId,
    );
  }

  // Verify 2FA code
  Future<bool> verify2FACode({
    required String code,
    String? ipAddress,
    String? deviceId,
  }) async {
    final user = currentUser;
    if (user == null) throw Exception('No authenticated user');

    final verificationService = getIt<VerificationService>();
    return await verificationService.verifyCode(
      userId: user.uid,
      code: code,
      type: VerificationType.twoFactor,
      ipAddress: ipAddress,
      deviceId: deviceId,
    );
  }

  // Get current user
  User? get currentUser => getIt<FirebaseService>().currentUser;

  // Get user role (for use in other parts of the app)
  Future<UserRole> getCurrentUserRole() async {
    final user = currentUser;
    if (user == null) throw Exception('No authenticated user');

    final roleService = RoleService();
    return await roleService.getUserRole(user.uid);
  }

  // Check if current user requires 2FA
  Future<bool> getCurrentUserRequires2FA() async {
    final user = currentUser;
    if (user == null) return false;

    final roleService = RoleService();
    return await roleService.requires2FA(user.uid);
  }

  // Helper method to create or update social user document
  // ignore: unused_element
  Future<void> _createOrUpdateSocialUser({
    required User user,
    required String name,
    required String email,
    required String provider,
    String? profilePictureUrl,
  }) async {
    final firestore = FirebaseFirestore.instance;
    final userDoc = firestore.collection('users').doc(user.uid);

    // Check if user document already exists
    final docSnapshot = await userDoc.get();

    if (docSnapshot.exists) {
      // Update existing user with social login info
      await userDoc.update({
        'lastLoginAt': FieldValue.serverTimestamp(),
        'loginProvider': provider,
        if (profilePictureUrl != null && profilePictureUrl.isNotEmpty)
          'profilePictureUrl': profilePictureUrl,
      });
    } else {
      // Create new user document for social login
      await userDoc.set({
        'uid': user.uid,
        'email': email,
        'name': name.isNotEmpty ? name : email.split('@')[0],
        'username': email.split('@')[0],
        'role': AccountType.user.name,
        'isBusinessAccount': false,
        'isAdmin': false,
        'isVerified': false,
        'isBillionaire': false,
        'requires2FA': false,
        'profilePictureUrl': profilePictureUrl ?? '',
        'bio': '',
        'postCount': 0,
        'followerCount': 0,
        'followingCount': 0,
        'businessName': null,
        'businessCategory': null,
        'website': '',
        'loginProvider': provider,
        'createdAt': FieldValue.serverTimestamp(),
        'lastLoginAt': FieldValue.serverTimestamp(),
      });
    }
  }
}
