// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'verification_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$verificationHash() => r'ff2a521e7ff7c89cfe0e217c16b6f15ca905bd28';

/// See also [Verification].
@ProviderFor(Verification)
final verificationProvider =
    AutoDisposeAsyncNotifierProvider<
      Verification,
      Map<String, dynamic>
    >.internal(
      Verification.new,
      name: r'verificationProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$verificationHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$Verification = AutoDisposeAsyncNotifier<Map<String, dynamic>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
