# Content Discovery System

## Overview

The Content Discovery System provides an engaging onboarding experience for new users with zero followers while maintaining strict privacy controls and user preferences. It helps users discover relevant content and accounts without compromising existing user experience.

## Core Components

### 1. Services

#### ContentDiscoveryService (`services/content_discovery_service.dart`)
- **Purpose**: Core logic for finding suggested accounts and discovery content
- **Key Methods**:
  - `getSuggestedAccounts()`: Returns personalized account suggestions
  - `getDiscoveryContent()`: Provides curated public posts
  - `needsDiscoveryContent()`: Checks if user needs discovery features

**Algorithm for Suggestions**:
- Mutual connections (friends of friends) - 25%
- High engagement accounts (trending users) - 25%
- Verified/popular accounts - 25%
- Recently active public profiles - 25%

#### ContentAccessService (`services/content_access_service.dart`)
- **Purpose**: Privacy-first access control and content filtering
- **Key Methods**:
  - `canViewPost()`: Validates post visibility permissions
  - `canViewStory()`: Validates story access (followers only)
  - `filterPostsByPermissions()`: Bulk content filtering
  - `getContentPreferences()`: User preference management

**Privacy Rules**:
- Public posts: Visible to everyone if active
- Followers-only: Requires following relationship
- Close friends: Requires close friends group membership
- Private: Only visible to owner
- Stories: Always require following relationship

#### DiscoveryAnalyticsService (`services/discovery_analytics_service.dart`)
- **Purpose**: Track discovery system performance and user engagement
- **Metrics Tracked**:
  - Suggested account views/follows/dismissals
  - Discovery content interactions
  - Content preference changes
  - Onboarding progress
  - Conversion rates

### 2. Providers (Riverpod State Management)

#### SuggestedAccountsProvider (`providers/discovery_feed_provider.dart`)
- Manages suggested accounts list
- Auto-refreshes every hour
- Removes accounts when followed/dismissed

#### DiscoveryContentProvider
- Provides discovery posts for new users
- Respects user preferences
- Implements pagination

#### ContentPreferencesProvider
- Manages user discovery settings
- Syncs with Firestore
- Triggers content refresh on changes

#### HybridFeedProvider
- Mixes following + discovery content
- Respects discovery percentage setting (0-50%)
- Provides seamless user experience

### 3. UI Components

#### SuggestedAccountsWidget (`widgets/suggested_accounts_widget.dart`)
- Horizontal scrollable account suggestions
- Follow/dismiss functionality
- "See All" modal for full list
- Loading skeletons

#### DiscoveryPostIndicator (`widgets/discovery_post_indicator.dart`)
- Visual indicators for discovery posts
- Types: suggested, trending, popular, mutual
- Follow buttons and dismiss options
- Enhanced version with context

#### NewUserOnboarding (`widgets/new_user_onboarding.dart`)
- Full onboarding experience for new users
- Discovery content preview
- Action buttons for engagement
- Compact version for feed integration

#### ContentPreferencesScreen (`screens/content_preferences_screen.dart`)
- Complete preference management UI
- Discovery toggles and sliders
- Content type filtering
- Blocked keywords management

### 4. Enhanced Feed Integration

#### EnhancedFeedScreen (`screens/enhanced_feed_screen.dart`)
- Seamlessly integrates discovery content
- Smart empty states for new users
- Preference-aware content mixing
- Performance optimized scrolling

## User Experience Flow

### New User (0 followers)
1. **Welcome**: See onboarding with suggested accounts
2. **Discovery**: Mixed feed with 30% discovery content by default
3. **Engagement**: Follow suggestions, interact with content
4. **Transition**: As user follows more accounts, discovery percentage decreases

### Existing User
1. **Preference Control**: Can enable/disable discovery features
2. **Customization**: Adjust discovery percentage (0-50%)
3. **Privacy**: All existing privacy rules maintained
4. **Performance**: No impact on regular feed performance

## Privacy & Security

### Access Control
- **Never expose**: Private posts, followers-only content to non-followers
- **Story restrictions**: Only from followed users
- **Block/mute respect**: Honor all existing user blocks
- **Report integration**: Full reporting system for discovery content

### Data Protection
- **Minimal data collection**: Only public information used
- **User consent**: All discovery features opt-in
- **Cache expiry**: Suggestions cached for max 1 hour
- **Analytics anonymization**: User IDs hashed for system metrics

## Performance Optimizations

### Caching Strategy
- **Suggested accounts**: 1-hour cache per user
- **Discovery content**: Real-time with pagination
- **User preferences**: Cached until changed
- **Analytics**: Batched writes, periodic cleanup

### Database Queries
- **Compound indexes**: Optimized for discovery queries
- **Batch operations**: Efficient user lookups
- **Rate limiting**: Prevents spam and abuse
- **Geographic queries**: GeoHash for location-based content

### Memory Management
- **Lazy loading**: Content loaded on demand
- **Image caching**: Efficient profile picture handling
- **State cleanup**: Proper disposal of providers
- **Background refresh**: Non-blocking updates

## Configuration

### Default Settings
```dart
{
  'showSuggestedContent': true,
  'showTrendingPosts': true,
  'showRegionalContent': true,
  'discoveryContentPercentage': 30.0,
  'showVerifiedOnly': false,
  'enableContentFiltering': true,
  'contentTypes': {
    'images': true,
    'videos': true,
    'text': true,
    'links': true,
  },
}
```

### Customizable Parameters
- **Discovery percentage**: 0-50% of feed content
- **Content types**: Filter by media type
- **Geographic scope**: Regional content preferences
- **Verification filter**: Verified accounts only
- **Keyword blocking**: Custom blocked terms

## Analytics & Metrics

### Success Metrics
- **User engagement**: Increased interaction for new users
- **Follow conversion**: % of suggestions that result in follows
- **Content interaction**: Likes/comments on discovery posts
- **User retention**: New user retention improvement
- **Privacy compliance**: Zero privacy violations

### Tracked Events
- Suggested account views/follows/dismissals
- Discovery content views/interactions
- Preference changes
- Onboarding completion rates
- Empty state interactions

## Implementation Notes

### Dependencies
- Firebase Auth for user identification
- Firestore for data storage and queries
- Riverpod for state management
- GetIt for dependency injection
- CacheService for performance optimization

### Error Handling
- Graceful degradation when services fail
- Fallback to regular feed if discovery fails
- User-friendly error messages
- Comprehensive logging for debugging

### Testing Strategy
- Unit tests for all service methods
- Widget tests for UI components
- Integration tests for full user flows
- Performance tests for large datasets
- Privacy compliance testing

## Future Enhancements

### Planned Features
- **Machine learning**: Improved suggestion algorithms
- **Real-time updates**: Live trending content
- **Social signals**: Friend activity integration
- **Content quality**: AI-powered content scoring
- **Personalization**: Advanced user preference learning

### Scalability Considerations
- **Cloud functions**: Move heavy computations server-side
- **CDN integration**: Faster content delivery
- **Microservices**: Separate discovery service
- **A/B testing**: Experiment with different algorithms
- **Global deployment**: Multi-region content discovery

## Troubleshooting

### Common Issues
1. **No suggestions showing**: Check user preferences and cache
2. **Performance issues**: Verify query indexes and caching
3. **Privacy violations**: Review access control logic
4. **Analytics not tracking**: Check service registration and permissions

### Debug Tools
- Comprehensive logging with prefixes (🔍, ❌, ✅)
- Analytics dashboard for system metrics
- User preference debugging in settings
- Cache inspection tools for developers

This system provides a comprehensive solution for content discovery while maintaining the highest standards of privacy, performance, and user experience.
