import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/core/instagram_theme.dart';
import 'package:billionaires_social/features/feed/providers/discovery_feed_provider.dart';
import 'package:billionaires_social/features/feed/providers/feed_provider.dart';
import 'package:billionaires_social/features/feed/providers/feed_filter_provider.dart';
import 'package:billionaires_social/features/feed/widgets/post_card.dart';
import 'package:billionaires_social/features/feed/widgets/post_card_skeleton.dart';
import 'package:billionaires_social/features/feed/widgets/category_selector.dart';
import 'package:billionaires_social/features/feed/widgets/suggested_accounts_widget.dart';
import 'package:billionaires_social/features/feed/widgets/discovery_post_indicator.dart';
import 'package:billionaires_social/features/stories/widgets/hybrid_story_carousel.dart';
import 'package:billionaires_social/features/stories/providers/story_provider.dart';
import 'package:billionaires_social/features/notifications/screens/notifications_screen.dart';
import 'package:billionaires_social/features/messaging/screens/chat_list_screen.dart';
import 'package:billionaires_social/features/creation/screens/create_hub_screen.dart';
import 'package:billionaires_social/features/profile/screens/content_preferences_screen.dart';
import 'package:firebase_auth/firebase_auth.dart';

class EnhancedFeedScreen extends ConsumerStatefulWidget {
  const EnhancedFeedScreen({super.key});

  @override
  ConsumerState<EnhancedFeedScreen> createState() => _EnhancedFeedScreenState();
}

class _EnhancedFeedScreenState extends ConsumerState<EnhancedFeedScreen> {
  final _scrollController = ScrollController();
  bool _showDiscoveryContent = true;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _loadUserPreferences();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    // Load more content when near bottom
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreContent();
    }
  }

  Future<void> _loadUserPreferences() async {
    final preferences = await ref.read(contentPreferencesProvider.future);
    setState(() {
      _showDiscoveryContent =
          preferences['showSuggestedContent'] as bool? ?? true;
    });
  }

  Future<void> _loadMoreContent() async {
    // Load more from discovery feeds (feed provider handles its own pagination)
    if (_showDiscoveryContent) {
      final discoveryNotifier = ref.read(discoveryContentProvider.notifier);
      await discoveryNotifier.loadMore();
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentUser = FirebaseAuth.instance.currentUser;

    return Scaffold(
      backgroundColor: InstagramTheme.white,
      appBar: AppBar(
        backgroundColor: InstagramTheme.white,
        title: Text(
          'Billionaires Social',
          style: AppTheme.fontStyles.title.copyWith(
            color: InstagramTheme.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            onPressed: () => Navigator.of(context).push(
              MaterialPageRoute(
                builder: (_) => const ContentPreferencesScreen(),
              ),
            ),
            icon: const FaIcon(FontAwesomeIcons.sliders, size: 20),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).push(
              MaterialPageRoute(builder: (_) => const NotificationsScreen()),
            ),
            icon: const FaIcon(FontAwesomeIcons.bell),
          ),
          IconButton(
            onPressed: () => Navigator.of(
              context,
            ).push(MaterialPageRoute(builder: (_) => const ChatListScreen())),
            icon: const FaIcon(FontAwesomeIcons.paperPlane),
          ),
          IconButton(
            onPressed: () => showModalBottomSheet(
              context: context,
              backgroundColor: Colors.transparent,
              builder: (_) => const CreateHubScreen(),
            ),
            icon: const FaIcon(FontAwesomeIcons.plus),
          ),
        ],
        elevation: 0,
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          // Refresh all content
          await Future.wait([
            ref.read(feedProvider.notifier).refresh(),
            ref.read(storyReelsProvider.notifier).refresh(),
            if (_showDiscoveryContent) ...[
              ref.read(suggestedAccountsProvider.notifier).refresh(),
              ref.read(discoveryContentProvider.notifier).refresh(),
            ],
          ]);
        },
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        color: Theme.of(context).colorScheme.secondary,
        child: CustomScrollView(
          controller: _scrollController,
          slivers: [
            // Stories section
            SliverToBoxAdapter(
              child: Container(
                decoration: const BoxDecoration(
                  color: InstagramTheme.white,
                  border: Border(
                    bottom: BorderSide(
                      color: InstagramTheme.borderGray,
                      width: 0.5,
                    ),
                  ),
                ),
                child: Consumer(
                  builder: (context, ref, child) {
                    final storyReelsAsync = ref.watch(storyReelsProvider);
                    return storyReelsAsync.when(
                      data: (storyReels) =>
                          HybridStoryCarousel(storyReels: storyReels),
                      loading: () => const SizedBox(
                        height: 100,
                        child: Center(child: CircularProgressIndicator()),
                      ),
                      error: (error, stack) => const SizedBox.shrink(),
                    );
                  },
                ),
              ),
            ),

            // Suggested accounts (only for users with few followers)
            if (_showDiscoveryContent && currentUser != null)
              const SliverToBoxAdapter(child: SuggestedAccountsWidget()),

            // Category selector
            const SliverToBoxAdapter(child: CategorySelector()),

            // Feed content
            _buildFeedContent(),
          ],
        ),
      ),
    );
  }

  Widget _buildFeedContent() {
    final currentFilter = ref.watch(feedFilterProvider);

    // Use hybrid feed for 'all' filter, regular feed for specific filters
    final selectedFeedProvider =
        currentFilter == FeedFilterType.all && _showDiscoveryContent
        ? hybridFeedProvider
        : feedProvider;

    final feedState = ref.watch(selectedFeedProvider);

    return feedState.when(
      data: (posts) {
        if (posts.isEmpty) {
          return _buildEmptyState(currentFilter);
        }

        return SliverList(
          delegate: SliverChildBuilderDelegate((context, index) {
            final post = posts[index];

            return Column(
              children: [
                // Show discovery indicator for discovery posts
                if (_shouldShowDiscoveryIndicator(post))
                  _buildDiscoveryIndicator(post),

                // Post card
                PostCard(post: post),

                // Insert suggested accounts periodically
                if (_shouldInsertSuggestedAccounts(index))
                  const SuggestedAccountsWidget(),
              ],
            );
          }, childCount: posts.length),
        );
      },
      loading: () => SliverList(
        delegate: SliverChildBuilderDelegate(
          (context, index) => const PostCardSkeleton(),
          childCount: 5,
        ),
      ),
      error: (error, stack) => SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text('Something went wrong', style: AppTheme.fontStyles.title),
              const SizedBox(height: 8),
              Text(
                error.toString(),
                style: AppTheme.fontStyles.body,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => ref.invalidate(feedProvider),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(FeedFilterType currentFilter) {
    if (currentFilter == FeedFilterType.followed) {
      return SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.people_outline, size: 64, color: Colors.grey),
              const SizedBox(height: 16),
              Text('No Posts in Your Feed', style: AppTheme.fontStyles.title),
              const SizedBox(height: 8),
              Text(
                'Follow some accounts to see their posts here',
                style: AppTheme.fontStyles.body,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              if (_showDiscoveryContent) ...[
                ElevatedButton(
                  onPressed: () {
                    // Switch to discovery content
                    ref
                        .read(feedFilterProvider.notifier)
                        .setFilter(FeedFilterType.all);
                  },
                  child: const Text('Discover Content'),
                ),
                const SizedBox(height: 12),
              ],
              ElevatedButton.icon(
                onPressed: () => showModalBottomSheet(
                  context: context,
                  backgroundColor: Colors.transparent,
                  builder: (_) => const CreateHubScreen(),
                ),
                icon: const Icon(Icons.add),
                label: const Text('Create Post'),
              ),
            ],
          ),
        ),
      );
    }

    return SliverFillRemaining(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.explore_outlined, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            Text('No Content Found', style: AppTheme.fontStyles.title),
            const SizedBox(height: 8),
            Text(
              'Try adjusting your filters or create some content',
              style: AppTheme.fontStyles.body,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => showModalBottomSheet(
                context: context,
                backgroundColor: Colors.transparent,
                builder: (_) => const CreateHubScreen(),
              ),
              icon: const Icon(Icons.add),
              label: const Text('Create Post'),
            ),
          ],
        ),
      ),
    );
  }

  bool _shouldShowDiscoveryIndicator(dynamic post) {
    // Check if this is a discovery post (from non-followed users)
    // This would need to be implemented based on your post model
    return false; // Placeholder
  }

  Widget _buildDiscoveryIndicator(dynamic post) {
    return EnhancedDiscoveryIndicator(
      type: 'suggested',
      username: post.username,
      reason: 'Popular in your network',
      onDismiss: () {
        // Remove post from discovery feed
        ref.read(discoveryContentProvider.notifier).removePost(post.id);
      },
      onFollow: () async {
        // Follow the user
        // Implementation depends on your follow service
      },
    );
  }

  bool _shouldInsertSuggestedAccounts(int index) {
    // Insert suggested accounts every 10 posts for discovery
    return _showDiscoveryContent && index > 0 && (index + 1) % 10 == 0;
  }
}
