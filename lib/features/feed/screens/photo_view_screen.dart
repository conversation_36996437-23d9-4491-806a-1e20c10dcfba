import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class PhotoViewScreen extends StatefulWidget {
  final String imageUrl;
  final String heroTag;

  const PhotoViewScreen({
    super.key,
    required this.imageUrl,
    required this.heroTag,
  });

  @override
  State<PhotoViewScreen> createState() => _PhotoViewScreenState();
}

class _PhotoViewScreenState extends State<PhotoViewScreen>
    with TickerProviderStateMixin {
  bool _showControls = true;
  final TransformationController _transformationController =
      TransformationController();
  late AnimationController _resetAnimationController;

  @override
  void initState() {
    super.initState();
    _resetAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
  }

  void _resetZoom() {
    final currentMatrix = _transformationController.value;
    final targetMatrix = Matrix4.identity();

    final resetAnimation = Matrix4Tween(begin: currentMatrix, end: targetMatrix)
        .animate(
          CurvedAnimation(
            parent: _resetAnimationController,
            curve: Curves.easeOutCubic,
          ),
        );

    resetAnimation.addListener(() {
      _transformationController.value = resetAnimation.value;
    });

    _resetAnimationController.forward().then((_) {
      resetAnimation.removeListener(() {});
      _transformationController.value = targetMatrix;
    });
  }

  void _onInteractionEnd(ScaleEndDetails details) {
    // Auto-reset to original scale and position when user releases
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _resetZoom();
      }
    });
  }

  @override
  void dispose() {
    _transformationController.dispose();
    _resetAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: Colors.black,
      extendBodyBehindAppBar: true,
      appBar: _showControls
          ? AppBar(
              backgroundColor: Colors.black.withValues(alpha: 0.3),
              elevation: 0,
              iconTheme: const IconThemeData(color: Colors.white),
              actions: [
                IconButton(
                  icon: const Icon(Icons.zoom_out, color: Colors.white),
                  onPressed: _resetZoom,
                ),
              ],
            )
          : null,
      body: GestureDetector(
        onTap: _toggleControls,
        onDoubleTap: _resetZoom,
        child: SizedBox.expand(
          child: InteractiveViewer(
            transformationController: _transformationController,
            minScale: 0.5,
            maxScale: 4.0,
            onInteractionEnd: _onInteractionEnd,
            child: Hero(
              tag: widget.heroTag,
              child: CachedNetworkImage(
                imageUrl: widget.imageUrl,
                fit: BoxFit.contain,
                width: screenSize.width,
                height: screenSize.height,
                placeholder: (context, url) => Container(
                  color: Colors.grey[900],
                  width: screenSize.width,
                  height: screenSize.height,
                  child: const Center(
                    child: CircularProgressIndicator(color: Colors.white),
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  color: Colors.grey[900],
                  width: screenSize.width,
                  height: screenSize.height,
                  child: const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          color: Colors.white,
                          size: 48,
                        ),
                        SizedBox(height: 16),
                        Text(
                          'Failed to load image',
                          style: TextStyle(color: Colors.white),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
