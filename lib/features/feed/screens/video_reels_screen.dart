import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';

/// TikTok/Instagram Reels-style video player screen
class VideoReelsScreen extends ConsumerStatefulWidget {
  final List<Post> videoPosts;
  final int initialIndex;

  const VideoReelsScreen({
    super.key,
    required this.videoPosts,
    this.initialIndex = 0,
  });

  @override
  ConsumerState<VideoReelsScreen> createState() => _VideoReelsScreenState();
}

class _VideoReelsScreenState extends ConsumerState<VideoReelsScreen> {
  late PageController _pageController;
  int _currentIndex = 0;
  final Map<int, VideoPlayerController> _videoControllers = {};
  final Map<int, ChewieController> _chewieControllers = {};

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
    _initializeVideoControllers();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _disposeVideoControllers();
    super.dispose();
  }

  void _initializeVideoControllers() {
    // Initialize controllers for current and adjacent videos
    final startIndex = (_currentIndex - 1).clamp(
      0,
      widget.videoPosts.length - 1,
    );
    final endIndex = (_currentIndex + 1).clamp(0, widget.videoPosts.length - 1);

    for (int i = startIndex; i <= endIndex; i++) {
      _initializeVideoController(i);
    }
  }

  void _initializeVideoController(int index) {
    if (index < 0 || index >= widget.videoPosts.length) return;
    if (_videoControllers.containsKey(index)) return;

    final post = widget.videoPosts[index];
    final videoController = VideoPlayerController.networkUrl(
      Uri.parse(post.mediaUrl),
    );

    _videoControllers[index] = videoController;

    videoController
        .initialize()
        .then((_) {
          if (mounted) {
            final chewieController = ChewieController(
              videoPlayerController: videoController,
              autoPlay: index == _currentIndex,
              looping: true,
              showControls: false,
              showControlsOnInitialize: false,
              aspectRatio: videoController.value.aspectRatio,
              placeholder: Container(
                color: Colors.black,
                child: const Center(
                  child: CircularProgressIndicator(color: Colors.white),
                ),
              ),
              errorBuilder: (context, errorMessage) {
                return Container(
                  color: Colors.black,
                  child: const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.error, color: Colors.white, size: 48),
                        SizedBox(height: 8),
                        Text(
                          'Video unavailable',
                          style: TextStyle(color: Colors.white),
                        ),
                      ],
                    ),
                  ),
                );
              },
            );

            _chewieControllers[index] = chewieController;

            // Auto-play current video
            if (index == _currentIndex) {
              videoController.play();
            }

            setState(() {});
          }
        })
        .catchError((error) {
          debugPrint('❌ Error initializing video controller: $error');
        });
  }

  void _disposeVideoControllers() {
    for (final controller in _chewieControllers.values) {
      controller.dispose();
    }
    for (final controller in _videoControllers.values) {
      controller.dispose();
    }
    _chewieControllers.clear();
    _videoControllers.clear();
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });

    // Pause all videos except current one
    for (int i = 0; i < _videoControllers.length; i++) {
      if (i != index && _videoControllers[i] != null) {
        _videoControllers[i]!.pause();
      }
    }

    // Play current video
    if (_videoControllers[index] != null) {
      _videoControllers[index]!.play();
    }

    // Initialize controllers for adjacent videos
    _initializeAdjacentControllers(index);
  }

  void _initializeAdjacentControllers(int currentIndex) {
    // Initialize previous video
    if (currentIndex > 0) {
      _initializeVideoController(currentIndex - 1);
    }

    // Initialize next video
    if (currentIndex < widget.videoPosts.length - 1) {
      _initializeVideoController(currentIndex + 1);
    }

    // Dispose far away controllers to save memory
    final controllersToDispose = <int>[];
    for (final index in _videoControllers.keys) {
      if ((index - currentIndex).abs() > 2) {
        controllersToDispose.add(index);
      }
    }

    for (final index in controllersToDispose) {
      _chewieControllers[index]?.dispose();
      _videoControllers[index]?.dispose();
      _chewieControllers.remove(index);
      _videoControllers.remove(index);
    }
  }

  void _togglePlayPause() {
    final controller = _videoControllers[_currentIndex];
    if (controller != null) {
      if (controller.value.isPlaying) {
        controller.pause();
      } else {
        controller.play();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Video PageView
          PageView.builder(
            controller: _pageController,
            scrollDirection: Axis.vertical,
            onPageChanged: _onPageChanged,
            itemCount: widget.videoPosts.length,
            itemBuilder: (context, index) {
              return _buildVideoPage(index);
            },
          ),

          // Top gradient overlay
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              height: 100,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black.withValues(alpha: 0.7),
                    Colors.transparent,
                  ],
                ),
              ),
            ),
          ),

          // Back button
          Positioned(
            top: MediaQuery.of(context).padding.top + 10,
            left: 16,
            child: IconButton(
              onPressed: () => Navigator.pop(context),
              icon: const Icon(Icons.arrow_back, color: Colors.white, size: 28),
            ),
          ),

          // Video counter
          Positioned(
            top: MediaQuery.of(context).padding.top + 10,
            right: 16,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                '${_currentIndex + 1}/${widget.videoPosts.length}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVideoPage(int index) {
    final post = widget.videoPosts[index];
    final chewieController = _chewieControllers[index];

    return GestureDetector(
      onTap: _togglePlayPause,
      child: Stack(
        fit: StackFit.expand,
        children: [
          // Video player
          if (chewieController != null)
            Center(child: Chewie(controller: chewieController))
          else
            Container(
              color: Colors.black,
              child: const Center(
                child: CircularProgressIndicator(color: Colors.white),
              ),
            ),

          // Right side actions
          Positioned(right: 16, bottom: 100, child: _buildRightActions(post)),

          // Bottom info
          Positioned(
            left: 16,
            right: 80,
            bottom: 100,
            child: _buildBottomInfo(post),
          ),
        ],
      ),
    );
  }

  Widget _buildRightActions(Post post) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Profile picture
        GestureDetector(
          onTap: () {
            // Navigate to user profile
          },
          child: Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white, width: 2),
            ),
            child: ClipOval(
              child: CachedNetworkImage(
                imageUrl: post.userAvatarUrl,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: Colors.grey,
                  child: const Icon(Icons.person, color: Colors.white),
                ),
                errorWidget: (context, url, error) => Container(
                  color: Colors.grey,
                  child: const Icon(Icons.person, color: Colors.white),
                ),
              ),
            ),
          ),
        ),

        const SizedBox(height: 20),

        // Like button
        _buildActionButton(
          icon: post.isLiked ? Icons.favorite : Icons.favorite_border,
          count: post.likeCount,
          color: post.isLiked ? Colors.red : Colors.white,
          onTap: () {
            // Handle like
          },
        ),

        const SizedBox(height: 20),

        // Comment button
        _buildActionButton(
          icon: Icons.comment,
          count: post.commentCount,
          color: Colors.white,
          onTap: () {
            // Handle comment
          },
        ),

        const SizedBox(height: 20),

        // Share button
        _buildActionButton(
          icon: Icons.share,
          count: post.shareCount ?? 0,
          color: Colors.white,
          onTap: () {
            // Handle share
          },
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required int count,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 4),
          Text(
            count > 0 ? _formatCount(count) : '',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomInfo(Post post) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Username
        Text(
          '@${post.username}',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),

        const SizedBox(height: 8),

        // Caption
        if (post.caption.isNotEmpty)
          Text(
            post.caption,
            style: const TextStyle(color: Colors.white, fontSize: 14),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),

        const SizedBox(height: 8),

        // Music/Audio info
        if (post.musicTrack != null)
          Row(
            children: [
              const Icon(Icons.music_note, color: Colors.white, size: 16),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  post.musicTrack!,
                  style: const TextStyle(color: Colors.white, fontSize: 12),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
      ],
    );
  }

  String _formatCount(int count) {
    if (count < 1000) return count.toString();
    if (count < 1000000) return '${(count / 1000).toStringAsFixed(1)}K';
    return '${(count / 1000000).toStringAsFixed(1)}M';
  }
}
