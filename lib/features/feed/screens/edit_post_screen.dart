import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/features/feed/services/feed_service.dart';
import 'package:billionaires_social/features/feed/providers/feed_provider.dart';
import 'package:billionaires_social/features/feed/providers/paginated_feed_provider.dart';
import 'package:billionaires_social/core/services/cache_service.dart';
import 'package:billionaires_social/features/profile/providers/profile_provider.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';

class EditPostScreen extends ConsumerStatefulWidget {
  final Post post;

  const EditPostScreen({super.key, required this.post});

  @override
  ConsumerState<EditPostScreen> createState() => _EditPostScreenState();
}

class _EditPostScreenState extends ConsumerState<EditPostScreen> {
  final TextEditingController _captionController = TextEditingController();
  final TextEditingController _locationController = TextEditingController();
  final FeedService _feedService = getIt<FeedService>();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _captionController.text = widget.post.caption;
    _locationController.text = widget.post.location ?? '';
  }

  @override
  void dispose() {
    _captionController.dispose();
    _locationController.dispose();
    super.dispose();
  }

  Future<void> _saveChanges() async {
    if (_captionController.text.trim().isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Caption cannot be empty')));
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      await _feedService.editPost(
        postId: widget.post.id,
        newCaption: _captionController.text.trim(),
        newLocation: _locationController.text.trim().isEmpty
            ? null
            : _locationController.text.trim(),
      );

      if (mounted) {
        debugPrint('✅ Post edit successful, refreshing providers...');
        debugPrint('   Updated caption: "${_captionController.text.trim()}"');
        debugPrint('   Updated location: "${_locationController.text.trim()}"');

        // Clear cache and refresh all relevant providers
        try {
          // Get cache service and clear all feed-related caches
          final cacheService = getIt<CacheService>();
          final feedService = getIt<FeedService>();
          final currentUserId = feedService.currentUserId;

          if (currentUserId != null) {
            debugPrint('🗑️ Clearing feed cache for user: $currentUserId');
            await cacheService.removeData('feed_posts_$currentUserId');
            debugPrint('✅ Feed cache cleared');
          }

          // Also clear any other related caches
          await cacheService.removeData('tagged_posts_${widget.post.userId}');
          debugPrint('✅ Tagged posts cache cleared');

          // Refresh main feed provider
          await ref.read(feedProvider.notifier).refresh();
          debugPrint('✅ Main feed provider refreshed');

          // Refresh paginated feed provider if available
          try {
            await ref.read(paginatedFeedProvider.notifier).refresh();
            debugPrint('✅ Paginated feed provider refreshed');
          } catch (e) {
            debugPrint('⚠️ Paginated feed provider refresh failed: $e');
          }

          // Refresh profile providers
          ref.invalidate(
            userPostsWithPinnedStatusStreamProvider(widget.post.userId),
          );
          debugPrint('✅ Profile providers invalidated');

          // Force a small delay to ensure cache clearing is complete
          await Future.delayed(const Duration(milliseconds: 200));
          debugPrint('✅ Cache clearing delay completed');

          // Create updated post object with new caption and location
          final updatedPost = widget.post.copyWith(
            caption: _captionController.text.trim(),
            location: _locationController.text.trim().isEmpty
                ? null
                : _locationController.text.trim(),
            lastEditedAt: DateTime.now(),
            editedBy: currentUserId,
          );

          debugPrint(
            '✅ Created updated post object with new caption: "${updatedPost.caption}"',
          );
        } catch (e) {
          debugPrint('❌ Error refreshing providers: $e');
        }

        debugPrint('✅ All providers refreshed, showing success message...');

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Post updated successfully')),
          );
        }

        debugPrint('✅ Navigating back to feed...');

        // Use a slight delay to ensure UI updates are complete
        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted && Navigator.of(context).canPop()) {
            Navigator.of(context).pop(true); // Return true to indicate success
            debugPrint('✅ Navigation completed successfully');
          } else {
            debugPrint(
              '❌ Navigation failed - widget not mounted or cannot pop',
            );
          }
        });
      }
    } catch (e) {
      debugPrint('❌ Post edit failed: $e');
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Failed to update post: $e')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        debugPrint('🔄 Edit post loading state reset');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Edit Post'),
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            )
          else
            TextButton(
              onPressed: _saveChanges,
              child: const Text(
                'Save',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Post media preview
            Container(
              width: double.infinity,
              height: 300,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: CachedNetworkImage(
                  imageUrl: widget.post.mediaUrl,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Container(
                    color: Colors.grey[200],
                    child: const Center(child: CircularProgressIndicator()),
                  ),
                  errorWidget: (context, url, error) => Container(
                    color: Colors.grey[200],
                    child: const Icon(Icons.error),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Caption field
            Text(
              'Caption',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _captionController,
              maxLines: 5,
              maxLength: 2000,
              decoration: InputDecoration(
                hintText: 'Write a caption...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Theme.of(context).primaryColor,
                    width: 2,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Location field
            Text(
              'Location (Optional)',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _locationController,
              decoration: InputDecoration(
                hintText: 'Add location...',
                prefixIcon: const Icon(Icons.location_on_outlined),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Theme.of(context).primaryColor,
                    width: 2,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Privacy settings
            Text(
              'Privacy',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Card(
              child: ListTile(
                leading: Icon(
                  widget.post.visibility == 'private'
                      ? Icons.lock
                      : widget.post.visibility == 'closeFriends'
                      ? Icons.people
                      : Icons.public,
                ),
                title: Text(
                  widget.post.visibility == 'private'
                      ? 'Private'
                      : widget.post.visibility == 'closeFriends'
                      ? 'Close Friends'
                      : 'Public',
                ),
                subtitle: Text(
                  widget.post.visibility == 'private'
                      ? 'Only you can see this post'
                      : widget.post.visibility == 'closeFriends'
                      ? 'Visible to close friends only'
                      : 'Visible to everyone',
                ),
                trailing: const Icon(Icons.chevron_right),
                onTap: () {
                  // TODO: Navigate to privacy settings
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Privacy settings coming soon'),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
