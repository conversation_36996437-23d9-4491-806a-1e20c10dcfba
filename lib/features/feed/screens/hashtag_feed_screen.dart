import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/features/feed/widgets/post_card.dart';
import 'package:billionaires_social/features/feed/providers/feed_provider.dart';
import 'package:billionaires_social/core/widgets/loading_states.dart';

class HashtagFeedScreen extends ConsumerWidget {
  final String hashtag;
  
  const HashtagFeedScreen({super.key, required this.hashtag});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: Text('#$hashtag'),
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
      ),
      body: FutureBuilder<List<Post>>(
        future: _fetchHashtagPosts(ref),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Center(
              child: LoadingStates.primaryLoading(
                message: 'Loading #$hashtag posts...',
              ),
            );
          }
          
          if (snapshot.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 48, color: Colors.grey),
                  const SizedBox(height: 16),
                  Text(
                    'Failed to load hashtag posts',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                  const SizedBox(height: 8),
                  TextButton(
                    onPressed: () {
                      // Trigger rebuild to retry
                      (context as Element).markNeedsBuild();
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }
          
          final posts = snapshot.data ?? [];
          
          if (posts.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.tag, size: 48, color: Colors.grey),
                  const SizedBox(height: 16),
                  Text(
                    'No posts found for #$hashtag',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Be the first to post with this hashtag!',
                    style: TextStyle(color: Colors.grey[500]),
                  ),
                ],
              ),
            );
          }
          
          return ListView.builder(
            itemCount: posts.length,
            itemBuilder: (context, index) {
              return PostCard(post: posts[index]);
            },
          );
        },
      ),
    );
  }
  
  Future<List<Post>> _fetchHashtagPosts(WidgetRef ref) async {
    try {
      // Get all posts from feed provider and filter by hashtag
      final feedState = ref.read(feedProvider);
      return feedState.when(
        data: (posts) {
          // Filter posts that contain the hashtag in caption
          return posts.where((post) {
            final caption = post.caption.toLowerCase();
            final searchHashtag = '#${hashtag.toLowerCase()}';
            return caption.contains(searchHashtag) ||
                   (post.hashtags?.any((tag) => tag.toLowerCase() == hashtag.toLowerCase()) ?? false);
          }).toList();
        },
        loading: () => <Post>[],
        error: (error, stack) => throw error,
      );
    } catch (e) {
      throw Exception('Failed to fetch hashtag posts: $e');
    }
  }
}
