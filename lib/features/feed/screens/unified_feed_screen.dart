import 'package:billionaires_social/features/feed/providers/feed_provider.dart';
import 'package:billionaires_social/features/feed/providers/feed_filter_provider.dart';
import 'package:billionaires_social/features/feed/widgets/post_card.dart';
import 'package:billionaires_social/features/feed/widgets/post_card_skeleton.dart';
import 'package:billionaires_social/features/feed/widgets/category_selector.dart';
import 'package:billionaires_social/features/stories/widgets/circular_story_test.dart';
import 'package:billionaires_social/features/stories/widgets/hybrid_story_carousel.dart';
import 'package:billionaires_social/features/stories/widgets/story_creation_test.dart';
import 'package:billionaires_social/features/stories/providers/story_provider.dart';
import 'package:billionaires_social/core/instagram_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'package:billionaires_social/features/messaging/screens/chat_list_screen.dart';
import 'package:billionaires_social/features/notifications/screens/notifications_screen.dart';
import 'package:billionaires_social/features/explore/screens/search_screen.dart';
import 'package:billionaires_social/features/profile/screens/followers_following_screen.dart';
import 'package:billionaires_social/features/creation/screens/create_hub_screen.dart';

class UnifiedFeedScreen extends ConsumerStatefulWidget {
  const UnifiedFeedScreen({super.key});

  @override
  ConsumerState<UnifiedFeedScreen> createState() => _UnifiedFeedScreenState();
}

class _UnifiedFeedScreenState extends ConsumerState<UnifiedFeedScreen> {
  final _scrollController = ScrollController();
  bool _useCircularLayout = false; // Toggle between layouts

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    // Pagination logic can be handled if needed
  }

  @override
  Widget build(BuildContext context) {
    final feedState = ref.watch(feedProvider);
    final filterTitle = ref.read(feedFilterProvider.notifier).getFilterTitle();

    return Scaffold(
      appBar: AppBar(
        title: Text(filterTitle, style: Theme.of(context).textTheme.titleLarge),
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
        actions: [
          IconButton(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (_) => const FollowersFollowingScreen(),
                ),
              );
            },
            icon: const FaIcon(FontAwesomeIcons.users),
          ),
          IconButton(
            onPressed: () {
              Navigator.of(
                context,
              ).push(MaterialPageRoute(builder: (_) => const SearchScreen()));
            },
            icon: const FaIcon(FontAwesomeIcons.magnifyingGlass),
          ),
          IconButton(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(builder: (_) => const NotificationsScreen()),
              );
            },
            icon: const FaIcon(FontAwesomeIcons.bell),
          ),
          IconButton(
            onPressed: () {
              Navigator.of(
                context,
              ).push(MaterialPageRoute(builder: (_) => const ChatListScreen()));
            },
            icon: const FaIcon(FontAwesomeIcons.paperPlane),
          ),
          IconButton(
            onPressed: () {
              showModalBottomSheet(
                context: context,
                backgroundColor: Colors.transparent,
                builder: (_) => const CreateHubScreen(),
              );
            },
            icon: const FaIcon(FontAwesomeIcons.plus),
          ),
        ],
        elevation: 0,
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          // Trigger feed refresh
          debugPrint('🔄 Pull-to-refresh triggered');
          await ref.read(feedProvider.notifier).refresh();
        },
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        color: Theme.of(context).colorScheme.secondary,
        child: CustomScrollView(
          controller: _scrollController,
          slivers: [
            // Stories section with Instagram-style border
            SliverToBoxAdapter(
              child: Container(
                decoration: const BoxDecoration(
                  color: InstagramTheme.white,
                  border: Border(
                    bottom: BorderSide(
                      color: InstagramTheme.borderGray,
                      width: 0.5,
                    ),
                  ),
                ),
                child: Column(
                  children: [
                    // Toggle button for layout
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      child: Row(
                        children: [
                          const Text(
                            'Stories',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          GestureDetector(
                            onTap: () {
                              debugPrint(
                                '🔄 Toggle tapped! Current: $_useCircularLayout',
                              );
                              setState(() {
                                _useCircularLayout = !_useCircularLayout;
                              });
                              debugPrint(
                                '🔄 Toggle changed to: $_useCircularLayout',
                              );

                              // Show feedback to user
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    'Switched to ${_useCircularLayout ? "Circular" : "Traditional"} layout',
                                  ),
                                  duration: const Duration(seconds: 1),
                                  backgroundColor: const Color(0xFFD4AF37),
                                ),
                              );
                            },
                            child: AnimatedContainer(
                              duration: const Duration(milliseconds: 300),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: _useCircularLayout
                                    ? const Color(0xFFD4AF37)
                                    : Colors.grey[300],
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: _useCircularLayout
                                      ? const Color(0xFFD4AF37)
                                      : Colors.grey[400]!,
                                  width: 2,
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    _useCircularLayout
                                        ? Icons.circle
                                        : Icons.view_list,
                                    size: 16,
                                    color: _useCircularLayout
                                        ? Colors.white
                                        : Colors.black,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    _useCircularLayout
                                        ? 'Circular'
                                        : 'Traditional',
                                    style: TextStyle(
                                      color: _useCircularLayout
                                          ? Colors.white
                                          : Colors.black,
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Hybrid story carousel
                    Consumer(
                      builder: (context, ref, child) {
                        final storyReelsAsync = ref.watch(storyReelsProvider);

                        return storyReelsAsync.when(
                          data: (storyReels) {
                            return HybridStoryCarousel(
                              storyReels: storyReels,
                              centerAvatarUrl: null, // Will be set from profile
                              centerUsername: 'You',
                              useCircularLayout: _useCircularLayout,
                            );
                          },
                          loading: () => const SizedBox(
                            height: 100,
                            child: Center(child: CircularProgressIndicator()),
                          ),
                          error: (error, stack) => SizedBox(
                            height: 100,
                            child: Center(
                              child: Text(
                                'Error loading stories: $error',
                                style: const TextStyle(color: Colors.red),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SliverToBoxAdapter(child: CategorySelector()),
            Consumer(
              builder: (context, ref, child) {
                final currentFilter = ref.watch(feedFilterProvider);

                return feedState.when(
                  data: (posts) {
                    if (posts.isEmpty) {
                      // Show different empty states based on filter
                      if (currentFilter == FeedFilterType.followed) {
                        return SliverFillRemaining(
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.people_outline,
                                  size: 64,
                                  color: Theme.of(context).colorScheme.onSurface
                                      .withValues(alpha: 0.6),
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'No Posts in Your Feed',
                                  style: Theme.of(context).textTheme.titleMedium
                                      ?.copyWith(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSurface
                                            .withValues(alpha: 0.8),
                                        fontWeight: FontWeight.w600,
                                      ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Follow some users to see their posts here',
                                  style: Theme.of(context).textTheme.bodyMedium
                                      ?.copyWith(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSurface
                                            .withValues(alpha: 0.6),
                                      ),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 24),
                                ElevatedButton.icon(
                                  onPressed: () {
                                    // Switch to all content filter for discovery
                                    ref
                                        .read(feedFilterProvider.notifier)
                                        .setFilter(FeedFilterType.all);
                                  },
                                  icon: const Icon(Icons.explore),
                                  label: const Text('Show All Content'),
                                ),
                                const SizedBox(height: 12),
                                TextButton(
                                  onPressed: () {
                                    // Switch to explore tab to find users to follow
                                    DefaultTabController.of(
                                      context,
                                    ).animateTo(1);
                                  },
                                  child: const Text('Find People to Follow'),
                                ),
                              ],
                            ),
                          ),
                        );
                      } else {
                        return SliverFillRemaining(
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.post_add_outlined,
                                  size: 64,
                                  color: Theme.of(context).colorScheme.onSurface
                                      .withValues(alpha: 0.6),
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'No posts yet',
                                  style: Theme.of(context).textTheme.titleMedium
                                      ?.copyWith(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSurface
                                            .withValues(alpha: 0.8),
                                        fontWeight: FontWeight.w600,
                                      ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Be the first to share something amazing!',
                                  style: Theme.of(context).textTheme.bodyMedium
                                      ?.copyWith(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSurface
                                            .withValues(alpha: 0.6),
                                      ),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 24),
                                ElevatedButton.icon(
                                  onPressed: () {
                                    showModalBottomSheet(
                                      context: context,
                                      backgroundColor: Colors.transparent,
                                      builder: (_) => const CreateHubScreen(),
                                    );
                                  },
                                  icon: const Icon(Icons.add),
                                  label: const Text('Create Post'),
                                ),
                                const SizedBox(height: 12),
                                TextButton(
                                  onPressed: () async {
                                    debugPrint(
                                      '🔄 Manual refresh button pressed',
                                    );
                                    await ref
                                        .read(feedProvider.notifier)
                                        .refresh();
                                  },
                                  child: const Text('Refresh'),
                                ),
                              ],
                            ),
                          ),
                        );
                      }
                    }
                    return SliverList(
                      delegate: SliverChildBuilderDelegate(
                        (context, index) => PostCard(post: posts[index]),
                        childCount: posts.length,
                      ),
                    );
                  },
                  error: (error, stackTrace) => SliverFillRemaining(
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.cloud_off_outlined,
                            size: 64,
                            color: Theme.of(context).colorScheme.error,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Connection Error',
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(
                                  color: Theme.of(context).colorScheme.error,
                                  fontWeight: FontWeight.w600,
                                ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Unable to load posts. Please check your connection.',
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(
                                  color: Theme.of(context).colorScheme.onSurface
                                      .withValues(alpha: 0.7),
                                ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 24),
                          ElevatedButton.icon(
                            onPressed: () async {
                              debugPrint('🔄 Retry button pressed');
                              await ref.read(feedProvider.notifier).refresh();
                            },
                            icon: const Icon(Icons.refresh),
                            label: const Text('Try Again'),
                          ),
                        ],
                      ),
                    ),
                  ),
                  loading: () => SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) => const PostCardSkeleton(),
                      childCount: 5,
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
      // Story testing buttons
      floatingActionButton: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          FloatingActionButton(
            heroTag: "story_creation",
            mini: true,
            backgroundColor: const Color(0xFFD4AF37),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const StoryCreationTest(),
                ),
              );
            },
            child: const Icon(Icons.add, color: Colors.black),
          ),
          const SizedBox(height: 8),
          const CircularStoryTestButton(),
        ],
      ),
    );
  }
}
