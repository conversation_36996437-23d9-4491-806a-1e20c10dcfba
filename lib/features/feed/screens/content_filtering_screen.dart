import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class ContentFilteringScreen extends StatefulWidget {
  const ContentFilteringScreen({super.key});

  @override
  State<ContentFilteringScreen> createState() => _ContentFilteringScreenState();
}

class _ContentFilteringScreenState extends State<ContentFilteringScreen> {
  // Content type filters
  bool _showPosts = true;
  bool _showStories = true;
  bool _showEvents = true;
  bool _showLiveStreams = true;
  bool _showMarketplace = true;
  bool _showInvestmentOpportunities = true;

  // Topic filters
  final Set<String> _selectedTopics = {
    'Technology',
    'Finance',
    'Real Estate',
    'Luxury Lifestyle',
  };

  // User filters
  bool _showVerifiedUsers = true;
  bool _showFollowedUsers = true;
  bool _showEliteMembers = true;
  bool _showNewUsers = false;

  // Content sensitivity
  bool _showSensitiveContent = false;
  bool _showPoliticalContent = false;
  bool _showControversialTopics = false;

  // Feed preferences
  String _feedSortBy = 'Latest';
  bool _autoRefresh = true;
  int _postsPerPage = 20;

  final List<String> _availableTopics = [
    'Technology',
    'Finance',
    'Real Estate',
    'Luxury Lifestyle',
    'Travel',
    'Art & Culture',
    'Sports',
    'Entertainment',
    'Business',
    'Investment',
    'Cryptocurrency',
    'Sustainability',
    'Health & Wellness',
    'Education',
  ];

  final List<String> _sortOptions = [
    'Latest',
    'Most Popular',
    'Trending',
    'Most Engaged',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        title: const Text(
          'Content Filters',
          style: TextStyle(color: Colors.white),
        ),
        actions: [
          TextButton(
            onPressed: _resetToDefaults,
            child: const Text(
              'Reset',
              style: TextStyle(color: Colors.blue, fontSize: 16),
            ),
          ),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(24),
        children: [
          _buildContentTypeSection(),
          const SizedBox(height: 32),
          _buildTopicSection(),
          const SizedBox(height: 32),
          _buildUserSection(),
          const SizedBox(height: 32),
          _buildSensitivitySection(),
          const SizedBox(height: 32),
          _buildFeedPreferencesSection(),
          const SizedBox(height: 40),
          _buildApplyButton(),
        ],
      ),
    );
  }

  Widget _buildContentTypeSection() {
    return _buildSection(
      title: 'Content Types',
      icon: FontAwesomeIcons.layerGroup,
      children: [
        _buildSwitchTile(
          title: 'Posts',
          subtitle: 'Show regular posts and updates',
          value: _showPosts,
          onChanged: (value) => setState(() => _showPosts = value),
        ),
        _buildSwitchTile(
          title: 'Stories',
          subtitle: 'Show 24-hour stories',
          value: _showStories,
          onChanged: (value) => setState(() => _showStories = value),
        ),
        _buildSwitchTile(
          title: 'Events',
          subtitle: 'Show upcoming events and gatherings',
          value: _showEvents,
          onChanged: (value) => setState(() => _showEvents = value),
        ),
        _buildSwitchTile(
          title: 'Live Streams',
          subtitle: 'Show live content',
          value: _showLiveStreams,
          onChanged: (value) => setState(() => _showLiveStreams = value),
        ),
        _buildSwitchTile(
          title: 'Marketplace',
          subtitle: 'Show luxury marketplace items',
          value: _showMarketplace,
          onChanged: (value) => setState(() => _showMarketplace = value),
        ),
        _buildSwitchTile(
          title: 'Investment Opportunities',
          subtitle: 'Show exclusive investment deals',
          value: _showInvestmentOpportunities,
          onChanged: (value) =>
              setState(() => _showInvestmentOpportunities = value),
        ),
      ],
    );
  }

  Widget _buildTopicSection() {
    return _buildSection(
      title: 'Topics & Categories',
      icon: FontAwesomeIcons.tags,
      children: [
        const Text(
          'Select topics you\'re interested in:',
          style: TextStyle(color: Colors.grey, fontSize: 14),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _availableTopics.map((topic) {
            final isSelected = _selectedTopics.contains(topic);
            return GestureDetector(
              onTap: () {
                setState(() {
                  if (isSelected) {
                    _selectedTopics.remove(topic);
                  } else {
                    _selectedTopics.add(topic);
                  }
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: isSelected ? Colors.blue : Colors.grey[800],
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: isSelected ? Colors.blue : Colors.white24,
                  ),
                ),
                child: Text(
                  topic,
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.grey[300],
                    fontSize: 14,
                    fontWeight: isSelected
                        ? FontWeight.bold
                        : FontWeight.normal,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildUserSection() {
    return _buildSection(
      title: 'User Filters',
      icon: FontAwesomeIcons.users,
      children: [
        _buildSwitchTile(
          title: 'Verified Users',
          subtitle: 'Show content from verified accounts',
          value: _showVerifiedUsers,
          onChanged: (value) => setState(() => _showVerifiedUsers = value),
        ),
        _buildSwitchTile(
          title: 'Followed Users',
          subtitle: 'Show content from people you follow',
          value: _showFollowedUsers,
          onChanged: (value) => setState(() => _showFollowedUsers = value),
        ),
        _buildSwitchTile(
          title: 'Elite Members',
          subtitle: 'Show content from elite members',
          value: _showEliteMembers,
          onChanged: (value) => setState(() => _showEliteMembers = value),
        ),
        _buildSwitchTile(
          title: 'New Users',
          subtitle: 'Show content from new members',
          value: _showNewUsers,
          onChanged: (value) => setState(() => _showNewUsers = value),
        ),
      ],
    );
  }

  Widget _buildSensitivitySection() {
    return _buildSection(
      title: 'Content Sensitivity',
      icon: FontAwesomeIcons.shield,
      children: [
        _buildSwitchTile(
          title: 'Sensitive Content',
          subtitle: 'Show potentially sensitive content',
          value: _showSensitiveContent,
          onChanged: (value) => setState(() => _showSensitiveContent = value),
        ),
        _buildSwitchTile(
          title: 'Political Content',
          subtitle: 'Show political discussions',
          value: _showPoliticalContent,
          onChanged: (value) => setState(() => _showPoliticalContent = value),
        ),
        _buildSwitchTile(
          title: 'Controversial Topics',
          subtitle: 'Show controversial discussions',
          value: _showControversialTopics,
          onChanged: (value) =>
              setState(() => _showControversialTopics = value),
        ),
      ],
    );
  }

  Widget _buildFeedPreferencesSection() {
    return _buildSection(
      title: 'Feed Preferences',
      icon: FontAwesomeIcons.gear,
      children: [
        _buildDropdownTile(
          title: 'Sort By',
          subtitle: 'Choose how to sort your feed',
          value: _feedSortBy,
          options: _sortOptions,
          onChanged: (value) => setState(() => _feedSortBy = value!),
        ),
        _buildSwitchTile(
          title: 'Auto Refresh',
          subtitle: 'Automatically refresh feed',
          value: _autoRefresh,
          onChanged: (value) => setState(() => _autoRefresh = value),
        ),
        _buildSliderTile(
          title: 'Posts Per Page',
          subtitle: 'Number of posts to load at once',
          value: _postsPerPage.toDouble(),
          min: 10,
          max: 50,
          divisions: 4,
          onChanged: (value) => setState(() => _postsPerPage = value.round()),
        ),
      ],
    );
  }

  Widget _buildSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: Colors.blue, size: 20),
            ),
            const SizedBox(width: 12),
            Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: Colors.grey[900],
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.white12),
          ),
          padding: const EdgeInsets.all(20),
          child: Column(children: children),
        ),
      ],
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(color: Colors.grey[400], fontSize: 14),
                ),
              ],
            ),
          ),
          Switch(value: value, onChanged: onChanged, activeColor: Colors.blue),
        ],
      ),
    );
  }

  Widget _buildDropdownTile({
    required String title,
    required String subtitle,
    required String value,
    required List<String> options,
    required ValueChanged<String?> onChanged,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: TextStyle(color: Colors.grey[400], fontSize: 14),
          ),
          const SizedBox(height: 8),
          Container(
            decoration: BoxDecoration(
              color: Colors.grey[800],
              borderRadius: BorderRadius.circular(12),
            ),
            child: DropdownButtonFormField<String>(
              value: value,
              onChanged: onChanged,
              dropdownColor: Colors.grey[800],
              style: const TextStyle(color: Colors.white),
              decoration: const InputDecoration(
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              items: options.map((String option) {
                return DropdownMenuItem<String>(
                  value: option,
                  child: Text(option),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSliderTile({
    required String title,
    required String subtitle,
    required double value,
    required double min,
    required double max,
    required int divisions,
    required ValueChanged<double> onChanged,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                value.round().toString(),
                style: const TextStyle(
                  color: Colors.blue,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: TextStyle(color: Colors.grey[400], fontSize: 14),
          ),
          const SizedBox(height: 8),
          Slider(
            value: value,
            min: min,
            max: max,
            divisions: divisions,
            onChanged: onChanged,
            activeColor: Colors.blue,
            inactiveColor: Colors.grey[700],
          ),
        ],
      ),
    );
  }

  Widget _buildApplyButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(30),
          ),
          textStyle: const TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
        ),
        onPressed: () {
          // Apply filters and navigate back
          Navigator.of(context).pop();
        },
        child: const Text('Apply Filters'),
      ),
    );
  }

  void _resetToDefaults() {
    setState(() {
      _showPosts = true;
      _showStories = true;
      _showEvents = true;
      _showLiveStreams = true;
      _showMarketplace = true;
      _showInvestmentOpportunities = true;
      _selectedTopics.clear();
      _selectedTopics.addAll([
        'Technology',
        'Finance',
        'Real Estate',
        'Luxury Lifestyle',
      ]);
      _showVerifiedUsers = true;
      _showFollowedUsers = true;
      _showEliteMembers = true;
      _showNewUsers = false;
      _showSensitiveContent = false;
      _showPoliticalContent = false;
      _showControversialTopics = false;
      _feedSortBy = 'Latest';
      _autoRefresh = true;
      _postsPerPage = 20;
    });
  }
}
