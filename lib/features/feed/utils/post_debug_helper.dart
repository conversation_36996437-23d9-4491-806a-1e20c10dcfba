import 'package:flutter/foundation.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';

/// Helper class to debug Post-related issues without printing entire objects
class PostDebugHelper {
  /// Create a safe debug summary of a Post object
  static String getPostSummary(Post post) {
    if (!kDebugMode) return '';
    
    try {
      return 'Post(id: ${post.id.substring(0, 8)}..., '
          'user: ${post.username}, '
          'type: ${post.mediaType}, '
          'likes: ${post.likeCount}, '
          'comments: ${post.commentCount})';
    } catch (e) {
      return 'Post(id: ${post.id}, error: $e)';
    }
  }

  /// Debug post permissions safely
  static void debugPostPermissions(Post post, Map<String, bool> permissions) {
    if (!kDebugMode) return;
    
    try {
      final postSummary = getPostSummary(post);
      debugPrint('🔐 Permissions for $postSummary:');
      permissions.forEach((key, value) {
        debugPrint('   $key: $value');
      });
    } catch (e) {
      debugPrint('❌ Error debugging post permissions: $e');
    }
  }

  /// Debug post state changes safely
  static void debugPostStateChange(Post post, String action, {String? details}) {
    if (!kDebugMode) return;
    
    try {
      final postSummary = getPostSummary(post);
      debugPrint('🔄 $action for $postSummary${details != null ? ': $details' : ''}');
    } catch (e) {
      debugPrint('❌ Error debugging post state change: $e');
    }
  }

  /// Debug post errors safely
  static void debugPostError(Post post, String operation, dynamic error) {
    if (!kDebugMode) return;
    
    try {
      final postSummary = getPostSummary(post);
      debugPrint('❌ $operation failed for $postSummary: ${error.toString()}');
    } catch (e) {
      debugPrint('❌ Critical error in post debug: $e');
    }
  }

  /// Check if a post object is valid
  static bool isValidPost(Post? post) {
    if (post == null) return false;
    
    try {
      return post.id.isNotEmpty && 
             post.userId.isNotEmpty && 
             post.username.isNotEmpty &&
             post.mediaUrl.isNotEmpty;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Post validation error: $e');
      }
      return false;
    }
  }

  /// Get safe post properties for error reporting
  static Map<String, dynamic> getSafePostProperties(Post post) {
    try {
      return {
        'id': post.id.length > 8 ? '${post.id.substring(0, 8)}...' : post.id,
        'userId': post.userId.length > 8 ? '${post.userId.substring(0, 8)}...' : post.userId,
        'username': post.username,
        'mediaType': post.mediaType.toString(),
        'likeCount': post.likeCount,
        'commentCount': post.commentCount,
        'isLiked': post.isLiked,
        'isBookmarked': post.isBookmarked,
        'isReposted': post.isReposted,
        'isArchived': post.isArchived,
        'isDeleted': post.isDeleted,
        'status': post.status,
      };
    } catch (e) {
      return {'error': 'Failed to extract post properties: $e'};
    }
  }

  /// Debug widget state issues
  static void debugWidgetState(String widgetName, String state, {Post? post}) {
    if (!kDebugMode) return;
    
    try {
      final postInfo = post != null ? ' for ${getPostSummary(post)}' : '';
      debugPrint('🔧 $widgetName: $state$postInfo');
    } catch (e) {
      debugPrint('❌ Widget state debug error: $e');
    }
  }

  /// Debug performance issues
  static void debugPerformance(String operation, Duration duration, {Post? post}) {
    if (!kDebugMode) return;
    
    try {
      final postInfo = post != null ? ' for ${getPostSummary(post)}' : '';
      final ms = duration.inMilliseconds;
      final emoji = ms > 1000 ? '🐌' : ms > 500 ? '⚠️' : '✅';
      debugPrint('$emoji $operation took ${ms}ms$postInfo');
    } catch (e) {
      debugPrint('❌ Performance debug error: $e');
    }
  }
}
