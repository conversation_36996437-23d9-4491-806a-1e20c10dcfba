import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';

enum FeedFilterType {
  all,
  billionaires,
  verified,
  celebrities,
  followed,
  closeFriends,
  allUsers,
}

enum ContentTypeFilter { all, media, text }

class FeedFilterNotifier extends StateNotifier<FeedFilterType> {
  FeedFilterNotifier()
    : super(FeedFilterType.all); // Start with 'all' for new users

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  void setFilter(FeedFilterType filterType) {
    state = filterType;
  }

  /// Universal smart filter initialization - works for ANY account
  Future<void> initializeSmartFilter() async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      // No user logged in - show all content
      state = FeedFilterType.all;
      debugPrint('🎯 Universal Smart Filter: No user → showing all content');
      return;
    }

    try {
      // Get user's feed preferences from universal account settings
      final userDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .get();

      if (userDoc.exists) {
        final userData = userDoc.data()!;
        final feedPreferences =
            userData['feedPreferences'] as Map<String, dynamic>?;

        if (feedPreferences != null) {
          final defaultFilter = feedPreferences['defaultFilter'] as String?;
          final autoSwitch =
              feedPreferences['autoSwitchToFollowed'] as bool? ?? true;

          // Use user's preferred default filter
          if (defaultFilter == 'followed' && autoSwitch) {
            // Check if user follows anyone before switching to followed
            final followingSnapshot = await _firestore
                .collection('users')
                .doc(currentUser.uid)
                .collection('following')
                .limit(1)
                .get();

            if (followingSnapshot.docs.isNotEmpty) {
              state = FeedFilterType.followed;
              debugPrint(
                '🎯 Universal Smart Filter: User follows people → showing followed content',
              );
            } else {
              state = FeedFilterType.all;
              debugPrint(
                '🎯 Universal Smart Filter: New user → showing all content for discovery',
              );
            }
          } else {
            // Use the default filter from preferences
            state = _parseFilterType(defaultFilter ?? 'all');
            debugPrint(
              '🎯 Universal Smart Filter: Using user preference → $state',
            );
          }
        } else {
          // No preferences set, use universal default
          state = FeedFilterType.all;
          debugPrint(
            '🎯 Universal Smart Filter: No preferences → using universal default (all)',
          );
        }
      } else {
        // User document doesn't exist, use safe default
        state = FeedFilterType.all;
        debugPrint(
          '🎯 Universal Smart Filter: No user document → using safe default (all)',
        );
      }
    } catch (e) {
      debugPrint('❌ Universal Smart Filter Error: $e');
      // Always default to all content on error for universal compatibility
      state = FeedFilterType.all;
    }
  }

  /// Parse string filter type to enum (universal helper)
  FeedFilterType _parseFilterType(String filterString) {
    switch (filterString.toLowerCase()) {
      case 'followed':
        return FeedFilterType.followed;
      case 'billionaires':
        return FeedFilterType.billionaires;
      case 'verified':
        return FeedFilterType.verified;
      case 'celebrities':
        return FeedFilterType.celebrities;
      case 'closefriends':
        return FeedFilterType.closeFriends;
      case 'allusers':
        return FeedFilterType.allUsers;
      default:
        return FeedFilterType.all;
    }
  }

  // Get filtered user IDs based on real Firestore data
  Future<List<String>> getFilteredUserIds() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return [];

      switch (state) {
        case FeedFilterType.all:
          // Get all users (with pagination for performance)
          final usersSnapshot = await _firestore
              .collection('users')
              .limit(100) // Limit for performance
              .get();
          return usersSnapshot.docs.map((doc) => doc.id).toList();

        case FeedFilterType.billionaires:
          // Get users with isBillionaire = true
          final billionairesSnapshot = await _firestore
              .collection('users')
              .where('isBillionaire', isEqualTo: true)
              .get();
          return billionairesSnapshot.docs.map((doc) => doc.id).toList();

        case FeedFilterType.verified:
          // Get users with isVerified = true
          final verifiedSnapshot = await _firestore
              .collection('users')
              .where('isVerified', isEqualTo: true)
              .get();
          return verifiedSnapshot.docs.map((doc) => doc.id).toList();

        case FeedFilterType.celebrities:
          // Get users with userType = celebrity or influencer
          final celebritiesSnapshot = await _firestore
              .collection('users')
              .where('userType', whereIn: ['celebrity', 'influencer'])
              .get();
          return celebritiesSnapshot.docs.map((doc) => doc.id).toList();

        case FeedFilterType.followed:
          // Get users that the current user follows
          final followingSnapshot = await _firestore
              .collection('users')
              .doc(currentUser.uid)
              .collection('following')
              .get();
          return followingSnapshot.docs.map((doc) => doc.id).toList();

        case FeedFilterType.closeFriends:
          // Get users that are marked as close friends
          final closeFriendsSnapshot = await _firestore
              .collection('users')
              .doc(currentUser.uid)
              .collection('closeFriends')
              .get();
          return closeFriendsSnapshot.docs.map((doc) => doc.id).toList();

        case FeedFilterType.allUsers:
          // Get all users (same as all for now)
          final allUsersSnapshot = await _firestore
              .collection('users')
              .limit(100)
              .get();
          return allUsersSnapshot.docs.map((doc) => doc.id).toList();
      }
    } catch (e) {
      debugPrint('❌ Error getting filtered user IDs: $e');
      return [];
    }
  }

  // Get filtered users with profile data (for UI display)
  Future<List<Map<String, dynamic>>> getFilteredUsersWithProfiles() async {
    try {
      final userIds = await getFilteredUserIds();
      if (userIds.isEmpty) return [];

      // Fetch user profiles in batches (Firestore limit is 10 for 'in' queries)
      final List<Map<String, dynamic>> allUsers = [];

      for (int i = 0; i < userIds.length; i += 10) {
        final batch = userIds.skip(i).take(10).toList();
        final usersSnapshot = await _firestore
            .collection('users')
            .where(FieldPath.documentId, whereIn: batch)
            .get();

        final batchUsers = usersSnapshot.docs.map((doc) {
          final data = doc.data();
          return {
            'id': doc.id,
            'name': data['name'] ?? '',
            'username': data['username'] ?? '',
            'profilePictureUrl': data['profilePictureUrl'] ?? '',
            'isVerified': data['isVerified'] ?? false,
            'isBillionaire': data['isBillionaire'] ?? false,
            'userType': data['userType'] ?? 'regular',
            'isFollowed': data['isFollowed'] ?? false,
          };
        }).toList();

        allUsers.addAll(batchUsers);
      }

      return allUsers;
    } catch (e) {
      debugPrint('❌ Error getting filtered users with profiles: $e');
      return [];
    }
  }

  String getFilterTitle() {
    switch (state) {
      case FeedFilterType.all:
        return 'Billionaires';
      case FeedFilterType.billionaires:
        return 'Billionaires';
      case FeedFilterType.verified:
        return 'Verified Accounts';
      case FeedFilterType.celebrities:
        return 'Celebrities';
      case FeedFilterType.followed:
        return 'People I Follow';
      case FeedFilterType.closeFriends:
        return 'Close Friends';
      case FeedFilterType.allUsers:
        return 'All Users';
    }
  }

  String getFilterDescription() {
    switch (state) {
      case FeedFilterType.all:
        return 'All posts from the community';
      case FeedFilterType.billionaires:
        return 'Posts from verified billionaires';
      case FeedFilterType.verified:
        return 'Posts from verified accounts';
      case FeedFilterType.celebrities:
        return 'Posts from celebrities and influencers';
      case FeedFilterType.followed:
        return 'Posts from people you follow';
      case FeedFilterType.closeFriends:
        return 'Posts from your close friends';
      case FeedFilterType.allUsers:
        return 'Posts from all users';
    }
  }

  String getFilterIcon() {
    switch (state) {
      case FeedFilterType.all:
        return '👑';
      case FeedFilterType.billionaires:
        return '💸';
      case FeedFilterType.verified:
        return '✅';
      case FeedFilterType.celebrities:
        return '🌟';
      case FeedFilterType.followed:
        return '👤';
      case FeedFilterType.closeFriends:
        return '💚';
      case FeedFilterType.allUsers:
        return '🌍';
    }
  }

  List<Map<String, dynamic>> getFilterOptions() {
    return [
      {
        'type': FeedFilterType.billionaires,
        'title': 'Billionaires',
        'icon': '💸',
        'description': 'Verified billionaires only',
      },
      {
        'type': FeedFilterType.verified,
        'title': 'Verified Accounts',
        'icon': '✅',
        'description': 'All verified users',
      },
      {
        'type': FeedFilterType.celebrities,
        'title': 'Celebrities',
        'icon': '🌟',
        'description': 'Celebrities and influencers',
      },
      {
        'type': FeedFilterType.followed,
        'title': 'People I Follow',
        'icon': '👤',
        'description': 'Users you follow',
      },
      {
        'type': FeedFilterType.closeFriends,
        'title': 'Close Friends',
        'icon': '💚',
        'description': 'Your close friends only',
      },
      {
        'type': FeedFilterType.allUsers,
        'title': 'All Users',
        'icon': '🌍',
        'description': 'Everyone in the community',
      },
    ];
  }
}

final feedFilterProvider =
    StateNotifierProvider<FeedFilterNotifier, FeedFilterType>((ref) {
      return FeedFilterNotifier();
    });

class ContentTypeFilterNotifier extends StateNotifier<ContentTypeFilter> {
  ContentTypeFilterNotifier() : super(ContentTypeFilter.all);

  void setContentType(ContentTypeFilter contentType) {
    state = contentType;
  }

  String getContentTypeTitle() {
    switch (state) {
      case ContentTypeFilter.all:
        return 'All Posts';
      case ContentTypeFilter.media:
        return 'Media';
      case ContentTypeFilter.text:
        return 'Text Posts';
    }
  }

  String getContentTypeDescription() {
    switch (state) {
      case ContentTypeFilter.all:
        return 'Show all types of posts';
      case ContentTypeFilter.media:
        return 'Show only media posts (photos & videos)';
      case ContentTypeFilter.text:
        return 'Show only text posts';
    }
  }

  List<Map<String, dynamic>> getContentTypeOptions() {
    return [
      {
        'type': ContentTypeFilter.all,
        'title': 'All Posts',
        'icon': Icons.dashboard_rounded,
        'iconEmoji': '🌟',
        'description': 'Images, videos, and text',
        'color': const Color(0xFF6366F1), // Indigo
      },
      {
        'type': ContentTypeFilter.media,
        'title': 'Media',
        'icon': Icons.perm_media_rounded,
        'iconEmoji': '📱',
        'description': 'Photos and videos',
        'color': const Color(0xFF10B981), // Emerald
      },
      {
        'type': ContentTypeFilter.text,
        'title': 'Text Posts',
        'icon': Icons.article_rounded,
        'iconEmoji': '✍️',
        'description': 'Text-only posts',
        'color': const Color(0xFF8B5CF6), // Violet
      },
    ];
  }
}

final contentTypeFilterProvider =
    StateNotifierProvider<ContentTypeFilterNotifier, ContentTypeFilter>(
      (ref) => ContentTypeFilterNotifier(),
    );
