import 'dart:async';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/features/feed/services/feed_service.dart';
import 'package:billionaires_social/features/feed/providers/feed_filter_provider.dart';
import 'package:billionaires_social/core/services/cache_service.dart';
import 'package:billionaires_social/core/services/trending_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter/foundation.dart';

part 'feed_provider.g.dart';

@Riverpod(keepAlive: true)
class Feed extends _$Feed {
  // Using keepAlive because the feed is a central part of the app
  // and we want to preserve its state when switching tabs.
  @override
  Future<List<Post>> build() async {
    // Initial fetch of posts with caching and filtering
    final feedService = getIt<FeedService>();
    final cacheService = getIt<CacheService>();

    // Get current user ID for cache key
    final currentUserId = feedService.currentUserId;
    if (currentUserId == null) {
      throw Exception('User not authenticated');
    }

    // Universal feed initialization - works for any account
    final feedFilter = ref.read(feedFilterProvider.notifier);

    // Apply universal smart filter logic
    await feedFilter.initializeSmartFilter();

    // Get current feed filter (universally initialized)
    final currentFilter = ref.watch(feedFilterProvider);
    final contentTypeFilter = ref.watch(contentTypeFilterProvider);

    // Get filtered user IDs based on current filter (universal logic)
    final filteredUserIds = await feedFilter.getFilteredUserIds();

    debugPrint(
      '🔍 Feed building with filter: $currentFilter, content type: $contentTypeFilter',
    );
    debugPrint('🔍 Filtered user IDs: ${filteredUserIds.length} users');

    // Check cache first (include both filters in cache key)
    final cacheKey =
        'feed_posts_${currentUserId}_${currentFilter.name}_${contentTypeFilter.name}';
    final cachedPosts = await cacheService.getData(cacheKey);
    if (cachedPosts != null) {
      try {
        final posts = (cachedPosts as List)
            .map((json) => Post.fromJson(json as Map<String, dynamic>))
            .toList();

        // Apply content type filtering to cached posts
        final filteredCachedPosts = _filterPostsByContentType(
          posts,
          contentTypeFilter,
        );

        // Return cached data immediately, then refresh in background
        _refreshFeedInBackground(currentUserId, feedService, cacheService);
        return filteredCachedPosts;
      } catch (e) {
        // If cached data is invalid, remove it and continue with fresh fetch
        await cacheService.removeData(cacheKey);
      }
    }

    // Fetch fresh data from Firestore with filtering
    final posts = await feedService.fetchFeedPosts(
      filteredUsers: filteredUserIds.isNotEmpty ? filteredUserIds : null,
    );

    // Apply content type filtering
    final filteredPosts = _filterPostsByContentType(posts, contentTypeFilter);

    debugPrint(
      '✅ Fetched ${posts.length} posts, filtered to ${filteredPosts.length} by content type',
    );

    // Cache the fresh data using the correct method
    final postsJson = filteredPosts.map((post) => post.toJson()).toList();
    await cacheService.cacheFeedPosts(currentUserId, postsJson);

    _setupPaginationListener();
    return filteredPosts;
  }

  void _setupPaginationListener() {
    // Pagination is now handled by the PaginatedFeedProvider
  }

  Future<void> fetchNextPage() async {
    if (state.isLoading || state.isReloading) return;

    final currentState = state.valueOrNull;
    if (currentState == null) return;

    // Set loading more state instead of full loading
    _isLoadingMore = true;
    state = AsyncValue.data(currentState); // Keep current data visible

    final feedService = getIt<FeedService>();
    final lastPostId = currentState.isNotEmpty ? currentState.last.id : null;

    try {
      debugPrint('📄 Fetching next page of posts...');
      final newPosts = await feedService.fetchFeedPosts(lastPostId: lastPostId);
      debugPrint('✅ Fetched ${newPosts.length} more posts');

      _isLoadingMore = false;
      _hasMore = newPosts.isNotEmpty;
      state = AsyncValue.data([...currentState, ...newPosts]);
    } catch (e) {
      debugPrint('❌ Error fetching next page: $e');
      _isLoadingMore = false;
      // Keep current data and show error
      state = AsyncValue.data(currentState);
      _lastError = e;
    }
  }

  bool _isLoadingMore = false;
  bool _hasMore = true;
  dynamic _lastError;

  bool get isLoadingMore => _isLoadingMore;
  bool get hasMore => _hasMore;
  dynamic get lastError => _lastError;

  Future<void> refresh() async {
    final feedService = getIt<FeedService>();
    final cacheService = getIt<CacheService>();

    final currentUserId = feedService.currentUserId;
    if (currentUserId != null) {
      // Clear cache and refetch
      await cacheService.removeData('feed_posts_$currentUserId');
    }

    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => build());
  }

  Future<void> fetchFilteredPosts(FeedFilterType filterType) async {
    state = const AsyncValue.loading();

    final feedService = getIt<FeedService>();
    final feedFilter = getIt<FeedFilterNotifier>();

    try {
      // Get filtered user IDs from real Firestore data
      final filteredUserIds = await feedFilter.getFilteredUserIds();

      debugPrint(
        '🔍 Fetching posts for ${filteredUserIds.length} filtered users',
      );

      if (filteredUserIds.isEmpty) {
        // If no users match the filter, return empty list
        state = const AsyncValue.data([]);
        return;
      }

      // Fetch posts for the filtered users
      final posts = await feedService.fetchFeedPosts(
        filteredUsers: filteredUserIds,
      );

      debugPrint('✅ Fetched ${posts.length} posts for filtered users');
      state = AsyncValue.data(posts);
    } catch (e, s) {
      debugPrint('❌ Error fetching filtered posts: $e');
      state = AsyncValue.error(e, s);
    }
  }

  // Add a new post to the beginning of the feed (for post creation)
  void addNewPost(Post newPost) {
    debugPrint('📝 Adding new post to feed: ${newPost.id}');
    debugPrint('   - Caption: ${newPost.caption}');
    debugPrint('   - Media URL: ${newPost.mediaUrl}');
    debugPrint('   - User: ${newPost.username}');

    final currentState = state.valueOrNull;
    if (currentState != null) {
      final updatedPosts = [newPost, ...currentState];
      debugPrint(
        '✅ Feed updated with ${updatedPosts.length} posts (was ${currentState.length})',
      );
      state = AsyncValue.data(updatedPosts);
    } else {
      debugPrint(
        '⚠️ Current feed state is null, creating new feed with single post',
      );
      state = AsyncValue.data([newPost]);
    }
  }

  // Optimistic updates for liking a post
  Future<void> toggleLike(String postId) async {
    final currentState = state.valueOrNull;
    if (currentState == null) return;

    // Safe post lookup to prevent race conditions
    final postIndex = currentState.indexWhere((p) => p.id == postId);
    if (postIndex == -1) {
      debugPrint(
        '⚠️ Post $postId not found in current state, skipping like toggle',
      );
      return;
    }

    final post = currentState[postIndex];
    final newIsLiked = !post.isLiked;

    // Optimistic update
    final updatedPosts = currentState.map((p) {
      if (p.id == postId) {
        return p.copyWith(
          isLiked: newIsLiked,
          likeCount: newIsLiked ? p.likeCount + 1 : p.likeCount - 1,
        );
      }
      return p;
    }).toList();

    state = AsyncValue.data(updatedPosts);

    // Backend persistence
    try {
      final feedService = getIt<FeedService>();
      await feedService.toggleLike(postId);
    } catch (e) {
      // Revert on error
      state = AsyncValue.data(currentState);
      rethrow;
    }
  }

  // Optimistic updates for bookmarking a post
  Future<void> toggleBookmark(String postId) async {
    final currentState = state.valueOrNull;
    if (currentState == null) return;

    // Safe post lookup to prevent race conditions
    final postIndex = currentState.indexWhere((p) => p.id == postId);
    if (postIndex == -1) {
      debugPrint(
        '⚠️ Post $postId not found in current state, skipping bookmark toggle',
      );
      return;
    }

    final post = currentState[postIndex];
    final newIsBookmarked = !post.isBookmarked;

    // Optimistic update
    final updatedPosts = currentState.map((p) {
      if (p.id == postId) {
        return p.copyWith(isBookmarked: newIsBookmarked);
      }
      return p;
    }).toList();

    state = AsyncValue.data(updatedPosts);

    // Backend persistence
    try {
      final feedService = getIt<FeedService>();
      await feedService.toggleBookmark(postId);
    } catch (e) {
      // Revert on error
      state = AsyncValue.data(currentState);
      rethrow;
    }
  }

  // Remove a post from the feed (for post deletion)
  void removePost(String postId) {
    final currentState = state.valueOrNull;
    if (currentState == null) return;

    final updatedPosts = currentState
        .where((post) => post.id != postId)
        .toList();
    state = AsyncValue.data(updatedPosts);
  }
}

Future<void> _refreshFeedInBackground(
  String currentUserId,
  FeedService feedService,
  CacheService cacheService,
) async {
  try {
    final posts = await feedService.fetchFeedPosts();
    final postsJson = posts.map((post) => post.toJson()).toList();
    await cacheService.cacheFeedPosts(currentUserId, postsJson);

    // Trigger trending detection in background
    _triggerTrendingDetectionInBackground();
  } catch (e) {
    debugPrint('❌ Background refresh failed: $e');
  }
}

Future<void> _triggerTrendingDetectionInBackground() async {
  try {
    final trendingService = getIt<TrendingService>();
    await trendingService.detectAndUpdateTrendingPosts();
  } catch (e) {
    debugPrint('❌ Background trending detection failed: $e');
  }
}

/// Fetch trending posts specifically
Future<List<Post>> fetchTrendingPosts({int limit = 10}) async {
  try {
    final trendingService = getIt<TrendingService>();
    return await trendingService.getTrendingPosts(limit: limit);
  } catch (e) {
    debugPrint('❌ Error fetching trending posts: $e');
    return [];
  }
}

/// Manually trigger trending detection (for testing/admin use)
Future<void> triggerTrendingDetection() async {
  try {
    final trendingService = getIt<TrendingService>();
    await trendingService.triggerTrendingDetection();
    debugPrint('✅ Trending detection completed');
  } catch (e) {
    debugPrint('❌ Error triggering trending detection: $e');
    rethrow;
  }
}

/// Filter posts by content type
List<Post> _filterPostsByContentType(
  List<Post> posts,
  ContentTypeFilter contentTypeFilter,
) {
  if (contentTypeFilter == ContentTypeFilter.all) {
    return posts;
  }

  return posts.where((post) {
    switch (contentTypeFilter) {
      case ContentTypeFilter.media:
        return post.mediaType == MediaType.image ||
            post.mediaType == MediaType.video;
      case ContentTypeFilter.all:
        return true;
    }
  }).toList();
}

// --- Real-time feed stream provider ---
final feedStreamProvider = StreamProvider<List<Post>>((ref) {
  debugPrint('🔄 FeedStreamProvider: Initializing feed stream');
  final feedService = getIt<FeedService>();
  debugPrint(
    '🔄 FeedStreamProvider: Got feed service, calling getFeedPostsStream()',
  );
  return feedService.getFeedPostsStream();
});
