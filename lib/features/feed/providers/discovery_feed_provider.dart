import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/features/profile/models/profile_model.dart';
import 'package:billionaires_social/features/feed/services/content_discovery_service.dart';
import 'package:billionaires_social/features/feed/services/content_access_service.dart';
import 'package:billionaires_social/features/feed/providers/feed_provider.dart';
import 'package:get_it/get_it.dart';

part 'discovery_feed_provider.g.dart';

/// Provider for suggested accounts for new users
@Riverpod(keepAlive: true)
class SuggestedAccounts extends _$SuggestedAccounts {
  @override
  Future<List<ProfileModel>> build() async {
    final contentDiscoveryService = GetIt.I<ContentDiscoveryService>();
    final currentUser = FirebaseAuth.instance.currentUser;

    if (currentUser == null) return [];

    try {
      final suggestions = await contentDiscoveryService.getSuggestedAccounts(
        limit: 20,
        excludeUserId: currentUser.uid,
      );

      debugPrint(
        '🔍 SuggestedAccounts: Found ${suggestions.length} suggestions',
      );
      return suggestions;
    } catch (e) {
      debugPrint('❌ Error loading suggested accounts: $e');
      return [];
    }
  }

  /// Refresh suggested accounts
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => build());
  }

  /// Remove a suggestion (when user dismisses or follows)
  void removeSuggestion(String userId) {
    final currentState = state.valueOrNull;
    if (currentState == null) return;

    final updatedSuggestions = currentState
        .where((profile) => profile.id != userId)
        .toList();

    state = AsyncValue.data(updatedSuggestions);
  }
}

/// Provider for discovery content (posts from non-followed users)
@Riverpod(keepAlive: true)
class DiscoveryContent extends _$DiscoveryContent {
  @override
  Future<List<Post>> build() async {
    final contentDiscoveryService = GetIt.I<ContentDiscoveryService>();
    final contentAccessService = GetIt.I<ContentAccessService>();
    final currentUser = FirebaseAuth.instance.currentUser;

    if (currentUser == null) return [];

    try {
      // Check if user needs discovery content
      final needsDiscovery = await contentDiscoveryService
          .needsDiscoveryContent(currentUser.uid);
      if (!needsDiscovery) {
        debugPrint(
          '🔍 DiscoveryContent: User has followers, skipping discovery',
        );
        return [];
      }

      // Check user preferences
      final shouldShow = await contentAccessService.shouldShowDiscoveryContent(
        currentUser.uid,
      );
      if (!shouldShow) {
        debugPrint('🔍 DiscoveryContent: User disabled discovery content');
        return [];
      }

      // Get suggested accounts for content preference
      final suggestedAccounts =
          ref.read(suggestedAccountsProvider).valueOrNull ?? [];
      final preferredUserIds = suggestedAccounts.map((p) => p.id).toList();

      // Get discovery content
      final discoveryPosts = await contentDiscoveryService.getDiscoveryContent(
        limit: 15,
        preferredUserIds: preferredUserIds,
      );

      // Filter posts based on user permissions and preferences
      final filteredPosts = await contentAccessService.filterPostsByPermissions(
        discoveryPosts,
        currentUser.uid,
      );

      debugPrint(
        '🔍 DiscoveryContent: Found ${filteredPosts.length} discovery posts',
      );
      return filteredPosts;
    } catch (e) {
      debugPrint('❌ Error loading discovery content: $e');
      return [];
    }
  }

  /// Refresh discovery content
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => build());
  }

  /// Load more discovery content
  Future<void> loadMore() async {
    final currentState = state.valueOrNull;
    if (currentState == null) return;

    try {
      final contentDiscoveryService = GetIt.I<ContentDiscoveryService>();
      final contentAccessService = GetIt.I<ContentAccessService>();
      final currentUser = FirebaseAuth.instance.currentUser;

      if (currentUser == null) return;

      final lastPostId = currentState.isNotEmpty ? currentState.last.id : null;

      // Get suggested accounts for content preference
      final suggestedAccounts =
          ref.read(suggestedAccountsProvider).valueOrNull ?? [];
      final preferredUserIds = suggestedAccounts.map((p) => p.id).toList();

      final newPosts = await contentDiscoveryService.getDiscoveryContent(
        limit: 10,
        lastPostId: lastPostId,
        preferredUserIds: preferredUserIds,
      );

      final filteredNewPosts = await contentAccessService
          .filterPostsByPermissions(newPosts, currentUser.uid);

      if (filteredNewPosts.isNotEmpty) {
        state = AsyncValue.data([...currentState, ...filteredNewPosts]);
      }
    } catch (e) {
      debugPrint('❌ Error loading more discovery content: $e');
    }
  }

  /// Remove a post from discovery content
  void removePost(String postId) {
    final currentState = state.valueOrNull;
    if (currentState == null) return;

    final updatedPosts = currentState
        .where((post) => post.id != postId)
        .toList();

    state = AsyncValue.data(updatedPosts);
  }
}

/// Provider for content preferences
@Riverpod(keepAlive: true)
class ContentPreferences extends _$ContentPreferences {
  @override
  Future<Map<String, dynamic>> build() async {
    final contentAccessService = GetIt.I<ContentAccessService>();
    final currentUser = FirebaseAuth.instance.currentUser;

    if (currentUser == null) return {};

    try {
      final preferences = await contentAccessService.getContentPreferences(
        currentUser.uid,
      );
      debugPrint('🔍 ContentPreferences: Loaded preferences');
      return preferences;
    } catch (e) {
      debugPrint('❌ Error loading content preferences: $e');
      return {};
    }
  }

  /// Update content preferences
  Future<void> updatePreferences(Map<String, dynamic> newPreferences) async {
    final contentAccessService = GetIt.I<ContentAccessService>();
    final currentUser = FirebaseAuth.instance.currentUser;

    if (currentUser == null) return;

    try {
      await contentAccessService.updateContentPreferences(
        currentUser.uid,
        newPreferences,
      );
      state = AsyncValue.data(newPreferences);

      // Refresh discovery content when preferences change
      ref.invalidate(discoveryContentProvider);

      debugPrint('🔍 ContentPreferences: Updated preferences');
    } catch (e) {
      debugPrint('❌ Error updating content preferences: $e');
      rethrow;
    }
  }

  /// Get specific preference value
  T? getPreference<T>(String key, [T? defaultValue]) {
    final preferences = state.valueOrNull;
    if (preferences == null) return defaultValue;

    return preferences[key] as T? ?? defaultValue;
  }

  /// Check if discovery content should be shown
  bool shouldShowDiscoveryContent() {
    return getPreference<bool>('showSuggestedContent', true) ?? true;
  }

  /// Get discovery content percentage
  double getDiscoveryContentPercentage() {
    final percentage =
        getPreference<double>('discoveryContentPercentage', 30.0) ?? 30.0;
    return percentage.clamp(0.0, 50.0);
  }
}

/// Provider for hybrid feed (following + discovery content)
@Riverpod(keepAlive: true)
class HybridFeed extends _$HybridFeed {
  @override
  Future<List<Post>> build() async {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) return [];

    try {
      // Get user's content preferences
      final preferences = await ref.read(contentPreferencesProvider.future);
      final discoveryPercentage =
          (preferences['discoveryContentPercentage'] as double? ?? 30.0).clamp(
            0.0,
            50.0,
          );

      // Get following feed posts
      final followingPosts = await ref.read(feedProvider.future);

      // Get discovery content if enabled
      List<Post> discoveryPosts = [];
      if (preferences['showSuggestedContent'] as bool? ?? true) {
        discoveryPosts = await ref.read(discoveryContentProvider.future);
      }

      // Mix content based on discovery percentage
      final hybridPosts = _mixContent(
        followingPosts,
        discoveryPosts,
        discoveryPercentage / 100.0,
      );

      debugPrint(
        '🔍 HybridFeed: Mixed ${followingPosts.length} following + ${discoveryPosts.length} discovery = ${hybridPosts.length} total',
      );
      return hybridPosts;
    } catch (e) {
      debugPrint('❌ Error building hybrid feed: $e');
      return [];
    }
  }

  /// Mix following and discovery content based on percentage
  List<Post> _mixContent(
    List<Post> followingPosts,
    List<Post> discoveryPosts,
    double discoveryRatio,
  ) {
    if (discoveryPosts.isEmpty) return followingPosts;
    if (followingPosts.isEmpty) return discoveryPosts;

    final mixedPosts = <Post>[];
    int followingIndex = 0;
    int discoveryIndex = 0;

    // Calculate how often to insert discovery content
    final discoveryInterval = discoveryRatio > 0
        ? (1.0 / discoveryRatio).round()
        : 10;

    while (followingIndex < followingPosts.length ||
        discoveryIndex < discoveryPosts.length) {
      // Add following posts
      for (
        int i = 0;
        i < discoveryInterval && followingIndex < followingPosts.length;
        i++
      ) {
        mixedPosts.add(followingPosts[followingIndex++]);
      }

      // Add one discovery post
      if (discoveryIndex < discoveryPosts.length) {
        mixedPosts.add(discoveryPosts[discoveryIndex++]);
      }
    }

    return mixedPosts;
  }

  /// Refresh hybrid feed
  Future<void> refresh() async {
    // Refresh both following and discovery content
    ref.invalidate(feedProvider);
    ref.invalidate(discoveryContentProvider);

    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => build());
  }
}
