// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'feed_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$feedHash() => r'444d6664fc8e06bf903ad1ce60e164ebc46703f5';

/// See also [Feed].
@ProviderFor(Feed)
final feedProvider = AsyncNotifierProvider<Feed, List<Post>>.internal(
  Feed.new,
  name: r'feedProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$feedHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Feed = AsyncNotifier<List<Post>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
