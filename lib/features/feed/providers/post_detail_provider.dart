import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/features/feed/models/comment_model.dart';
import 'package:billionaires_social/features/feed/services/feed_service.dart';
import 'package:billionaires_social/core/service_locator.dart';

part 'post_detail_provider.g.dart';

@riverpod
Future<Post> postDetail(Ref ref, String postId) async {
  final feedService = getIt<FeedService>();
  final posts = await feedService.fetchFeedPosts();
  try {
    return posts.firstWhere((p) => p.id == postId);
  } catch (e) {
    throw Exception('Post with ID $postId not found');
  }
}

@riverpod
Stream<List<Comment>> comments(Ref ref, String postId) {
  final feedService = getIt<FeedService>();
  return feedService.getComments(postId);
}
