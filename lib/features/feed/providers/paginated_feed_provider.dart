import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

import '../models/post_model.dart';

class PaginatedFeedNotifier extends StateNotifier<AsyncValue<List<Post>>> {
  static const int _pageSize = 10;
  DocumentSnapshot? _lastDocument;
  bool _hasMore = true;
  bool _isLoadingMore = false;

  PaginatedFeedNotifier() : super(const AsyncValue.loading()) {
    _initializeData();
  }

  Future<void> _initializeData() async {
    state = await AsyncValue.guard(() => _loadInitialPosts());
  }

  Future<List<Post>> _loadInitialPosts() async {
    final result = await _fetchPosts(limit: _pageSize);

    if (result.posts.isNotEmpty) {
      _lastDocument = result.lastDocument;
      _hasMore = result.posts.length == _pageSize;
      return result.posts;
    } else {
      _hasMore = false;
      return [];
    }
  }

  Future<void> loadMore() async {
    if (_isLoadingMore || !_hasMore) return;

    _isLoadingMore = true;

    try {
      final currentPosts = state.valueOrNull ?? [];
      final result = await _fetchPosts(
        limit: _pageSize,
        startAfter: _lastDocument,
      );

      if (result.posts.isNotEmpty) {
        _lastDocument = result.lastDocument;
        _hasMore = result.posts.length == _pageSize;

        // Update state with new posts appended
        state = AsyncValue.data([...currentPosts, ...result.posts]);
      } else {
        _hasMore = false;
      }
    } catch (e) {
      debugPrint('Error loading more posts: $e');
      // Don't update state on error, keep existing posts
    } finally {
      _isLoadingMore = false;
    }
  }

  Future<PaginationResult> _fetchPosts({
    required int limit,
    DocumentSnapshot? startAfter,
  }) async {
    final firestore = FirebaseFirestore.instance;

    Query query = firestore
        .collection('posts')
        .where('status', isEqualTo: 'active')
        .orderBy('createdAt', descending: true)
        .limit(limit);

    if (startAfter != null) {
      query = query.startAfterDocument(startAfter);
    }

    final snapshot = await query.get();
    final posts = <Post>[];
    DocumentSnapshot? lastDoc;

    for (final doc in snapshot.docs) {
      try {
        final data = doc.data() as Map<String, dynamic>;
        final post = Post.fromJson({...data, 'id': doc.id});
        posts.add(post);
        lastDoc = doc;
      } catch (e) {
        debugPrint('Error parsing post ${doc.id}: $e');
        // Skip invalid posts
      }
    }

    return PaginationResult(posts: posts, lastDocument: lastDoc);
  }

  Future<void> refresh() async {
    _lastDocument = null;
    _hasMore = true;
    _isLoadingMore = false;

    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => _loadInitialPosts());
  }

  void addPost(Post post) {
    final currentPosts = state.valueOrNull ?? [];
    state = AsyncValue.data([post, ...currentPosts]);
  }

  void removePost(String postId) {
    final currentPosts = state.valueOrNull ?? [];
    final updatedPosts = currentPosts
        .where((post) => post.id != postId)
        .toList();
    state = AsyncValue.data(updatedPosts);
  }

  void updatePost(Post updatedPost) {
    final currentPosts = state.valueOrNull ?? [];
    final updatedPosts = currentPosts.map((post) {
      return post.id == updatedPost.id ? updatedPost : post;
    }).toList();
    state = AsyncValue.data(updatedPosts);
  }

  // Getters for UI
  bool get hasMore => _hasMore;
  bool get isLoadingMore => _isLoadingMore;
  int get currentCount => state.valueOrNull?.length ?? 0;
}

class PaginationResult {
  final List<Post> posts;
  final DocumentSnapshot? lastDocument;

  PaginationResult({required this.posts, this.lastDocument});
}

// Simple providers without code generation
final paginatedFeedProvider =
    StateNotifierProvider<PaginatedFeedNotifier, AsyncValue<List<Post>>>((ref) {
      return PaginatedFeedNotifier();
    });

final feedScrollControllerProvider = StateProvider<bool>((ref) => false);

final feedPerformanceMetricsProvider = StateProvider<FeedMetrics>((ref) {
  return FeedMetrics(
    loadTime: 0,
    postsLoaded: 0,
    cacheHitRate: 0.0,
    averageRenderTime: 0,
  );
});

class FeedMetrics {
  final int loadTime;
  final int postsLoaded;
  final double cacheHitRate;
  final int averageRenderTime;

  FeedMetrics({
    required this.loadTime,
    required this.postsLoaded,
    required this.cacheHitRate,
    required this.averageRenderTime,
  });

  FeedMetrics copyWith({
    int? loadTime,
    int? postsLoaded,
    double? cacheHitRate,
    int? averageRenderTime,
  }) {
    return FeedMetrics(
      loadTime: loadTime ?? this.loadTime,
      postsLoaded: postsLoaded ?? this.postsLoaded,
      cacheHitRate: cacheHitRate ?? this.cacheHitRate,
      averageRenderTime: averageRenderTime ?? this.averageRenderTime,
    );
  }

  @override
  String toString() {
    return 'FeedMetrics(loadTime: ${loadTime}ms, posts: $postsLoaded, '
        'cacheHit: ${(cacheHitRate * 100).toStringAsFixed(1)}%, '
        'renderTime: ${averageRenderTime}ms)';
  }
}
