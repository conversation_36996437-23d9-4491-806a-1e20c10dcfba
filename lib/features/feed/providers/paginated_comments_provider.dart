import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:billionaires_social/features/feed/models/comment_model.dart';
import 'package:billionaires_social/features/feed/services/comment_service.dart';

// Provider for paginated comments
final paginatedCommentsProvider =
    StateNotifierProvider.family<
      PaginatedCommentsNotifier,
      PaginatedCommentsState,
      String
    >((ref, postId) {
      return PaginatedCommentsNotifier(
        postId: postId,
        commentService: ref.read(commentServiceProvider),
      );
    });

class PaginatedCommentsState {
  final List<Comment> comments;
  final bool isLoading;
  final bool hasMore;
  final String? error;
  final DocumentSnapshot? lastDocument;
  final int totalCount;

  const PaginatedCommentsState({
    this.comments = const [],
    this.isLoading = false,
    this.hasMore = true,
    this.error,
    this.lastDocument,
    this.totalCount = 0,
  });

  PaginatedCommentsState copyWith({
    List<Comment>? comments,
    bool? isLoading,
    bool? hasMore,
    String? error,
    DocumentSnapshot? lastDocument,
    int? totalCount,
  }) {
    return PaginatedCommentsState(
      comments: comments ?? this.comments,
      isLoading: isLoading ?? this.isLoading,
      hasMore: hasMore ?? this.hasMore,
      error: error ?? this.error,
      lastDocument: lastDocument ?? this.lastDocument,
      totalCount: totalCount ?? this.totalCount,
    );
  }
}

class PaginatedCommentsNotifier extends StateNotifier<PaginatedCommentsState> {
  final String postId;
  final CommentService commentService;
  static const int _pageSize = 20;

  PaginatedCommentsNotifier({
    required this.postId,
    required this.commentService,
  }) : super(const PaginatedCommentsState()) {
    loadInitialComments();
  }

  /// Load initial comments
  Future<void> loadInitialComments() async {
    if (state.isLoading) return;

    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await commentService.getCommentsWithPagination(
        postId: postId,
        limit: _pageSize,
      );

      state = state.copyWith(
        comments: result.comments,
        isLoading: false,
        hasMore: result.hasMore,
        lastDocument: result.lastDocument,
        totalCount: result.totalCount,
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// Load more comments (pagination)
  Future<void> loadMoreComments() async {
    if (state.isLoading || !state.hasMore) return;

    state = state.copyWith(isLoading: true);

    try {
      final result = await commentService.getCommentsWithPagination(
        postId: postId,
        limit: _pageSize,
        startAfter: state.lastDocument,
      );

      final updatedComments = [...state.comments, ...result.comments];

      state = state.copyWith(
        comments: updatedComments,
        isLoading: false,
        hasMore: result.hasMore,
        lastDocument: result.lastDocument,
        totalCount: result.totalCount,
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// Refresh comments
  Future<void> refreshComments() async {
    state = const PaginatedCommentsState();
    await loadInitialComments();
  }

  /// Add a new comment to the beginning of the list
  void addComment(Comment comment) {
    final updatedComments = [comment, ...state.comments];
    state = state.copyWith(
      comments: updatedComments,
      totalCount: state.totalCount + 1,
    );
  }

  /// Update an existing comment
  void updateComment(Comment updatedComment) {
    final updatedComments = state.comments.map((comment) {
      return comment.id == updatedComment.id ? updatedComment : comment;
    }).toList();

    state = state.copyWith(comments: updatedComments);
  }

  /// Remove a comment
  void removeComment(String commentId) {
    final updatedComments = state.comments
        .where((comment) => comment.id != commentId)
        .toList();

    state = state.copyWith(
      comments: updatedComments,
      totalCount: state.totalCount - 1,
    );
  }

  /// Update comment reactions
  void updateCommentReactions({
    required String commentId,
    required Map<String, String> reactions,
    required Map<String, int> reactionCounts,
    required int totalReactionCount,
  }) {
    final updatedComments = state.comments.map((comment) {
      if (comment.id == commentId) {
        return comment.copyWith(
          reactions: reactions,
          reactionCounts: reactionCounts,
          reactionCount: totalReactionCount,
        );
      }
      return comment;
    }).toList();

    state = state.copyWith(comments: updatedComments);
  }

  /// Search comments within the current list
  List<Comment> searchComments(String query) {
    if (query.trim().isEmpty) return state.comments;

    final searchQuery = query.toLowerCase();
    return state.comments.where((comment) {
      return comment.text.toLowerCase().contains(searchQuery) ||
          comment.username.toLowerCase().contains(searchQuery);
    }).toList();
  }

  /// Filter comments by type
  List<Comment> filterCommentsByType(CommentType? type) {
    if (type == null) return state.comments;
    return state.comments.where((comment) => comment.type == type).toList();
  }

  /// Get comments count by type
  Map<CommentType, int> getCommentCountsByType() {
    final counts = <CommentType, int>{
      CommentType.text: 0,
      CommentType.voice: 0,
    };

    for (final comment in state.comments) {
      counts[comment.type] = (counts[comment.type] ?? 0) + 1;
    }

    return counts;
  }
}
