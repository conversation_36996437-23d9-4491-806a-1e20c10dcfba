import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:billionaires_social/features/feed/models/reaction_model.dart';

class ReactionButton extends StatefulWidget {
  final ReactionType type;
  final bool isSelected;
  final int count;
  final VoidCallback? onPressed;
  final bool showCount;
  final double size;

  const ReactionButton({
    super.key,
    required this.type,
    this.isSelected = false,
    this.count = 0,
    this.onPressed,
    this.showCount = true,
    this.size = 28,
  });

  @override
  State<ReactionButton> createState() => _ReactionButtonState();
}

class _ReactionButtonState extends State<ReactionButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.3).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.elasticOut),
    );

    _rotationAnimation = Tween<double>(begin: 0.0, end: 0.1).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTap() {
    HapticFeedback.lightImpact();
    _animationController.forward().then((_) {
      _animationController.reverse();
    });
    widget.onPressed?.call();
  }

  IconData _getIcon() {
    switch (widget.type) {
      case ReactionType.like:
        return Icons.favorite;
      case ReactionType.love:
        return Icons.favorite;
      case ReactionType.haha:
        return Icons.sentiment_satisfied;
      case ReactionType.wow:
        return Icons.sentiment_neutral;
      case ReactionType.sad:
        return Icons.sentiment_dissatisfied;
      case ReactionType.angry:
        return Icons.sentiment_very_dissatisfied;
    }
  }

  Color _getColor() {
    if (!widget.isSelected) {
      return Colors.grey;
    }

    switch (widget.type) {
      case ReactionType.like:
      case ReactionType.love:
        return Colors.red;
      case ReactionType.haha:
        return Colors.yellow.shade700;
      case ReactionType.wow:
        return Colors.orange;
      case ReactionType.sad:
        return Colors.blue;
      case ReactionType.angry:
        return Colors.red.shade800;
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _handleTap,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Transform.rotate(
              angle: _rotationAnimation.value,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(_getIcon(), color: _getColor(), size: widget.size),
                  if (widget.showCount && widget.count > 0) ...[
                    const SizedBox(width: 4),
                    Text(
                      _formatCount(widget.count),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: widget.isSelected ? _getColor() : Colors.grey,
                        fontWeight: widget.isSelected
                            ? FontWeight.bold
                            : FontWeight.normal,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  String _formatCount(int count) {
    if (count < 1000) {
      return count.toString();
    } else if (count < 1000000) {
      return '${(count / 1000).toStringAsFixed(1)}K';
    } else {
      return '${(count / 1000000).toStringAsFixed(1)}M';
    }
  }
}

class ReactionPicker extends StatefulWidget {
  final ReactionType? currentReaction;
  final Function(ReactionType) onReactionSelected;
  final VoidCallback? onReactionRemoved;

  const ReactionPicker({
    super.key,
    this.currentReaction,
    required this.onReactionSelected,
    this.onReactionRemoved,
  });

  @override
  State<ReactionPicker> createState() => _ReactionPickerState();
}

class _ReactionPickerState extends State<ReactionPicker>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.elasticOut),
    );

    _opacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _opacityAnimation.value,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                borderRadius: BorderRadius.circular(25),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: ReactionType.values.map((reaction) {
                  final isSelected = widget.currentReaction == reaction;
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4),
                    child: GestureDetector(
                      onTap: () {
                        HapticFeedback.lightImpact();
                        if (isSelected) {
                          widget.onReactionRemoved?.call();
                        } else {
                          widget.onReactionSelected(reaction);
                        }
                      },
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 150),
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? _getReactionColor(reaction).withValues(alpha: 0.2)
                              : Colors.transparent,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Icon(
                          _getReactionIcon(reaction),
                          color: isSelected
                              ? _getReactionColor(reaction)
                              : Colors.grey,
                          size: 24,
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        );
      },
    );
  }

  IconData _getReactionIcon(ReactionType type) {
    switch (type) {
      case ReactionType.like:
        return Icons.favorite;
      case ReactionType.love:
        return Icons.favorite;
      case ReactionType.haha:
        return Icons.sentiment_satisfied;
      case ReactionType.wow:
        return Icons.sentiment_neutral;
      case ReactionType.sad:
        return Icons.sentiment_dissatisfied;
      case ReactionType.angry:
        return Icons.sentiment_very_dissatisfied;
    }
  }

  Color _getReactionColor(ReactionType type) {
    switch (type) {
      case ReactionType.like:
      case ReactionType.love:
        return Colors.red;
      case ReactionType.haha:
        return Colors.yellow.shade700;
      case ReactionType.wow:
        return Colors.orange;
      case ReactionType.sad:
        return Colors.blue;
      case ReactionType.angry:
        return Colors.red.shade800;
    }
  }
}
