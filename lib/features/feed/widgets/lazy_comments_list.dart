import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/feed/models/comment_model.dart';
import 'package:billionaires_social/features/feed/providers/paginated_comments_provider.dart';
import 'package:billionaires_social/features/feed/widgets/voice_comment_widget.dart';
import 'package:billionaires_social/features/feed/widgets/text_comment_widget.dart';

class LazyCommentsList extends ConsumerStatefulWidget {
  final String postId;
  final Function(Comment)? onReply;
  final Function(Comment)? onEdit;
  final Function(Comment)? onDelete;
  final bool Function(Comment)? canEdit;
  final bool Function(Comment)? canDelete;
  final CommentType? filterType;
  final String? searchQuery;

  const LazyCommentsList({
    super.key,
    required this.postId,
    this.onReply,
    this.onEdit,
    this.onDelete,
    this.canEdit,
    this.canDelete,
    this.filterType,
    this.searchQuery,
  });

  @override
  ConsumerState<LazyCommentsList> createState() => _LazyCommentsListState();
}

class _LazyCommentsListState extends ConsumerState<LazyCommentsList> {
  final ScrollController _scrollController = ScrollController();
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreComments();
    }
  }

  Future<void> _loadMoreComments() async {
    if (_isLoadingMore) return;

    setState(() => _isLoadingMore = true);

    final notifier = ref.read(
      paginatedCommentsProvider(widget.postId).notifier,
    );
    await notifier.loadMoreComments();

    if (mounted) {
      setState(() => _isLoadingMore = false);
    }
  }

  Future<void> _refreshComments() async {
    final notifier = ref.read(
      paginatedCommentsProvider(widget.postId).notifier,
    );
    await notifier.refreshComments();
  }

  List<Comment> _getFilteredComments(List<Comment> comments) {
    var filteredComments = comments;

    // Apply type filter
    if (widget.filterType != null) {
      filteredComments = filteredComments
          .where((comment) => comment.type == widget.filterType)
          .toList();
    }

    // Apply search filter
    if (widget.searchQuery != null && widget.searchQuery!.trim().isNotEmpty) {
      final query = widget.searchQuery!.toLowerCase();
      filteredComments = filteredComments.where((comment) {
        return comment.text.toLowerCase().contains(query) ||
            comment.username.toLowerCase().contains(query);
      }).toList();
    }

    return filteredComments;
  }

  @override
  Widget build(BuildContext context) {
    final commentsState = ref.watch(paginatedCommentsProvider(widget.postId));
    final filteredComments = _getFilteredComments(commentsState.comments);

    if (commentsState.isLoading && commentsState.comments.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(20),
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (commentsState.error != null && commentsState.comments.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.error_outline, size: 48, color: Colors.grey[400]),
              const SizedBox(height: 16),
              Text(
                'Failed to load comments',
                style: TextStyle(color: Colors.grey[600], fontSize: 16),
              ),
              const SizedBox(height: 8),
              Text(
                commentsState.error!,
                style: TextStyle(color: Colors.grey[500], fontSize: 12),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _refreshComments,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    if (filteredComments.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.chat_bubble_outline,
                size: 48,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                _getEmptyMessage(),
                style: TextStyle(color: Colors.grey[600], fontSize: 16),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _refreshComments,
      child: ListView.builder(
        controller: _scrollController,
        physics: const AlwaysScrollableScrollPhysics(),
        itemCount: filteredComments.length + (commentsState.hasMore ? 1 : 0),
        itemBuilder: (context, index) {
          // Loading indicator at the end
          if (index == filteredComments.length) {
            return _buildLoadingIndicator();
          }

          final comment = filteredComments[index];
          return _buildCommentItem(comment);
        },
      ),
    );
  }

  Widget _buildCommentItem(Comment comment) {
    switch (comment.type) {
      case CommentType.voice:
        return VoiceCommentWidget(
          comment: comment,
          postId: widget.postId,
          onReply: widget.onReply != null
              ? () => widget.onReply!(comment)
              : null,
          onEdit: widget.onEdit != null ? () => widget.onEdit!(comment) : null,
          onDelete: widget.onDelete != null
              ? () => widget.onDelete!(comment)
              : null,
          canEdit: widget.canEdit?.call(comment) ?? false,
          canDelete: widget.canDelete?.call(comment) ?? false,
        );
      case CommentType.text:
        return TextCommentWidget(
          comment: comment,
          postId: widget.postId,
          onReply: widget.onReply != null
              ? () => widget.onReply!(comment)
              : null,
          onEdit: widget.onEdit != null ? () => widget.onEdit!(comment) : null,
          onDelete: widget.onDelete != null
              ? () => widget.onDelete!(comment)
              : null,
          canEdit: widget.canEdit?.call(comment) ?? false,
          canDelete: widget.canDelete?.call(comment) ?? false,
        );
    }
  }

  Widget _buildLoadingIndicator() {
    final commentsState = ref.watch(paginatedCommentsProvider(widget.postId));

    if (!commentsState.hasMore) {
      return Padding(
        padding: const EdgeInsets.all(16),
        child: Center(
          child: Text(
            'No more comments',
            style: TextStyle(color: Colors.grey[500], fontSize: 14),
          ),
        ),
      );
    }

    return const Padding(
      padding: EdgeInsets.all(16),
      child: Center(
        child: SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(strokeWidth: 2),
        ),
      ),
    );
  }

  String _getEmptyMessage() {
    if (widget.searchQuery != null && widget.searchQuery!.trim().isNotEmpty) {
      return 'No comments found for "${widget.searchQuery}"';
    }

    if (widget.filterType == CommentType.voice) {
      return 'No voice comments yet.\nBe the first to leave a voice comment!';
    }

    if (widget.filterType == CommentType.text) {
      return 'No text comments yet.\nBe the first to comment!';
    }

    return 'No comments yet.\nBe the first to comment!';
  }
}

// Comments header with stats and filters
class CommentsHeader extends ConsumerWidget {
  final String postId;
  final CommentType? selectedFilter;
  final Function(CommentType?)? onFilterChanged;
  final String? searchQuery;
  final Function(String)? onSearchChanged;

  const CommentsHeader({
    super.key,
    required this.postId,
    this.selectedFilter,
    this.onFilterChanged,
    this.searchQuery,
    this.onSearchChanged,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final commentsState = ref.watch(paginatedCommentsProvider(postId));
    final notifier = ref.read(paginatedCommentsProvider(postId).notifier);
    final commentCounts = notifier.getCommentCountsByType();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(bottom: BorderSide(color: Colors.grey[200]!, width: 1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Comments count and stats
          Row(
            children: [
              Text(
                '${commentsState.totalCount} Comments',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              if (commentCounts[CommentType.voice]! > 0) ...[
                Icon(Icons.mic, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  '${commentCounts[CommentType.voice]}',
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
                const SizedBox(width: 12),
              ],
              Icon(
                Icons.chat_bubble_outline,
                size: 16,
                color: Colors.grey[600],
              ),
              const SizedBox(width: 4),
              Text(
                '${commentCounts[CommentType.text]}',
                style: TextStyle(color: Colors.grey[600], fontSize: 12),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Filter chips
          if (onFilterChanged != null)
            Wrap(
              spacing: 8,
              children: [
                _buildFilterChip(
                  label: 'All',
                  isSelected: selectedFilter == null,
                  onTap: () => onFilterChanged!(null),
                ),
                _buildFilterChip(
                  label: 'Text',
                  isSelected: selectedFilter == CommentType.text,
                  onTap: () => onFilterChanged!(CommentType.text),
                ),
                if (commentCounts[CommentType.voice]! > 0)
                  _buildFilterChip(
                    label: 'Voice',
                    isSelected: selectedFilter == CommentType.voice,
                    onTap: () => onFilterChanged!(CommentType.voice),
                  ),
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildFilterChip({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue : Colors.grey[100],
          borderRadius: BorderRadius.circular(16),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.grey[700],
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}
