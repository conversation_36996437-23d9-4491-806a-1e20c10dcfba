import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/features/feed/providers/discovery_feed_provider.dart';
import 'package:billionaires_social/features/feed/widgets/suggested_accounts_widget.dart';
import 'package:billionaires_social/features/profile/screens/content_preferences_screen.dart';
import 'package:billionaires_social/features/creation/screens/create_hub_screen.dart';

class NewUserOnboarding extends ConsumerWidget {
  const NewUserOnboarding({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          const SizedBox(height: 40),

          // Welcome illustration
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: AppTheme.accentColor.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              FontAwesomeIcons.compass,
              size: 60,
              color: AppTheme.accentColor,
            ),
          ),

          const SizedBox(height: 32),

          // Welcome message
          Text(
            'Welcome to Billionaires Social!',
            style: AppTheme.fontStyles.title.copyWith(
              fontSize: 28,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 16),

          Text(
            'Discover amazing content and connect with interesting people to get started.',
            style: AppTheme.fontStyles.body.copyWith(
              color: Colors.grey[600],
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 40),

          // Suggested accounts section
          const SuggestedAccountsWidget(),

          const SizedBox(height: 32),

          // Discovery content preview
          _buildDiscoveryPreview(context, ref),

          const SizedBox(height: 32),

          // Action buttons
          _buildActionButtons(context),

          const SizedBox(height: 24),

          // Settings link
          TextButton.icon(
            onPressed: () => Navigator.of(context).push(
              MaterialPageRoute(
                builder: (_) => const ContentPreferencesScreen(),
              ),
            ),
            icon: const FaIcon(FontAwesomeIcons.gear, size: 16),
            label: const Text('Customize your experience'),
            style: TextButton.styleFrom(foregroundColor: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  Widget _buildDiscoveryPreview(BuildContext context, WidgetRef ref) {
    final discoveryContent = ref.watch(discoveryContentProvider);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const FaIcon(
                  FontAwesomeIcons.fire,
                  size: 18,
                  color: Colors.orange,
                ),
                const SizedBox(width: 8),
                Text('Trending Content', style: AppTheme.fontStyles.subtitle),
              ],
            ),
            const SizedBox(height: 12),

            discoveryContent.when(
              data: (posts) {
                if (posts.isEmpty) {
                  return const Text(
                    'Loading trending content...',
                    style: TextStyle(color: Colors.grey),
                  );
                }

                return Column(
                  children: posts.take(3).map((post) {
                    return ListTile(
                      contentPadding: EdgeInsets.zero,
                      leading: CircleAvatar(
                        backgroundImage: post.userAvatarUrl.isNotEmpty
                            ? NetworkImage(post.userAvatarUrl)
                            : null,
                        child: post.userAvatarUrl.isEmpty
                            ? const Icon(Icons.person)
                            : null,
                      ),
                      title: Text(
                        '@${post.username}',
                        style: AppTheme.fontStyles.bodyBold,
                      ),
                      subtitle: Text(
                        post.caption.length > 50
                            ? '${post.caption.substring(0, 50)}...'
                            : post.caption,
                        style: AppTheme.fontStyles.caption,
                      ),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.favorite,
                            size: 16,
                            color: Colors.red,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${post.likeCount}',
                            style: AppTheme.fontStyles.caption,
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                );
              },
              loading: () => Column(
                children: List.generate(
                  3,
                  (index) => ListTile(
                    contentPadding: EdgeInsets.zero,
                    leading: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        shape: BoxShape.circle,
                      ),
                    ),
                    title: Container(
                      width: 100,
                      height: 16,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    subtitle: Container(
                      width: 200,
                      height: 12,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
                ),
              ),
              error: (error, stack) => const Text(
                'Unable to load trending content',
                style: TextStyle(color: Colors.grey),
              ),
            ),

            const SizedBox(height: 12),

            Center(
              child: TextButton(
                onPressed: () {
                  // Navigate to discovery feed
                  // This would trigger showing discovery content in main feed
                },
                child: const Text('See more trending content'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Column(
      children: [
        // Primary action - Create first post
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () => showModalBottomSheet(
              context: context,
              backgroundColor: Colors.transparent,
              builder: (_) => const CreateHubScreen(),
            ),
            icon: const Icon(Icons.add),
            label: const Text('Create Your First Post'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.accentColor,
              foregroundColor: Colors.black,
              padding: const EdgeInsets.symmetric(vertical: 16),
              textStyle: AppTheme.fontStyles.bodyBold,
            ),
          ),
        ),

        const SizedBox(height: 12),

        // Secondary action - Explore
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () {
              // Navigate to explore/discovery
            },
            icon: const FaIcon(FontAwesomeIcons.compass, size: 18),
            label: const Text('Explore Content'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              side: BorderSide(color: AppTheme.accentColor),
              foregroundColor: AppTheme.accentColor,
            ),
          ),
        ),
      ],
    );
  }
}

/// Compact version for use in feed when user has no content
class CompactNewUserPrompt extends StatelessWidget {
  const CompactNewUserPrompt({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.accentColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.accentColor.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Icon(FontAwesomeIcons.compass, size: 40, color: AppTheme.accentColor),

          const SizedBox(height: 16),

          Text(
            'Discover Amazing Content',
            style: AppTheme.fontStyles.subtitle.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 8),

          Text(
            'Follow interesting accounts or explore trending posts to personalize your feed.',
            style: AppTheme.fontStyles.body.copyWith(color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    // Show suggested accounts
                  },
                  child: const Text('Find People'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton(
                  onPressed: () => showModalBottomSheet(
                    context: context,
                    backgroundColor: Colors.transparent,
                    builder: (_) => const CreateHubScreen(),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.accentColor,
                    foregroundColor: Colors.black,
                  ),
                  child: const Text('Create Post'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
