import 'package:flutter/material.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/features/feed/services/feed_service.dart';
import 'package:billionaires_social/core/service_locator.dart';

class InfiniteScrollController {
  final ScrollController scrollController;
  final FeedService _feedService = getIt<FeedService>();

  bool _isLoading = false;
  bool _hasMoreData = true;
  String? _lastPostId;
  final int _pageSize = 10;

  final List<Post> _posts = [];
  final Function(List<Post>) onPostsLoaded;
  final Function(String) onError;
  final VoidCallback? onLoadingStateChanged;

  InfiniteScrollController({
    required this.onPostsLoaded,
    required this.onError,
    this.onLoadingStateChanged,
  }) : scrollController = ScrollController() {
    _setupScrollListener();
  }

  void _setupScrollListener() {
    scrollController.addListener(() {
      if (_shouldLoadMore()) {
        _loadMorePosts();
      }
    });
  }

  bool _shouldLoadMore() {
    if (_isLoading || !_hasMoreData) return false;

    final maxScroll = scrollController.position.maxScrollExtent;
    final currentScroll = scrollController.position.pixels;
    final threshold = maxScroll * 0.8; // Load when 80% scrolled

    return currentScroll >= threshold;
  }

  Future<void> loadInitialPosts() async {
    if (_isLoading) return;

    _setLoading(true);
    _posts.clear();
    _lastPostId = null;
    _hasMoreData = true;

    try {
      final posts = await _feedService.fetchFeedPosts(limit: _pageSize);
      _posts.addAll(posts);

      if (posts.length < _pageSize) {
        _hasMoreData = false;
      }

      if (posts.isNotEmpty) {
        _lastPostId = posts.last.id;
      }

      onPostsLoaded(_posts);
    } catch (e) {
      onError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  Future<void> _loadMorePosts() async {
    if (_isLoading || !_hasMoreData) return;

    _setLoading(true);

    try {
      final posts = await _feedService.fetchFeedPosts(
        lastPostId: _lastPostId,
        limit: _pageSize,
      );

      if (posts.isEmpty) {
        _hasMoreData = false;
      } else {
        _posts.addAll(posts);
        _lastPostId = posts.last.id;

        if (posts.length < _pageSize) {
          _hasMoreData = false;
        }

        onPostsLoaded(_posts);
      }
    } catch (e) {
      onError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  Future<void> refresh() async {
    await loadInitialPosts();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    onLoadingStateChanged?.call();
  }

  bool get isLoading => _isLoading;
  bool get hasMoreData => _hasMoreData;
  List<Post> get posts => List.unmodifiable(_posts);

  void addPost(Post post) {
    _posts.insert(0, post);
    onPostsLoaded(_posts);
  }

  void removePost(String postId) {
    _posts.removeWhere((post) => post.id == postId);
    onPostsLoaded(_posts);
  }

  void updatePost(Post updatedPost) {
    final index = _posts.indexWhere((post) => post.id == updatedPost.id);
    if (index != -1) {
      _posts[index] = updatedPost;
      onPostsLoaded(_posts);
    }
  }

  void dispose() {
    scrollController.dispose();
  }
}

class InfiniteScrollWidget extends StatefulWidget {
  final Widget Function(BuildContext context, int index) itemBuilder;
  final int itemCount;
  final bool isLoading;
  final bool hasMoreData;
  final VoidCallback? onLoadMore;
  final VoidCallback? onRefresh;
  final Widget? loadingWidget;
  final Widget? emptyWidget;
  final Widget? errorWidget;
  final ScrollController? scrollController;

  const InfiniteScrollWidget({
    super.key,
    required this.itemBuilder,
    required this.itemCount,
    this.isLoading = false,
    this.hasMoreData = true,
    this.onLoadMore,
    this.onRefresh,
    this.loadingWidget,
    this.emptyWidget,
    this.errorWidget,
    this.scrollController,
  });

  @override
  State<InfiniteScrollWidget> createState() => _InfiniteScrollWidgetState();
}

class _InfiniteScrollWidgetState extends State<InfiniteScrollWidget> {
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.scrollController ?? ScrollController();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    if (widget.scrollController == null) {
      _scrollController.dispose();
    }
    super.dispose();
  }

  void _onScroll() {
    if (_shouldLoadMore()) {
      widget.onLoadMore?.call();
    }
  }

  bool _shouldLoadMore() {
    if (widget.isLoading || !widget.hasMoreData) return false;

    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.position.pixels;
    final threshold = maxScroll * 0.8;

    return currentScroll >= threshold;
  }

  @override
  Widget build(BuildContext context) {
    if (widget.itemCount == 0) {
      return widget.emptyWidget ?? _buildDefaultEmptyWidget();
    }

    return RefreshIndicator(
      onRefresh: () async {
        widget.onRefresh?.call();
      },
      child: ListView.builder(
        controller: _scrollController,
        itemCount: widget.itemCount + (widget.hasMoreData ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == widget.itemCount) {
            return widget.loadingWidget ?? _buildDefaultLoadingWidget();
          }
          return widget.itemBuilder(context, index);
        },
      ),
    );
  }

  Widget _buildDefaultEmptyWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
          ),
          const SizedBox(height: 16),
          Text(
            'No posts found',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try refreshing or check back later',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.4),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultLoadingWidget() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: const Center(child: CircularProgressIndicator()),
    );
  }
}
