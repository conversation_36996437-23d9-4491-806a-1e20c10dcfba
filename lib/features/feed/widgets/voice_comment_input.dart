import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:billionaires_social/features/feed/services/voice_comment_service.dart';

class VoiceCommentInput extends ConsumerStatefulWidget {
  final Function(String voiceUrl, int duration, String? waveformData)
  onVoiceRecorded;
  final VoidCallback? onCancel;
  final String postId;

  const VoiceCommentInput({
    super.key,
    required this.onVoiceRecorded,
    this.onCancel,
    required this.postId,
  });

  @override
  ConsumerState<VoiceCommentInput> createState() => _VoiceCommentInputState();
}

class _VoiceCommentInputState extends ConsumerState<VoiceCommentInput>
    with TickerProviderStateMixin {
  final VoiceCommentService _voiceService = VoiceCommentService();

  bool _isRecording = false;
  bool _isPlaying = false;
  bool _hasRecording = false;
  bool _isUploading = false;
  String? _recordingPath;
  int _recordingDuration = 0;

  RecorderController? _recorderController;
  PlayerController? _playerController;

  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _setupAnimations();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _recorderController?.dispose();
    _playerController?.dispose();
    super.dispose();
  }

  void _initializeControllers() {
    _voiceService.initializeControllers();
    _recorderController = _voiceService.recorderController;
    _playerController = _voiceService.playerController;
  }

  void _setupAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
  }

  void _startRecording() async {
    final success = await _voiceService.startRecording();
    if (success) {
      setState(() {
        _isRecording = true;
        _hasRecording = false;
        _recordingDuration = 0;
      });
      _pulseController.repeat(reverse: true);
      _startDurationTimer();
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'Failed to start recording. Please check microphone permissions.',
            ),
          ),
        );
      }
    }
  }

  void _stopRecording() async {
    final path = await _voiceService.stopRecording();
    if (path != null) {
      setState(() {
        _isRecording = false;
        _hasRecording = true;
        _recordingPath = path;
      });
      _pulseController.stop();
      _pulseController.reset();

      // Initialize player for playback
      if (_playerController != null) {
        try {
          await _playerController!.preparePlayer(
            path: path,
            shouldExtractWaveform: true,
          );
        } catch (e) {
          debugPrint('Error preparing player: $e');
        }
      }
    }
  }

  void _cancelRecording() async {
    await _voiceService.cancelRecording();
    setState(() {
      _isRecording = false;
      _hasRecording = false;
      _recordingPath = null;
      _recordingDuration = 0;
    });
    _pulseController.stop();
    _pulseController.reset();
    widget.onCancel?.call();
  }

  void _playRecording() async {
    if (_recordingPath == null || _playerController == null) return;

    try {
      if (_isPlaying) {
        await _playerController!.pausePlayer();
        setState(() => _isPlaying = false);
      } else {
        await _playerController!.startPlayer();
        setState(() => _isPlaying = true);

        _playerController!.onCompletion.listen((_) {
          if (mounted) {
            setState(() => _isPlaying = false);
          }
        });
      }
    } catch (e) {
      debugPrint('Error playing recording: $e');
    }
  }

  void _sendVoiceComment() async {
    if (_recordingPath == null) return;

    setState(() => _isUploading = true);

    try {
      // Upload to Firebase Storage
      final voiceUrl = await _voiceService.uploadVoiceComment(
        _recordingPath!,
        widget.postId,
      );

      if (voiceUrl != null) {
        // Get duration
        final duration = await _voiceService.getRecordingDuration(
          _recordingPath!,
        );

        // Generate waveform data
        final waveformData = await _voiceService.generateWaveformData(
          _recordingPath!,
        );

        widget.onVoiceRecorded(voiceUrl, duration ?? 0, waveformData);

        // Clean up
        setState(() {
          _hasRecording = false;
          _recordingPath = null;
          _recordingDuration = 0;
        });
      } else {
        throw Exception('Failed to upload voice comment');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to send voice comment: $e')),
        );
      }
    } finally {
      setState(() => _isUploading = false);
    }
  }

  void _startDurationTimer() {
    Future.delayed(const Duration(seconds: 1), () {
      if (_isRecording && mounted) {
        setState(() => _recordingDuration++);
        _startDurationTimer();
      }
    });
  }

  String _formatDuration(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '$minutes:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Voice Comment',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              IconButton(
                onPressed: _cancelRecording,
                icon: const Icon(Icons.close),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Recording/Playback Area
          if (_isRecording) _buildRecordingUI(),
          if (_hasRecording && !_isRecording) _buildPlaybackUI(),
          if (!_isRecording && !_hasRecording) _buildInitialUI(),

          const SizedBox(height: 20),

          // Action Buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              if (!_hasRecording) ...[
                // Record Button
                AnimatedBuilder(
                  animation: _pulseAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _isRecording ? _pulseAnimation.value : 1.0,
                      child: GestureDetector(
                        onTap: _isRecording ? _stopRecording : _startRecording,
                        child: Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            color: _isRecording
                                ? Colors.red
                                : Theme.of(context).primaryColor,
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            _isRecording ? Icons.stop : Icons.mic,
                            color: Colors.white,
                            size: 32,
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ] else ...[
                // Play Button
                GestureDetector(
                  onTap: _playRecording,
                  child: Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      _isPlaying ? Icons.pause : Icons.play_arrow,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),

                // Re-record Button
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _hasRecording = false;
                      _recordingPath = null;
                    });
                  },
                  child: Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: Colors.grey[400],
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.refresh,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),

                // Send Button
                GestureDetector(
                  onTap: _isUploading ? null : _sendVoiceComment,
                  child: Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: _isUploading ? Colors.grey[400] : Colors.green,
                      shape: BoxShape.circle,
                    ),
                    child: _isUploading
                        ? const SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          )
                        : const Icon(Icons.send, color: Colors.white, size: 24),
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInitialUI() {
    return Column(
      children: [
        Icon(Icons.mic, size: 64, color: Colors.grey[400]),
        const SizedBox(height: 16),
        Text(
          'Tap to record a voice comment',
          style: TextStyle(fontSize: 16, color: Colors.grey[600]),
        ),
      ],
    );
  }

  Widget _buildRecordingUI() {
    return Column(
      children: [
        // Recording Waveform
        if (_recorderController != null)
          AudioWaveforms(
            size: const Size(300, 60),
            recorderController: _recorderController!,
            enableGesture: false,
            waveStyle: WaveStyle(
              waveColor: Theme.of(context).primaryColor,
              showDurationLabel: false,
              spacing: 8.0,
              showBottom: false,
              extendWaveform: true,
              showMiddleLine: false,
            ),
          ),
        const SizedBox(height: 16),
        Text(
          _formatDuration(_recordingDuration),
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        Text(
          'Recording...',
          style: TextStyle(fontSize: 14, color: Colors.grey[600]),
        ),
      ],
    );
  }

  Widget _buildPlaybackUI() {
    return Column(
      children: [
        // Playback Waveform
        if (_playerController != null)
          AudioFileWaveforms(
            size: const Size(300, 60),
            playerController: _playerController!,
            enableSeekGesture: true,
            waveformType: WaveformType.fitWidth,
            playerWaveStyle: PlayerWaveStyle(
              fixedWaveColor: Colors.grey[400]!,
              liveWaveColor: Theme.of(context).primaryColor,
              spacing: 6,
              showSeekLine: false,
              waveThickness: 3,
            ),
          ),
        const SizedBox(height: 16),
        Text(
          _formatDuration(_recordingDuration),
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        Text(
          'Tap play to preview',
          style: TextStyle(fontSize: 14, color: Colors.grey[600]),
        ),
      ],
    );
  }
}
