import 'package:flutter/material.dart';

class HashtagFeedScreen extends StatelessWidget {
  final String hashtag;
  const HashtagFeedScreen({super.key, required this.hashtag});

  @override
  Widget build(BuildContext context) {
    // TODO: Connect to Firestore and fetch posts for this hashtag
    return Scaffold(
      appBar: AppBar(title: Text('#$hashtag')),
      body: Center(child: Text('Posts for #$hashtag')),
    );
  }
}
