import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/features/feed/providers/discovery_feed_provider.dart';
import 'package:billionaires_social/features/profile/models/profile_model.dart';
import 'package:billionaires_social/features/profile/services/followers_service.dart';
import 'package:billionaires_social/features/profile/screens/user_profile_screen.dart';
import 'package:billionaires_social/features/stories/widgets/story_aware_profile_image.dart';
import 'package:get_it/get_it.dart';

class SuggestedAccountsWidget extends ConsumerStatefulWidget {
  const SuggestedAccountsWidget({super.key});

  @override
  ConsumerState<SuggestedAccountsWidget> createState() => _SuggestedAccountsWidgetState();
}

class _SuggestedAccountsWidgetState extends ConsumerState<SuggestedAccountsWidget> {
  final FollowersService _followersService = GetIt.I<FollowersService>();
  final Set<String> _followingUsers = {};
  final Set<String> _dismissedUsers = {};

  @override
  Widget build(BuildContext context) {
    final suggestedAccountsAsync = ref.watch(suggestedAccountsProvider);

    return suggestedAccountsAsync.when(
      data: (accounts) {
        if (accounts.isEmpty) return const SizedBox.shrink();

        // Filter out dismissed accounts
        final visibleAccounts = accounts
            .where((account) => !_dismissedUsers.contains(account.id))
            .toList();

        if (visibleAccounts.isEmpty) return const SizedBox.shrink();

        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const FaIcon(FontAwesomeIcons.userPlus, size: 18),
                    const SizedBox(width: 8),
                    Text(
                      'Suggested for you',
                      style: AppTheme.fontStyles.subtitle,
                    ),
                    const Spacer(),
                    TextButton(
                      onPressed: () => _showAllSuggestions(context, visibleAccounts),
                      child: const Text('See All'),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                SizedBox(
                  height: 120,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: visibleAccounts.take(10).length,
                    itemBuilder: (context, index) {
                      final account = visibleAccounts[index];
                      return _buildSuggestedAccountCard(account);
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
      loading: () => _buildLoadingSkeleton(),
      error: (error, stack) => const SizedBox.shrink(),
    );
  }

  Widget _buildSuggestedAccountCard(ProfileModel account) {
    final isFollowing = _followingUsers.contains(account.id);

    return Container(
      width: 100,
      margin: const EdgeInsets.only(right: 12),
      child: Column(
        children: [
          GestureDetector(
            onTap: () => _navigateToProfile(account.id),
            child: StoryAwareProfileImage(
              userId: account.id,
              profileImageUrl: account.profilePictureUrl,
              size: 60,
              showStoryIndicator: false,
            ),
          ),
          const SizedBox(height: 8),
          GestureDetector(
            onTap: () => _navigateToProfile(account.id),
            child: Text(
              account.username,
              style: AppTheme.fontStyles.caption.copyWith(
                fontWeight: FontWeight.w600,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 4),
          if (isFollowing)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                'Following',
                style: AppTheme.fontStyles.caption.copyWith(
                  color: Colors.grey[700],
                ),
              ),
            )
          else
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _followUser(account),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.accentColor,
                      foregroundColor: Colors.black,
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      minimumSize: const Size(0, 28),
                      textStyle: AppTheme.fontStyles.caption.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    child: const Text('Follow'),
                  ),
                ),
                const SizedBox(width: 4),
                GestureDetector(
                  onTap: () => _dismissSuggestion(account.id),
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    child: const Icon(
                      Icons.close,
                      size: 16,
                      color: Colors.grey,
                    ),
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildLoadingSkeleton() {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 18,
                  height: 18,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  width: 120,
                  height: 16,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            SizedBox(
              height: 120,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: 5,
                itemBuilder: (context, index) {
                  return Container(
                    width: 100,
                    margin: const EdgeInsets.only(right: 12),
                    child: Column(
                      children: [
                        Container(
                          width: 60,
                          height: 60,
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          width: 80,
                          height: 12,
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Container(
                          width: 60,
                          height: 24,
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToProfile(String userId) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => UserProfileScreen(userId: userId),
      ),
    );
  }

  Future<void> _followUser(ProfileModel account) async {
    try {
      await _followersService.followUser(account.id);
      
      setState(() {
        _followingUsers.add(account.id);
      });

      // Remove from suggestions after following
      ref.read(suggestedAccountsProvider.notifier).removeSuggestion(account.id);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Now following ${account.username}'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to follow ${account.username}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _dismissSuggestion(String userId) {
    setState(() {
      _dismissedUsers.add(userId);
    });

    // Remove from provider
    ref.read(suggestedAccountsProvider.notifier).removeSuggestion(userId);
  }

  void _showAllSuggestions(BuildContext context, List<ProfileModel> accounts) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) {
          return Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: Column(
              children: [
                Container(
                  margin: const EdgeInsets.symmetric(vertical: 8),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Text(
                        'Suggested for you',
                        style: AppTheme.fontStyles.title,
                      ),
                      const Spacer(),
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(Icons.close),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: ListView.builder(
                    controller: scrollController,
                    itemCount: accounts.length,
                    itemBuilder: (context, index) {
                      final account = accounts[index];
                      return _buildSuggestionListTile(account);
                    },
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildSuggestionListTile(ProfileModel account) {
    final isFollowing = _followingUsers.contains(account.id);

    return ListTile(
      leading: StoryAwareProfileImage(
        userId: account.id,
        profileImageUrl: account.profilePictureUrl,
        size: 50,
        showStoryIndicator: false,
      ),
      title: Text(
        account.username,
        style: AppTheme.fontStyles.bodyBold,
      ),
      subtitle: Text(
        account.name,
        style: AppTheme.fontStyles.caption,
      ),
      trailing: isFollowing
          ? Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                'Following',
                style: AppTheme.fontStyles.caption.copyWith(
                  color: Colors.grey[700],
                ),
              ),
            )
          : ElevatedButton(
              onPressed: () => _followUser(account),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.accentColor,
                foregroundColor: Colors.black,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              child: const Text('Follow'),
            ),
      onTap: () {
        Navigator.pop(context);
        _navigateToProfile(account.id);
      },
    );
  }
}
