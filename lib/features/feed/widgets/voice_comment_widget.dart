import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:billionaires_social/features/feed/models/comment_model.dart';
import 'package:billionaires_social/features/feed/widgets/comment_reaction_picker.dart';
import 'package:billionaires_social/features/feed/services/voice_comment_service.dart';
import 'package:cached_network_image/cached_network_image.dart';

class VoiceCommentWidget extends ConsumerStatefulWidget {
  final Comment comment;
  final String postId;
  final VoidCallback? onReply;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final bool canDelete;
  final bool canEdit;

  const VoiceCommentWidget({
    super.key,
    required this.comment,
    required this.postId,
    this.onReply,
    this.onEdit,
    this.onDelete,
    this.canDelete = false,
    this.canEdit = false,
  });

  @override
  ConsumerState<VoiceCommentWidget> createState() => _VoiceCommentWidgetState();
}

class _VoiceCommentWidgetState extends ConsumerState<VoiceCommentWidget> {
  final VoiceCommentService _voiceService = VoiceCommentService();
  bool _isPlaying = false;
  bool _isLoading = false;

  PlayerController? _playerController;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  @override
  void dispose() {
    _playerController?.dispose();
    _voiceService.stopPlayback(); // Clean up voice service
    super.dispose();
  }

  void _initializePlayer() async {
    if (widget.comment.voiceUrl != null) {
      _playerController = PlayerController();
      try {
        await _playerController!.preparePlayer(
          path: widget.comment.voiceUrl!,
          shouldExtractWaveform: true,
        );
        if (mounted) setState(() {});
      } catch (e) {
        debugPrint('Error initializing player: $e');
      }
    }
  }

  void _togglePlayback() async {
    if (widget.comment.voiceUrl == null) return;

    setState(() => _isLoading = true);

    try {
      if (_isPlaying) {
        await _voiceService.stopPlayback();
        setState(() => _isPlaying = false);
      } else {
        final success = await _voiceService.playVoiceComment(
          widget.comment.voiceUrl!,
        );
        if (success) {
          setState(() => _isPlaying = true);
        } else {
          throw Exception('Failed to start playback');
        }
      }
    } catch (e) {
      debugPrint('Error toggling playback: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error playing voice comment: $e')),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  String _formatDuration(int? seconds) {
    if (seconds == null) return '0:00';
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '$minutes:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  void _showReactionPicker() {
    showDialog(
      context: context,
      barrierColor: Colors.transparent,
      builder: (context) {
        return Stack(
          children: [
            Positioned.fill(
              child: GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: Container(color: Colors.transparent),
              ),
            ),
            Positioned(
              bottom: 100,
              left: 20,
              child: CommentReactionPicker(
                postId: widget.postId,
                commentId: widget.comment.id,
                onReactionAdded: () {
                  // Refresh the widget to show new reactions
                  if (mounted) setState(() {});
                },
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // User Avatar
          CircleAvatar(
            radius: 16,
            backgroundImage: _isValidImageUrl(widget.comment.userAvatarUrl)
                ? CachedNetworkImageProvider(widget.comment.userAvatarUrl)
                : null,
            backgroundColor: Colors.grey[300],
            child: !_isValidImageUrl(widget.comment.userAvatarUrl)
                ? const Icon(Icons.person, size: 16, color: Colors.grey)
                : null,
          ),
          const SizedBox(width: 12),

          // Voice Comment Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Username and timestamp
                Row(
                  children: [
                    Text(
                      widget.comment.username,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 13,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      _formatTimeAgo(widget.comment.timestamp),
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                    if (widget.comment.isEdited) ...[
                      const SizedBox(width: 4),
                      Text(
                        '(edited)',
                        style: TextStyle(
                          color: Colors.grey[500],
                          fontSize: 11,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ],
                ),
                const SizedBox(height: 8),

                // Voice Player Container
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(color: Colors.grey[300]!, width: 1),
                  ),
                  child: Row(
                    children: [
                      // Play/Pause Button
                      GestureDetector(
                        onTap: _togglePlayback,
                        child: Container(
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor,
                            shape: BoxShape.circle,
                          ),
                          child: _isLoading
                              ? const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color: Colors.white,
                                  ),
                                )
                              : Icon(
                                  _isPlaying ? Icons.pause : Icons.play_arrow,
                                  color: Colors.white,
                                  size: 18,
                                ),
                        ),
                      ),
                      const SizedBox(width: 12),

                      // Waveform or Progress Indicator
                      Expanded(child: _buildWaveform()),
                      const SizedBox(width: 8),

                      // Duration
                      Text(
                        _formatDuration(widget.comment.voiceDuration),
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 8),

                // Reactions Display
                if (widget.comment.reactionCounts != null &&
                    widget.comment.reactionCounts!.isNotEmpty)
                  CommentReactionDisplay(
                    postId: widget.postId,
                    commentId: widget.comment.id,
                    reactions: widget.comment.reactions ?? {},
                    reactionCounts: widget.comment.reactionCounts ?? {},
                    totalReactionCount: widget.comment.reactionCount,
                    onReactionTap: _showReactionPicker,
                  ),

                const SizedBox(height: 8),

                // Action Buttons
                Row(
                  children: [
                    // React button
                    GestureDetector(
                      onTap: _showReactionPicker,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.add_reaction_outlined,
                              size: 14,
                              color: Colors.grey[600],
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'React',
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    if (widget.onReply != null)
                      GestureDetector(
                        onTap: widget.onReply,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          child: Text(
                            'Reply',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    if (widget.canEdit && widget.onEdit != null)
                      GestureDetector(
                        onTap: widget.onEdit,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          child: Text(
                            'Edit',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    if (widget.canDelete && widget.onDelete != null)
                      GestureDetector(
                        onTap: widget.onDelete,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          child: Text(
                            'Delete',
                            style: TextStyle(
                              color: Colors.red[600],
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWaveform() {
    if (_playerController == null) {
      return const SizedBox(
        height: 32,
        child: Center(
          child: Text(
            'Loading...',
            style: TextStyle(fontSize: 12, color: Colors.grey),
          ),
        ),
      );
    }

    return AudioFileWaveforms(
      size: const Size(double.infinity, 32),
      playerController: _playerController!,
      enableSeekGesture: true,
      waveformType: WaveformType.fitWidth,
      playerWaveStyle: PlayerWaveStyle(
        fixedWaveColor: Colors.grey[400]!,
        liveWaveColor: Colors.blue,
        spacing: 6,
        showSeekLine: false,
        waveThickness: 3,
      ),
    );
  }

  String _formatTimeAgo(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d';
    } else {
      return '${difference.inDays ~/ 7}w';
    }
  }

  bool _isValidImageUrl(String? url) {
    if (url == null || url.isEmpty) return false;
    return url.startsWith('http://') || url.startsWith('https://');
  }
}
