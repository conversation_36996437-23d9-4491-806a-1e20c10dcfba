import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/feed/providers/feed_filter_provider.dart';
import 'package:billionaires_social/features/feed/providers/feed_provider.dart';
import 'package:billionaires_social/features/stories/providers/story_provider.dart';

class FilterModal extends ConsumerWidget {
  final String categoryName;

  const FilterModal({super.key, required this.categoryName});

  String _getCategoryIcon(String category) {
    switch (category) {
      case 'Billionaires':
        return '👑';
      case 'Places':
        return '📍';
      case 'Trends':
        return '🔥';
      case 'Reels':
        return '🎬';
      default:
        return '👑';
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentFilter = ref.watch(feedFilterProvider);
    final filterOptions = ref
        .read(feedFilterProvider.notifier)
        .getFilterOptions();
    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: theme.dividerColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Text(
                  _getCategoryIcon(categoryName),
                  style: TextStyle(fontSize: 24),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Filter $categoryName',
                        style: theme.textTheme.headlineSmall,
                      ),
                      Text(
                        'Choose which users to show in $categoryName',
                        style: theme.textTheme.bodyMedium,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Filter options
          Flexible(
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: filterOptions.length,
              itemBuilder: (context, index) {
                final option = filterOptions[index];
                final filterType = option['type'] as FeedFilterType;
                final isSelected = currentFilter == filterType;

                return ListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: isSelected
                          ? theme.colorScheme.secondary.withAlpha(51)
                          : theme.dividerColor.withAlpha(128),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Center(
                      child: Text(
                        option['icon'] as String,
                        style: TextStyle(
                          fontSize: 18,
                          color: isSelected
                              ? theme.colorScheme.secondary
                              : theme.iconTheme.color,
                        ),
                      ),
                    ),
                  ),
                  title: Text(
                    option['title'] as String,
                    style: theme.textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: isSelected ? theme.colorScheme.secondary : null,
                    ),
                  ),
                  subtitle: Text(
                    option['description'] as String,
                    style: theme.textTheme.bodyMedium,
                  ),
                  trailing: isSelected
                      ? Icon(
                          Icons.check_circle,
                          color: theme.colorScheme.secondary,
                        )
                      : null,
                  onTap: () {
                    // Set the global filter
                    ref.read(feedFilterProvider.notifier).setFilter(filterType);

                    // Refresh all content types with the new filter
                    ref
                        .read(feedProvider.notifier)
                        .fetchFilteredPosts(filterType);
                    ref.read(storyReelsProvider.notifier).refresh();

                    // Show success message
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          'Showing ${option['title']} content in ${categoryName.toLowerCase()}',
                        ),
                        backgroundColor: Colors.green,
                        duration: const Duration(seconds: 2),
                      ),
                    );

                    Navigator.of(context).pop();
                  },
                );
              },
            ),
          ),

          // Bottom padding
          const SizedBox(height: 20),
        ],
      ),
    );
  }
}

void showFilterModal(BuildContext context, String categoryName) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) => FilterModal(categoryName: categoryName),
  );
}
