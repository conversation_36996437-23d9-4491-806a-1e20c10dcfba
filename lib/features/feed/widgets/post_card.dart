import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/features/feed/providers/feed_provider.dart';
import 'package:billionaires_social/features/feed/providers/paginated_feed_provider.dart';
import 'package:billionaires_social/features/feed/services/post_action_service.dart';
import 'package:billionaires_social/features/feed/services/post_management_service.dart';
import 'package:billionaires_social/features/feed/services/feed_service.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/features/feed/widgets/comment_bottom_sheet.dart';
import 'package:billionaires_social/features/feed/widgets/post_actions_sheet.dart';
import 'package:billionaires_social/features/feed/widgets/share_post_sheet.dart';
import 'package:billionaires_social/features/feed/widgets/likers_list_modal.dart';
import 'package:billionaires_social/features/feed/screens/photo_view_screen.dart';
import 'package:billionaires_social/features/feed/screens/edit_post_screen.dart';
import 'package:billionaires_social/features/feed/screens/hashtag_feed_screen.dart';
import 'package:billionaires_social/features/feed/screens/video_reels_screen.dart';
import 'package:billionaires_social/core/services/universal_navigation_service.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cached_network_image/cached_network_image.dart';

/// Clean, simplified PostCard with working functionality
class PostCard extends ConsumerStatefulWidget {
  final Post post;
  final bool showAnimation;

  const PostCard({super.key, required this.post, this.showAnimation = true});

  @override
  ConsumerState<PostCard> createState() => _PostCardState();
}

class _PostCardState extends ConsumerState<PostCard>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _fadeController;
  late AnimationController _captionController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  bool _isLiked = false;
  bool _isBookmarked = false;
  bool _isReposted = false;
  bool _postingRepost = false;
  bool _reporting = false;
  bool _isLiking = false; // Prevent rapid like tapping
  bool _isBookmarking = false; // Prevent rapid bookmark tapping
  bool _isCaptionExpanded = false;

  final PostActionService _actionService = PostActionService();
  final PostManagementService _managementService = PostManagementService();

  // Permission states - Default to true for basic actions
  bool _canLike = true;
  bool _canComment = true;
  bool _canShare = true;
  bool _canRepost = true;
  bool _canReport = true;
  bool _canEdit = false;
  bool _canDelete = false;
  final UserPostRole _userRole = UserPostRole.viewer;

  @override
  void initState() {
    super.initState();
    _isLiked = widget.post.isLiked;
    _isBookmarked = widget.post.isBookmarked;
    _isReposted = widget.post.isReposted;

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _captionController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeOut));

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(parent: _fadeController, curve: Curves.easeOutCubic),
        );

    if (widget.showAnimation) {
      _fadeController.forward();
      _scaleController.forward();
    }

    // Initialize permissions and like state
    _initializePermissions();
    _initializeLikeState();
  }

  /// Initialize user permissions for this post
  Future<void> _initializePermissions() async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        // Set defaults for unauthenticated users
        setState(() {
          _canLike = false;
          _canComment = false;
          _canShare = true; // Allow sharing even for unauthenticated
          _canRepost = false;
          _canReport = false;
          _canEdit = false;
          _canDelete = false;
        });
        return;
      }

      // Set permissions based on user authentication and post ownership
      final isOwner = currentUser.uid == widget.post.userId;

      if (mounted) {
        setState(() {
          _canLike = true; // Allow like for authenticated users
          _canComment = true; // Allow comment for authenticated users
          _canShare = true; // Allow share for authenticated users
          _canRepost = true; // Allow repost for authenticated users
          _canReport = !isOwner; // Allow report for non-owners
          _canEdit = isOwner; // Allow edit for owners only
          _canDelete = isOwner; // Allow delete for owners only
        });
      }
    } catch (e) {
      debugPrint('Error initializing permissions: $e');

      // Set safe defaults on error
      if (mounted) {
        setState(() {
          _canLike = true;
          _canComment = true;
          _canShare = true;
          _canRepost = true;
          _canReport = true;
          _canEdit = false;
          _canDelete = false;
        });
      }
    }
  }

  /// Initialize like state based on current user
  Future<void> _initializeLikeState() async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) return;

      // Check if current user has liked this post
      final likeDoc = await FirebaseFirestore.instance
          .collection('posts')
          .doc(widget.post.id)
          .collection('likes')
          .doc(currentUser.uid)
          .get();

      if (mounted) {
        setState(() {
          _isLiked = likeDoc.exists;
        });
        debugPrint(
          '🔄 Like state initialized: $_isLiked for post ${widget.post.id}',
        );
      }
    } catch (e) {
      debugPrint('❌ Error initializing like state: $e');
      // Keep the default state from widget.post.isLiked
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _scaleController.dispose();
    _captionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Prevent rebuilds if widget is not mounted or post is null
    if (!mounted || widget.post.id.isEmpty) {
      return const SizedBox.shrink();
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Container(
          margin: EdgeInsets.zero, // Instagram has no margin between posts
          decoration: const BoxDecoration(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(context),
              _buildPostMedia(context),
              _buildActionButtons(context),
              _buildPostDetails(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    final currentUserId = FirebaseAuth.instance.currentUser?.uid;
    final isOwner = currentUserId == widget.post.userId;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      child: Row(
        children: [
          // Profile Picture(s) - Support co-authors
          _buildProfilePictures(),
          const SizedBox(width: 12.0),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildUsernameRow(context),
                if (widget.post.userRole != null) _buildRoleDisplay(context),
                if (widget.post.location != null)
                  _buildLocationDisplay(context),
              ],
            ),
          ),
          _buildMoreButton(context, isOwner),
        ],
      ),
    );
  }

  Widget _buildProfilePictures() {
    final List<String> avatarUrls = [widget.post.userAvatarUrl];
    final List<String> usernames = [widget.post.username];

    // Add co-authors if they exist
    if (widget.post.coAuthorAvatars != null) {
      avatarUrls.addAll(widget.post.coAuthorAvatars!);
      if (widget.post.coAuthorUsernames != null) {
        usernames.addAll(widget.post.coAuthorUsernames!);
      }
    }

    if (avatarUrls.length == 1) {
      return Stack(
        children: [
          GestureDetector(
            onTap: () => _navigateToProfile(widget.post.userId),
            child: CircleAvatar(
              radius: 24,
              backgroundImage: CachedNetworkImageProvider(avatarUrls[0]),
              onBackgroundImageError: (exception, stackTrace) {
                debugPrint('Error loading profile image: $exception');
              },
            ),
          ),
          if (widget.post.isVerified == true)
            Positioned(
              bottom: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.all(2),
                decoration: const BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.verified, color: Colors.blue, size: 16),
              ),
            ),
        ],
      );
    }

    // Multiple co-authors
    return Row(
      children: avatarUrls.take(3).map((avatarUrl) {
        final index = avatarUrls.indexOf(avatarUrl);
        return Container(
          margin: EdgeInsets.only(
            right: index < avatarUrls.length - 1 ? -8 : 0,
          ),
          child: GestureDetector(
            onTap: () => _navigateToProfile(
              index == 0
                  ? widget.post.userId
                  : widget.post.coAuthorIds![index - 1],
            ),
            child: CircleAvatar(
              radius: 20,
              backgroundImage: CachedNetworkImageProvider(avatarUrl),
              onBackgroundImageError: (exception, stackTrace) {
                debugPrint('Error loading profile image: $exception');
              },
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildUsernameRow(BuildContext context) {
    final List<String> usernames = [widget.post.username];
    if (widget.post.coAuthorUsernames != null) {
      usernames.addAll(widget.post.coAuthorUsernames!);
    }

    return Row(
      children: [
        Expanded(
          child: GestureDetector(
            onTap: () => _navigateToProfile(widget.post.userId),
            child: RichText(
              text: TextSpan(
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
                children: usernames.asMap().entries.map((entry) {
                  final index = entry.key;
                  final username = entry.value;
                  return TextSpan(
                    children: [
                      if (index > 0) const TextSpan(text: ' & '),
                      TextSpan(
                        text: username,
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ],
                  );
                }).toList(),
              ),
            ),
          ),
        ),
        if (widget.post.trending == true)
          Padding(
            padding: const EdgeInsets.only(right: 4.0),
            child: Icon(
              Icons.local_fire_department,
              color: Theme.of(context).colorScheme.secondary,
              size: 18,
              shadows: [
                Shadow(
                  color: Theme.of(
                    context,
                  ).colorScheme.secondary.withValues(alpha: 0.4),
                  blurRadius: 6,
                  offset: Offset(0, 2),
                ),
              ],
            ),
          ),
        if (widget.post.isVerified == true)
          const Icon(Icons.verified, color: Colors.blue, size: 16),
      ],
    );
  }

  Widget _buildRoleDisplay(BuildContext context) {
    if (widget.post.userRole == null) return const SizedBox.shrink();

    return Text(
      widget.post.userRole!,
      style: Theme.of(context).textTheme.bodySmall?.copyWith(
        color: Theme.of(context).colorScheme.secondary,
        fontStyle: FontStyle.italic,
      ),
    );
  }

  Widget _buildLocationDisplay(BuildContext context) {
    return GestureDetector(
      onTap: () => _navigateToLocation(),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.location_on,
            size: 14,
            color: Theme.of(context).colorScheme.secondary,
          ),
          const SizedBox(width: 4),
          Text(
            widget.post.location!,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.secondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMoreButton(BuildContext context, bool isOwner) {
    return IconButton(
      icon: Icon(Icons.more_horiz, color: Theme.of(context).iconTheme.color),
      onPressed: () {
        // Debug permission flags when three-dot menu is tapped
        if (kDebugMode) {
          final currentUserId = FirebaseAuth.instance.currentUser?.uid;
          debugPrint(
            '🔧 Three-dot menu tapped for post ${widget.post.id.substring(0, 8)}...',
          );
          debugPrint('   Current user: $currentUserId');
          debugPrint('   Post owner: ${widget.post.userId}');
          debugPrint('   isOwner: $isOwner');
          debugPrint('   _canEdit: $_canEdit');
          debugPrint('   _canDelete: $_canDelete');
          debugPrint('   _userRole: $_userRole');
          debugPrint(
            '   Delete callback will be: ${(_canDelete || isOwner) ? "PROVIDED" : "NULL"}',
          );
        }

        showModalBottomSheet(
          context: context,
          backgroundColor: Colors.transparent,
          builder: (context) => PostActionsSheet(
            post: widget.post,
            isOwner: isOwner,
            onRepost: _handleRepost,
            onReport: _handleReport,
            onDelete: (_canDelete || isOwner)
                ? () => _deletePost(context)
                : null,
            onEdit: (_canEdit || isOwner) ? () => _editPost(context) : null,
            onArchive: _userRole == UserPostRole.owner
                ? () => _archivePost(context)
                : null,
            onPin: _userRole == UserPostRole.owner
                ? () => _togglePinPost(context)
                : null,
            onPostDeleted: null, // Handled directly in _deletePost method
          ),
        );
      },
    );
  }

  Widget _buildPostMedia(BuildContext context) {
    // Check if post has multiple media
    final hasMultipleMedia =
        widget.post.mediaUrls != null &&
        widget.post.mediaUrls!.isNotEmpty &&
        widget.post.mediaTypes != null &&
        widget.post.mediaTypes!.isNotEmpty;

    if (hasMultipleMedia) {
      return GestureDetector(
        onDoubleTap: () => _handleDoubleTap(),
        onTap: () => _handleMediaTap(0), // Tap on first media
        child: Stack(
          children: [
            // Multi-media carousel - simplified implementation
            SizedBox(
              height: MediaQuery.of(context).size.height * 0.6,
              width: double.infinity,
              child: widget.post.mediaUrls!.isNotEmpty
                  ? CachedNetworkImage(
                      imageUrl: widget.post.mediaUrls!.first,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        color: Colors.grey[300],
                        child: const Center(child: CircularProgressIndicator()),
                      ),
                      errorWidget: (context, url, error) => Container(
                        color: Colors.grey[300],
                        child: const Center(child: Icon(Icons.error)),
                      ),
                    )
                  : Container(
                      color: Colors.grey[300],
                      child: const Center(child: Icon(Icons.image)),
                    ),
            ),
            // Media tags overlay - simplified (placeholder)
            if (widget.post.mediaTags != null &&
                widget.post.mediaTags!.isNotEmpty)
              const SizedBox.shrink(),
            // Double tap heart animation - simplified
            if (_isLiked)
              const Positioned.fill(
                child: Center(
                  child: Icon(Icons.favorite, color: Colors.red, size: 80),
                ),
              ),
          ],
        ),
      );
    } else {
      // Single media display (legacy)
      return GestureDetector(
        onDoubleTap: () => _handleDoubleTap(),
        onTap: () => _navigateToPhotoView(),
        child: Stack(
          children: [
            Hero(
              tag: 'post_image_${widget.post.id}',
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8.0),
                child: InteractiveViewer(
                  minScale: 0.8,
                  maxScale: 3.0,
                  child: CachedNetworkImage(
                    imageUrl: widget.post.mediaUrl,
                    fit: BoxFit.contain, // Keep aspect ratio, no distortion
                    width: double.infinity,
                    height:
                        MediaQuery.of(context).size.height *
                        0.6, // Max 60% of screen height
                    placeholder: (context, url) => Container(
                      height: MediaQuery.of(context).size.height * 0.6,
                      color: Theme.of(context).cardColor.withAlpha(128),
                      child: const Center(child: CircularProgressIndicator()),
                    ),
                    errorWidget: (context, url, error) => Container(
                      height: MediaQuery.of(context).size.height * 0.6,
                      color: Theme.of(context).colorScheme.errorContainer,
                      child: const Center(child: Icon(Icons.error, size: 48)),
                    ),
                  ),
                ),
              ),
            ),
            // Media tags overlay - simplified (placeholder)
            if (widget.post.mediaTags != null &&
                widget.post.mediaTags!.isNotEmpty)
              const SizedBox.shrink(),
            // Double tap heart animation - simplified
            if (_isLiked)
              const Positioned.fill(
                child: Center(
                  child: Icon(Icons.favorite, color: Colors.red, size: 80),
                ),
              ),
          ],
        ),
      );
    }
  }

  Widget _buildActionButtons(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              // Like button - show for all authenticated users
              if (_canLike || FirebaseAuth.instance.currentUser != null) ...[
                _buildLikeButton(context),
                const SizedBox(width: 16.0),
              ],
              // Comment button - only show if user can comment
              if (_canComment) ...[
                _buildCommentButton(context),
                const SizedBox(width: 16.0),
              ],
              // Share button - only show if user can share
              if (_canShare) ...[
                _buildShareButton(context),
                const SizedBox(width: 8),
              ],
              // Repost button - only show if user can repost
              if (_canRepost) ...[
                _buildRepostButton(context),
                const SizedBox(width: 8),
              ],
            ],
          ),
          _buildBookmarkButton(context),
        ],
      ),
    );
  }

  Widget _buildLikeButton(BuildContext context) {
    return GestureDetector(
      onTap: () {
        debugPrint('🔥 Like button tapped for post ${widget.post.id}');
        _handleLike();
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.all(4), // Add padding for better tap area
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          // Add subtle background for better visibility
          color: _isLiked
              ? Colors.red.withValues(alpha: 0.1)
              : Colors.transparent,
          // Add border for better visibility in debug
          border: kDebugMode
              ? Border.all(color: Colors.grey.withValues(alpha: 0.3), width: 1)
              : null,
        ),
        child: AnimatedScale(
          scale: _isLiked ? 1.1 : 1.0,
          duration: const Duration(milliseconds: 150),
          child: Icon(
            _isLiked ? Icons.favorite : Icons.favorite_border,
            color: _isLiked ? Colors.red : Theme.of(context).iconTheme.color,
            size: 28,
          ),
        ),
      ),
    );
  }

  Widget _buildCommentButton(BuildContext context) {
    return GestureDetector(
      onTap: () => _showComments(),
      child: Icon(
        Icons.chat_bubble_outline,
        size: 26,
        color: Theme.of(context).iconTheme.color,
      ),
    );
  }

  Widget _buildShareButton(BuildContext context) {
    return GestureDetector(
      onTap: () => _handleShare(),
      child: const Icon(Icons.send, size: 24),
    );
  }

  Widget _buildRepostButton(BuildContext context) {
    return GestureDetector(
      onTap: () => _handleRepost(),
      child: Icon(
        _isReposted ? Icons.repeat : Icons.repeat_outlined,
        color: _isReposted
            ? Theme.of(context).colorScheme.primary
            : Theme.of(context).iconTheme.color,
        size: 26,
      ),
    );
  }

  Widget _buildBookmarkButton(BuildContext context) {
    return GestureDetector(
      onTap: () => _handleBookmark(),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        child: Icon(
          _isBookmarked ? Icons.bookmark : Icons.bookmark_border,
          color: _isBookmarked
              ? Theme.of(context).colorScheme.secondary
              : Theme.of(context).iconTheme.color,
          size: 26,
        ),
      ),
    );
  }

  Widget _buildPostDetails(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildLikesSection(context),
          const SizedBox(height: 8),
          _buildCaptionSection(context),
          const SizedBox(height: 8),
          _buildTimestamp(context),
        ],
      ),
    );
  }

  Widget _buildLikesSection(BuildContext context) {
    return GestureDetector(
      onTap: () => _showLikersList(),
      child: Text(
        '${widget.post.likeCount} likes',
        style: Theme.of(
          context,
        ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
      ),
    );
  }

  Widget _buildCaptionSection(BuildContext context) {
    if (widget.post.caption.isEmpty) return const SizedBox.shrink();

    final caption = widget.post.caption;
    final shouldShowSeeMore = caption.length > 100 && !_isCaptionExpanded;
    final displayCaption = shouldShowSeeMore
        ? '${caption.substring(0, math.min(100, caption.length))}...'
        : caption;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AnimatedSize(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
          child: RichText(
            text: TextSpan(
              style: Theme.of(context).textTheme.bodyMedium,
              children: [..._buildCaptionSpans(context, displayCaption)],
            ),
          ),
        ),
        if (shouldShowSeeMore)
          GestureDetector(
            onTap: () {
              setState(() {
                _isCaptionExpanded = true;
              });
              _captionController.forward();
            },
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              child: Text(
                'see more',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.secondary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildTimestamp(BuildContext context) {
    return Text(
      _getTimeAgo(widget.post.timestamp),
      style: Theme.of(context).textTheme.bodySmall?.copyWith(
        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
      ),
    );
  }

  List<InlineSpan> _buildCaptionSpans(BuildContext context, String caption) {
    if (caption.isEmpty) return [const TextSpan(text: '')];

    final regex = RegExp(r'(#[\w]+|@[\w]+)');
    final matches = regex.allMatches(caption);
    int lastEnd = 0;
    final spans = <InlineSpan>[];

    for (final match in matches) {
      // Ensure indices are within bounds
      if (match.start > lastEnd && match.start <= caption.length) {
        final startIndex = math.max(0, lastEnd);
        final endIndex = math.min(match.start, caption.length);
        if (startIndex < endIndex) {
          spans.add(TextSpan(text: caption.substring(startIndex, endIndex)));
        }
      }

      final text = match.group(0);
      if (text != null && text.isNotEmpty) {
        if (text.startsWith('#') && text.length > 1) {
          spans.add(
            WidgetSpan(
              child: GestureDetector(
                onTap: () => _navigateToHashtag(text.substring(1)),
                child: Text(
                  text,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          );
        } else if (text.startsWith('@') && text.length > 1) {
          spans.add(
            WidgetSpan(
              child: GestureDetector(
                onTap: () => _navigateToMention(text.substring(1)),
                child: Text(
                  text,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          );
        }
      }
      lastEnd = math.min(match.end, caption.length);
    }

    // Add remaining text if any
    if (lastEnd < caption.length) {
      final remainingText = caption.substring(lastEnd);
      if (remainingText.isNotEmpty) {
        spans.add(TextSpan(text: remainingText));
      }
    }

    // Ensure we always return at least one span
    if (spans.isEmpty) {
      spans.add(TextSpan(text: caption));
    }

    return spans;
  }

  // Interaction Handlers
  Future<void> _handleLike() async {
    // Prevent rapid tapping (debouncing)
    if (_isLiking) {
      debugPrint('🔄 Like operation already in progress, ignoring tap');
      return;
    }

    // Check authentication
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) {
      debugPrint('❌ Like failed: No authenticated user');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please log in to like posts')),
        );
      }
      return;
    }

    setState(() => _isLiking = true);

    // Store original state for potential revert
    final originalLikeState = _isLiked;

    // Optimistically update UI
    setState(() {
      _isLiked = !_isLiked;
    });

    // Micro-animation for like button
    _scaleController.forward().then((_) {
      _scaleController.reverse();
    });

    debugPrint(
      '👍 Like button tapped: ${_isLiked ? "Liked" : "Unliked"} post ${widget.post.id}',
    );

    // Try to update backend using Universal Social Interaction Service
    try {
      // Use the PostActionService for like/unlike
      await _actionService.toggleLike(widget.post.id);
      debugPrint('✅ Like action successful: $_isLiked');
    } catch (e) {
      debugPrint('❌ Like action failed: $e');

      // Revert to original state on error
      if (mounted) {
        setState(() {
          _isLiked = originalLikeState;
        });

        // Show user-friendly error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to ${!originalLikeState ? 'like' : 'unlike'} post',
            ),
            action: SnackBarAction(
              label: 'Retry',
              onPressed: () => _handleLike(),
            ),
          ),
        );
      }
    } finally {
      // Always reset the liking state
      if (mounted) {
        setState(() => _isLiking = false);
      }
    }
  }

  Future<void> _handleBookmark() async {
    // Prevent rapid tapping (debouncing)
    if (_isBookmarking) {
      debugPrint('🔄 Bookmark operation already in progress, ignoring tap');
      return;
    }

    setState(() {
      _isBookmarking = true;
      _isBookmarked = !_isBookmarked;
    });

    // Micro-animation for bookmark
    _scaleController.forward().then((_) {
      _scaleController.reverse();
    });

    try {
      await ref.read(feedProvider.notifier).toggleBookmark(widget.post.id);
    } catch (e) {
      // Revert on error
      setState(() {
        _isBookmarked = !_isBookmarked;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to ${_isBookmarked ? 'bookmark' : 'unbookmark'} post: $e',
            ),
          ),
        );
      }
    } finally {
      // Always reset the bookmarking state
      if (mounted) {
        setState(() => _isBookmarking = false);
      }
    }
  }

  void _handleRepost() async {
    if (_postingRepost) return;

    // Show repost options dialog
    final shouldRepost = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Repost'),
        content: const Text('Share this post to your profile?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Repost'),
          ),
        ],
      ),
    );

    if (shouldRepost != true) return;

    setState(() => _postingRepost = true);
    try {
      // Optimistic UI update
      setState(() => _isReposted = true);

      // Create repost using feed service
      final feedService = getIt<FeedService>();
      await feedService.repost(widget.post.id);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Post reposted successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      // Revert optimistic update on error
      setState(() => _isReposted = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to repost: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _postingRepost = false);
    }
  }

  void _handleShare() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => SharePostSheet(
        post: widget.post,
        onRepost: () {
          setState(() => _isReposted = true);
        },
      ),
    );
  }

  void _handleDoubleTap() {
    if (!_isLiked) {
      _handleLike();
    }
  }

  // Navigation Handlers - Universal Logic
  void _navigateToProfile(String userId) {
    debugPrint(
      '🔍 Universal Navigation: Navigating to profile for userId=$userId',
    );

    // Use Universal Navigation Service for consistent routing
    UniversalNavigationService.navigateToProfile(context, userId);
  }

  void _navigateToLocation() {
    if (widget.post.location != null) {
      // Navigate to location - placeholder
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Location: ${widget.post.location}')),
      );
    }
  }

  void _navigateToPhotoView() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PhotoViewScreen(
          imageUrl: widget.post.mediaUrl,
          heroTag: 'post_image_${widget.post.id}',
        ),
      ),
    );
  }

  void _handleMediaTap(int index) {
    // Check if it's a video and navigate to video reels
    final hasMultipleMedia =
        widget.post.mediaUrls != null &&
        widget.post.mediaUrls!.isNotEmpty &&
        widget.post.mediaTypes != null &&
        widget.post.mediaTypes!.isNotEmpty;

    if (hasMultipleMedia) {
      final mediaType = widget.post.mediaTypes![index];
      final mediaUrl = widget.post.mediaUrls![index];

      if (mediaType == MediaType.video) {
        // Navigate to video reels screen
        _navigateToVideoReels(index);
      } else {
        // Navigate to photo view
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => PhotoViewScreen(
              imageUrl: mediaUrl,
              heroTag: 'post_media_${widget.post.id}_$index',
            ),
          ),
        );
      }
    } else {
      // Single media - check if it's video
      if (widget.post.mediaType == MediaType.video) {
        _navigateToVideoReels(0);
      } else {
        _navigateToPhotoView();
      }
    }
  }

  void _navigateToVideoReels(int initialIndex) {
    // Get all video posts from the feed for reels experience
    final feedState = ref.read(feedProvider);
    feedState.whenData((posts) {
      final videoPosts = posts
          .where(
            (post) =>
                post.mediaType == MediaType.video ||
                (post.mediaTypes != null &&
                    post.mediaTypes!.contains(MediaType.video)),
          )
          .toList();

      if (videoPosts.isNotEmpty) {
        // Find the index of current post in video posts
        final currentPostIndex = videoPosts.indexWhere(
          (post) => post.id == widget.post.id,
        );

        final startIndex = currentPostIndex >= 0 ? currentPostIndex : 0;

        // Navigate to video reels screen
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => VideoReelsScreen(
              videoPosts: videoPosts,
              initialIndex: startIndex,
            ),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('No video posts available')),
        );
      }
    });
  }

  void _navigateToHashtag(String hashtag) {
    // Navigate to hashtag feed
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => HashtagFeedScreen(hashtag: hashtag),
      ),
    );
  }

  void _navigateToMention(String username) {
    // Navigate to user profile by username
    _navigateToProfileByUsername(username);
  }

  void _navigateToProfileByUsername(String username) async {
    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      // Find user by username
      final userQuery = await FirebaseFirestore.instance
          .collection('users')
          .where('username', isEqualTo: username)
          .limit(1)
          .get();

      if (mounted) {
        Navigator.pop(context); // Close loading dialog

        if (userQuery.docs.isNotEmpty) {
          final userId = userQuery.docs.first.id;
          UniversalNavigationService.navigateToProfile(context, userId);
        } else {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('User @$username not found')));
        }
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // Close loading dialog
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error finding user: $e')));
      }
    }
  }

  // Modal Handlers
  void _showComments() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CommentBottomSheet(
        postId: widget.post.id,
        postOwnerId: widget.post.userId,
      ),
    );
  }

  void _showLikersList() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => LikersListModal(postId: widget.post.id),
    );
  }

  void _handleReport() async {
    if (_reporting || !_canReport) return;

    // Show report dialog to get reason
    final reason = await _showReportDialog();
    if (reason == null) return;

    setState(() => _reporting = true);
    try {
      // Use FeedService for reporting (it has the reportPost method)
      final feedService = getIt<FeedService>();
      await feedService.reportPost(widget.post.id, reason);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Post reported for: $reason'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to report: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _reporting = false);
    }
  }

  Future<String?> _showReportDialog() async {
    final reportReasons = [
      'Spam',
      'Inappropriate content',
      'Harassment or bullying',
      'False information',
      'Hate speech',
      'Violence or dangerous content',
      'Copyright violation',
      'Other',
    ];

    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Report Post'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Why are you reporting this post?'),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: ListView.builder(
                itemCount: reportReasons.length,
                itemBuilder: (context, index) {
                  return ListTile(
                    title: Text(reportReasons[index]),
                    onTap: () => Navigator.pop(context, reportReasons[index]),
                  );
                },
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  // Utility Methods
  String _getTimeAgo(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'now';
    }
  }

  // Post Management Actions
  void _editPost(BuildContext context) {
    debugPrint('Navigating to edit screen for post ${widget.post.id}');

    // Navigate to edit post screen using MaterialPageRoute
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (context) => EditPostScreen(post: widget.post),
          ),
        )
        .then((result) async {
          debugPrint('Returned from edit screen: $result');

          // Refresh the feed if the post was successfully edited
          if (result == true && mounted) {
            // Force a more aggressive refresh
            try {
              await ref.read(feedProvider.notifier).refresh();
              debugPrint('Post edited successfully, all providers refreshed');
            } catch (e) {
              debugPrint('Post edit refresh failed: $e');
            }
          }
        })
        .catchError((error) {
          debugPrint('Edit navigation error: $error');
        });
  }

  Future<void> _deletePost(BuildContext context) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    // Don't pop the context here - let the PostActionsSheet handle it
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Post'),
        content: const Text(
          'Are you sure you want to delete this post? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _actionService.deletePost(widget.post.id);

        // Remove post from feed immediately for better UX
        ref.read(feedProvider.notifier).removePost(widget.post.id);

        // Also remove from paginated feed provider
        ref.read(paginatedFeedProvider.notifier).removePost(widget.post.id);

        // Refresh feed providers after deletion
        try {
          await ref.read(feedProvider.notifier).refresh();
          await ref.read(paginatedFeedProvider.notifier).refresh();
          debugPrint('✅ POST CARD DELETION: All feed providers refreshed');
        } catch (e) {
          debugPrint('❌ POST CARD DELETION: Feed refresh failed: $e');
        }

        if (mounted) {
          scaffoldMessenger.showSnackBar(
            const SnackBar(content: Text('Post deleted successfully')),
          );
        }
      } catch (e) {
        if (mounted) {
          scaffoldMessenger.showSnackBar(
            SnackBar(content: Text('Failed to delete post: $e')),
          );
        }
      }
    }
  }

  Future<void> _archivePost(BuildContext context) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    // Don't pop the context here - let the PostActionsSheet handle it

    try {
      // Use PostManagementService for archiving
      if (widget.post.isArchived) {
        await _managementService.unarchivePost(widget.post.id);
      } else {
        await _managementService.archivePost(widget.post.id);
      }

      // Remove post from feed
      ref.read(feedProvider.notifier).removePost(widget.post.id);

      if (mounted) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(
              widget.post.isArchived ? 'Post unarchived' : 'Post archived',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('Failed to archive post: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _togglePinPost(BuildContext context) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    Navigator.pop(context);

    try {
      // Pin/unpin functionality using PostManagementService
      if (widget.post.isPinned) {
        await _managementService.unpinPost(widget.post.id);
        if (mounted) {
          scaffoldMessenger.showSnackBar(
            const SnackBar(
              content: Text('Post unpinned from profile'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        await _managementService.pinPost(widget.post.id);
        if (mounted) {
          scaffoldMessenger.showSnackBar(
            const SnackBar(
              content: Text('Post pinned to profile'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(
              'Failed to ${widget.post.isPinned ? 'unpin' : 'pin'} post: $e',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
