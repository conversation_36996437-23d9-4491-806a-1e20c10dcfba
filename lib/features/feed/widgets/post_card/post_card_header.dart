import 'package:flutter/material.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/core/widgets/billionaire_badge.dart';
import 'package:billionaires_social/features/stories/widgets/story_aware_profile_image.dart';
import 'package:billionaires_social/core/instagram_theme.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

/// Post card header component containing user info and post metadata
class PostCardHeader extends StatelessWidget {
  final Post post;
  final VoidCallback? onProfileTap;
  final VoidCallback? onMoreTap;
  final VoidCallback? onLocationTap;

  const PostCardHeader({
    super.key,
    required this.post,
    this.onProfileTap,
    this.onMoreTap,
    this.onLocationTap,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: InstagramTheme.spacingL,
        vertical: InstagramTheme.spacingM,
      ),
      child: Row(
        children: [
          // Profile Picture(s) - Support co-authors
          _buildProfilePictures(),
          const SizedBox(width: InstagramTheme.spacingM),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildUserInfo(context),
                if (post.location != null) _buildLocationInfo(context),
              ],
            ),
          ),
          _buildMoreButton(),
        ],
      ),
    );
  }

  Widget _buildProfilePictures() {
    final avatarUrls = [
      post.userAvatarUrl,
      // Add co-author avatars if available
      if (post.coAuthorAvatars != null) ...post.coAuthorAvatars!.take(2),
    ];

    if (avatarUrls.length == 1) {
      return Stack(
        children: [
          StoryAwareProfileImage(
            userId: post.userId,
            profileImageUrl: avatarUrls[0],
            size: 48,
            onTap: onProfileTap,
          ),
          if (post.isVerified == true)
            Positioned(bottom: 0, right: 0, child: BillionaireBadge(size: 16)),
        ],
      );
    }

    // Multiple profile pictures for co-authored posts
    return SizedBox(
      width: 48,
      height: 48,
      child: Stack(
        children: avatarUrls.asMap().entries.map((entry) {
          final index = entry.key;
          final url = entry.value;
          return Positioned(
            left: index * 20.0,
            child: Container(
              margin: EdgeInsets.only(
                right: index < avatarUrls.length - 1 ? -8 : 0,
              ),
              child: StoryAwareProfileImage(
                userId: index == 0 ? post.userId : post.coAuthorIds![index - 1],
                profileImageUrl: url,
                size: 32,
                onTap: onProfileTap,
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildUserInfo(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Text(
            post.username,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        if (post.isVerified == true)
          const Padding(
            padding: EdgeInsets.only(left: 4.0),
            child: Icon(Icons.verified, color: Colors.blue, size: 16),
          ),
        if (post.isPinned)
          Padding(
            padding: const EdgeInsets.only(right: 4.0),
            child: Icon(
              FontAwesomeIcons.fire,
              color: Theme.of(context).colorScheme.secondary,
              size: 18,
              shadows: [
                Shadow(
                  color: Colors.orange.withValues(alpha: 0.3),
                  blurRadius: 4,
                ),
              ],
            ),
          ),
        if (post.isVerified == true) const BillionaireBadge(size: 16),
      ],
    );
  }

  Widget _buildLocationInfo(BuildContext context) {
    return GestureDetector(
      onTap: onLocationTap,
      child: Padding(
        padding: const EdgeInsets.only(top: 2.0),
        child: Text(
          post.location!,
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: Colors.black87),
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }

  Widget _buildMoreButton() {
    return GestureDetector(
      onTap: onMoreTap,
      child: const Padding(
        padding: EdgeInsets.all(8.0),
        child: Icon(Icons.more_vert, color: Colors.black, size: 24),
      ),
    );
  }
}
