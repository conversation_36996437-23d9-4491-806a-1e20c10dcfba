import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/core/instagram_theme.dart';

/// Post card actions component for like, comment, share, bookmark
class PostCardActions extends StatelessWidget {
  final Post post;
  final VoidCallback? onLikeTap;
  final VoidCallback? onCommentTap;
  final VoidCallback? onShareTap;
  final VoidCallback? onBookmarkTap;
  final VoidCallback? onLikersListTap;

  const PostCardActions({
    super.key,
    required this.post,
    this.onLikeTap,
    this.onCommentTap,
    this.onShareTap,
    this.onBookmarkTap,
    this.onLikersListTap,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: InstagramTheme.spacingL,
        vertical: InstagramTheme.spacingS,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildActionButtons(),
          const SizedBox(height: InstagramTheme.spacingS),
          _buildEngagementStats(context),
          const SizedBox(height: InstagramTheme.spacingS),
          _buildTimeStamp(context),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        _buildLikeButton(),
        const SizedBox(width: InstagramTheme.spacingL),
        _buildCommentButton(),
        const SizedBox(width: InstagramTheme.spacingL),
        _buildShareButton(),
        const Spacer(),
        _buildBookmarkButton(),
      ],
    );
  }

  Widget _buildLikeButton() {
    return GestureDetector(
      onTap: onLikeTap,
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 200),
        child: Icon(
          post.isLiked ? Icons.favorite : Icons.favorite_border,
          key: ValueKey(post.isLiked),
          color: post.isLiked ? Colors.red : Colors.black,
          size: 28,
        ),
      ),
    );
  }

  Widget _buildCommentButton() {
    return GestureDetector(
      onTap: onCommentTap,
      child: const Icon(
        Icons.chat_bubble_outline,
        color: Colors.black,
        size: 28,
      ),
    );
  }

  Widget _buildShareButton() {
    return GestureDetector(
      onTap: onShareTap,
      child: const FaIcon(
        FontAwesomeIcons.paperPlane,
        color: Colors.black,
        size: 24,
      ),
    );
  }

  Widget _buildBookmarkButton() {
    return GestureDetector(
      onTap: onBookmarkTap,
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 200),
        child: Icon(
          post.isBookmarked ? Icons.bookmark : Icons.bookmark_border,
          key: ValueKey(post.isBookmarked),
          color: Colors.black,
          size: 28,
        ),
      ),
    );
  }

  Widget _buildEngagementStats(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (post.likeCount > 0) _buildLikesCount(context),
        if (post.commentCount > 0) _buildCommentsCount(context),
      ],
    );
  }

  Widget _buildLikesCount(BuildContext context) {
    return GestureDetector(
      onTap: onLikersListTap,
      child: RichText(
        text: TextSpan(
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(color: Colors.black),
          children: [
            TextSpan(
              text: _formatCount(post.likeCount),
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
            TextSpan(text: post.likeCount == 1 ? ' like' : ' likes'),
          ],
        ),
      ),
    );
  }

  Widget _buildCommentsCount(BuildContext context) {
    return GestureDetector(
      onTap: onCommentTap,
      child: Padding(
        padding: const EdgeInsets.only(top: 4.0),
        child: Text(
          'View all ${_formatCount(post.commentCount)} comments',
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
        ),
      ),
    );
  }

  Widget _buildTimeStamp(BuildContext context) {
    return Text(
      _formatTimeAgo(post.timestamp),
      style: Theme.of(
        context,
      ).textTheme.bodySmall?.copyWith(color: Colors.grey[600], fontSize: 11),
    );
  }

  String _formatCount(int count) {
    if (count < 1000) {
      return count.toString();
    } else if (count < 1000000) {
      return '${(count / 1000).toStringAsFixed(1)}K';
    } else {
      return '${(count / 1000000).toStringAsFixed(1)}M';
    }
  }

  String _formatTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 7) {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m';
    } else {
      return 'now';
    }
  }
}
