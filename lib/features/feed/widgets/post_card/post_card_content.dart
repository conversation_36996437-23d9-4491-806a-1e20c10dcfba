import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/core/instagram_theme.dart';

/// Post card content component for displaying media and text
class PostCardContent extends StatelessWidget {
  final Post post;
  final VoidCallback? onMediaTap;
  final VoidCallback? onDoubleTap;

  const PostCardContent({
    super.key,
    required this.post,
    this.onMediaTap,
    this.onDoubleTap,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Media content
        if (post.mediaUrls?.isNotEmpty == true) _buildMediaContent(context),

        // Text content
        if (post.caption.isNotEmpty) _buildTextContent(context),
      ],
    );
  }

  Widget _buildMediaContent(BuildContext context) {
    if (post.mediaType == MediaType.image &&
        post.mediaUrls?.isNotEmpty == true) {
      return _buildImageContent(context);
    } else if (post.mediaType == MediaType.video) {
      return _buildVideoContent(context);
    }
    return const SizedBox.shrink();
  }

  Widget _buildImageContent(BuildContext context) {
    final imageUrls = post.mediaUrls ?? [post.mediaUrl];
    if (imageUrls.length == 1) {
      return _buildSingleImage(context, imageUrls.first);
    } else {
      return _buildMultipleImages(context, imageUrls);
    }
  }

  Widget _buildSingleImage(BuildContext context, String imageUrl) {
    return GestureDetector(
      onTap: onMediaTap,
      onDoubleTap: onDoubleTap,
      child: Hero(
        tag: 'post_image_${post.id}',
        child: Container(
          width: double.infinity,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.6,
            minHeight: 200,
          ),
          child: CachedNetworkImage(
            imageUrl: imageUrl,
            fit: BoxFit.contain, // Keep aspect ratio
            placeholder: (context, url) => Container(
              color: Colors.grey[300],
              child: const Center(child: CircularProgressIndicator()),
            ),
            errorWidget: (context, url, error) => Container(
              color: Colors.grey[300],
              child: const Center(
                child: Icon(Icons.error, size: 64, color: Colors.grey),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMultipleImages(BuildContext context, List<String> imageUrls) {
    return SizedBox(
      height: MediaQuery.of(context).size.height * 0.6,
      child: PageView.builder(
        itemCount: imageUrls.length,
        itemBuilder: (context, index) {
          return GestureDetector(
            onTap: () => onMediaTap?.call(),
            onDoubleTap: onDoubleTap,
            child: Hero(
              tag: 'post_media_${post.id}_$index',
              child: CachedNetworkImage(
                imageUrl: imageUrls[index],
                fit: BoxFit.contain, // Keep aspect ratio
                placeholder: (context, url) => Container(
                  color: Colors.grey[300],
                  child: const Center(child: CircularProgressIndicator()),
                ),
                errorWidget: (context, url, error) => Container(
                  color: Colors.grey[300],
                  child: const Center(
                    child: Icon(Icons.error, size: 64, color: Colors.grey),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildVideoContent(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 300,
      color: Colors.black,
      child: Stack(
        alignment: Alignment.center,
        children: [
          CachedNetworkImage(
            imageUrl: '${post.mediaUrl}_thumbnail',
            fit: BoxFit.cover,
            width: double.infinity,
            height: double.infinity,
            errorWidget: (context, url, error) => Container(
              color: Colors.grey[800],
              child: const Center(
                child: Icon(Icons.video_library, size: 64, color: Colors.white),
              ),
            ),
          ),
          GestureDetector(
            onTap: onMediaTap,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.6),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.play_arrow,
                color: Colors.white,
                size: 48,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextContent(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: InstagramTheme.spacingL,
        vertical: InstagramTheme.spacingM,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCaptionText(context),
          if (post.hashtags?.isNotEmpty == true) _buildHashtags(context),
        ],
      ),
    );
  }

  Widget _buildCaptionText(BuildContext context) {
    return RichText(
      text: TextSpan(
        style: Theme.of(
          context,
        ).textTheme.bodyMedium?.copyWith(color: Colors.black),
        children: _parseContentWithMentions(post.caption),
      ),
    );
  }

  List<TextSpan> _parseContentWithMentions(String content) {
    final List<TextSpan> spans = [];
    final RegExp mentionRegex = RegExp(r'@(\w+)');
    // final RegExp hashtagRegex = RegExp(r'#(\w+)'); // For future hashtag parsing

    int lastIndex = 0;

    for (final match in mentionRegex.allMatches(content)) {
      // Add text before mention
      if (match.start > lastIndex) {
        spans.add(TextSpan(text: content.substring(lastIndex, match.start)));
      }

      // Add mention
      spans.add(
        TextSpan(
          text: match.group(0),
          style: const TextStyle(
            color: Colors.blue,
            fontWeight: FontWeight.w600,
          ),
        ),
      );

      lastIndex = match.end;
    }

    // Add remaining text
    if (lastIndex < content.length) {
      spans.add(TextSpan(text: content.substring(lastIndex)));
    }

    return spans;
  }

  Widget _buildHashtags(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 8.0),
      child: Wrap(
        spacing: 8.0,
        runSpacing: 4.0,
        children:
            post.hashtags?.map((hashtag) {
              return GestureDetector(
                onTap: () {
                  // Navigate to hashtag feed
                },
                child: Text(
                  '#$hashtag',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.blue,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              );
            }).toList() ??
            [],
      ),
    );
  }
}
