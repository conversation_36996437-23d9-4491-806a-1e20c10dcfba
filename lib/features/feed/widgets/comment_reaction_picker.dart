import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/feed/services/comment_reactions_service.dart';

class CommentReactionPicker extends ConsumerStatefulWidget {
  final String postId;
  final String commentId;
  final VoidCallback? onReactionAdded;
  final Offset? position;

  const CommentReactionPicker({
    super.key,
    required this.postId,
    required this.commentId,
    this.onReactionAdded,
    this.position,
  });

  @override
  ConsumerState<CommentReactionPicker> createState() =>
      _CommentReactionPickerState();
}

class _CommentReactionPickerState extends ConsumerState<CommentReactionPicker>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.elasticOut),
    );

    _opacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _addReaction(String emoji) async {
    try {
      final reactionsService = ref.read(commentReactionsServiceProvider);
      await reactionsService.addReaction(
        postId: widget.postId,
        commentId: widget.commentId,
        emoji: emoji,
      );

      widget.onReactionAdded?.call();

      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to add reaction: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final reactionsService = ref.read(commentReactionsServiceProvider);
    final emojis = reactionsService.getPopularEmojis();

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _opacityAnimation.value,
            child: Material(
              color: Colors.transparent,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(25),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.2),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: emojis.map((emoji) {
                    return _buildEmojiButton(emoji, reactionsService);
                  }).toList(),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildEmojiButton(
    String emoji,
    CommentReactionsService reactionsService,
  ) {
    return GestureDetector(
      onTap: () => _addReaction(emoji),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 2),
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          color: Colors.transparent,
        ),
        child: Text(
          emoji,
          style: const TextStyle(fontSize: 24),
          semanticsLabel: reactionsService.getEmojiName(emoji),
        ),
      ),
    );
  }
}

class CommentReactionDisplay extends ConsumerStatefulWidget {
  final String postId;
  final String commentId;
  final Map<String, String> reactions;
  final Map<String, int> reactionCounts;
  final int totalReactionCount;
  final VoidCallback? onReactionTap;

  const CommentReactionDisplay({
    super.key,
    required this.postId,
    required this.commentId,
    required this.reactions,
    required this.reactionCounts,
    required this.totalReactionCount,
    this.onReactionTap,
  });

  @override
  ConsumerState<CommentReactionDisplay> createState() =>
      _CommentReactionDisplayState();
}

class _CommentReactionDisplayState
    extends ConsumerState<CommentReactionDisplay> {
  Future<void> _removeReaction() async {
    try {
      final reactionsService = ref.read(commentReactionsServiceProvider);
      await reactionsService.removeReaction(
        postId: widget.postId,
        commentId: widget.commentId,
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to remove reaction: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showReactionUsers(String emoji) async {
    try {
      final reactionsService = ref.read(commentReactionsServiceProvider);
      final users = await reactionsService.getReactionUsers(
        postId: widget.postId,
        commentId: widget.commentId,
        emoji: emoji,
      );

      if (mounted && users.isNotEmpty) {
        showModalBottomSheet(
          context: context,
          builder: (context) => _buildReactionUsersSheet(emoji, users),
        );
      }
    } catch (e) {
      debugPrint('Error showing reaction users: $e');
    }
  }

  Widget _buildReactionUsersSheet(
    String emoji,
    List<Map<String, dynamic>> users,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Text(emoji, style: const TextStyle(fontSize: 24)),
              const SizedBox(width: 8),
              Text(
                '${users.length} ${users.length == 1 ? 'person' : 'people'}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Flexible(
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: users.length,
              itemBuilder: (context, index) {
                final user = users[index];
                return ListTile(
                  leading: CircleAvatar(
                    backgroundImage: user['profilePictureUrl'].isNotEmpty
                        ? NetworkImage(user['profilePictureUrl'])
                        : null,
                    child: user['profilePictureUrl'].isEmpty
                        ? const Icon(Icons.person)
                        : null,
                  ),
                  title: Row(
                    children: [
                      Expanded(
                        child: Text(
                          user['name'].isNotEmpty
                              ? user['name']
                              : user['username'],
                          style: const TextStyle(fontWeight: FontWeight.w600),
                        ),
                      ),
                      if (user['isVerified']) ...[
                        const SizedBox(width: 4),
                        const Icon(
                          Icons.verified,
                          color: Colors.blue,
                          size: 16,
                        ),
                      ],
                    ],
                  ),
                  subtitle: Text('@${user['username']}'),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.reactionCounts.isEmpty) {
      return const SizedBox.shrink();
    }

    final reactionsService = ref.read(commentReactionsServiceProvider);
    final userReaction = reactionsService.getUserReaction(
      postId: widget.postId,
      commentId: widget.commentId,
      reactions: widget.reactions,
    );

    return Container(
      margin: const EdgeInsets.only(top: 4),
      child: Wrap(
        spacing: 4,
        runSpacing: 4,
        children: [
          ...widget.reactionCounts.entries.map((entry) {
            final emoji = entry.key;
            final count = entry.value;
            final isUserReaction = userReaction == emoji;

            return GestureDetector(
              onTap: () {
                if (isUserReaction) {
                  _removeReaction();
                } else {
                  _showReactionUsers(emoji);
                }
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: isUserReaction
                      ? Colors.blue.withValues(alpha: 0.1)
                      : Colors.grey.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: isUserReaction
                      ? Border.all(color: Colors.blue, width: 1)
                      : null,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(emoji, style: const TextStyle(fontSize: 14)),
                    if (count > 1) ...[
                      const SizedBox(width: 4),
                      Text(
                        count.toString(),
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: isUserReaction
                              ? Colors.blue
                              : Colors.grey[700],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            );
          }),

          // Add reaction button
          GestureDetector(
            onTap: widget.onReactionTap,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(Icons.add, size: 16, color: Colors.grey),
            ),
          ),
        ],
      ),
    );
  }
}
