import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/feed/providers/feed_provider.dart';
import 'package:billionaires_social/features/profile/models/profile_model.dart';
import 'package:billionaires_social/core/services/universal_navigation_service.dart';
import 'package:billionaires_social/features/profile/services/followers_service.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:billionaires_social/core/widgets/loading_states.dart';

class LikersListModal extends ConsumerWidget {
  final String postId;
  const LikersListModal({super.key, required this.postId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                const Text(
                  'Likes',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
          ),

          const Divider(height: 1),

          // Likers list
          Expanded(
            child: FutureBuilder<List<ProfileModel>>(
              future: _fetchLikers(ref),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Center(
                    child: LoadingStates.primaryLoading(
                      message: 'Loading likes...',
                    ),
                  );
                }

                if (snapshot.hasError) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.error_outline,
                          size: 48,
                          color: Colors.grey,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Failed to load likes',
                          style: TextStyle(color: Colors.grey[600]),
                        ),
                        const SizedBox(height: 8),
                        TextButton(
                          onPressed: () {
                            // Trigger rebuild to retry
                            (context as Element).markNeedsBuild();
                          },
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  );
                }

                final likers = snapshot.data ?? [];

                if (likers.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.favorite_border,
                          size: 48,
                          color: Colors.grey,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'No likes yet',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Be the first to like this post!',
                          style: TextStyle(color: Colors.grey[500]),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  itemCount: likers.length,
                  itemBuilder: (context, index) {
                    final liker = likers[index];
                    return _LikerTile(liker: liker);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Future<List<ProfileModel>> _fetchLikers(WidgetRef ref) async {
    try {
      // Get the post from feed provider
      final feedState = ref.read(feedProvider);
      return feedState.when(
        data: (posts) {
          final post = posts.firstWhere(
            (p) => p.id == postId,
            orElse: () => throw Exception('Post not found'),
          );

          // For now, return mock data based on like count
          // In a real app, this would fetch actual liker profiles from Firestore
          return List.generate(
            post.likeCount.clamp(0, 10), // Limit to 10 for demo
            (index) => ProfileModel(
              id: 'liker_$index',
              name: 'User ${index + 1}',
              username: 'user${index + 1}',
              profilePictureUrl: 'https://picsum.photos/100/100?random=$index',
              bio: 'Sample user bio',
              postCount: (index + 1) * 10,
              followerCount: (index + 1) * 100,
              followingCount: (index + 1) * 50,
              isVerified: index % 3 == 0, // Every 3rd user is verified
              createdAt: DateTime.now().subtract(Duration(days: index)),
            ),
          );
        },
        loading: () => <ProfileModel>[],
        error: (error, stack) => throw error,
      );
    } catch (e) {
      throw Exception('Failed to fetch likers: $e');
    }
  }
}

class _LikerTile extends StatelessWidget {
  final ProfileModel liker;

  const _LikerTile({required this.liker});

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: CircleAvatar(
        radius: 20,
        backgroundImage: CachedNetworkImageProvider(liker.profilePictureUrl),
        onBackgroundImageError: (exception, stackTrace) {
          debugPrint('Error loading profile image: $exception');
        },
      ),
      title: Row(
        children: [
          Flexible(
            child: Text(
              liker.name,
              style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 14),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (liker.isVerified) ...[
            const SizedBox(width: 4),
            const Icon(Icons.verified, color: Colors.blue, size: 16),
          ],
        ],
      ),
      subtitle: Text(
        '@${liker.username}',
        style: TextStyle(color: Colors.grey[600], fontSize: 13),
      ),
      trailing: _buildFollowButton(),
      onTap: () {
        Navigator.pop(context);
        UniversalNavigationService.navigateToProfile(context, liker.id);
      },
    );
  }

  Widget _buildFollowButton() {
    return FutureBuilder<bool>(
      future: _checkFollowingStatus(),
      builder: (context, snapshot) {
        final isFollowing = snapshot.data ?? false;
        final isLoading = snapshot.connectionState == ConnectionState.waiting;

        return SizedBox(
          width: 80,
          height: 32,
          child: OutlinedButton(
            onPressed: isLoading
                ? null
                : () async {
                    try {
                      final userService = getIt<FollowersService>();
                      if (isFollowing) {
                        await userService.unfollowUser(liker.id);
                      } else {
                        await userService.followUser(liker.id);
                      }
                      // Trigger rebuild to update button state
                      (context as Element).markNeedsBuild();
                    } catch (e) {
                      debugPrint(
                        'Error ${isFollowing ? 'unfollowing' : 'following'} user: $e',
                      );
                    }
                  },
            style: OutlinedButton.styleFrom(
              backgroundColor: isFollowing ? Colors.grey[100] : Colors.blue,
              foregroundColor: isFollowing ? Colors.black : Colors.white,
              side: BorderSide(
                color: isFollowing ? Colors.grey[300]! : Colors.blue,
              ),
              padding: const EdgeInsets.symmetric(horizontal: 12),
            ),
            child: isLoading
                ? const SizedBox(
                    width: 12,
                    height: 12,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : Text(
                    isFollowing ? 'Following' : 'Follow',
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        );
      },
    );
  }

  Future<bool> _checkFollowingStatus() async {
    try {
      final userService = getIt<FollowersService>();
      final currentUserId = FirebaseAuth.instance.currentUser?.uid;
      if (currentUserId == null) return false;

      // Check if current user is following this liker
      final followers = await userService.getFollowing(currentUserId);
      return followers.any((follower) => follower.id == liker.id);
    } catch (e) {
      debugPrint('Error checking follow status: $e');
      return false;
    }
  }
}
