import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/features/feed/widgets/report_post_dialog.dart';

class PostActionsSheet extends ConsumerStatefulWidget {
  final Post post;
  final bool isOwner;
  final VoidCallback? onRepost;
  final VoidCallback? onReport;
  final VoidCallback? onPostDeleted;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onArchive;
  final VoidCallback? onPin;
  final Function(String)? onPrivacyChange;

  const PostActionsSheet({
    super.key,
    required this.post,
    required this.isOwner,
    this.onRepost,
    this.onReport,
    this.onPostDeleted,
    this.onEdit,
    this.onDelete,
    this.onArchive,
    this.onPin,
    this.onPrivacyChange,
  });

  @override
  ConsumerState<PostActionsSheet> createState() => _PostActionsSheetState();
}

class _PostActionsSheetState extends ConsumerState<PostActionsSheet> {
  @override
  Widget build(BuildContext context) {
    // Debug post actions availability (only show issues)
    if (kDebugMode) {
      final postIdShort = widget.post.id.length > 8
          ? widget.post.id.substring(0, 8)
          : widget.post.id;

      // Only log if there are permission issues
      if (widget.isOwner && widget.onDelete == null) {
        debugPrint(
          '⚠️ PostActionsSheet $postIdShort: DELETE OPTION MISSING for owner',
        );
      }

      // Log available actions count for debugging
      final availableActions = [
        widget.onEdit,
        widget.onDelete,
        widget.onArchive,
        widget.onPin,
      ].where((action) => action != null).length;

      if (availableActions == 0 && widget.isOwner) {
        debugPrint(
          '⚠️ PostActionsSheet $postIdShort: No actions available for owner',
        );
      }
    }

    return DraggableScrollableSheet(
      initialChildSize: 0.5,
      minChildSize: 0.3,
      maxChildSize: 0.9,
      expand: false,
      builder: (context, scrollController) {
        return Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: SingleChildScrollView(
            controller: scrollController,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Handle bar
                Container(
                  margin: const EdgeInsets.only(top: 12),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 20),
                // Title
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Row(
                    children: [
                      Text(
                        'Post Options',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(Icons.close),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
                // Owner actions
                if (widget.isOwner) ...[
                  if (widget.onEdit != null)
                    _buildActionTile(
                      context,
                      icon: Icons.edit,
                      title: 'Edit Post',
                      subtitle: 'Modify your post content',
                      onTap: () {
                        Navigator.pop(context);
                        widget.onEdit?.call();
                      },
                    ),
                  if (widget.onArchive != null)
                    _buildActionTile(
                      context,
                      icon: Icons.archive,
                      title: widget.post.isArchived
                          ? 'Unarchive Post'
                          : 'Archive Post',
                      subtitle: widget.post.isArchived
                          ? 'Make post visible again'
                          : 'Hide post from profile',
                      onTap: () {
                        Navigator.pop(context);
                        widget.onArchive?.call();
                      },
                    ),
                  if (widget.onPin != null)
                    _buildActionTile(
                      context,
                      icon: widget.post.isPinned
                          ? Icons.push_pin
                          : Icons.push_pin_outlined,
                      title: widget.post.isPinned
                          ? 'Unpin Post'
                          : 'Pin to Profile',
                      subtitle: widget.post.isPinned
                          ? 'Remove from pinned posts'
                          : 'Pin this post to the top of your profile',
                      onTap: () {
                        Navigator.pop(context);
                        widget.onPin?.call();
                      },
                    ),
                  _buildPrivacySection(context),
                  const Divider(height: 1),
                  if (widget.onDelete != null)
                    _buildActionTile(
                      context,
                      icon: Icons.delete,
                      title: 'Delete Post',
                      subtitle: 'Permanently remove this post',
                      isDestructive: true,
                      onTap: () {
                        Navigator.pop(context);
                        widget.onDelete?.call();
                      },
                    ),
                ] else ...[
                  // Visitor actions
                  _buildActionTile(
                    context,
                    icon: Icons.report,
                    title: 'Report Post',
                    subtitle: 'Report inappropriate content',
                    onTap: () {
                      Navigator.pop(context);
                      showDialog(
                        context: context,
                        builder: (context) => ReportPostDialog(
                          postId: widget.post.id,
                          postAuthorName: widget.post.username,
                        ),
                      );
                    },
                  ),
                  _buildActionTile(
                    context,
                    icon: Icons.block,
                    title: 'Block User',
                    subtitle: 'Block this user',
                    onTap: () {
                      Navigator.pop(context);
                      // TODO: Implement block functionality
                    },
                  ),
                ],
                const SizedBox(height: 20),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildActionTile(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    bool isDestructive = false,
    VoidCallback? onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: isDestructive ? Colors.red : Colors.grey[700]),
      title: Text(
        title,
        style: TextStyle(
          color: isDestructive ? Colors.red : Colors.black,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(color: Colors.grey[600], fontSize: 12),
      ),
      onTap: onTap,
    );
  }

  Widget _buildPrivacySection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
          child: Text(
            'Privacy',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: Colors.grey[700],
            ),
          ),
        ),
        _buildPrivacyOption(
          context,
          value: 'public',
          title: 'Everyone',
          subtitle: 'Visible to everyone',
          icon: Icons.public,
        ),
        _buildPrivacyOption(
          context,
          value: 'closeFriends',
          title: 'Close Friends',
          subtitle: 'Visible to close friends only',
          icon: Icons.people,
        ),
        _buildPrivacyOption(
          context,
          value: 'private',
          title: 'Only Me',
          subtitle: 'Visible to you only',
          icon: Icons.lock,
        ),
      ],
    );
  }

  Widget _buildPrivacyOption(
    BuildContext context, {
    required String value,
    required String title,
    required String subtitle,
    required IconData icon,
  }) {
    final isSelected = widget.post.visibility == value;
    return ListTile(
      leading: Icon(
        icon,
        color: isSelected ? Theme.of(context).primaryColor : Colors.grey[600],
      ),
      title: Text(
        title,
        style: TextStyle(
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
          color: isSelected ? Theme.of(context).primaryColor : Colors.black,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(color: Colors.grey[600], fontSize: 12),
      ),
      trailing: isSelected
          ? Icon(Icons.check_circle, color: Theme.of(context).primaryColor)
          : null,
      onTap: () {
        Navigator.pop(context);
        widget.onPrivacyChange?.call(value);
      },
    );
  }
}
