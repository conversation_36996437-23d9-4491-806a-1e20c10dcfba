import 'package:billionaires_social/core/app_theme.dart';
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class PostCardSkeleton extends StatelessWidget {
  const PostCardSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: AppTheme.primaryColor,
      highlightColor: AppTheme.luxuryGrey,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const CircleAvatar(radius: 20),
                const SizedBox(width: 12),
                Container(width: 150, height: 16, color: Colors.white),
                const Spacer(),
                Container(width: 24, height: 24, color: Colors.white),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              height: MediaQuery.of(context).size.width - 56, // Adjust height
              width: double.infinity,
              color: Colors.white,
            ),
            const SizedBox(height: 12),
            Row(
              children: List.generate(
                4,
                (index) => Container(
                  width: 30,
                  height: 30,
                  margin: const EdgeInsets.only(right: 12),
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
