import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/features/feed/providers/feed_filter_provider.dart';
import 'package:billionaires_social/features/feed/providers/feed_provider.dart';

class ContentTypeFilterWidget extends ConsumerWidget {
  const ContentTypeFilterWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentContentType = ref.watch(contentTypeFilterProvider);
    final theme = Theme.of(context);

    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: theme.cardColor,
        border: Border(
          bottom: BorderSide(
            color: theme.dividerColor.withValues(alpha: 0.3),
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.filter_list,
            size: 20,
            color: theme.iconTheme.color?.withValues(alpha: 0.7),
          ),
          const SizedBox(width: 8),
          Text(
            'Content Type:',
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              color: theme.textTheme.bodyMedium?.color?.withValues(alpha: 0.8),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: ContentTypeFilter.values.map((contentType) {
                  final isSelected = currentContentType == contentType;
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: _buildFilterChip(
                      context,
                      ref,
                      contentType,
                      isSelected,
                      theme,
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(
    BuildContext context,
    WidgetRef ref,
    ContentTypeFilter contentType,
    bool isSelected,
    ThemeData theme,
  ) {
    final contentTypeNotifier = ref.read(contentTypeFilterProvider.notifier);
    final options = contentTypeNotifier.getContentTypeOptions();
    final option = options.firstWhere((opt) => opt['type'] == contentType);
    final optionColor = option['color'] as Color;

    return GestureDetector(
      onTap: () => _onContentTypeSelected(ref, contentType),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected
              ? optionColor.withValues(alpha: 0.15)
              : theme.cardColor,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected
                ? optionColor
                : theme.dividerColor.withValues(alpha: 0.3),
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: optionColor.withValues(alpha: 0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Use Material Icon with enhanced styling
            Container(
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: isSelected
                    ? optionColor.withValues(alpha: 0.2)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                option['icon'] as IconData,
                size: 16,
                color: isSelected
                    ? optionColor
                    : theme.iconTheme.color?.withValues(alpha: 0.7),
              ),
            ),
            const SizedBox(width: 8),
            Text(
              option['title'] as String,
              style: theme.textTheme.bodySmall?.copyWith(
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                color: isSelected
                    ? optionColor
                    : theme.textTheme.bodySmall?.color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _onContentTypeSelected(WidgetRef ref, ContentTypeFilter contentType) {
    // Update the content type filter
    ref.read(contentTypeFilterProvider.notifier).setContentType(contentType);

    // Refresh the feed with the new filter
    ref.read(feedProvider.notifier).refresh();

    // Show feedback to user - capture context early to avoid disposal issues
    final context = ref.context;
    final contentTypeNotifier = ref.read(contentTypeFilterProvider.notifier);

    // Check if context is still valid before showing snackbar
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Showing ${contentTypeNotifier.getContentTypeTitle()}'),
          duration: const Duration(seconds: 1),
          backgroundColor: AppTheme.accentColor,
        ),
      );
    }
  }
}

/// Floating action button style content type filter
class FloatingContentTypeFilter extends ConsumerWidget {
  const FloatingContentTypeFilter({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentContentType = ref.watch(contentTypeFilterProvider);
    final contentTypeNotifier = ref.read(contentTypeFilterProvider.notifier);
    final options = contentTypeNotifier.getContentTypeOptions();
    final currentOption = options.firstWhere(
      (opt) => opt['type'] == currentContentType,
    );
    final theme = Theme.of(context);

    return FloatingActionButton.extended(
      onPressed: () => _showContentTypeModal(context, ref),
      backgroundColor: (currentOption['color'] as Color).withValues(alpha: 0.1),
      foregroundColor: currentOption['color'] as Color,
      elevation: 6,
      icon: Icon(currentOption['icon'] as IconData, size: 20),
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            _getShortTitle(currentContentType),
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: currentOption['color'] as Color,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            currentOption['iconEmoji'] as String,
            style: const TextStyle(fontSize: 14),
          ),
        ],
      ),
    );
  }

  String _getShortTitle(ContentTypeFilter contentType) {
    switch (contentType) {
      case ContentTypeFilter.all:
        return 'All';
      case ContentTypeFilter.media:
        return 'Media';
      case ContentTypeFilter.text:
        return 'Text';
    }
  }

  void _showContentTypeModal(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => ContentTypeFilterModal(),
    );
  }
}

/// Modal for selecting content type
class ContentTypeFilterModal extends ConsumerWidget {
  const ContentTypeFilterModal({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentContentType = ref.watch(contentTypeFilterProvider);
    final contentTypeNotifier = ref.read(contentTypeFilterProvider.notifier);
    final options = contentTypeNotifier.getContentTypeOptions();
    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: theme.dividerColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                const Icon(Icons.filter_list, size: 24),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Filter by Content Type',
                        style: theme.textTheme.headlineSmall,
                      ),
                      Text(
                        'Choose what type of posts to show',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.textTheme.bodyMedium?.color?.withValues(
                            alpha: 0.7,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Options
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              children: options.map((option) {
                final contentType = option['type'] as ContentTypeFilter;
                final isSelected = currentContentType == contentType;

                return ListTile(
                  contentPadding: EdgeInsets.zero,
                  leading: Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: isSelected
                          ? (option['color'] as Color).withValues(alpha: 0.15)
                          : theme.cardColor,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected
                            ? (option['color'] as Color)
                            : theme.dividerColor.withValues(alpha: 0.3),
                        width: isSelected ? 2 : 1,
                      ),
                      boxShadow: isSelected
                          ? [
                              BoxShadow(
                                color: (option['color'] as Color).withValues(
                                  alpha: 0.2,
                                ),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ]
                          : null,
                    ),
                    child: Stack(
                      children: [
                        // Background icon
                        Center(
                          child: Icon(
                            option['icon'] as IconData,
                            size: 20,
                            color: isSelected
                                ? (option['color'] as Color)
                                : theme.iconTheme.color?.withValues(alpha: 0.7),
                          ),
                        ),
                        // Emoji overlay for extra visual appeal
                        Positioned(
                          bottom: 2,
                          right: 2,
                          child: Text(
                            option['iconEmoji'] as String,
                            style: const TextStyle(fontSize: 12),
                          ),
                        ),
                      ],
                    ),
                  ),
                  title: Text(
                    option['title'] as String,
                    style: theme.textTheme.bodyLarge?.copyWith(
                      fontWeight: isSelected
                          ? FontWeight.w600
                          : FontWeight.w500,
                    ),
                  ),
                  subtitle: Text(
                    option['description'] as String,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.textTheme.bodyMedium?.color?.withValues(
                        alpha: 0.7,
                      ),
                    ),
                  ),
                  trailing: isSelected
                      ? Icon(Icons.check_circle, color: AppTheme.accentColor)
                      : null,
                  onTap: () {
                    // Set the content type filter
                    ref
                        .read(contentTypeFilterProvider.notifier)
                        .setContentType(contentType);

                    // Refresh the feed with the new filter
                    ref.read(feedProvider.notifier).refresh();

                    // Show success message
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Showing ${option['title']} content'),
                        backgroundColor: AppTheme.accentColor,
                        duration: const Duration(seconds: 2),
                      ),
                    );

                    Navigator.of(context).pop();
                  },
                );
              }).toList(),
            ),
          ),

          // Bottom padding
          const SizedBox(height: 20),
        ],
      ),
    );
  }
}
