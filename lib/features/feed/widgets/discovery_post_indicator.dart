import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:billionaires_social/core/app_theme.dart';

class DiscoveryPostIndicator extends StatelessWidget {
  final String type; // 'suggested', 'trending', 'popular'
  final VoidCallback? onDismiss;
  final bool showFollowButton;
  final VoidCallback? onFollow;

  const DiscoveryPostIndicator({
    super.key,
    required this.type,
    this.onDismiss,
    this.showFollowButton = false,
    this.onFollow,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: _getBackgroundColor(),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _getBorderColor(),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            _getIcon(),
            size: 16,
            color: _getIconColor(),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _getLabel(),
              style: AppTheme.fontStyles.caption.copyWith(
                color: _getTextColor(),
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          if (showFollowButton) ...[
            const SizedBox(width: 8),
            GestureDetector(
              onTap: onFollow,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: AppTheme.accentColor,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  'Follow',
                  style: AppTheme.fontStyles.caption.copyWith(
                    color: Colors.black,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
          if (onDismiss != null) ...[
            const SizedBox(width: 8),
            GestureDetector(
              onTap: onDismiss,
              child: Icon(
                Icons.close,
                size: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Color _getBackgroundColor() {
    switch (type) {
      case 'trending':
        return Colors.orange.withValues(alpha: 0.1);
      case 'popular':
        return Colors.purple.withValues(alpha: 0.1);
      case 'suggested':
      default:
        return Colors.blue.withValues(alpha: 0.1);
    }
  }

  Color _getBorderColor() {
    switch (type) {
      case 'trending':
        return Colors.orange.withValues(alpha: 0.3);
      case 'popular':
        return Colors.purple.withValues(alpha: 0.3);
      case 'suggested':
      default:
        return Colors.blue.withValues(alpha: 0.3);
    }
  }

  IconData _getIcon() {
    switch (type) {
      case 'trending':
        return FontAwesomeIcons.fire;
      case 'popular':
        return FontAwesomeIcons.star;
      case 'suggested':
      default:
        return FontAwesomeIcons.compass;
    }
  }

  Color _getIconColor() {
    switch (type) {
      case 'trending':
        return Colors.orange[700]!;
      case 'popular':
        return Colors.purple[700]!;
      case 'suggested':
      default:
        return Colors.blue[700]!;
    }
  }

  Color _getTextColor() {
    switch (type) {
      case 'trending':
        return Colors.orange[800]!;
      case 'popular':
        return Colors.purple[800]!;
      case 'suggested':
      default:
        return Colors.blue[800]!;
    }
  }

  String _getLabel() {
    switch (type) {
      case 'trending':
        return 'Trending post';
      case 'popular':
        return 'Popular in your area';
      case 'suggested':
      default:
        return 'Suggested for you';
    }
  }
}

/// Enhanced discovery indicator with more context
class EnhancedDiscoveryIndicator extends StatelessWidget {
  final String type;
  final String? username;
  final String? reason; // e.g., "Followed by 3 people you follow"
  final VoidCallback? onDismiss;
  final VoidCallback? onFollow;
  final bool isFollowing;

  const EnhancedDiscoveryIndicator({
    super.key,
    required this.type,
    this.username,
    this.reason,
    this.onDismiss,
    this.onFollow,
    this.isFollowing = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _getBackgroundColor(),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getBorderColor(),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _getIcon(),
                size: 16,
                color: _getIconColor(),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  _getLabel(),
                  style: AppTheme.fontStyles.caption.copyWith(
                    color: _getTextColor(),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              if (onDismiss != null)
                GestureDetector(
                  onTap: onDismiss,
                  child: Icon(
                    Icons.close,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                ),
            ],
          ),
          if (reason != null) ...[
            const SizedBox(height: 4),
            Text(
              reason!,
              style: AppTheme.fontStyles.caption.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
          if (username != null && onFollow != null) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: Text(
                    'From @$username',
                    style: AppTheme.fontStyles.caption.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                if (!isFollowing)
                  GestureDetector(
                    onTap: onFollow,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: AppTheme.accentColor,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(
                        'Follow',
                        style: AppTheme.fontStyles.caption.copyWith(
                          color: Colors.black,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  )
                else
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      'Following',
                      style: AppTheme.fontStyles.caption.copyWith(
                        color: Colors.grey[700],
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Color _getBackgroundColor() {
    switch (type) {
      case 'trending':
        return Colors.orange.withValues(alpha: 0.05);
      case 'popular':
        return Colors.purple.withValues(alpha: 0.05);
      case 'mutual':
        return Colors.green.withValues(alpha: 0.05);
      case 'suggested':
      default:
        return Colors.blue.withValues(alpha: 0.05);
    }
  }

  Color _getBorderColor() {
    switch (type) {
      case 'trending':
        return Colors.orange.withValues(alpha: 0.2);
      case 'popular':
        return Colors.purple.withValues(alpha: 0.2);
      case 'mutual':
        return Colors.green.withValues(alpha: 0.2);
      case 'suggested':
      default:
        return Colors.blue.withValues(alpha: 0.2);
    }
  }

  IconData _getIcon() {
    switch (type) {
      case 'trending':
        return FontAwesomeIcons.fire;
      case 'popular':
        return FontAwesomeIcons.star;
      case 'mutual':
        return FontAwesomeIcons.userGroup;
      case 'suggested':
      default:
        return FontAwesomeIcons.compass;
    }
  }

  Color _getIconColor() {
    switch (type) {
      case 'trending':
        return Colors.orange[600]!;
      case 'popular':
        return Colors.purple[600]!;
      case 'mutual':
        return Colors.green[600]!;
      case 'suggested':
      default:
        return Colors.blue[600]!;
    }
  }

  Color _getTextColor() {
    switch (type) {
      case 'trending':
        return Colors.orange[700]!;
      case 'popular':
        return Colors.purple[700]!;
      case 'mutual':
        return Colors.green[700]!;
      case 'suggested':
      default:
        return Colors.blue[700]!;
    }
  }

  String _getLabel() {
    switch (type) {
      case 'trending':
        return 'Trending now';
      case 'popular':
        return 'Popular in your area';
      case 'mutual':
        return 'Mutual connections';
      case 'suggested':
      default:
        return 'Suggested for you';
    }
  }
}
