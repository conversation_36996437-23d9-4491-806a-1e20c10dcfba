import 'package:flutter/material.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';

class MediaTagOverlay extends StatelessWidget {
  final List<MediaTag> mediaTags;
  final Function(String) onTagTap;

  const MediaTagOverlay({
    super.key,
    required this.mediaTags,
    required this.onTagTap,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: mediaTags.map((tag) {
        return Positioned(
          left: tag.x * MediaQuery.of(context).size.width,
          top: tag.y * (MediaQuery.of(context).size.width * 0.8),
          child: GestureDetector(
            onTap: () => onTagTap(tag.userId),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.person, color: Colors.white, size: 12),
                  const SizedBox(width: 4),
                  Text(
                    tag.username,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }
}
