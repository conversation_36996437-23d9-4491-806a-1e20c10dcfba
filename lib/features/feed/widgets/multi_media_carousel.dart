import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';

/// Multi-media carousel widget for posts with multiple images/videos
class MultiMediaCarousel extends StatefulWidget {
  final List<String> mediaUrls;
  final List<MediaType> mediaTypes;
  final double height;
  final bool showIndicators;
  final bool autoPlay;
  final Function(int)? onPageChanged;
  final Function(int)? onMediaTap;

  const MultiMediaCarousel({
    super.key,
    required this.mediaUrls,
    required this.mediaTypes,
    this.height = 400,
    this.showIndicators = true,
    this.autoPlay = false,
    this.onPageChanged,
    this.onMediaTap,
  });

  @override
  State<MultiMediaCarousel> createState() => _MultiMediaCarouselState();
}

class _MultiMediaCarouselState extends State<MultiMediaCarousel> {
  late PageController _pageController;
  int _currentIndex = 0;
  final Map<int, VideoPlayerController> _videoControllers = {};
  final Map<int, ChewieController> _chewieControllers = {};

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _initializeVideoControllers();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _disposeVideoControllers();
    super.dispose();
  }

  void _initializeVideoControllers() {
    for (int i = 0; i < widget.mediaUrls.length; i++) {
      if (widget.mediaTypes[i] == MediaType.video) {
        _initializeVideoController(i);
      }
    }
  }

  void _initializeVideoController(int index) {
    final videoController = VideoPlayerController.networkUrl(
      Uri.parse(widget.mediaUrls[index]),
    );

    _videoControllers[index] = videoController;

    videoController
        .initialize()
        .then((_) {
          // Double-check: widget might be disposed and controller removed
          if (mounted && _videoControllers.containsKey(index)) {
            final chewieController = ChewieController(
              videoPlayerController: videoController,
              autoPlay: widget.autoPlay && index == 0,
              looping: true,
              showControls: true,
              showControlsOnInitialize: false,
              aspectRatio: videoController.value.aspectRatio,
              placeholder: Container(
                color: Colors.black,
                child: const Center(
                  child: CircularProgressIndicator(color: Colors.white),
                ),
              ),
              errorBuilder: (context, errorMessage) {
                return Container(
                  color: Colors.black,
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.error, color: Colors.white, size: 48),
                        const SizedBox(height: 8),
                        Text(
                          'Video unavailable',
                          style: const TextStyle(color: Colors.white),
                        ),
                      ],
                    ),
                  ),
                );
              },
            );

            _chewieControllers[index] = chewieController;
            setState(() {});
          }
        })
        .catchError((error) {
          debugPrint('❌ Error initializing video controller: $error');
        });
  }

  void _disposeVideoControllers() {
    for (final controller in _chewieControllers.values) {
      controller.dispose();
    }
    for (final controller in _videoControllers.values) {
      controller.dispose();
    }
    _chewieControllers.clear();
    _videoControllers.clear();
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });

    // Pause all videos except current one
    for (int i = 0; i < _videoControllers.length; i++) {
      if (i != index && _videoControllers[i] != null) {
        _videoControllers[i]!.pause();
      }
    }

    // Auto-play current video if it's a video
    if (widget.mediaTypes[index] == MediaType.video &&
        _videoControllers[index] != null) {
      _videoControllers[index]!.play();
    }

    widget.onPageChanged?.call(index);
  }

  @override
  Widget build(BuildContext context) {
    if (widget.mediaUrls.isEmpty) {
      return Container(
        height: widget.height,
        color: Colors.grey[300],
        child: const Center(
          child: Icon(Icons.image_not_supported, size: 48, color: Colors.grey),
        ),
      );
    }

    return SizedBox(
      height: widget.height,
      child: Stack(
        children: [
          // Media carousel
          PageView.builder(
            controller: _pageController,
            onPageChanged: _onPageChanged,
            itemCount: widget.mediaUrls.length,
            itemBuilder: (context, index) {
              return GestureDetector(
                onTap: () => widget.onMediaTap?.call(index),
                child: _buildMediaItem(index),
              );
            },
          ),

          // Page indicators
          if (widget.showIndicators && widget.mediaUrls.length > 1)
            Positioned(top: 12, right: 12, child: _buildPageIndicators()),

          // Media counter
          if (widget.mediaUrls.length > 1)
            Positioned(top: 12, left: 12, child: _buildMediaCounter()),
        ],
      ),
    );
  }

  Widget _buildMediaItem(int index) {
    final mediaType = widget.mediaTypes[index];
    final mediaUrl = widget.mediaUrls[index];

    switch (mediaType) {
      case MediaType.video:
        return _buildVideoItem(index);
      case MediaType.image:
        return _buildImageItem(mediaUrl);
      case MediaType.text:
        return _buildTextItem(mediaUrl);
    }
  }

  Widget _buildVideoItem(int index) {
    final chewieController = _chewieControllers[index];

    if (chewieController == null) {
      return Container(
        color: Colors.black,
        child: const Center(
          child: CircularProgressIndicator(color: Colors.white),
        ),
      );
    }

    return Container(
      color: Colors.black,
      child: Center(child: Chewie(controller: chewieController)),
    );
  }

  Widget _buildImageItem(String imageUrl) {
    return CachedNetworkImage(
      imageUrl: imageUrl,
      fit: BoxFit.cover,
      width: double.infinity,
      placeholder: (context, url) => Container(
        color: Colors.grey[300],
        child: const Center(child: CircularProgressIndicator()),
      ),
      errorWidget: (context, url, error) => Container(
        color: Colors.grey[300],
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error, size: 48, color: Colors.grey),
              SizedBox(height: 8),
              Text('Image unavailable', style: TextStyle(color: Colors.grey)),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextItem(String text) {
    return Container(
      color: Colors.black,
      padding: const EdgeInsets.all(20),
      child: Center(
        child: Text(
          text,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _buildPageIndicators() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: List.generate(
          widget.mediaUrls.length,
          (index) => Container(
            width: 6,
            height: 6,
            margin: const EdgeInsets.symmetric(horizontal: 2),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: index == _currentIndex
                  ? Colors.white
                  : Colors.white.withValues(alpha: 0.5),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMediaCounter() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        '${_currentIndex + 1}/${widget.mediaUrls.length}',
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
