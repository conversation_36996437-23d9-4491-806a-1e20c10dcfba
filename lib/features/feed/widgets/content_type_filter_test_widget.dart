import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/feed/providers/feed_filter_provider.dart';
import 'package:billionaires_social/features/feed/providers/feed_provider.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';

/// Test widget to verify content type filter functionality
class ContentTypeFilterTestWidget extends ConsumerWidget {
  const ContentTypeFilterTestWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentContentType = ref.watch(contentTypeFilterProvider);
    final feedState = ref.watch(feedProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text('Content Filter Test - ${currentContentType.name}'),
      ),
      body: Column(
        children: [
          // Filter buttons for testing
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: ContentTypeFilter.values.map((filter) {
                final isSelected = currentContentType == filter;
                return ElevatedButton(
                  onPressed: () {
                    ref
                        .read(contentTypeFilterProvider.notifier)
                        .setContentType(filter);
                    ref.read(feedProvider.notifier).refresh();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: isSelected ? Colors.blue : Colors.grey,
                    foregroundColor: Colors.white,
                  ),
                  child: Text(filter.name.toUpperCase()),
                );
              }).toList(),
            ),
          ),

          // Test results display
          Expanded(
            child: feedState.when(
              data: (posts) => _buildTestResults(posts, currentContentType),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Center(child: Text('Error: $error')),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTestResults(List<Post> posts, ContentTypeFilter currentFilter) {
    return Builder(
      builder: (context) =>
          _buildTestResultsContent(context, posts, currentFilter),
    );
  }

  Widget _buildTestResultsContent(
    BuildContext context,
    List<Post> posts,
    ContentTypeFilter currentFilter,
  ) {
    // Analyze the filtered posts
    final imageCount = posts
        .where((p) => p.mediaType == MediaType.image)
        .length;
    final videoCount = posts
        .where((p) => p.mediaType == MediaType.video)
        .length;
    final textCount = posts
        .where(
          (p) =>
              p.mediaType == MediaType.text ||
              (p.mediaUrl.isEmpty && p.caption.isNotEmpty),
        )
        .length;

    // Verify filtering is working correctly
    bool isFilteringCorrect = true;
    String filteringStatus = 'PASS';

    switch (currentFilter) {
      case ContentTypeFilter.all:
        // All filter should show all types
        isFilteringCorrect = true;
        break;
      case ContentTypeFilter.media:
        // Media filter should only show images and videos
        isFilteringCorrect = textCount == 0;
        break;
    }

    if (!isFilteringCorrect) {
      filteringStatus = 'FAIL';
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Test status
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: isFilteringCorrect ? Colors.green[100] : Colors.red[100],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isFilteringCorrect ? Colors.green : Colors.red,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  isFilteringCorrect ? Icons.check_circle : Icons.error,
                  color: isFilteringCorrect ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                Text(
                  'Filter Test: $filteringStatus',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: isFilteringCorrect
                        ? Colors.green[800]
                        : Colors.red[800],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Statistics
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Filter Statistics',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Text('Current Filter: ${currentFilter.name.toUpperCase()}'),
                  Text('Total Posts: ${posts.length}'),
                  Text('Image Posts: $imageCount'),
                  Text('Video Posts: $videoCount'),
                  Text('Text Posts: $textCount'),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Expected vs Actual
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Expected Behavior',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Text(_getExpectedBehavior(currentFilter)),
                  const SizedBox(height: 8),
                  Text(
                    'Actual Result: ${isFilteringCorrect ? "✅ Correct" : "❌ Incorrect"}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: isFilteringCorrect ? Colors.green : Colors.red,
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Post list preview
          Expanded(
            child: Card(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Text(
                      'Filtered Posts Preview',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                  ),
                  Expanded(
                    child: ListView.builder(
                      itemCount: posts.take(10).length,
                      itemBuilder: (context, index) {
                        final post = posts[index];
                        return ListTile(
                          leading: CircleAvatar(
                            child: Text(_getMediaTypeIcon(post.mediaType)),
                          ),
                          title: Text(post.username),
                          subtitle: Text(
                            post.caption.length > 50
                                ? '${post.caption.substring(0, 50)}...'
                                : post.caption,
                          ),
                          trailing: Chip(
                            label: Text(post.mediaType.name),
                            backgroundColor: _getMediaTypeColor(post.mediaType),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getExpectedBehavior(ContentTypeFilter filter) {
    switch (filter) {
      case ContentTypeFilter.all:
        return 'Should show all post types (images, videos, text)';
      case ContentTypeFilter.media:
        return 'Should show only media posts (imageCount > 0 OR videoCount > 0, textCount = 0)';
    }
  }

  String _getMediaTypeIcon(MediaType mediaType) {
    switch (mediaType) {
      case MediaType.image:
        return '�';
      case MediaType.video:
        return '�';
      case MediaType.text:
        return '✍️';
    }
  }

  Color _getMediaTypeColor(MediaType mediaType) {
    switch (mediaType) {
      case MediaType.image:
        return Colors.blue[100]!;
      case MediaType.video:
        return Colors.red[100]!;
      case MediaType.text:
        return Colors.green[100]!;
    }
  }
}
