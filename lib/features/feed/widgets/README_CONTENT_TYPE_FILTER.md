# Content Type Filter

## Overview

The Content Type Filter allows users to filter feed posts by content type: All Posts, Images, Videos, or Text Posts. This feature enhances user experience by letting them focus on specific types of content.

## Components

### 1. ContentTypeFilter Enum
Located in `feed_filter_provider.dart`:
```dart
enum ContentTypeFilter {
  all,      // Show all types of posts
  images,   // Show only image posts
  videos,   // Show only video posts
  text,     // Show only text posts
}
```

### 2. ContentTypeFilterNotifier
State management for the content type filter with methods:
- `setContentType(ContentTypeFilter)` - Change the active filter
- `getContentTypeTitle()` - Get display title for current filter
- `getContentTypeDescription()` - Get description for current filter
- `getContentTypeOptions()` - Get all available filter options

### 3. ContentTypeFilterWidget
The main UI component that displays filter options as horizontal chips.

### 4. FloatingContentTypeFilter
Alternative floating action button style filter.

### 5. ContentTypeFilterModal
Modal bottom sheet for selecting content type with detailed descriptions.

## Integration

### Automatic Integration
The content type filter is automatically integrated into the `CategorySelector` widget, which appears in the main feed screen. No additional setup required.

### Manual Integration Options

#### Option 1: Horizontal Filter Bar
```dart
// Add to any screen
const ContentTypeFilterWidget()
```

#### Option 2: Floating Action Button
```dart
// Add as floating action button
floatingActionButton: const FloatingContentTypeFilter()
```

#### Option 3: Modal Trigger
```dart
// Show modal on button press
ElevatedButton(
  onPressed: () => showModalBottomSheet(
    context: context,
    backgroundColor: Colors.transparent,
    builder: (context) => const ContentTypeFilterModal(),
  ),
  child: const Text('Filter Content'),
)
```

## How It Works

### 1. Filter Logic
The filter works by examining the `MediaType` property of each post:
- **Images**: `post.mediaType == MediaType.image`
- **Videos**: `post.mediaType == MediaType.video`
- **Text**: `post.mediaType == MediaType.text` OR posts with empty mediaUrl but non-empty caption

### 2. State Management
- Uses Riverpod for state management
- Filter state persists across app navigation
- Automatically refreshes feed when filter changes

### 3. Caching
- Filtered results are cached with filter type in cache key
- Cache key format: `feed_posts_{userId}_{feedFilter}_{contentTypeFilter}`
- Improves performance by avoiding re-filtering cached data

### 4. User Feedback
- Shows snackbar confirmation when filter changes
- Visual indicators show active filter state
- Smooth animations for filter transitions

## Customization

### Styling
Modify the filter appearance in `ContentTypeFilterWidget`:
```dart
// Change chip colors
color: isSelected
    ? AppTheme.accentColor.withValues(alpha: 0.15)
    : theme.cardColor,

// Change border colors
border: Border.all(
  color: isSelected
      ? AppTheme.accentColor
      : theme.dividerColor.withValues(alpha: 0.3),
),
```

### Filter Options
Add new content types by:
1. Adding to `ContentTypeFilter` enum
2. Updating filter logic in `_filterPostsByContentType()`
3. Adding UI option in `getContentTypeOptions()`

### Icons and Labels
Customize filter options in `getContentTypeOptions()`:
```dart
{
  'type': ContentTypeFilter.images,
  'title': 'Photos',                    // Custom title
  'icon': Icons.photo_camera_rounded,   // Material Design icon
  'iconEmoji': '📸',                   // Emoji overlay
  'description': 'Photo posts only',
  'color': const Color(0xFF10B981),    // Custom color
},
```

### Enhanced Icon System
The filter now uses a dual-icon system:
- **Material Design Icons**: Professional, consistent iconography
- **Emoji Overlays**: Fun, recognizable visual elements
- **Custom Colors**: Each filter type has its own brand color
- **Visual Effects**: Shadows, borders, and animations for better UX

## Performance Considerations

### Efficient Filtering
- Filtering happens after database fetch, not during query
- Uses efficient `where()` method on List<Post>
- Minimal performance impact on feed loading

### Caching Strategy
- Separate cache entries for each filter combination
- Prevents unnecessary re-filtering of cached data
- Background refresh maintains cache freshness

### Memory Management
- Filter state is lightweight (single enum value)
- No memory leaks from filter operations
- Proper disposal of providers when not needed

## Usage Examples

### Basic Usage (Already Integrated)
The filter is automatically available in the main feed screen through the CategorySelector.

### Custom Implementation
```dart
class CustomFeedScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final contentTypeFilter = ref.watch(contentTypeFilterProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: Text('Feed - ${contentTypeFilter.name}'),
      ),
      body: Column(
        children: [
          // Content type filter
          const ContentTypeFilterWidget(),
          
          // Feed content
          Expanded(
            child: Consumer(
              builder: (context, ref, child) {
                final feedAsync = ref.watch(feedProvider);
                return feedAsync.when(
                  data: (posts) => ListView.builder(
                    itemCount: posts.length,
                    itemBuilder: (context, index) => PostCard(post: posts[index]),
                  ),
                  loading: () => const CircularProgressIndicator(),
                  error: (error, stack) => Text('Error: $error'),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
```

### Programmatic Filter Control
```dart
// Change filter programmatically
ref.read(contentTypeFilterProvider.notifier).setContentType(ContentTypeFilter.images);

// Get current filter
final currentFilter = ref.read(contentTypeFilterProvider);

// Listen to filter changes
ref.listen(contentTypeFilterProvider, (previous, next) {
  print('Filter changed from $previous to $next');
});
```

## Testing

### Unit Tests
Test the filter logic:
```dart
test('should filter posts by content type', () {
  final posts = [
    Post(mediaType: MediaType.image, ...),
    Post(mediaType: MediaType.video, ...),
    Post(mediaType: MediaType.text, ...),
  ];
  
  final filteredPosts = feedProvider._filterPostsByContentType(
    posts, 
    ContentTypeFilter.images
  );
  
  expect(filteredPosts.length, 1);
  expect(filteredPosts.first.mediaType, MediaType.image);
});
```

### Widget Tests
Test the filter UI:
```dart
testWidgets('should show content type filter options', (tester) async {
  await tester.pumpWidget(
    ProviderScope(
      child: MaterialApp(
        home: Scaffold(
          body: ContentTypeFilterWidget(),
        ),
      ),
    ),
  );
  
  expect(find.text('All Posts'), findsOneWidget);
  expect(find.text('Images'), findsOneWidget);
  expect(find.text('Videos'), findsOneWidget);
  expect(find.text('Text Posts'), findsOneWidget);
});
```

## Troubleshooting

### Common Issues

1. **Filter not working**: Ensure `feedProvider` is properly watching `contentTypeFilterProvider`
2. **UI not updating**: Check that widgets are using `Consumer` or `ref.watch()`
3. **Cache issues**: Clear cache if filter results seem stale
4. **Performance issues**: Verify filtering logic is efficient for large post lists

### Debug Tips
- Use `debugPrint()` statements in filter logic to trace execution
- Check provider state with Flutter Inspector
- Monitor cache keys to ensure proper cache separation
- Verify post `MediaType` values are set correctly

This content type filter provides a seamless way for users to customize their feed experience while maintaining excellent performance and user experience.
