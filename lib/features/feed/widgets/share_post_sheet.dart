import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/features/feed/services/feed_service.dart';
import 'package:billionaires_social/features/messaging/screens/chat_screen.dart';
import 'package:billionaires_social/features/messaging/services/chat_service.dart';
import 'package:billionaires_social/features/stories/screens/unified_story_editor_screen.dart';
import 'package:billionaires_social/features/profile/models/profile_model.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:share_plus/share_plus.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:http/http.dart' as http;
import 'dart:io';

class SharePostSheet extends ConsumerStatefulWidget {
  final Post post;
  final VoidCallback? onRepost;

  const SharePostSheet({super.key, required this.post, this.onRepost});

  @override
  ConsumerState<SharePostSheet> createState() => _SharePostSheetState();
}

class _SharePostSheetState extends ConsumerState<SharePostSheet> {
  bool _isReposting = false;
  bool _isSharingToStory = false;
  List<ProfileModel> _friends = [];
  bool _isLoadingFriends = false;
  final ChatService _chatService = getIt<ChatService>();

  @override
  void initState() {
    super.initState();
    _loadFriends();
  }

  Future<void> _loadFriends() async {
    setState(() => _isLoadingFriends = true);
    try {
      // TODO: Replace with actual friends loading from messaging service
      // For now, using mock data
      _friends = [
        ProfileModel(
          id: 'friend1',
          username: 'john_doe',
          name: 'John Doe',
          profilePictureUrl: 'https://example.com/avatar1.jpg',
          bio: 'CEO at Tech Corp',
          postCount: 45,
          followerCount: 1200,
          followingCount: 890,
        ),
        ProfileModel(
          id: 'friend2',
          username: 'jane_smith',
          name: 'Jane Smith',
          profilePictureUrl: 'https://example.com/avatar2.jpg',
          bio: 'Investor & Entrepreneur',
          postCount: 23,
          followerCount: 3400,
          followingCount: 567,
        ),
      ];
    } catch (e) {
      // Handle error silently
    } finally {
      setState(() => _isLoadingFriends = false);
    }
  }

  Future<void> _shareToFriend(ProfileModel friend) async {
    try {
      // Track analytics
      final feedService = ref.read(feedServiceProvider);
      await feedService.trackShareToFriend(widget.post.id, friend.id);

      // Get or create a direct chat with the friend
      final chatId = await _chatService.getOrCreateDirectChat(
        friend.id,
        friend.name,
        friend.profilePictureUrl,
      );

      // Send the shared post
      await _chatService.sendSharedPost(
        chatId: chatId,
        postId: widget.post.id,
        postUsername: widget.post.username,
        postCaption: widget.post.caption,
        postMediaUrl: widget.post.mediaUrl,
        postUserAvatarUrl: widget.post.userAvatarUrl,
        postLocation: widget.post.location,
      );

      if (mounted) {
        Navigator.pop(context);

        // Get the chat model and navigate to chat
        final chat = await _chatService.getChatById(chatId);
        if (chat != null && mounted) {
          Navigator.of(context).push(
            MaterialPageRoute(builder: (context) => ChatScreen(chat: chat)),
          );
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Post shared with ${friend.name}!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to share post: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _repostToFeed() async {
    if (_isReposting) return;

    setState(() => _isReposting = true);

    try {
      final feedService = ref.read(feedServiceProvider);
      await feedService.repost(widget.post.id);

      // Track analytics
      await feedService.trackRepost(widget.post.id, widget.post.userId);

      if (mounted) {
        Navigator.pop(context);
        widget.onRepost?.call();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Post reposted to your feed!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to repost: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isReposting = false);
      }
    }
  }

  Future<void> _shareToStory() async {
    if (_isSharingToStory) return;

    setState(() => _isSharingToStory = true);

    try {
      // Track analytics
      final feedService = ref.read(feedServiceProvider);
      await feedService.trackShareToStory(widget.post.id);

      // Download the media from the post
      final response = await http.get(Uri.parse(widget.post.mediaUrl));
      if (response.statusCode == 200) {
        final bytes = response.bodyBytes;

        // Create a temporary file
        final tempDir = Directory.systemTemp;
        final tempFile = File(
          '${tempDir.path}/shared_post_${DateTime.now().millisecondsSinceEpoch}.jpg',
        );
        await tempFile.writeAsBytes(bytes);

        if (mounted) {
          Navigator.pop(context);

          // Navigate to story editor with the downloaded media
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) =>
                  UnifiedStoryEditorScreen(mediaFile: tempFile),
            ),
          );
        }
      } else {
        throw Exception('Failed to download media');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to share to story: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSharingToStory = false);
      }
    }
  }

  void _shareExternally() {
    Navigator.pop(context);

    // Track analytics
    final feedService = ref.read(feedServiceProvider);
    feedService.trackExternalShare(widget.post.id);

    SharePlus.instance.share(
      ShareParams(
        text:
            'Check out this post from ${widget.post.username}!\n\n${widget.post.caption}\n\nhttps://billionaires.social/post/${widget.post.id}',
        subject: 'Check out this post from Billionaires Social',
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 16),

          // Post preview
          _buildPostPreview(),
          const SizedBox(height: 16),

          // Share options
          _buildShareOptions(),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildPostPreview() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          // Post media preview
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: widget.post.mediaType == MediaType.video
                ? Container(
                    width: 60,
                    height: 60,
                    color: Colors.grey[300],
                    child: const Icon(Icons.play_arrow, size: 24),
                  )
                : CachedNetworkImage(
                    imageUrl: widget.post.mediaUrl,
                    width: 60,
                    height: 60,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      width: 60,
                      height: 60,
                      color: Colors.grey[300],
                      child: const Center(
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      width: 60,
                      height: 60,
                      color: Colors.grey[300],
                      child: const Icon(Icons.error, size: 24),
                    ),
                  ),
          ),
          const SizedBox(width: 12),

          // Post info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.post.username,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  widget.post.caption,
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShareOptions() {
    return Column(
      children: [
        // Repost to Feed
        ListTile(
          leading: _isReposting
              ? const SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Icon(Icons.repeat, color: Colors.blue),
          title: const Text('Repost to Feed'),
          subtitle: const Text('Share this post to your profile'),
          onTap: _isReposting ? null : _repostToFeed,
        ),

        // Share to Story
        ListTile(
          leading: _isSharingToStory
              ? const SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const FaIcon(
                  FontAwesomeIcons.solidCircle,
                  color: Colors.purple,
                ),
          title: const Text('Share to Story'),
          subtitle: const Text('Add to your story for 24 hours'),
          onTap: _isSharingToStory ? null : _shareToStory,
        ),

        // Share with Friends
        if (_friends.isNotEmpty) ...[
          const Divider(),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                const Icon(Icons.people, color: Colors.green),
                const SizedBox(width: 12),
                const Text(
                  'Share with Friends',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 80,
            child: _isLoadingFriends
                ? const Center(child: CircularProgressIndicator())
                : ListView.builder(
                    scrollDirection: Axis.horizontal,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: _friends.length,
                    itemBuilder: (context, index) {
                      final friend = _friends[index];
                      return GestureDetector(
                        onTap: () => _shareToFriend(friend),
                        child: Container(
                          width: 60,
                          margin: const EdgeInsets.only(right: 12),
                          child: Column(
                            children: [
                              CircleAvatar(
                                radius: 25,
                                backgroundImage:
                                    friend.profilePictureUrl.isNotEmpty
                                    ? CachedNetworkImageProvider(
                                        friend.profilePictureUrl,
                                      )
                                    : null,
                                child: friend.profilePictureUrl.isEmpty
                                    ? const Icon(Icons.person, size: 20)
                                    : null,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                friend.name,
                                style: const TextStyle(fontSize: 10),
                                textAlign: TextAlign.center,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
          ),
        ],

        // External Share
        const Divider(),
        ListTile(
          leading: const Icon(Icons.share, color: Colors.orange),
          title: const Text('Share Externally'),
          subtitle: const Text('Copy link or share to other apps'),
          onTap: _shareExternally,
        ),
      ],
    );
  }
}
