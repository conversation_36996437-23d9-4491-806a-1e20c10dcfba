import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:billionaires_social/features/feed/providers/feed_filter_provider.dart';
import 'package:billionaires_social/features/feed/widgets/filter_modal.dart';
import 'package:billionaires_social/features/feed/widgets/content_type_filter.dart'
    as content_filter;

class CategorySelector extends ConsumerWidget {
  const CategorySelector({super.key});

  // Brand colors for each category
  static const Map<String, Color> _categoryColors = {
    'Billionaires': Color(0xFFD4AF37), // Luxurious golden color
    'Places': Color(0xFF059669), // Keep current elegant green
    'Trends': Color(0xFFFF5E3A), // High-luminance orange-red for trending feel
    'Reels': Color(0xFF000000), // Pure black for clean, cinematic feel
  };

  // Helper method to get appropriate icon color for light/dark mode
  Color _getIconColor(String label, Color brandColor, ThemeData theme) {
    // For Reels, use white in dark mode and black in light mode for visibility
    if (label == 'Reels') {
      return theme.brightness == Brightness.dark ? Colors.white : Colors.black;
    }
    // For other categories, use the brand color as is
    return brandColor;
  }

  // Helper method to get text color - always black
  Color _getTextColor(ThemeData theme) {
    return Colors.black;
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentFilter = ref.watch(feedFilterProvider);
    final theme = Theme.of(context);

    final categories = [
      {
        'icon': FontAwesomeIcons.crown,
        'label': 'Billionaires',
        'filterType': FeedFilterType.billionaires,
        'showFilterModal': true,
        'brandColor': _categoryColors['Billionaires']!,
      },
      {
        'icon': FontAwesomeIcons.locationDot,
        'label': 'Places',
        'filterType': FeedFilterType.all,
        'showFilterModal': false,
        'brandColor': _categoryColors['Places']!,
      },
      {
        'icon': FontAwesomeIcons.fire,
        'label': 'Trends',
        'filterType': FeedFilterType.verified,
        'showFilterModal': false,
        'brandColor': _categoryColors['Trends']!,
      },
      {
        'icon': FontAwesomeIcons.play,
        'label': 'Reels',
        'filterType': FeedFilterType.followed,
        'showFilterModal': false,
        'brandColor': _categoryColors['Reels']!,
      },
    ];

    return Column(
      children: [
        // Category selector
        Container(
          height: 100,
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: categories.length,
            itemBuilder: (context, index) {
              final category = categories[index];
              final filterType = category['filterType'] as FeedFilterType;
              final isSelected = currentFilter == filterType;

              return GestureDetector(
                onTap: () {
                  // Always show filter modal for all tabs to allow user type selection
                  showFilterModal(context, category['label'] as String);
                },
                child: Container(
                  width: 100,
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: isSelected
                        ? (category['brandColor'] as Color).withAlpha(51)
                        : theme.dividerColor.withAlpha(128),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      FaIcon(
                        category['icon'] as IconData,
                        color: _getIconColor(
                          category['label'] as String,
                          category['brandColor'] as Color,
                          theme,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        category['label'] as String,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: _getTextColor(theme),
                          fontWeight: isSelected ? FontWeight.bold : null,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),

        // Content type filter
        const content_filter.ContentTypeFilterWidget(),
      ],
    );
  }
}
