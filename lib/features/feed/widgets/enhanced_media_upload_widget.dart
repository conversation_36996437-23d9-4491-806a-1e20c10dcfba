import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:billionaires_social/core/services/enhanced_media_picker_service.dart';
import 'package:billionaires_social/features/feed/widgets/multi_media_carousel.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';

/// Enhanced media upload widget supporting multiple images and videos
class EnhancedMediaUploadWidget extends ConsumerStatefulWidget {
  final Function(List<MediaFile>) onMediaSelected;
  final int maxImages;
  final int maxVideos;
  final bool allowImages;
  final bool allowVideos;
  final Duration? maxVideoDuration;

  const EnhancedMediaUploadWidget({
    super.key,
    required this.onMediaSelected,
    this.maxImages = 10,
    this.maxVideos = 5,
    this.allowImages = true,
    this.allowVideos = true,
    this.maxVideoDuration,
  });

  @override
  ConsumerState<EnhancedMediaUploadWidget> createState() =>
      _EnhancedMediaUploadWidgetState();
}

class _EnhancedMediaUploadWidgetState
    extends ConsumerState<EnhancedMediaUploadWidget> {
  final EnhancedMediaPickerService _mediaPickerService =
      EnhancedMediaPickerService();
  List<MediaFile> _selectedMedia = [];
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Selected media preview
        if (_selectedMedia.isNotEmpty) _buildMediaPreview(),

        // Upload buttons
        _buildUploadButtons(),

        // Loading indicator
        if (_isLoading)
          const Padding(
            padding: EdgeInsets.all(16),
            child: CircularProgressIndicator(),
          ),
      ],
    );
  }

  Widget _buildMediaPreview() {
    return Container(
      height: 300,
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Stack(
        children: [
          // Media carousel
          MultiMediaCarousel(
            mediaUrls: _selectedMedia.map((m) => m.file.path).toList(),
            mediaTypes: _selectedMedia
                .map(
                  (m) => m.type == MediaFileType.video
                      ? MediaType.video
                      : MediaType.image,
                )
                .toList(),
            height: 300,
            showIndicators: true,
            onMediaTap: (index) => _showMediaOptions(index),
          ),

          // Remove all button
          Positioned(
            top: 8,
            right: 8,
            child: GestureDetector(
              onTap: _clearAllMedia,
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: const BoxDecoration(
                  color: Colors.black54,
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.close, color: Colors.white, size: 20),
              ),
            ),
          ),

          // Media count
          Positioned(
            bottom: 8,
            left: 8,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.black54,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${_selectedMedia.length} ${_selectedMedia.length == 1 ? 'item' : 'items'}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUploadButtons() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Multiple media button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _isLoading ? null : _pickMultipleMedia,
              icon: const Icon(Icons.photo_library),
              label: Text(
                _selectedMedia.isEmpty
                    ? 'Select Photos & Videos'
                    : 'Add More Media (${_selectedMedia.length})',
              ),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),

          const SizedBox(height: 12),

          // Quick action buttons
          Row(
            children: [
              // Camera button
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _isLoading ? null : _takePhoto,
                  icon: const Icon(Icons.camera_alt),
                  label: const Text('Camera'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                  ),
                ),
              ),

              const SizedBox(width: 12),

              // Video button
              if (widget.allowVideos)
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _isLoading ? null : _recordVideo,
                    icon: const Icon(Icons.videocam),
                    label: const Text('Video'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                    ),
                  ),
                ),
            ],
          ),

          // Media limits info
          if (widget.maxImages < 50 || widget.maxVideos < 50)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'Max: ${widget.maxImages} images, ${widget.maxVideos} videos',
                style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
              ),
            ),
        ],
      ),
    );
  }

  Future<void> _pickMultipleMedia() async {
    setState(() => _isLoading = true);

    try {
      final mediaFiles = await _mediaPickerService.pickMultipleMedia(
        maxImages: widget.maxImages,
        maxVideos: widget.maxVideos,
        allowImages: widget.allowImages,
        allowVideos: widget.allowVideos,
        maxVideoDuration: widget.maxVideoDuration,
      );

      if (mediaFiles.isNotEmpty) {
        setState(() {
          _selectedMedia = mediaFiles;
        });
        widget.onMediaSelected(_selectedMedia);
      }
    } catch (e) {
      _showErrorSnackBar('Failed to select media: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _takePhoto() async {
    setState(() => _isLoading = true);

    try {
      final mediaFile = await _mediaPickerService.pickSingleMedia(
        source: ImageSource.camera,
        allowImages: true,
        allowVideos: false,
      );

      if (mediaFile != null) {
        setState(() {
          _selectedMedia = [mediaFile];
        });
        widget.onMediaSelected(_selectedMedia);
      }
    } catch (e) {
      _showErrorSnackBar('Failed to take photo: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _recordVideo() async {
    setState(() => _isLoading = true);

    try {
      final mediaFile = await _mediaPickerService.pickSingleMedia(
        source: ImageSource.camera,
        allowImages: false,
        allowVideos: true,
        maxVideoDuration: widget.maxVideoDuration,
      );

      if (mediaFile != null) {
        setState(() {
          _selectedMedia = [mediaFile];
        });
        widget.onMediaSelected(_selectedMedia);
      }
    } catch (e) {
      _showErrorSnackBar('Failed to record video: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showMediaOptions(int index) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text('Remove this item'),
              onTap: () {
                Navigator.pop(context);
                _removeMediaAt(index);
              },
            ),
            ListTile(
              leading: const Icon(Icons.info),
              title: const Text('Media info'),
              onTap: () {
                Navigator.pop(context);
                _showMediaInfo(index);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _removeMediaAt(int index) {
    setState(() {
      _selectedMedia.removeAt(index);
    });
    widget.onMediaSelected(_selectedMedia);
  }

  void _clearAllMedia() {
    setState(() {
      _selectedMedia.clear();
    });
    widget.onMediaSelected(_selectedMedia);
  }

  void _showMediaInfo(int index) {
    final media = _selectedMedia[index];
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Media Info'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Type: ${media.type.name}'),
            Text('Size: ${media.sizeInMB.toStringAsFixed(2)} MB'),
            if (media.duration != null)
              Text('Duration: ${media.duration!.inSeconds}s'),
            Text('Extension: ${media.extension}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }
}
