import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/feed/models/comment_model.dart';
import 'package:billionaires_social/features/feed/providers/post_detail_provider.dart';
import 'package:billionaires_social/features/feed/services/feed_service.dart';
import 'package:billionaires_social/features/feed/widgets/voice_comment_input.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:billionaires_social/features/auth/providers/auth_provider.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class CommentBottomSheet extends ConsumerStatefulWidget {
  final String postId;
  final String? postOwnerId;
  const CommentBottomSheet({super.key, required this.postId, this.postOwnerId});

  @override
  ConsumerState<CommentBottomSheet> createState() => _CommentBottomSheetState();
}

class CommentThread {
  final Comment comment;
  final List<CommentThread> replies;
  CommentThread(this.comment, [List<CommentThread>? replies])
    : replies = replies ?? <CommentThread>[];
}

List<CommentThread> buildCommentThreads(List<Comment> comments) {
  final Map<String, CommentThread> map = {};
  final List<CommentThread> roots = [];

  for (final c in comments) {
    map[c.id] = CommentThread(c);
  }

  for (final c in comments) {
    if (c.parentId != null) {
      final parent = map[c.parentId!];
      if (parent != null) {
        parent.replies.add(map[c.id]!);
      } else {
        roots.add(map[c.id]!);
      }
    } else {
      roots.add(map[c.id]!);
    }
  }

  return roots;
}

class _CommentBottomSheetState extends ConsumerState<CommentBottomSheet> {
  final _controller = TextEditingController();
  final _inputFocusNode = FocusNode();
  bool _posting = false;
  String _inputText = '';
  String? _expandedCommentId; // For collapsible threads
  String? _replyingToUsername;
  String? _replyingToCommentId;
  String? _editingCommentId;
  bool _showVoiceInput = false;

  @override
  void initState() {
    super.initState();
    _controller.addListener(_onInputChanged);
  }

  void _onInputChanged() {
    setState(() {
      _inputText = _controller.text.trim();
    });
  }

  void _toggleThread(String commentId) {
    setState(() {
      _expandedCommentId = _expandedCommentId == commentId ? null : commentId;
    });
  }

  void setReply(String username, String commentId) {
    setState(() {
      _replyingToUsername = username;
      _replyingToCommentId = commentId;
      _editingCommentId = null;
    });
    _controller.text = '@$username ';
    _controller.selection = TextSelection.fromPosition(
      TextPosition(offset: _controller.text.length),
    );
    FocusScope.of(context).requestFocus(_inputFocusNode);
  }

  void setEdit(String commentId, String text) {
    setState(() {
      _editingCommentId = commentId;
      _replyingToUsername = null;
      _replyingToCommentId = null;
    });
    _controller.text = text;
    _controller.selection = TextSelection.fromPosition(
      TextPosition(offset: _controller.text.length),
    );
    FocusScope.of(context).requestFocus(_inputFocusNode);
  }

  void clearInput() {
    setState(() {
      _replyingToUsername = null;
      _replyingToCommentId = null;
      _editingCommentId = null;
    });
    _controller.clear();
  }

  Future<void> handleSend() async {
    final text = _controller.text.trim();
    if (text.isEmpty || _posting) return;

    setState(() => _posting = true);
    try {
      if (_editingCommentId != null) {
        await ref
            .read(feedServiceProvider)
            .editComment(widget.postId, _editingCommentId!, text);
      } else {
        await ref
            .read(feedServiceProvider)
            .addComment(widget.postId, text, parentId: _replyingToCommentId);
      }
      clearInput();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              e.toString(),
              style: const TextStyle(color: Colors.white),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _posting = false);
      }
    }
  }

  Future<void> handleDelete(String commentId) async {
    await ref.read(feedServiceProvider).deleteComment(widget.postId, commentId);
  }

  void _toggleVoiceInput() {
    setState(() {
      _showVoiceInput = !_showVoiceInput;
    });
  }

  Future<void> _handleVoiceComment(
    String voiceUrl,
    int duration,
    String? waveformData,
  ) async {
    setState(() => _posting = true);
    try {
      await ref
          .read(feedServiceProvider)
          .addVoiceComment(
            widget.postId,
            voiceUrl,
            duration,
            waveformData,
            parentId: _replyingToCommentId,
          );
      clearInput();
      setState(() => _showVoiceInput = false);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to send voice comment: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _posting = false);
      }
    }
  }

  void _cancelVoiceInput() {
    setState(() {
      _showVoiceInput = false;
    });
  }

  @override
  void dispose() {
    _controller.removeListener(_onInputChanged);
    _controller.dispose();
    _inputFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final commentsAsync = ref.watch(commentsProvider(widget.postId));
    final currentUser = ref.watch(authProvider).value;
    final bottom = MediaQuery.of(context).viewInsets.bottom;

    return Stack(
      children: [
        DraggableScrollableSheet(
          initialChildSize: 0.7,
          minChildSize: 0.4,
          maxChildSize: 0.95,
          expand: false,
          builder: (context, scrollController) {
            return Container(
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(24),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.08),
                    blurRadius: 16,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: SafeArea(
                top: false,
                child: Column(
                  children: [
                    Container(
                      width: 40,
                      height: 5,
                      margin: const EdgeInsets.symmetric(vertical: 12),
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(2.5),
                      ),
                    ),
                    Expanded(
                      child: commentsAsync.when(
                        data: (comments) {
                          if (comments.isEmpty) {
                            return const Center(
                              child: Text('No comments yet.'),
                            );
                          }
                          final threads = buildCommentThreads(comments);
                          return ListView(
                            controller: scrollController,
                            children: threads
                                .map(
                                  (t) => _CommentThreadWidget(
                                    thread: t,
                                    currentUserId: currentUser?.uid,
                                    onReply: setReply,
                                    onEdit: setEdit,
                                    onDelete: handleDelete,
                                    expandedCommentId: _expandedCommentId,
                                    onToggleThread: _toggleThread,
                                    postId: widget.postId,
                                    postOwnerId: widget.postOwnerId,
                                    allComments: comments,
                                  ),
                                )
                                .toList(),
                          );
                        },
                        loading: () =>
                            const Center(child: CircularProgressIndicator()),
                        error: (e, _) => Center(child: Text('Error: $e')),
                      ),
                    ),
                    if (_replyingToUsername != null &&
                        _replyingToCommentId != null)
                      Padding(
                        padding: const EdgeInsets.only(
                          left: 16,
                          right: 16,
                          top: 4,
                        ),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: Theme.of(
                                  context,
                                ).colorScheme.secondary.withValues(alpha: 0.08),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                children: [
                                  const Icon(
                                    Icons.reply,
                                    size: 16,
                                    color: Colors.blue,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    'Replying to @$_replyingToUsername',
                                    style: const TextStyle(color: Colors.blue),
                                  ),
                                ],
                              ),
                            ),
                            IconButton(
                              icon: const Icon(Icons.close),
                              onPressed: clearInput,
                            ),
                          ],
                        ),
                      ),
                    if (_editingCommentId != null)
                      Padding(
                        padding: const EdgeInsets.only(
                          left: 16,
                          right: 16,
                          top: 4,
                        ),
                        child: Row(
                          children: [
                            Text(
                              'Editing comment',
                              style: TextStyle(color: Colors.orange),
                            ),
                            IconButton(
                              icon: Icon(Icons.close),
                              onPressed: clearInput,
                            ),
                          ],
                        ),
                      ),
                    Padding(
                      padding: EdgeInsets.only(
                        left: 12,
                        right: 12,
                        bottom: bottom > 0 ? bottom : 12,
                        top: 8,
                      ),
                      child: DecoratedBox(
                        decoration: BoxDecoration(
                          color: Theme.of(context).cardColor,
                          borderRadius: BorderRadius.circular(24),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.04),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Row(
                          children: [
                            // Voice comment button
                            Material(
                              color: Colors.transparent,
                              child: InkWell(
                                borderRadius: BorderRadius.circular(20),
                                onTap: _toggleVoiceInput,
                                child: Padding(
                                  padding: const EdgeInsets.all(8),
                                  child: Icon(
                                    Icons.mic,
                                    size: 24,
                                    color: _showVoiceInput
                                        ? Theme.of(context).colorScheme.primary
                                        : Colors.grey[600],
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: _LuxuryCommentInputField(
                                controller: _controller,
                                focusNode: _inputFocusNode,
                                hintText: _replyingToUsername != null
                                    ? 'Reply to @$_replyingToUsername...'
                                    : _editingCommentId != null
                                    ? 'Edit your comment...'
                                    : 'Add a comment...',
                                onSubmitted: (_) => handleSend(),
                              ),
                            ),
                            const SizedBox(width: 4),
                            AnimatedSwitcher(
                              duration: const Duration(milliseconds: 200),
                              child: _posting
                                  ? const SizedBox(
                                      width: 32,
                                      height: 32,
                                      child: Padding(
                                        padding: EdgeInsets.all(6),
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2.2,
                                        ),
                                      ),
                                    )
                                  : Material(
                                      color: Colors.transparent,
                                      child: InkWell(
                                        borderRadius: BorderRadius.circular(20),
                                        onTap:
                                            (!_posting && _inputText.isNotEmpty)
                                            ? handleSend
                                            : null,
                                        child: Padding(
                                          padding: const EdgeInsets.all(8),
                                          child: Icon(
                                            Icons.send_rounded,
                                            size: 24,
                                            color:
                                                (!_posting &&
                                                    _inputText.isNotEmpty)
                                                ? Theme.of(
                                                    context,
                                                  ).colorScheme.primary
                                                : Colors.grey[400],
                                          ),
                                        ),
                                      ),
                                    ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
        if (_showVoiceInput)
          Positioned.fill(
            child: Container(
              color: Colors.black.withValues(alpha: 0.5),
              child: Center(
                child: VoiceCommentInput(
                  postId: widget.postId,
                  onVoiceRecorded: _handleVoiceComment,
                  onCancel: _cancelVoiceInput,
                ),
              ),
            ),
          ),
      ],
    );
  }
}

class _CommentThreadWidget extends StatelessWidget {
  final CommentThread thread;
  final String? currentUserId;
  final void Function(String username, String commentId) onReply;
  final void Function(String commentId, String text) onEdit;
  final Future<void> Function(String commentId) onDelete;
  final int depth;
  final String? expandedCommentId;
  final void Function(String commentId) onToggleThread;
  final String postId;
  final String? postOwnerId;
  final List<Comment> allComments;
  static const int maxVisibleDepth = 3;
  const _CommentThreadWidget({
    required this.thread,
    required this.currentUserId,
    required this.onReply,
    required this.onEdit,
    required this.onDelete,
    this.depth = 0,
    required this.expandedCommentId,
    required this.onToggleThread,
    required this.postId,
    this.postOwnerId,
    required this.allComments,
  });

  String? _getParentUsername(String? parentId) {
    if (parentId == null || parentId.isEmpty) return null;
    final parentComment = allComments.firstWhere(
      (comment) => comment.id == parentId,
      orElse: () => Comment(
        id: '',
        postId: '',
        userId: '',
        username: 'Unknown',
        userAvatarUrl: '',
        text: '',
        timestamp: DateTime.now(),
      ),
    );
    return parentComment.username;
  }

  void _navigateToUserProfile(String userId) {
    // TODO: Implement navigation to user profile
    // This would typically use Navigator.push or a routing solution
  }

  @override
  Widget build(BuildContext context) {
    final c = thread.comment;
    final hasReplies = thread.replies.isNotEmpty;
    final isExpanded = expandedCommentId == c.id || depth == 0;
    final canDelete =
        (currentUserId == c.userId) ||
        (postOwnerId != null && currentUserId == postOwnerId);
    // final canEdit = currentUserId == c.userId; // TODO: Use for voice comment editing
    final replyCount = thread.replies.length;
    final showAvatar = depth < 2;
    final bgColor = depth == 0
        ? Theme.of(context).scaffoldBackgroundColor
        : depth == 1
        ? Theme.of(context).cardColor.withValues(alpha: 0.97)
        : Theme.of(context).cardColor.withValues(alpha: 0.93);
    final borderRadius = BorderRadius.circular(depth == 0 ? 0 : 14);
    final leftPad = depth * 20.0;
    final showReplyLabel =
        c.parentId != null && c.parentId!.isNotEmpty && depth > 0;
    final parentUsername = _getParentUsername(c.parentId);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Vertical connector for nested replies
            if (depth > 0)
              Container(
                width: 16,
                alignment: Alignment.topCenter,
                child: Container(
                  width: 2,
                  height: 56,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(1),
                  ),
                ),
              ),
            Expanded(
              child: Container(
                margin: EdgeInsets.only(
                  left: leftPad,
                  right: 8,
                  top: 8,
                  bottom: 0,
                ),
                decoration: BoxDecoration(
                  color: bgColor,
                  borderRadius: borderRadius,
                ),
                child: Padding(
                  padding: EdgeInsets.only(
                    left: showAvatar ? 0 : 8,
                    right: 8,
                    top: 8,
                    bottom: 8,
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (showAvatar)
                        CircleAvatar(
                          radius: depth == 0 ? 18 : 14,
                          backgroundImage: NetworkImage(c.userAvatarUrl),
                        ),
                      if (!showAvatar) SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (showReplyLabel && parentUsername != null)
                              Padding(
                                padding: const EdgeInsets.only(bottom: 2),
                                child: GestureDetector(
                                  onTap: () =>
                                      _navigateToUserProfile(c.parentId!),
                                  child: Text(
                                    'replying to @$parentUsername',
                                    style: TextStyle(
                                      fontSize: 11,
                                      color: Colors.blueGrey[400],
                                      fontStyle: FontStyle.italic,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                ),
                              ),
                            Row(
                              children: [
                                Flexible(
                                  child: Text(
                                    c.username,
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                      letterSpacing: 0.1,
                                    ),
                                    maxLines: 2,
                                    overflow: TextOverflow.visible,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 2),
                            // Render comment content based on type
                            if (c.type == CommentType.voice)
                              _buildVoiceCommentContent(c)
                            else
                              Text(
                                c.text,
                                style: const TextStyle(
                                  fontSize: 15,
                                  height: 1.4,
                                ),
                              ),
                            const SizedBox(height: 6),
                            Row(
                              children: [
                                Text(
                                  timeago.format(c.timestamp),
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[500],
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                                const Spacer(),
                                ..._buildActionButtons(context, c, canDelete),
                              ],
                            ),
                            _ElegantReactionBar(
                              commentId: c.id,
                              currentUserId: currentUserId,
                              postId: postId,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
        if (hasReplies)
          Padding(
            padding: EdgeInsets.only(
              left: (depth + 1) * 20.0,
              top: 2,
              bottom: 2,
            ),
            child: (depth >= maxVisibleDepth && !isExpanded)
                ? GestureDetector(
                    onTap: () => onToggleThread(c.id),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      child: Text(
                        'View more replies ($replyCount)',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.w500,
                          fontSize: 13,
                        ),
                      ),
                    ),
                  )
                : Column(
                    children: thread.replies
                        .map(
                          (r) => _CommentThreadWidget(
                            thread: r,
                            currentUserId: currentUserId,
                            onReply: onReply,
                            onEdit: onEdit,
                            onDelete: onDelete,
                            depth: depth + 1,
                            expandedCommentId: expandedCommentId,
                            onToggleThread: onToggleThread,
                            postId: postId,
                            postOwnerId: postOwnerId,
                            allComments: allComments,
                          ),
                        )
                        .toList(),
                  ),
          ),
      ],
    );
  }

  List<Widget> _buildActionButtons(
    BuildContext context,
    Comment c,
    bool canDelete,
  ) {
    final List<Widget> actions = [];

    // Reply button
    actions.add(
      GestureDetector(
        onTap: () => onReply(c.username, c.id),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4),
          child: Icon(Icons.reply, size: 18, color: Colors.grey[500]),
        ),
      ),
    );

    // Edit button (only for own comments)
    if (currentUserId == c.userId) {
      actions.add(
        GestureDetector(
          onTap: () => onEdit(c.id, c.text),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: Icon(Icons.edit, size: 18, color: Colors.grey[500]),
          ),
        ),
      );
    }

    // Delete button
    if (canDelete) {
      actions.add(
        GestureDetector(
          onTap: () => onDelete(c.id),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: Icon(Icons.delete, size: 18, color: Colors.grey[500]),
          ),
        ),
      );
    }

    return actions;
  }

  Widget _buildVoiceCommentContent(Comment c) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        children: [
          Icon(Icons.mic, color: Colors.grey[600], size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Voice message • ${_formatDuration(c.voiceDuration)}',
              style: TextStyle(
                color: Colors.grey[700],
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Icon(Icons.play_arrow, color: Colors.grey[600], size: 20),
        ],
      ),
    );
  }

  String _formatDuration(int? seconds) {
    if (seconds == null) return '0:00';
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '$minutes:${remainingSeconds.toString().padLeft(2, '0')}';
  }
}

class _LuxuryCommentInputField extends StatelessWidget {
  final TextEditingController controller;
  final FocusNode focusNode;
  final String hintText;
  final ValueChanged<String>? onSubmitted;
  const _LuxuryCommentInputField({
    required this.controller,
    required this.focusNode,
    required this.hintText,
    this.onSubmitted,
  });

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: controller,
      focusNode: focusNode,
      style: const TextStyle(fontSize: 15, height: 1.3),
      decoration: InputDecoration(
        hintText: hintText,
        border: InputBorder.none,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
      ),
      minLines: 1,
      maxLines: 4,
      textInputAction: TextInputAction.send,
      onSubmitted: onSubmitted,
      keyboardType: TextInputType.multiline,
      enableSuggestions: true,
      autocorrect: true,
    );
  }
}

class _ElegantReactionBar extends ConsumerStatefulWidget {
  final String commentId;
  final String? currentUserId;
  final String postId;
  const _ElegantReactionBar({
    required this.commentId,
    required this.currentUserId,
    required this.postId,
  });
  @override
  ConsumerState<_ElegantReactionBar> createState() =>
      _ElegantReactionBarState();
}

class _ElegantReactionBarState extends ConsumerState<_ElegantReactionBar> {
  static const List<String> _emojis = [
    '👍',
    '❤️',
    '😂',
    '😮',
    '😢',
    '😡',
    '🔥',
    '👏',
  ];
  bool _showPicker = false;
  bool _loading = false;
  String? _error;

  void _togglePicker() {
    setState(() {
      _showPicker = !_showPicker;
    });
  }

  Future<void> _onSelect(
    String emoji,
    Map<String, dynamic> userReactions,
  ) async {
    setState(() {
      _loading = true;
      _error = null;
    });
    final feedService = ref.read(feedServiceProvider);
    final currentUserId = widget.currentUserId;
    if (currentUserId == null) {
      setState(() {
        _loading = false;
        _error = 'Not authenticated.';
      });
      return;
    }
    final alreadyReacted = userReactions[currentUserId] == emoji;
    bool success;
    if (alreadyReacted) {
      success = await feedService.removeCommentReaction(
        postId: widget.postId,
        commentId: widget.commentId,
        emoji: emoji,
      );
    } else {
      success = await feedService.addCommentReaction(
        postId: widget.postId,
        commentId: widget.commentId,
        emoji: emoji,
      );
    }
    setState(() {
      _loading = false;
      _showPicker = false;
      if (!success) _error = 'Failed to update reaction.';
    });
  }

  @override
  Widget build(BuildContext context) {
    final postId = widget.postId;
    return StreamBuilder<QuerySnapshot>(
      stream: FirebaseFirestore.instance
          .collection('posts')
          .doc(postId)
          .collection('comments')
          .doc(widget.commentId)
          .collection('reactions')
          .snapshots(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const SizedBox(
            height: 24,
            child: Center(child: CircularProgressIndicator(strokeWidth: 2)),
          );
        }
        if (snapshot.hasError) {
          return const Text(
            'Error loading reactions',
            style: TextStyle(color: Colors.red),
          );
        }
        final docs = snapshot.data?.docs ?? [];
        // Group by emoji and count
        final Map<String, int> counts = {};
        final Map<String, String> userReactions = {};
        for (final doc in docs) {
          final data = doc.data() as Map<String, dynamic>;
          final emoji = data['emoji'] as String?;
          final userId = data['userId'] as String?;
          if (emoji != null) {
            counts[emoji] = (counts[emoji] ?? 0) + 1;
            if (userId != null) userReactions[userId] = emoji;
          }
        }
        final currentUserId = widget.currentUserId;
        final selected = currentUserId != null
            ? userReactions[currentUserId]
            : null;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                GestureDetector(
                  onTap: _togglePicker,
                  child: Icon(
                    Icons.emoji_emotions_outlined,
                    size: 20,
                    color: Theme.of(context).colorScheme.secondary,
                  ),
                ),
                const SizedBox(width: 6),
                ..._emojis
                    .where((e) => (counts[e] ?? 0) > 0)
                    .map(
                      (emoji) => Container(
                        margin: const EdgeInsets.symmetric(horizontal: 2),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: selected == emoji
                              ? Theme.of(
                                  context,
                                ).colorScheme.secondary.withValues(alpha: 0.18)
                              : Colors.transparent,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Text(
                              emoji,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: selected == emoji
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                              ),
                            ),
                            if ((counts[emoji] ?? 0) > 0)
                              Text(
                                ' ${counts[emoji]}',
                                style: const TextStyle(fontSize: 12),
                              ),
                          ],
                        ),
                      ),
                    ),
              ],
            ),
            if (_showPicker)
              Container(
                margin: const EdgeInsets.only(top: 4),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                decoration: BoxDecoration(
                  color: Theme.of(context).cardColor,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.07),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: _emojis
                      .map(
                        (emoji) => GestureDetector(
                          onTap: _loading
                              ? null
                              : () => _onSelect(emoji, userReactions),
                          child: Container(
                            margin: const EdgeInsets.symmetric(horizontal: 4),
                            padding: const EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: selected == emoji
                                  ? Theme.of(context).colorScheme.secondary
                                        .withValues(alpha: 0.18)
                                  : Colors.transparent,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              emoji,
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: selected == emoji
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                              ),
                            ),
                          ),
                        ),
                      )
                      .toList(),
                ),
              ),
            if (_error != null)
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  _error!,
                  style: const TextStyle(color: Colors.red, fontSize: 12),
                ),
              ),
          ],
        );
      },
    );
  }
}
