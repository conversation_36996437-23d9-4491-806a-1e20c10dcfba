import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';

/// Service for tracking analytics related to content discovery
class DiscoveryAnalyticsService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  String? get currentUserId => _auth.currentUser?.uid;

  /// Track when a user views a suggested account
  Future<void> trackSuggestedAccountView(
    String suggestedUserId,
    String reason,
  ) async {
    await _trackEvent('suggested_account_view', {
      'suggested_user_id': suggestedUserId,
      'reason': reason,
      'timestamp': FieldValue.serverTimestamp(),
    });
  }

  /// Track when a user follows a suggested account
  Future<void> trackSuggestedAccountFollow(
    String suggestedUserId,
    String reason,
  ) async {
    await _trackEvent('suggested_account_follow', {
      'suggested_user_id': suggestedUserId,
      'reason': reason,
      'timestamp': FieldValue.serverTimestamp(),
    });
  }

  /// Track when a user dismisses a suggested account
  Future<void> trackSuggestedAccountDismiss(
    String suggestedUserId,
    String reason,
  ) async {
    await _trackEvent('suggested_account_dismiss', {
      'suggested_user_id': suggestedUserId,
      'reason': reason,
      'timestamp': FieldValue.serverTimestamp(),
    });
  }

  /// Track when a user views discovery content
  Future<void> trackDiscoveryContentView(
    String postId,
    String postUserId,
    String contentType,
  ) async {
    await _trackEvent('discovery_content_view', {
      'post_id': postId,
      'post_user_id': postUserId,
      'content_type': contentType, // 'trending', 'suggested', 'popular'
      'timestamp': FieldValue.serverTimestamp(),
    });
  }

  /// Track when a user interacts with discovery content
  Future<void> trackDiscoveryContentInteraction(
    String postId,
    String postUserId,
    String interactionType,
    String contentType,
  ) async {
    await _trackEvent('discovery_content_interaction', {
      'post_id': postId,
      'post_user_id': postUserId,
      'interaction_type': interactionType, // 'like', 'comment', 'share', 'save'
      'content_type': contentType,
      'timestamp': FieldValue.serverTimestamp(),
    });
  }

  /// Track when a user follows someone from discovery content
  Future<void> trackDiscoveryContentFollow(
    String postId,
    String followedUserId,
    String contentType,
  ) async {
    await _trackEvent('discovery_content_follow', {
      'post_id': postId,
      'followed_user_id': followedUserId,
      'content_type': contentType,
      'timestamp': FieldValue.serverTimestamp(),
    });
  }

  /// Track when a user dismisses discovery content
  Future<void> trackDiscoveryContentDismiss(
    String postId,
    String postUserId,
    String contentType,
  ) async {
    await _trackEvent('discovery_content_dismiss', {
      'post_id': postId,
      'post_user_id': postUserId,
      'content_type': contentType,
      'timestamp': FieldValue.serverTimestamp(),
    });
  }

  /// Track content preference changes
  Future<void> trackContentPreferenceChange(
    String preferenceKey,
    dynamic oldValue,
    dynamic newValue,
  ) async {
    await _trackEvent('content_preference_change', {
      'preference_key': preferenceKey,
      'old_value': oldValue,
      'new_value': newValue,
      'timestamp': FieldValue.serverTimestamp(),
    });
  }

  /// Track discovery system performance metrics
  Future<void> trackDiscoveryMetrics({
    required int suggestedAccountsShown,
    required int suggestedAccountsFollowed,
    required int discoveryPostsShown,
    required int discoveryPostsInteracted,
    required double discoveryContentPercentage,
  }) async {
    await _trackEvent('discovery_metrics', {
      'suggested_accounts_shown': suggestedAccountsShown,
      'suggested_accounts_followed': suggestedAccountsFollowed,
      'discovery_posts_shown': discoveryPostsShown,
      'discovery_posts_interacted': discoveryPostsInteracted,
      'discovery_content_percentage': discoveryContentPercentage,
      'follow_conversion_rate': suggestedAccountsShown > 0
          ? suggestedAccountsFollowed / suggestedAccountsShown
          : 0.0,
      'interaction_rate': discoveryPostsShown > 0
          ? discoveryPostsInteracted / discoveryPostsShown
          : 0.0,
      'timestamp': FieldValue.serverTimestamp(),
    });
  }

  /// Track user onboarding progress
  Future<void> trackOnboardingStep(
    String step,
    Map<String, dynamic> additionalData,
  ) async {
    await _trackEvent('onboarding_step', {
      'step': step,
      'timestamp': FieldValue.serverTimestamp(),
      ...additionalData,
    });
  }

  /// Track feed empty state interactions
  Future<void> trackEmptyStateInteraction(String action, String context) async {
    await _trackEvent('empty_state_interaction', {
      'action': action, // 'create_post', 'discover_content', 'find_people'
      'context': context, // 'no_following', 'no_content', 'filtered_empty'
      'timestamp': FieldValue.serverTimestamp(),
    });
  }

  /// Get discovery analytics for a user
  Future<Map<String, dynamic>> getDiscoveryAnalytics(
    String userId, {
    int days = 30,
  }) async {
    try {
      final cutoffDate = DateTime.now().subtract(Duration(days: days));

      final analyticsSnapshot = await _firestore
          .collection('analytics')
          .doc(userId)
          .collection('discovery_events')
          .where('timestamp', isGreaterThan: cutoffDate)
          .get();

      final events = analyticsSnapshot.docs.map((doc) => doc.data()).toList();

      return _aggregateAnalytics(events);
    } catch (e) {
      debugPrint('❌ Error getting discovery analytics: $e');
      return {};
    }
  }

  /// Get system-wide discovery metrics
  Future<Map<String, dynamic>> getSystemDiscoveryMetrics({int days = 7}) async {
    try {
      // This would typically be done with a cloud function for performance
      // For now, we'll return placeholder data
      return {
        'total_suggestions_shown': 0,
        'total_follows_from_suggestions': 0,
        'total_discovery_posts_shown': 0,
        'total_discovery_interactions': 0,
        'average_follow_conversion_rate': 0.0,
        'average_interaction_rate': 0.0,
        'active_discovery_users': 0,
      };
    } catch (e) {
      debugPrint('❌ Error getting system discovery metrics: $e');
      return {};
    }
  }

  /// Private method to track events
  Future<void> _trackEvent(String eventType, Map<String, dynamic> data) async {
    final userId = currentUserId;
    if (userId == null) return;

    try {
      await _firestore
          .collection('analytics')
          .doc(userId)
          .collection('discovery_events')
          .add({'event_type': eventType, 'user_id': userId, ...data});

      debugPrint('📊 Analytics: Tracked $eventType for user $userId');
    } catch (e) {
      debugPrint('❌ Error tracking analytics event: $e');
    }
  }

  /// Aggregate analytics data
  Map<String, dynamic> _aggregateAnalytics(List<Map<String, dynamic>> events) {
    final analytics = <String, dynamic>{
      'total_events': events.length,
      'suggested_account_views': 0,
      'suggested_account_follows': 0,
      'suggested_account_dismissals': 0,
      'discovery_content_views': 0,
      'discovery_content_interactions': 0,
      'discovery_content_follows': 0,
      'discovery_content_dismissals': 0,
      'preference_changes': 0,
      'onboarding_steps': 0,
      'empty_state_interactions': 0,
    };

    for (final event in events) {
      final eventType = event['event_type'] as String?;
      if (eventType == null) continue;

      switch (eventType) {
        case 'suggested_account_view':
          analytics['suggested_account_views']++;
          break;
        case 'suggested_account_follow':
          analytics['suggested_account_follows']++;
          break;
        case 'suggested_account_dismiss':
          analytics['suggested_account_dismissals']++;
          break;
        case 'discovery_content_view':
          analytics['discovery_content_views']++;
          break;
        case 'discovery_content_interaction':
          analytics['discovery_content_interactions']++;
          break;
        case 'discovery_content_follow':
          analytics['discovery_content_follows']++;
          break;
        case 'discovery_content_dismiss':
          analytics['discovery_content_dismissals']++;
          break;
        case 'content_preference_change':
          analytics['preference_changes']++;
          break;
        case 'onboarding_step':
          analytics['onboarding_steps']++;
          break;
        case 'empty_state_interaction':
          analytics['empty_state_interactions']++;
          break;
      }
    }

    // Calculate conversion rates
    final suggestedViews = analytics['suggested_account_views'] as int;
    final suggestedFollows = analytics['suggested_account_follows'] as int;
    analytics['suggestion_follow_rate'] = suggestedViews > 0
        ? suggestedFollows / suggestedViews
        : 0.0;

    final discoveryViews = analytics['discovery_content_views'] as int;
    final discoveryInteractions =
        analytics['discovery_content_interactions'] as int;
    analytics['discovery_interaction_rate'] = discoveryViews > 0
        ? discoveryInteractions / discoveryViews
        : 0.0;

    return analytics;
  }

  /// Clean up old analytics data (call periodically)
  Future<void> cleanupOldAnalytics({int retentionDays = 90}) async {
    final userId = currentUserId;
    if (userId == null) return;

    try {
      final cutoffDate = DateTime.now().subtract(Duration(days: retentionDays));

      final oldEventsSnapshot = await _firestore
          .collection('analytics')
          .doc(userId)
          .collection('discovery_events')
          .where('timestamp', isLessThan: cutoffDate)
          .get();

      final batch = _firestore.batch();
      for (final doc in oldEventsSnapshot.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
      debugPrint(
        '🧹 Cleaned up ${oldEventsSnapshot.docs.length} old analytics events',
      );
    } catch (e) {
      debugPrint('❌ Error cleaning up old analytics: $e');
    }
  }
}
