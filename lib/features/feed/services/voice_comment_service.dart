import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:record/record.dart';
import 'package:just_audio/just_audio.dart';
import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

class VoiceCommentService {
  static final VoiceCommentService _instance = VoiceCommentService._internal();
  factory VoiceCommentService() => _instance;
  VoiceCommentService._internal();

  final AudioRecorder _recorder = AudioRecorder();
  final AudioPlayer _player = AudioPlayer();
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  PlayerController? _playerController;
  RecorderController? _recorderController;

  // Getters for controllers
  PlayerController? get playerController => _playerController;
  RecorderController? get recorderController => _recorderController;

  bool _isRecording = false;
  bool _isPlaying = false;
  String? _currentRecordingPath;

  bool get isRecording => _isRecording;
  bool get isPlaying => _isPlaying;
  String? get currentRecordingPath => _currentRecordingPath;

  /// Initialize controllers for waveform visualization
  void initializeControllers() {
    _playerController = PlayerController();
    _recorderController = RecorderController()
      ..androidEncoder = AndroidEncoder.aac
      ..androidOutputFormat = AndroidOutputFormat.mpeg4
      ..iosEncoder = IosEncoder.kAudioFormatMPEG4AAC
      ..sampleRate = 44100;
  }

  /// Dispose controllers
  void dispose() {
    _playerController?.dispose();
    _recorderController?.dispose();
    _recorder.dispose();
    _player.dispose();
  }

  /// Check and request microphone permission
  Future<bool> checkMicrophonePermission() async {
    final status = await Permission.microphone.status;
    if (status.isDenied) {
      final result = await Permission.microphone.request();
      return result.isGranted;
    }
    return status.isGranted;
  }

  /// Start recording voice comment
  Future<bool> startRecording() async {
    try {
      if (_isRecording) return false;

      final hasPermission = await checkMicrophonePermission();
      if (!hasPermission) {
        throw Exception('Microphone permission denied');
      }

      // Get temporary directory for recording
      final tempDir = await getTemporaryDirectory();
      final fileName =
          'voice_comment_${DateTime.now().millisecondsSinceEpoch}.m4a';
      _currentRecordingPath = '${tempDir.path}/$fileName';

      // Start recording with both services for compatibility
      await _recorder.start(
        RecordConfig(
          encoder: AudioEncoder.aacLc,
          bitRate: 128000,
          sampleRate: 44100,
        ),
        path: _currentRecordingPath!,
      );

      // Start waveform recording if controller is available
      if (_recorderController != null) {
        await _recorderController!.record(path: _currentRecordingPath!);
      }

      _isRecording = true;
      debugPrint('🎙️ Started recording voice comment: $_currentRecordingPath');
      return true;
    } catch (e) {
      debugPrint('❌ Error starting recording: $e');
      return false;
    }
  }

  /// Stop recording voice comment
  Future<String?> stopRecording() async {
    try {
      if (!_isRecording) return null;

      final path = await _recorder.stop();

      // Stop waveform recording
      if (_recorderController != null) {
        await _recorderController!.stop();
      }

      _isRecording = false;

      if (path != null) {
        _currentRecordingPath = path;
        debugPrint('✅ Stopped recording voice comment: $path');
        return path;
      }

      return _currentRecordingPath;
    } catch (e) {
      debugPrint('❌ Error stopping recording: $e');
      _isRecording = false;
      return null;
    }
  }

  /// Cancel current recording
  Future<void> cancelRecording() async {
    try {
      if (_isRecording) {
        await _recorder.stop();
        if (_recorderController != null) {
          await _recorderController!.stop();
        }
        _isRecording = false;
      }

      // Delete the recording file if it exists
      if (_currentRecordingPath != null) {
        final file = File(_currentRecordingPath!);
        if (await file.exists()) {
          await file.delete();
        }
        _currentRecordingPath = null;
      }

      debugPrint('🗑️ Cancelled voice recording');
    } catch (e) {
      debugPrint('❌ Error cancelling recording: $e');
    }
  }

  /// Get recording duration
  Future<int?> getRecordingDuration(String filePath) async {
    try {
      await _player.setFilePath(filePath);
      final duration = _player.duration;
      return duration?.inSeconds;
    } catch (e) {
      debugPrint('❌ Error getting recording duration: $e');
      return null;
    }
  }

  /// Play voice comment
  Future<bool> playVoiceComment(String filePath) async {
    try {
      if (_isPlaying) {
        await stopPlayback();
      }

      await _player.setFilePath(filePath);
      await _player.play();
      _isPlaying = true;

      // Listen for playback completion
      _player.playerStateStream.listen((state) {
        if (state.processingState == ProcessingState.completed) {
          _isPlaying = false;
        }
      });

      debugPrint('▶️ Playing voice comment: $filePath');
      return true;
    } catch (e) {
      debugPrint('❌ Error playing voice comment: $e');
      return false;
    }
  }

  /// Stop playback
  Future<void> stopPlayback() async {
    try {
      await _player.stop();
      _isPlaying = false;
      debugPrint('⏹️ Stopped voice playback');
    } catch (e) {
      debugPrint('❌ Error stopping playback: $e');
    }
  }

  /// Upload voice comment to Firebase Storage
  Future<String?> uploadVoiceComment(String filePath, String postId) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('Voice file not found');
      }

      final fileName =
          'voice_comment_${DateTime.now().millisecondsSinceEpoch}.m4a';
      final ref = _storage
          .ref()
          .child('voice_comments')
          .child(postId)
          .child(currentUser.uid)
          .child(fileName);

      final uploadTask = ref.putFile(file);
      final snapshot = await uploadTask;
      final downloadUrl = await snapshot.ref.getDownloadURL();

      debugPrint('☁️ Uploaded voice comment: $downloadUrl');
      return downloadUrl;
    } catch (e) {
      debugPrint('❌ Error uploading voice comment: $e');
      return null;
    }
  }

  /// Generate waveform data from audio file
  Future<String?> generateWaveformData(String filePath) async {
    try {
      if (_playerController == null) return null;

      await _playerController!.preparePlayer(
        path: filePath,
        shouldExtractWaveform: true,
      );

      // Get waveform data
      final waveformData = _playerController!.waveformData;
      if (waveformData.isNotEmpty) {
        // Convert to JSON string for storage
        return waveformData.join(',');
      }

      return null;
    } catch (e) {
      debugPrint('❌ Error generating waveform: $e');
      return null;
    }
  }

  /// Clean up temporary files
  Future<void> cleanupTempFiles() async {
    try {
      final tempDir = await getTemporaryDirectory();
      final files = tempDir.listSync();

      for (final file in files) {
        if (file.path.contains('voice_comment_') &&
            file.path.endsWith('.m4a')) {
          await file.delete();
        }
      }

      debugPrint('🧹 Cleaned up temporary voice files');
    } catch (e) {
      debugPrint('❌ Error cleaning up temp files: $e');
    }
  }
}
