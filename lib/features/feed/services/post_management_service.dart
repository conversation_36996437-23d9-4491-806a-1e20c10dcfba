import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/features/feed/services/post_permission_service.dart';

/// Service to handle post management actions (edit, archive, moderate, etc.)
class PostManagementService {
  static final PostManagementService _instance =
      PostManagementService._internal();
  factory PostManagementService() => _instance;
  PostManagementService._internal();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final PostPermissionService _permissionService = PostPermissionService();

  /// Edit a post (owner only)
  Future<void> editPost({
    required String postId,
    required String newCaption,
    String? newLocation,
    List<String>? newHashtags,
    List<String>? newMentionedUsers,
  }) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    // Get the post first
    final postDoc = await _firestore.collection('posts').doc(postId).get();
    if (!postDoc.exists) {
      throw Exception('Post not found');
    }

    final postData = postDoc.data()!;

    // Ensure required fields have default values to prevent null casting errors
    final safePostData = {
      ...postData,
      'id': postId,
      'userId': postData['userId'] ?? '',
      'username': postData['username'] ?? '',
      'userAvatarUrl': postData['userAvatarUrl'] ?? '',
      'mediaUrl': postData['mediaUrl'] ?? '',
      'caption': postData['caption'] ?? '',
    };

    final post = Post.fromJson(safePostData);

    // Check permissions
    if (!await _permissionService.canEditPost(post)) {
      throw Exception('You cannot edit this post');
    }

    final updateData = <String, dynamic>{
      'caption': newCaption,
      'updatedAt': FieldValue.serverTimestamp(),
      'lastEditedAt': FieldValue.serverTimestamp(),
      'editedBy': currentUser.uid,
    };

    if (newLocation != null) {
      updateData['location'] = newLocation;
    }

    if (newHashtags != null) {
      updateData['hashtags'] = newHashtags;
    }

    if (newMentionedUsers != null) {
      updateData['mentionedUsers'] = newMentionedUsers;
    }

    await _firestore.collection('posts').doc(postId).update(updateData);
    debugPrint('✅ Post edited successfully: $postId');
  }

  /// Update post settings (owner only)
  Future<void> updatePostSettings({
    required String postId,
    String? visibility,
    bool? allowComments,
    bool? allowRemix,
    bool? allowRepost,
    bool? allowShare,
    bool? hideLikes,
  }) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    // Get the post first
    final postDoc = await _firestore.collection('posts').doc(postId).get();
    if (!postDoc.exists) {
      throw Exception('Post not found');
    }

    final postData = postDoc.data()!;
    final post = Post.fromJson({...postData, 'id': postId});

    // Check permissions
    if (!await _permissionService.canEditPost(post)) {
      throw Exception('You cannot update this post settings');
    }

    final updateData = <String, dynamic>{
      'updatedAt': FieldValue.serverTimestamp(),
    };

    if (visibility != null) {
      updateData['visibility'] = visibility;
    }

    if (allowComments != null) {
      updateData['commentsDisabled'] = !allowComments;
    }

    if (allowRemix != null) {
      updateData['allowRemix'] = allowRemix;
    }

    if (allowRepost != null) {
      updateData['allowRepost'] = allowRepost;
    }

    if (allowShare != null) {
      updateData['allowShare'] = allowShare;
    }

    if (hideLikes != null) {
      updateData['likesHidden'] = hideLikes;
    }

    await _firestore.collection('posts').doc(postId).update(updateData);
    debugPrint('✅ Post settings updated successfully: $postId');
  }

  /// Archive a post (owner only)
  Future<void> archivePost(String postId) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    // Get the post first
    final postDoc = await _firestore.collection('posts').doc(postId).get();
    if (!postDoc.exists) {
      throw Exception('Post not found');
    }

    final postData = postDoc.data()!;

    // Ensure required fields have default values to prevent null casting errors
    final safePostData = {
      ...postData,
      'id': postId,
      'userId': postData['userId'] ?? '',
      'username': postData['username'] ?? '',
      'userAvatarUrl': postData['userAvatarUrl'] ?? '',
      'mediaUrl': postData['mediaUrl'] ?? '',
      'caption': postData['caption'] ?? '',
    };

    final post = Post.fromJson(safePostData);

    // Check permissions
    if (!await _permissionService.canArchivePost(post)) {
      throw Exception('You cannot archive this post');
    }

    await _firestore.collection('posts').doc(postId).update({
      'isArchived': true,
      'status': 'archived',
      'archivedAt': FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
    });

    debugPrint('✅ Post archived successfully: $postId');
  }

  /// Unarchive a post (owner only)
  Future<void> unarchivePost(String postId) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    // Get the post first
    final postDoc = await _firestore.collection('posts').doc(postId).get();
    if (!postDoc.exists) {
      throw Exception('Post not found');
    }

    final postData = postDoc.data()!;

    // Ensure required fields have default values to prevent null casting errors
    final safePostData = {
      ...postData,
      'id': postId,
      'userId': postData['userId'] ?? '',
      'username': postData['username'] ?? '',
      'userAvatarUrl': postData['userAvatarUrl'] ?? '',
      'mediaUrl': postData['mediaUrl'] ?? '',
      'caption': postData['caption'] ?? '',
    };

    final post = Post.fromJson(safePostData);

    // Check permissions
    if (!await _permissionService.canArchivePost(post)) {
      throw Exception('You cannot unarchive this post');
    }

    await _firestore.collection('posts').doc(postId).update({
      'isArchived': false,
      'status': 'active',
      'archivedAt': FieldValue.delete(),
      'updatedAt': FieldValue.serverTimestamp(),
    });

    debugPrint('✅ Post unarchived successfully: $postId');
  }

  /// Pin a post to profile (owner only)
  Future<void> pinPost(String postId) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    // Get the post first
    final postDoc = await _firestore.collection('posts').doc(postId).get();
    if (!postDoc.exists) {
      throw Exception('Post not found');
    }

    final postData = postDoc.data()!;
    final post = Post.fromJson({...postData, 'id': postId});

    // Check permissions
    if (!await _permissionService.canPinPost(post)) {
      throw Exception('You cannot pin this post');
    }

    // Unpin any existing pinned posts for this user
    await _firestore
        .collection('posts')
        .where('userId', isEqualTo: currentUser.uid)
        .where('isPinned', isEqualTo: true)
        .get()
        .then((snapshot) {
          for (final doc in snapshot.docs) {
            doc.reference.update({'isPinned': false});
          }
        });

    // Pin the new post
    await _firestore.collection('posts').doc(postId).update({
      'isPinned': true,
      'pinnedAt': FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
    });

    debugPrint('✅ Post pinned successfully: $postId');
  }

  /// Unpin a post (owner only)
  Future<void> unpinPost(String postId) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    // Get the post first
    final postDoc = await _firestore.collection('posts').doc(postId).get();
    if (!postDoc.exists) {
      throw Exception('Post not found');
    }

    final postData = postDoc.data()!;
    final post = Post.fromJson({...postData, 'id': postId});

    // Check permissions
    if (!await _permissionService.canPinPost(post)) {
      throw Exception('You cannot unpin this post');
    }

    await _firestore.collection('posts').doc(postId).update({
      'isPinned': false,
      'pinnedAt': FieldValue.delete(),
      'updatedAt': FieldValue.serverTimestamp(),
    });

    debugPrint('✅ Post unpinned successfully: $postId');
  }

  /// Hide a post (admin/moderator only)
  Future<void> hidePost(String postId, String reason) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    // Get the post first
    final postDoc = await _firestore.collection('posts').doc(postId).get();
    if (!postDoc.exists) {
      throw Exception('Post not found');
    }

    final postData = postDoc.data()!;

    // Ensure required fields have default values to prevent null casting errors
    final safePostData = {
      ...postData,
      'id': postId,
      'userId': postData['userId'] ?? '',
      'username': postData['username'] ?? '',
      'userAvatarUrl': postData['userAvatarUrl'] ?? '',
      'mediaUrl': postData['mediaUrl'] ?? '',
      'caption': postData['caption'] ?? '',
    };

    final post = Post.fromJson(safePostData);

    // Check permissions
    if (!await _permissionService.canModeratePost(post)) {
      throw Exception('You cannot moderate this post');
    }

    await _firestore.collection('posts').doc(postId).update({
      'status': 'hidden',
      'moderationAction': 'hidden',
      'moderationReason': reason,
      'moderatedBy': currentUser.uid,
      'moderatedAt': FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
    });

    debugPrint('✅ Post hidden successfully: $postId');
  }

  /// Restore a hidden post (admin/moderator only)
  Future<void> restorePost(String postId) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    // Get the post first
    final postDoc = await _firestore.collection('posts').doc(postId).get();
    if (!postDoc.exists) {
      throw Exception('Post not found');
    }

    final postData = postDoc.data()!;
    final post = Post.fromJson({...postData, 'id': postId});

    // Check permissions
    if (!await _permissionService.canModeratePost(post)) {
      throw Exception('You cannot moderate this post');
    }

    await _firestore.collection('posts').doc(postId).update({
      'status': 'active',
      'moderationAction': FieldValue.delete(),
      'moderationReason': FieldValue.delete(),
      'moderatedBy': FieldValue.delete(),
      'moderatedAt': FieldValue.delete(),
      'updatedAt': FieldValue.serverTimestamp(),
    });

    debugPrint('✅ Post restored successfully: $postId');
  }
}
