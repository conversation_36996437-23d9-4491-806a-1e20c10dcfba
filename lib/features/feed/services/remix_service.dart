import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/features/feed/services/post_permission_service.dart';
import 'package:billionaires_social/core/services/analytics_service.dart';
import 'package:billionaires_social/features/notifications/services/notification_service.dart';
import 'package:billionaires_social/core/service_locator.dart';

/// Service to handle post remixing functionality
class RemixService {
  static final RemixService _instance = RemixService._internal();
  factory RemixService() => _instance;
  RemixService._internal();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final PostPermissionService _permissionService = PostPermissionService();
  final AnalyticsService _analyticsService = getIt<AnalyticsService>();
  final NotificationService _notificationService = getIt<NotificationService>();

  /// Create a remix of an existing post
  Future<String> createRemix({
    required String originalPostId,
    required String newCaption,
    String? newLocation,
    List<String>? newHashtags,
    List<String>? newMentionedUsers,
    String? visibility = 'public',
  }) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    // Get the original post
    final originalPostDoc = await _firestore
        .collection('posts')
        .doc(originalPostId)
        .get();
    if (!originalPostDoc.exists) {
      throw Exception('Original post not found');
    }

    final originalPostData = originalPostDoc.data()!;
    final originalPost = Post.fromJson({
      ...originalPostData,
      'id': originalPostId,
    });

    // Check permissions
    if (!await _permissionService.canRemixPost(originalPost)) {
      throw Exception('You cannot remix this post');
    }

    // Get current user profile
    final userDoc = await _firestore
        .collection('users')
        .doc(currentUser.uid)
        .get();
    final userData = userDoc.data() ?? {};

    // Create remix data
    final remixData = {
      'userId': currentUser.uid,
      'username': userData['username'] ?? 'Unknown',
      'userAvatarUrl': userData['profilePictureUrl'] ?? '',
      'userRole': userData['userRole'],
      'isVerified': userData['isVerified'] ?? false,
      'mediaType': originalPostData['mediaType'],
      'mediaUrl': originalPostData['mediaUrl'],
      'caption': newCaption,
      'location': newLocation,
      'hashtags': newHashtags ?? [],
      'mentionedUsers': newMentionedUsers ?? [],
      'originalPostId': originalPostId,
      'originalUserId': originalPost.userId,
      'originalUsername': originalPost.username,
      'isRemix': true,
      'allowRemix': true,
      'allowRepost': true,
      'allowShare': true,
      'visibility': visibility,
      'status': 'active',
      'likeCount': 0,
      'commentCount': 0,
      'shareCount': 0,
      'repostCount': 0,
      'viewCount': 0,
      'createdAt': FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
    };

    // Add remix to Firestore
    final remixRef = await _firestore.collection('posts').add(remixData);

    // Increment user's post count (since remix is also a post)
    await _firestore.collection('users').doc(currentUser.uid).update({
      'postCount': FieldValue.increment(1),
      'updatedAt': FieldValue.serverTimestamp(),
    });
    debugPrint(
      '✅ User post count incremented for remix user: ${currentUser.uid}',
    );

    // Update original post's remix count
    await _firestore.collection('posts').doc(originalPostId).update({
      'remixCount': FieldValue.increment(1),
      'updatedAt': FieldValue.serverTimestamp(),
    });

    // Send notification to original post owner
    if (originalPost.userId != currentUser.uid) {
      await _notificationService.createRemixNotification(
        originalPostId,
        originalPost.userId,
        remixRef.id,
      );
    }

    // Log analytics
    await _analyticsService.logPostRemixed(
      originalPostId: originalPostId,
      remixPostId: remixRef.id,
      originalAuthorId: originalPost.userId,
    );

    debugPrint('✅ Remix created successfully: ${remixRef.id}');
    return remixRef.id;
  }

  /// Get all remixes of a post
  Future<List<Post>> getPostRemixes(String originalPostId) async {
    final remixesSnapshot = await _firestore
        .collection('posts')
        .where('originalPostId', isEqualTo: originalPostId)
        .where('isRemix', isEqualTo: true)
        .where('status', isEqualTo: 'active')
        .orderBy('createdAt', descending: true)
        .get();

    final remixes = <Post>[];
    for (final doc in remixesSnapshot.docs) {
      final data = doc.data();
      final remix = Post.fromJson({...data, 'id': doc.id});

      // Check if user can view this remix
      if (await _permissionService.canViewPost(remix)) {
        remixes.add(remix);
      }
    }

    return remixes;
  }

  /// Get remixes created by a specific user
  Future<List<Post>> getUserRemixes(String userId) async {
    final remixesSnapshot = await _firestore
        .collection('posts')
        .where('userId', isEqualTo: userId)
        .where('isRemix', isEqualTo: true)
        .where('status', isEqualTo: 'active')
        .orderBy('createdAt', descending: true)
        .get();

    final remixes = <Post>[];
    for (final doc in remixesSnapshot.docs) {
      final data = doc.data();
      final remix = Post.fromJson({...data, 'id': doc.id});

      // Check if user can view this remix
      if (await _permissionService.canViewPost(remix)) {
        remixes.add(remix);
      }
    }

    return remixes;
  }

  /// Get the original post for a remix
  Future<Post?> getOriginalPost(String remixPostId) async {
    final remixDoc = await _firestore
        .collection('posts')
        .doc(remixPostId)
        .get();
    if (!remixDoc.exists) return null;

    final remixData = remixDoc.data()!;
    final originalPostId = remixData['originalPostId'] as String?;

    if (originalPostId == null) return null;

    final originalPostDoc = await _firestore
        .collection('posts')
        .doc(originalPostId)
        .get();
    if (!originalPostDoc.exists) return null;

    final originalPostData = originalPostDoc.data()!;
    final originalPost = Post.fromJson({
      ...originalPostData,
      'id': originalPostId,
    });

    // Check if user can view the original post
    if (await _permissionService.canViewPost(originalPost)) {
      return originalPost;
    }

    return null;
  }

  /// Check if a post is a remix
  bool isRemix(Post post) {
    return post.isRemix && post.originalPostId != null;
  }

  /// Get remix chain (original -> remix -> remix of remix, etc.)
  Future<List<Post>> getRemixChain(String postId) async {
    final chain = <Post>[];
    String? currentPostId = postId;

    while (currentPostId != null) {
      final postDoc = await _firestore
          .collection('posts')
          .doc(currentPostId)
          .get();
      if (!postDoc.exists) break;

      final postData = postDoc.data()!;
      final post = Post.fromJson({...postData, 'id': currentPostId});

      // Check if user can view this post
      if (await _permissionService.canViewPost(post)) {
        chain.add(post);
      }

      // Move to the original post if this is a remix
      if (post.isRemix && post.originalPostId != null) {
        currentPostId = post.originalPostId;
      } else {
        break;
      }
    }

    return chain.reversed
        .toList(); // Return in chronological order (original first)
  }

  /// Get remix statistics for a post
  Future<Map<String, dynamic>> getRemixStats(String postId) async {
    final remixesSnapshot = await _firestore
        .collection('posts')
        .where('originalPostId', isEqualTo: postId)
        .where('isRemix', isEqualTo: true)
        .where('status', isEqualTo: 'active')
        .get();

    final totalRemixes = remixesSnapshot.docs.length;
    final remixers = <String>{};
    var totalLikes = 0;
    var totalComments = 0;

    for (final doc in remixesSnapshot.docs) {
      final data = doc.data();
      remixers.add(data['userId'] as String);
      totalLikes += (data['likeCount'] as int? ?? 0);
      totalComments += (data['commentCount'] as int? ?? 0);
    }

    return {
      'totalRemixes': totalRemixes,
      'uniqueRemixers': remixers.length,
      'totalLikesOnRemixes': totalLikes,
      'totalCommentsOnRemixes': totalComments,
      'remixEngagementRate': totalRemixes > 0
          ? (totalLikes + totalComments) / totalRemixes
          : 0.0,
    };
  }

  /// Delete a remix (only by remix creator or admin)
  Future<void> deleteRemix(String remixId) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    // Get the remix
    final remixDoc = await _firestore.collection('posts').doc(remixId).get();
    if (!remixDoc.exists) {
      throw Exception('Remix not found');
    }

    final remixData = remixDoc.data()!;
    final remix = Post.fromJson({...remixData, 'id': remixId});

    // Check permissions
    if (!await _permissionService.canDeletePost(remix)) {
      throw Exception('You cannot delete this remix');
    }

    // Soft delete the remix
    await _firestore.collection('posts').doc(remixId).update({
      'status': 'deleted',
      'isDeleted': true,
      'deletedAt': FieldValue.serverTimestamp(),
      'deletedBy': currentUser.uid,
      'updatedAt': FieldValue.serverTimestamp(),
    });

    // Decrement user's post count (since remix is also a post)
    await _firestore.collection('users').doc(remix.userId).update({
      'postCount': FieldValue.increment(-1),
      'updatedAt': FieldValue.serverTimestamp(),
    });
    debugPrint('✅ User post count decremented for remix user: ${remix.userId}');

    // Decrement original post's remix count
    if (remix.originalPostId != null) {
      await _firestore.collection('posts').doc(remix.originalPostId!).update({
        'remixCount': FieldValue.increment(-1),
        'updatedAt': FieldValue.serverTimestamp(),
      });
    }

    debugPrint('✅ Remix deleted successfully: $remixId');
  }
}
