import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/core/services/role_service.dart';

/// Service to handle post permissions based on user roles
class PostPermissionService {
  static final PostPermissionService _instance =
      PostPermissionService._internal();
  factory PostPermissionService() => _instance;
  PostPermissionService._internal();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final RoleService _roleService = RoleService();

  /// Get the current user's role in relation to a post
  Future<UserPostRole> getUserPostRole(Post post) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return UserPostRole.viewer;

    // Check if user is the post owner
    if (post.userId == currentUser.uid) {
      return UserPostRole.owner;
    }

    // Check if user is admin/moderator
    final userRole = await _roleService.getUserRole(currentUser.uid);
    if (userRole == UserRole.admin) {
      return UserPostRole.admin;
    }

    // Default to viewer
    return UserPostRole.viewer;
  }

  /// Check if user can view a post based on visibility settings
  Future<bool> canViewPost(Post post) async {
    final currentUser = _auth.currentUser;

    // Public posts are visible to everyone
    if (post.visibility == 'public' && post.status == 'active') {
      return true;
    }

    // If no user is logged in, only public posts are visible
    if (currentUser == null) {
      return post.visibility == 'public' && post.status == 'active';
    }

    // Post owner can always view their posts
    if (post.userId == currentUser.uid) {
      return true;
    }

    // Admins can view all posts
    final userRole = await _roleService.getUserRole(currentUser.uid);
    if (userRole == UserRole.admin) {
      return true;
    }

    // Check visibility rules
    switch (post.visibility) {
      case 'public':
        return post.status == 'active';
      case 'followers':
        return await _isFollowing(currentUser.uid, post.userId);
      case 'closeFriends':
        return await _isInCloseFriends(
          currentUser.uid,
          post.userId,
          post.closeFriendsGroupIds,
        );
      case 'private':
        return false; // Only owner and admins can see private posts
      default:
        return false;
    }
  }

  /// Check if user can edit a post
  Future<bool> canEditPost(Post post) async {
    final userRole = await getUserPostRole(post);
    return userRole == UserPostRole.owner;
  }

  /// Check if user can delete a post
  Future<bool> canDeletePost(Post post) async {
    final userRole = await getUserPostRole(post);
    return userRole == UserPostRole.owner || userRole == UserPostRole.admin;
  }

  /// Check if user can like a post
  Future<bool> canLikePost(Post post) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      debugPrint('❌ Like permission denied: No authenticated user');
      return false;
    }

    // Allow users to like their own posts (common in social media)
    // This was previously blocking own posts: if (post.userId == currentUser.uid) return false;

    // Must be able to view the post
    final canView = await canViewPost(post);
    debugPrint(
      '👍 Like permission for post ${post.id}: $canView (user: ${currentUser.uid}, owner: ${post.userId})',
    );
    return canView;
  }

  /// Check if user can comment on a post
  Future<bool> canCommentOnPost(Post post) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return false;

    // Check if comments are disabled
    if (post.commentsDisabled) {
      final userRole = await getUserPostRole(post);
      return userRole == UserPostRole.owner || userRole == UserPostRole.admin;
    }

    // Must be able to view the post
    return await canViewPost(post);
  }

  /// Check if user can share a post
  Future<bool> canSharePost(Post post) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return false;

    // Check if sharing is allowed
    if (!post.allowShare) return false;

    // Must be able to view the post
    return await canViewPost(post);
  }

  /// Check if user can repost a post
  Future<bool> canRepostPost(Post post) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return false;

    // Can't repost your own post
    if (post.userId == currentUser.uid) return false;

    // Check if reposting is allowed
    if (!post.allowRepost) return false;

    // Must be able to view the post
    return await canViewPost(post);
  }

  /// Check if user can remix a post
  Future<bool> canRemixPost(Post post) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return false;

    // Can't remix your own post
    if (post.userId == currentUser.uid) return false;

    // Check if remixing is allowed
    if (!post.allowRemix) return false;

    // Must be able to view the post
    return await canViewPost(post);
  }

  /// Check if user can report a post
  Future<bool> canReportPost(Post post) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return false;

    // Can't report your own post
    if (post.userId == currentUser.uid) return false;

    // Must be able to view the post
    return await canViewPost(post);
  }

  /// Check if user can see post insights/analytics
  Future<bool> canViewPostInsights(Post post) async {
    final userRole = await getUserPostRole(post);
    return userRole == UserPostRole.owner || userRole == UserPostRole.admin;
  }

  /// Check if user can moderate a post (hide, flag, etc.)
  Future<bool> canModeratePost(Post post) async {
    final userRole = await getUserPostRole(post);
    return userRole == UserPostRole.admin;
  }

  /// Check if user can pin/unpin a post
  Future<bool> canPinPost(Post post) async {
    final userRole = await getUserPostRole(post);
    return userRole == UserPostRole.owner;
  }

  /// Check if user can archive a post
  Future<bool> canArchivePost(Post post) async {
    final userRole = await getUserPostRole(post);
    return userRole == UserPostRole.owner;
  }

  /// Check if user can see who liked a post
  Future<bool> canViewLikers(Post post) async {
    // If likes are hidden, only owner and admins can see
    if (post.likesHidden) {
      final userRole = await getUserPostRole(post);
      return userRole == UserPostRole.owner || userRole == UserPostRole.admin;
    }

    // Otherwise, anyone who can view the post can see likes
    return await canViewPost(post);
  }

  /// Check if user can tag others in a post
  Future<bool> canTagUsers(Post post) async {
    final userRole = await getUserPostRole(post);
    return userRole == UserPostRole.owner;
  }

  // Helper methods
  Future<bool> _isFollowing(String followerId, String followeeId) async {
    try {
      final doc = await _firestore
          .collection('users')
          .doc(followerId)
          .collection('following')
          .doc(followeeId)
          .get();
      return doc.exists;
    } catch (e) {
      debugPrint('Error checking follow status: $e');
      return false;
    }
  }

  Future<bool> _isInCloseFriends(
    String userId,
    String postOwnerId,
    List<String>? groupIds,
  ) async {
    if (groupIds == null || groupIds.isEmpty) return false;

    try {
      for (final groupId in groupIds) {
        final doc = await _firestore
            .collection('users')
            .doc(postOwnerId)
            .collection('closeFriendsGroups')
            .doc(groupId)
            .get();

        if (doc.exists) {
          final members = List<String>.from(doc.data()?['members'] ?? []);
          if (members.contains(userId)) {
            return true;
          }
        }
      }
      return false;
    } catch (e) {
      debugPrint('Error checking close friends status: $e');
      return false;
    }
  }
}
