import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/features/feed/models/liker_model.dart';
import 'package:billionaires_social/features/profile/models/profile_model.dart';
import 'package:billionaires_social/features/profile/services/profile_service.dart';
import 'package:billionaires_social/core/services/firebase_service.dart';
import 'package:billionaires_social/core/services/analytics_service.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/feed/models/comment_model.dart';
import 'package:billionaires_social/features/notifications/services/notification_service.dart';
import 'package:billionaires_social/core/services/content_moderation_service.dart';
import 'package:billionaires_social/features/feed/models/reaction_model.dart';

final feedServiceProvider = Provider<FeedService>((ref) {
  return getIt<FeedService>();
});

class FeedService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseService _firebaseService = getIt<FirebaseService>();
  final AnalyticsService _analyticsService = getIt<AnalyticsService>();
  final NotificationService _notificationService = getIt<NotificationService>();
  ContentModerationService get _moderationService =>
      getIt<ContentModerationService>();
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Get current user ID
  String? get currentUserId => _auth.currentUser?.uid;

  // Get current user with null check
  User? get currentUser => _auth.currentUser;

  // Get posts for feed with real-time updates and permission filtering
  Stream<List<Post>> getFeedPostsStream({
    String? lastPostId,
    int limit = 10,
    List<String>? filteredUsers,
  }) {
    Query query = _firestore
        .collection('posts')
        .where('status', isEqualTo: 'active') // Only show active posts
        .orderBy('createdAt', descending: true)
        .limit(limit);

    if (filteredUsers != null && filteredUsers.isNotEmpty) {
      query = query.where('userId', whereIn: filteredUsers);
    }

    return query.snapshots().asyncMap((snapshot) async {
      final posts = <Post>[];
      final currentUser = _auth.currentUser;

      // Batch bookmark checks for better performance
      Map<String, bool> bookmarkCache = {};
      if (currentUser != null) {
        final postIds = snapshot.docs.map((doc) => doc.id).toList();
        bookmarkCache = await _batchCheckBookmarks(postIds, currentUser.uid);
      }

      for (final doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;

        final isBookmarked = bookmarkCache[doc.id] ?? false;

        final post = Post(
          id: doc.id,
          userId: data['userId'] ?? '',
          username: data['username'] ?? '',
          userAvatarUrl: data['userAvatarUrl'] ?? '',
          userRole: data['userRole'],
          isVerified: data['isVerified'],
          mediaType: MediaType.values.firstWhere(
            (e) => e.name == (data['mediaType'] ?? 'image'),
            orElse: () => MediaType.image,
          ),
          mediaUrl: data['mediaUrl'] ?? '',
          caption: data['caption'] ?? '',
          location: data['location'],
          likeCount: data['likeCount'] ?? 0,
          commentCount: data['commentCount'] ?? 0,
          timestamp:
              (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
          isLiked: data['isLiked'] ?? false,
          isBookmarked: isBookmarked,
          isArchived: data['isArchived'] ?? false,
          isPublic: data['isPublic'] ?? true,
          visibility: data['visibility'] ?? 'public',
          isPinned: data['isPinned'] ?? false,
          coAuthorIds: List<String>.from(data['coAuthorIds'] ?? []),
          coAuthorUsernames: List<String>.from(data['coAuthorUsernames'] ?? []),
          coAuthorAvatars: List<String>.from(data['coAuthorAvatars'] ?? []),
          mentionedUsers: List<String>.from(data['mentionedUsers'] ?? []),
          hashtags: List<String>.from(data['hashtags'] ?? []),
          mediaTags: (data['mediaTags'] as List<dynamic>?)
              ?.map((tag) => MediaTag.fromJson(tag as Map<String, dynamic>))
              .toList(),
        );

        // Add post to list - permission filtering moved to batch processing
        posts.add(post);
      }

      // Batch filter posts by permissions for better performance
      final filteredPosts = await _batchFilterPostsByPermissions(posts);
      return filteredPosts;
    });
  }

  // Get posts for feed (non-streaming version for compatibility)
  Future<List<Post>> fetchFeedPosts({
    String? lastPostId,
    int limit = 10,
    List<String>? filteredUsers,
  }) async {
    Query query = _firestore
        .collection('posts')
        .where('status', isEqualTo: 'active') // Filter out deleted posts
        .orderBy('createdAt', descending: true)
        .limit(limit);

    if (filteredUsers != null && filteredUsers.isNotEmpty) {
      query = query.where('userId', whereIn: filteredUsers);
    }

    final snapshot = await query.get();
    final currentUser = _auth.currentUser;
    final posts = <Post>[];

    for (final doc in snapshot.docs) {
      final data = doc.data() as Map<String, dynamic>;

      // Check if current user has bookmarked this post
      final isBookmarked = currentUser != null
          ? await _checkUserBookmark(doc.id, currentUser.uid)
          : false;

      // Parse media types for multiple media support
      List<MediaType>? mediaTypes;
      if (data['mediaTypes'] is List) {
        mediaTypes = (data['mediaTypes'] as List)
            .map(
              (type) => MediaType.values.firstWhere(
                (e) => e.name == type,
                orElse: () => MediaType.image,
              ),
            )
            .toList();
      }

      final post = Post(
        id: doc.id,
        userId: data['userId'] ?? '',
        username: data['username'] ?? '',
        userAvatarUrl: data['userAvatarUrl'] ?? '',
        mediaType: MediaType.values.firstWhere(
          (e) => e.name == (data['mediaType'] ?? 'image'),
          orElse: () => MediaType.image,
        ),
        mediaUrl: data['mediaUrl'] ?? '',
        caption: data['caption'] ?? '',
        location: data['location'],
        likeCount: data['likeCount'] ?? 0,
        commentCount: data['commentCount'] ?? 0,
        timestamp:
            (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
        isLiked: data['isLiked'] ?? false,
        isBookmarked: isBookmarked,
        // Add multiple media support
        mediaUrls: data['mediaUrls'] is List
            ? List<String>.from(data['mediaUrls'])
            : null,
        mediaTypes: mediaTypes,
        // Add other missing fields
        coAuthorIds: data['coAuthorIds'] is List
            ? List<String>.from(data['coAuthorIds'])
            : null,
        coAuthorUsernames: data['coAuthorUsernames'] is List
            ? List<String>.from(data['coAuthorUsernames'])
            : null,
        coAuthorAvatars: data['coAuthorAvatars'] is List
            ? List<String>.from(data['coAuthorAvatars'])
            : null,
        mentionedUsers: data['mentionedUsers'] is List
            ? List<String>.from(data['mentionedUsers'])
            : null,
        hashtags: data['hashtags'] is List
            ? List<String>.from(data['hashtags'])
            : null,
        visibility: data['visibility'] ?? 'public',
        isArchived: data['isArchived'] ?? false,
        isPublic: data['isPublic'] ?? true,
        isPinned: data['isPinned'] ?? false,
        isReposted: data['isReposted'] ?? false,
      );
      posts.add(post);
    }

    return posts;
  }

  // Create a new post with analytics
  Future<Post> createPost({
    required MediaType mediaType,
    required String mediaUrl,
    required String caption,
    String? location,
    List<String>? coAuthorIds,
    List<String>? coAuthorUsernames,
    List<String>? coAuthorAvatars,
    List<String>? mentionedUsers,
    List<String>? hashtags,
    List<MediaTag>? mediaTags,
    // Multiple media support
    List<String>? mediaUrls,
    List<MediaType>? mediaTypes,
  }) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    debugPrint('📝 Creating post for user: ${currentUser.uid}');

    // Get user profile for username and avatar
    try {
      final userProfile = await _firebaseService.getUserProfile(
        currentUser.uid,
      );
      debugPrint('✅ User profile fetched successfully: ${userProfile?.name}');

      final username =
          userProfile?.name ?? userProfile?.username ?? 'Unknown User';
      final userAvatarUrl = userProfile?.profilePictureUrl ?? '';

      debugPrint('👤 Using username: $username, avatar: $userAvatarUrl');

      // Debug caption content
      debugPrint('🔍 CAPTION DEBUG:');
      debugPrint('  - Raw caption: "$caption"');
      debugPrint('  - Caption length: ${caption.length}');
      debugPrint('  - Caption isEmpty: ${caption.isEmpty}');
      debugPrint('  - Caption == "...": ${caption == "..."}');

      // Convert empty captions to a default message for debugging
      final finalCaption = caption.isEmpty ? "..." : caption;
      debugPrint('  - Final caption: "$finalCaption"');

      final postData = {
        'userId': currentUser.uid,
        'username': username,
        'userAvatarUrl': userAvatarUrl,
        'mediaType': mediaType.name,
        'mediaUrl': mediaUrl,
        'caption': finalCaption,
        'location': location,
        'likeCount': 0,
        'commentCount': 0,
        'isLiked': false,
        'isBookmarked': false,
        'isArchived': false,
        'isPublic': true,
        'visibility': 'public',
        'isPinned': false,
        'status': 'active', // Add status field for feed filtering
        'isDeleted': false, // Add isDeleted field for soft delete tracking
        'createdAt': FieldValue.serverTimestamp(),
        'localCreatedAt': DateTime.now(), // Optimistic timestamp for UI
        'updatedAt': FieldValue.serverTimestamp(),
        if (coAuthorIds != null) 'coAuthorIds': coAuthorIds,
        if (coAuthorUsernames != null) 'coAuthorUsernames': coAuthorUsernames,
        if (coAuthorAvatars != null) 'coAuthorAvatars': coAuthorAvatars,
        if (mentionedUsers != null) 'mentionedUsers': mentionedUsers,
        if (hashtags != null) 'hashtags': hashtags,
        if (mediaTags != null)
          'mediaTags': mediaTags.map((tag) => tag.toJson()).toList(),
        // Multiple media support
        if (mediaUrls != null) 'mediaUrls': mediaUrls,
        if (mediaTypes != null)
          'mediaTypes': mediaTypes.map((type) => type.name).toList(),
      };

      // Debug print for createdAt
      debugPrint(
        '🕒 postData[createdAt] before Firestore: ${postData['createdAt']}',
      );

      // Validate post data before sending
      debugPrint('📋 Post data being sent to Firestore:');
      debugPrint('  - userId: ${postData['userId']}');
      debugPrint('  - username: ${postData['username']}');
      debugPrint(
        '  - caption: ${postData['caption']} (length: ${caption.length})',
      );
      debugPrint('  - mediaType: ${postData['mediaType']}');
      debugPrint('  - mediaUrl: ${postData['mediaUrl']}');
      debugPrint('  - location: ${postData['location']}');
      debugPrint('  - coAuthorIds: ${postData['coAuthorIds']}');
      debugPrint('  - coAuthorUsernames: ${postData['coAuthorUsernames']}');
      debugPrint('  - coAuthorAvatars: ${postData['coAuthorAvatars']}');
      debugPrint('  - mentionedUsers: ${postData['mentionedUsers']}');
      debugPrint('  - hashtags: ${postData['hashtags']}');
      debugPrint('  - mediaTags: ${postData['mediaTags']}');

      // Validate caption length
      if (finalCaption.length > 2000) {
        throw Exception(
          'Caption is too long. Maximum 2000 characters allowed.',
        );
      }

      // Content moderation check
      final severity = await _moderationService.moderateContent(caption);
      if (severity == ContentSeverity.blocked) {
        throw Exception(
          'Content violates community guidelines and cannot be posted.',
        );
      } else if (severity == ContentSeverity.severe) {
        throw Exception(
          'Content contains inappropriate material. Please review and try again.',
        );
      }

      // Rate limiting check
      final canPost = await _moderationService.checkRateLimit();
      if (!canPost) {
        throw Exception(
          'You have exceeded the posting limit. Please wait before creating another post.',
        );
      }

      final docRef = await _firestore.collection('posts').add(postData);
      debugPrint('✅ Post created successfully with ID: ${docRef.id}');
      // Fetch the created post to check createdAt
      final createdDoc = await docRef.get();
      debugPrint(
        '🕒 createdAt in Firestore: ${createdDoc.data()?['createdAt']}',
      );

      // Instead of incrementing, trigger smart filtering recalculation
      debugPrint(
        '🔄 POST CREATION: Triggering post count recalculation with smart filtering...',
      );
      debugPrint('🔄 POST CREATION: New post ID: ${docRef.id}');
      debugPrint('🔄 POST CREATION: New post caption: "$caption"');
      debugPrint('🔄 POST CREATION: New post userId: ${currentUser.uid}');

      final profileService = getIt<ProfileService>();
      await profileService.recalculatePostCount(currentUser.uid);
      debugPrint(
        '✅ POST CREATION: Post count recalculated with smart filtering for user: ${currentUser.uid}',
      );

      // Create Post object to return
      final createdPost = Post(
        id: docRef.id,
        userId: currentUser.uid,
        username: username,
        userAvatarUrl: userAvatarUrl,
        mediaType: mediaType,
        mediaUrl: mediaUrl,
        caption: finalCaption,
        location: location,
        likeCount: 0,
        commentCount: 0,
        timestamp: DateTime.now(),
        isLiked: false,
        isBookmarked: false,
        isArchived: false,
        isPublic: true,
        visibility: 'public',
        isPinned: false,
        coAuthorIds: coAuthorIds,
        coAuthorUsernames: coAuthorUsernames,
        coAuthorAvatars: coAuthorAvatars,
        mentionedUsers: mentionedUsers,
        hashtags: hashtags,
        mediaTags: mediaTags,
        // Multiple media support
        mediaUrls: mediaUrls,
        mediaTypes: mediaTypes,
      );

      // Log analytics event
      await _analyticsService.logPostCreated(
        postId: docRef.id,
        contentType: mediaType.name,
        hasMedia: mediaUrl.isNotEmpty,
        location: location,
      );

      return createdPost;
    } catch (e) {
      debugPrint('❌ Error creating post: ${e.toString()}');
      rethrow;
    }
  }

  // Add reaction to a post
  Future<void> addReaction(String postId, ReactionType reactionType) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    final postRef = _firestore.collection('posts').doc(postId);
    final reactionRef = _firestore
        .collection('posts')
        .doc(postId)
        .collection('reactions')
        .doc(currentUser.uid);

    await _firestore.runTransaction((transaction) async {
      final postDoc = await transaction.get(postRef);
      if (!postDoc.exists) return;

      final reactionDoc = await transaction.get(reactionRef);
      final existingReaction = reactionDoc.exists ? reactionDoc.data() : null;

      // Get user profile for reaction data
      final userProfile = await _firebaseService.getUserProfile(
        currentUser.uid,
      );
      final username =
          userProfile?.name ?? userProfile?.username ?? 'Unknown User';
      final userAvatarUrl = userProfile?.profilePictureUrl ?? '';

      if (existingReaction != null &&
          existingReaction['type'] == reactionType.name) {
        // Remove reaction if same type
        transaction.delete(reactionRef);
        transaction.update(postRef, {
          'reactionCount': FieldValue.increment(-1),
          'updatedAt': FieldValue.serverTimestamp(),
        });
      } else {
        // Add or update reaction
        final reactionData = {
          'postId': postId,
          'userId': currentUser.uid,
          'username': username,
          'userAvatarUrl': userAvatarUrl,
          'type': reactionType.name,
          'timestamp': FieldValue.serverTimestamp(),
        };

        transaction.set(reactionRef, reactionData);

        // Update post reaction count
        final increment = existingReaction != null
            ? 0
            : 1; // Only increment if new reaction
        transaction.update(postRef, {
          'reactionCount': FieldValue.increment(increment),
          'updatedAt': FieldValue.serverTimestamp(),
        });
      }
    });

    // Log analytics
    final postDoc = await _firestore.collection('posts').doc(postId).get();
    await _analyticsService.logPostLiked(
      postId: postId,
      postAuthorId: postDoc.data()?['userId'] ?? '',
    );
  }

  // Get reactions for a post
  Stream<List<PostReaction>> getPostReactions(String postId) {
    return _firestore
        .collection('posts')
        .doc(postId)
        .collection('reactions')
        .orderBy('timestamp', descending: true)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs.map((doc) {
            final data = doc.data();
            return PostReaction(
              id: doc.id,
              postId: data['postId'] ?? '',
              userId: data['userId'] ?? '',
              username: data['username'] ?? '',
              userAvatarUrl: data['userAvatarUrl'] ?? '',
              type: ReactionType.values.firstWhere(
                (e) => e.name == (data['type'] ?? 'like'),
                orElse: () => ReactionType.like,
              ),
              timestamp:
                  (data['timestamp'] as Timestamp?)?.toDate() ?? DateTime.now(),
            );
          }).toList(),
        );
  }

  // Get reaction summary for a post
  Future<ReactionSummary> getReactionSummary(String postId) async {
    final reactions = await _firestore
        .collection('posts')
        .doc(postId)
        .collection('reactions')
        .get();

    final currentUser = _auth.currentUser;
    final userReaction = currentUser != null
        ? reactions.docs
              .where((doc) => doc.data()['userId'] == currentUser.uid)
              .map(
                (doc) => ReactionType.values.firstWhere(
                  (e) => e.name == (doc.data()['type'] ?? 'like'),
                  orElse: () => ReactionType.like,
                ),
              )
              .firstOrNull
        : null;

    // Count reactions by type
    final reactionCounts = <ReactionType, int>{};
    for (final doc in reactions.docs) {
      final type = ReactionType.values.firstWhere(
        (e) => e.name == (doc.data()['type'] ?? 'like'),
        orElse: () => ReactionType.like,
      );
      reactionCounts[type] = (reactionCounts[type] ?? 0) + 1;
    }

    final reactionCountList = reactionCounts.entries.map((entry) {
      return ReactionCount(
        type: entry.key,
        count: entry.value,
        isUserReaction: userReaction == entry.key,
      );
    }).toList();

    return ReactionSummary(
      postId: postId,
      totalReactions: reactions.docs.length,
      reactionCounts: reactionCountList,
      userReaction: userReaction,
      lastUpdated: DateTime.now(),
    );
  }

  // Like/unlike a post with analytics (legacy method for backward compatibility)
  Future<void> toggleLike(String postId) async {
    final userId = currentUserId;
    if (userId == null) {
      throw Exception('User not authenticated');
    }

    final postRef = _firestore.collection('posts').doc(postId);
    final likeRef = _firestore
        .collection('posts')
        .doc(postId)
        .collection('likes')
        .doc(userId);

    await _firestore.runTransaction((transaction) async {
      final postDoc = await transaction.get(postRef);
      if (!postDoc.exists) return;

      final likeDoc = await transaction.get(likeRef);
      final isLiked = likeDoc.exists;

      if (isLiked) {
        // Unlike
        transaction.delete(likeRef);
        transaction.update(postRef, {
          'likeCount': FieldValue.increment(-1),
          'updatedAt': FieldValue.serverTimestamp(),
        });
      } else {
        // Like
        transaction.set(likeRef, {
          'userId': userId,
          'timestamp': FieldValue.serverTimestamp(),
        });
        transaction.update(postRef, {
          'likeCount': FieldValue.increment(1),
          'updatedAt': FieldValue.serverTimestamp(),
        });

        // Send notification for like
        final postData = postDoc.data();
        if (postData != null) {
          final postOwnerId = postData['userId'] as String?;
          if (postOwnerId != null && postOwnerId != userId) {
            await _notificationService.createLikeNotification(
              postId,
              postOwnerId,
            );
          }

          // Log analytics event for like
          await _analyticsService.logPostLiked(
            postId: postId,
            postAuthorId: postData['userId'] ?? '',
          );
        }
      }
    });
  }

  // Bookmark/unbookmark a post with analytics
  Future<void> toggleBookmark(String postId) async {
    if (currentUserId == null) {
      throw Exception('User not authenticated');
    }

    debugPrint('🔖 Toggling bookmark for post: $postId, user: $currentUserId');

    final bookmarkRef = _firestore
        .collection('users')
        .doc(currentUserId!)
        .collection('bookmarks')
        .doc(postId);

    await _firestore.runTransaction((transaction) async {
      final bookmarkDoc = await transaction.get(bookmarkRef);
      final isBookmarked = bookmarkDoc.exists;

      if (isBookmarked) {
        // Remove bookmark
        debugPrint('🔖 Removing bookmark for post: $postId');
        transaction.delete(bookmarkRef);
      } else {
        // Add bookmark
        debugPrint('🔖 Adding bookmark for post: $postId');
        transaction.set(bookmarkRef, {
          'postId': postId,
          'timestamp': FieldValue.serverTimestamp(),
        });

        // Log analytics event for bookmark
        await _analyticsService.logPostBookmarked(postId: postId);
      }
    });
  }

  // Get saved posts for current user
  Future<List<Post>> getSavedPosts() async {
    if (currentUserId == null) {
      throw Exception('User not authenticated');
    }

    debugPrint('🔖 Getting saved posts for user: $currentUserId');

    final bookmarksSnapshot = await _firestore
        .collection('users')
        .doc(currentUserId!)
        .collection('bookmarks')
        .orderBy('timestamp', descending: true)
        .get();

    debugPrint('🔖 Found ${bookmarksSnapshot.docs.length} bookmarks');

    if (bookmarksSnapshot.docs.isEmpty) {
      debugPrint('🔖 No bookmarks found');
      return [];
    }

    final postIds = bookmarksSnapshot.docs.map((doc) => doc.id).toList();
    debugPrint('🔖 Bookmark post IDs: $postIds');

    final postsSnapshot = await _firestore
        .collection('posts')
        .where(FieldPath.documentId, whereIn: postIds)
        .get();

    debugPrint('🔖 Found ${postsSnapshot.docs.length} posts from bookmarks');

    final savedPosts = postsSnapshot.docs
        .where((doc) {
          final data = doc.data();
          // Only include active posts (or posts without status field for backward compatibility)
          final status = data['status'];
          final isActiveStatus = status == 'active' || status == null;

          // Apply smart filtering to only include valid posts
          final isValidData = _isValidPostData(data);

          return isActiveStatus && isValidData;
        })
        .map((doc) {
          final data = doc.data();
          return Post(
            id: doc.id,
            userId: data['userId'] ?? '',
            username: data['username'] ?? '',
            userAvatarUrl: data['userAvatarUrl'] ?? '',
            mediaType: MediaType.values.firstWhere(
              (e) => e.name == (data['mediaType'] ?? 'image'),
              orElse: () => MediaType.image,
            ),
            mediaUrl: data['mediaUrl'] ?? '',
            caption: data['caption'] ?? '',
            location: data['location'],
            likeCount: data['likeCount'] ?? 0,
            commentCount: data['commentCount'] ?? 0,
            timestamp:
                (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
            isLiked: data['isLiked'] ?? false,
            isBookmarked: true, // These are saved posts
          );
        })
        .toList();

    debugPrint('🔖 Returning ${savedPosts.length} saved posts');
    return savedPosts;
  }

  // Get posts by user ID
  Future<List<Post>> getUserPosts(String userId) async {
    final snapshot = await _firestore
        .collection('posts')
        .where('userId', isEqualTo: userId)
        .orderBy('createdAt', descending: true)
        .get();

    final currentUser = _auth.currentUser;
    final posts = <Post>[];

    for (final doc in snapshot.docs) {
      final data = doc.data();

      // Check if current user has bookmarked this post
      final isBookmarked = currentUser != null
          ? await _checkUserBookmark(doc.id, currentUser.uid)
          : false;

      final post = Post(
        id: doc.id,
        userId: data['userId'] ?? '',
        username: data['username'] ?? '',
        userAvatarUrl: data['userAvatarUrl'] ?? '',
        mediaType: MediaType.values.firstWhere(
          (e) => e.name == (data['mediaType'] ?? 'image'),
          orElse: () => MediaType.image,
        ),
        mediaUrl: data['mediaUrl'] ?? '',
        caption: data['caption'] ?? '',
        location: data['location'],
        likeCount: data['likeCount'] ?? 0,
        commentCount: data['commentCount'] ?? 0,
        timestamp:
            (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
        isLiked: data['isLiked'] ?? false,
        isBookmarked: isBookmarked,
      );
      posts.add(post);
    }

    // Posts are already sorted by Firestore query, but ensure consistency
    // by sorting again in memory as a fallback
    posts.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    debugPrint(
      '📊 Feed service: ${posts.length} posts fetched for user $userId',
    );
    debugPrint(
      '📅 First post timestamp: ${posts.isNotEmpty ? posts.first.timestamp : 'No posts'}',
    );
    debugPrint(
      '📅 Last post timestamp: ${posts.isNotEmpty ? posts.last.timestamp : 'No posts'}',
    );

    return posts;
  }

  // Delete a post (soft delete)
  Future<void> deletePost(String postId) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    final postRef = _firestore.collection('posts').doc(postId);
    final postDoc = await postRef.get();

    if (!postDoc.exists) {
      throw Exception('Post not found');
    }

    final data = postDoc.data()!;
    if (data['userId'] != currentUser.uid) {
      throw Exception('You can only delete your own posts');
    }

    // Soft delete - mark as deleted instead of actually deleting
    try {
      await postRef.update({
        'status': 'deleted',
        'isDeleted': true,
        'deletedAt': FieldValue.serverTimestamp(),
        'deletedBy': currentUser.uid,
        'updatedAt': FieldValue.serverTimestamp(),
      });
      debugPrint('✅ Post soft deleted successfully: $postId');
    } catch (e) {
      debugPrint('❌ Error during soft delete: $e');
      throw Exception('Failed to soft delete post: $e');
    }

    // Recalculate post count with smart filtering after soft deletion
    debugPrint('🔄 Triggering post count recalculation after soft deletion...');
    final profileService = getIt<ProfileService>();
    await profileService.recalculatePostCount(currentUser.uid);
    debugPrint(
      '✅ Post count recalculated after soft deletion for user: ${currentUser.uid}',
    );
  }

  // Track post share for analytics
  Future<void> trackShare(String postId) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    try {
      // Update share count in post document
      await _firestore.collection('posts').doc(postId).update({
        'shareCount': FieldValue.increment(1),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Log analytics event
      await _analyticsService.logPostShared(
        postId: postId,
        shareMethod: 'native_share',
      );

      debugPrint('✅ Post share tracked: $postId');
    } catch (e) {
      debugPrint('❌ Error tracking post share: $e');
      // Don't throw error for analytics tracking failures
    }
  }

  // Update a post with any fields (only by author)
  Future<void> updatePost(
    String postId,
    Map<String, dynamic> updateData, {
    String? newStatus,
    bool? allowRemix,
    bool? allowRepost,
    bool? allowShare,
    bool? commentsDisabled,
    bool? likesHidden,
  }) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    final postRef = _firestore.collection('posts').doc(postId);
    final postDoc = await postRef.get();

    if (!postDoc.exists) {
      throw Exception('Post not found');
    }

    final data = postDoc.data()!;
    if (data['userId'] != currentUser.uid) {
      throw Exception('You can only update your own posts');
    }

    // Add updated timestamp
    updateData['updatedAt'] = FieldValue.serverTimestamp();

    await postRef.update(updateData);
    debugPrint('✅ Post updated successfully: $postId');
  }

  // Edit a post (only by author)
  Future<void> editPost({
    required String postId,
    required String newCaption,
    String? newLocation,
  }) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    final postRef = _firestore.collection('posts').doc(postId);
    final postDoc = await postRef.get();

    if (!postDoc.exists) {
      throw Exception('Post not found');
    }

    final data = postDoc.data()!;
    if (data['userId'] != currentUser.uid) {
      throw Exception('You can only edit your own posts');
    }

    final updateData = <String, dynamic>{
      'caption': newCaption,
      'updatedAt': FieldValue.serverTimestamp(),
      'lastEditedAt': FieldValue.serverTimestamp(),
      'editedBy': currentUser.uid,
    };

    if (newLocation != null) {
      updateData['location'] = newLocation;
    }

    await postRef.update(updateData);
    debugPrint('✅ Post edited successfully: $postId');
  }

  // Archive a post (only by author)
  Future<void> archivePost(String postId) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    final postRef = _firestore.collection('posts').doc(postId);
    final postDoc = await postRef.get();

    if (!postDoc.exists) {
      throw Exception('Post not found');
    }

    final data = postDoc.data()!;
    if (data['userId'] != currentUser.uid) {
      throw Exception('You can only archive your own posts');
    }

    await postRef.update({
      'isArchived': true,
      'archivedAt': FieldValue.serverTimestamp(),
    });
    debugPrint('✅ Post archived successfully: $postId');
  }

  // Get comments for a post
  Stream<List<Comment>> getComments(String postId) {
    return _firestore
        .collection('posts')
        .doc(postId)
        .collection('comments')
        .orderBy('timestamp', descending: true)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs.map((doc) {
            final data = doc.data();
            data['id'] = doc.id;
            return Comment.fromJson(data);
          }).toList(),
        );
  }

  // Add a comment to a post (supports replies)
  Future<void> addComment(
    String postId,
    String text, {
    String? parentId,
  }) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    final userProfile = await _firebaseService.getUserProfile(currentUser.uid);
    final username = userProfile?.username ?? 'Anonymous';
    final userAvatarUrl = userProfile?.profilePictureUrl ?? '';

    final commentData = {
      'postId': postId,
      'userId': currentUser.uid,
      'username': username,
      'userAvatarUrl': userAvatarUrl,
      'text': text,
      'timestamp': FieldValue.serverTimestamp(),
      'type': 'text',
      'reactions': {},
      'reactionCount': 0,
      'isEdited': false,
      if (parentId != null && parentId.isNotEmpty) 'parentId': parentId,
    };

    final postRef = _firestore.collection('posts').doc(postId);
    final commentRef = postRef.collection('comments').doc();

    try {
      await _firestore.runTransaction((transaction) async {
        transaction.set(commentRef, commentData);
        transaction.update(postRef, {'commentCount': FieldValue.increment(1)});
      });
    } catch (e) {
      debugPrint('❌ Error adding comment: $e');
      throw Exception('Failed to add comment. Please try again.');
    }
  }

  // Add a voice comment to a post (supports replies)
  Future<void> addVoiceComment(
    String postId,
    String voiceUrl,
    int duration,
    String? waveformData, {
    String? parentId,
  }) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    final userProfile = await _firebaseService.getUserProfile(currentUser.uid);
    final username = userProfile?.username ?? 'Anonymous';
    final userAvatarUrl = userProfile?.profilePictureUrl ?? '';

    final commentData = {
      'postId': postId,
      'userId': currentUser.uid,
      'username': username,
      'userAvatarUrl': userAvatarUrl,
      'text': '', // Empty text for voice comments
      'timestamp': FieldValue.serverTimestamp(),
      'type': 'voice',
      'voiceUrl': voiceUrl,
      'voiceDuration': duration,
      'waveformData': waveformData,
      'reactions': {},
      'reactionCount': 0,
      'isEdited': false,
      if (parentId != null && parentId.isNotEmpty) 'parentId': parentId,
    };

    final postRef = _firestore.collection('posts').doc(postId);
    final commentRef = postRef.collection('comments').doc();

    try {
      await _firestore.runTransaction((transaction) async {
        transaction.set(commentRef, commentData);
        transaction.update(postRef, {'commentCount': FieldValue.increment(1)});
      });
      debugPrint('✅ Voice comment added to post $postId');
    } catch (e) {
      debugPrint('❌ Error adding voice comment: $e');
      throw Exception('Failed to add voice comment. Please try again.');
    }
  }

  // Edit a comment (only by author)
  Future<void> editComment(
    String postId,
    String commentId,
    String newText,
  ) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }
    final commentRef = _firestore
        .collection('posts')
        .doc(postId)
        .collection('comments')
        .doc(commentId);
    final commentDoc = await commentRef.get();
    if (!commentDoc.exists) throw Exception('Comment not found');
    if (commentDoc['userId'] != currentUser.uid) {
      throw Exception('You can only edit your own comments');
    }
    await commentRef.update({'text': newText});
  }

  // Delete a comment (only by author)
  Future<void> deleteComment(String postId, String commentId) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }
    final commentRef = _firestore
        .collection('posts')
        .doc(postId)
        .collection('comments')
        .doc(commentId);
    final commentDoc = await commentRef.get();
    if (!commentDoc.exists) throw Exception('Comment not found');
    if (commentDoc['userId'] != currentUser.uid) {
      throw Exception('You can only delete your own comments');
    }
    await commentRef.delete();
  }

  // Add a reaction to a comment
  Future<bool> addCommentReaction({
    required String postId,
    required String commentId,
    required String emoji,
  }) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return false;
    try {
      final reactionRef = _firestore
          .collection('posts')
          .doc(postId)
          .collection('comments')
          .doc(commentId)
          .collection('reactions')
          .doc(currentUser.uid);
      await reactionRef.set({
        'userId': currentUser.uid,
        'emoji': emoji,
        'timestamp': FieldValue.serverTimestamp(),
      });
      return true;
    } catch (e) {
      debugPrint('❌ Error adding comment reaction: $e');
      return false;
    }
  }

  // Remove a reaction from a comment
  Future<bool> removeCommentReaction({
    required String postId,
    required String commentId,
    required String emoji,
  }) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return false;
    try {
      final reactionRef = _firestore
          .collection('posts')
          .doc(postId)
          .collection('comments')
          .doc(commentId)
          .collection('reactions')
          .doc(currentUser.uid);
      final doc = await reactionRef.get();
      if (doc.exists && doc['emoji'] == emoji) {
        await reactionRef.delete();
      }
      return true;
    } catch (e) {
      debugPrint('❌ Error removing comment reaction: $e');
      return false;
    }
  }

  // Repost a post
  Future<void> repost(String postId) async {
    try {
      if (currentUserId == null) throw Exception('User not authenticated');

      final postRef = _firestore.collection('posts').doc(postId);
      final repostRef = _firestore.collection('posts').doc();

      // Get the original post
      final postDoc = await postRef.get();
      if (!postDoc.exists) throw Exception('Post not found');

      final postData = postDoc.data()!;

      // Get current user profile data from Firestore (not Firebase Auth)
      final userProfile = await _firebaseService.getUserProfile(currentUserId!);
      final username = userProfile?.username ?? 'Unknown';
      final userAvatarUrl = userProfile?.profilePictureUrl ?? '';

      // Create repost data
      final repostData = {
        'userId': currentUserId,
        'username': username,
        'userAvatarUrl': userAvatarUrl,
        'userRole': postData['userRole'],
        'isVerified': postData['isVerified'] ?? false,
        'mediaUrl': postData['mediaUrl'],
        'mediaType': postData['mediaType'],
        'caption': postData['caption'],
        'location': postData['location'],
        'timestamp': FieldValue.serverTimestamp(),
        'likeCount': 0,
        'commentCount': 0,
        'repostCount': 0,
        'isLiked': false,
        'isBookmarked': false,
        'isReposted': true,
        'isArchived': false,
        'isDeleted': false,
        'originalPostId': postId,
        'originalUserId': postData['userId'],
        'originalUsername': postData['username'],
        'tags': postData['tags'] ?? [],
        'mentions': postData['mentions'] ?? [],
        'hashtags': postData['hashtags'] ?? [],
        'coAuthorIds': postData['coAuthorIds'] ?? [],
        'coAuthorUsernames': postData['coAuthorUsernames'] ?? [],
        'coAuthorAvatars': postData['coAuthorAvatars'] ?? [],
        'type': 'repost',
      };

      // Add repost to Firestore
      await repostRef.set(repostData);

      // Update original post's repost count
      await postRef.update({'repostCount': FieldValue.increment(1)});

      // Track analytics
      await _analyticsService.logPostShared(
        postId: postId,
        shareMethod: 'repost',
      );
    } catch (e) {
      throw Exception('Failed to repost: $e');
    }
  }

  // Report a post
  Future<void> reportPost(String postId, String reason) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    try {
      await _firestore.collection('reports').add({
        'postId': postId,
        'reportedBy': currentUser.uid,
        'reason': reason,
        'reportedAt': FieldValue.serverTimestamp(),
        'status': 'pending',
      });

      debugPrint('✅ Post reported successfully: $postId');
    } catch (e) {
      debugPrint('❌ Error reporting post: $e');
      rethrow;
    }
  }

  // Get users who liked a post
  Future<List<LikerModel>> getPostLikers(String postId) async {
    try {
      final likesSnapshot = await _firestore
          .collection('posts')
          .doc(postId)
          .collection('likes')
          .orderBy('timestamp', descending: true)
          .get();

      final List<LikerModel> likers = [];

      for (final likeDoc in likesSnapshot.docs) {
        final userId = likeDoc.data()['userId'] as String;
        final userProfile = await getUserProfile(userId);

        if (userProfile != null) {
          likers.add(
            LikerModel(
              id: userProfile.id,
              username: userProfile.username,
              name: userProfile.name,
              profilePictureUrl: userProfile.profilePictureUrl,
              isVerified: userProfile.isVerified,
              likedAt: (likeDoc.data()['timestamp'] as Timestamp).toDate(),
            ),
          );
        }
      }

      return likers;
    } catch (e) {
      throw Exception('Failed to get post likers: ${e.toString()}');
    }
  }

  // Get posts by hashtag
  Future<List<Post>> getPostsByHashtag(String hashtag) async {
    try {
      // Simplified query to avoid composite index requirement
      final postsSnapshot = await _firestore
          .collection('posts')
          .where('status', isEqualTo: 'active')
          .orderBy('createdAt', descending: true)
          .limit(100) // Get more posts to filter client-side
          .get();

      // Filter posts client-side for hashtag match
      final allPosts = postsSnapshot.docs.map((doc) {
        final data = doc.data();
        return Post(
          id: doc.id,
          userId: data['userId'] ?? '',
          username: data['username'] ?? '',
          userAvatarUrl: data['userAvatarUrl'] ?? '',
          mediaType: MediaType.values.firstWhere(
            (e) => e.name == (data['mediaType'] ?? 'image'),
            orElse: () => MediaType.image,
          ),
          mediaUrl: data['mediaUrl'] ?? '',
          caption: data['caption'] ?? '',
          location: data['location'],
          likeCount: data['likeCount'] ?? 0,
          commentCount: data['commentCount'] ?? 0,
          timestamp:
              (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
          isLiked: data['isLiked'] ?? false,
          isBookmarked: data['isBookmarked'] ?? false,
        );
      }).toList();

      // Filter for hashtag matches
      final hashtagPattern = '#$hashtag';
      return allPosts
          .where(
            (post) => post.caption.toLowerCase().contains(
              hashtagPattern.toLowerCase(),
            ),
          )
          .take(50)
          .toList();
    } catch (e) {
      throw Exception('Failed to get posts by hashtag: ${e.toString()}');
    }
  }

  // Get user ID by username
  Future<String?> getUserIdByUsername(String username) async {
    try {
      final usersSnapshot = await _firestore
          .collection('users')
          .where('username', isEqualTo: username)
          .limit(1)
          .get();

      if (usersSnapshot.docs.isNotEmpty) {
        return usersSnapshot.docs.first.id;
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get user ID by username: ${e.toString()}');
    }
  }

  // Helper method to get user profile
  Future<ProfileModel?> getUserProfile(String userId) async {
    try {
      final doc = await _firestore.collection('users').doc(userId).get();
      if (doc.exists) {
        final data = doc.data()!;
        return ProfileModel.fromJson({
          'id': doc.id,
          'username': data['username'] ?? '',
          'name': data['name'] ?? '',
          'profilePictureUrl': data['profilePictureUrl'] ?? '',
          'bio': data['bio'] ?? '',
          'postCount': data['postCount'] ?? 0,
          'followerCount': data['followerCount'] ?? 0,
          'followingCount': data['followingCount'] ?? 0,
          'isVerified': data['isVerified'] ?? false,
          'isBillionaire': data['isBillionaire'] ?? false,
          'isAdmin': data['isAdmin'] ?? false,
          'isBusinessAccount': data['isBusinessAccount'] ?? false,
          'businessName': data['businessName'] ?? '',
          'businessCategory': data['businessCategory'] ?? '',
          'website': data['website'] ?? '',
          'email': data['email'] ?? '',
          'createdAt': data['createdAt'] != null
              ? (data['createdAt'] as Timestamp).toDate().toIso8601String()
              : DateTime.now().toIso8601String(),
        });
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  // Analytics tracking for sharing actions
  Future<void> trackShareAction(
    String postId,
    String shareType, {
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return;

      await _analyticsService.logPostShared(
        postId: postId,
        shareMethod: shareType,
      );
    } catch (e) {
      // Silently fail analytics tracking
      debugPrint('Failed to track share action: $e');
    }
  }

  // Track repost analytics
  Future<void> trackRepost(String postId, String originalUserId) async {
    await trackShareAction(postId, 'repost');
  }

  // Track share to story analytics
  Future<void> trackShareToStory(String postId) async {
    await trackShareAction(postId, 'story');
  }

  // Track share to friend analytics
  Future<void> trackShareToFriend(String postId, String friendId) async {
    await trackShareAction(postId, 'friend');
  }

  // Check if user has bookmarked a post
  Future<bool> _checkUserBookmark(String postId, String userId) async {
    try {
      final bookmarkDoc = await _firestore
          .collection('users')
          .doc(userId)
          .collection('bookmarks')
          .doc(postId)
          .get();
      return bookmarkDoc.exists;
    } catch (e) {
      debugPrint('Error checking bookmark status: $e');
      return false;
    }
  }

  // Batch check bookmarks for better performance
  Future<Map<String, bool>> _batchCheckBookmarks(
    List<String> postIds,
    String userId,
  ) async {
    try {
      final bookmarkCollection = _firestore
          .collection('users')
          .doc(userId)
          .collection('bookmarks');

      final results = <String, bool>{};

      // Firebase has a limit of 10 documents per batch, so we chunk the requests
      const batchSize = 10;
      for (int i = 0; i < postIds.length; i += batchSize) {
        final batch = postIds.skip(i).take(batchSize).toList();
        final futures = batch.map(
          (postId) => bookmarkCollection.doc(postId).get(),
        );
        final snapshots = await Future.wait(futures);

        for (int j = 0; j < batch.length; j++) {
          results[batch[j]] = snapshots[j].exists;
        }
      }

      return results;
    } catch (e) {
      debugPrint('Error batch checking bookmarks: $e');
      // Return empty map, individual checks will return false
      return {};
    }
  }

  // Batch filter posts by permissions for better performance
  Future<List<Post>> _batchFilterPostsByPermissions(List<Post> posts) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      // If no user is logged in, only show public posts
      return posts
          .where(
            (post) => post.visibility == 'public' && post.status == 'active',
          )
          .toList();
    }

    final filteredPosts = <Post>[];

    // Group posts by visibility type for batch processing
    final publicPosts = posts
        .where((p) => p.visibility == 'public' && p.status == 'active')
        .toList();
    final followerPosts = posts
        .where((p) => p.visibility == 'followers')
        .toList();
    final privatePosts = posts.where((p) => p.visibility == 'private').toList();

    // Add all public posts
    filteredPosts.addAll(publicPosts);

    // Batch check following relationships for follower-only posts
    if (followerPosts.isNotEmpty) {
      final userIds = followerPosts.map((p) => p.userId).toSet().toList();
      final followingMap = await _batchCheckFollowing(userIds, currentUser.uid);

      for (final post in followerPosts) {
        if (post.userId == currentUser.uid ||
            followingMap[post.userId] == true) {
          filteredPosts.add(post);
        }
      }
    }

    // Only add private posts if user is the owner
    for (final post in privatePosts) {
      if (post.userId == currentUser.uid) {
        filteredPosts.add(post);
      }
    }

    return filteredPosts;
  }

  // Batch check following relationships
  Future<Map<String, bool>> _batchCheckFollowing(
    List<String> userIds,
    String currentUserId,
  ) async {
    try {
      final results = <String, bool>{};

      // Check if current user follows these users
      final followsQuery = await _firestore
          .collection('follows')
          .where('followerId', isEqualTo: currentUserId)
          .where('followingId', whereIn: userIds)
          .where('isActive', isEqualTo: true)
          .get();

      // Initialize all as false
      for (final userId in userIds) {
        results[userId] = false;
      }

      // Mark followed users as true
      for (final doc in followsQuery.docs) {
        final followingId = doc.data()['followingId'] as String;
        results[followingId] = true;
      }

      return results;
    } catch (e) {
      debugPrint('Error batch checking following: $e');
      return {};
    }
  }

  // Validate post data from Firestore
  bool _isValidPostData(Map<String, dynamic> data) {
    // Check if post has a valid creation date
    final createdAt = data['createdAt'] as Timestamp?;
    if (createdAt == null) {
      return false;
    }

    // Allow posts with empty captions or placeholder captions
    // Users should be able to post without captions if they want
    final caption = (data['caption'] as String?)?.trim() ?? '';

    // Only filter out specific test captions that are clearly for debugging
    if (caption == "This is a test post from profile screen" ||
        caption.startsWith("Test post from debug tools")) {
      return false;
    }

    return true;
  }

  // Track external share analytics
  Future<void> trackExternalShare(String postId) async {
    await trackShareAction(postId, 'external');
  }
}
