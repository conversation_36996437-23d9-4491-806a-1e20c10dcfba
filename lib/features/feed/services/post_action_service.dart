import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/features/feed/services/post_permission_service.dart';
import 'package:billionaires_social/features/profile/services/profile_service.dart';
import 'package:billionaires_social/core/services/analytics_service.dart';
import 'package:billionaires_social/features/notifications/services/notification_service.dart';
import 'package:billionaires_social/core/service_locator.dart';

/// Service to handle all post actions with proper permission checks
class PostActionService {
  static final PostActionService _instance = PostActionService._internal();
  factory PostActionService() => _instance;
  PostActionService._internal();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final PostPermissionService _permissionService = PostPermissionService();
  final AnalyticsService _analyticsService = getIt<AnalyticsService>();
  final NotificationService _notificationService = getIt<NotificationService>();

  /// Like or unlike a post
  Future<bool> toggleLike(String postId) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    // Get the post first
    final postDoc = await _firestore.collection('posts').doc(postId).get();
    if (!postDoc.exists) {
      throw Exception('Post not found');
    }

    final postData = postDoc.data()!;
    final post = Post.fromJson({...postData, 'id': postId});

    // Check permissions (but allow fallback for authenticated users)
    final canLike = await _permissionService.canLikePost(post);
    if (!canLike) {
      debugPrint(
        '⚠️ Like permission check failed, but proceeding for authenticated user',
      );
    }

    final likeRef = _firestore
        .collection('posts')
        .doc(postId)
        .collection('likes')
        .doc(currentUser.uid);

    final postRef = _firestore.collection('posts').doc(postId);

    return await _firestore.runTransaction((transaction) async {
      final likeDoc = await transaction.get(likeRef);
      final isLiked = likeDoc.exists;

      if (isLiked) {
        // Unlike
        transaction.delete(likeRef);
        transaction.update(postRef, {
          'likeCount': FieldValue.increment(-1),
          'updatedAt': FieldValue.serverTimestamp(),
        });
        return false;
      } else {
        // Like
        transaction.set(likeRef, {
          'userId': currentUser.uid,
          'timestamp': FieldValue.serverTimestamp(),
        });
        transaction.update(postRef, {
          'likeCount': FieldValue.increment(1),
          'updatedAt': FieldValue.serverTimestamp(),
        });

        // Send notification for like (don't await to avoid blocking)
        if (post.userId != currentUser.uid) {
          _notificationService.createLikeNotification(postId, post.userId);
        }

        // Log analytics
        _analyticsService.logPostLiked(
          postId: postId,
          postAuthorId: post.userId,
        );

        return true;
      }
    });
  }

  /// Bookmark or unbookmark a post
  Future<bool> toggleBookmark(String postId) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    // Get the post first
    final postDoc = await _firestore.collection('posts').doc(postId).get();
    if (!postDoc.exists) {
      throw Exception('Post not found');
    }

    final postData = postDoc.data()!;
    final post = Post.fromJson({...postData, 'id': postId});

    // Check if user can view the post
    if (!await _permissionService.canViewPost(post)) {
      throw Exception('You cannot bookmark this post');
    }

    final bookmarkRef = _firestore
        .collection('users')
        .doc(currentUser.uid)
        .collection('bookmarks')
        .doc(postId);

    final bookmarkDoc = await bookmarkRef.get();
    final isBookmarked = bookmarkDoc.exists;

    if (isBookmarked) {
      // Remove bookmark
      await bookmarkRef.delete();
      return false;
    } else {
      // Add bookmark
      await bookmarkRef.set({
        'postId': postId,
        'timestamp': FieldValue.serverTimestamp(),
      });
      return true;
    }
  }

  /// Share a post
  Future<void> sharePost(String postId, String shareMethod) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    // Get the post first
    final postDoc = await _firestore.collection('posts').doc(postId).get();
    if (!postDoc.exists) {
      throw Exception('Post not found');
    }

    final postData = postDoc.data()!;
    final post = Post.fromJson({...postData, 'id': postId});

    // Check permissions
    if (!await _permissionService.canSharePost(post)) {
      throw Exception('You cannot share this post');
    }

    // Update share count
    await _firestore.collection('posts').doc(postId).update({
      'shareCount': FieldValue.increment(1),
      'updatedAt': FieldValue.serverTimestamp(),
    });

    // Log analytics
    await _analyticsService.logPostShared(
      postId: postId,
      shareMethod: shareMethod,
    );
  }

  /// Repost a post with optional quote caption
  Future<String> repostPost(
    String originalPostId, {
    String? quoteCaption,
    String? visibility = 'public',
    bool isQuoteRepost = false,
  }) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    // Get the original post
    final originalPostDoc = await _firestore
        .collection('posts')
        .doc(originalPostId)
        .get();
    if (!originalPostDoc.exists) {
      throw Exception('Original post not found');
    }

    final originalPostData = originalPostDoc.data()!;
    final originalPost = Post.fromJson({
      ...originalPostData,
      'id': originalPostId,
    });

    // Check permissions
    if (!await _permissionService.canRepostPost(originalPost)) {
      throw Exception('You cannot repost this post');
    }

    // Get current user profile
    final userDoc = await _firestore
        .collection('users')
        .doc(currentUser.uid)
        .get();
    final userData = userDoc.data() ?? {};

    // Create repost data
    final repostData = {
      'userId': currentUser.uid,
      'username': userData['username'] ?? 'Unknown',
      'userAvatarUrl': userData['profilePictureUrl'] ?? '',
      'userRole': userData['userRole'],
      'isVerified': userData['isVerified'] ?? false,
      'mediaType': originalPostData['mediaType'],
      'mediaUrl': originalPostData['mediaUrl'],
      'caption': quoteCaption ?? '',
      'originalPostId': originalPostId,
      'originalUserId': originalPost.userId,
      'originalUsername': originalPost.username,
      'originalCaption': originalPost.caption,
      'originalTimestamp': originalPost.timestamp.toIso8601String(),
      'isRepost': true,
      'isQuoteRepost': isQuoteRepost,
      'allowRemix': true,
      'allowRepost': true,
      'allowShare': true,
      'visibility': visibility,
      'status': 'active',
      'likeCount': 0,
      'commentCount': 0,
      'shareCount': 0,
      'repostCount': 0,
      'viewCount': 0,
      'createdAt': FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
    };

    // Add repost to Firestore
    final repostRef = await _firestore.collection('posts').add(repostData);

    // Recalculate post count with smart filtering after repost creation
    debugPrint('🔄 Triggering post count recalculation after repost...');
    final profileService = getIt<ProfileService>();
    await profileService.recalculatePostCount(currentUser.uid);
    debugPrint(
      '✅ Post count recalculated after repost for user: ${currentUser.uid}',
    );

    // Update original post's repost count
    await _firestore.collection('posts').doc(originalPostId).update({
      'repostCount': FieldValue.increment(1),
      'updatedAt': FieldValue.serverTimestamp(),
    });

    // Send notification to original post owner
    if (originalPost.userId != currentUser.uid) {
      await _notificationService.createRepostNotification(
        originalPostId,
        originalPost.userId,
        repostRef.id,
      );
    }

    // Log analytics
    await _analyticsService.logPostShared(
      postId: originalPostId,
      shareMethod: isQuoteRepost ? 'quote_repost' : 'repost',
    );

    debugPrint('✅ Repost created successfully: ${repostRef.id}');
    return repostRef.id;
  }

  /// Report a post
  Future<void> reportPost(
    String postId,
    String reason, {
    String? details,
  }) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    // Get the post first
    final postDoc = await _firestore.collection('posts').doc(postId).get();
    if (!postDoc.exists) {
      throw Exception('Post not found');
    }

    final postData = postDoc.data()!;

    // Ensure required fields have default values to prevent null casting errors
    final safePostData = {
      ...postData,
      'id': postId,
      'userId': postData['userId'] ?? '',
      'username': postData['username'] ?? '',
      'userAvatarUrl': postData['userAvatarUrl'] ?? '',
      'mediaUrl': postData['mediaUrl'] ?? '',
      'caption': postData['caption'] ?? '',
    };

    final post = Post.fromJson(safePostData);

    // Check permissions
    if (!await _permissionService.canReportPost(post)) {
      throw Exception('You cannot report this post');
    }

    // Create report
    await _firestore.collection('reports').add({
      'postId': postId,
      'reportedBy': currentUser.uid,
      'postOwnerId': post.userId,
      'reason': reason,
      'details': details,
      'status': 'pending',
      'createdAt': FieldValue.serverTimestamp(),
    });

    // Update post report count
    await _firestore.collection('posts').doc(postId).update({
      'reportCount': FieldValue.increment(1),
      'isFlagged': true,
      'updatedAt': FieldValue.serverTimestamp(),
    });

    debugPrint('✅ Post reported successfully: $postId');
  }

  /// Delete a post (owner or admin only)
  Future<void> deletePost(String postId) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    // Get the post first
    final postDoc = await _firestore.collection('posts').doc(postId).get();
    if (!postDoc.exists) {
      throw Exception('Post not found');
    }

    final postData = postDoc.data()!;

    // Ensure required fields have default values to prevent null casting errors
    final safePostData = {
      ...postData,
      'id': postId,
      'userId': postData['userId'] ?? '',
      'username': postData['username'] ?? '',
      'userAvatarUrl': postData['userAvatarUrl'] ?? '',
      'mediaUrl': postData['mediaUrl'] ?? '',
      'caption': postData['caption'] ?? '',
    };

    final post = Post.fromJson(safePostData);

    // Check permissions
    if (!await _permissionService.canDeletePost(post)) {
      throw Exception('You cannot delete this post');
    }

    // Soft delete - mark as deleted instead of actually deleting
    await _firestore.collection('posts').doc(postId).update({
      'status': 'deleted',
      'isDeleted': true,
      'deletedAt': FieldValue.serverTimestamp(),
      'deletedBy': currentUser.uid,
      'updatedAt': FieldValue.serverTimestamp(),
    });

    // Recalculate post count with smart filtering after soft deletion
    debugPrint('🔄 Triggering post count recalculation after soft deletion...');
    final profileService = getIt<ProfileService>();
    await profileService.recalculatePostCount(post.userId);
    debugPrint(
      '✅ Post count recalculated after soft deletion for user: ${post.userId}',
    );

    debugPrint('✅ Post deleted successfully: $postId');
  }

  /// Get all reposts of a post
  Future<List<Post>> getPostReposts(String originalPostId) async {
    final repostsSnapshot = await _firestore
        .collection('posts')
        .where('originalPostId', isEqualTo: originalPostId)
        .where('isRepost', isEqualTo: true)
        .where('status', isEqualTo: 'active')
        .orderBy('createdAt', descending: true)
        .get();

    final reposts = <Post>[];
    for (final doc in repostsSnapshot.docs) {
      final data = doc.data();
      final repost = Post.fromJson({...data, 'id': doc.id});

      // Check if user can view this repost
      if (await _permissionService.canViewPost(repost)) {
        reposts.add(repost);
      }
    }

    return reposts;
  }

  /// Get reposts created by a specific user
  Future<List<Post>> getUserReposts(String userId) async {
    final repostsSnapshot = await _firestore
        .collection('posts')
        .where('userId', isEqualTo: userId)
        .where('isRepost', isEqualTo: true)
        .where('status', isEqualTo: 'active')
        .orderBy('createdAt', descending: true)
        .get();

    final reposts = <Post>[];
    for (final doc in repostsSnapshot.docs) {
      final data = doc.data();
      final repost = Post.fromJson({...data, 'id': doc.id});

      // Check if user can view this repost
      if (await _permissionService.canViewPost(repost)) {
        reposts.add(repost);
      }
    }

    return reposts;
  }

  /// Check if a post is a repost
  bool isRepost(Post post) {
    return post.isReposted && post.originalPostId != null;
  }

  /// Check if a post is a quote repost
  bool isQuoteRepost(Post post) {
    return post.isReposted &&
        post.originalPostId != null &&
        post.caption.isNotEmpty;
  }

  /// Get repost statistics for a post
  Future<Map<String, dynamic>> getRepostStats(String postId) async {
    final repostsSnapshot = await _firestore
        .collection('posts')
        .where('originalPostId', isEqualTo: postId)
        .where('isRepost', isEqualTo: true)
        .where('status', isEqualTo: 'active')
        .get();

    final totalReposts = repostsSnapshot.docs.length;
    final reposters = <String>{};
    var quoteReposts = 0;
    var totalLikes = 0;
    var totalComments = 0;

    for (final doc in repostsSnapshot.docs) {
      final data = doc.data();
      reposters.add(data['userId'] as String);

      if ((data['isQuoteRepost'] as bool?) == true ||
          (data['caption'] as String?)?.isNotEmpty == true) {
        quoteReposts++;
      }

      totalLikes += (data['likeCount'] as int? ?? 0);
      totalComments += (data['commentCount'] as int? ?? 0);
    }

    return {
      'totalReposts': totalReposts,
      'uniqueReposters': reposters.length,
      'quoteReposts': quoteReposts,
      'simpleReposts': totalReposts - quoteReposts,
      'totalLikesOnReposts': totalLikes,
      'totalCommentsOnReposts': totalComments,
      'repostEngagementRate': totalReposts > 0
          ? (totalLikes + totalComments) / totalReposts
          : 0.0,
    };
  }

  /// Delete a repost (only by repost creator or admin)
  Future<void> deleteRepost(String repostId) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    // Get the repost
    final repostDoc = await _firestore.collection('posts').doc(repostId).get();
    if (!repostDoc.exists) {
      throw Exception('Repost not found');
    }

    final repostData = repostDoc.data()!;

    // Ensure required fields have default values to prevent null casting errors
    final safeRepostData = {
      ...repostData,
      'id': repostId,
      'userId': repostData['userId'] ?? '',
      'username': repostData['username'] ?? '',
      'userAvatarUrl': repostData['userAvatarUrl'] ?? '',
      'mediaUrl': repostData['mediaUrl'] ?? '',
      'caption': repostData['caption'] ?? '',
    };

    final repost = Post.fromJson(safeRepostData);

    // Check permissions
    if (!await _permissionService.canDeletePost(repost)) {
      throw Exception('You cannot delete this repost');
    }

    // Soft delete the repost
    await _firestore.collection('posts').doc(repostId).update({
      'status': 'deleted',
      'isDeleted': true,
      'deletedAt': FieldValue.serverTimestamp(),
      'deletedBy': currentUser.uid,
      'updatedAt': FieldValue.serverTimestamp(),
    });

    // Recalculate post count with smart filtering after repost deletion
    debugPrint(
      '🔄 Triggering post count recalculation after repost deletion...',
    );
    final profileService = getIt<ProfileService>();
    await profileService.recalculatePostCount(repost.userId);
    debugPrint(
      '✅ Post count recalculated after repost deletion for user: ${repost.userId}',
    );

    // Decrement original post's repost count
    if (repost.originalPostId != null) {
      await _firestore.collection('posts').doc(repost.originalPostId!).update({
        'repostCount': FieldValue.increment(-1),
        'updatedAt': FieldValue.serverTimestamp(),
      });
    }

    debugPrint('✅ Repost deleted successfully: $repostId');
  }
}
