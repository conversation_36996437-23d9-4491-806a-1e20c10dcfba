import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:billionaires_social/features/profile/models/profile_model.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/core/services/cache_service.dart';
import 'package:get_it/get_it.dart';

/// Service for discovering content for users with zero or few followers
class ContentDiscoveryService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final CacheService _cacheService = GetIt.I<CacheService>();

  String? get currentUserId => _auth.currentUser?.uid;

  /// Get suggested accounts for new users
  Future<List<ProfileModel>> getSuggestedAccounts({
    int limit = 20,
    String? excludeUserId,
  }) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return [];

    final cacheKey = 'suggested_accounts_${currentUser.uid}';

    // Check cache first (1 hour expiry)
    final cached = await _cacheService.getData(cacheKey);
    if (cached != null) {
      try {
        return (cached as List)
            .map((json) => ProfileModel.fromJson(json))
            .toList();
      } catch (e) {
        debugPrint('❌ Error parsing cached suggested accounts: $e');
      }
    }

    try {
      final suggestions = <ProfileModel>[];
      final excludeIds = {
        currentUser.uid,
        if (excludeUserId != null) excludeUserId,
      };

      // 1. Get mutual connections (friends of friends)
      final mutualConnections = await _getMutualConnections(
        currentUser.uid,
        limit: limit ~/ 4,
        excludeIds: excludeIds,
      );
      suggestions.addAll(mutualConnections);
      excludeIds.addAll(mutualConnections.map((p) => p.id));

      // 2. Get high engagement accounts (trending users)
      final trendingUsers = await _getTrendingUsers(
        limit: limit ~/ 4,
        excludeIds: excludeIds,
      );
      suggestions.addAll(trendingUsers);
      excludeIds.addAll(trendingUsers.map((p) => p.id));

      // 3. Get verified/popular accounts
      final popularAccounts = await _getPopularAccounts(
        limit: limit ~/ 4,
        excludeIds: excludeIds,
      );
      suggestions.addAll(popularAccounts);
      excludeIds.addAll(popularAccounts.map((p) => p.id));

      // 4. Get recently active public profiles
      final activeProfiles = await _getRecentlyActiveProfiles(
        limit: limit - suggestions.length,
        excludeIds: excludeIds,
      );
      suggestions.addAll(activeProfiles);

      // Cache results for 1 hour
      final jsonData = suggestions.map((p) => p.toJson()).toList();
      await _cacheService.setData(
        cacheKey,
        jsonData,
        expiry: const Duration(hours: 1),
      );

      debugPrint(
        '🔍 ContentDiscovery: Found ${suggestions.length} suggested accounts',
      );
      return suggestions.take(limit).toList();
    } catch (e) {
      debugPrint('❌ Error getting suggested accounts: $e');
      return [];
    }
  }

  /// Get public content for discovery feed
  Future<List<Post>> getDiscoveryContent({
    int limit = 15,
    String? lastPostId,
    List<String>? preferredUserIds,
  }) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return [];

    try {
      final discoveryPosts = <Post>[];

      // 1. Get trending public posts (40% of content)
      final trendingLimit = (limit * 0.4).round();
      final trendingPosts = await _getTrendingPublicPosts(
        limit: trendingLimit,
        lastPostId: lastPostId,
      );
      discoveryPosts.addAll(trendingPosts);

      // 2. Get posts from suggested users (40% of content)
      if (preferredUserIds != null && preferredUserIds.isNotEmpty) {
        final suggestedLimit = (limit * 0.4).round();
        final suggestedPosts = await _getPostsFromSuggestedUsers(
          preferredUserIds,
          limit: suggestedLimit,
          excludePostIds: discoveryPosts.map((p) => p.id).toSet(),
        );
        discoveryPosts.addAll(suggestedPosts);
      }

      // 3. Fill remaining with high-quality public posts (20% of content)
      final remainingLimit = limit - discoveryPosts.length;
      if (remainingLimit > 0) {
        final qualityPosts = await _getHighQualityPublicPosts(
          limit: remainingLimit,
          excludePostIds: discoveryPosts.map((p) => p.id).toSet(),
        );
        discoveryPosts.addAll(qualityPosts);
      }

      // Sort by engagement score and recency
      discoveryPosts.sort((a, b) {
        final scoreA = _calculateEngagementScore(a);
        final scoreB = _calculateEngagementScore(b);
        return scoreB.compareTo(scoreA);
      });

      debugPrint(
        '🔍 ContentDiscovery: Found ${discoveryPosts.length} discovery posts',
      );
      return discoveryPosts.take(limit).toList();
    } catch (e) {
      debugPrint('❌ Error getting discovery content: $e');
      return [];
    }
  }

  /// Check if user has zero followers (needs discovery content)
  Future<bool> needsDiscoveryContent(String userId) async {
    try {
      final followingSnapshot = await _firestore
          .collection('users')
          .doc(userId)
          .collection('following')
          .limit(1)
          .get();

      return followingSnapshot.docs.isEmpty;
    } catch (e) {
      debugPrint('❌ Error checking discovery need: $e');
      return true; // Default to showing discovery content
    }
  }

  /// Get mutual connections (friends of friends)
  Future<List<ProfileModel>> _getMutualConnections(
    String userId, {
    required int limit,
    required Set<String> excludeIds,
  }) async {
    try {
      // Get user's current following list
      final followingSnapshot = await _firestore
          .collection('users')
          .doc(userId)
          .collection('following')
          .limit(50) // Limit to prevent excessive queries
          .get();

      if (followingSnapshot.docs.isEmpty) return [];

      final followingIds = followingSnapshot.docs.map((doc) => doc.id).toList();
      final mutualConnections = <String>{};

      // Get followers of people the user follows
      for (final followingId in followingIds.take(10)) {
        // Limit iterations
        final mutualSnapshot = await _firestore
            .collection('users')
            .doc(followingId)
            .collection('following')
            .limit(20)
            .get();

        for (final doc in mutualSnapshot.docs) {
          final mutualId = doc.id;
          if (!excludeIds.contains(mutualId) &&
              !followingIds.contains(mutualId)) {
            mutualConnections.add(mutualId);
          }
        }

        if (mutualConnections.length >= limit) break;
      }

      return await _getProfilesByIds(mutualConnections.take(limit).toList());
    } catch (e) {
      debugPrint('❌ Error getting mutual connections: $e');
      return [];
    }
  }

  /// Get trending users based on recent activity
  Future<List<ProfileModel>> _getTrendingUsers({
    required int limit,
    required Set<String> excludeIds,
  }) async {
    try {
      // Get users with high recent engagement
      final cutoffTime = DateTime.now().subtract(const Duration(days: 7));

      // Simplified query to avoid composite index requirement
      final postsSnapshot = await _firestore
          .collection('posts')
          .where('visibility', isEqualTo: 'public')
          .where('status', isEqualTo: 'active')
          .orderBy('createdAt', descending: true)
          .limit(200) // Get more posts to filter client-side
          .get();

      final userEngagement = <String, int>{};

      for (final doc in postsSnapshot.docs) {
        final data = doc.data();
        final userId = data['userId'] as String?;
        final likeCount = data['likeCount'] as int? ?? 0;
        final commentCount = data['commentCount'] as int? ?? 0;
        final createdAt = (data['createdAt'] as Timestamp?)?.toDate();

        // Filter by cutoff time client-side
        if (userId != null &&
            !excludeIds.contains(userId) &&
            createdAt != null &&
            createdAt.isAfter(cutoffTime)) {
          userEngagement[userId] =
              (userEngagement[userId] ?? 0) +
              (likeCount * 2 + commentCount * 3);
        }
      }

      // Sort by engagement and get top users
      final sortedUsers = userEngagement.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));

      final topUserIds = sortedUsers
          .take(limit)
          .map((entry) => entry.key)
          .toList();

      return await _getProfilesByIds(topUserIds);
    } catch (e) {
      debugPrint('❌ Error getting trending users: $e');
      return [];
    }
  }

  /// Get popular accounts (verified, high follower count)
  Future<List<ProfileModel>> _getPopularAccounts({
    required int limit,
    required Set<String> excludeIds,
  }) async {
    try {
      final popularSnapshot = await _firestore
          .collection('users')
          .where('isVerified', isEqualTo: true)
          .orderBy('followerCount', descending: true)
          .limit(limit * 2) // Get more to filter out excluded
          .get();

      final profiles = <ProfileModel>[];

      for (final doc in popularSnapshot.docs) {
        if (excludeIds.contains(doc.id)) continue;

        final data = doc.data();
        profiles.add(ProfileModel.fromJson({...data, 'id': doc.id}));

        if (profiles.length >= limit) break;
      }

      return profiles;
    } catch (e) {
      debugPrint('❌ Error getting popular accounts: $e');
      return [];
    }
  }

  /// Get recently active public profiles
  Future<List<ProfileModel>> _getRecentlyActiveProfiles({
    required int limit,
    required Set<String> excludeIds,
  }) async {
    try {
      final cutoffTime = DateTime.now().subtract(const Duration(days: 3));

      final activeSnapshot = await _firestore
          .collection('users')
          .where('lastActiveAt', isGreaterThan: cutoffTime)
          .orderBy('lastActiveAt', descending: true)
          .limit(limit * 2)
          .get();

      final profiles = <ProfileModel>[];

      for (final doc in activeSnapshot.docs) {
        if (excludeIds.contains(doc.id)) continue;

        final data = doc.data();
        profiles.add(ProfileModel.fromJson({...data, 'id': doc.id}));

        if (profiles.length >= limit) break;
      }

      return profiles;
    } catch (e) {
      debugPrint('❌ Error getting recently active profiles: $e');
      return [];
    }
  }

  /// Get trending public posts
  Future<List<Post>> _getTrendingPublicPosts({
    required int limit,
    String? lastPostId,
  }) async {
    try {
      Query query = _firestore
          .collection('posts')
          .where('visibility', isEqualTo: 'public')
          .where('status', isEqualTo: 'active')
          .where('trending', isEqualTo: true)
          .orderBy('trendingScore', descending: true)
          .limit(limit);

      if (lastPostId != null) {
        final lastDoc = await _firestore
            .collection('posts')
            .doc(lastPostId)
            .get();
        if (lastDoc.exists) {
          query = query.startAfterDocument(lastDoc);
        }
      }

      final snapshot = await query.get();
      return await _convertDocumentsToPosts(snapshot.docs);
    } catch (e) {
      debugPrint('❌ Error getting trending public posts: $e');
      return [];
    }
  }

  /// Get posts from suggested users
  Future<List<Post>> _getPostsFromSuggestedUsers(
    List<String> userIds, {
    required int limit,
    required Set<String> excludePostIds,
  }) async {
    try {
      final posts = <Post>[];

      // Get recent posts from suggested users
      for (final userId in userIds.take(10)) {
        // Limit to prevent excessive queries
        final userPostsSnapshot = await _firestore
            .collection('posts')
            .where('userId', isEqualTo: userId)
            .where('visibility', isEqualTo: 'public')
            .where('status', isEqualTo: 'active')
            .orderBy('createdAt', descending: true)
            .limit(3) // Max 3 posts per user
            .get();

        final userPosts = await _convertDocumentsToPosts(
          userPostsSnapshot.docs,
        );
        posts.addAll(
          userPosts.where((post) => !excludePostIds.contains(post.id)),
        );

        if (posts.length >= limit) break;
      }

      return posts.take(limit).toList();
    } catch (e) {
      debugPrint('❌ Error getting posts from suggested users: $e');
      return [];
    }
  }

  /// Get high quality public posts
  Future<List<Post>> _getHighQualityPublicPosts({
    required int limit,
    required Set<String> excludePostIds,
  }) async {
    try {
      final cutoffTime = DateTime.now().subtract(const Duration(days: 7));

      // Simplified query to avoid composite index requirement
      final snapshot = await _firestore
          .collection('posts')
          .where('visibility', isEqualTo: 'public')
          .where('status', isEqualTo: 'active')
          .orderBy('createdAt', descending: true)
          .limit(limit * 4) // Get more posts to filter client-side
          .get();

      final posts = await _convertDocumentsToPosts(snapshot.docs);
      return posts
          .where(
            (post) =>
                !excludePostIds.contains(post.id) &&
                post.timestamp.isAfter(cutoffTime) &&
                post.likeCount > 5,
          ) // Client-side filtering for engagement
          .take(limit)
          .toList();
    } catch (e) {
      debugPrint('❌ Error getting high quality public posts: $e');
      return [];
    }
  }

  /// Convert Firestore documents to Post objects
  Future<List<Post>> _convertDocumentsToPosts(
    List<QueryDocumentSnapshot> docs,
  ) async {
    final currentUser = _auth.currentUser;
    final posts = <Post>[];

    for (final doc in docs) {
      try {
        final data = doc.data() as Map<String, dynamic>;

        // Check if current user has bookmarked this post
        final isBookmarked = currentUser != null
            ? await _checkUserBookmark(doc.id, currentUser.uid)
            : false;

        final post = Post(
          id: doc.id,
          userId: data['userId'] ?? '',
          username: data['username'] ?? '',
          userAvatarUrl: data['userAvatarUrl'] ?? '',
          mediaType: MediaType.values.firstWhere(
            (e) => e.name == (data['mediaType'] ?? 'image'),
            orElse: () => MediaType.image,
          ),
          mediaUrl: data['mediaUrl'] ?? '',
          caption: data['caption'] ?? '',
          location: data['location'],
          likeCount: data['likeCount'] ?? 0,
          commentCount: data['commentCount'] ?? 0,
          timestamp:
              (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
          isLiked: data['isLiked'] ?? false,
          isBookmarked: isBookmarked,
          visibility: data['visibility'] ?? 'public',
          status: data['status'] ?? 'active',
          shareCount: data['shareCount'] ?? 0,
          viewCount: data['viewCount'] ?? 0,
        );

        posts.add(post);
      } catch (e) {
        debugPrint('❌ Error converting document to post: $e');
      }
    }

    return posts;
  }

  /// Check if user has bookmarked a post
  Future<bool> _checkUserBookmark(String postId, String userId) async {
    try {
      final bookmarkDoc = await _firestore
          .collection('users')
          .doc(userId)
          .collection('bookmarks')
          .doc(postId)
          .get();
      return bookmarkDoc.exists;
    } catch (e) {
      return false;
    }
  }

  /// Calculate engagement score for sorting
  double _calculateEngagementScore(Post post) {
    final hoursSincePost =
        DateTime.now().difference(post.timestamp).inHours + 1;
    final score =
        (post.likeCount * 3 +
            post.commentCount * 2 +
            (post.shareCount ?? 0) * 4 +
            (post.viewCount ?? 0) * 1) /
        hoursSincePost;
    return score;
  }

  /// Get profiles by IDs
  Future<List<ProfileModel>> _getProfilesByIds(List<String> userIds) async {
    if (userIds.isEmpty) return [];

    try {
      final profiles = <ProfileModel>[];

      // Process in batches of 10 (Firestore limit)
      for (int i = 0; i < userIds.length; i += 10) {
        final batch = userIds.skip(i).take(10).toList();
        final snapshot = await _firestore
            .collection('users')
            .where(FieldPath.documentId, whereIn: batch)
            .get();

        for (final doc in snapshot.docs) {
          final data = doc.data();
          profiles.add(ProfileModel.fromJson({...data, 'id': doc.id}));
        }
      }

      return profiles;
    } catch (e) {
      debugPrint('❌ Error getting profiles by IDs: $e');
      return [];
    }
  }
}
