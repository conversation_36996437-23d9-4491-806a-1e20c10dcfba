import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/features/profile/models/profile_model.dart';
import 'package:billionaires_social/features/stories/models/story_reel_model.dart';

/// Service for managing content access permissions and privacy controls
class ContentAccessService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  String? get currentUserId => _auth.currentUser?.uid;

  /// Check if user can view a post based on visibility settings
  Future<bool> canViewPost(
    Post post,
    String? currentUserId,
    ProfileModel? postOwner,
  ) async {
    // Public posts are visible to everyone (if active)
    if (post.visibility == 'public' && post.status == 'active') {
      return true;
    }

    // If no user is logged in, only public posts are visible
    if (currentUserId == null) {
      return post.visibility == 'public' && post.status == 'active';
    }

    // Post owner can always see their own content
    if (post.userId == currentUserId) {
      return true;
    }

    // Check if user is blocked by post owner
    if (postOwner != null && await _isUserBlocked(currentUserId, post.userId)) {
      return false;
    }

    // Check visibility rules
    switch (post.visibility) {
      case 'public':
        return post.status == 'active';
      case 'followers':
        return await _isFollowing(currentUserId, post.userId);
      case 'closeFriends':
        return await _isInCloseFriends(
          currentUserId,
          post.userId,
          post.closeFriendsGroupIds ?? [],
        );
      case 'private':
        return false; // Only owner can see private posts
      default:
        return false;
    }
  }

  /// Check if user can view a story
  Future<bool> canViewStory(StoryItem story, String? currentUserId) async {
    // Stories require explicit following relationship or ownership
    if (currentUserId == null) return false;

    // Story owner can always see their own stories
    if (story.userId == currentUserId) return true;

    // Check if user is blocked
    if (await _isUserBlocked(currentUserId, story.userId)) {
      return false;
    }

    // Stories are only visible to followers
    return await _isFollowing(currentUserId, story.userId);
  }

  /// Check if user can interact with content (like, comment, share)
  Future<bool> canInteractWithPost(Post post, String? currentUserId) async {
    if (currentUserId == null) return false;

    // Can't interact with own posts in some contexts
    if (post.userId == currentUserId) return true;

    // Check if user is blocked
    if (await _isUserBlocked(currentUserId, post.userId)) {
      return false;
    }

    // Must be able to view the post to interact with it
    return await canViewPost(post, currentUserId, null);
  }

  /// Filter posts based on user permissions
  Future<List<Post>> filterPostsByPermissions(
    List<Post> posts,
    String? currentUserId,
  ) async {
    if (currentUserId == null) {
      // Non-authenticated users can only see public posts
      return posts
          .where(
            (post) => post.visibility == 'public' && post.status == 'active',
          )
          .toList();
    }

    final filteredPosts = <Post>[];

    // Get blocked users list for efficiency
    final blockedUsers = await _getBlockedUsers(currentUserId);

    for (final post in posts) {
      // Skip posts from blocked users
      if (blockedUsers.contains(post.userId)) continue;

      // Check post visibility
      if (await canViewPost(post, currentUserId, null)) {
        filteredPosts.add(post);
      }
    }

    return filteredPosts;
  }

  /// Get content preferences for discovery
  Future<Map<String, dynamic>> getContentPreferences(String userId) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();

      if (userDoc.exists) {
        final userData = userDoc.data()!;
        return userData['contentPreferences'] as Map<String, dynamic>? ??
            _getDefaultPreferences();
      }
    } catch (e) {
      debugPrint('❌ Error getting content preferences: $e');
    }

    return _getDefaultPreferences();
  }

  /// Update content preferences
  Future<void> updateContentPreferences(
    String userId,
    Map<String, dynamic> preferences,
  ) async {
    try {
      await _firestore.collection('users').doc(userId).update({
        'contentPreferences': preferences,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('❌ Error updating content preferences: $e');
      throw Exception('Failed to update content preferences');
    }
  }

  /// Check if discovery content should be shown
  Future<bool> shouldShowDiscoveryContent(String userId) async {
    final preferences = await getContentPreferences(userId);
    return preferences['showSuggestedContent'] as bool? ?? true;
  }

  /// Get discovery content percentage (0-50%)
  Future<double> getDiscoveryContentPercentage(String userId) async {
    final preferences = await getContentPreferences(userId);
    final percentage =
        preferences['discoveryContentPercentage'] as double? ?? 30.0;
    return percentage.clamp(0.0, 50.0); // Ensure it's between 0-50%
  }

  /// Check if user is following another user
  Future<bool> _isFollowing(String followerId, String followingId) async {
    try {
      final followDoc = await _firestore
          .collection('follows')
          .where('followerId', isEqualTo: followerId)
          .where('followingId', isEqualTo: followingId)
          .where('isActive', isEqualTo: true)
          .limit(1)
          .get();

      return followDoc.docs.isNotEmpty;
    } catch (e) {
      debugPrint('❌ Error checking follow status: $e');
      return false;
    }
  }

  /// Check if user is in close friends list
  Future<bool> _isInCloseFriends(
    String userId,
    String postOwnerId,
    List<String> closeFriendsGroupIds,
  ) async {
    if (closeFriendsGroupIds.isEmpty) return false;

    try {
      for (final groupId in closeFriendsGroupIds) {
        final groupDoc = await _firestore
            .collection('users')
            .doc(postOwnerId)
            .collection('closeFriendsGroups')
            .doc(groupId)
            .get();

        if (groupDoc.exists) {
          final groupData = groupDoc.data()!;
          final members = List<String>.from(groupData['members'] ?? []);
          if (members.contains(userId)) {
            return true;
          }
        }
      }
      return false;
    } catch (e) {
      debugPrint('❌ Error checking close friends status: $e');
      return false;
    }
  }

  /// Check if user is blocked
  Future<bool> _isUserBlocked(String userId, String potentialBlockerId) async {
    try {
      final blockDoc = await _firestore
          .collection('users')
          .doc(potentialBlockerId)
          .collection('blockedUsers')
          .doc(userId)
          .get();

      return blockDoc.exists;
    } catch (e) {
      debugPrint('❌ Error checking block status: $e');
      return false;
    }
  }

  /// Get list of blocked users
  Future<Set<String>> _getBlockedUsers(String userId) async {
    try {
      final blockedSnapshot = await _firestore
          .collection('users')
          .doc(userId)
          .collection('blockedUsers')
          .get();

      return blockedSnapshot.docs.map((doc) => doc.id).toSet();
    } catch (e) {
      debugPrint('❌ Error getting blocked users: $e');
      return <String>{};
    }
  }

  /// Get default content preferences
  Map<String, dynamic> _getDefaultPreferences() {
    return {
      'showSuggestedContent': true,
      'showTrendingPosts': true,
      'showRegionalContent': true,
      'discoveryContentPercentage': 30.0,
      'showVerifiedOnly': false,
      'showFollowedUsersOnly': false,
      'enableContentFiltering': true,
      'blockedKeywords': <String>[],
      'preferredLanguages': ['en'],
      'contentTypes': {
        'images': true,
        'videos': true,
        'text': true,
        'links': true,
      },
    };
  }

  /// Check if content matches user preferences
  Future<bool> matchesContentPreferences(
    Post post,
    String userId,
    Map<String, dynamic>? preferences,
  ) async {
    preferences ??= await getContentPreferences(userId);

    // Check content type preferences
    final contentTypes =
        preferences['contentTypes'] as Map<String, dynamic>? ?? {};
    switch (post.mediaType) {
      case MediaType.image:
        if (!(contentTypes['images'] as bool? ?? true)) return false;
        break;
      case MediaType.video:
        if (!(contentTypes['videos'] as bool? ?? true)) return false;
        break;
      case MediaType.text:
        if (!(contentTypes['text'] as bool? ?? true)) return false;
        break;
    }

    // Check blocked keywords
    final blockedKeywords = List<String>.from(
      preferences['blockedKeywords'] ?? [],
    );
    if (blockedKeywords.isNotEmpty) {
      final caption = post.caption.toLowerCase();
      for (final keyword in blockedKeywords) {
        if (caption.contains(keyword.toLowerCase())) {
          return false;
        }
      }
    }

    return true;
  }
}
