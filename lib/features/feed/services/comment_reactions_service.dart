import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final commentReactionsServiceProvider = Provider<CommentReactionsService>((
  ref,
) {
  return CommentReactionsService();
});

class CommentReactionsService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Add or update a reaction to a comment
  Future<void> addReaction({
    required String postId,
    required String commentId,
    required String emoji,
  }) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    try {
      final commentRef = _firestore
          .collection('posts')
          .doc(postId)
          .collection('comments')
          .doc(commentId);

      await _firestore.runTransaction((transaction) async {
        final commentDoc = await transaction.get(commentRef);

        if (!commentDoc.exists) {
          throw Exception('Comment not found');
        }

        final data = commentDoc.data()!;
        final reactions = Map<String, String>.from(data['reactions'] ?? {});
        final reactionCounts = Map<String, int>.from(
          data['reactionCounts'] ?? {},
        );

        // Remove previous reaction if exists
        final previousReaction = reactions[currentUser.uid];
        if (previousReaction != null) {
          reactionCounts[previousReaction] =
              (reactionCounts[previousReaction] ?? 1) - 1;
          if (reactionCounts[previousReaction]! <= 0) {
            reactionCounts.remove(previousReaction);
          }
        }

        // Add new reaction
        reactions[currentUser.uid] = emoji;
        reactionCounts[emoji] = (reactionCounts[emoji] ?? 0) + 1;

        // Calculate total reaction count
        final totalReactionCount = reactionCounts.values.fold(
          0,
          (total, reactionCount) => total + reactionCount,
        );

        transaction.update(commentRef, {
          'reactions': reactions,
          'reactionCounts': reactionCounts,
          'reactionCount': totalReactionCount,
        });
      });

      debugPrint('✅ Added reaction $emoji to comment $commentId');
    } catch (e) {
      debugPrint('❌ Error adding reaction: $e');
      throw Exception('Failed to add reaction. Please try again.');
    }
  }

  /// Remove a reaction from a comment
  Future<void> removeReaction({
    required String postId,
    required String commentId,
  }) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    try {
      final commentRef = _firestore
          .collection('posts')
          .doc(postId)
          .collection('comments')
          .doc(commentId);

      await _firestore.runTransaction((transaction) async {
        final commentDoc = await transaction.get(commentRef);

        if (!commentDoc.exists) {
          throw Exception('Comment not found');
        }

        final data = commentDoc.data()!;
        final reactions = Map<String, String>.from(data['reactions'] ?? {});
        final reactionCounts = Map<String, int>.from(
          data['reactionCounts'] ?? {},
        );

        // Remove user's reaction
        final userReaction = reactions.remove(currentUser.uid);
        if (userReaction != null) {
          reactionCounts[userReaction] =
              (reactionCounts[userReaction] ?? 1) - 1;
          if (reactionCounts[userReaction]! <= 0) {
            reactionCounts.remove(userReaction);
          }
        }

        // Calculate total reaction count
        final totalReactionCount = reactionCounts.values.fold(
          0,
          (total, reactionCount) => total + reactionCount,
        );

        transaction.update(commentRef, {
          'reactions': reactions,
          'reactionCounts': reactionCounts,
          'reactionCount': totalReactionCount,
        });
      });

      debugPrint('✅ Removed reaction from comment $commentId');
    } catch (e) {
      debugPrint('❌ Error removing reaction: $e');
      throw Exception('Failed to remove reaction. Please try again.');
    }
  }

  /// Get users who reacted with a specific emoji
  Future<List<Map<String, dynamic>>> getReactionUsers({
    required String postId,
    required String commentId,
    required String emoji,
  }) async {
    try {
      final commentDoc = await _firestore
          .collection('posts')
          .doc(postId)
          .collection('comments')
          .doc(commentId)
          .get();

      if (!commentDoc.exists) {
        return [];
      }

      final reactions = Map<String, String>.from(
        commentDoc.data()!['reactions'] ?? {},
      );
      final userIds = reactions.entries
          .where((entry) => entry.value == emoji)
          .map((entry) => entry.key)
          .toList();

      if (userIds.isEmpty) return [];

      // Fetch user details
      final userDocs = await _firestore
          .collection('users')
          .where(FieldPath.documentId, whereIn: userIds)
          .get();

      return userDocs.docs
          .map(
            (doc) => {
              'id': doc.id,
              'username': doc.data()['username'] ?? '',
              'name': doc.data()['name'] ?? '',
              'profilePictureUrl': doc.data()['profilePictureUrl'] ?? '',
              'isVerified': doc.data()['isVerified'] ?? false,
            },
          )
          .toList();
    } catch (e) {
      debugPrint('❌ Error getting reaction users: $e');
      return [];
    }
  }

  /// Get all reactions for a comment
  Future<Map<String, dynamic>> getCommentReactions({
    required String postId,
    required String commentId,
  }) async {
    try {
      final commentDoc = await _firestore
          .collection('posts')
          .doc(postId)
          .collection('comments')
          .doc(commentId)
          .get();

      if (!commentDoc.exists) {
        return {
          'reactions': <String, String>{},
          'reactionCounts': <String, int>{},
          'reactionCount': 0,
        };
      }

      final data = commentDoc.data()!;
      return {
        'reactions': Map<String, String>.from(data['reactions'] ?? {}),
        'reactionCounts': Map<String, int>.from(data['reactionCounts'] ?? {}),
        'reactionCount': data['reactionCount'] ?? 0,
      };
    } catch (e) {
      debugPrint('❌ Error getting comment reactions: $e');
      return {
        'reactions': <String, String>{},
        'reactionCounts': <String, int>{},
        'reactionCount': 0,
      };
    }
  }

  /// Check if current user has reacted to a comment
  String? getUserReaction({
    required String postId,
    required String commentId,
    required Map<String, String> reactions,
  }) {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return null;

    return reactions[currentUser.uid];
  }

  /// Get popular emojis for quick selection
  List<String> getPopularEmojis() {
    return [
      '❤️', // Heart
      '😂', // Laughing
      '😮', // Surprised
      '😢', // Sad
      '😡', // Angry
      '👏', // Clapping
      '🔥', // Fire
      '💯', // 100
      '🙌', // Praise
      '👍', // Thumbs up
      '👎', // Thumbs down
      '😍', // Heart eyes
    ];
  }

  /// Get emoji name for accessibility
  String getEmojiName(String emoji) {
    const emojiNames = {
      '❤️': 'Heart',
      '😂': 'Laughing',
      '😮': 'Surprised',
      '😢': 'Sad',
      '😡': 'Angry',
      '👏': 'Clapping',
      '🔥': 'Fire',
      '💯': 'Hundred',
      '🙌': 'Praise',
      '👍': 'Thumbs up',
      '👎': 'Thumbs down',
      '😍': 'Heart eyes',
    };

    return emojiNames[emoji] ?? emoji;
  }

  /// Batch update reactions (for migration or bulk operations)
  Future<void> batchUpdateReactions({
    required String postId,
    required List<Map<String, dynamic>> updates,
  }) async {
    try {
      final batch = _firestore.batch();

      for (final update in updates) {
        final commentRef = _firestore
            .collection('posts')
            .doc(postId)
            .collection('comments')
            .doc(update['commentId']);

        batch.update(commentRef, {
          'reactions': update['reactions'] ?? {},
          'reactionCounts': update['reactionCounts'] ?? {},
          'reactionCount': update['reactionCount'] ?? 0,
        });
      }

      await batch.commit();
      debugPrint('✅ Batch updated ${updates.length} comment reactions');
    } catch (e) {
      debugPrint('❌ Error batch updating reactions: $e');
      throw Exception('Failed to update reactions. Please try again.');
    }
  }
}
