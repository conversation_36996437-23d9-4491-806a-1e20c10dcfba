import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/features/feed/models/feed_config_model.dart';
import 'package:billionaires_social/core/services/analytics_service.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:math';

final enhancedFeedServiceProvider = Provider<EnhancedFeedService>((ref) {
  return getIt<EnhancedFeedService>();
});

class EnhancedFeedService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final AnalyticsService _analyticsService = getIt<AnalyticsService>();
  final FirebaseAuth _auth = FirebaseAuth.instance;

  String? get currentUserId => _auth.currentUser?.uid;

  // Get hybrid feed (following + suggested content)
  Stream<List<Post>> getHybridFeedStream({
    required FeedConfig config,
    String? lastPostId,
    int limit = 20,
  }) {
    return _firestore
        .collection('posts')
        .orderBy('createdAt', descending: true)
        .limit(limit)
        .snapshots()
        .asyncMap((snapshot) async {
          final posts = await _processPosts(snapshot.docs);
          return _applyFeedConfig(posts, config);
        });
  }

  // Get following feed (posts from followed users only)
  Stream<List<Post>> getFollowingFeedStream({
    required FeedConfig config,
    String? lastPostId,
    int limit = 20,
  }) async* {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return;

    // Get user's following list
    final followingSnapshot = await _firestore
        .collection('users')
        .doc(currentUser.uid)
        .collection('following')
        .get();

    final followingIds = followingSnapshot.docs.map((doc) => doc.id).toList();
    if (followingIds.isEmpty) return;

    // Fetch posts from followed users
    final postsQuery = _firestore
        .collection('posts')
        .where('userId', whereIn: followingIds)
        .orderBy('createdAt', descending: true)
        .limit(limit);

    yield* postsQuery.snapshots().asyncMap((snapshot) async {
      final posts = await _processPosts(snapshot.docs);
      return _applyFeedConfig(posts, config);
    });
  }

  // Get suggested feed (AI-powered recommendations)
  Stream<List<Post>> getSuggestedFeedStream({
    required FeedConfig config,
    String? lastPostId,
    int limit = 20,
  }) async* {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return;

    // Get user preferences and interests
    final userPreferences = await _getUserPreferences(currentUser.uid);
    final userInterests = userPreferences?.userInterests ?? {};

    // Get trending posts and posts from users with similar interests
    final suggestedPosts = await _getSuggestedPosts(
      currentUser.uid,
      userInterests,
      limit,
    );

    yield _applyFeedConfig(suggestedPosts, config);
  }

  // Get trending feed
  Stream<List<Post>> getTrendingFeedStream({
    required FeedConfig config,
    String? lastPostId,
    int limit = 20,
  }) {
    return _firestore
        .collection('posts')
        .where('trending', isEqualTo: true)
        .orderBy('trendingScore', descending: true)
        .orderBy('createdAt', descending: true)
        .limit(limit)
        .snapshots()
        .asyncMap((snapshot) async {
          final posts = await _processPosts(snapshot.docs);
          return _applyFeedConfig(posts, config);
        });
  }

  // Get business feed
  Stream<List<Post>> getBusinessFeedStream({
    required FeedConfig config,
    String? lastPostId,
    int limit = 20,
  }) {
    return _firestore
        .collection('posts')
        .where('userType', isEqualTo: 'business')
        .orderBy('createdAt', descending: true)
        .limit(limit)
        .snapshots()
        .asyncMap((snapshot) async {
          final posts = await _processPosts(snapshot.docs);
          return _applyFeedConfig(posts, config);
        });
  }

  // Get verified users feed
  Stream<List<Post>> getVerifiedFeedStream({
    required FeedConfig config,
    String? lastPostId,
    int limit = 20,
  }) {
    return _firestore
        .collection('posts')
        .where('isVerified', isEqualTo: true)
        .orderBy('createdAt', descending: true)
        .limit(limit)
        .snapshots()
        .asyncMap((snapshot) async {
          final posts = await _processPosts(snapshot.docs);
          return _applyFeedConfig(posts, config);
        });
  }

  // Process raw Firestore documents into Post objects
  Future<List<Post>> _processPosts(List<QueryDocumentSnapshot> docs) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return [];

    final posts = <Post>[];

    for (final doc in docs) {
      final data = doc.data() as Map<String, dynamic>;

      // Check if user has liked/bookmarked this post
      final isLiked = await _checkUserLike(doc.id, currentUser.uid);
      final isBookmarked = await _checkUserBookmark(doc.id, currentUser.uid);

      final post = Post(
        id: doc.id,
        userId: data['userId'] ?? '',
        username: data['username'] ?? '',
        userAvatarUrl: data['userAvatarUrl'] ?? '',
        userRole: data['userRole'],
        isVerified: data['isVerified'] ?? false,
        mediaType: MediaType.values.firstWhere(
          (e) => e.name == (data['mediaType'] ?? 'image'),
          orElse: () => MediaType.image,
        ),
        mediaUrl: data['mediaUrl'] ?? '',
        caption: data['caption'] ?? '',
        location: data['location'],
        locationId: data['locationId'],
        likeCount: data['likeCount'] ?? 0,
        commentCount: data['commentCount'] ?? 0,
        timestamp:
            (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
        isLiked: isLiked,
        isBookmarked: isBookmarked,
        isReposted: data['isReposted'] ?? false,
        coAuthorIds: data['coAuthorIds'] != null
            ? List<String>.from(data['coAuthorIds'])
            : null,
        coAuthorUsernames: data['coAuthorUsernames'] != null
            ? List<String>.from(data['coAuthorUsernames'])
            : null,
        coAuthorAvatars: data['coAuthorAvatars'] != null
            ? List<String>.from(data['coAuthorAvatars'])
            : null,
        mentionedUsers: data['mentionedUsers'] != null
            ? List<String>.from(data['mentionedUsers'])
            : null,
        hashtags: data['hashtags'] != null
            ? List<String>.from(data['hashtags'])
            : null,
        mediaTags: data['mediaTags'] != null
            ? (data['mediaTags'] as List)
                  .map((tag) => MediaTag.fromJson(tag))
                  .toList()
            : null,
        likedBy: data['likedBy'] != null
            ? List<String>.from(data['likedBy'])
            : null,
        bookmarkedBy: data['bookmarkedBy'] != null
            ? List<String>.from(data['bookmarkedBy'])
            : null,
        repostedBy: data['repostedBy'] != null
            ? List<String>.from(data['repostedBy'])
            : null,
        isPublic: data['isPublic'] ?? true,
        visibility: data['visibility'] ?? 'public',
        isArchived: data['isArchived'] ?? false,
        isReported: data['isReported'] ?? false,
        isPinned: data['isPinned'] ?? false,
        viewCount: data['viewCount'],
        shareCount: data['shareCount'],
        trending: data['trending'] ?? false,
        trendingScore: data['trendingScore'],
        lastEditedAt: data['lastEditedAt'] != null
            ? (data['lastEditedAt'] as Timestamp).toDate()
            : null,
        editedBy: data['editedBy'],
      );

      posts.add(post);
    }

    return posts;
  }

  // Apply feed configuration filters
  List<Post> _applyFeedConfig(List<Post> posts, FeedConfig config) {
    var filteredPosts = posts;

    // Apply media filter
    if (config.mediaFilter != MediaFilter.all) {
      filteredPosts = filteredPosts.where((post) {
        switch (config.mediaFilter) {
          case MediaFilter.photos:
            return post.mediaType == MediaType.image;
          case MediaFilter.videos:
            return post.mediaType == MediaType.video;
          case MediaFilter.text:
            return post.mediaType == MediaType.text;
          default:
            return true;
        }
      }).toList();
    }

    // Apply business filter
    if (!config.showBusinessPosts) {
      filteredPosts = filteredPosts.where((post) {
        return post.userRole?.toLowerCase().contains('business') == true
            ? false
            : true;
      }).toList();
    }

    // Apply verified filter
    if (!config.showVerifiedPosts) {
      filteredPosts = filteredPosts
          .where((post) => post.isVerified != true)
          .toList();
    }

    // Apply trending filter
    if (!config.showTrendingPosts) {
      filteredPosts = filteredPosts
          .where((post) => post.trending != true)
          .toList();
    }

    // Exclude muted users
    if (config.excludedUserIds.isNotEmpty) {
      filteredPosts = filteredPosts.where((post) {
        return !config.excludedUserIds.contains(post.userId);
      }).toList();
    }

    // Exclude muted hashtags
    if (config.excludedHashtags.isNotEmpty) {
      filteredPosts = filteredPosts.where((post) {
        if (post.hashtags == null) return true;
        return !post.hashtags!.any(
          (hashtag) => config.excludedHashtags.contains(hashtag),
        );
      }).toList();
    }

    // Apply sort order
    switch (config.sortOrder) {
      case SortOrder.latest:
        filteredPosts.sort((a, b) => b.timestamp.compareTo(a.timestamp));
        break;
      case SortOrder.mostPopular:
        filteredPosts.sort((a, b) => b.likeCount.compareTo(a.likeCount));
        break;
      case SortOrder.trending:
        filteredPosts.sort((a, b) {
          final aScore = a.trendingScore ?? 0;
          final bScore = b.trendingScore ?? 0;
          return bScore.compareTo(aScore);
        });
        break;
      case SortOrder.mostEngaged:
        filteredPosts.sort((a, b) {
          final aEngagement =
              a.likeCount + a.commentCount + (a.shareCount ?? 0);
          final bEngagement =
              b.likeCount + b.commentCount + (b.shareCount ?? 0);
          return bEngagement.compareTo(aEngagement);
        });
        break;
    }

    return filteredPosts;
  }

  // Get AI-powered suggested posts
  Future<List<Post>> _getSuggestedPosts(
    String userId,
    Map<String, dynamic> userInterests,
    int limit,
  ) async {
    final posts = <Post>[];

    // Get trending posts (30% of suggestions)
    final trendingLimit = (limit * 0.3).round();
    final trendingPosts = await _getTrendingPosts(trendingLimit);
    posts.addAll(trendingPosts);

    // Get posts from users with similar interests (40% of suggestions)
    final similarLimit = (limit * 0.4).round();
    final similarPosts = await _getPostsFromSimilarUsers(
      userId,
      userInterests,
      similarLimit,
    );
    posts.addAll(similarPosts);

    // Get posts from followed topics (30% of suggestions)
    final topicLimit = limit - posts.length;
    if (topicLimit > 0) {
      final topicPosts = await _getPostsFromFollowedTopics(
        userInterests,
        topicLimit,
      );
      posts.addAll(topicPosts);
    }

    // Shuffle to create natural feed flow
    final random = Random();
    posts.shuffle(random);

    return posts.take(limit).toList();
  }

  // Get trending posts
  Future<List<Post>> _getTrendingPosts(int limit) async {
    final snapshot = await _firestore
        .collection('posts')
        .where('trending', isEqualTo: true)
        .orderBy('trendingScore', descending: true)
        .limit(limit)
        .get();

    return _processPosts(snapshot.docs);
  }

  // Get posts from users with similar interests
  Future<List<Post>> _getPostsFromSimilarUsers(
    String userId,
    Map<String, dynamic> userInterests,
    int limit,
  ) async {
    // This is a simplified implementation
    // In a real app, you'd use ML models to find similar users
    final snapshot = await _firestore
        .collection('posts')
        .orderBy('likeCount', descending: true)
        .limit(limit * 2) // Get more to filter
        .get();

    final posts = await _processPosts(snapshot.docs);

    // Filter by user interests (simplified)
    return posts
        .where((post) {
          if (post.hashtags == null) return false;
          return post.hashtags!.any(
            (hashtag) => userInterests.containsKey(hashtag.toLowerCase()),
          );
        })
        .take(limit)
        .toList();
  }

  // Get posts from followed topics
  Future<List<Post>> _getPostsFromFollowedTopics(
    Map<String, dynamic> userInterests,
    int limit,
  ) async {
    if (userInterests.isEmpty) return [];

    final topics = userInterests.keys.take(3).toList(); // Top 3 interests
    final posts = <Post>[];

    for (final topic in topics) {
      final snapshot = await _firestore
          .collection('posts')
          .where('hashtags', arrayContains: '#$topic')
          .orderBy('createdAt', descending: true)
          .limit(limit ~/ topics.length)
          .get();

      final topicPosts = await _processPosts(snapshot.docs);
      posts.addAll(topicPosts);
    }

    return posts;
  }

  // Check if user has liked a post
  Future<bool> _checkUserLike(String postId, String userId) async {
    try {
      final likeDoc = await _firestore
          .collection('posts')
          .doc(postId)
          .collection('likes')
          .doc(userId)
          .get();
      return likeDoc.exists;
    } catch (e) {
      return false;
    }
  }

  // Check if user has bookmarked a post
  Future<bool> _checkUserBookmark(String postId, String userId) async {
    try {
      final bookmarkDoc = await _firestore
          .collection('users')
          .doc(userId)
          .collection('bookmarks')
          .doc(postId)
          .get();
      return bookmarkDoc.exists;
    } catch (e) {
      return false;
    }
  }

  // Get user preferences
  Future<FeedPreferences?> _getUserPreferences(String userId) async {
    try {
      final doc = await _firestore
          .collection('feed_preferences')
          .doc(userId)
          .get();

      if (doc.exists) {
        return FeedPreferences.fromJson(doc.data()!);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting user preferences: $e');
      return null;
    }
  }

  // Save user preferences
  Future<void> saveUserPreferences(FeedPreferences preferences) async {
    try {
      await _firestore
          .collection('feed_preferences')
          .doc(preferences.userId)
          .set(preferences.toJson());
    } catch (e) {
      debugPrint('Error saving user preferences: $e');
      rethrow;
    }
  }

  // Track feed interaction for analytics
  Future<void> trackFeedInteraction({
    required String postId,
    required String interactionType,
    required Map<String, dynamic> metadata,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return;

      await _analyticsService.logEventSafely(
        eventName: 'feed_interaction',
        parameters: {
          'user_id': currentUser.uid,
          'post_id': postId,
          'interaction_type': interactionType,
          ...metadata,
        },
      );
    } catch (e) {
      debugPrint('Error tracking feed interaction: $e');
    }
  }

  // Get feed analytics for a user
  Future<FeedAnalytics?> getFeedAnalytics(String userId, DateTime date) async {
    try {
      final doc = await _firestore
          .collection('feed_analytics')
          .doc(userId)
          .collection('daily')
          .doc(date.toIso8601String().split('T')[0])
          .get();

      if (doc.exists) {
        return FeedAnalytics.fromJson(doc.data()!);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting feed analytics: $e');
      return null;
    }
  }
}
