import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/feed/models/comment_model.dart';
import 'package:billionaires_social/features/feed/services/feed_service.dart';

final commentServiceProvider = Provider<CommentService>((ref) {
  return CommentService(ref.read(feedServiceProvider));
});

class CommentService {
  final FeedService _feedService;

  CommentService(this._feedService);

  /// Get comments stream for a post
  Stream<List<Comment>> getComments(String postId) {
    return _feedService.getComments(postId);
  }

  /// Add a text comment to a post
  Future<void> addComment(
    String postId,
    String text, {
    String? parentId,
  }) async {
    return _feedService.addComment(postId, text, parentId: parentId);
  }

  /// Add a voice comment to a post
  Future<void> addVoiceComment(
    String postId,
    String voiceUrl,
    int duration,
    String? waveformData, {
    String? parentId,
  }) async {
    return _feedService.addVoiceComment(
      postId,
      voiceUrl,
      duration,
      waveformData,
      parentId: parentId,
    );
  }

  /// Edit a comment (only by author)
  Future<void> editComment(
    String postId,
    String commentId,
    String newText,
  ) async {
    return _feedService.editComment(postId, commentId, newText);
  }

  /// Delete a comment (only by author)
  Future<void> deleteComment(String postId, String commentId) async {
    return _feedService.deleteComment(postId, commentId);
  }

  /// Add a reaction to a comment
  Future<bool> addCommentReaction({
    required String postId,
    required String commentId,
    required String emoji,
  }) async {
    return _feedService.addCommentReaction(
      postId: postId,
      commentId: commentId,
      emoji: emoji,
    );
  }

  /// Get comments with pagination support
  Future<PaginationResult<Comment>> getCommentsWithPagination({
    required String postId,
    int limit = 20,
    DocumentSnapshot? startAfter,
  }) async {
    try {
      Query query = FirebaseFirestore.instance
          .collection('posts')
          .doc(postId)
          .collection('comments')
          .orderBy('timestamp', descending: true)
          .limit(limit);

      if (startAfter != null) {
        query = query.startAfterDocument(startAfter);
      }

      final snapshot = await query.get();
      final comments = snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Comment.fromJson({
          ...data,
          'id': doc.id,
        });
      }).toList();

      // Get total count (cached or estimated)
      final totalCount = await _getTotalCommentsCount(postId);

      return PaginationResult<Comment>(
        items: comments,
        hasMore: snapshot.docs.length == limit,
        lastDocument: snapshot.docs.isNotEmpty ? snapshot.docs.last : null,
        totalCount: totalCount,
      );
    } catch (e) {
      debugPrint('❌ Error loading paginated comments: $e');
      throw Exception('Failed to load comments: $e');
    }
  }

  /// Get total comments count for a post
  Future<int> _getTotalCommentsCount(String postId) async {
    try {
      // Try to get cached count from post document
      final postDoc = await FirebaseFirestore.instance
          .collection('posts')
          .doc(postId)
          .get();

      if (postDoc.exists) {
        final data = postDoc.data()!;
        return data['commentCount'] ?? 0;
      }

      return 0;
    } catch (e) {
      debugPrint('❌ Error getting comments count: $e');
      return 0;
    }
  }

  /// Get comment by ID
  Future<Comment?> getCommentById(String postId, String commentId) async {
    try {
      final commentDoc = await FirebaseFirestore.instance
          .collection('posts')
          .doc(postId)
          .collection('comments')
          .doc(commentId)
          .get();

      if (!commentDoc.exists) return null;

      final data = commentDoc.data()!;
      return Comment.fromJson({
        ...data,
        'id': commentDoc.id,
      });
    } catch (e) {
      debugPrint('❌ Error getting comment by ID: $e');
      return null;
    }
  }

  /// Get replies for a comment
  Future<List<Comment>> getReplies(String postId, String parentCommentId) async {
    try {
      final repliesSnapshot = await FirebaseFirestore.instance
          .collection('posts')
          .doc(postId)
          .collection('comments')
          .where('parentId', isEqualTo: parentCommentId)
          .orderBy('timestamp', descending: false)
          .get();

      return repliesSnapshot.docs.map((doc) {
        final data = doc.data();
        return Comment.fromJson({
          ...data,
          'id': doc.id,
        });
      }).toList();
    } catch (e) {
      debugPrint('❌ Error getting replies: $e');
      return [];
    }
  }

  /// Search comments within a post
  Future<List<Comment>> searchComments({
    required String postId,
    required String query,
    int limit = 50,
  }) async {
    try {
      if (query.trim().isEmpty) return [];

      final searchQuery = query.toLowerCase();
      
      // Get all comments for the post (limited)
      final commentsSnapshot = await FirebaseFirestore.instance
          .collection('posts')
          .doc(postId)
          .collection('comments')
          .orderBy('timestamp', descending: true)
          .limit(limit)
          .get();

      final allComments = commentsSnapshot.docs.map((doc) {
        final data = doc.data();
        return Comment.fromJson({
          ...data,
          'id': doc.id,
        });
      }).toList();

      // Filter comments that match the search query
      return allComments.where((comment) {
        return comment.text.toLowerCase().contains(searchQuery) ||
               comment.username.toLowerCase().contains(searchQuery);
      }).toList();
    } catch (e) {
      debugPrint('❌ Error searching comments: $e');
      return [];
    }
  }

  /// Get comments by type
  Future<List<Comment>> getCommentsByType({
    required String postId,
    required CommentType type,
    int limit = 50,
  }) async {
    try {
      final commentsSnapshot = await FirebaseFirestore.instance
          .collection('posts')
          .doc(postId)
          .collection('comments')
          .where('type', isEqualTo: type.name)
          .orderBy('timestamp', descending: true)
          .limit(limit)
          .get();

      return commentsSnapshot.docs.map((doc) {
        final data = doc.data();
        return Comment.fromJson({
          ...data,
          'id': doc.id,
        });
      }).toList();
    } catch (e) {
      debugPrint('❌ Error getting comments by type: $e');
      return [];
    }
  }

  /// Get comment statistics
  Future<Map<String, dynamic>> getCommentStats(String postId) async {
    try {
      final commentsSnapshot = await FirebaseFirestore.instance
          .collection('posts')
          .doc(postId)
          .collection('comments')
          .get();

      int textComments = 0;
      int voiceComments = 0;
      int totalReactions = 0;

      for (final doc in commentsSnapshot.docs) {
        final data = doc.data();
        final type = data['type'] ?? 'text';
        
        if (type == 'voice') {
          voiceComments++;
        } else {
          textComments++;
        }

        totalReactions += (data['reactionCount'] ?? 0) as int;
      }

      return {
        'totalComments': commentsSnapshot.docs.length,
        'textComments': textComments,
        'voiceComments': voiceComments,
        'totalReactions': totalReactions,
      };
    } catch (e) {
      debugPrint('❌ Error getting comment stats: $e');
      return {
        'totalComments': 0,
        'textComments': 0,
        'voiceComments': 0,
        'totalReactions': 0,
      };
    }
  }
}

/// Pagination result wrapper
class PaginationResult<T> {
  final List<T> items;
  final bool hasMore;
  final DocumentSnapshot? lastDocument;
  final int totalCount;

  const PaginationResult({
    required this.items,
    required this.hasMore,
    this.lastDocument,
    required this.totalCount,
  });

  // Convenience getter for comments
  List<Comment> get comments => items.cast<Comment>();
}
