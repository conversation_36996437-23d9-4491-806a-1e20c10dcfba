import 'package:freezed_annotation/freezed_annotation.dart';

part 'reaction_model.freezed.dart';
part 'reaction_model.g.dart';

enum ReactionType { like, love, haha, wow, sad, angry }

@freezed
abstract class PostReaction with _$PostReaction {
  const factory PostReaction({
    required String id,
    required String postId,
    required String userId,
    required String username,
    required String userAvatarUrl,
    required ReactionType type,
    required DateTime timestamp,
  }) = _PostReaction;

  factory PostReaction.fromJson(Map<String, dynamic> json) =>
      _$PostReactionFromJson(json);
}

@freezed
abstract class ReactionCount with _$ReactionCount {
  const factory ReactionCount({
    required ReactionType type,
    required int count,
    @Default(false) bool isUserReaction,
  }) = _ReactionCount;

  factory ReactionCount.fromJson(Map<String, dynamic> json) =>
      _$ReactionCountFromJson(json);
}

@freezed
abstract class ReactionSummary with _$ReactionSummary {
  const factory ReactionSummary({
    required String postId,
    required int totalReactions,
    required List<ReactionCount> reactionCounts,
    ReactionType? userReaction,
    required DateTime lastUpdated,
  }) = _ReactionSummary;

  factory ReactionSummary.fromJson(Map<String, dynamic> json) =>
      _$ReactionSummaryFromJson(json);
}
