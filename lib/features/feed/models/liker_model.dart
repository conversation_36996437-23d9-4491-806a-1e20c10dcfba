import 'package:freezed_annotation/freezed_annotation.dart';

part 'liker_model.freezed.dart';
part 'liker_model.g.dart';

@freezed
abstract class LikerModel with _$LikerModel {
  const factory LikerModel({
    required String id,
    required String username,
    required String name,
    required String profilePictureUrl,
    required bool isVerified,
    required DateTime likedAt,
  }) = _LikerModel;

  factory LikerModel.fromJson(Map<String, dynamic> json) =>
      _$LikerModelFromJson(json);
}
