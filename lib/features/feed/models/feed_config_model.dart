import 'package:freezed_annotation/freezed_annotation.dart';

part 'feed_config_model.freezed.dart';
part 'feed_config_model.g.dart';

enum FeedType {
  following,
  suggested,
  hybrid,
  trending,
  business,
  verified,
  all,
}

enum MediaFilter { all, photos, videos, text }

enum SortOrder { latest, mostPopular, trending, mostEngaged }

@freezed
abstract class FeedConfig with _$FeedConfig {
  const factory FeedConfig({
    @Default(FeedType.hybrid) FeedType feedType,
    @Default(MediaFilter.all) MediaFilter mediaFilter,
    @Default(SortOrder.latest) SortOrder sortOrder,
    @Default(20) int postsPerPage,
    @Default(true) bool showBusinessPosts,
    @Default(true) bool showVerifiedPosts,
    @Default(true) bool showTrendingPosts,
    @Default([]) List<String> excludedUserIds,
    @Default([]) List<String> excludedHashtags,
    @Default([]) List<String> preferredTopics,
    @Default(false) bool autoRefresh,
    @Default(300) int autoRefreshIntervalSeconds,
  }) = _FeedConfig;

  factory FeedConfig.fromJson(Map<String, dynamic> json) =>
      _$FeedConfigFromJson(json);
}

@freezed
abstract class FeedPreferences with _$FeedPreferences {
  const factory FeedPreferences({
    required String userId,
    required FeedConfig config,
    required DateTime lastUpdated,
    @Default({}) Map<String, dynamic> userInterests,
    @Default([]) List<String> followedTopics,
    @Default([]) List<String> mutedUsers,
    @Default([]) List<String> mutedHashtags,
    @Default(true) bool showSensitiveContent,
    @Default(true) bool showPoliticalContent,
    @Default(true) bool showControversialTopics,
  }) = _FeedPreferences;

  factory FeedPreferences.fromJson(Map<String, dynamic> json) =>
      _$FeedPreferencesFromJson(json);
}

@freezed
abstract class FeedAnalytics with _$FeedAnalytics {
  const factory FeedAnalytics({
    required String userId,
    required DateTime date,
    required int postsViewed,
    required int postsLiked,
    required int postsCommented,
    required int postsShared,
    required int timeSpentSeconds,
    required Map<String, int> topicEngagement,
    required Map<String, int> userEngagement,
    required List<String> trendingTopics,
  }) = _FeedAnalytics;

  factory FeedAnalytics.fromJson(Map<String, dynamic> json) =>
      _$FeedAnalyticsFromJson(json);
}
