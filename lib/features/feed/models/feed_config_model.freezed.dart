// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'feed_config_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$FeedConfig {

 FeedType get feedType; MediaFilter get mediaFilter; SortOrder get sortOrder; int get postsPerPage; bool get showBusinessPosts; bool get showVerifiedPosts; bool get showTrendingPosts; List<String> get excludedUserIds; List<String> get excludedHashtags; List<String> get preferredTopics; bool get autoRefresh; int get autoRefreshIntervalSeconds;
/// Create a copy of FeedConfig
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$FeedConfigCopyWith<FeedConfig> get copyWith => _$FeedConfigCopyWithImpl<FeedConfig>(this as FeedConfig, _$identity);

  /// Serializes this FeedConfig to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is FeedConfig&&(identical(other.feedType, feedType) || other.feedType == feedType)&&(identical(other.mediaFilter, mediaFilter) || other.mediaFilter == mediaFilter)&&(identical(other.sortOrder, sortOrder) || other.sortOrder == sortOrder)&&(identical(other.postsPerPage, postsPerPage) || other.postsPerPage == postsPerPage)&&(identical(other.showBusinessPosts, showBusinessPosts) || other.showBusinessPosts == showBusinessPosts)&&(identical(other.showVerifiedPosts, showVerifiedPosts) || other.showVerifiedPosts == showVerifiedPosts)&&(identical(other.showTrendingPosts, showTrendingPosts) || other.showTrendingPosts == showTrendingPosts)&&const DeepCollectionEquality().equals(other.excludedUserIds, excludedUserIds)&&const DeepCollectionEquality().equals(other.excludedHashtags, excludedHashtags)&&const DeepCollectionEquality().equals(other.preferredTopics, preferredTopics)&&(identical(other.autoRefresh, autoRefresh) || other.autoRefresh == autoRefresh)&&(identical(other.autoRefreshIntervalSeconds, autoRefreshIntervalSeconds) || other.autoRefreshIntervalSeconds == autoRefreshIntervalSeconds));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,feedType,mediaFilter,sortOrder,postsPerPage,showBusinessPosts,showVerifiedPosts,showTrendingPosts,const DeepCollectionEquality().hash(excludedUserIds),const DeepCollectionEquality().hash(excludedHashtags),const DeepCollectionEquality().hash(preferredTopics),autoRefresh,autoRefreshIntervalSeconds);

@override
String toString() {
  return 'FeedConfig(feedType: $feedType, mediaFilter: $mediaFilter, sortOrder: $sortOrder, postsPerPage: $postsPerPage, showBusinessPosts: $showBusinessPosts, showVerifiedPosts: $showVerifiedPosts, showTrendingPosts: $showTrendingPosts, excludedUserIds: $excludedUserIds, excludedHashtags: $excludedHashtags, preferredTopics: $preferredTopics, autoRefresh: $autoRefresh, autoRefreshIntervalSeconds: $autoRefreshIntervalSeconds)';
}


}

/// @nodoc
abstract mixin class $FeedConfigCopyWith<$Res>  {
  factory $FeedConfigCopyWith(FeedConfig value, $Res Function(FeedConfig) _then) = _$FeedConfigCopyWithImpl;
@useResult
$Res call({
 FeedType feedType, MediaFilter mediaFilter, SortOrder sortOrder, int postsPerPage, bool showBusinessPosts, bool showVerifiedPosts, bool showTrendingPosts, List<String> excludedUserIds, List<String> excludedHashtags, List<String> preferredTopics, bool autoRefresh, int autoRefreshIntervalSeconds
});




}
/// @nodoc
class _$FeedConfigCopyWithImpl<$Res>
    implements $FeedConfigCopyWith<$Res> {
  _$FeedConfigCopyWithImpl(this._self, this._then);

  final FeedConfig _self;
  final $Res Function(FeedConfig) _then;

/// Create a copy of FeedConfig
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? feedType = null,Object? mediaFilter = null,Object? sortOrder = null,Object? postsPerPage = null,Object? showBusinessPosts = null,Object? showVerifiedPosts = null,Object? showTrendingPosts = null,Object? excludedUserIds = null,Object? excludedHashtags = null,Object? preferredTopics = null,Object? autoRefresh = null,Object? autoRefreshIntervalSeconds = null,}) {
  return _then(_self.copyWith(
feedType: null == feedType ? _self.feedType : feedType // ignore: cast_nullable_to_non_nullable
as FeedType,mediaFilter: null == mediaFilter ? _self.mediaFilter : mediaFilter // ignore: cast_nullable_to_non_nullable
as MediaFilter,sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as SortOrder,postsPerPage: null == postsPerPage ? _self.postsPerPage : postsPerPage // ignore: cast_nullable_to_non_nullable
as int,showBusinessPosts: null == showBusinessPosts ? _self.showBusinessPosts : showBusinessPosts // ignore: cast_nullable_to_non_nullable
as bool,showVerifiedPosts: null == showVerifiedPosts ? _self.showVerifiedPosts : showVerifiedPosts // ignore: cast_nullable_to_non_nullable
as bool,showTrendingPosts: null == showTrendingPosts ? _self.showTrendingPosts : showTrendingPosts // ignore: cast_nullable_to_non_nullable
as bool,excludedUserIds: null == excludedUserIds ? _self.excludedUserIds : excludedUserIds // ignore: cast_nullable_to_non_nullable
as List<String>,excludedHashtags: null == excludedHashtags ? _self.excludedHashtags : excludedHashtags // ignore: cast_nullable_to_non_nullable
as List<String>,preferredTopics: null == preferredTopics ? _self.preferredTopics : preferredTopics // ignore: cast_nullable_to_non_nullable
as List<String>,autoRefresh: null == autoRefresh ? _self.autoRefresh : autoRefresh // ignore: cast_nullable_to_non_nullable
as bool,autoRefreshIntervalSeconds: null == autoRefreshIntervalSeconds ? _self.autoRefreshIntervalSeconds : autoRefreshIntervalSeconds // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [FeedConfig].
extension FeedConfigPatterns on FeedConfig {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _FeedConfig value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _FeedConfig() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _FeedConfig value)  $default,){
final _that = this;
switch (_that) {
case _FeedConfig():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _FeedConfig value)?  $default,){
final _that = this;
switch (_that) {
case _FeedConfig() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( FeedType feedType,  MediaFilter mediaFilter,  SortOrder sortOrder,  int postsPerPage,  bool showBusinessPosts,  bool showVerifiedPosts,  bool showTrendingPosts,  List<String> excludedUserIds,  List<String> excludedHashtags,  List<String> preferredTopics,  bool autoRefresh,  int autoRefreshIntervalSeconds)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _FeedConfig() when $default != null:
return $default(_that.feedType,_that.mediaFilter,_that.sortOrder,_that.postsPerPage,_that.showBusinessPosts,_that.showVerifiedPosts,_that.showTrendingPosts,_that.excludedUserIds,_that.excludedHashtags,_that.preferredTopics,_that.autoRefresh,_that.autoRefreshIntervalSeconds);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( FeedType feedType,  MediaFilter mediaFilter,  SortOrder sortOrder,  int postsPerPage,  bool showBusinessPosts,  bool showVerifiedPosts,  bool showTrendingPosts,  List<String> excludedUserIds,  List<String> excludedHashtags,  List<String> preferredTopics,  bool autoRefresh,  int autoRefreshIntervalSeconds)  $default,) {final _that = this;
switch (_that) {
case _FeedConfig():
return $default(_that.feedType,_that.mediaFilter,_that.sortOrder,_that.postsPerPage,_that.showBusinessPosts,_that.showVerifiedPosts,_that.showTrendingPosts,_that.excludedUserIds,_that.excludedHashtags,_that.preferredTopics,_that.autoRefresh,_that.autoRefreshIntervalSeconds);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( FeedType feedType,  MediaFilter mediaFilter,  SortOrder sortOrder,  int postsPerPage,  bool showBusinessPosts,  bool showVerifiedPosts,  bool showTrendingPosts,  List<String> excludedUserIds,  List<String> excludedHashtags,  List<String> preferredTopics,  bool autoRefresh,  int autoRefreshIntervalSeconds)?  $default,) {final _that = this;
switch (_that) {
case _FeedConfig() when $default != null:
return $default(_that.feedType,_that.mediaFilter,_that.sortOrder,_that.postsPerPage,_that.showBusinessPosts,_that.showVerifiedPosts,_that.showTrendingPosts,_that.excludedUserIds,_that.excludedHashtags,_that.preferredTopics,_that.autoRefresh,_that.autoRefreshIntervalSeconds);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _FeedConfig implements FeedConfig {
  const _FeedConfig({this.feedType = FeedType.hybrid, this.mediaFilter = MediaFilter.all, this.sortOrder = SortOrder.latest, this.postsPerPage = 20, this.showBusinessPosts = true, this.showVerifiedPosts = true, this.showTrendingPosts = true, final  List<String> excludedUserIds = const [], final  List<String> excludedHashtags = const [], final  List<String> preferredTopics = const [], this.autoRefresh = false, this.autoRefreshIntervalSeconds = 300}): _excludedUserIds = excludedUserIds,_excludedHashtags = excludedHashtags,_preferredTopics = preferredTopics;
  factory _FeedConfig.fromJson(Map<String, dynamic> json) => _$FeedConfigFromJson(json);

@override@JsonKey() final  FeedType feedType;
@override@JsonKey() final  MediaFilter mediaFilter;
@override@JsonKey() final  SortOrder sortOrder;
@override@JsonKey() final  int postsPerPage;
@override@JsonKey() final  bool showBusinessPosts;
@override@JsonKey() final  bool showVerifiedPosts;
@override@JsonKey() final  bool showTrendingPosts;
 final  List<String> _excludedUserIds;
@override@JsonKey() List<String> get excludedUserIds {
  if (_excludedUserIds is EqualUnmodifiableListView) return _excludedUserIds;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_excludedUserIds);
}

 final  List<String> _excludedHashtags;
@override@JsonKey() List<String> get excludedHashtags {
  if (_excludedHashtags is EqualUnmodifiableListView) return _excludedHashtags;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_excludedHashtags);
}

 final  List<String> _preferredTopics;
@override@JsonKey() List<String> get preferredTopics {
  if (_preferredTopics is EqualUnmodifiableListView) return _preferredTopics;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_preferredTopics);
}

@override@JsonKey() final  bool autoRefresh;
@override@JsonKey() final  int autoRefreshIntervalSeconds;

/// Create a copy of FeedConfig
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$FeedConfigCopyWith<_FeedConfig> get copyWith => __$FeedConfigCopyWithImpl<_FeedConfig>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$FeedConfigToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _FeedConfig&&(identical(other.feedType, feedType) || other.feedType == feedType)&&(identical(other.mediaFilter, mediaFilter) || other.mediaFilter == mediaFilter)&&(identical(other.sortOrder, sortOrder) || other.sortOrder == sortOrder)&&(identical(other.postsPerPage, postsPerPage) || other.postsPerPage == postsPerPage)&&(identical(other.showBusinessPosts, showBusinessPosts) || other.showBusinessPosts == showBusinessPosts)&&(identical(other.showVerifiedPosts, showVerifiedPosts) || other.showVerifiedPosts == showVerifiedPosts)&&(identical(other.showTrendingPosts, showTrendingPosts) || other.showTrendingPosts == showTrendingPosts)&&const DeepCollectionEquality().equals(other._excludedUserIds, _excludedUserIds)&&const DeepCollectionEquality().equals(other._excludedHashtags, _excludedHashtags)&&const DeepCollectionEquality().equals(other._preferredTopics, _preferredTopics)&&(identical(other.autoRefresh, autoRefresh) || other.autoRefresh == autoRefresh)&&(identical(other.autoRefreshIntervalSeconds, autoRefreshIntervalSeconds) || other.autoRefreshIntervalSeconds == autoRefreshIntervalSeconds));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,feedType,mediaFilter,sortOrder,postsPerPage,showBusinessPosts,showVerifiedPosts,showTrendingPosts,const DeepCollectionEquality().hash(_excludedUserIds),const DeepCollectionEquality().hash(_excludedHashtags),const DeepCollectionEquality().hash(_preferredTopics),autoRefresh,autoRefreshIntervalSeconds);

@override
String toString() {
  return 'FeedConfig(feedType: $feedType, mediaFilter: $mediaFilter, sortOrder: $sortOrder, postsPerPage: $postsPerPage, showBusinessPosts: $showBusinessPosts, showVerifiedPosts: $showVerifiedPosts, showTrendingPosts: $showTrendingPosts, excludedUserIds: $excludedUserIds, excludedHashtags: $excludedHashtags, preferredTopics: $preferredTopics, autoRefresh: $autoRefresh, autoRefreshIntervalSeconds: $autoRefreshIntervalSeconds)';
}


}

/// @nodoc
abstract mixin class _$FeedConfigCopyWith<$Res> implements $FeedConfigCopyWith<$Res> {
  factory _$FeedConfigCopyWith(_FeedConfig value, $Res Function(_FeedConfig) _then) = __$FeedConfigCopyWithImpl;
@override @useResult
$Res call({
 FeedType feedType, MediaFilter mediaFilter, SortOrder sortOrder, int postsPerPage, bool showBusinessPosts, bool showVerifiedPosts, bool showTrendingPosts, List<String> excludedUserIds, List<String> excludedHashtags, List<String> preferredTopics, bool autoRefresh, int autoRefreshIntervalSeconds
});




}
/// @nodoc
class __$FeedConfigCopyWithImpl<$Res>
    implements _$FeedConfigCopyWith<$Res> {
  __$FeedConfigCopyWithImpl(this._self, this._then);

  final _FeedConfig _self;
  final $Res Function(_FeedConfig) _then;

/// Create a copy of FeedConfig
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? feedType = null,Object? mediaFilter = null,Object? sortOrder = null,Object? postsPerPage = null,Object? showBusinessPosts = null,Object? showVerifiedPosts = null,Object? showTrendingPosts = null,Object? excludedUserIds = null,Object? excludedHashtags = null,Object? preferredTopics = null,Object? autoRefresh = null,Object? autoRefreshIntervalSeconds = null,}) {
  return _then(_FeedConfig(
feedType: null == feedType ? _self.feedType : feedType // ignore: cast_nullable_to_non_nullable
as FeedType,mediaFilter: null == mediaFilter ? _self.mediaFilter : mediaFilter // ignore: cast_nullable_to_non_nullable
as MediaFilter,sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as SortOrder,postsPerPage: null == postsPerPage ? _self.postsPerPage : postsPerPage // ignore: cast_nullable_to_non_nullable
as int,showBusinessPosts: null == showBusinessPosts ? _self.showBusinessPosts : showBusinessPosts // ignore: cast_nullable_to_non_nullable
as bool,showVerifiedPosts: null == showVerifiedPosts ? _self.showVerifiedPosts : showVerifiedPosts // ignore: cast_nullable_to_non_nullable
as bool,showTrendingPosts: null == showTrendingPosts ? _self.showTrendingPosts : showTrendingPosts // ignore: cast_nullable_to_non_nullable
as bool,excludedUserIds: null == excludedUserIds ? _self._excludedUserIds : excludedUserIds // ignore: cast_nullable_to_non_nullable
as List<String>,excludedHashtags: null == excludedHashtags ? _self._excludedHashtags : excludedHashtags // ignore: cast_nullable_to_non_nullable
as List<String>,preferredTopics: null == preferredTopics ? _self._preferredTopics : preferredTopics // ignore: cast_nullable_to_non_nullable
as List<String>,autoRefresh: null == autoRefresh ? _self.autoRefresh : autoRefresh // ignore: cast_nullable_to_non_nullable
as bool,autoRefreshIntervalSeconds: null == autoRefreshIntervalSeconds ? _self.autoRefreshIntervalSeconds : autoRefreshIntervalSeconds // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}


/// @nodoc
mixin _$FeedPreferences {

 String get userId; FeedConfig get config; DateTime get lastUpdated; Map<String, dynamic> get userInterests; List<String> get followedTopics; List<String> get mutedUsers; List<String> get mutedHashtags; bool get showSensitiveContent; bool get showPoliticalContent; bool get showControversialTopics;
/// Create a copy of FeedPreferences
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$FeedPreferencesCopyWith<FeedPreferences> get copyWith => _$FeedPreferencesCopyWithImpl<FeedPreferences>(this as FeedPreferences, _$identity);

  /// Serializes this FeedPreferences to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is FeedPreferences&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.config, config) || other.config == config)&&(identical(other.lastUpdated, lastUpdated) || other.lastUpdated == lastUpdated)&&const DeepCollectionEquality().equals(other.userInterests, userInterests)&&const DeepCollectionEquality().equals(other.followedTopics, followedTopics)&&const DeepCollectionEquality().equals(other.mutedUsers, mutedUsers)&&const DeepCollectionEquality().equals(other.mutedHashtags, mutedHashtags)&&(identical(other.showSensitiveContent, showSensitiveContent) || other.showSensitiveContent == showSensitiveContent)&&(identical(other.showPoliticalContent, showPoliticalContent) || other.showPoliticalContent == showPoliticalContent)&&(identical(other.showControversialTopics, showControversialTopics) || other.showControversialTopics == showControversialTopics));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,userId,config,lastUpdated,const DeepCollectionEquality().hash(userInterests),const DeepCollectionEquality().hash(followedTopics),const DeepCollectionEquality().hash(mutedUsers),const DeepCollectionEquality().hash(mutedHashtags),showSensitiveContent,showPoliticalContent,showControversialTopics);

@override
String toString() {
  return 'FeedPreferences(userId: $userId, config: $config, lastUpdated: $lastUpdated, userInterests: $userInterests, followedTopics: $followedTopics, mutedUsers: $mutedUsers, mutedHashtags: $mutedHashtags, showSensitiveContent: $showSensitiveContent, showPoliticalContent: $showPoliticalContent, showControversialTopics: $showControversialTopics)';
}


}

/// @nodoc
abstract mixin class $FeedPreferencesCopyWith<$Res>  {
  factory $FeedPreferencesCopyWith(FeedPreferences value, $Res Function(FeedPreferences) _then) = _$FeedPreferencesCopyWithImpl;
@useResult
$Res call({
 String userId, FeedConfig config, DateTime lastUpdated, Map<String, dynamic> userInterests, List<String> followedTopics, List<String> mutedUsers, List<String> mutedHashtags, bool showSensitiveContent, bool showPoliticalContent, bool showControversialTopics
});


$FeedConfigCopyWith<$Res> get config;

}
/// @nodoc
class _$FeedPreferencesCopyWithImpl<$Res>
    implements $FeedPreferencesCopyWith<$Res> {
  _$FeedPreferencesCopyWithImpl(this._self, this._then);

  final FeedPreferences _self;
  final $Res Function(FeedPreferences) _then;

/// Create a copy of FeedPreferences
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? userId = null,Object? config = null,Object? lastUpdated = null,Object? userInterests = null,Object? followedTopics = null,Object? mutedUsers = null,Object? mutedHashtags = null,Object? showSensitiveContent = null,Object? showPoliticalContent = null,Object? showControversialTopics = null,}) {
  return _then(_self.copyWith(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,config: null == config ? _self.config : config // ignore: cast_nullable_to_non_nullable
as FeedConfig,lastUpdated: null == lastUpdated ? _self.lastUpdated : lastUpdated // ignore: cast_nullable_to_non_nullable
as DateTime,userInterests: null == userInterests ? _self.userInterests : userInterests // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,followedTopics: null == followedTopics ? _self.followedTopics : followedTopics // ignore: cast_nullable_to_non_nullable
as List<String>,mutedUsers: null == mutedUsers ? _self.mutedUsers : mutedUsers // ignore: cast_nullable_to_non_nullable
as List<String>,mutedHashtags: null == mutedHashtags ? _self.mutedHashtags : mutedHashtags // ignore: cast_nullable_to_non_nullable
as List<String>,showSensitiveContent: null == showSensitiveContent ? _self.showSensitiveContent : showSensitiveContent // ignore: cast_nullable_to_non_nullable
as bool,showPoliticalContent: null == showPoliticalContent ? _self.showPoliticalContent : showPoliticalContent // ignore: cast_nullable_to_non_nullable
as bool,showControversialTopics: null == showControversialTopics ? _self.showControversialTopics : showControversialTopics // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}
/// Create a copy of FeedPreferences
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$FeedConfigCopyWith<$Res> get config {
  
  return $FeedConfigCopyWith<$Res>(_self.config, (value) {
    return _then(_self.copyWith(config: value));
  });
}
}


/// Adds pattern-matching-related methods to [FeedPreferences].
extension FeedPreferencesPatterns on FeedPreferences {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _FeedPreferences value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _FeedPreferences() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _FeedPreferences value)  $default,){
final _that = this;
switch (_that) {
case _FeedPreferences():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _FeedPreferences value)?  $default,){
final _that = this;
switch (_that) {
case _FeedPreferences() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String userId,  FeedConfig config,  DateTime lastUpdated,  Map<String, dynamic> userInterests,  List<String> followedTopics,  List<String> mutedUsers,  List<String> mutedHashtags,  bool showSensitiveContent,  bool showPoliticalContent,  bool showControversialTopics)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _FeedPreferences() when $default != null:
return $default(_that.userId,_that.config,_that.lastUpdated,_that.userInterests,_that.followedTopics,_that.mutedUsers,_that.mutedHashtags,_that.showSensitiveContent,_that.showPoliticalContent,_that.showControversialTopics);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String userId,  FeedConfig config,  DateTime lastUpdated,  Map<String, dynamic> userInterests,  List<String> followedTopics,  List<String> mutedUsers,  List<String> mutedHashtags,  bool showSensitiveContent,  bool showPoliticalContent,  bool showControversialTopics)  $default,) {final _that = this;
switch (_that) {
case _FeedPreferences():
return $default(_that.userId,_that.config,_that.lastUpdated,_that.userInterests,_that.followedTopics,_that.mutedUsers,_that.mutedHashtags,_that.showSensitiveContent,_that.showPoliticalContent,_that.showControversialTopics);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String userId,  FeedConfig config,  DateTime lastUpdated,  Map<String, dynamic> userInterests,  List<String> followedTopics,  List<String> mutedUsers,  List<String> mutedHashtags,  bool showSensitiveContent,  bool showPoliticalContent,  bool showControversialTopics)?  $default,) {final _that = this;
switch (_that) {
case _FeedPreferences() when $default != null:
return $default(_that.userId,_that.config,_that.lastUpdated,_that.userInterests,_that.followedTopics,_that.mutedUsers,_that.mutedHashtags,_that.showSensitiveContent,_that.showPoliticalContent,_that.showControversialTopics);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _FeedPreferences implements FeedPreferences {
  const _FeedPreferences({required this.userId, required this.config, required this.lastUpdated, final  Map<String, dynamic> userInterests = const {}, final  List<String> followedTopics = const [], final  List<String> mutedUsers = const [], final  List<String> mutedHashtags = const [], this.showSensitiveContent = true, this.showPoliticalContent = true, this.showControversialTopics = true}): _userInterests = userInterests,_followedTopics = followedTopics,_mutedUsers = mutedUsers,_mutedHashtags = mutedHashtags;
  factory _FeedPreferences.fromJson(Map<String, dynamic> json) => _$FeedPreferencesFromJson(json);

@override final  String userId;
@override final  FeedConfig config;
@override final  DateTime lastUpdated;
 final  Map<String, dynamic> _userInterests;
@override@JsonKey() Map<String, dynamic> get userInterests {
  if (_userInterests is EqualUnmodifiableMapView) return _userInterests;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_userInterests);
}

 final  List<String> _followedTopics;
@override@JsonKey() List<String> get followedTopics {
  if (_followedTopics is EqualUnmodifiableListView) return _followedTopics;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_followedTopics);
}

 final  List<String> _mutedUsers;
@override@JsonKey() List<String> get mutedUsers {
  if (_mutedUsers is EqualUnmodifiableListView) return _mutedUsers;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_mutedUsers);
}

 final  List<String> _mutedHashtags;
@override@JsonKey() List<String> get mutedHashtags {
  if (_mutedHashtags is EqualUnmodifiableListView) return _mutedHashtags;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_mutedHashtags);
}

@override@JsonKey() final  bool showSensitiveContent;
@override@JsonKey() final  bool showPoliticalContent;
@override@JsonKey() final  bool showControversialTopics;

/// Create a copy of FeedPreferences
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$FeedPreferencesCopyWith<_FeedPreferences> get copyWith => __$FeedPreferencesCopyWithImpl<_FeedPreferences>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$FeedPreferencesToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _FeedPreferences&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.config, config) || other.config == config)&&(identical(other.lastUpdated, lastUpdated) || other.lastUpdated == lastUpdated)&&const DeepCollectionEquality().equals(other._userInterests, _userInterests)&&const DeepCollectionEquality().equals(other._followedTopics, _followedTopics)&&const DeepCollectionEquality().equals(other._mutedUsers, _mutedUsers)&&const DeepCollectionEquality().equals(other._mutedHashtags, _mutedHashtags)&&(identical(other.showSensitiveContent, showSensitiveContent) || other.showSensitiveContent == showSensitiveContent)&&(identical(other.showPoliticalContent, showPoliticalContent) || other.showPoliticalContent == showPoliticalContent)&&(identical(other.showControversialTopics, showControversialTopics) || other.showControversialTopics == showControversialTopics));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,userId,config,lastUpdated,const DeepCollectionEquality().hash(_userInterests),const DeepCollectionEquality().hash(_followedTopics),const DeepCollectionEquality().hash(_mutedUsers),const DeepCollectionEquality().hash(_mutedHashtags),showSensitiveContent,showPoliticalContent,showControversialTopics);

@override
String toString() {
  return 'FeedPreferences(userId: $userId, config: $config, lastUpdated: $lastUpdated, userInterests: $userInterests, followedTopics: $followedTopics, mutedUsers: $mutedUsers, mutedHashtags: $mutedHashtags, showSensitiveContent: $showSensitiveContent, showPoliticalContent: $showPoliticalContent, showControversialTopics: $showControversialTopics)';
}


}

/// @nodoc
abstract mixin class _$FeedPreferencesCopyWith<$Res> implements $FeedPreferencesCopyWith<$Res> {
  factory _$FeedPreferencesCopyWith(_FeedPreferences value, $Res Function(_FeedPreferences) _then) = __$FeedPreferencesCopyWithImpl;
@override @useResult
$Res call({
 String userId, FeedConfig config, DateTime lastUpdated, Map<String, dynamic> userInterests, List<String> followedTopics, List<String> mutedUsers, List<String> mutedHashtags, bool showSensitiveContent, bool showPoliticalContent, bool showControversialTopics
});


@override $FeedConfigCopyWith<$Res> get config;

}
/// @nodoc
class __$FeedPreferencesCopyWithImpl<$Res>
    implements _$FeedPreferencesCopyWith<$Res> {
  __$FeedPreferencesCopyWithImpl(this._self, this._then);

  final _FeedPreferences _self;
  final $Res Function(_FeedPreferences) _then;

/// Create a copy of FeedPreferences
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? userId = null,Object? config = null,Object? lastUpdated = null,Object? userInterests = null,Object? followedTopics = null,Object? mutedUsers = null,Object? mutedHashtags = null,Object? showSensitiveContent = null,Object? showPoliticalContent = null,Object? showControversialTopics = null,}) {
  return _then(_FeedPreferences(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,config: null == config ? _self.config : config // ignore: cast_nullable_to_non_nullable
as FeedConfig,lastUpdated: null == lastUpdated ? _self.lastUpdated : lastUpdated // ignore: cast_nullable_to_non_nullable
as DateTime,userInterests: null == userInterests ? _self._userInterests : userInterests // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,followedTopics: null == followedTopics ? _self._followedTopics : followedTopics // ignore: cast_nullable_to_non_nullable
as List<String>,mutedUsers: null == mutedUsers ? _self._mutedUsers : mutedUsers // ignore: cast_nullable_to_non_nullable
as List<String>,mutedHashtags: null == mutedHashtags ? _self._mutedHashtags : mutedHashtags // ignore: cast_nullable_to_non_nullable
as List<String>,showSensitiveContent: null == showSensitiveContent ? _self.showSensitiveContent : showSensitiveContent // ignore: cast_nullable_to_non_nullable
as bool,showPoliticalContent: null == showPoliticalContent ? _self.showPoliticalContent : showPoliticalContent // ignore: cast_nullable_to_non_nullable
as bool,showControversialTopics: null == showControversialTopics ? _self.showControversialTopics : showControversialTopics // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

/// Create a copy of FeedPreferences
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$FeedConfigCopyWith<$Res> get config {
  
  return $FeedConfigCopyWith<$Res>(_self.config, (value) {
    return _then(_self.copyWith(config: value));
  });
}
}


/// @nodoc
mixin _$FeedAnalytics {

 String get userId; DateTime get date; int get postsViewed; int get postsLiked; int get postsCommented; int get postsShared; int get timeSpentSeconds; Map<String, int> get topicEngagement; Map<String, int> get userEngagement; List<String> get trendingTopics;
/// Create a copy of FeedAnalytics
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$FeedAnalyticsCopyWith<FeedAnalytics> get copyWith => _$FeedAnalyticsCopyWithImpl<FeedAnalytics>(this as FeedAnalytics, _$identity);

  /// Serializes this FeedAnalytics to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is FeedAnalytics&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.date, date) || other.date == date)&&(identical(other.postsViewed, postsViewed) || other.postsViewed == postsViewed)&&(identical(other.postsLiked, postsLiked) || other.postsLiked == postsLiked)&&(identical(other.postsCommented, postsCommented) || other.postsCommented == postsCommented)&&(identical(other.postsShared, postsShared) || other.postsShared == postsShared)&&(identical(other.timeSpentSeconds, timeSpentSeconds) || other.timeSpentSeconds == timeSpentSeconds)&&const DeepCollectionEquality().equals(other.topicEngagement, topicEngagement)&&const DeepCollectionEquality().equals(other.userEngagement, userEngagement)&&const DeepCollectionEquality().equals(other.trendingTopics, trendingTopics));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,userId,date,postsViewed,postsLiked,postsCommented,postsShared,timeSpentSeconds,const DeepCollectionEquality().hash(topicEngagement),const DeepCollectionEquality().hash(userEngagement),const DeepCollectionEquality().hash(trendingTopics));

@override
String toString() {
  return 'FeedAnalytics(userId: $userId, date: $date, postsViewed: $postsViewed, postsLiked: $postsLiked, postsCommented: $postsCommented, postsShared: $postsShared, timeSpentSeconds: $timeSpentSeconds, topicEngagement: $topicEngagement, userEngagement: $userEngagement, trendingTopics: $trendingTopics)';
}


}

/// @nodoc
abstract mixin class $FeedAnalyticsCopyWith<$Res>  {
  factory $FeedAnalyticsCopyWith(FeedAnalytics value, $Res Function(FeedAnalytics) _then) = _$FeedAnalyticsCopyWithImpl;
@useResult
$Res call({
 String userId, DateTime date, int postsViewed, int postsLiked, int postsCommented, int postsShared, int timeSpentSeconds, Map<String, int> topicEngagement, Map<String, int> userEngagement, List<String> trendingTopics
});




}
/// @nodoc
class _$FeedAnalyticsCopyWithImpl<$Res>
    implements $FeedAnalyticsCopyWith<$Res> {
  _$FeedAnalyticsCopyWithImpl(this._self, this._then);

  final FeedAnalytics _self;
  final $Res Function(FeedAnalytics) _then;

/// Create a copy of FeedAnalytics
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? userId = null,Object? date = null,Object? postsViewed = null,Object? postsLiked = null,Object? postsCommented = null,Object? postsShared = null,Object? timeSpentSeconds = null,Object? topicEngagement = null,Object? userEngagement = null,Object? trendingTopics = null,}) {
  return _then(_self.copyWith(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,date: null == date ? _self.date : date // ignore: cast_nullable_to_non_nullable
as DateTime,postsViewed: null == postsViewed ? _self.postsViewed : postsViewed // ignore: cast_nullable_to_non_nullable
as int,postsLiked: null == postsLiked ? _self.postsLiked : postsLiked // ignore: cast_nullable_to_non_nullable
as int,postsCommented: null == postsCommented ? _self.postsCommented : postsCommented // ignore: cast_nullable_to_non_nullable
as int,postsShared: null == postsShared ? _self.postsShared : postsShared // ignore: cast_nullable_to_non_nullable
as int,timeSpentSeconds: null == timeSpentSeconds ? _self.timeSpentSeconds : timeSpentSeconds // ignore: cast_nullable_to_non_nullable
as int,topicEngagement: null == topicEngagement ? _self.topicEngagement : topicEngagement // ignore: cast_nullable_to_non_nullable
as Map<String, int>,userEngagement: null == userEngagement ? _self.userEngagement : userEngagement // ignore: cast_nullable_to_non_nullable
as Map<String, int>,trendingTopics: null == trendingTopics ? _self.trendingTopics : trendingTopics // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}

}


/// Adds pattern-matching-related methods to [FeedAnalytics].
extension FeedAnalyticsPatterns on FeedAnalytics {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _FeedAnalytics value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _FeedAnalytics() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _FeedAnalytics value)  $default,){
final _that = this;
switch (_that) {
case _FeedAnalytics():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _FeedAnalytics value)?  $default,){
final _that = this;
switch (_that) {
case _FeedAnalytics() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String userId,  DateTime date,  int postsViewed,  int postsLiked,  int postsCommented,  int postsShared,  int timeSpentSeconds,  Map<String, int> topicEngagement,  Map<String, int> userEngagement,  List<String> trendingTopics)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _FeedAnalytics() when $default != null:
return $default(_that.userId,_that.date,_that.postsViewed,_that.postsLiked,_that.postsCommented,_that.postsShared,_that.timeSpentSeconds,_that.topicEngagement,_that.userEngagement,_that.trendingTopics);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String userId,  DateTime date,  int postsViewed,  int postsLiked,  int postsCommented,  int postsShared,  int timeSpentSeconds,  Map<String, int> topicEngagement,  Map<String, int> userEngagement,  List<String> trendingTopics)  $default,) {final _that = this;
switch (_that) {
case _FeedAnalytics():
return $default(_that.userId,_that.date,_that.postsViewed,_that.postsLiked,_that.postsCommented,_that.postsShared,_that.timeSpentSeconds,_that.topicEngagement,_that.userEngagement,_that.trendingTopics);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String userId,  DateTime date,  int postsViewed,  int postsLiked,  int postsCommented,  int postsShared,  int timeSpentSeconds,  Map<String, int> topicEngagement,  Map<String, int> userEngagement,  List<String> trendingTopics)?  $default,) {final _that = this;
switch (_that) {
case _FeedAnalytics() when $default != null:
return $default(_that.userId,_that.date,_that.postsViewed,_that.postsLiked,_that.postsCommented,_that.postsShared,_that.timeSpentSeconds,_that.topicEngagement,_that.userEngagement,_that.trendingTopics);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _FeedAnalytics implements FeedAnalytics {
  const _FeedAnalytics({required this.userId, required this.date, required this.postsViewed, required this.postsLiked, required this.postsCommented, required this.postsShared, required this.timeSpentSeconds, required final  Map<String, int> topicEngagement, required final  Map<String, int> userEngagement, required final  List<String> trendingTopics}): _topicEngagement = topicEngagement,_userEngagement = userEngagement,_trendingTopics = trendingTopics;
  factory _FeedAnalytics.fromJson(Map<String, dynamic> json) => _$FeedAnalyticsFromJson(json);

@override final  String userId;
@override final  DateTime date;
@override final  int postsViewed;
@override final  int postsLiked;
@override final  int postsCommented;
@override final  int postsShared;
@override final  int timeSpentSeconds;
 final  Map<String, int> _topicEngagement;
@override Map<String, int> get topicEngagement {
  if (_topicEngagement is EqualUnmodifiableMapView) return _topicEngagement;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_topicEngagement);
}

 final  Map<String, int> _userEngagement;
@override Map<String, int> get userEngagement {
  if (_userEngagement is EqualUnmodifiableMapView) return _userEngagement;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_userEngagement);
}

 final  List<String> _trendingTopics;
@override List<String> get trendingTopics {
  if (_trendingTopics is EqualUnmodifiableListView) return _trendingTopics;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_trendingTopics);
}


/// Create a copy of FeedAnalytics
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$FeedAnalyticsCopyWith<_FeedAnalytics> get copyWith => __$FeedAnalyticsCopyWithImpl<_FeedAnalytics>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$FeedAnalyticsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _FeedAnalytics&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.date, date) || other.date == date)&&(identical(other.postsViewed, postsViewed) || other.postsViewed == postsViewed)&&(identical(other.postsLiked, postsLiked) || other.postsLiked == postsLiked)&&(identical(other.postsCommented, postsCommented) || other.postsCommented == postsCommented)&&(identical(other.postsShared, postsShared) || other.postsShared == postsShared)&&(identical(other.timeSpentSeconds, timeSpentSeconds) || other.timeSpentSeconds == timeSpentSeconds)&&const DeepCollectionEquality().equals(other._topicEngagement, _topicEngagement)&&const DeepCollectionEquality().equals(other._userEngagement, _userEngagement)&&const DeepCollectionEquality().equals(other._trendingTopics, _trendingTopics));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,userId,date,postsViewed,postsLiked,postsCommented,postsShared,timeSpentSeconds,const DeepCollectionEquality().hash(_topicEngagement),const DeepCollectionEquality().hash(_userEngagement),const DeepCollectionEquality().hash(_trendingTopics));

@override
String toString() {
  return 'FeedAnalytics(userId: $userId, date: $date, postsViewed: $postsViewed, postsLiked: $postsLiked, postsCommented: $postsCommented, postsShared: $postsShared, timeSpentSeconds: $timeSpentSeconds, topicEngagement: $topicEngagement, userEngagement: $userEngagement, trendingTopics: $trendingTopics)';
}


}

/// @nodoc
abstract mixin class _$FeedAnalyticsCopyWith<$Res> implements $FeedAnalyticsCopyWith<$Res> {
  factory _$FeedAnalyticsCopyWith(_FeedAnalytics value, $Res Function(_FeedAnalytics) _then) = __$FeedAnalyticsCopyWithImpl;
@override @useResult
$Res call({
 String userId, DateTime date, int postsViewed, int postsLiked, int postsCommented, int postsShared, int timeSpentSeconds, Map<String, int> topicEngagement, Map<String, int> userEngagement, List<String> trendingTopics
});




}
/// @nodoc
class __$FeedAnalyticsCopyWithImpl<$Res>
    implements _$FeedAnalyticsCopyWith<$Res> {
  __$FeedAnalyticsCopyWithImpl(this._self, this._then);

  final _FeedAnalytics _self;
  final $Res Function(_FeedAnalytics) _then;

/// Create a copy of FeedAnalytics
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? userId = null,Object? date = null,Object? postsViewed = null,Object? postsLiked = null,Object? postsCommented = null,Object? postsShared = null,Object? timeSpentSeconds = null,Object? topicEngagement = null,Object? userEngagement = null,Object? trendingTopics = null,}) {
  return _then(_FeedAnalytics(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,date: null == date ? _self.date : date // ignore: cast_nullable_to_non_nullable
as DateTime,postsViewed: null == postsViewed ? _self.postsViewed : postsViewed // ignore: cast_nullable_to_non_nullable
as int,postsLiked: null == postsLiked ? _self.postsLiked : postsLiked // ignore: cast_nullable_to_non_nullable
as int,postsCommented: null == postsCommented ? _self.postsCommented : postsCommented // ignore: cast_nullable_to_non_nullable
as int,postsShared: null == postsShared ? _self.postsShared : postsShared // ignore: cast_nullable_to_non_nullable
as int,timeSpentSeconds: null == timeSpentSeconds ? _self.timeSpentSeconds : timeSpentSeconds // ignore: cast_nullable_to_non_nullable
as int,topicEngagement: null == topicEngagement ? _self._topicEngagement : topicEngagement // ignore: cast_nullable_to_non_nullable
as Map<String, int>,userEngagement: null == userEngagement ? _self._userEngagement : userEngagement // ignore: cast_nullable_to_non_nullable
as Map<String, int>,trendingTopics: null == trendingTopics ? _self._trendingTopics : trendingTopics // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}


}

// dart format on
