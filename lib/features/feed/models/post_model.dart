import 'package:freezed_annotation/freezed_annotation.dart';

part 'post_model.freezed.dart';
part 'post_model.g.dart';

enum MediaType { image, video, text }

enum PostVisibility { public, followers, closeFriends, private }

enum UserPostRole { owner, viewer, admin, moderator }

enum PostStatus { active, archived, deleted, flagged, review }

@freezed
abstract class Post with _$Post {
  const factory Post({
    required String id,
    required String userId,
    required String username,
    required String userAvatarUrl,
    String? userRole, // CEO, <PERSON> Surgeon, etc.
    bool? isVerified, // Billionaire badge
    required MediaType mediaType,
    required String mediaUrl,
    // Multiple media support
    List<String>? mediaUrls, // For multiple images/videos
    List<MediaType>? mediaTypes, // Corresponding media types
    required String caption,
    String? location,
    String? locationId, // For location navigation
    required int likeCount,
    required int commentCount,
    required DateTime timestamp,
    DateTime? localCreatedAt,
    @Default(false) bool isLiked,
    @Default(false) bool isBookmarked,
    @Default(false) bool isReposted,
    // Co-author system (Remax feature)
    List<String>? coAuthorIds,
    List<String>? coAuthorUsernames,
    List<String>? coAuthorAvatars,
    // Tagging system
    List<String>? mentionedUsers, // @mentions in caption
    List<String>? hashtags, // #hashtags in caption
    List<MediaTag>? mediaTags, // Tags on photos/videos
    List<String>? taggedUserIds, // Users tagged in media
    // Interaction tracking
    List<String>? likedBy,
    List<String>? bookmarkedBy,
    List<String>? repostedBy,
    // Enhanced permissions and visibility
    @Default(true) bool isPublic,
    @Default('public')
    String visibility, // 'public', 'followers', 'closeFriends', 'private'
    @Default(false) bool isArchived,
    @Default(false) bool isReported,
    @Default(false) bool isPinned, // For pinned posts on profile
    @Default(false) bool isDeleted,
    @Default(false) bool isFlagged,
    @Default('active')
    String status, // 'active', 'archived', 'deleted', 'flagged', 'review'
    // Analytics and engagement
    int? viewCount,
    int? shareCount,
    int? repostCount,
    int? saveCount,
    @Default(false) bool trending, // Trending badge
    num? trendingScore, // Trending score for ranking
    DateTime? lastEditedAt,
    String? editedBy,
    // Remix and Repost features
    @Default(false) bool isRemix,
    @Default(false) bool allowRemix,
    @Default(true) bool allowRepost,
    @Default(true) bool allowShare,
    String? originalPostId, // For remixes and reposts
    String? originalUserId,
    String? originalUsername,
    // Enhanced post features
    @Default(false) bool commentsDisabled,
    @Default(false) bool likesHidden,
    @Default(false) bool hasAIContent,
    String? musicTrack,
    String? musicUrl,
    // Admin features
    List<String>? flaggedReasons,
    DateTime? flaggedAt,
    String? flaggedBy,
    int? reportCount,
    DateTime? reviewedAt,
    String? reviewedBy,
    String? moderationAction,
    // Close friends and groups
    List<String>? closeFriendsGroupIds,
    // Content metadata
    Map<String, dynamic>? metadata,
    List<String>? contentWarnings,
    String? ageRestriction,
  }) = _Post;

  factory Post.fromJson(Map<String, dynamic> json) => _$PostFromJson(json);
}

@freezed
abstract class MediaTag with _$MediaTag {
  const factory MediaTag({
    required String userId,
    required String username,
    required double x, // X position on media (0-1)
    required double y, // Y position on media (0-1)
  }) = _MediaTag;

  factory MediaTag.fromJson(Map<String, dynamic> json) =>
      _$MediaTagFromJson(json);
}

@freezed
abstract class PostInteraction with _$PostInteraction {
  const factory PostInteraction({
    required String postId,
    required String userId,
    required String username,
    required String userAvatarUrl,
    required InteractionType type,
    required DateTime timestamp,
    String? comment, // For comment interactions
  }) = _PostInteraction;

  factory PostInteraction.fromJson(Map<String, dynamic> json) =>
      _$PostInteractionFromJson(json);
}

enum InteractionType { like, comment, bookmark, share, repost, report }

@freezed
abstract class PostPermission with _$PostPermission {
  const factory PostPermission({
    required String postId,
    required String userId,
    required bool canEdit,
    required bool canDelete,
    required bool canModerateComments,
    required bool canArchive,
    required bool canReport,
  }) = _PostPermission;

  factory PostPermission.fromJson(Map<String, dynamic> json) =>
      _$PostPermissionFromJson(json);
}
