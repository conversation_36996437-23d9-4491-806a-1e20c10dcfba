// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'comment_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Comment _$CommentFromJson(Map<String, dynamic> json) => _Comment(
  id: json['id'] as String,
  postId: json['postId'] as String,
  userId: json['userId'] as String,
  username: json['username'] as String,
  userAvatarUrl: json['userAvatarUrl'] as String,
  text: json['text'] as String,
  timestamp: const TimestampConverter().fromJson(json['timestamp']),
  parentId: json['parentId'] as String?,
  type:
      $enumDecodeNullable(_$CommentTypeEnumMap, json['type']) ??
      CommentType.text,
  voiceUrl: json['voiceUrl'] as String?,
  voiceDuration: (json['voiceDuration'] as num?)?.toInt(),
  waveformData: json['waveformData'] as String?,
  reactions: (json['reactions'] as Map<String, dynamic>?)?.map(
    (k, e) => MapEntry(k, e as String),
  ),
  reactionCount: (json['reactionCount'] as num?)?.toInt() ?? 0,
  reactionCounts: (json['reactionCounts'] as Map<String, dynamic>?)?.map(
    (k, e) => MapEntry(k, (e as num).toInt()),
  ),
  isEdited: json['isEdited'] as bool? ?? false,
  editedAt: json['editedAt'] == null
      ? null
      : DateTime.parse(json['editedAt'] as String),
);

Map<String, dynamic> _$CommentToJson(_Comment instance) => <String, dynamic>{
  'id': instance.id,
  'postId': instance.postId,
  'userId': instance.userId,
  'username': instance.username,
  'userAvatarUrl': instance.userAvatarUrl,
  'text': instance.text,
  'timestamp': const TimestampConverter().toJson(instance.timestamp),
  'parentId': instance.parentId,
  'type': _$CommentTypeEnumMap[instance.type]!,
  'voiceUrl': instance.voiceUrl,
  'voiceDuration': instance.voiceDuration,
  'waveformData': instance.waveformData,
  'reactions': instance.reactions,
  'reactionCount': instance.reactionCount,
  'reactionCounts': instance.reactionCounts,
  'isEdited': instance.isEdited,
  'editedAt': instance.editedAt?.toIso8601String(),
};

const _$CommentTypeEnumMap = {
  CommentType.text: 'text',
  CommentType.voice: 'voice',
};
