// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'reaction_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PostReaction {

 String get id; String get postId; String get userId; String get username; String get userAvatarUrl; ReactionType get type; DateTime get timestamp;
/// Create a copy of PostReaction
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PostReactionCopyWith<PostReaction> get copyWith => _$PostReactionCopyWithImpl<PostReaction>(this as PostReaction, _$identity);

  /// Serializes this PostReaction to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PostReaction&&(identical(other.id, id) || other.id == id)&&(identical(other.postId, postId) || other.postId == postId)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.username, username) || other.username == username)&&(identical(other.userAvatarUrl, userAvatarUrl) || other.userAvatarUrl == userAvatarUrl)&&(identical(other.type, type) || other.type == type)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,postId,userId,username,userAvatarUrl,type,timestamp);

@override
String toString() {
  return 'PostReaction(id: $id, postId: $postId, userId: $userId, username: $username, userAvatarUrl: $userAvatarUrl, type: $type, timestamp: $timestamp)';
}


}

/// @nodoc
abstract mixin class $PostReactionCopyWith<$Res>  {
  factory $PostReactionCopyWith(PostReaction value, $Res Function(PostReaction) _then) = _$PostReactionCopyWithImpl;
@useResult
$Res call({
 String id, String postId, String userId, String username, String userAvatarUrl, ReactionType type, DateTime timestamp
});




}
/// @nodoc
class _$PostReactionCopyWithImpl<$Res>
    implements $PostReactionCopyWith<$Res> {
  _$PostReactionCopyWithImpl(this._self, this._then);

  final PostReaction _self;
  final $Res Function(PostReaction) _then;

/// Create a copy of PostReaction
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? postId = null,Object? userId = null,Object? username = null,Object? userAvatarUrl = null,Object? type = null,Object? timestamp = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,postId: null == postId ? _self.postId : postId // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,userAvatarUrl: null == userAvatarUrl ? _self.userAvatarUrl : userAvatarUrl // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as ReactionType,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// Adds pattern-matching-related methods to [PostReaction].
extension PostReactionPatterns on PostReaction {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PostReaction value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PostReaction() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PostReaction value)  $default,){
final _that = this;
switch (_that) {
case _PostReaction():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PostReaction value)?  $default,){
final _that = this;
switch (_that) {
case _PostReaction() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String postId,  String userId,  String username,  String userAvatarUrl,  ReactionType type,  DateTime timestamp)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PostReaction() when $default != null:
return $default(_that.id,_that.postId,_that.userId,_that.username,_that.userAvatarUrl,_that.type,_that.timestamp);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String postId,  String userId,  String username,  String userAvatarUrl,  ReactionType type,  DateTime timestamp)  $default,) {final _that = this;
switch (_that) {
case _PostReaction():
return $default(_that.id,_that.postId,_that.userId,_that.username,_that.userAvatarUrl,_that.type,_that.timestamp);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String postId,  String userId,  String username,  String userAvatarUrl,  ReactionType type,  DateTime timestamp)?  $default,) {final _that = this;
switch (_that) {
case _PostReaction() when $default != null:
return $default(_that.id,_that.postId,_that.userId,_that.username,_that.userAvatarUrl,_that.type,_that.timestamp);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PostReaction implements PostReaction {
  const _PostReaction({required this.id, required this.postId, required this.userId, required this.username, required this.userAvatarUrl, required this.type, required this.timestamp});
  factory _PostReaction.fromJson(Map<String, dynamic> json) => _$PostReactionFromJson(json);

@override final  String id;
@override final  String postId;
@override final  String userId;
@override final  String username;
@override final  String userAvatarUrl;
@override final  ReactionType type;
@override final  DateTime timestamp;

/// Create a copy of PostReaction
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PostReactionCopyWith<_PostReaction> get copyWith => __$PostReactionCopyWithImpl<_PostReaction>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PostReactionToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PostReaction&&(identical(other.id, id) || other.id == id)&&(identical(other.postId, postId) || other.postId == postId)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.username, username) || other.username == username)&&(identical(other.userAvatarUrl, userAvatarUrl) || other.userAvatarUrl == userAvatarUrl)&&(identical(other.type, type) || other.type == type)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,postId,userId,username,userAvatarUrl,type,timestamp);

@override
String toString() {
  return 'PostReaction(id: $id, postId: $postId, userId: $userId, username: $username, userAvatarUrl: $userAvatarUrl, type: $type, timestamp: $timestamp)';
}


}

/// @nodoc
abstract mixin class _$PostReactionCopyWith<$Res> implements $PostReactionCopyWith<$Res> {
  factory _$PostReactionCopyWith(_PostReaction value, $Res Function(_PostReaction) _then) = __$PostReactionCopyWithImpl;
@override @useResult
$Res call({
 String id, String postId, String userId, String username, String userAvatarUrl, ReactionType type, DateTime timestamp
});




}
/// @nodoc
class __$PostReactionCopyWithImpl<$Res>
    implements _$PostReactionCopyWith<$Res> {
  __$PostReactionCopyWithImpl(this._self, this._then);

  final _PostReaction _self;
  final $Res Function(_PostReaction) _then;

/// Create a copy of PostReaction
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? postId = null,Object? userId = null,Object? username = null,Object? userAvatarUrl = null,Object? type = null,Object? timestamp = null,}) {
  return _then(_PostReaction(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,postId: null == postId ? _self.postId : postId // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,userAvatarUrl: null == userAvatarUrl ? _self.userAvatarUrl : userAvatarUrl // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as ReactionType,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}


/// @nodoc
mixin _$ReactionCount {

 ReactionType get type; int get count; bool get isUserReaction;
/// Create a copy of ReactionCount
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ReactionCountCopyWith<ReactionCount> get copyWith => _$ReactionCountCopyWithImpl<ReactionCount>(this as ReactionCount, _$identity);

  /// Serializes this ReactionCount to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ReactionCount&&(identical(other.type, type) || other.type == type)&&(identical(other.count, count) || other.count == count)&&(identical(other.isUserReaction, isUserReaction) || other.isUserReaction == isUserReaction));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,type,count,isUserReaction);

@override
String toString() {
  return 'ReactionCount(type: $type, count: $count, isUserReaction: $isUserReaction)';
}


}

/// @nodoc
abstract mixin class $ReactionCountCopyWith<$Res>  {
  factory $ReactionCountCopyWith(ReactionCount value, $Res Function(ReactionCount) _then) = _$ReactionCountCopyWithImpl;
@useResult
$Res call({
 ReactionType type, int count, bool isUserReaction
});




}
/// @nodoc
class _$ReactionCountCopyWithImpl<$Res>
    implements $ReactionCountCopyWith<$Res> {
  _$ReactionCountCopyWithImpl(this._self, this._then);

  final ReactionCount _self;
  final $Res Function(ReactionCount) _then;

/// Create a copy of ReactionCount
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? type = null,Object? count = null,Object? isUserReaction = null,}) {
  return _then(_self.copyWith(
type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as ReactionType,count: null == count ? _self.count : count // ignore: cast_nullable_to_non_nullable
as int,isUserReaction: null == isUserReaction ? _self.isUserReaction : isUserReaction // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [ReactionCount].
extension ReactionCountPatterns on ReactionCount {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ReactionCount value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ReactionCount() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ReactionCount value)  $default,){
final _that = this;
switch (_that) {
case _ReactionCount():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ReactionCount value)?  $default,){
final _that = this;
switch (_that) {
case _ReactionCount() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( ReactionType type,  int count,  bool isUserReaction)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ReactionCount() when $default != null:
return $default(_that.type,_that.count,_that.isUserReaction);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( ReactionType type,  int count,  bool isUserReaction)  $default,) {final _that = this;
switch (_that) {
case _ReactionCount():
return $default(_that.type,_that.count,_that.isUserReaction);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( ReactionType type,  int count,  bool isUserReaction)?  $default,) {final _that = this;
switch (_that) {
case _ReactionCount() when $default != null:
return $default(_that.type,_that.count,_that.isUserReaction);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ReactionCount implements ReactionCount {
  const _ReactionCount({required this.type, required this.count, this.isUserReaction = false});
  factory _ReactionCount.fromJson(Map<String, dynamic> json) => _$ReactionCountFromJson(json);

@override final  ReactionType type;
@override final  int count;
@override@JsonKey() final  bool isUserReaction;

/// Create a copy of ReactionCount
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ReactionCountCopyWith<_ReactionCount> get copyWith => __$ReactionCountCopyWithImpl<_ReactionCount>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ReactionCountToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ReactionCount&&(identical(other.type, type) || other.type == type)&&(identical(other.count, count) || other.count == count)&&(identical(other.isUserReaction, isUserReaction) || other.isUserReaction == isUserReaction));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,type,count,isUserReaction);

@override
String toString() {
  return 'ReactionCount(type: $type, count: $count, isUserReaction: $isUserReaction)';
}


}

/// @nodoc
abstract mixin class _$ReactionCountCopyWith<$Res> implements $ReactionCountCopyWith<$Res> {
  factory _$ReactionCountCopyWith(_ReactionCount value, $Res Function(_ReactionCount) _then) = __$ReactionCountCopyWithImpl;
@override @useResult
$Res call({
 ReactionType type, int count, bool isUserReaction
});




}
/// @nodoc
class __$ReactionCountCopyWithImpl<$Res>
    implements _$ReactionCountCopyWith<$Res> {
  __$ReactionCountCopyWithImpl(this._self, this._then);

  final _ReactionCount _self;
  final $Res Function(_ReactionCount) _then;

/// Create a copy of ReactionCount
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? type = null,Object? count = null,Object? isUserReaction = null,}) {
  return _then(_ReactionCount(
type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as ReactionType,count: null == count ? _self.count : count // ignore: cast_nullable_to_non_nullable
as int,isUserReaction: null == isUserReaction ? _self.isUserReaction : isUserReaction // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$ReactionSummary {

 String get postId; int get totalReactions; List<ReactionCount> get reactionCounts; ReactionType? get userReaction; DateTime get lastUpdated;
/// Create a copy of ReactionSummary
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ReactionSummaryCopyWith<ReactionSummary> get copyWith => _$ReactionSummaryCopyWithImpl<ReactionSummary>(this as ReactionSummary, _$identity);

  /// Serializes this ReactionSummary to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ReactionSummary&&(identical(other.postId, postId) || other.postId == postId)&&(identical(other.totalReactions, totalReactions) || other.totalReactions == totalReactions)&&const DeepCollectionEquality().equals(other.reactionCounts, reactionCounts)&&(identical(other.userReaction, userReaction) || other.userReaction == userReaction)&&(identical(other.lastUpdated, lastUpdated) || other.lastUpdated == lastUpdated));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,postId,totalReactions,const DeepCollectionEquality().hash(reactionCounts),userReaction,lastUpdated);

@override
String toString() {
  return 'ReactionSummary(postId: $postId, totalReactions: $totalReactions, reactionCounts: $reactionCounts, userReaction: $userReaction, lastUpdated: $lastUpdated)';
}


}

/// @nodoc
abstract mixin class $ReactionSummaryCopyWith<$Res>  {
  factory $ReactionSummaryCopyWith(ReactionSummary value, $Res Function(ReactionSummary) _then) = _$ReactionSummaryCopyWithImpl;
@useResult
$Res call({
 String postId, int totalReactions, List<ReactionCount> reactionCounts, ReactionType? userReaction, DateTime lastUpdated
});




}
/// @nodoc
class _$ReactionSummaryCopyWithImpl<$Res>
    implements $ReactionSummaryCopyWith<$Res> {
  _$ReactionSummaryCopyWithImpl(this._self, this._then);

  final ReactionSummary _self;
  final $Res Function(ReactionSummary) _then;

/// Create a copy of ReactionSummary
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? postId = null,Object? totalReactions = null,Object? reactionCounts = null,Object? userReaction = freezed,Object? lastUpdated = null,}) {
  return _then(_self.copyWith(
postId: null == postId ? _self.postId : postId // ignore: cast_nullable_to_non_nullable
as String,totalReactions: null == totalReactions ? _self.totalReactions : totalReactions // ignore: cast_nullable_to_non_nullable
as int,reactionCounts: null == reactionCounts ? _self.reactionCounts : reactionCounts // ignore: cast_nullable_to_non_nullable
as List<ReactionCount>,userReaction: freezed == userReaction ? _self.userReaction : userReaction // ignore: cast_nullable_to_non_nullable
as ReactionType?,lastUpdated: null == lastUpdated ? _self.lastUpdated : lastUpdated // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// Adds pattern-matching-related methods to [ReactionSummary].
extension ReactionSummaryPatterns on ReactionSummary {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ReactionSummary value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ReactionSummary() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ReactionSummary value)  $default,){
final _that = this;
switch (_that) {
case _ReactionSummary():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ReactionSummary value)?  $default,){
final _that = this;
switch (_that) {
case _ReactionSummary() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String postId,  int totalReactions,  List<ReactionCount> reactionCounts,  ReactionType? userReaction,  DateTime lastUpdated)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ReactionSummary() when $default != null:
return $default(_that.postId,_that.totalReactions,_that.reactionCounts,_that.userReaction,_that.lastUpdated);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String postId,  int totalReactions,  List<ReactionCount> reactionCounts,  ReactionType? userReaction,  DateTime lastUpdated)  $default,) {final _that = this;
switch (_that) {
case _ReactionSummary():
return $default(_that.postId,_that.totalReactions,_that.reactionCounts,_that.userReaction,_that.lastUpdated);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String postId,  int totalReactions,  List<ReactionCount> reactionCounts,  ReactionType? userReaction,  DateTime lastUpdated)?  $default,) {final _that = this;
switch (_that) {
case _ReactionSummary() when $default != null:
return $default(_that.postId,_that.totalReactions,_that.reactionCounts,_that.userReaction,_that.lastUpdated);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ReactionSummary implements ReactionSummary {
  const _ReactionSummary({required this.postId, required this.totalReactions, required final  List<ReactionCount> reactionCounts, this.userReaction, required this.lastUpdated}): _reactionCounts = reactionCounts;
  factory _ReactionSummary.fromJson(Map<String, dynamic> json) => _$ReactionSummaryFromJson(json);

@override final  String postId;
@override final  int totalReactions;
 final  List<ReactionCount> _reactionCounts;
@override List<ReactionCount> get reactionCounts {
  if (_reactionCounts is EqualUnmodifiableListView) return _reactionCounts;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_reactionCounts);
}

@override final  ReactionType? userReaction;
@override final  DateTime lastUpdated;

/// Create a copy of ReactionSummary
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ReactionSummaryCopyWith<_ReactionSummary> get copyWith => __$ReactionSummaryCopyWithImpl<_ReactionSummary>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ReactionSummaryToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ReactionSummary&&(identical(other.postId, postId) || other.postId == postId)&&(identical(other.totalReactions, totalReactions) || other.totalReactions == totalReactions)&&const DeepCollectionEquality().equals(other._reactionCounts, _reactionCounts)&&(identical(other.userReaction, userReaction) || other.userReaction == userReaction)&&(identical(other.lastUpdated, lastUpdated) || other.lastUpdated == lastUpdated));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,postId,totalReactions,const DeepCollectionEquality().hash(_reactionCounts),userReaction,lastUpdated);

@override
String toString() {
  return 'ReactionSummary(postId: $postId, totalReactions: $totalReactions, reactionCounts: $reactionCounts, userReaction: $userReaction, lastUpdated: $lastUpdated)';
}


}

/// @nodoc
abstract mixin class _$ReactionSummaryCopyWith<$Res> implements $ReactionSummaryCopyWith<$Res> {
  factory _$ReactionSummaryCopyWith(_ReactionSummary value, $Res Function(_ReactionSummary) _then) = __$ReactionSummaryCopyWithImpl;
@override @useResult
$Res call({
 String postId, int totalReactions, List<ReactionCount> reactionCounts, ReactionType? userReaction, DateTime lastUpdated
});




}
/// @nodoc
class __$ReactionSummaryCopyWithImpl<$Res>
    implements _$ReactionSummaryCopyWith<$Res> {
  __$ReactionSummaryCopyWithImpl(this._self, this._then);

  final _ReactionSummary _self;
  final $Res Function(_ReactionSummary) _then;

/// Create a copy of ReactionSummary
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? postId = null,Object? totalReactions = null,Object? reactionCounts = null,Object? userReaction = freezed,Object? lastUpdated = null,}) {
  return _then(_ReactionSummary(
postId: null == postId ? _self.postId : postId // ignore: cast_nullable_to_non_nullable
as String,totalReactions: null == totalReactions ? _self.totalReactions : totalReactions // ignore: cast_nullable_to_non_nullable
as int,reactionCounts: null == reactionCounts ? _self._reactionCounts : reactionCounts // ignore: cast_nullable_to_non_nullable
as List<ReactionCount>,userReaction: freezed == userReaction ? _self.userReaction : userReaction // ignore: cast_nullable_to_non_nullable
as ReactionType?,lastUpdated: null == lastUpdated ? _self.lastUpdated : lastUpdated // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}

// dart format on
