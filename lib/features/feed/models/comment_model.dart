import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

part 'comment_model.freezed.dart';
part 'comment_model.g.dart';

enum CommentType { text, voice }

class TimestampConverter implements JsonConverter<DateTime, dynamic> {
  const TimestampConverter();

  @override
  DateTime fromJson(dynamic json) {
    if (json is Timestamp) {
      return json.toDate();
    } else if (json is String) {
      return DateTime.parse(json);
    } else if (json is int) {
      // Unix timestamp in seconds
      return DateTime.fromMillisecondsSinceEpoch(json * 1000);
    }
    throw Exception('Invalid timestamp: $json');
  }

  @override
  dynamic toJson(DateTime date) => date.toIso8601String();
}

@freezed
abstract class Comment with _$Comment {
  const factory Comment({
    required String id,
    required String postId,
    required String userId,
    required String username,
    required String userAvatarUrl,
    required String text,
    @TimestampConverter() required DateTime timestamp,
    String? parentId,
    @Default(CommentType.text) CommentType type,
    String? voiceUrl,
    int? voiceDuration, // Duration in seconds
    String? waveformData, // JSON string of waveform data for visualization
    Map<String, String>? reactions, // userId -> emoji reactions
    @Default(0) int reactionCount,
    Map<String, int>? reactionCounts, // emoji -> count mapping
    @Default(false) bool isEdited,
    DateTime? editedAt,
  }) = _Comment;

  factory Comment.fromJson(Map<String, dynamic> json) =>
      _$CommentFromJson(json);
}
