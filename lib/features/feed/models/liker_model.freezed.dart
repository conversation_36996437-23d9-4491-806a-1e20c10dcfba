// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'liker_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LikerModel {

 String get id; String get username; String get name; String get profilePictureUrl; bool get isVerified; DateTime get likedAt;
/// Create a copy of LikerModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LikerModelCopyWith<LikerModel> get copyWith => _$LikerModelCopyWithImpl<LikerModel>(this as LikerModel, _$identity);

  /// Serializes this LikerModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LikerModel&&(identical(other.id, id) || other.id == id)&&(identical(other.username, username) || other.username == username)&&(identical(other.name, name) || other.name == name)&&(identical(other.profilePictureUrl, profilePictureUrl) || other.profilePictureUrl == profilePictureUrl)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.likedAt, likedAt) || other.likedAt == likedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,username,name,profilePictureUrl,isVerified,likedAt);

@override
String toString() {
  return 'LikerModel(id: $id, username: $username, name: $name, profilePictureUrl: $profilePictureUrl, isVerified: $isVerified, likedAt: $likedAt)';
}


}

/// @nodoc
abstract mixin class $LikerModelCopyWith<$Res>  {
  factory $LikerModelCopyWith(LikerModel value, $Res Function(LikerModel) _then) = _$LikerModelCopyWithImpl;
@useResult
$Res call({
 String id, String username, String name, String profilePictureUrl, bool isVerified, DateTime likedAt
});




}
/// @nodoc
class _$LikerModelCopyWithImpl<$Res>
    implements $LikerModelCopyWith<$Res> {
  _$LikerModelCopyWithImpl(this._self, this._then);

  final LikerModel _self;
  final $Res Function(LikerModel) _then;

/// Create a copy of LikerModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? username = null,Object? name = null,Object? profilePictureUrl = null,Object? isVerified = null,Object? likedAt = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,profilePictureUrl: null == profilePictureUrl ? _self.profilePictureUrl : profilePictureUrl // ignore: cast_nullable_to_non_nullable
as String,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,likedAt: null == likedAt ? _self.likedAt : likedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// Adds pattern-matching-related methods to [LikerModel].
extension LikerModelPatterns on LikerModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _LikerModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _LikerModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _LikerModel value)  $default,){
final _that = this;
switch (_that) {
case _LikerModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _LikerModel value)?  $default,){
final _that = this;
switch (_that) {
case _LikerModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String username,  String name,  String profilePictureUrl,  bool isVerified,  DateTime likedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _LikerModel() when $default != null:
return $default(_that.id,_that.username,_that.name,_that.profilePictureUrl,_that.isVerified,_that.likedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String username,  String name,  String profilePictureUrl,  bool isVerified,  DateTime likedAt)  $default,) {final _that = this;
switch (_that) {
case _LikerModel():
return $default(_that.id,_that.username,_that.name,_that.profilePictureUrl,_that.isVerified,_that.likedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String username,  String name,  String profilePictureUrl,  bool isVerified,  DateTime likedAt)?  $default,) {final _that = this;
switch (_that) {
case _LikerModel() when $default != null:
return $default(_that.id,_that.username,_that.name,_that.profilePictureUrl,_that.isVerified,_that.likedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _LikerModel implements LikerModel {
  const _LikerModel({required this.id, required this.username, required this.name, required this.profilePictureUrl, required this.isVerified, required this.likedAt});
  factory _LikerModel.fromJson(Map<String, dynamic> json) => _$LikerModelFromJson(json);

@override final  String id;
@override final  String username;
@override final  String name;
@override final  String profilePictureUrl;
@override final  bool isVerified;
@override final  DateTime likedAt;

/// Create a copy of LikerModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LikerModelCopyWith<_LikerModel> get copyWith => __$LikerModelCopyWithImpl<_LikerModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$LikerModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LikerModel&&(identical(other.id, id) || other.id == id)&&(identical(other.username, username) || other.username == username)&&(identical(other.name, name) || other.name == name)&&(identical(other.profilePictureUrl, profilePictureUrl) || other.profilePictureUrl == profilePictureUrl)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.likedAt, likedAt) || other.likedAt == likedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,username,name,profilePictureUrl,isVerified,likedAt);

@override
String toString() {
  return 'LikerModel(id: $id, username: $username, name: $name, profilePictureUrl: $profilePictureUrl, isVerified: $isVerified, likedAt: $likedAt)';
}


}

/// @nodoc
abstract mixin class _$LikerModelCopyWith<$Res> implements $LikerModelCopyWith<$Res> {
  factory _$LikerModelCopyWith(_LikerModel value, $Res Function(_LikerModel) _then) = __$LikerModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String username, String name, String profilePictureUrl, bool isVerified, DateTime likedAt
});




}
/// @nodoc
class __$LikerModelCopyWithImpl<$Res>
    implements _$LikerModelCopyWith<$Res> {
  __$LikerModelCopyWithImpl(this._self, this._then);

  final _LikerModel _self;
  final $Res Function(_LikerModel) _then;

/// Create a copy of LikerModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? username = null,Object? name = null,Object? profilePictureUrl = null,Object? isVerified = null,Object? likedAt = null,}) {
  return _then(_LikerModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,profilePictureUrl: null == profilePictureUrl ? _self.profilePictureUrl : profilePictureUrl // ignore: cast_nullable_to_non_nullable
as String,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,likedAt: null == likedAt ? _self.likedAt : likedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}

// dart format on
