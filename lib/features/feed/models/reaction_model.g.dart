// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'reaction_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_PostReaction _$PostReactionFromJson(Map<String, dynamic> json) =>
    _PostReaction(
      id: json['id'] as String,
      postId: json['postId'] as String,
      userId: json['userId'] as String,
      username: json['username'] as String,
      userAvatarUrl: json['userAvatarUrl'] as String,
      type: $enumDecode(_$ReactionTypeEnumMap, json['type']),
      timestamp: DateTime.parse(json['timestamp'] as String),
    );

Map<String, dynamic> _$PostReactionToJson(_PostReaction instance) =>
    <String, dynamic>{
      'id': instance.id,
      'postId': instance.postId,
      'userId': instance.userId,
      'username': instance.username,
      'userAvatarUrl': instance.userAvatarUrl,
      'type': _$ReactionTypeEnumMap[instance.type]!,
      'timestamp': instance.timestamp.toIso8601String(),
    };

const _$ReactionTypeEnumMap = {
  ReactionType.like: 'like',
  ReactionType.love: 'love',
  ReactionType.haha: 'haha',
  ReactionType.wow: 'wow',
  ReactionType.sad: 'sad',
  ReactionType.angry: 'angry',
};

_ReactionCount _$ReactionCountFromJson(Map<String, dynamic> json) =>
    _ReactionCount(
      type: $enumDecode(_$ReactionTypeEnumMap, json['type']),
      count: (json['count'] as num).toInt(),
      isUserReaction: json['isUserReaction'] as bool? ?? false,
    );

Map<String, dynamic> _$ReactionCountToJson(_ReactionCount instance) =>
    <String, dynamic>{
      'type': _$ReactionTypeEnumMap[instance.type]!,
      'count': instance.count,
      'isUserReaction': instance.isUserReaction,
    };

_ReactionSummary _$ReactionSummaryFromJson(Map<String, dynamic> json) =>
    _ReactionSummary(
      postId: json['postId'] as String,
      totalReactions: (json['totalReactions'] as num).toInt(),
      reactionCounts: (json['reactionCounts'] as List<dynamic>)
          .map((e) => ReactionCount.fromJson(e as Map<String, dynamic>))
          .toList(),
      userReaction: $enumDecodeNullable(
        _$ReactionTypeEnumMap,
        json['userReaction'],
      ),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$ReactionSummaryToJson(_ReactionSummary instance) =>
    <String, dynamic>{
      'postId': instance.postId,
      'totalReactions': instance.totalReactions,
      'reactionCounts': instance.reactionCounts,
      'userReaction': _$ReactionTypeEnumMap[instance.userReaction],
      'lastUpdated': instance.lastUpdated.toIso8601String(),
    };
