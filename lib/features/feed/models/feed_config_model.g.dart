// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'feed_config_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_FeedConfig _$FeedConfigFromJson(Map<String, dynamic> json) => _FeedConfig(
  feedType:
      $enumDecodeNullable(_$FeedTypeEnumMap, json['feedType']) ??
      FeedType.hybrid,
  mediaFilter:
      $enumDecodeNullable(_$MediaFilterEnumMap, json['mediaFilter']) ??
      MediaFilter.all,
  sortOrder:
      $enumDecodeNullable(_$SortOrderEnumMap, json['sortOrder']) ??
      SortOrder.latest,
  postsPerPage: (json['postsPerPage'] as num?)?.toInt() ?? 20,
  showBusinessPosts: json['showBusinessPosts'] as bool? ?? true,
  showVerifiedPosts: json['showVerifiedPosts'] as bool? ?? true,
  showTrendingPosts: json['showTrendingPosts'] as bool? ?? true,
  excludedUserIds:
      (json['excludedUserIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  excludedHashtags:
      (json['excludedHashtags'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  preferredTopics:
      (json['preferredTopics'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  autoRefresh: json['autoRefresh'] as bool? ?? false,
  autoRefreshIntervalSeconds:
      (json['autoRefreshIntervalSeconds'] as num?)?.toInt() ?? 300,
);

Map<String, dynamic> _$FeedConfigToJson(_FeedConfig instance) =>
    <String, dynamic>{
      'feedType': _$FeedTypeEnumMap[instance.feedType]!,
      'mediaFilter': _$MediaFilterEnumMap[instance.mediaFilter]!,
      'sortOrder': _$SortOrderEnumMap[instance.sortOrder]!,
      'postsPerPage': instance.postsPerPage,
      'showBusinessPosts': instance.showBusinessPosts,
      'showVerifiedPosts': instance.showVerifiedPosts,
      'showTrendingPosts': instance.showTrendingPosts,
      'excludedUserIds': instance.excludedUserIds,
      'excludedHashtags': instance.excludedHashtags,
      'preferredTopics': instance.preferredTopics,
      'autoRefresh': instance.autoRefresh,
      'autoRefreshIntervalSeconds': instance.autoRefreshIntervalSeconds,
    };

const _$FeedTypeEnumMap = {
  FeedType.following: 'following',
  FeedType.suggested: 'suggested',
  FeedType.hybrid: 'hybrid',
  FeedType.trending: 'trending',
  FeedType.business: 'business',
  FeedType.verified: 'verified',
  FeedType.all: 'all',
};

const _$MediaFilterEnumMap = {
  MediaFilter.all: 'all',
  MediaFilter.photos: 'photos',
  MediaFilter.videos: 'videos',
  MediaFilter.text: 'text',
};

const _$SortOrderEnumMap = {
  SortOrder.latest: 'latest',
  SortOrder.mostPopular: 'mostPopular',
  SortOrder.trending: 'trending',
  SortOrder.mostEngaged: 'mostEngaged',
};

_FeedPreferences _$FeedPreferencesFromJson(Map<String, dynamic> json) =>
    _FeedPreferences(
      userId: json['userId'] as String,
      config: FeedConfig.fromJson(json['config'] as Map<String, dynamic>),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      userInterests: json['userInterests'] as Map<String, dynamic>? ?? const {},
      followedTopics:
          (json['followedTopics'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      mutedUsers:
          (json['mutedUsers'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      mutedHashtags:
          (json['mutedHashtags'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      showSensitiveContent: json['showSensitiveContent'] as bool? ?? true,
      showPoliticalContent: json['showPoliticalContent'] as bool? ?? true,
      showControversialTopics: json['showControversialTopics'] as bool? ?? true,
    );

Map<String, dynamic> _$FeedPreferencesToJson(_FeedPreferences instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'config': instance.config,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
      'userInterests': instance.userInterests,
      'followedTopics': instance.followedTopics,
      'mutedUsers': instance.mutedUsers,
      'mutedHashtags': instance.mutedHashtags,
      'showSensitiveContent': instance.showSensitiveContent,
      'showPoliticalContent': instance.showPoliticalContent,
      'showControversialTopics': instance.showControversialTopics,
    };

_FeedAnalytics _$FeedAnalyticsFromJson(Map<String, dynamic> json) =>
    _FeedAnalytics(
      userId: json['userId'] as String,
      date: DateTime.parse(json['date'] as String),
      postsViewed: (json['postsViewed'] as num).toInt(),
      postsLiked: (json['postsLiked'] as num).toInt(),
      postsCommented: (json['postsCommented'] as num).toInt(),
      postsShared: (json['postsShared'] as num).toInt(),
      timeSpentSeconds: (json['timeSpentSeconds'] as num).toInt(),
      topicEngagement: Map<String, int>.from(json['topicEngagement'] as Map),
      userEngagement: Map<String, int>.from(json['userEngagement'] as Map),
      trendingTopics: (json['trendingTopics'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$FeedAnalyticsToJson(_FeedAnalytics instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'date': instance.date.toIso8601String(),
      'postsViewed': instance.postsViewed,
      'postsLiked': instance.postsLiked,
      'postsCommented': instance.postsCommented,
      'postsShared': instance.postsShared,
      'timeSpentSeconds': instance.timeSpentSeconds,
      'topicEngagement': instance.topicEngagement,
      'userEngagement': instance.userEngagement,
      'trendingTopics': instance.trendingTopics,
    };
