// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'post_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Post {

 String get id; String get userId; String get username; String get userAvatarUrl; String? get userRole;// CEO, Plastic Surgeon, etc.
 bool? get isVerified;// Billionaire badge
 MediaType get mediaType; String get mediaUrl;// Multiple media support
 List<String>? get mediaUrls;// For multiple images/videos
 List<MediaType>? get mediaTypes;// Corresponding media types
 String get caption; String? get location; String? get locationId;// For location navigation
 int get likeCount; int get commentCount; DateTime get timestamp; DateTime? get localCreatedAt; bool get isLiked; bool get isBookmarked; bool get isReposted;// Co-author system (Remax feature)
 List<String>? get coAuthorIds; List<String>? get coAuthorUsernames; List<String>? get coAuthorAvatars;// Tagging system
 List<String>? get mentionedUsers;// @mentions in caption
 List<String>? get hashtags;// #hashtags in caption
 List<MediaTag>? get mediaTags;// Tags on photos/videos
 List<String>? get taggedUserIds;// Users tagged in media
// Interaction tracking
 List<String>? get likedBy; List<String>? get bookmarkedBy; List<String>? get repostedBy;// Enhanced permissions and visibility
 bool get isPublic; String get visibility;// 'public', 'followers', 'closeFriends', 'private'
 bool get isArchived; bool get isReported; bool get isPinned;// For pinned posts on profile
 bool get isDeleted; bool get isFlagged; String get status;// 'active', 'archived', 'deleted', 'flagged', 'review'
// Analytics and engagement
 int? get viewCount; int? get shareCount; int? get repostCount; int? get saveCount; bool get trending;// Trending badge
 num? get trendingScore;// Trending score for ranking
 DateTime? get lastEditedAt; String? get editedBy;// Remix and Repost features
 bool get isRemix; bool get allowRemix; bool get allowRepost; bool get allowShare; String? get originalPostId;// For remixes and reposts
 String? get originalUserId; String? get originalUsername;// Enhanced post features
 bool get commentsDisabled; bool get likesHidden; bool get hasAIContent; String? get musicTrack; String? get musicUrl;// Admin features
 List<String>? get flaggedReasons; DateTime? get flaggedAt; String? get flaggedBy; int? get reportCount; DateTime? get reviewedAt; String? get reviewedBy; String? get moderationAction;// Close friends and groups
 List<String>? get closeFriendsGroupIds;// Content metadata
 Map<String, dynamic>? get metadata; List<String>? get contentWarnings; String? get ageRestriction;
/// Create a copy of Post
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PostCopyWith<Post> get copyWith => _$PostCopyWithImpl<Post>(this as Post, _$identity);

  /// Serializes this Post to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Post&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.username, username) || other.username == username)&&(identical(other.userAvatarUrl, userAvatarUrl) || other.userAvatarUrl == userAvatarUrl)&&(identical(other.userRole, userRole) || other.userRole == userRole)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.mediaType, mediaType) || other.mediaType == mediaType)&&(identical(other.mediaUrl, mediaUrl) || other.mediaUrl == mediaUrl)&&const DeepCollectionEquality().equals(other.mediaUrls, mediaUrls)&&const DeepCollectionEquality().equals(other.mediaTypes, mediaTypes)&&(identical(other.caption, caption) || other.caption == caption)&&(identical(other.location, location) || other.location == location)&&(identical(other.locationId, locationId) || other.locationId == locationId)&&(identical(other.likeCount, likeCount) || other.likeCount == likeCount)&&(identical(other.commentCount, commentCount) || other.commentCount == commentCount)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.localCreatedAt, localCreatedAt) || other.localCreatedAt == localCreatedAt)&&(identical(other.isLiked, isLiked) || other.isLiked == isLiked)&&(identical(other.isBookmarked, isBookmarked) || other.isBookmarked == isBookmarked)&&(identical(other.isReposted, isReposted) || other.isReposted == isReposted)&&const DeepCollectionEquality().equals(other.coAuthorIds, coAuthorIds)&&const DeepCollectionEquality().equals(other.coAuthorUsernames, coAuthorUsernames)&&const DeepCollectionEquality().equals(other.coAuthorAvatars, coAuthorAvatars)&&const DeepCollectionEquality().equals(other.mentionedUsers, mentionedUsers)&&const DeepCollectionEquality().equals(other.hashtags, hashtags)&&const DeepCollectionEquality().equals(other.mediaTags, mediaTags)&&const DeepCollectionEquality().equals(other.taggedUserIds, taggedUserIds)&&const DeepCollectionEquality().equals(other.likedBy, likedBy)&&const DeepCollectionEquality().equals(other.bookmarkedBy, bookmarkedBy)&&const DeepCollectionEquality().equals(other.repostedBy, repostedBy)&&(identical(other.isPublic, isPublic) || other.isPublic == isPublic)&&(identical(other.visibility, visibility) || other.visibility == visibility)&&(identical(other.isArchived, isArchived) || other.isArchived == isArchived)&&(identical(other.isReported, isReported) || other.isReported == isReported)&&(identical(other.isPinned, isPinned) || other.isPinned == isPinned)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.isFlagged, isFlagged) || other.isFlagged == isFlagged)&&(identical(other.status, status) || other.status == status)&&(identical(other.viewCount, viewCount) || other.viewCount == viewCount)&&(identical(other.shareCount, shareCount) || other.shareCount == shareCount)&&(identical(other.repostCount, repostCount) || other.repostCount == repostCount)&&(identical(other.saveCount, saveCount) || other.saveCount == saveCount)&&(identical(other.trending, trending) || other.trending == trending)&&(identical(other.trendingScore, trendingScore) || other.trendingScore == trendingScore)&&(identical(other.lastEditedAt, lastEditedAt) || other.lastEditedAt == lastEditedAt)&&(identical(other.editedBy, editedBy) || other.editedBy == editedBy)&&(identical(other.isRemix, isRemix) || other.isRemix == isRemix)&&(identical(other.allowRemix, allowRemix) || other.allowRemix == allowRemix)&&(identical(other.allowRepost, allowRepost) || other.allowRepost == allowRepost)&&(identical(other.allowShare, allowShare) || other.allowShare == allowShare)&&(identical(other.originalPostId, originalPostId) || other.originalPostId == originalPostId)&&(identical(other.originalUserId, originalUserId) || other.originalUserId == originalUserId)&&(identical(other.originalUsername, originalUsername) || other.originalUsername == originalUsername)&&(identical(other.commentsDisabled, commentsDisabled) || other.commentsDisabled == commentsDisabled)&&(identical(other.likesHidden, likesHidden) || other.likesHidden == likesHidden)&&(identical(other.hasAIContent, hasAIContent) || other.hasAIContent == hasAIContent)&&(identical(other.musicTrack, musicTrack) || other.musicTrack == musicTrack)&&(identical(other.musicUrl, musicUrl) || other.musicUrl == musicUrl)&&const DeepCollectionEquality().equals(other.flaggedReasons, flaggedReasons)&&(identical(other.flaggedAt, flaggedAt) || other.flaggedAt == flaggedAt)&&(identical(other.flaggedBy, flaggedBy) || other.flaggedBy == flaggedBy)&&(identical(other.reportCount, reportCount) || other.reportCount == reportCount)&&(identical(other.reviewedAt, reviewedAt) || other.reviewedAt == reviewedAt)&&(identical(other.reviewedBy, reviewedBy) || other.reviewedBy == reviewedBy)&&(identical(other.moderationAction, moderationAction) || other.moderationAction == moderationAction)&&const DeepCollectionEquality().equals(other.closeFriendsGroupIds, closeFriendsGroupIds)&&const DeepCollectionEquality().equals(other.metadata, metadata)&&const DeepCollectionEquality().equals(other.contentWarnings, contentWarnings)&&(identical(other.ageRestriction, ageRestriction) || other.ageRestriction == ageRestriction));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,userId,username,userAvatarUrl,userRole,isVerified,mediaType,mediaUrl,const DeepCollectionEquality().hash(mediaUrls),const DeepCollectionEquality().hash(mediaTypes),caption,location,locationId,likeCount,commentCount,timestamp,localCreatedAt,isLiked,isBookmarked,isReposted,const DeepCollectionEquality().hash(coAuthorIds),const DeepCollectionEquality().hash(coAuthorUsernames),const DeepCollectionEquality().hash(coAuthorAvatars),const DeepCollectionEquality().hash(mentionedUsers),const DeepCollectionEquality().hash(hashtags),const DeepCollectionEquality().hash(mediaTags),const DeepCollectionEquality().hash(taggedUserIds),const DeepCollectionEquality().hash(likedBy),const DeepCollectionEquality().hash(bookmarkedBy),const DeepCollectionEquality().hash(repostedBy),isPublic,visibility,isArchived,isReported,isPinned,isDeleted,isFlagged,status,viewCount,shareCount,repostCount,saveCount,trending,trendingScore,lastEditedAt,editedBy,isRemix,allowRemix,allowRepost,allowShare,originalPostId,originalUserId,originalUsername,commentsDisabled,likesHidden,hasAIContent,musicTrack,musicUrl,const DeepCollectionEquality().hash(flaggedReasons),flaggedAt,flaggedBy,reportCount,reviewedAt,reviewedBy,moderationAction,const DeepCollectionEquality().hash(closeFriendsGroupIds),const DeepCollectionEquality().hash(metadata),const DeepCollectionEquality().hash(contentWarnings),ageRestriction]);

@override
String toString() {
  return 'Post(id: $id, userId: $userId, username: $username, userAvatarUrl: $userAvatarUrl, userRole: $userRole, isVerified: $isVerified, mediaType: $mediaType, mediaUrl: $mediaUrl, mediaUrls: $mediaUrls, mediaTypes: $mediaTypes, caption: $caption, location: $location, locationId: $locationId, likeCount: $likeCount, commentCount: $commentCount, timestamp: $timestamp, localCreatedAt: $localCreatedAt, isLiked: $isLiked, isBookmarked: $isBookmarked, isReposted: $isReposted, coAuthorIds: $coAuthorIds, coAuthorUsernames: $coAuthorUsernames, coAuthorAvatars: $coAuthorAvatars, mentionedUsers: $mentionedUsers, hashtags: $hashtags, mediaTags: $mediaTags, taggedUserIds: $taggedUserIds, likedBy: $likedBy, bookmarkedBy: $bookmarkedBy, repostedBy: $repostedBy, isPublic: $isPublic, visibility: $visibility, isArchived: $isArchived, isReported: $isReported, isPinned: $isPinned, isDeleted: $isDeleted, isFlagged: $isFlagged, status: $status, viewCount: $viewCount, shareCount: $shareCount, repostCount: $repostCount, saveCount: $saveCount, trending: $trending, trendingScore: $trendingScore, lastEditedAt: $lastEditedAt, editedBy: $editedBy, isRemix: $isRemix, allowRemix: $allowRemix, allowRepost: $allowRepost, allowShare: $allowShare, originalPostId: $originalPostId, originalUserId: $originalUserId, originalUsername: $originalUsername, commentsDisabled: $commentsDisabled, likesHidden: $likesHidden, hasAIContent: $hasAIContent, musicTrack: $musicTrack, musicUrl: $musicUrl, flaggedReasons: $flaggedReasons, flaggedAt: $flaggedAt, flaggedBy: $flaggedBy, reportCount: $reportCount, reviewedAt: $reviewedAt, reviewedBy: $reviewedBy, moderationAction: $moderationAction, closeFriendsGroupIds: $closeFriendsGroupIds, metadata: $metadata, contentWarnings: $contentWarnings, ageRestriction: $ageRestriction)';
}


}

/// @nodoc
abstract mixin class $PostCopyWith<$Res>  {
  factory $PostCopyWith(Post value, $Res Function(Post) _then) = _$PostCopyWithImpl;
@useResult
$Res call({
 String id, String userId, String username, String userAvatarUrl, String? userRole, bool? isVerified, MediaType mediaType, String mediaUrl, List<String>? mediaUrls, List<MediaType>? mediaTypes, String caption, String? location, String? locationId, int likeCount, int commentCount, DateTime timestamp, DateTime? localCreatedAt, bool isLiked, bool isBookmarked, bool isReposted, List<String>? coAuthorIds, List<String>? coAuthorUsernames, List<String>? coAuthorAvatars, List<String>? mentionedUsers, List<String>? hashtags, List<MediaTag>? mediaTags, List<String>? taggedUserIds, List<String>? likedBy, List<String>? bookmarkedBy, List<String>? repostedBy, bool isPublic, String visibility, bool isArchived, bool isReported, bool isPinned, bool isDeleted, bool isFlagged, String status, int? viewCount, int? shareCount, int? repostCount, int? saveCount, bool trending, num? trendingScore, DateTime? lastEditedAt, String? editedBy, bool isRemix, bool allowRemix, bool allowRepost, bool allowShare, String? originalPostId, String? originalUserId, String? originalUsername, bool commentsDisabled, bool likesHidden, bool hasAIContent, String? musicTrack, String? musicUrl, List<String>? flaggedReasons, DateTime? flaggedAt, String? flaggedBy, int? reportCount, DateTime? reviewedAt, String? reviewedBy, String? moderationAction, List<String>? closeFriendsGroupIds, Map<String, dynamic>? metadata, List<String>? contentWarnings, String? ageRestriction
});




}
/// @nodoc
class _$PostCopyWithImpl<$Res>
    implements $PostCopyWith<$Res> {
  _$PostCopyWithImpl(this._self, this._then);

  final Post _self;
  final $Res Function(Post) _then;

/// Create a copy of Post
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? username = null,Object? userAvatarUrl = null,Object? userRole = freezed,Object? isVerified = freezed,Object? mediaType = null,Object? mediaUrl = null,Object? mediaUrls = freezed,Object? mediaTypes = freezed,Object? caption = null,Object? location = freezed,Object? locationId = freezed,Object? likeCount = null,Object? commentCount = null,Object? timestamp = null,Object? localCreatedAt = freezed,Object? isLiked = null,Object? isBookmarked = null,Object? isReposted = null,Object? coAuthorIds = freezed,Object? coAuthorUsernames = freezed,Object? coAuthorAvatars = freezed,Object? mentionedUsers = freezed,Object? hashtags = freezed,Object? mediaTags = freezed,Object? taggedUserIds = freezed,Object? likedBy = freezed,Object? bookmarkedBy = freezed,Object? repostedBy = freezed,Object? isPublic = null,Object? visibility = null,Object? isArchived = null,Object? isReported = null,Object? isPinned = null,Object? isDeleted = null,Object? isFlagged = null,Object? status = null,Object? viewCount = freezed,Object? shareCount = freezed,Object? repostCount = freezed,Object? saveCount = freezed,Object? trending = null,Object? trendingScore = freezed,Object? lastEditedAt = freezed,Object? editedBy = freezed,Object? isRemix = null,Object? allowRemix = null,Object? allowRepost = null,Object? allowShare = null,Object? originalPostId = freezed,Object? originalUserId = freezed,Object? originalUsername = freezed,Object? commentsDisabled = null,Object? likesHidden = null,Object? hasAIContent = null,Object? musicTrack = freezed,Object? musicUrl = freezed,Object? flaggedReasons = freezed,Object? flaggedAt = freezed,Object? flaggedBy = freezed,Object? reportCount = freezed,Object? reviewedAt = freezed,Object? reviewedBy = freezed,Object? moderationAction = freezed,Object? closeFriendsGroupIds = freezed,Object? metadata = freezed,Object? contentWarnings = freezed,Object? ageRestriction = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,userAvatarUrl: null == userAvatarUrl ? _self.userAvatarUrl : userAvatarUrl // ignore: cast_nullable_to_non_nullable
as String,userRole: freezed == userRole ? _self.userRole : userRole // ignore: cast_nullable_to_non_nullable
as String?,isVerified: freezed == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool?,mediaType: null == mediaType ? _self.mediaType : mediaType // ignore: cast_nullable_to_non_nullable
as MediaType,mediaUrl: null == mediaUrl ? _self.mediaUrl : mediaUrl // ignore: cast_nullable_to_non_nullable
as String,mediaUrls: freezed == mediaUrls ? _self.mediaUrls : mediaUrls // ignore: cast_nullable_to_non_nullable
as List<String>?,mediaTypes: freezed == mediaTypes ? _self.mediaTypes : mediaTypes // ignore: cast_nullable_to_non_nullable
as List<MediaType>?,caption: null == caption ? _self.caption : caption // ignore: cast_nullable_to_non_nullable
as String,location: freezed == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String?,locationId: freezed == locationId ? _self.locationId : locationId // ignore: cast_nullable_to_non_nullable
as String?,likeCount: null == likeCount ? _self.likeCount : likeCount // ignore: cast_nullable_to_non_nullable
as int,commentCount: null == commentCount ? _self.commentCount : commentCount // ignore: cast_nullable_to_non_nullable
as int,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,localCreatedAt: freezed == localCreatedAt ? _self.localCreatedAt : localCreatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isLiked: null == isLiked ? _self.isLiked : isLiked // ignore: cast_nullable_to_non_nullable
as bool,isBookmarked: null == isBookmarked ? _self.isBookmarked : isBookmarked // ignore: cast_nullable_to_non_nullable
as bool,isReposted: null == isReposted ? _self.isReposted : isReposted // ignore: cast_nullable_to_non_nullable
as bool,coAuthorIds: freezed == coAuthorIds ? _self.coAuthorIds : coAuthorIds // ignore: cast_nullable_to_non_nullable
as List<String>?,coAuthorUsernames: freezed == coAuthorUsernames ? _self.coAuthorUsernames : coAuthorUsernames // ignore: cast_nullable_to_non_nullable
as List<String>?,coAuthorAvatars: freezed == coAuthorAvatars ? _self.coAuthorAvatars : coAuthorAvatars // ignore: cast_nullable_to_non_nullable
as List<String>?,mentionedUsers: freezed == mentionedUsers ? _self.mentionedUsers : mentionedUsers // ignore: cast_nullable_to_non_nullable
as List<String>?,hashtags: freezed == hashtags ? _self.hashtags : hashtags // ignore: cast_nullable_to_non_nullable
as List<String>?,mediaTags: freezed == mediaTags ? _self.mediaTags : mediaTags // ignore: cast_nullable_to_non_nullable
as List<MediaTag>?,taggedUserIds: freezed == taggedUserIds ? _self.taggedUserIds : taggedUserIds // ignore: cast_nullable_to_non_nullable
as List<String>?,likedBy: freezed == likedBy ? _self.likedBy : likedBy // ignore: cast_nullable_to_non_nullable
as List<String>?,bookmarkedBy: freezed == bookmarkedBy ? _self.bookmarkedBy : bookmarkedBy // ignore: cast_nullable_to_non_nullable
as List<String>?,repostedBy: freezed == repostedBy ? _self.repostedBy : repostedBy // ignore: cast_nullable_to_non_nullable
as List<String>?,isPublic: null == isPublic ? _self.isPublic : isPublic // ignore: cast_nullable_to_non_nullable
as bool,visibility: null == visibility ? _self.visibility : visibility // ignore: cast_nullable_to_non_nullable
as String,isArchived: null == isArchived ? _self.isArchived : isArchived // ignore: cast_nullable_to_non_nullable
as bool,isReported: null == isReported ? _self.isReported : isReported // ignore: cast_nullable_to_non_nullable
as bool,isPinned: null == isPinned ? _self.isPinned : isPinned // ignore: cast_nullable_to_non_nullable
as bool,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,isFlagged: null == isFlagged ? _self.isFlagged : isFlagged // ignore: cast_nullable_to_non_nullable
as bool,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,viewCount: freezed == viewCount ? _self.viewCount : viewCount // ignore: cast_nullable_to_non_nullable
as int?,shareCount: freezed == shareCount ? _self.shareCount : shareCount // ignore: cast_nullable_to_non_nullable
as int?,repostCount: freezed == repostCount ? _self.repostCount : repostCount // ignore: cast_nullable_to_non_nullable
as int?,saveCount: freezed == saveCount ? _self.saveCount : saveCount // ignore: cast_nullable_to_non_nullable
as int?,trending: null == trending ? _self.trending : trending // ignore: cast_nullable_to_non_nullable
as bool,trendingScore: freezed == trendingScore ? _self.trendingScore : trendingScore // ignore: cast_nullable_to_non_nullable
as num?,lastEditedAt: freezed == lastEditedAt ? _self.lastEditedAt : lastEditedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,editedBy: freezed == editedBy ? _self.editedBy : editedBy // ignore: cast_nullable_to_non_nullable
as String?,isRemix: null == isRemix ? _self.isRemix : isRemix // ignore: cast_nullable_to_non_nullable
as bool,allowRemix: null == allowRemix ? _self.allowRemix : allowRemix // ignore: cast_nullable_to_non_nullable
as bool,allowRepost: null == allowRepost ? _self.allowRepost : allowRepost // ignore: cast_nullable_to_non_nullable
as bool,allowShare: null == allowShare ? _self.allowShare : allowShare // ignore: cast_nullable_to_non_nullable
as bool,originalPostId: freezed == originalPostId ? _self.originalPostId : originalPostId // ignore: cast_nullable_to_non_nullable
as String?,originalUserId: freezed == originalUserId ? _self.originalUserId : originalUserId // ignore: cast_nullable_to_non_nullable
as String?,originalUsername: freezed == originalUsername ? _self.originalUsername : originalUsername // ignore: cast_nullable_to_non_nullable
as String?,commentsDisabled: null == commentsDisabled ? _self.commentsDisabled : commentsDisabled // ignore: cast_nullable_to_non_nullable
as bool,likesHidden: null == likesHidden ? _self.likesHidden : likesHidden // ignore: cast_nullable_to_non_nullable
as bool,hasAIContent: null == hasAIContent ? _self.hasAIContent : hasAIContent // ignore: cast_nullable_to_non_nullable
as bool,musicTrack: freezed == musicTrack ? _self.musicTrack : musicTrack // ignore: cast_nullable_to_non_nullable
as String?,musicUrl: freezed == musicUrl ? _self.musicUrl : musicUrl // ignore: cast_nullable_to_non_nullable
as String?,flaggedReasons: freezed == flaggedReasons ? _self.flaggedReasons : flaggedReasons // ignore: cast_nullable_to_non_nullable
as List<String>?,flaggedAt: freezed == flaggedAt ? _self.flaggedAt : flaggedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,flaggedBy: freezed == flaggedBy ? _self.flaggedBy : flaggedBy // ignore: cast_nullable_to_non_nullable
as String?,reportCount: freezed == reportCount ? _self.reportCount : reportCount // ignore: cast_nullable_to_non_nullable
as int?,reviewedAt: freezed == reviewedAt ? _self.reviewedAt : reviewedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,reviewedBy: freezed == reviewedBy ? _self.reviewedBy : reviewedBy // ignore: cast_nullable_to_non_nullable
as String?,moderationAction: freezed == moderationAction ? _self.moderationAction : moderationAction // ignore: cast_nullable_to_non_nullable
as String?,closeFriendsGroupIds: freezed == closeFriendsGroupIds ? _self.closeFriendsGroupIds : closeFriendsGroupIds // ignore: cast_nullable_to_non_nullable
as List<String>?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,contentWarnings: freezed == contentWarnings ? _self.contentWarnings : contentWarnings // ignore: cast_nullable_to_non_nullable
as List<String>?,ageRestriction: freezed == ageRestriction ? _self.ageRestriction : ageRestriction // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [Post].
extension PostPatterns on Post {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Post value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Post() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Post value)  $default,){
final _that = this;
switch (_that) {
case _Post():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Post value)?  $default,){
final _that = this;
switch (_that) {
case _Post() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  String username,  String userAvatarUrl,  String? userRole,  bool? isVerified,  MediaType mediaType,  String mediaUrl,  List<String>? mediaUrls,  List<MediaType>? mediaTypes,  String caption,  String? location,  String? locationId,  int likeCount,  int commentCount,  DateTime timestamp,  DateTime? localCreatedAt,  bool isLiked,  bool isBookmarked,  bool isReposted,  List<String>? coAuthorIds,  List<String>? coAuthorUsernames,  List<String>? coAuthorAvatars,  List<String>? mentionedUsers,  List<String>? hashtags,  List<MediaTag>? mediaTags,  List<String>? taggedUserIds,  List<String>? likedBy,  List<String>? bookmarkedBy,  List<String>? repostedBy,  bool isPublic,  String visibility,  bool isArchived,  bool isReported,  bool isPinned,  bool isDeleted,  bool isFlagged,  String status,  int? viewCount,  int? shareCount,  int? repostCount,  int? saveCount,  bool trending,  num? trendingScore,  DateTime? lastEditedAt,  String? editedBy,  bool isRemix,  bool allowRemix,  bool allowRepost,  bool allowShare,  String? originalPostId,  String? originalUserId,  String? originalUsername,  bool commentsDisabled,  bool likesHidden,  bool hasAIContent,  String? musicTrack,  String? musicUrl,  List<String>? flaggedReasons,  DateTime? flaggedAt,  String? flaggedBy,  int? reportCount,  DateTime? reviewedAt,  String? reviewedBy,  String? moderationAction,  List<String>? closeFriendsGroupIds,  Map<String, dynamic>? metadata,  List<String>? contentWarnings,  String? ageRestriction)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Post() when $default != null:
return $default(_that.id,_that.userId,_that.username,_that.userAvatarUrl,_that.userRole,_that.isVerified,_that.mediaType,_that.mediaUrl,_that.mediaUrls,_that.mediaTypes,_that.caption,_that.location,_that.locationId,_that.likeCount,_that.commentCount,_that.timestamp,_that.localCreatedAt,_that.isLiked,_that.isBookmarked,_that.isReposted,_that.coAuthorIds,_that.coAuthorUsernames,_that.coAuthorAvatars,_that.mentionedUsers,_that.hashtags,_that.mediaTags,_that.taggedUserIds,_that.likedBy,_that.bookmarkedBy,_that.repostedBy,_that.isPublic,_that.visibility,_that.isArchived,_that.isReported,_that.isPinned,_that.isDeleted,_that.isFlagged,_that.status,_that.viewCount,_that.shareCount,_that.repostCount,_that.saveCount,_that.trending,_that.trendingScore,_that.lastEditedAt,_that.editedBy,_that.isRemix,_that.allowRemix,_that.allowRepost,_that.allowShare,_that.originalPostId,_that.originalUserId,_that.originalUsername,_that.commentsDisabled,_that.likesHidden,_that.hasAIContent,_that.musicTrack,_that.musicUrl,_that.flaggedReasons,_that.flaggedAt,_that.flaggedBy,_that.reportCount,_that.reviewedAt,_that.reviewedBy,_that.moderationAction,_that.closeFriendsGroupIds,_that.metadata,_that.contentWarnings,_that.ageRestriction);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  String username,  String userAvatarUrl,  String? userRole,  bool? isVerified,  MediaType mediaType,  String mediaUrl,  List<String>? mediaUrls,  List<MediaType>? mediaTypes,  String caption,  String? location,  String? locationId,  int likeCount,  int commentCount,  DateTime timestamp,  DateTime? localCreatedAt,  bool isLiked,  bool isBookmarked,  bool isReposted,  List<String>? coAuthorIds,  List<String>? coAuthorUsernames,  List<String>? coAuthorAvatars,  List<String>? mentionedUsers,  List<String>? hashtags,  List<MediaTag>? mediaTags,  List<String>? taggedUserIds,  List<String>? likedBy,  List<String>? bookmarkedBy,  List<String>? repostedBy,  bool isPublic,  String visibility,  bool isArchived,  bool isReported,  bool isPinned,  bool isDeleted,  bool isFlagged,  String status,  int? viewCount,  int? shareCount,  int? repostCount,  int? saveCount,  bool trending,  num? trendingScore,  DateTime? lastEditedAt,  String? editedBy,  bool isRemix,  bool allowRemix,  bool allowRepost,  bool allowShare,  String? originalPostId,  String? originalUserId,  String? originalUsername,  bool commentsDisabled,  bool likesHidden,  bool hasAIContent,  String? musicTrack,  String? musicUrl,  List<String>? flaggedReasons,  DateTime? flaggedAt,  String? flaggedBy,  int? reportCount,  DateTime? reviewedAt,  String? reviewedBy,  String? moderationAction,  List<String>? closeFriendsGroupIds,  Map<String, dynamic>? metadata,  List<String>? contentWarnings,  String? ageRestriction)  $default,) {final _that = this;
switch (_that) {
case _Post():
return $default(_that.id,_that.userId,_that.username,_that.userAvatarUrl,_that.userRole,_that.isVerified,_that.mediaType,_that.mediaUrl,_that.mediaUrls,_that.mediaTypes,_that.caption,_that.location,_that.locationId,_that.likeCount,_that.commentCount,_that.timestamp,_that.localCreatedAt,_that.isLiked,_that.isBookmarked,_that.isReposted,_that.coAuthorIds,_that.coAuthorUsernames,_that.coAuthorAvatars,_that.mentionedUsers,_that.hashtags,_that.mediaTags,_that.taggedUserIds,_that.likedBy,_that.bookmarkedBy,_that.repostedBy,_that.isPublic,_that.visibility,_that.isArchived,_that.isReported,_that.isPinned,_that.isDeleted,_that.isFlagged,_that.status,_that.viewCount,_that.shareCount,_that.repostCount,_that.saveCount,_that.trending,_that.trendingScore,_that.lastEditedAt,_that.editedBy,_that.isRemix,_that.allowRemix,_that.allowRepost,_that.allowShare,_that.originalPostId,_that.originalUserId,_that.originalUsername,_that.commentsDisabled,_that.likesHidden,_that.hasAIContent,_that.musicTrack,_that.musicUrl,_that.flaggedReasons,_that.flaggedAt,_that.flaggedBy,_that.reportCount,_that.reviewedAt,_that.reviewedBy,_that.moderationAction,_that.closeFriendsGroupIds,_that.metadata,_that.contentWarnings,_that.ageRestriction);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  String username,  String userAvatarUrl,  String? userRole,  bool? isVerified,  MediaType mediaType,  String mediaUrl,  List<String>? mediaUrls,  List<MediaType>? mediaTypes,  String caption,  String? location,  String? locationId,  int likeCount,  int commentCount,  DateTime timestamp,  DateTime? localCreatedAt,  bool isLiked,  bool isBookmarked,  bool isReposted,  List<String>? coAuthorIds,  List<String>? coAuthorUsernames,  List<String>? coAuthorAvatars,  List<String>? mentionedUsers,  List<String>? hashtags,  List<MediaTag>? mediaTags,  List<String>? taggedUserIds,  List<String>? likedBy,  List<String>? bookmarkedBy,  List<String>? repostedBy,  bool isPublic,  String visibility,  bool isArchived,  bool isReported,  bool isPinned,  bool isDeleted,  bool isFlagged,  String status,  int? viewCount,  int? shareCount,  int? repostCount,  int? saveCount,  bool trending,  num? trendingScore,  DateTime? lastEditedAt,  String? editedBy,  bool isRemix,  bool allowRemix,  bool allowRepost,  bool allowShare,  String? originalPostId,  String? originalUserId,  String? originalUsername,  bool commentsDisabled,  bool likesHidden,  bool hasAIContent,  String? musicTrack,  String? musicUrl,  List<String>? flaggedReasons,  DateTime? flaggedAt,  String? flaggedBy,  int? reportCount,  DateTime? reviewedAt,  String? reviewedBy,  String? moderationAction,  List<String>? closeFriendsGroupIds,  Map<String, dynamic>? metadata,  List<String>? contentWarnings,  String? ageRestriction)?  $default,) {final _that = this;
switch (_that) {
case _Post() when $default != null:
return $default(_that.id,_that.userId,_that.username,_that.userAvatarUrl,_that.userRole,_that.isVerified,_that.mediaType,_that.mediaUrl,_that.mediaUrls,_that.mediaTypes,_that.caption,_that.location,_that.locationId,_that.likeCount,_that.commentCount,_that.timestamp,_that.localCreatedAt,_that.isLiked,_that.isBookmarked,_that.isReposted,_that.coAuthorIds,_that.coAuthorUsernames,_that.coAuthorAvatars,_that.mentionedUsers,_that.hashtags,_that.mediaTags,_that.taggedUserIds,_that.likedBy,_that.bookmarkedBy,_that.repostedBy,_that.isPublic,_that.visibility,_that.isArchived,_that.isReported,_that.isPinned,_that.isDeleted,_that.isFlagged,_that.status,_that.viewCount,_that.shareCount,_that.repostCount,_that.saveCount,_that.trending,_that.trendingScore,_that.lastEditedAt,_that.editedBy,_that.isRemix,_that.allowRemix,_that.allowRepost,_that.allowShare,_that.originalPostId,_that.originalUserId,_that.originalUsername,_that.commentsDisabled,_that.likesHidden,_that.hasAIContent,_that.musicTrack,_that.musicUrl,_that.flaggedReasons,_that.flaggedAt,_that.flaggedBy,_that.reportCount,_that.reviewedAt,_that.reviewedBy,_that.moderationAction,_that.closeFriendsGroupIds,_that.metadata,_that.contentWarnings,_that.ageRestriction);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Post implements Post {
  const _Post({required this.id, required this.userId, required this.username, required this.userAvatarUrl, this.userRole, this.isVerified, required this.mediaType, required this.mediaUrl, final  List<String>? mediaUrls, final  List<MediaType>? mediaTypes, required this.caption, this.location, this.locationId, required this.likeCount, required this.commentCount, required this.timestamp, this.localCreatedAt, this.isLiked = false, this.isBookmarked = false, this.isReposted = false, final  List<String>? coAuthorIds, final  List<String>? coAuthorUsernames, final  List<String>? coAuthorAvatars, final  List<String>? mentionedUsers, final  List<String>? hashtags, final  List<MediaTag>? mediaTags, final  List<String>? taggedUserIds, final  List<String>? likedBy, final  List<String>? bookmarkedBy, final  List<String>? repostedBy, this.isPublic = true, this.visibility = 'public', this.isArchived = false, this.isReported = false, this.isPinned = false, this.isDeleted = false, this.isFlagged = false, this.status = 'active', this.viewCount, this.shareCount, this.repostCount, this.saveCount, this.trending = false, this.trendingScore, this.lastEditedAt, this.editedBy, this.isRemix = false, this.allowRemix = false, this.allowRepost = true, this.allowShare = true, this.originalPostId, this.originalUserId, this.originalUsername, this.commentsDisabled = false, this.likesHidden = false, this.hasAIContent = false, this.musicTrack, this.musicUrl, final  List<String>? flaggedReasons, this.flaggedAt, this.flaggedBy, this.reportCount, this.reviewedAt, this.reviewedBy, this.moderationAction, final  List<String>? closeFriendsGroupIds, final  Map<String, dynamic>? metadata, final  List<String>? contentWarnings, this.ageRestriction}): _mediaUrls = mediaUrls,_mediaTypes = mediaTypes,_coAuthorIds = coAuthorIds,_coAuthorUsernames = coAuthorUsernames,_coAuthorAvatars = coAuthorAvatars,_mentionedUsers = mentionedUsers,_hashtags = hashtags,_mediaTags = mediaTags,_taggedUserIds = taggedUserIds,_likedBy = likedBy,_bookmarkedBy = bookmarkedBy,_repostedBy = repostedBy,_flaggedReasons = flaggedReasons,_closeFriendsGroupIds = closeFriendsGroupIds,_metadata = metadata,_contentWarnings = contentWarnings;
  factory _Post.fromJson(Map<String, dynamic> json) => _$PostFromJson(json);

@override final  String id;
@override final  String userId;
@override final  String username;
@override final  String userAvatarUrl;
@override final  String? userRole;
// CEO, Plastic Surgeon, etc.
@override final  bool? isVerified;
// Billionaire badge
@override final  MediaType mediaType;
@override final  String mediaUrl;
// Multiple media support
 final  List<String>? _mediaUrls;
// Multiple media support
@override List<String>? get mediaUrls {
  final value = _mediaUrls;
  if (value == null) return null;
  if (_mediaUrls is EqualUnmodifiableListView) return _mediaUrls;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

// For multiple images/videos
 final  List<MediaType>? _mediaTypes;
// For multiple images/videos
@override List<MediaType>? get mediaTypes {
  final value = _mediaTypes;
  if (value == null) return null;
  if (_mediaTypes is EqualUnmodifiableListView) return _mediaTypes;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

// Corresponding media types
@override final  String caption;
@override final  String? location;
@override final  String? locationId;
// For location navigation
@override final  int likeCount;
@override final  int commentCount;
@override final  DateTime timestamp;
@override final  DateTime? localCreatedAt;
@override@JsonKey() final  bool isLiked;
@override@JsonKey() final  bool isBookmarked;
@override@JsonKey() final  bool isReposted;
// Co-author system (Remax feature)
 final  List<String>? _coAuthorIds;
// Co-author system (Remax feature)
@override List<String>? get coAuthorIds {
  final value = _coAuthorIds;
  if (value == null) return null;
  if (_coAuthorIds is EqualUnmodifiableListView) return _coAuthorIds;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<String>? _coAuthorUsernames;
@override List<String>? get coAuthorUsernames {
  final value = _coAuthorUsernames;
  if (value == null) return null;
  if (_coAuthorUsernames is EqualUnmodifiableListView) return _coAuthorUsernames;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<String>? _coAuthorAvatars;
@override List<String>? get coAuthorAvatars {
  final value = _coAuthorAvatars;
  if (value == null) return null;
  if (_coAuthorAvatars is EqualUnmodifiableListView) return _coAuthorAvatars;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

// Tagging system
 final  List<String>? _mentionedUsers;
// Tagging system
@override List<String>? get mentionedUsers {
  final value = _mentionedUsers;
  if (value == null) return null;
  if (_mentionedUsers is EqualUnmodifiableListView) return _mentionedUsers;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

// @mentions in caption
 final  List<String>? _hashtags;
// @mentions in caption
@override List<String>? get hashtags {
  final value = _hashtags;
  if (value == null) return null;
  if (_hashtags is EqualUnmodifiableListView) return _hashtags;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

// #hashtags in caption
 final  List<MediaTag>? _mediaTags;
// #hashtags in caption
@override List<MediaTag>? get mediaTags {
  final value = _mediaTags;
  if (value == null) return null;
  if (_mediaTags is EqualUnmodifiableListView) return _mediaTags;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

// Tags on photos/videos
 final  List<String>? _taggedUserIds;
// Tags on photos/videos
@override List<String>? get taggedUserIds {
  final value = _taggedUserIds;
  if (value == null) return null;
  if (_taggedUserIds is EqualUnmodifiableListView) return _taggedUserIds;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

// Users tagged in media
// Interaction tracking
 final  List<String>? _likedBy;
// Users tagged in media
// Interaction tracking
@override List<String>? get likedBy {
  final value = _likedBy;
  if (value == null) return null;
  if (_likedBy is EqualUnmodifiableListView) return _likedBy;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<String>? _bookmarkedBy;
@override List<String>? get bookmarkedBy {
  final value = _bookmarkedBy;
  if (value == null) return null;
  if (_bookmarkedBy is EqualUnmodifiableListView) return _bookmarkedBy;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<String>? _repostedBy;
@override List<String>? get repostedBy {
  final value = _repostedBy;
  if (value == null) return null;
  if (_repostedBy is EqualUnmodifiableListView) return _repostedBy;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

// Enhanced permissions and visibility
@override@JsonKey() final  bool isPublic;
@override@JsonKey() final  String visibility;
// 'public', 'followers', 'closeFriends', 'private'
@override@JsonKey() final  bool isArchived;
@override@JsonKey() final  bool isReported;
@override@JsonKey() final  bool isPinned;
// For pinned posts on profile
@override@JsonKey() final  bool isDeleted;
@override@JsonKey() final  bool isFlagged;
@override@JsonKey() final  String status;
// 'active', 'archived', 'deleted', 'flagged', 'review'
// Analytics and engagement
@override final  int? viewCount;
@override final  int? shareCount;
@override final  int? repostCount;
@override final  int? saveCount;
@override@JsonKey() final  bool trending;
// Trending badge
@override final  num? trendingScore;
// Trending score for ranking
@override final  DateTime? lastEditedAt;
@override final  String? editedBy;
// Remix and Repost features
@override@JsonKey() final  bool isRemix;
@override@JsonKey() final  bool allowRemix;
@override@JsonKey() final  bool allowRepost;
@override@JsonKey() final  bool allowShare;
@override final  String? originalPostId;
// For remixes and reposts
@override final  String? originalUserId;
@override final  String? originalUsername;
// Enhanced post features
@override@JsonKey() final  bool commentsDisabled;
@override@JsonKey() final  bool likesHidden;
@override@JsonKey() final  bool hasAIContent;
@override final  String? musicTrack;
@override final  String? musicUrl;
// Admin features
 final  List<String>? _flaggedReasons;
// Admin features
@override List<String>? get flaggedReasons {
  final value = _flaggedReasons;
  if (value == null) return null;
  if (_flaggedReasons is EqualUnmodifiableListView) return _flaggedReasons;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override final  DateTime? flaggedAt;
@override final  String? flaggedBy;
@override final  int? reportCount;
@override final  DateTime? reviewedAt;
@override final  String? reviewedBy;
@override final  String? moderationAction;
// Close friends and groups
 final  List<String>? _closeFriendsGroupIds;
// Close friends and groups
@override List<String>? get closeFriendsGroupIds {
  final value = _closeFriendsGroupIds;
  if (value == null) return null;
  if (_closeFriendsGroupIds is EqualUnmodifiableListView) return _closeFriendsGroupIds;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

// Content metadata
 final  Map<String, dynamic>? _metadata;
// Content metadata
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

 final  List<String>? _contentWarnings;
@override List<String>? get contentWarnings {
  final value = _contentWarnings;
  if (value == null) return null;
  if (_contentWarnings is EqualUnmodifiableListView) return _contentWarnings;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override final  String? ageRestriction;

/// Create a copy of Post
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PostCopyWith<_Post> get copyWith => __$PostCopyWithImpl<_Post>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PostToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Post&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.username, username) || other.username == username)&&(identical(other.userAvatarUrl, userAvatarUrl) || other.userAvatarUrl == userAvatarUrl)&&(identical(other.userRole, userRole) || other.userRole == userRole)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.mediaType, mediaType) || other.mediaType == mediaType)&&(identical(other.mediaUrl, mediaUrl) || other.mediaUrl == mediaUrl)&&const DeepCollectionEquality().equals(other._mediaUrls, _mediaUrls)&&const DeepCollectionEquality().equals(other._mediaTypes, _mediaTypes)&&(identical(other.caption, caption) || other.caption == caption)&&(identical(other.location, location) || other.location == location)&&(identical(other.locationId, locationId) || other.locationId == locationId)&&(identical(other.likeCount, likeCount) || other.likeCount == likeCount)&&(identical(other.commentCount, commentCount) || other.commentCount == commentCount)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.localCreatedAt, localCreatedAt) || other.localCreatedAt == localCreatedAt)&&(identical(other.isLiked, isLiked) || other.isLiked == isLiked)&&(identical(other.isBookmarked, isBookmarked) || other.isBookmarked == isBookmarked)&&(identical(other.isReposted, isReposted) || other.isReposted == isReposted)&&const DeepCollectionEquality().equals(other._coAuthorIds, _coAuthorIds)&&const DeepCollectionEquality().equals(other._coAuthorUsernames, _coAuthorUsernames)&&const DeepCollectionEquality().equals(other._coAuthorAvatars, _coAuthorAvatars)&&const DeepCollectionEquality().equals(other._mentionedUsers, _mentionedUsers)&&const DeepCollectionEquality().equals(other._hashtags, _hashtags)&&const DeepCollectionEquality().equals(other._mediaTags, _mediaTags)&&const DeepCollectionEquality().equals(other._taggedUserIds, _taggedUserIds)&&const DeepCollectionEquality().equals(other._likedBy, _likedBy)&&const DeepCollectionEquality().equals(other._bookmarkedBy, _bookmarkedBy)&&const DeepCollectionEquality().equals(other._repostedBy, _repostedBy)&&(identical(other.isPublic, isPublic) || other.isPublic == isPublic)&&(identical(other.visibility, visibility) || other.visibility == visibility)&&(identical(other.isArchived, isArchived) || other.isArchived == isArchived)&&(identical(other.isReported, isReported) || other.isReported == isReported)&&(identical(other.isPinned, isPinned) || other.isPinned == isPinned)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.isFlagged, isFlagged) || other.isFlagged == isFlagged)&&(identical(other.status, status) || other.status == status)&&(identical(other.viewCount, viewCount) || other.viewCount == viewCount)&&(identical(other.shareCount, shareCount) || other.shareCount == shareCount)&&(identical(other.repostCount, repostCount) || other.repostCount == repostCount)&&(identical(other.saveCount, saveCount) || other.saveCount == saveCount)&&(identical(other.trending, trending) || other.trending == trending)&&(identical(other.trendingScore, trendingScore) || other.trendingScore == trendingScore)&&(identical(other.lastEditedAt, lastEditedAt) || other.lastEditedAt == lastEditedAt)&&(identical(other.editedBy, editedBy) || other.editedBy == editedBy)&&(identical(other.isRemix, isRemix) || other.isRemix == isRemix)&&(identical(other.allowRemix, allowRemix) || other.allowRemix == allowRemix)&&(identical(other.allowRepost, allowRepost) || other.allowRepost == allowRepost)&&(identical(other.allowShare, allowShare) || other.allowShare == allowShare)&&(identical(other.originalPostId, originalPostId) || other.originalPostId == originalPostId)&&(identical(other.originalUserId, originalUserId) || other.originalUserId == originalUserId)&&(identical(other.originalUsername, originalUsername) || other.originalUsername == originalUsername)&&(identical(other.commentsDisabled, commentsDisabled) || other.commentsDisabled == commentsDisabled)&&(identical(other.likesHidden, likesHidden) || other.likesHidden == likesHidden)&&(identical(other.hasAIContent, hasAIContent) || other.hasAIContent == hasAIContent)&&(identical(other.musicTrack, musicTrack) || other.musicTrack == musicTrack)&&(identical(other.musicUrl, musicUrl) || other.musicUrl == musicUrl)&&const DeepCollectionEquality().equals(other._flaggedReasons, _flaggedReasons)&&(identical(other.flaggedAt, flaggedAt) || other.flaggedAt == flaggedAt)&&(identical(other.flaggedBy, flaggedBy) || other.flaggedBy == flaggedBy)&&(identical(other.reportCount, reportCount) || other.reportCount == reportCount)&&(identical(other.reviewedAt, reviewedAt) || other.reviewedAt == reviewedAt)&&(identical(other.reviewedBy, reviewedBy) || other.reviewedBy == reviewedBy)&&(identical(other.moderationAction, moderationAction) || other.moderationAction == moderationAction)&&const DeepCollectionEquality().equals(other._closeFriendsGroupIds, _closeFriendsGroupIds)&&const DeepCollectionEquality().equals(other._metadata, _metadata)&&const DeepCollectionEquality().equals(other._contentWarnings, _contentWarnings)&&(identical(other.ageRestriction, ageRestriction) || other.ageRestriction == ageRestriction));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,userId,username,userAvatarUrl,userRole,isVerified,mediaType,mediaUrl,const DeepCollectionEquality().hash(_mediaUrls),const DeepCollectionEquality().hash(_mediaTypes),caption,location,locationId,likeCount,commentCount,timestamp,localCreatedAt,isLiked,isBookmarked,isReposted,const DeepCollectionEquality().hash(_coAuthorIds),const DeepCollectionEquality().hash(_coAuthorUsernames),const DeepCollectionEquality().hash(_coAuthorAvatars),const DeepCollectionEquality().hash(_mentionedUsers),const DeepCollectionEquality().hash(_hashtags),const DeepCollectionEquality().hash(_mediaTags),const DeepCollectionEquality().hash(_taggedUserIds),const DeepCollectionEquality().hash(_likedBy),const DeepCollectionEquality().hash(_bookmarkedBy),const DeepCollectionEquality().hash(_repostedBy),isPublic,visibility,isArchived,isReported,isPinned,isDeleted,isFlagged,status,viewCount,shareCount,repostCount,saveCount,trending,trendingScore,lastEditedAt,editedBy,isRemix,allowRemix,allowRepost,allowShare,originalPostId,originalUserId,originalUsername,commentsDisabled,likesHidden,hasAIContent,musicTrack,musicUrl,const DeepCollectionEquality().hash(_flaggedReasons),flaggedAt,flaggedBy,reportCount,reviewedAt,reviewedBy,moderationAction,const DeepCollectionEquality().hash(_closeFriendsGroupIds),const DeepCollectionEquality().hash(_metadata),const DeepCollectionEquality().hash(_contentWarnings),ageRestriction]);

@override
String toString() {
  return 'Post(id: $id, userId: $userId, username: $username, userAvatarUrl: $userAvatarUrl, userRole: $userRole, isVerified: $isVerified, mediaType: $mediaType, mediaUrl: $mediaUrl, mediaUrls: $mediaUrls, mediaTypes: $mediaTypes, caption: $caption, location: $location, locationId: $locationId, likeCount: $likeCount, commentCount: $commentCount, timestamp: $timestamp, localCreatedAt: $localCreatedAt, isLiked: $isLiked, isBookmarked: $isBookmarked, isReposted: $isReposted, coAuthorIds: $coAuthorIds, coAuthorUsernames: $coAuthorUsernames, coAuthorAvatars: $coAuthorAvatars, mentionedUsers: $mentionedUsers, hashtags: $hashtags, mediaTags: $mediaTags, taggedUserIds: $taggedUserIds, likedBy: $likedBy, bookmarkedBy: $bookmarkedBy, repostedBy: $repostedBy, isPublic: $isPublic, visibility: $visibility, isArchived: $isArchived, isReported: $isReported, isPinned: $isPinned, isDeleted: $isDeleted, isFlagged: $isFlagged, status: $status, viewCount: $viewCount, shareCount: $shareCount, repostCount: $repostCount, saveCount: $saveCount, trending: $trending, trendingScore: $trendingScore, lastEditedAt: $lastEditedAt, editedBy: $editedBy, isRemix: $isRemix, allowRemix: $allowRemix, allowRepost: $allowRepost, allowShare: $allowShare, originalPostId: $originalPostId, originalUserId: $originalUserId, originalUsername: $originalUsername, commentsDisabled: $commentsDisabled, likesHidden: $likesHidden, hasAIContent: $hasAIContent, musicTrack: $musicTrack, musicUrl: $musicUrl, flaggedReasons: $flaggedReasons, flaggedAt: $flaggedAt, flaggedBy: $flaggedBy, reportCount: $reportCount, reviewedAt: $reviewedAt, reviewedBy: $reviewedBy, moderationAction: $moderationAction, closeFriendsGroupIds: $closeFriendsGroupIds, metadata: $metadata, contentWarnings: $contentWarnings, ageRestriction: $ageRestriction)';
}


}

/// @nodoc
abstract mixin class _$PostCopyWith<$Res> implements $PostCopyWith<$Res> {
  factory _$PostCopyWith(_Post value, $Res Function(_Post) _then) = __$PostCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, String username, String userAvatarUrl, String? userRole, bool? isVerified, MediaType mediaType, String mediaUrl, List<String>? mediaUrls, List<MediaType>? mediaTypes, String caption, String? location, String? locationId, int likeCount, int commentCount, DateTime timestamp, DateTime? localCreatedAt, bool isLiked, bool isBookmarked, bool isReposted, List<String>? coAuthorIds, List<String>? coAuthorUsernames, List<String>? coAuthorAvatars, List<String>? mentionedUsers, List<String>? hashtags, List<MediaTag>? mediaTags, List<String>? taggedUserIds, List<String>? likedBy, List<String>? bookmarkedBy, List<String>? repostedBy, bool isPublic, String visibility, bool isArchived, bool isReported, bool isPinned, bool isDeleted, bool isFlagged, String status, int? viewCount, int? shareCount, int? repostCount, int? saveCount, bool trending, num? trendingScore, DateTime? lastEditedAt, String? editedBy, bool isRemix, bool allowRemix, bool allowRepost, bool allowShare, String? originalPostId, String? originalUserId, String? originalUsername, bool commentsDisabled, bool likesHidden, bool hasAIContent, String? musicTrack, String? musicUrl, List<String>? flaggedReasons, DateTime? flaggedAt, String? flaggedBy, int? reportCount, DateTime? reviewedAt, String? reviewedBy, String? moderationAction, List<String>? closeFriendsGroupIds, Map<String, dynamic>? metadata, List<String>? contentWarnings, String? ageRestriction
});




}
/// @nodoc
class __$PostCopyWithImpl<$Res>
    implements _$PostCopyWith<$Res> {
  __$PostCopyWithImpl(this._self, this._then);

  final _Post _self;
  final $Res Function(_Post) _then;

/// Create a copy of Post
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? username = null,Object? userAvatarUrl = null,Object? userRole = freezed,Object? isVerified = freezed,Object? mediaType = null,Object? mediaUrl = null,Object? mediaUrls = freezed,Object? mediaTypes = freezed,Object? caption = null,Object? location = freezed,Object? locationId = freezed,Object? likeCount = null,Object? commentCount = null,Object? timestamp = null,Object? localCreatedAt = freezed,Object? isLiked = null,Object? isBookmarked = null,Object? isReposted = null,Object? coAuthorIds = freezed,Object? coAuthorUsernames = freezed,Object? coAuthorAvatars = freezed,Object? mentionedUsers = freezed,Object? hashtags = freezed,Object? mediaTags = freezed,Object? taggedUserIds = freezed,Object? likedBy = freezed,Object? bookmarkedBy = freezed,Object? repostedBy = freezed,Object? isPublic = null,Object? visibility = null,Object? isArchived = null,Object? isReported = null,Object? isPinned = null,Object? isDeleted = null,Object? isFlagged = null,Object? status = null,Object? viewCount = freezed,Object? shareCount = freezed,Object? repostCount = freezed,Object? saveCount = freezed,Object? trending = null,Object? trendingScore = freezed,Object? lastEditedAt = freezed,Object? editedBy = freezed,Object? isRemix = null,Object? allowRemix = null,Object? allowRepost = null,Object? allowShare = null,Object? originalPostId = freezed,Object? originalUserId = freezed,Object? originalUsername = freezed,Object? commentsDisabled = null,Object? likesHidden = null,Object? hasAIContent = null,Object? musicTrack = freezed,Object? musicUrl = freezed,Object? flaggedReasons = freezed,Object? flaggedAt = freezed,Object? flaggedBy = freezed,Object? reportCount = freezed,Object? reviewedAt = freezed,Object? reviewedBy = freezed,Object? moderationAction = freezed,Object? closeFriendsGroupIds = freezed,Object? metadata = freezed,Object? contentWarnings = freezed,Object? ageRestriction = freezed,}) {
  return _then(_Post(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,userAvatarUrl: null == userAvatarUrl ? _self.userAvatarUrl : userAvatarUrl // ignore: cast_nullable_to_non_nullable
as String,userRole: freezed == userRole ? _self.userRole : userRole // ignore: cast_nullable_to_non_nullable
as String?,isVerified: freezed == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool?,mediaType: null == mediaType ? _self.mediaType : mediaType // ignore: cast_nullable_to_non_nullable
as MediaType,mediaUrl: null == mediaUrl ? _self.mediaUrl : mediaUrl // ignore: cast_nullable_to_non_nullable
as String,mediaUrls: freezed == mediaUrls ? _self._mediaUrls : mediaUrls // ignore: cast_nullable_to_non_nullable
as List<String>?,mediaTypes: freezed == mediaTypes ? _self._mediaTypes : mediaTypes // ignore: cast_nullable_to_non_nullable
as List<MediaType>?,caption: null == caption ? _self.caption : caption // ignore: cast_nullable_to_non_nullable
as String,location: freezed == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String?,locationId: freezed == locationId ? _self.locationId : locationId // ignore: cast_nullable_to_non_nullable
as String?,likeCount: null == likeCount ? _self.likeCount : likeCount // ignore: cast_nullable_to_non_nullable
as int,commentCount: null == commentCount ? _self.commentCount : commentCount // ignore: cast_nullable_to_non_nullable
as int,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,localCreatedAt: freezed == localCreatedAt ? _self.localCreatedAt : localCreatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isLiked: null == isLiked ? _self.isLiked : isLiked // ignore: cast_nullable_to_non_nullable
as bool,isBookmarked: null == isBookmarked ? _self.isBookmarked : isBookmarked // ignore: cast_nullable_to_non_nullable
as bool,isReposted: null == isReposted ? _self.isReposted : isReposted // ignore: cast_nullable_to_non_nullable
as bool,coAuthorIds: freezed == coAuthorIds ? _self._coAuthorIds : coAuthorIds // ignore: cast_nullable_to_non_nullable
as List<String>?,coAuthorUsernames: freezed == coAuthorUsernames ? _self._coAuthorUsernames : coAuthorUsernames // ignore: cast_nullable_to_non_nullable
as List<String>?,coAuthorAvatars: freezed == coAuthorAvatars ? _self._coAuthorAvatars : coAuthorAvatars // ignore: cast_nullable_to_non_nullable
as List<String>?,mentionedUsers: freezed == mentionedUsers ? _self._mentionedUsers : mentionedUsers // ignore: cast_nullable_to_non_nullable
as List<String>?,hashtags: freezed == hashtags ? _self._hashtags : hashtags // ignore: cast_nullable_to_non_nullable
as List<String>?,mediaTags: freezed == mediaTags ? _self._mediaTags : mediaTags // ignore: cast_nullable_to_non_nullable
as List<MediaTag>?,taggedUserIds: freezed == taggedUserIds ? _self._taggedUserIds : taggedUserIds // ignore: cast_nullable_to_non_nullable
as List<String>?,likedBy: freezed == likedBy ? _self._likedBy : likedBy // ignore: cast_nullable_to_non_nullable
as List<String>?,bookmarkedBy: freezed == bookmarkedBy ? _self._bookmarkedBy : bookmarkedBy // ignore: cast_nullable_to_non_nullable
as List<String>?,repostedBy: freezed == repostedBy ? _self._repostedBy : repostedBy // ignore: cast_nullable_to_non_nullable
as List<String>?,isPublic: null == isPublic ? _self.isPublic : isPublic // ignore: cast_nullable_to_non_nullable
as bool,visibility: null == visibility ? _self.visibility : visibility // ignore: cast_nullable_to_non_nullable
as String,isArchived: null == isArchived ? _self.isArchived : isArchived // ignore: cast_nullable_to_non_nullable
as bool,isReported: null == isReported ? _self.isReported : isReported // ignore: cast_nullable_to_non_nullable
as bool,isPinned: null == isPinned ? _self.isPinned : isPinned // ignore: cast_nullable_to_non_nullable
as bool,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,isFlagged: null == isFlagged ? _self.isFlagged : isFlagged // ignore: cast_nullable_to_non_nullable
as bool,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,viewCount: freezed == viewCount ? _self.viewCount : viewCount // ignore: cast_nullable_to_non_nullable
as int?,shareCount: freezed == shareCount ? _self.shareCount : shareCount // ignore: cast_nullable_to_non_nullable
as int?,repostCount: freezed == repostCount ? _self.repostCount : repostCount // ignore: cast_nullable_to_non_nullable
as int?,saveCount: freezed == saveCount ? _self.saveCount : saveCount // ignore: cast_nullable_to_non_nullable
as int?,trending: null == trending ? _self.trending : trending // ignore: cast_nullable_to_non_nullable
as bool,trendingScore: freezed == trendingScore ? _self.trendingScore : trendingScore // ignore: cast_nullable_to_non_nullable
as num?,lastEditedAt: freezed == lastEditedAt ? _self.lastEditedAt : lastEditedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,editedBy: freezed == editedBy ? _self.editedBy : editedBy // ignore: cast_nullable_to_non_nullable
as String?,isRemix: null == isRemix ? _self.isRemix : isRemix // ignore: cast_nullable_to_non_nullable
as bool,allowRemix: null == allowRemix ? _self.allowRemix : allowRemix // ignore: cast_nullable_to_non_nullable
as bool,allowRepost: null == allowRepost ? _self.allowRepost : allowRepost // ignore: cast_nullable_to_non_nullable
as bool,allowShare: null == allowShare ? _self.allowShare : allowShare // ignore: cast_nullable_to_non_nullable
as bool,originalPostId: freezed == originalPostId ? _self.originalPostId : originalPostId // ignore: cast_nullable_to_non_nullable
as String?,originalUserId: freezed == originalUserId ? _self.originalUserId : originalUserId // ignore: cast_nullable_to_non_nullable
as String?,originalUsername: freezed == originalUsername ? _self.originalUsername : originalUsername // ignore: cast_nullable_to_non_nullable
as String?,commentsDisabled: null == commentsDisabled ? _self.commentsDisabled : commentsDisabled // ignore: cast_nullable_to_non_nullable
as bool,likesHidden: null == likesHidden ? _self.likesHidden : likesHidden // ignore: cast_nullable_to_non_nullable
as bool,hasAIContent: null == hasAIContent ? _self.hasAIContent : hasAIContent // ignore: cast_nullable_to_non_nullable
as bool,musicTrack: freezed == musicTrack ? _self.musicTrack : musicTrack // ignore: cast_nullable_to_non_nullable
as String?,musicUrl: freezed == musicUrl ? _self.musicUrl : musicUrl // ignore: cast_nullable_to_non_nullable
as String?,flaggedReasons: freezed == flaggedReasons ? _self._flaggedReasons : flaggedReasons // ignore: cast_nullable_to_non_nullable
as List<String>?,flaggedAt: freezed == flaggedAt ? _self.flaggedAt : flaggedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,flaggedBy: freezed == flaggedBy ? _self.flaggedBy : flaggedBy // ignore: cast_nullable_to_non_nullable
as String?,reportCount: freezed == reportCount ? _self.reportCount : reportCount // ignore: cast_nullable_to_non_nullable
as int?,reviewedAt: freezed == reviewedAt ? _self.reviewedAt : reviewedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,reviewedBy: freezed == reviewedBy ? _self.reviewedBy : reviewedBy // ignore: cast_nullable_to_non_nullable
as String?,moderationAction: freezed == moderationAction ? _self.moderationAction : moderationAction // ignore: cast_nullable_to_non_nullable
as String?,closeFriendsGroupIds: freezed == closeFriendsGroupIds ? _self._closeFriendsGroupIds : closeFriendsGroupIds // ignore: cast_nullable_to_non_nullable
as List<String>?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,contentWarnings: freezed == contentWarnings ? _self._contentWarnings : contentWarnings // ignore: cast_nullable_to_non_nullable
as List<String>?,ageRestriction: freezed == ageRestriction ? _self.ageRestriction : ageRestriction // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$MediaTag {

 String get userId; String get username; double get x;// X position on media (0-1)
 double get y;
/// Create a copy of MediaTag
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MediaTagCopyWith<MediaTag> get copyWith => _$MediaTagCopyWithImpl<MediaTag>(this as MediaTag, _$identity);

  /// Serializes this MediaTag to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MediaTag&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.username, username) || other.username == username)&&(identical(other.x, x) || other.x == x)&&(identical(other.y, y) || other.y == y));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,userId,username,x,y);

@override
String toString() {
  return 'MediaTag(userId: $userId, username: $username, x: $x, y: $y)';
}


}

/// @nodoc
abstract mixin class $MediaTagCopyWith<$Res>  {
  factory $MediaTagCopyWith(MediaTag value, $Res Function(MediaTag) _then) = _$MediaTagCopyWithImpl;
@useResult
$Res call({
 String userId, String username, double x, double y
});




}
/// @nodoc
class _$MediaTagCopyWithImpl<$Res>
    implements $MediaTagCopyWith<$Res> {
  _$MediaTagCopyWithImpl(this._self, this._then);

  final MediaTag _self;
  final $Res Function(MediaTag) _then;

/// Create a copy of MediaTag
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? userId = null,Object? username = null,Object? x = null,Object? y = null,}) {
  return _then(_self.copyWith(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,x: null == x ? _self.x : x // ignore: cast_nullable_to_non_nullable
as double,y: null == y ? _self.y : y // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [MediaTag].
extension MediaTagPatterns on MediaTag {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _MediaTag value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _MediaTag() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _MediaTag value)  $default,){
final _that = this;
switch (_that) {
case _MediaTag():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _MediaTag value)?  $default,){
final _that = this;
switch (_that) {
case _MediaTag() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String userId,  String username,  double x,  double y)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _MediaTag() when $default != null:
return $default(_that.userId,_that.username,_that.x,_that.y);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String userId,  String username,  double x,  double y)  $default,) {final _that = this;
switch (_that) {
case _MediaTag():
return $default(_that.userId,_that.username,_that.x,_that.y);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String userId,  String username,  double x,  double y)?  $default,) {final _that = this;
switch (_that) {
case _MediaTag() when $default != null:
return $default(_that.userId,_that.username,_that.x,_that.y);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _MediaTag implements MediaTag {
  const _MediaTag({required this.userId, required this.username, required this.x, required this.y});
  factory _MediaTag.fromJson(Map<String, dynamic> json) => _$MediaTagFromJson(json);

@override final  String userId;
@override final  String username;
@override final  double x;
// X position on media (0-1)
@override final  double y;

/// Create a copy of MediaTag
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MediaTagCopyWith<_MediaTag> get copyWith => __$MediaTagCopyWithImpl<_MediaTag>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$MediaTagToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _MediaTag&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.username, username) || other.username == username)&&(identical(other.x, x) || other.x == x)&&(identical(other.y, y) || other.y == y));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,userId,username,x,y);

@override
String toString() {
  return 'MediaTag(userId: $userId, username: $username, x: $x, y: $y)';
}


}

/// @nodoc
abstract mixin class _$MediaTagCopyWith<$Res> implements $MediaTagCopyWith<$Res> {
  factory _$MediaTagCopyWith(_MediaTag value, $Res Function(_MediaTag) _then) = __$MediaTagCopyWithImpl;
@override @useResult
$Res call({
 String userId, String username, double x, double y
});




}
/// @nodoc
class __$MediaTagCopyWithImpl<$Res>
    implements _$MediaTagCopyWith<$Res> {
  __$MediaTagCopyWithImpl(this._self, this._then);

  final _MediaTag _self;
  final $Res Function(_MediaTag) _then;

/// Create a copy of MediaTag
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? userId = null,Object? username = null,Object? x = null,Object? y = null,}) {
  return _then(_MediaTag(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,x: null == x ? _self.x : x // ignore: cast_nullable_to_non_nullable
as double,y: null == y ? _self.y : y // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}


/// @nodoc
mixin _$PostInteraction {

 String get postId; String get userId; String get username; String get userAvatarUrl; InteractionType get type; DateTime get timestamp; String? get comment;
/// Create a copy of PostInteraction
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PostInteractionCopyWith<PostInteraction> get copyWith => _$PostInteractionCopyWithImpl<PostInteraction>(this as PostInteraction, _$identity);

  /// Serializes this PostInteraction to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PostInteraction&&(identical(other.postId, postId) || other.postId == postId)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.username, username) || other.username == username)&&(identical(other.userAvatarUrl, userAvatarUrl) || other.userAvatarUrl == userAvatarUrl)&&(identical(other.type, type) || other.type == type)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.comment, comment) || other.comment == comment));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,postId,userId,username,userAvatarUrl,type,timestamp,comment);

@override
String toString() {
  return 'PostInteraction(postId: $postId, userId: $userId, username: $username, userAvatarUrl: $userAvatarUrl, type: $type, timestamp: $timestamp, comment: $comment)';
}


}

/// @nodoc
abstract mixin class $PostInteractionCopyWith<$Res>  {
  factory $PostInteractionCopyWith(PostInteraction value, $Res Function(PostInteraction) _then) = _$PostInteractionCopyWithImpl;
@useResult
$Res call({
 String postId, String userId, String username, String userAvatarUrl, InteractionType type, DateTime timestamp, String? comment
});




}
/// @nodoc
class _$PostInteractionCopyWithImpl<$Res>
    implements $PostInteractionCopyWith<$Res> {
  _$PostInteractionCopyWithImpl(this._self, this._then);

  final PostInteraction _self;
  final $Res Function(PostInteraction) _then;

/// Create a copy of PostInteraction
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? postId = null,Object? userId = null,Object? username = null,Object? userAvatarUrl = null,Object? type = null,Object? timestamp = null,Object? comment = freezed,}) {
  return _then(_self.copyWith(
postId: null == postId ? _self.postId : postId // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,userAvatarUrl: null == userAvatarUrl ? _self.userAvatarUrl : userAvatarUrl // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as InteractionType,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,comment: freezed == comment ? _self.comment : comment // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [PostInteraction].
extension PostInteractionPatterns on PostInteraction {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PostInteraction value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PostInteraction() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PostInteraction value)  $default,){
final _that = this;
switch (_that) {
case _PostInteraction():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PostInteraction value)?  $default,){
final _that = this;
switch (_that) {
case _PostInteraction() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String postId,  String userId,  String username,  String userAvatarUrl,  InteractionType type,  DateTime timestamp,  String? comment)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PostInteraction() when $default != null:
return $default(_that.postId,_that.userId,_that.username,_that.userAvatarUrl,_that.type,_that.timestamp,_that.comment);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String postId,  String userId,  String username,  String userAvatarUrl,  InteractionType type,  DateTime timestamp,  String? comment)  $default,) {final _that = this;
switch (_that) {
case _PostInteraction():
return $default(_that.postId,_that.userId,_that.username,_that.userAvatarUrl,_that.type,_that.timestamp,_that.comment);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String postId,  String userId,  String username,  String userAvatarUrl,  InteractionType type,  DateTime timestamp,  String? comment)?  $default,) {final _that = this;
switch (_that) {
case _PostInteraction() when $default != null:
return $default(_that.postId,_that.userId,_that.username,_that.userAvatarUrl,_that.type,_that.timestamp,_that.comment);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PostInteraction implements PostInteraction {
  const _PostInteraction({required this.postId, required this.userId, required this.username, required this.userAvatarUrl, required this.type, required this.timestamp, this.comment});
  factory _PostInteraction.fromJson(Map<String, dynamic> json) => _$PostInteractionFromJson(json);

@override final  String postId;
@override final  String userId;
@override final  String username;
@override final  String userAvatarUrl;
@override final  InteractionType type;
@override final  DateTime timestamp;
@override final  String? comment;

/// Create a copy of PostInteraction
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PostInteractionCopyWith<_PostInteraction> get copyWith => __$PostInteractionCopyWithImpl<_PostInteraction>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PostInteractionToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PostInteraction&&(identical(other.postId, postId) || other.postId == postId)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.username, username) || other.username == username)&&(identical(other.userAvatarUrl, userAvatarUrl) || other.userAvatarUrl == userAvatarUrl)&&(identical(other.type, type) || other.type == type)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.comment, comment) || other.comment == comment));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,postId,userId,username,userAvatarUrl,type,timestamp,comment);

@override
String toString() {
  return 'PostInteraction(postId: $postId, userId: $userId, username: $username, userAvatarUrl: $userAvatarUrl, type: $type, timestamp: $timestamp, comment: $comment)';
}


}

/// @nodoc
abstract mixin class _$PostInteractionCopyWith<$Res> implements $PostInteractionCopyWith<$Res> {
  factory _$PostInteractionCopyWith(_PostInteraction value, $Res Function(_PostInteraction) _then) = __$PostInteractionCopyWithImpl;
@override @useResult
$Res call({
 String postId, String userId, String username, String userAvatarUrl, InteractionType type, DateTime timestamp, String? comment
});




}
/// @nodoc
class __$PostInteractionCopyWithImpl<$Res>
    implements _$PostInteractionCopyWith<$Res> {
  __$PostInteractionCopyWithImpl(this._self, this._then);

  final _PostInteraction _self;
  final $Res Function(_PostInteraction) _then;

/// Create a copy of PostInteraction
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? postId = null,Object? userId = null,Object? username = null,Object? userAvatarUrl = null,Object? type = null,Object? timestamp = null,Object? comment = freezed,}) {
  return _then(_PostInteraction(
postId: null == postId ? _self.postId : postId // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,userAvatarUrl: null == userAvatarUrl ? _self.userAvatarUrl : userAvatarUrl // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as InteractionType,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,comment: freezed == comment ? _self.comment : comment // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$PostPermission {

 String get postId; String get userId; bool get canEdit; bool get canDelete; bool get canModerateComments; bool get canArchive; bool get canReport;
/// Create a copy of PostPermission
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PostPermissionCopyWith<PostPermission> get copyWith => _$PostPermissionCopyWithImpl<PostPermission>(this as PostPermission, _$identity);

  /// Serializes this PostPermission to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PostPermission&&(identical(other.postId, postId) || other.postId == postId)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.canEdit, canEdit) || other.canEdit == canEdit)&&(identical(other.canDelete, canDelete) || other.canDelete == canDelete)&&(identical(other.canModerateComments, canModerateComments) || other.canModerateComments == canModerateComments)&&(identical(other.canArchive, canArchive) || other.canArchive == canArchive)&&(identical(other.canReport, canReport) || other.canReport == canReport));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,postId,userId,canEdit,canDelete,canModerateComments,canArchive,canReport);

@override
String toString() {
  return 'PostPermission(postId: $postId, userId: $userId, canEdit: $canEdit, canDelete: $canDelete, canModerateComments: $canModerateComments, canArchive: $canArchive, canReport: $canReport)';
}


}

/// @nodoc
abstract mixin class $PostPermissionCopyWith<$Res>  {
  factory $PostPermissionCopyWith(PostPermission value, $Res Function(PostPermission) _then) = _$PostPermissionCopyWithImpl;
@useResult
$Res call({
 String postId, String userId, bool canEdit, bool canDelete, bool canModerateComments, bool canArchive, bool canReport
});




}
/// @nodoc
class _$PostPermissionCopyWithImpl<$Res>
    implements $PostPermissionCopyWith<$Res> {
  _$PostPermissionCopyWithImpl(this._self, this._then);

  final PostPermission _self;
  final $Res Function(PostPermission) _then;

/// Create a copy of PostPermission
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? postId = null,Object? userId = null,Object? canEdit = null,Object? canDelete = null,Object? canModerateComments = null,Object? canArchive = null,Object? canReport = null,}) {
  return _then(_self.copyWith(
postId: null == postId ? _self.postId : postId // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,canEdit: null == canEdit ? _self.canEdit : canEdit // ignore: cast_nullable_to_non_nullable
as bool,canDelete: null == canDelete ? _self.canDelete : canDelete // ignore: cast_nullable_to_non_nullable
as bool,canModerateComments: null == canModerateComments ? _self.canModerateComments : canModerateComments // ignore: cast_nullable_to_non_nullable
as bool,canArchive: null == canArchive ? _self.canArchive : canArchive // ignore: cast_nullable_to_non_nullable
as bool,canReport: null == canReport ? _self.canReport : canReport // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [PostPermission].
extension PostPermissionPatterns on PostPermission {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PostPermission value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PostPermission() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PostPermission value)  $default,){
final _that = this;
switch (_that) {
case _PostPermission():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PostPermission value)?  $default,){
final _that = this;
switch (_that) {
case _PostPermission() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String postId,  String userId,  bool canEdit,  bool canDelete,  bool canModerateComments,  bool canArchive,  bool canReport)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PostPermission() when $default != null:
return $default(_that.postId,_that.userId,_that.canEdit,_that.canDelete,_that.canModerateComments,_that.canArchive,_that.canReport);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String postId,  String userId,  bool canEdit,  bool canDelete,  bool canModerateComments,  bool canArchive,  bool canReport)  $default,) {final _that = this;
switch (_that) {
case _PostPermission():
return $default(_that.postId,_that.userId,_that.canEdit,_that.canDelete,_that.canModerateComments,_that.canArchive,_that.canReport);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String postId,  String userId,  bool canEdit,  bool canDelete,  bool canModerateComments,  bool canArchive,  bool canReport)?  $default,) {final _that = this;
switch (_that) {
case _PostPermission() when $default != null:
return $default(_that.postId,_that.userId,_that.canEdit,_that.canDelete,_that.canModerateComments,_that.canArchive,_that.canReport);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PostPermission implements PostPermission {
  const _PostPermission({required this.postId, required this.userId, required this.canEdit, required this.canDelete, required this.canModerateComments, required this.canArchive, required this.canReport});
  factory _PostPermission.fromJson(Map<String, dynamic> json) => _$PostPermissionFromJson(json);

@override final  String postId;
@override final  String userId;
@override final  bool canEdit;
@override final  bool canDelete;
@override final  bool canModerateComments;
@override final  bool canArchive;
@override final  bool canReport;

/// Create a copy of PostPermission
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PostPermissionCopyWith<_PostPermission> get copyWith => __$PostPermissionCopyWithImpl<_PostPermission>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PostPermissionToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PostPermission&&(identical(other.postId, postId) || other.postId == postId)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.canEdit, canEdit) || other.canEdit == canEdit)&&(identical(other.canDelete, canDelete) || other.canDelete == canDelete)&&(identical(other.canModerateComments, canModerateComments) || other.canModerateComments == canModerateComments)&&(identical(other.canArchive, canArchive) || other.canArchive == canArchive)&&(identical(other.canReport, canReport) || other.canReport == canReport));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,postId,userId,canEdit,canDelete,canModerateComments,canArchive,canReport);

@override
String toString() {
  return 'PostPermission(postId: $postId, userId: $userId, canEdit: $canEdit, canDelete: $canDelete, canModerateComments: $canModerateComments, canArchive: $canArchive, canReport: $canReport)';
}


}

/// @nodoc
abstract mixin class _$PostPermissionCopyWith<$Res> implements $PostPermissionCopyWith<$Res> {
  factory _$PostPermissionCopyWith(_PostPermission value, $Res Function(_PostPermission) _then) = __$PostPermissionCopyWithImpl;
@override @useResult
$Res call({
 String postId, String userId, bool canEdit, bool canDelete, bool canModerateComments, bool canArchive, bool canReport
});




}
/// @nodoc
class __$PostPermissionCopyWithImpl<$Res>
    implements _$PostPermissionCopyWith<$Res> {
  __$PostPermissionCopyWithImpl(this._self, this._then);

  final _PostPermission _self;
  final $Res Function(_PostPermission) _then;

/// Create a copy of PostPermission
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? postId = null,Object? userId = null,Object? canEdit = null,Object? canDelete = null,Object? canModerateComments = null,Object? canArchive = null,Object? canReport = null,}) {
  return _then(_PostPermission(
postId: null == postId ? _self.postId : postId // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,canEdit: null == canEdit ? _self.canEdit : canEdit // ignore: cast_nullable_to_non_nullable
as bool,canDelete: null == canDelete ? _self.canDelete : canDelete // ignore: cast_nullable_to_non_nullable
as bool,canModerateComments: null == canModerateComments ? _self.canModerateComments : canModerateComments // ignore: cast_nullable_to_non_nullable
as bool,canArchive: null == canArchive ? _self.canArchive : canArchive // ignore: cast_nullable_to_non_nullable
as bool,canReport: null == canReport ? _self.canReport : canReport // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
