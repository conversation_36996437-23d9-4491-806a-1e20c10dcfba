// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'comment_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Comment {

 String get id; String get postId; String get userId; String get username; String get userAvatarUrl; String get text;@TimestampConverter() DateTime get timestamp; String? get parentId; CommentType get type; String? get voiceUrl; int? get voiceDuration;// Duration in seconds
 String? get waveformData;// JSON string of waveform data for visualization
 Map<String, String>? get reactions;// userId -> emoji reactions
 int get reactionCount; Map<String, int>? get reactionCounts;// emoji -> count mapping
 bool get isEdited; DateTime? get editedAt;
/// Create a copy of Comment
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CommentCopyWith<Comment> get copyWith => _$CommentCopyWithImpl<Comment>(this as Comment, _$identity);

  /// Serializes this Comment to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Comment&&(identical(other.id, id) || other.id == id)&&(identical(other.postId, postId) || other.postId == postId)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.username, username) || other.username == username)&&(identical(other.userAvatarUrl, userAvatarUrl) || other.userAvatarUrl == userAvatarUrl)&&(identical(other.text, text) || other.text == text)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.parentId, parentId) || other.parentId == parentId)&&(identical(other.type, type) || other.type == type)&&(identical(other.voiceUrl, voiceUrl) || other.voiceUrl == voiceUrl)&&(identical(other.voiceDuration, voiceDuration) || other.voiceDuration == voiceDuration)&&(identical(other.waveformData, waveformData) || other.waveformData == waveformData)&&const DeepCollectionEquality().equals(other.reactions, reactions)&&(identical(other.reactionCount, reactionCount) || other.reactionCount == reactionCount)&&const DeepCollectionEquality().equals(other.reactionCounts, reactionCounts)&&(identical(other.isEdited, isEdited) || other.isEdited == isEdited)&&(identical(other.editedAt, editedAt) || other.editedAt == editedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,postId,userId,username,userAvatarUrl,text,timestamp,parentId,type,voiceUrl,voiceDuration,waveformData,const DeepCollectionEquality().hash(reactions),reactionCount,const DeepCollectionEquality().hash(reactionCounts),isEdited,editedAt);

@override
String toString() {
  return 'Comment(id: $id, postId: $postId, userId: $userId, username: $username, userAvatarUrl: $userAvatarUrl, text: $text, timestamp: $timestamp, parentId: $parentId, type: $type, voiceUrl: $voiceUrl, voiceDuration: $voiceDuration, waveformData: $waveformData, reactions: $reactions, reactionCount: $reactionCount, reactionCounts: $reactionCounts, isEdited: $isEdited, editedAt: $editedAt)';
}


}

/// @nodoc
abstract mixin class $CommentCopyWith<$Res>  {
  factory $CommentCopyWith(Comment value, $Res Function(Comment) _then) = _$CommentCopyWithImpl;
@useResult
$Res call({
 String id, String postId, String userId, String username, String userAvatarUrl, String text,@TimestampConverter() DateTime timestamp, String? parentId, CommentType type, String? voiceUrl, int? voiceDuration, String? waveformData, Map<String, String>? reactions, int reactionCount, Map<String, int>? reactionCounts, bool isEdited, DateTime? editedAt
});




}
/// @nodoc
class _$CommentCopyWithImpl<$Res>
    implements $CommentCopyWith<$Res> {
  _$CommentCopyWithImpl(this._self, this._then);

  final Comment _self;
  final $Res Function(Comment) _then;

/// Create a copy of Comment
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? postId = null,Object? userId = null,Object? username = null,Object? userAvatarUrl = null,Object? text = null,Object? timestamp = null,Object? parentId = freezed,Object? type = null,Object? voiceUrl = freezed,Object? voiceDuration = freezed,Object? waveformData = freezed,Object? reactions = freezed,Object? reactionCount = null,Object? reactionCounts = freezed,Object? isEdited = null,Object? editedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,postId: null == postId ? _self.postId : postId // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,userAvatarUrl: null == userAvatarUrl ? _self.userAvatarUrl : userAvatarUrl // ignore: cast_nullable_to_non_nullable
as String,text: null == text ? _self.text : text // ignore: cast_nullable_to_non_nullable
as String,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,parentId: freezed == parentId ? _self.parentId : parentId // ignore: cast_nullable_to_non_nullable
as String?,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as CommentType,voiceUrl: freezed == voiceUrl ? _self.voiceUrl : voiceUrl // ignore: cast_nullable_to_non_nullable
as String?,voiceDuration: freezed == voiceDuration ? _self.voiceDuration : voiceDuration // ignore: cast_nullable_to_non_nullable
as int?,waveformData: freezed == waveformData ? _self.waveformData : waveformData // ignore: cast_nullable_to_non_nullable
as String?,reactions: freezed == reactions ? _self.reactions : reactions // ignore: cast_nullable_to_non_nullable
as Map<String, String>?,reactionCount: null == reactionCount ? _self.reactionCount : reactionCount // ignore: cast_nullable_to_non_nullable
as int,reactionCounts: freezed == reactionCounts ? _self.reactionCounts : reactionCounts // ignore: cast_nullable_to_non_nullable
as Map<String, int>?,isEdited: null == isEdited ? _self.isEdited : isEdited // ignore: cast_nullable_to_non_nullable
as bool,editedAt: freezed == editedAt ? _self.editedAt : editedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [Comment].
extension CommentPatterns on Comment {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Comment value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Comment() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Comment value)  $default,){
final _that = this;
switch (_that) {
case _Comment():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Comment value)?  $default,){
final _that = this;
switch (_that) {
case _Comment() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String postId,  String userId,  String username,  String userAvatarUrl,  String text, @TimestampConverter()  DateTime timestamp,  String? parentId,  CommentType type,  String? voiceUrl,  int? voiceDuration,  String? waveformData,  Map<String, String>? reactions,  int reactionCount,  Map<String, int>? reactionCounts,  bool isEdited,  DateTime? editedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Comment() when $default != null:
return $default(_that.id,_that.postId,_that.userId,_that.username,_that.userAvatarUrl,_that.text,_that.timestamp,_that.parentId,_that.type,_that.voiceUrl,_that.voiceDuration,_that.waveformData,_that.reactions,_that.reactionCount,_that.reactionCounts,_that.isEdited,_that.editedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String postId,  String userId,  String username,  String userAvatarUrl,  String text, @TimestampConverter()  DateTime timestamp,  String? parentId,  CommentType type,  String? voiceUrl,  int? voiceDuration,  String? waveformData,  Map<String, String>? reactions,  int reactionCount,  Map<String, int>? reactionCounts,  bool isEdited,  DateTime? editedAt)  $default,) {final _that = this;
switch (_that) {
case _Comment():
return $default(_that.id,_that.postId,_that.userId,_that.username,_that.userAvatarUrl,_that.text,_that.timestamp,_that.parentId,_that.type,_that.voiceUrl,_that.voiceDuration,_that.waveformData,_that.reactions,_that.reactionCount,_that.reactionCounts,_that.isEdited,_that.editedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String postId,  String userId,  String username,  String userAvatarUrl,  String text, @TimestampConverter()  DateTime timestamp,  String? parentId,  CommentType type,  String? voiceUrl,  int? voiceDuration,  String? waveformData,  Map<String, String>? reactions,  int reactionCount,  Map<String, int>? reactionCounts,  bool isEdited,  DateTime? editedAt)?  $default,) {final _that = this;
switch (_that) {
case _Comment() when $default != null:
return $default(_that.id,_that.postId,_that.userId,_that.username,_that.userAvatarUrl,_that.text,_that.timestamp,_that.parentId,_that.type,_that.voiceUrl,_that.voiceDuration,_that.waveformData,_that.reactions,_that.reactionCount,_that.reactionCounts,_that.isEdited,_that.editedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Comment implements Comment {
  const _Comment({required this.id, required this.postId, required this.userId, required this.username, required this.userAvatarUrl, required this.text, @TimestampConverter() required this.timestamp, this.parentId, this.type = CommentType.text, this.voiceUrl, this.voiceDuration, this.waveformData, final  Map<String, String>? reactions, this.reactionCount = 0, final  Map<String, int>? reactionCounts, this.isEdited = false, this.editedAt}): _reactions = reactions,_reactionCounts = reactionCounts;
  factory _Comment.fromJson(Map<String, dynamic> json) => _$CommentFromJson(json);

@override final  String id;
@override final  String postId;
@override final  String userId;
@override final  String username;
@override final  String userAvatarUrl;
@override final  String text;
@override@TimestampConverter() final  DateTime timestamp;
@override final  String? parentId;
@override@JsonKey() final  CommentType type;
@override final  String? voiceUrl;
@override final  int? voiceDuration;
// Duration in seconds
@override final  String? waveformData;
// JSON string of waveform data for visualization
 final  Map<String, String>? _reactions;
// JSON string of waveform data for visualization
@override Map<String, String>? get reactions {
  final value = _reactions;
  if (value == null) return null;
  if (_reactions is EqualUnmodifiableMapView) return _reactions;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

// userId -> emoji reactions
@override@JsonKey() final  int reactionCount;
 final  Map<String, int>? _reactionCounts;
@override Map<String, int>? get reactionCounts {
  final value = _reactionCounts;
  if (value == null) return null;
  if (_reactionCounts is EqualUnmodifiableMapView) return _reactionCounts;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

// emoji -> count mapping
@override@JsonKey() final  bool isEdited;
@override final  DateTime? editedAt;

/// Create a copy of Comment
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CommentCopyWith<_Comment> get copyWith => __$CommentCopyWithImpl<_Comment>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CommentToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Comment&&(identical(other.id, id) || other.id == id)&&(identical(other.postId, postId) || other.postId == postId)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.username, username) || other.username == username)&&(identical(other.userAvatarUrl, userAvatarUrl) || other.userAvatarUrl == userAvatarUrl)&&(identical(other.text, text) || other.text == text)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.parentId, parentId) || other.parentId == parentId)&&(identical(other.type, type) || other.type == type)&&(identical(other.voiceUrl, voiceUrl) || other.voiceUrl == voiceUrl)&&(identical(other.voiceDuration, voiceDuration) || other.voiceDuration == voiceDuration)&&(identical(other.waveformData, waveformData) || other.waveformData == waveformData)&&const DeepCollectionEquality().equals(other._reactions, _reactions)&&(identical(other.reactionCount, reactionCount) || other.reactionCount == reactionCount)&&const DeepCollectionEquality().equals(other._reactionCounts, _reactionCounts)&&(identical(other.isEdited, isEdited) || other.isEdited == isEdited)&&(identical(other.editedAt, editedAt) || other.editedAt == editedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,postId,userId,username,userAvatarUrl,text,timestamp,parentId,type,voiceUrl,voiceDuration,waveformData,const DeepCollectionEquality().hash(_reactions),reactionCount,const DeepCollectionEquality().hash(_reactionCounts),isEdited,editedAt);

@override
String toString() {
  return 'Comment(id: $id, postId: $postId, userId: $userId, username: $username, userAvatarUrl: $userAvatarUrl, text: $text, timestamp: $timestamp, parentId: $parentId, type: $type, voiceUrl: $voiceUrl, voiceDuration: $voiceDuration, waveformData: $waveformData, reactions: $reactions, reactionCount: $reactionCount, reactionCounts: $reactionCounts, isEdited: $isEdited, editedAt: $editedAt)';
}


}

/// @nodoc
abstract mixin class _$CommentCopyWith<$Res> implements $CommentCopyWith<$Res> {
  factory _$CommentCopyWith(_Comment value, $Res Function(_Comment) _then) = __$CommentCopyWithImpl;
@override @useResult
$Res call({
 String id, String postId, String userId, String username, String userAvatarUrl, String text,@TimestampConverter() DateTime timestamp, String? parentId, CommentType type, String? voiceUrl, int? voiceDuration, String? waveformData, Map<String, String>? reactions, int reactionCount, Map<String, int>? reactionCounts, bool isEdited, DateTime? editedAt
});




}
/// @nodoc
class __$CommentCopyWithImpl<$Res>
    implements _$CommentCopyWith<$Res> {
  __$CommentCopyWithImpl(this._self, this._then);

  final _Comment _self;
  final $Res Function(_Comment) _then;

/// Create a copy of Comment
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? postId = null,Object? userId = null,Object? username = null,Object? userAvatarUrl = null,Object? text = null,Object? timestamp = null,Object? parentId = freezed,Object? type = null,Object? voiceUrl = freezed,Object? voiceDuration = freezed,Object? waveformData = freezed,Object? reactions = freezed,Object? reactionCount = null,Object? reactionCounts = freezed,Object? isEdited = null,Object? editedAt = freezed,}) {
  return _then(_Comment(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,postId: null == postId ? _self.postId : postId // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,userAvatarUrl: null == userAvatarUrl ? _self.userAvatarUrl : userAvatarUrl // ignore: cast_nullable_to_non_nullable
as String,text: null == text ? _self.text : text // ignore: cast_nullable_to_non_nullable
as String,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,parentId: freezed == parentId ? _self.parentId : parentId // ignore: cast_nullable_to_non_nullable
as String?,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as CommentType,voiceUrl: freezed == voiceUrl ? _self.voiceUrl : voiceUrl // ignore: cast_nullable_to_non_nullable
as String?,voiceDuration: freezed == voiceDuration ? _self.voiceDuration : voiceDuration // ignore: cast_nullable_to_non_nullable
as int?,waveformData: freezed == waveformData ? _self.waveformData : waveformData // ignore: cast_nullable_to_non_nullable
as String?,reactions: freezed == reactions ? _self._reactions : reactions // ignore: cast_nullable_to_non_nullable
as Map<String, String>?,reactionCount: null == reactionCount ? _self.reactionCount : reactionCount // ignore: cast_nullable_to_non_nullable
as int,reactionCounts: freezed == reactionCounts ? _self._reactionCounts : reactionCounts // ignore: cast_nullable_to_non_nullable
as Map<String, int>?,isEdited: null == isEdited ? _self.isEdited : isEdited // ignore: cast_nullable_to_non_nullable
as bool,editedAt: freezed == editedAt ? _self.editedAt : editedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}

// dart format on
