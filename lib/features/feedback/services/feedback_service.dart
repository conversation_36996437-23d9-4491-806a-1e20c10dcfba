import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';

class FeedbackService {
  static final FeedbackService _instance = FeedbackService._internal();
  factory FeedbackService() => _instance;
  FeedbackService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Submit user feedback
  Future<void> submitFeedback({
    required String feedbackType,
    required String message,
    String? category,
    String? severity,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final feedback = {
        'userId': user.uid,
        'userEmail': user.email,
        'feedbackType': feedbackType,
        'message': message,
        'category': category ?? 'general',
        'severity': severity ?? 'medium',
        'additionalData': additionalData ?? {},
        'timestamp': FieldValue.serverTimestamp(),
        'appVersion': '1.0.0',
        'platform': defaultTargetPlatform.name,
        'status': 'pending',
        'reviewed': false,
      };

      await _firestore.collection('feedback').add(feedback);

      debugPrint('Feedback submitted successfully: $feedbackType');
    } catch (e) {
      debugPrint('Error submitting feedback: $e');
      rethrow;
    }
  }

  // Submit bug report
  Future<void> submitBugReport({
    required String title,
    required String description,
    String? steps,
    String? expectedBehavior,
    String? actualBehavior,
    Map<String, dynamic>? deviceInfo,
  }) async {
    await submitFeedback(
      feedbackType: 'bug_report',
      message: description,
      category: 'bug',
      severity: 'high',
      additionalData: {
        'title': title,
        'steps': steps,
        'expected_behavior': expectedBehavior,
        'actual_behavior': actualBehavior,
        'device_info': deviceInfo,
      },
    );
  }

  // Submit feature request
  Future<void> submitFeatureRequest({
    required String title,
    required String description,
    String? useCase,
    String? priority,
  }) async {
    await submitFeedback(
      feedbackType: 'feature_request',
      message: description,
      category: 'feature',
      severity: priority ?? 'medium',
      additionalData: {'title': title, 'use_case': useCase},
    );
  }

  // Submit general feedback
  Future<void> submitGeneralFeedback({
    required String message,
    String? category,
  }) async {
    await submitFeedback(
      feedbackType: 'general_feedback',
      message: message,
      category: category ?? 'general',
      severity: 'low',
    );
  }

  // Get user's feedback history
  Future<List<Map<String, dynamic>>> getUserFeedbackHistory() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final querySnapshot = await _firestore
          .collection('feedback')
          .where('userId', isEqualTo: user.uid)
          .orderBy('timestamp', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => {'id': doc.id, ...doc.data()})
          .toList();
    } catch (e) {
      debugPrint('Error getting feedback history: $e');
      rethrow;
    }
  }

  // Get feedback statistics (admin only)
  Future<Map<String, dynamic>> getFeedbackStats() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Check if user is admin
      final userDoc = await _firestore.collection('users').doc(user.uid).get();
      final userData = userDoc.data();
      if (!userDoc.exists || userData == null || userData['isAdmin'] != true) {
        throw Exception('Admin access required');
      }

      final querySnapshot = await _firestore.collection('feedback').get();
      final feedbacks = querySnapshot.docs.map((doc) => doc.data()).toList();

      final stats = {
        'total': feedbacks.length,
        'by_type': <String, int>{},
        'by_category': <String, int>{},
        'by_severity': <String, int>{},
        'by_status': <String, int>{},
        'recent': 0,
      };

      final now = DateTime.now();
      final oneWeekAgo = now.subtract(const Duration(days: 7));

      for (final feedback in feedbacks) {
        // Count by type
        final type = feedback['feedbackType'] as String? ?? 'unknown';
        final byType = stats['by_type'] as Map<String, int>;
        byType[type] = (byType[type] ?? 0) + 1;

        // Count by category
        final category = feedback['category'] as String? ?? 'unknown';
        final byCategory = stats['by_category'] as Map<String, int>;
        byCategory[category] = (byCategory[category] ?? 0) + 1;

        // Count by severity
        final severity = feedback['severity'] as String? ?? 'unknown';
        final bySeverity = stats['by_severity'] as Map<String, int>;
        bySeverity[severity] = (bySeverity[severity] ?? 0) + 1;

        // Count by status
        final status = feedback['status'] as String? ?? 'unknown';
        final byStatus = stats['by_status'] as Map<String, int>;
        byStatus[status] = (byStatus[status] ?? 0) + 1;

        // Count recent feedback
        final timestamp = feedback['timestamp'] as Timestamp?;
        if (timestamp != null && timestamp.toDate().isAfter(oneWeekAgo)) {
          stats['recent'] = (stats['recent'] as int) + 1;
        }
      }

      return stats;
    } catch (e) {
      debugPrint('Error getting feedback stats: $e');
      rethrow;
    }
  }

  // Update feedback status (admin only)
  Future<void> updateFeedbackStatus({
    required String feedbackId,
    required String status,
    String? adminResponse,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Check if user is admin
      final userDoc = await _firestore.collection('users').doc(user.uid).get();
      final userData = userDoc.data();
      if (!userDoc.exists || userData == null || userData['isAdmin'] != true) {
        throw Exception('Admin access required');
      }

      final updateData = {
        'status': status,
        'reviewed': true,
        'reviewedBy': user.uid,
        'reviewedAt': FieldValue.serverTimestamp(),
      };

      if (adminResponse != null) {
        updateData['adminResponse'] = adminResponse;
      }

      await _firestore
          .collection('feedback')
          .doc(feedbackId)
          .update(updateData);

      debugPrint('Feedback status updated: $feedbackId -> $status');
    } catch (e) {
      debugPrint('Error updating feedback status: $e');
      rethrow;
    }
  }
}
