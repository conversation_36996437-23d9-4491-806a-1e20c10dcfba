import 'dart:math';

import 'package:flutter/material.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/core/services/analytics_service.dart';
import 'package:billionaires_social/core/services/analytics_validator.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:billionaires_social/core/services/performance_service.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/core/services/firebase_service.dart';
import 'package:billionaires_social/core/services/trending_service.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:billionaires_social/features/notifications/services/notification_service.dart';
import 'package:billionaires_social/features/feed/services/feed_service.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/features/stories/services/story_service.dart';
import 'package:billionaires_social/features/profile/services/profile_service.dart';
import 'package:billionaires_social/features/search/services/user_search_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class SlowScreenForTesting extends StatelessWidget {
  const SlowScreenForTesting({super.key});

  // Simulate a computationally expensive build method
  void _performExpensiveCalculation() {
    final random = Random();
    double result = 0;
    for (int i = 0; i < 200000000; i++) {
      result += sin(random.nextDouble());
    }
    debugPrint('Expensive calculation finished with result: $result');
  }

  @override
  Widget build(BuildContext context) {
    _performExpensiveCalculation(); // This will cause jank
    return Scaffold(
      appBar: AppBar(
        title: const Text('Performance Issue Simulation'),
        backgroundColor: AppTheme.luxuryBlack,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [AppTheme.luxuryBlack, AppTheme.luxuryGrey],
          ),
        ),
        child: const Center(
          child: Padding(
            padding: EdgeInsets.all(16.0),
            child: Text(
              'This screen simulates a performance bottleneck by running an expensive calculation in the build method, causing UI jank.',
              style: TextStyle(color: Colors.white, fontSize: 18),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
    );
  }
}

class DebugToolsScreen extends ConsumerStatefulWidget {
  const DebugToolsScreen({super.key});

  @override
  ConsumerState<DebugToolsScreen> createState() => _DebugToolsScreenState();
}

class _DebugToolsScreenState extends ConsumerState<DebugToolsScreen> {
  final AnalyticsService _analyticsService = getIt<AnalyticsService>();
  final PerformanceService _performanceService = getIt<PerformanceService>();
  final FirebaseService _firebaseService = getIt<FirebaseService>();
  final AnalyticsValidator _analyticsValidator = AnalyticsValidator();

  bool _isLoading = false;
  String _lastResult = '';

  @override
  void initState() {
    super.initState();
    // Track screen view
    _analyticsService.logScreenView(
      screenName: 'Debug Tools',
      screenClass: 'DebugToolsScreen',
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Debug Tools'),
        backgroundColor: AppTheme.luxuryBlack,
        foregroundColor: AppTheme.primaryGold,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [AppTheme.luxuryBlack, AppTheme.luxuryGrey],
          ),
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildSection('Analytics Testing', [
                _buildButton(
                  'Test All Analytics Events',
                  _testAllAnalyticsEvents,
                ),
                _buildButton(
                  'Validate Analytics Implementation',
                  _validateAnalytics,
                ),
                _buildButton('Test Billionaire Events', _testBillionaireEvents),
                _buildButton('Test Standard Events', _testStandardEvents),
              ]),
              const SizedBox(height: 24),
              _buildSection('Crash & Performance Testing', [
                _buildButton('Trigger Test Crash', _triggerTestCrash),
                _buildButton(
                  'Simulate Performance Issue',
                  _simulatePerformanceIssue,
                ),
                _buildButton('Test Performance Traces', _testPerformanceTraces),
              ]),
              const SizedBox(height: 24),
              _buildSection('Major Flow Testing', [
                _buildButton('Test VIP Flow', _testVIPFlow),
                _buildButton('Test Investment Flow', _testInvestmentFlow),
                _buildButton('Test Marketplace Flow', _testMarketplaceFlow),
                _buildButton('Test Booking Flow', _testBookingFlow),
                _buildButton('Test Social Flow', _testSocialFlow),
              ]),
              const SizedBox(height: 24),
              _buildSection('System Testing', [
                _buildButton('Test Cache Service', _testCacheService),
                _buildButton('Test Version Control', _testVersionControl),
                _buildButton(
                  'Test Firebase Connection',
                  _testFirebaseConnection,
                ),
                _buildButton(
                  'Trigger iOS Analytics Test',
                  _triggerIOSAnalyticsTest,
                ),
                _buildButton('Test Push Notifications', _testPushNotifications),
                _buildButton('Fix Follower Counts', _fixFollowerCounts),
                _buildButton('Debug User Search', _debugUserSearch),
              ]),
              const SizedBox(height: 24),
              _buildSection('Firestore Rules Testing', [
                _buildButton('Test Firestore Rules', _testFirestoreRules),
                _buildButton('Create Test Post', _createTestPost),
              ]),
              const SizedBox(height: 24),
              _buildSection('Close Friends Testing', [
                _buildButton('Add Test User as Close Friend', _addCloseFriend),
                _buildButton(
                  'Remove Test User from Close Friends',
                  _removeCloseFriend,
                ),
              ]),
              const SizedBox(height: 24),
              _buildSection('Story Border Testing', [
                _buildButton(
                  'Create Test Story (Unseen)',
                  _createTestStoryUnseen,
                ),
                _buildButton('Create Test Story (Seen)', _createTestStorySeen),
                _buildButton(
                  'Test Story Border Colors',
                  _testStoryBorderColors,
                ),
              ]),
              const SizedBox(height: 24),
              _buildSection('Profile Picture Testing', [
                _buildButton(
                  'Test Profile Picture Update',
                  _testProfilePictureUpdate,
                ),
              ]),
              const SizedBox(height: 24),
              _buildSection('Trending System Testing', [
                _buildButton(
                  'Trigger Trending Detection',
                  _triggerTrendingDetection,
                ),
                _buildButton('View Trending Posts', _viewTrendingPosts),
                _buildButton(
                  'View Posts with Trending Scores',
                  _viewPostsWithTrendingScores,
                ),
              ]),
              if (_isLoading)
                const Center(child: CircularProgressIndicator())
              else if (_lastResult.isNotEmpty)
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    _lastResult,
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.primaryGold.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              color: AppTheme.primaryGold,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildButton(String text, VoidCallback onPressed) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 8),
      child: ElevatedButton(
        onPressed: _isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.primaryGold,
          foregroundColor: AppTheme.luxuryBlack,
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
        child: Text(text),
      ),
    );
  }

  // Analytics Testing Methods
  Future<void> _testAllAnalyticsEvents() async {
    setState(() {
      _isLoading = true;
      _lastResult = 'Testing all analytics events...';
    });

    try {
      await _analyticsValidator.validateAnalytics();
      setState(() {
        _lastResult = '✅ All analytics events tested successfully!';
      });
    } catch (e) {
      setState(() {
        _lastResult = '❌ Analytics testing failed: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _validateAnalytics() async {
    setState(() {
      _isLoading = true;
      _lastResult = 'Validating analytics implementation...';
    });

    try {
      // Test parameter validation (now with correct boolean handling)
      await _analyticsService.logFeatureUsage(
        featureName: 'test_validation',
        parameters: {
          'valid_param': 'test_value',
          'numeric_param': 123,
          'boolean_param': true.toString(), // Correctly passing bool as string
        },
      );

      setState(() {
        _lastResult =
            '✅ Analytics validation event sent. Check Firebase DebugView for event "test_validation".';
      });
    } catch (e) {
      setState(() {
        _lastResult = '❌ Analytics validation failed: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testBillionaireEvents() async {
    setState(() {
      _isLoading = true;
      _lastResult = 'Testing billionaire-specific events...';
    });

    try {
      // Test VIP membership
      await _analyticsService.logExclusiveClubMembership(
        userId: 'test_user_123',
        clubName: 'Elite Club',
        membershipFee: 9999.0,
        membershipType: 'diamond',
      );

      // Test luxury travel
      await _analyticsService.logLuxuryTravelBooking(
        userId: 'test_user_123',
        destination: 'Monaco',
        cost: 50000.0,
        travelType: 'private_jet',
      );

      // Test exclusive dining
      await _analyticsService.logExclusiveDiningExperience(
        userId: 'test_user_123',
        restaurantName: 'Le Grand Restaurant',
        cost: 2500.0,
        chefName: 'Chef Gordon',
      );

      setState(() {
        _lastResult = '✅ Billionaire events tested successfully!';
      });
    } catch (e) {
      setState(() {
        _lastResult = '❌ Billionaire events failed: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testStandardEvents() async {
    setState(() {
      _isLoading = true;
      _lastResult = 'Testing standard events...';
    });

    try {
      // Test post events
      await _analyticsService.logPostCreated(
        postId: 'test_post_123',
        contentType: 'image',
        hasMedia: true,
        location: 'Monaco',
      );

      // Test social events
      await _analyticsService.logFollow(
        followerId: 'test_follower_123',
        followedId: 'test_followed_123',
      );

      // Test screen view
      await _analyticsService.logScreenView(
        screenName: 'Debug Tools',
        screenClass: 'DebugToolsScreen',
      );

      setState(() {
        _lastResult = '✅ Standard events tested successfully!';
      });
    } catch (e) {
      setState(() {
        _lastResult = '❌ Standard events failed: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Crash & Performance Testing Methods
  Future<void> _triggerTestCrash() async {
    setState(() {
      _isLoading = true;
      _lastResult = 'Triggering test crash...';
    });

    try {
      // Log a non-fatal error first
      await FirebaseCrashlytics.instance.recordError(
        'Test non-fatal error from debug tools',
        StackTrace.current,
        reason: 'Debug testing',
      );

      // Trigger a fatal crash after a delay
      Future.delayed(const Duration(seconds: 2), () {
        throw Exception('Test fatal crash from debug tools');
      });

      setState(() {
        _lastResult = '✅ Test crash triggered! Check Crashlytics dashboard.';
      });
    } catch (e) {
      setState(() {
        _lastResult = '❌ Crash test failed: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _simulatePerformanceIssue() async {
    setState(() {
      _lastResult = 'Navigating to a slow screen to simulate jank...';
    });
    // This will now navigate to a screen that is intentionally slow to build
    await Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const SlowScreenForTesting()),
    );
    setState(() {
      _lastResult =
          '✅ Navigated back from the slow screen. Check the Performance Monitoring dashboard in Firebase for slow rendering traces.';
    });
  }

  Future<void> _testPerformanceTraces() async {
    setState(() {
      _isLoading = true;
      _lastResult = 'Testing custom performance traces...';
    });

    try {
      // Use the performance service to trace a mock data loading operation
      final trace = _performanceService.startTrace('debug_data_loading_trace');
      await trace.start();
      trace.putAttribute('source', 'debug_tools');

      // Simulate a network request or heavy processing
      await Future.delayed(const Duration(seconds: 2));
      trace.setMetric('items_loaded', 150);

      await trace.stop();

      setState(() {
        _lastResult =
            '✅ Custom performance trace "debug_data_loading_trace" completed. Check the Firebase Performance dashboard.';
      });
    } catch (e) {
      setState(() {
        _lastResult = '❌ Performance traces failed: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Major Flow Testing Methods
  Future<void> _testVIPFlow() async {
    setState(() {
      _isLoading = true;
      _lastResult = 'Testing VIP flow...';
    });

    try {
      // Test VIP membership upgrade
      await _analyticsService.logExclusiveClubMembership(
        userId: 'test_user_123',
        clubName: 'VIP Club',
        membershipFee: 4999.0,
        membershipType: 'platinum',
      );

      // Test exclusive content access
      await _analyticsService.logFeatureUsage(
        featureName: 'vip_content_access',
        parameters: {'content_type': 'exclusive_investment'},
      );

      setState(() {
        _lastResult = '✅ VIP flow tested successfully!';
      });
    } catch (e) {
      setState(() {
        _lastResult = '❌ VIP flow failed: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testInvestmentFlow() async {
    setState(() {
      _isLoading = true;
      _lastResult = 'Testing investment flow...';
    });

    try {
      // Test investment opportunity view
      await _analyticsService.logFeatureUsage(
        featureName: 'investment_opportunity_viewed',
        parameters: {'opportunity_type': 'startup', 'sector': 'technology'},
      );

      // Test investment made
      await _analyticsService.logFeatureUsage(
        featureName: 'investment_made',
        parameters: {'amount': 1000000, 'currency': 'USD'},
      );

      setState(() {
        _lastResult = '✅ Investment flow tested successfully!';
      });
    } catch (e) {
      setState(() {
        _lastResult = '❌ Investment flow failed: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testMarketplaceFlow() async {
    setState(() {
      _isLoading = true;
      _lastResult = 'Testing marketplace flow...';
    });

    try {
      // Test product view
      await _analyticsService.logFeatureUsage(
        featureName: 'product_viewed',
        parameters: {'product_category': 'luxury_watches'},
      );

      // Test product purchase
      await _analyticsService.logFeatureUsage(
        featureName: 'product_purchased',
        parameters: {'product_price': 25000, 'currency': 'USD'},
      );

      setState(() {
        _lastResult = '✅ Marketplace flow tested successfully!';
      });
    } catch (e) {
      setState(() {
        _lastResult = '❌ Marketplace flow failed: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testBookingFlow() async {
    setState(() {
      _isLoading = true;
      _lastResult = 'Testing booking flow...';
    });

    try {
      // Test luxury travel booking
      await _analyticsService.logLuxuryTravelBooking(
        userId: 'test_user_123',
        destination: 'Maldives',
        cost: 75000.0,
        travelType: 'private_jet',
      );

      // Test exclusive dining booking
      await _analyticsService.logExclusiveDiningExperience(
        userId: 'test_user_123',
        restaurantName: 'Le Grand Restaurant',
        cost: 3500.0,
        chefName: 'Chef Gordon',
      );

      setState(() {
        _lastResult = '✅ Booking flow tested successfully!';
      });
    } catch (e) {
      setState(() {
        _lastResult = '❌ Booking flow failed: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testSocialFlow() async {
    setState(() {
      _isLoading = true;
      _lastResult = 'Testing social flow...';
    });

    try {
      // Test post creation
      await _analyticsService.logPostCreated(
        postId: 'test_post_456',
        contentType: 'video',
        hasMedia: true,
        location: 'Dubai',
      );

      // Test story creation
      await _analyticsService.logFeatureUsage(
        featureName: 'story_created',
        parameters: {'story_type': 'video', 'has_filter': true},
      );

      // Test networking connection
      await _analyticsService.logFeatureUsage(
        featureName: 'networking_connection',
        parameters: {'connection_type': 'business_partner'},
      );

      setState(() {
        _lastResult = '✅ Social flow tested successfully!';
      });
    } catch (e) {
      setState(() {
        _lastResult = '❌ Social flow failed: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // System Testing Methods
  Future<void> _testCacheService() async {
    setState(() {
      _isLoading = true;
      _lastResult = 'Testing cache service...';
    });

    try {
      setState(() {
        _lastResult = '✅ Cache service test completed (simplified)!';
      });
    } catch (e) {
      setState(() {
        _lastResult = '❌ Cache service failed: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testVersionControl() async {
    setState(() {
      _isLoading = true;
      _lastResult = 'Testing version control...';
    });

    try {
      setState(() {
        _lastResult = '✅ Version control test completed (simplified)!';
      });
    } catch (e) {
      setState(() {
        _lastResult = '❌ Version control failed: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testFirebaseConnection() async {
    setState(() {
      _isLoading = true;
      _lastResult = 'Testing Firebase connection...';
    });

    try {
      final isConnected = await _firebaseService.isFirebaseConnected();
      if (isConnected) {
        setState(() {
          _lastResult = '✅ Firebase connection successful!';
        });
      } else {
        setState(() {
          _lastResult =
              '❌ Firebase connection failed. Could not connect to Firestore.';
        });
      }
    } catch (e) {
      setState(() {
        _lastResult = '❌ Firebase connection test failed with error: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _triggerIOSAnalyticsTest() async {
    setState(() {
      _isLoading = true;
      _lastResult = 'Triggering iOS analytics test...';
    });

    try {
      // Send multiple test events to verify iOS analytics
      await FirebaseAnalytics.instance.logEvent(name: 'ios_debug_test_1');
      await FirebaseAnalytics.instance.logEvent(
        name: 'ios_debug_test_2',
        parameters: {
          'platform': 'ios',
          'test_type': 'debug',
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      // Test a custom event
      await _analyticsService.logFeatureUsage(
        featureName: 'ios_debug_feature_test',
        parameters: {'device_type': 'ios', 'test_success': 'true'},
      );

      setState(() {
        _lastResult =
            '✅ iOS analytics test completed! Check Firebase DebugView for events: ios_debug_test_1, ios_debug_test_2, ios_debug_feature_test';
      });
    } catch (e) {
      setState(() {
        _lastResult = '❌ iOS analytics test failed: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testPushNotifications() async {
    setState(() {
      _isLoading = true;
      _lastResult = 'Testing push notifications...';
    });

    try {
      final notificationService = getIt<NotificationService>();
      await notificationService.testPushNotificationSetup();

      setState(() {
        _lastResult =
            '✅ Push notifications test completed! Check console for FCM and APNs tokens.';
      });
    } catch (e) {
      setState(() {
        _lastResult = '❌ Push notifications test failed: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Test Firestore rules for post creation
  Future<void> _testFirestoreRules() async {
    try {
      setState(() {
        _isLoading = true;
        _lastResult = 'Testing Firestore rules for post creation...';
      });

      final feedService = FeedService();

      // Test with valid post data
      await feedService.createPost(
        mediaType: MediaType.image,
        mediaUrl: 'https://example.com/test-image.jpg',
        caption:
            'Test post from debug tools - ${DateTime.now().millisecondsSinceEpoch}',
        location: 'Debug Location',
      );

      setState(() {
        _lastResult =
            '✅ Firestore rules test passed! Post created successfully.';
      });
    } catch (e) {
      setState(() {
        _lastResult = '❌ Firestore rules test failed: $e';
      });
      debugPrint('Firestore rules test error: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Create a simple test post for feed testing
  Future<void> _createTestPost() async {
    try {
      setState(() {
        _isLoading = true;
        _lastResult = 'Creating test post...';
      });

      final feedService = FeedService();

      // Create a test post with a sample image
      await feedService.createPost(
        mediaType: MediaType.image,
        mediaUrl:
            'https://picsum.photos/seed/test_${DateTime.now().millisecondsSinceEpoch}/400/400',
        caption:
            '🎉 Test post created from debug tools! #test #debug #billionaires @testuser',
        location: 'Debug Test Location',
        hashtags: ['test', 'debug', 'billionaires'],
        mentionedUsers: ['testuser'],
      );

      setState(() {
        _lastResult =
            '✅ Test post created successfully! Check the feed to see it.';
      });
    } catch (e) {
      setState(() {
        _lastResult = '❌ Error creating test post: $e';
      });
      debugPrint('Test post creation error: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _addCloseFriend() async {
    try {
      setState(() {
        _isLoading = true;
        _lastResult = 'Adding test user as close friend...';
      });

      final storyService = StoryService();
      final testUserId = 'test_user_123'; // You can change this to any user ID

      await storyService.addCloseFriend(testUserId);

      setState(() {
        _lastResult =
            '✅ Test user $testUserId added as close friend! Refresh stories to see green border.';
      });
    } catch (e) {
      setState(() {
        _lastResult = '❌ Error adding close friend: $e';
      });
      debugPrint('Close friend error: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _removeCloseFriend() async {
    try {
      setState(() {
        _isLoading = true;
        _lastResult = 'Removing test user from close friends...';
      });

      final storyService = StoryService();
      final testUserId = 'test_user_123'; // You can change this to any user ID

      await storyService.removeCloseFriend(testUserId);

      setState(() {
        _lastResult =
            '✅ Test user $testUserId removed from close friends! Refresh stories to see normal border.';
      });
    } catch (e) {
      setState(() {
        _lastResult = '❌ Error removing close friend: $e';
      });
      debugPrint('Remove close friend error: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _createTestStoryUnseen() async {
    try {
      setState(() {
        _isLoading = true;
        _lastResult = 'Creating test story (Unseen)...';
      });

      final storyService = StoryService();
      await storyService.createTestStoryWithPrivacy(
        isCloseFriend: false,
        isSeen: false,
        title: 'Test Story (Unseen)',
      );

      setState(() {
        _lastResult = '✅ Test story (Unseen) created successfully!';
      });
    } catch (e) {
      setState(() {
        _lastResult = '❌ Error creating test story (Unseen): $e';
      });
      debugPrint('Story creation error: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _createTestStorySeen() async {
    try {
      setState(() {
        _isLoading = true;
        _lastResult = 'Creating test story (Seen)...';
      });

      final storyService = StoryService();
      await storyService.createTestStoryWithPrivacy(
        isCloseFriend: false,
        isSeen: true,
        title: 'Test Story (Seen)',
      );

      setState(() {
        _lastResult = '✅ Test story (Seen) created successfully!';
      });
    } catch (e) {
      setState(() {
        _lastResult = '❌ Error creating test story (Seen): $e';
      });
      debugPrint('Story creation error: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testProfilePictureUpdate() async {
    setState(() {
      _isLoading = true;
      _lastResult = 'Testing profile picture update...';
    });

    try {
      final profileService = getIt<ProfileService>();
      final currentUserId = profileService.getCurrentUserId();

      if (currentUserId == null) {
        throw Exception('No user is currently signed in');
      }

      // Get current profile
      final currentProfile = await profileService.getCurrentUserProfile();
      debugPrint(
        '🔍 Current profile picture URL: ${currentProfile?.profilePictureUrl}',
      );

      // Test with a sample image URL
      final testImageUrl =
          'https://picsum.photos/seed/${DateTime.now().millisecondsSinceEpoch}/200/200';

      // Update profile with test image
      final updatedProfile = currentProfile!.copyWith(
        profilePictureUrl: testImageUrl,
        updatedAt: DateTime.now(),
      );

      final success = await profileService.updateProfile(updatedProfile);

      if (success) {
        setState(() {
          _lastResult =
              '✅ Profile picture update test completed!\nNew URL: $testImageUrl';
        });
        debugPrint('✅ Test profile picture URL set: $testImageUrl');
      } else {
        throw Exception('Failed to update profile');
      }
    } catch (e) {
      setState(() {
        _lastResult = '❌ Profile picture update test failed: $e';
      });
      debugPrint('❌ Profile picture update test error: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testStoryBorderColors() async {
    try {
      setState(() {
        _isLoading = true;
        _lastResult = 'Creating test stories for border color testing...';
      });

      final storyService = StoryService();
      await storyService.createTestStoriesForUsers();

      setState(() {
        _lastResult =
            '✅ Test stories created! Check stories carousel for:\n'
            '🔴 Red border = Public stories\n'
            '🟢 Green border = Close friends stories\n'
            '⚫ Grey border = Viewed stories';
      });
    } catch (e) {
      setState(() {
        _lastResult = '❌ Error creating test stories: $e';
      });
      debugPrint('Story border test error: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _triggerTrendingDetection() async {
    setState(() {
      _isLoading = true;
      _lastResult = 'Triggering trending detection...';
    });

    try {
      final trendingService = getIt<TrendingService>();
      await trendingService.triggerTrendingDetection();

      setState(() {
        _lastResult = '✅ Trending detection triggered!';
      });
    } catch (e) {
      setState(() {
        _lastResult = '❌ Trending detection failed: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _viewTrendingPosts() async {
    setState(() {
      _isLoading = true;
      _lastResult = 'Viewing trending posts...';
    });

    try {
      final trendingService = getIt<TrendingService>();
      await trendingService.getTrendingPosts();

      setState(() {
        _lastResult = '✅ Trending posts retrieved!';
      });
    } catch (e) {
      setState(() {
        _lastResult = '❌ Error retrieving trending posts: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _viewPostsWithTrendingScores() async {
    setState(() {
      _isLoading = true;
      _lastResult = 'Viewing posts with trending scores...';
    });

    try {
      final trendingService = getIt<TrendingService>();
      await trendingService.getPostsWithTrendingScores();

      setState(() {
        _lastResult = '✅ Posts with trending scores retrieved!';
      });
    } catch (e) {
      setState(() {
        _lastResult = '❌ Error retrieving posts with trending scores: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _fixFollowerCounts() async {
    setState(() {
      _isLoading = true;
      _lastResult = 'Fixing follower/following counts...';
    });

    try {
      final profileService = getIt<ProfileService>();
      final currentUserId = profileService.getCurrentUserId();

      if (currentUserId == null) {
        setState(() {
          _lastResult = '❌ No user logged in';
        });
        return;
      }

      // Recalculate counts for current user
      await profileService.recalculateFollowerCounts(currentUserId);

      setState(() {
        _lastResult =
            '✅ Follower/following counts fixed successfully!\n'
            'Counts have been recalculated from actual relationships.\n'
            'Profile cache has been cleared for fresh data.';
      });
    } catch (e) {
      setState(() {
        _lastResult = '❌ Error fixing follower counts: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _debugUserSearch() async {
    setState(() {
      _isLoading = true;
      _lastResult = 'Debugging user search for "ismailghazy"...';
    });

    try {
      final userSearchService = ref.read(userSearchServiceProvider);

      // Search for the specific user
      final results = await userSearchService.searchUsers(
        query: 'ismailghazy',
        limit: 10,
      );

      // Also try searching for variations (removed unused variable)

      // Check if user exists by querying Firestore directly
      final directQuery = await FirebaseFirestore.instance
          .collection('users')
          .where('username', isEqualTo: 'ismailghazy')
          .get();

      final directQuery2 = await FirebaseFirestore.instance
          .collection('users')
          .where('name', isEqualTo: 'ismailghazy')
          .get();

      // Check for partial matches
      final partialQuery = await FirebaseFirestore.instance
          .collection('users')
          .where('username', isGreaterThanOrEqualTo: 'ismail')
          .where('username', isLessThan: 'ismail\uf8ff')
          .get();

      setState(() {
        _lastResult =
            '''🔍 User Search Debug Results:

📊 Search Service Results:
- Found ${results.length} users for "ismailghazy"
- Results: ${results.map((u) => '${u.name} (@${u.username})').join(', ')}

🎯 Exact Username Match:
- Found ${directQuery.docs.length} users with username "ismailghazy"
- Users: ${directQuery.docs.map((d) => '${d.data()['name']} (@${d.data()['username']})').join(', ')}

📝 Name Match:
- Found ${directQuery2.docs.length} users with name "ismailghazy"
- Users: ${directQuery2.docs.map((d) => '${d.data()['name']} (@${d.data()['username']})').join(', ')}

🔤 Partial Username Match (ismail*):
- Found ${partialQuery.docs.length} users starting with "ismail"
- Users: ${partialQuery.docs.map((d) => '${d.data()['name']} (@${d.data()['username']})').take(5).join(', ')}

💡 Suggestions:
1. Check if username is exactly "ismailghazy"
2. Try searching for display name instead
3. Check if user has searchKeywords field
4. Verify user exists in database''';
      });
    } catch (e) {
      setState(() {
        _lastResult = '❌ Error debugging user search: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
