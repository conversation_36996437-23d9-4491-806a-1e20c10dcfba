import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:billionaires_social/features/marketplace/models/marketplace_models.dart'
    as marketplace_models;

class MarketplaceService {
  static final MarketplaceService _instance = MarketplaceService._internal();
  factory MarketplaceService() => _instance;
  MarketplaceService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Product methods
  Future<List<marketplace_models.MarketplaceItem>> getProducts({
    marketplace_models.MarketplaceFilter? filter,
  }) async {
    try {
      Query query = _firestore.collection('marketplace_products');

      if (filter != null) {
        if (filter.categories.isNotEmpty) {
          query = query.where(
            'category',
            whereIn: filter.categories.map((c) => c.name).toList(),
          );
        }

        if (filter.minPrice != null) {
          query = query.where('price', isGreaterThanOrEqualTo: filter.minPrice);
        }

        if (filter.maxPrice != null) {
          query = query.where('price', isLessThanOrEqualTo: filter.maxPrice);
        }

        if (filter.verifiedOnly) {
          query = query.where('isVerified', isEqualTo: true);
        }

        if (filter.exclusiveOnly) {
          query = query.where('isExclusive', isEqualTo: true);
        }

        if (filter.featuredOnly) {
          query = query.where('isFeatured', isEqualTo: true);
        }

        if (filter.brand != null && filter.brand!.isNotEmpty) {
          query = query.where('brand', isEqualTo: filter.brand);
        }

        if (filter.sellerId != null) {
          query = query.where('sellerId', isEqualTo: filter.sellerId);
        }

        if (filter.condition != null) {
          query = query.where('condition', isEqualTo: filter.condition!.name);
        }

        if (filter.minRating > 0) {
          query = query.where(
            'rating',
            isGreaterThanOrEqualTo: filter.minRating,
          );
        }
      }

      final snapshot = await query.get();
      final products = <marketplace_models.MarketplaceItem>[];

      for (final doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final sellerData = data['seller'] as Map<String, dynamic>;

        final seller = marketplace_models.Seller(
          id: sellerData['id'] as String,
          name: sellerData['name'] as String,
          avatarUrl: sellerData['avatarUrl'] as String,
          description: sellerData['description'] as String?,
          rating: (sellerData['rating'] as num?)?.toDouble() ?? 0.0,
          reviewCount: sellerData['reviewCount'] as int? ?? 0,
          totalSales: sellerData['totalSales'] as int? ?? 0,
          isVerified: sellerData['isVerified'] as bool? ?? false,
          isPremium: sellerData['isPremium'] as bool? ?? false,
          location: sellerData['location'] as String?,
          joinedDate: sellerData['joinedDate'] != null
              ? (sellerData['joinedDate'] as Timestamp).toDate()
              : null,
          specializations: List<String>.from(
            sellerData['specializations'] ?? [],
          ),
        );

        final product = marketplace_models.MarketplaceItem(
          id: doc.id,
          name: data['name'] as String,
          price: (data['price'] as num).toDouble(),
          category: marketplace_models.ProductCategory.values.firstWhere(
            (e) => e.name == data['category'],
            orElse: () => marketplace_models.ProductCategory.watches,
          ),
          images: List<String>.from(data['images'] ?? []),
          sellerId: data['sellerId'] as String,
          seller: seller,
          description: data['description'] as String?,
          isVerified: data['isVerified'] as bool? ?? false,
          isExclusive: data['isExclusive'] as bool? ?? false,
          isFeatured: data['isFeatured'] as bool? ?? false,
          condition: marketplace_models.ProductCondition.values.firstWhere(
            (e) => e.name == (data['condition'] ?? 'brandNew'),
            orElse: () => marketplace_models.ProductCondition.brandNew,
          ),
          availableQuantity: data['availableQuantity'] as int? ?? 1,
          soldQuantity: data['soldQuantity'] as int? ?? 0,
          rating: (data['rating'] as num?)?.toDouble() ?? 0.0,
          reviewCount: data['reviewCount'] as int? ?? 0,
          brand: data['brand'] as String?,
          model: data['model'] as String?,
          year: data['year'] as String?,
          location: data['location'] as String?,
          specifications: Map<String, dynamic>.from(
            data['specifications'] ?? {},
          ),
          tags: List<String>.from(data['tags'] ?? []),
          createdAt: data['createdAt'] != null
              ? (data['createdAt'] as Timestamp).toDate()
              : null,
          updatedAt: data['updatedAt'] != null
              ? (data['updatedAt'] as Timestamp).toDate()
              : null,
        );

        products.add(product);
      }

      // Apply search filter in memory if needed
      if (filter?.searchQuery != null && filter!.searchQuery!.isNotEmpty) {
        final query = filter.searchQuery!.toLowerCase();
        products.removeWhere(
          (item) =>
              !item.name.toLowerCase().contains(query) &&
              !(item.description?.toLowerCase().contains(query) ?? false) &&
              !(item.brand?.toLowerCase().contains(query) ?? false),
        );
      }

      if (filter?.location != null && filter!.location!.isNotEmpty) {
        final location = filter.location!.toLowerCase();
        products.removeWhere(
          (item) => !(item.location?.toLowerCase().contains(location) ?? false),
        );
      }

      return products;
    } catch (e) {
      throw Exception('Failed to fetch products: $e');
    }
  }

  Future<marketplace_models.MarketplaceItem?> getProductById(String id) async {
    try {
      final doc = await _firestore
          .collection('marketplace_products')
          .doc(id)
          .get();
      if (!doc.exists) return null;

      final data = doc.data()!;
      final sellerData = data['seller'] as Map<String, dynamic>;

      final seller = marketplace_models.Seller(
        id: sellerData['id'] as String,
        name: sellerData['name'] as String,
        avatarUrl: sellerData['avatarUrl'] as String,
        description: sellerData['description'] as String?,
        rating: (sellerData['rating'] as num?)?.toDouble() ?? 0.0,
        reviewCount: sellerData['reviewCount'] as int? ?? 0,
        totalSales: sellerData['totalSales'] as int? ?? 0,
        isVerified: sellerData['isVerified'] as bool? ?? false,
        isPremium: sellerData['isPremium'] as bool? ?? false,
        location: sellerData['location'] as String?,
        joinedDate: sellerData['joinedDate'] != null
            ? (sellerData['joinedDate'] as Timestamp).toDate()
            : null,
        specializations: List<String>.from(sellerData['specializations'] ?? []),
      );

      return marketplace_models.MarketplaceItem(
        id: doc.id,
        name: data['name'] as String,
        price: (data['price'] as num).toDouble(),
        category: marketplace_models.ProductCategory.values.firstWhere(
          (e) => e.name == data['category'],
          orElse: () => marketplace_models.ProductCategory.watches,
        ),
        images: List<String>.from(data['images'] ?? []),
        sellerId: data['sellerId'] as String,
        seller: seller,
        description: data['description'] as String?,
        isVerified: data['isVerified'] as bool? ?? false,
        isExclusive: data['isExclusive'] as bool? ?? false,
        isFeatured: data['isFeatured'] as bool? ?? false,
        condition: marketplace_models.ProductCondition.values.firstWhere(
          (e) => e.name == (data['condition'] ?? 'brandNew'),
          orElse: () => marketplace_models.ProductCondition.brandNew,
        ),
        availableQuantity: data['availableQuantity'] as int? ?? 1,
        soldQuantity: data['soldQuantity'] as int? ?? 0,
        rating: (data['rating'] as num?)?.toDouble() ?? 0.0,
        reviewCount: data['reviewCount'] as int? ?? 0,
        brand: data['brand'] as String?,
        model: data['model'] as String?,
        year: data['year'] as String?,
        location: data['location'] as String?,
        specifications: Map<String, dynamic>.from(data['specifications'] ?? {}),
        tags: List<String>.from(data['tags'] ?? []),
        createdAt: data['createdAt'] != null
            ? (data['createdAt'] as Timestamp).toDate()
            : null,
        updatedAt: data['updatedAt'] != null
            ? (data['updatedAt'] as Timestamp).toDate()
            : null,
      );
    } catch (e) {
      throw Exception('Failed to fetch product: $e');
    }
  }

  Future<List<marketplace_models.MarketplaceItem>> getFeaturedProducts() async {
    return getProducts(
      filter: marketplace_models.MarketplaceFilter(featuredOnly: true),
    );
  }

  Future<List<marketplace_models.MarketplaceItem>> getProductsByCategory(
    marketplace_models.ProductCategory category,
  ) async {
    return getProducts(
      filter: marketplace_models.MarketplaceFilter(categories: {category}),
    );
  }

  Future<List<marketplace_models.MarketplaceItem>> getProductsBySeller(
    String sellerId,
  ) async {
    return getProducts(
      filter: marketplace_models.MarketplaceFilter(sellerId: sellerId),
    );
  }

  Future<List<marketplace_models.MarketplaceItem>> searchProducts(
    String query,
  ) async {
    return getProducts(
      filter: marketplace_models.MarketplaceFilter(searchQuery: query),
    );
  }

  // Cart methods
  Future<marketplace_models.Cart> getCart(String userId) async {
    try {
      final doc = await _firestore.collection('carts').doc(userId).get();

      if (!doc.exists) {
        // Create new cart
        final newCart = marketplace_models.Cart(
          id: userId,
          userId: userId,
          items: [],
          subtotal: 0.0,
          tax: 0.0,
          shipping: 0.0,
          total: 0.0,
          lastUpdated: DateTime.now(),
        );

        await _firestore.collection('carts').doc(userId).set(newCart.toJson());
        return newCart;
      }

      final data = doc.data()!;
      final items = <marketplace_models.CartItem>[];

      for (final itemData in data['items'] ?? []) {
        final productData = itemData['item'] as Map<String, dynamic>;
        final sellerData = productData['seller'] as Map<String, dynamic>;

        final seller = marketplace_models.Seller(
          id: sellerData['id'] as String,
          name: sellerData['name'] as String,
          avatarUrl: sellerData['avatarUrl'] as String,
          description: sellerData['description'] as String?,
          rating: (sellerData['rating'] as num?)?.toDouble() ?? 0.0,
          reviewCount: sellerData['reviewCount'] as int? ?? 0,
          totalSales: sellerData['totalSales'] as int? ?? 0,
          isVerified: sellerData['isVerified'] as bool? ?? false,
          isPremium: sellerData['isPremium'] as bool? ?? false,
          location: sellerData['location'] as String?,
          joinedDate: sellerData['joinedDate'] != null
              ? (sellerData['joinedDate'] as Timestamp).toDate()
              : null,
          specializations: List<String>.from(
            sellerData['specializations'] ?? [],
          ),
        );

        final product = marketplace_models.MarketplaceItem(
          id: productData['id'] as String,
          name: productData['name'] as String,
          price: (productData['price'] as num).toDouble(),
          category: marketplace_models.ProductCategory.values.firstWhere(
            (e) => e.name == productData['category'],
            orElse: () => marketplace_models.ProductCategory.watches,
          ),
          images: List<String>.from(productData['images'] ?? []),
          sellerId: productData['sellerId'] as String,
          seller: seller,
          description: productData['description'] as String?,
          isVerified: productData['isVerified'] as bool? ?? false,
          isExclusive: productData['isExclusive'] as bool? ?? false,
          isFeatured: productData['isFeatured'] as bool? ?? false,
          condition: marketplace_models.ProductCondition.values.firstWhere(
            (e) => e.name == (productData['condition'] ?? 'brandNew'),
            orElse: () => marketplace_models.ProductCondition.brandNew,
          ),
          availableQuantity: productData['availableQuantity'] as int? ?? 1,
          soldQuantity: productData['soldQuantity'] as int? ?? 0,
          rating: (productData['rating'] as num?)?.toDouble() ?? 0.0,
          reviewCount: productData['reviewCount'] as int? ?? 0,
          brand: productData['brand'] as String?,
          model: productData['model'] as String?,
          year: productData['year'] as String?,
          location: productData['location'] as String?,
          specifications: Map<String, dynamic>.from(
            productData['specifications'] ?? {},
          ),
          tags: List<String>.from(productData['tags'] ?? []),
          createdAt: productData['createdAt'] != null
              ? (productData['createdAt'] as Timestamp).toDate()
              : null,
          updatedAt: productData['updatedAt'] != null
              ? (productData['updatedAt'] as Timestamp).toDate()
              : null,
        );

        final cartItem = marketplace_models.CartItem(
          id: itemData['id'] as String,
          itemId: itemData['itemId'] as String,
          item: product,
          quantity: itemData['quantity'] as int,
          addedAt: (itemData['addedAt'] as Timestamp).toDate(),
        );

        items.add(cartItem);
      }

      return marketplace_models.Cart(
        id: doc.id,
        userId: data['userId'] as String,
        items: items,
        subtotal: (data['subtotal'] as num).toDouble(),
        tax: (data['tax'] as num).toDouble(),
        shipping: (data['shipping'] as num).toDouble(),
        total: (data['total'] as num).toDouble(),
        lastUpdated: (data['lastUpdated'] as Timestamp).toDate(),
      );
    } catch (e) {
      throw Exception('Failed to fetch cart: $e');
    }
  }

  Future<void> addToCart(String userId, String itemId, int quantity) async {
    try {
      final product = await getProductById(itemId);
      if (product == null) {
        throw Exception('Product not found');
      }

      if (product.availableQuantity < quantity) {
        throw Exception('Insufficient stock available');
      }

      final cart = await getCart(userId);
      final existingItemIndex = cart.items.indexWhere(
        (item) => item.itemId == itemId,
      );

      List<marketplace_models.CartItem> updatedItems;
      if (existingItemIndex != -1) {
        // Update existing item quantity
        updatedItems = cart.items.map((item) {
          if (item.itemId == itemId) {
            return item.copyWith(quantity: item.quantity + quantity);
          }
          return item;
        }).toList();
      } else {
        // Add new item
        final newItem = marketplace_models.CartItem(
          id: '${itemId}_${DateTime.now().millisecondsSinceEpoch}',
          itemId: itemId,
          item: product,
          quantity: quantity,
          addedAt: DateTime.now(),
        );
        updatedItems = [...cart.items, newItem];
      }

      final updatedCart = cart.copyWith(
        items: updatedItems,
        lastUpdated: DateTime.now(),
      );

      await _recalculateCartTotals(updatedCart);
      await _firestore
          .collection('carts')
          .doc(userId)
          .set(updatedCart.toJson());
    } catch (e) {
      throw Exception('Failed to add to cart: $e');
    }
  }

  Future<void> updateCartItemQuantity(
    String userId,
    String itemId,
    int quantity,
  ) async {
    try {
      final cart = await getCart(userId);
      final updatedItems = cart.items.map((item) {
        if (item.itemId == itemId) {
          return item.copyWith(quantity: quantity);
        }
        return item;
      }).toList();

      final updatedCart = cart.copyWith(
        items: updatedItems,
        lastUpdated: DateTime.now(),
      );

      await _recalculateCartTotals(updatedCart);
      await _firestore
          .collection('carts')
          .doc(userId)
          .set(updatedCart.toJson());
    } catch (e) {
      throw Exception('Failed to update cart item: $e');
    }
  }

  Future<void> removeFromCart(String userId, String itemId) async {
    try {
      final cart = await getCart(userId);
      final updatedItems = cart.items
          .where((item) => item.itemId != itemId)
          .toList();

      final updatedCart = cart.copyWith(
        items: updatedItems,
        lastUpdated: DateTime.now(),
      );

      await _recalculateCartTotals(updatedCart);
      await _firestore
          .collection('carts')
          .doc(userId)
          .set(updatedCart.toJson());
    } catch (e) {
      throw Exception('Failed to remove from cart: $e');
    }
  }

  Future<void> clearCart(String userId) async {
    try {
      final emptyCart = marketplace_models.Cart(
        id: userId,
        userId: userId,
        items: [],
        subtotal: 0.0,
        tax: 0.0,
        shipping: 0.0,
        total: 0.0,
        lastUpdated: DateTime.now(),
      );
      await _firestore.collection('carts').doc(userId).set(emptyCart.toJson());
    } catch (e) {
      throw Exception('Failed to clear cart: $e');
    }
  }

  Future<void> _recalculateCartTotals(marketplace_models.Cart cart) async {
    double subtotal = 0.0;

    for (final item in cart.items) {
      subtotal += item.item.price * item.quantity;
    }

    const taxRate = 0.08; // 8% tax
    const shippingRate = 0.02; // 2% shipping

    final tax = subtotal * taxRate;
    final shipping = subtotal * shippingRate;
    final total = subtotal + tax + shipping;

    final updatedCart = cart.copyWith(
      subtotal: subtotal,
      tax: tax,
      shipping: shipping,
      total: total,
      lastUpdated: DateTime.now(),
    );

    await _firestore
        .collection('carts')
        .doc(cart.userId)
        .set(updatedCart.toJson());
  }

  // Order methods
  Future<marketplace_models.Order> createOrder(
    String userId,
    marketplace_models.CheckoutRequest checkoutRequest,
  ) async {
    try {
      final cart = await getCart(userId);
      if (cart.items.isEmpty) {
        throw Exception('Cart is empty');
      }

      // Validate stock availability
      for (final item in cart.items) {
        final product = await getProductById(item.itemId);
        if (product == null) {
          throw Exception('Product ${item.item.name} no longer available');
        }
        if (product.availableQuantity < item.quantity) {
          throw Exception('Insufficient stock for ${item.item.name}');
        }
      }

      final order = marketplace_models.Order(
        id: 'order_${DateTime.now().millisecondsSinceEpoch}',
        userId: userId,
        sellerId: cart
            .items
            .first
            .item
            .sellerId, // Simplified - assumes single seller
        items: List.from(cart.items),
        subtotal: cart.subtotal,
        tax: cart.tax,
        shipping: cart.shipping,
        total: cart.total,
        status: marketplace_models.OrderStatus.pending,
        paymentStatus: marketplace_models.PaymentStatus.pending,
        orderDate: DateTime.now(),
        shippingAddress: checkoutRequest.shippingAddress,
        billingAddress: checkoutRequest.billingAddress,
        notes: checkoutRequest.notes,
      );

      // Save order to Firestore
      await _firestore.collection('orders').doc(order.id).set(order.toJson());

      // Update product quantities
      for (final item in cart.items) {
        final productRef = _firestore
            .collection('marketplace_products')
            .doc(item.itemId);
        await _firestore.runTransaction((transaction) async {
          final productDoc = await transaction.get(productRef);
          if (productDoc.exists) {
            final currentQuantity =
                productDoc.data()!['availableQuantity'] as int;
            final soldQuantity = productDoc.data()!['soldQuantity'] as int;

            transaction.update(productRef, {
              'availableQuantity': currentQuantity - item.quantity,
              'soldQuantity': soldQuantity + item.quantity,
              'updatedAt': FieldValue.serverTimestamp(),
            });
          }
        });
      }

      // Clear cart
      await clearCart(userId);

      return order;
    } catch (e) {
      throw Exception('Failed to create order: $e');
    }
  }

  Future<List<marketplace_models.Order>> getUserOrders(String userId) async {
    try {
      final snapshot = await _firestore
          .collection('orders')
          .where('userId', isEqualTo: userId)
          .orderBy('orderDate', descending: true)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();
        final items = <marketplace_models.CartItem>[];

        for (final itemData in data['items'] ?? []) {
          final productData = itemData['item'] as Map<String, dynamic>;
          final sellerData = productData['seller'] as Map<String, dynamic>;

          final seller = marketplace_models.Seller(
            id: sellerData['id'] as String,
            name: sellerData['name'] as String,
            avatarUrl: sellerData['avatarUrl'] as String,
            description: sellerData['description'] as String?,
            rating: (sellerData['rating'] as num?)?.toDouble() ?? 0.0,
            reviewCount: sellerData['reviewCount'] as int? ?? 0,
            totalSales: sellerData['totalSales'] as int? ?? 0,
            isVerified: sellerData['isVerified'] as bool? ?? false,
            isPremium: sellerData['isPremium'] as bool? ?? false,
            location: sellerData['location'] as String?,
            joinedDate: sellerData['joinedDate'] != null
                ? (sellerData['joinedDate'] as Timestamp).toDate()
                : null,
            specializations: List<String>.from(
              sellerData['specializations'] ?? [],
            ),
          );

          final product = marketplace_models.MarketplaceItem(
            id: productData['id'] as String,
            name: productData['name'] as String,
            price: (productData['price'] as num).toDouble(),
            category: marketplace_models.ProductCategory.values.firstWhere(
              (e) => e.name == productData['category'],
              orElse: () => marketplace_models.ProductCategory.watches,
            ),
            images: List<String>.from(productData['images'] ?? []),
            sellerId: productData['sellerId'] as String,
            seller: seller,
            description: productData['description'] as String?,
            isVerified: productData['isVerified'] as bool? ?? false,
            isExclusive: productData['isExclusive'] as bool? ?? false,
            isFeatured: productData['isFeatured'] as bool? ?? false,
            condition: marketplace_models.ProductCondition.values.firstWhere(
              (e) => e.name == (productData['condition'] ?? 'brandNew'),
              orElse: () => marketplace_models.ProductCondition.brandNew,
            ),
            availableQuantity: productData['availableQuantity'] as int? ?? 1,
            soldQuantity: productData['soldQuantity'] as int? ?? 0,
            rating: (productData['rating'] as num?)?.toDouble() ?? 0.0,
            reviewCount: productData['reviewCount'] as int? ?? 0,
            brand: productData['brand'] as String?,
            model: productData['model'] as String?,
            year: productData['year'] as String?,
            location: productData['location'] as String?,
            specifications: Map<String, dynamic>.from(
              productData['specifications'] ?? {},
            ),
            tags: List<String>.from(productData['tags'] ?? []),
            createdAt: productData['createdAt'] != null
                ? (productData['createdAt'] as Timestamp).toDate()
                : null,
            updatedAt: productData['updatedAt'] != null
                ? (productData['updatedAt'] as Timestamp).toDate()
                : null,
          );

          final cartItem = marketplace_models.CartItem(
            id: itemData['id'] as String,
            itemId: itemData['itemId'] as String,
            item: product,
            quantity: itemData['quantity'] as int,
            addedAt: (itemData['addedAt'] as Timestamp).toDate(),
          );

          items.add(cartItem);
        }

        return marketplace_models.Order(
          id: doc.id,
          userId: data['userId'] as String,
          sellerId: data['sellerId'] as String,
          items: items,
          subtotal: (data['subtotal'] as num).toDouble(),
          tax: (data['tax'] as num).toDouble(),
          shipping: (data['shipping'] as num).toDouble(),
          total: (data['total'] as num).toDouble(),
          status: marketplace_models.OrderStatus.values.firstWhere(
            (e) => e.name == data['status'],
            orElse: () => marketplace_models.OrderStatus.pending,
          ),
          paymentStatus: marketplace_models.PaymentStatus.values.firstWhere(
            (e) => e.name == data['paymentStatus'],
            orElse: () => marketplace_models.PaymentStatus.pending,
          ),
          orderDate: (data['orderDate'] as Timestamp).toDate(),
          shippedDate: data['shippedDate'] != null
              ? (data['shippedDate'] as Timestamp).toDate()
              : null,
          deliveredDate: data['deliveredDate'] != null
              ? (data['deliveredDate'] as Timestamp).toDate()
              : null,
          trackingNumber: data['trackingNumber'] as String?,
          shippingAddress: data['shippingAddress'] as String?,
          billingAddress: data['billingAddress'] as String?,
          notes: data['notes'] as String?,
        );
      }).toList();
    } catch (e) {
      throw Exception('Failed to fetch user orders: $e');
    }
  }

  Future<marketplace_models.Order?> getOrderById(String orderId) async {
    try {
      final doc = await _firestore.collection('orders').doc(orderId).get();
      if (!doc.exists) return null;

      final data = doc.data()!;
      final items = <marketplace_models.CartItem>[];

      for (final itemData in data['items'] ?? []) {
        final productData = itemData['item'] as Map<String, dynamic>;
        final sellerData = productData['seller'] as Map<String, dynamic>;

        final seller = marketplace_models.Seller(
          id: sellerData['id'] as String,
          name: sellerData['name'] as String,
          avatarUrl: sellerData['avatarUrl'] as String,
          description: sellerData['description'] as String?,
          rating: (sellerData['rating'] as num?)?.toDouble() ?? 0.0,
          reviewCount: sellerData['reviewCount'] as int? ?? 0,
          totalSales: sellerData['totalSales'] as int? ?? 0,
          isVerified: sellerData['isVerified'] as bool? ?? false,
          isPremium: sellerData['isPremium'] as bool? ?? false,
          location: sellerData['location'] as String?,
          joinedDate: sellerData['joinedDate'] != null
              ? (sellerData['joinedDate'] as Timestamp).toDate()
              : null,
          specializations: List<String>.from(
            sellerData['specializations'] ?? [],
          ),
        );

        final product = marketplace_models.MarketplaceItem(
          id: productData['id'] as String,
          name: productData['name'] as String,
          price: (productData['price'] as num).toDouble(),
          category: marketplace_models.ProductCategory.values.firstWhere(
            (e) => e.name == productData['category'],
            orElse: () => marketplace_models.ProductCategory.watches,
          ),
          images: List<String>.from(productData['images'] ?? []),
          sellerId: productData['sellerId'] as String,
          seller: seller,
          description: productData['description'] as String?,
          isVerified: productData['isVerified'] as bool? ?? false,
          isExclusive: productData['isExclusive'] as bool? ?? false,
          isFeatured: productData['isFeatured'] as bool? ?? false,
          condition: marketplace_models.ProductCondition.values.firstWhere(
            (e) => e.name == (productData['condition'] ?? 'brandNew'),
            orElse: () => marketplace_models.ProductCondition.brandNew,
          ),
          availableQuantity: productData['availableQuantity'] as int? ?? 1,
          soldQuantity: productData['soldQuantity'] as int? ?? 0,
          rating: (productData['rating'] as num?)?.toDouble() ?? 0.0,
          reviewCount: productData['reviewCount'] as int? ?? 0,
          brand: productData['brand'] as String?,
          model: productData['model'] as String?,
          year: productData['year'] as String?,
          location: productData['location'] as String?,
          specifications: Map<String, dynamic>.from(
            productData['specifications'] ?? {},
          ),
          tags: List<String>.from(productData['tags'] ?? []),
          createdAt: productData['createdAt'] != null
              ? (productData['createdAt'] as Timestamp).toDate()
              : null,
          updatedAt: productData['updatedAt'] != null
              ? (productData['updatedAt'] as Timestamp).toDate()
              : null,
        );

        final cartItem = marketplace_models.CartItem(
          id: itemData['id'] as String,
          itemId: itemData['itemId'] as String,
          item: product,
          quantity: itemData['quantity'] as int,
          addedAt: (itemData['addedAt'] as Timestamp).toDate(),
        );

        items.add(cartItem);
      }

      return marketplace_models.Order(
        id: doc.id,
        userId: data['userId'] as String,
        sellerId: data['sellerId'] as String,
        items: items,
        subtotal: (data['subtotal'] as num).toDouble(),
        tax: (data['tax'] as num).toDouble(),
        shipping: (data['shipping'] as num).toDouble(),
        total: (data['total'] as num).toDouble(),
        status: marketplace_models.OrderStatus.values.firstWhere(
          (e) => e.name == data['status'],
          orElse: () => marketplace_models.OrderStatus.pending,
        ),
        paymentStatus: marketplace_models.PaymentStatus.values.firstWhere(
          (e) => e.name == data['paymentStatus'],
          orElse: () => marketplace_models.PaymentStatus.pending,
        ),
        orderDate: (data['orderDate'] as Timestamp).toDate(),
        shippedDate: data['shippedDate'] != null
            ? (data['shippedDate'] as Timestamp).toDate()
            : null,
        deliveredDate: data['deliveredDate'] != null
            ? (data['deliveredDate'] as Timestamp).toDate()
            : null,
        trackingNumber: data['trackingNumber'] as String?,
        shippingAddress: data['shippingAddress'] as String?,
        billingAddress: data['billingAddress'] as String?,
        notes: data['notes'] as String?,
      );
    } catch (e) {
      throw Exception('Failed to fetch order: $e');
    }
  }

  Future<void> updateOrderStatus(
    String orderId,
    marketplace_models.OrderStatus status,
  ) async {
    try {
      await _firestore.collection('orders').doc(orderId).update({
        'status': status.name,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('Failed to update order status: $e');
    }
  }

  // Seller methods
  Future<List<marketplace_models.Seller>> getAllSellers() async {
    try {
      final snapshot = await _firestore.collection('sellers').get();
      return snapshot.docs.map((doc) {
        final data = doc.data();
        return marketplace_models.Seller(
          id: doc.id,
          name: data['name'] as String,
          avatarUrl: data['avatarUrl'] as String,
          description: data['description'] as String?,
          rating: (data['rating'] as num?)?.toDouble() ?? 0.0,
          reviewCount: data['reviewCount'] as int? ?? 0,
          totalSales: data['totalSales'] as int? ?? 0,
          isVerified: data['isVerified'] as bool? ?? false,
          isPremium: data['isPremium'] as bool? ?? false,
          location: data['location'] as String?,
          joinedDate: data['joinedDate'] != null
              ? (data['joinedDate'] as Timestamp).toDate()
              : null,
          specializations: List<String>.from(data['specializations'] ?? []),
        );
      }).toList();
    } catch (e) {
      throw Exception('Failed to fetch sellers: $e');
    }
  }

  Future<marketplace_models.Seller?> getSellerById(String sellerId) async {
    try {
      final doc = await _firestore.collection('sellers').doc(sellerId).get();
      if (!doc.exists) return null;

      final data = doc.data()!;
      return marketplace_models.Seller(
        id: doc.id,
        name: data['name'] as String,
        avatarUrl: data['avatarUrl'] as String,
        description: data['description'] as String?,
        rating: (data['rating'] as num?)?.toDouble() ?? 0.0,
        reviewCount: data['reviewCount'] as int? ?? 0,
        totalSales: data['totalSales'] as int? ?? 0,
        isVerified: data['isVerified'] as bool? ?? false,
        isPremium: data['isPremium'] as bool? ?? false,
        location: data['location'] as String?,
        joinedDate: data['joinedDate'] != null
            ? (data['joinedDate'] as Timestamp).toDate()
            : null,
        specializations: List<String>.from(data['specializations'] ?? []),
      );
    } catch (e) {
      throw Exception('Failed to fetch seller: $e');
    }
  }

  // Utility methods
  Future<List<String>> getBrands() async {
    try {
      final snapshot = await _firestore
          .collection('marketplace_products')
          .where('brand', isNotEqualTo: null)
          .get();

      final brands = snapshot.docs
          .map((doc) => doc.data()['brand'] as String)
          .where((brand) => brand.isNotEmpty)
          .toSet()
          .toList();

      brands.sort();
      return brands;
    } catch (e) {
      throw Exception('Failed to fetch brands: $e');
    }
  }

  Future<List<String>> getLocations() async {
    try {
      final snapshot = await _firestore
          .collection('marketplace_products')
          .where('location', isNotEqualTo: null)
          .get();

      final locations = snapshot.docs
          .map((doc) => doc.data()['location'] as String)
          .where((location) => location.isNotEmpty)
          .toSet()
          .toList();

      locations.sort();
      return locations;
    } catch (e) {
      throw Exception('Failed to fetch locations: $e');
    }
  }
}
