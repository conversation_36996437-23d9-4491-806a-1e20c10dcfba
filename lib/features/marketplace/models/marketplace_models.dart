import 'package:freezed_annotation/freezed_annotation.dart';

part 'marketplace_models.freezed.dart';
part 'marketplace_models.g.dart';

enum ProductCategory {
  watches,
  art,
  realEstate,
  vehicles,
  nfts,
  collectibles,
  designerItems,
  jewelry,
  wine,
  yachts,
  privateJets,
  luxuryCars,
  antiques,
  rareBooks,
  sportsMemorabilia,
}

enum ProductCondition {
  brandNew,
  likeNew,
  excellent,
  good,
  fair,
  vintage,
  antique,
}

enum OrderStatus {
  pending,
  confirmed,
  processing,
  shipped,
  delivered,
  cancelled,
  refunded,
}

enum PaymentStatus { pending, paid, failed, refunded }

@freezed
abstract class Seller with _$Seller {
  const factory Seller({
    required String id,
    required String name,
    required String avatarUrl,
    String? description,
    @Default(0.0) double rating,
    @Default(0) int reviewCount,
    @Default(0) int totalSales,
    @Default(false) bool isVerified,
    @Default(false) bool isPremium,
    String? location,
    DateTime? joinedDate,
    @Default([]) List<String> specializations,
  }) = _Seller;

  factory Seller.fromJson(Map<String, dynamic> json) => _$SellerFromJson(json);
}

@freezed
abstract class MarketplaceItem with _$MarketplaceItem {
  const factory MarketplaceItem({
    required String id,
    required String name,
    required double price,
    required ProductCategory category,
    required List<String> images,
    required String sellerId,
    required Seller seller,
    String? description,
    @Default(false) bool isVerified,
    @Default(false) bool isExclusive,
    @Default(false) bool isFeatured,
    @Default(ProductCondition.brandNew) ProductCondition condition,
    @Default(1) int availableQuantity,
    @Default(0) int soldQuantity,
    @Default(0.0) double rating,
    @Default(0) int reviewCount,
    String? brand,
    String? model,
    String? year,
    String? location,
    Map<String, dynamic>? specifications,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _MarketplaceItem;

  factory MarketplaceItem.fromJson(Map<String, dynamic> json) =>
      _$MarketplaceItemFromJson(json);
}

@freezed
abstract class CartItem with _$CartItem {
  const factory CartItem({
    required String id,
    required String itemId,
    required MarketplaceItem item,
    required int quantity,
    required DateTime addedAt,
  }) = _CartItem;

  factory CartItem.fromJson(Map<String, dynamic> json) =>
      _$CartItemFromJson(json);
}

@freezed
abstract class Cart with _$Cart {
  const factory Cart({
    required String id,
    required String userId,
    @Default([]) List<CartItem> items,
    @Default(0.0) double subtotal,
    @Default(0.0) double tax,
    @Default(0.0) double shipping,
    @Default(0.0) double total,
    DateTime? lastUpdated,
  }) = _Cart;

  factory Cart.fromJson(Map<String, dynamic> json) => _$CartFromJson(json);
}

@freezed
abstract class Order with _$Order {
  const factory Order({
    required String id,
    required String userId,
    required String sellerId,
    required List<CartItem> items,
    required double subtotal,
    required double tax,
    required double shipping,
    required double total,
    required OrderStatus status,
    required PaymentStatus paymentStatus,
    required DateTime orderDate,
    DateTime? shippedDate,
    DateTime? deliveredDate,
    String? trackingNumber,
    String? shippingAddress,
    String? billingAddress,
    String? notes,
  }) = _Order;

  factory Order.fromJson(Map<String, dynamic> json) => _$OrderFromJson(json);
}

@freezed
abstract class CheckoutRequest with _$CheckoutRequest {
  const factory CheckoutRequest({
    required String cartId,
    required String shippingAddress,
    required String billingAddress,
    String? notes,
    String? promoCode,
  }) = _CheckoutRequest;

  factory CheckoutRequest.fromJson(Map<String, dynamic> json) =>
      _$CheckoutRequestFromJson(json);
}

@freezed
abstract class MarketplaceFilter with _$MarketplaceFilter {
  const factory MarketplaceFilter({
    @Default({}) Set<ProductCategory> categories,
    double? minPrice,
    double? maxPrice,
    @Default(false) bool verifiedOnly,
    @Default(false) bool exclusiveOnly,
    @Default(false) bool featuredOnly,
    String? searchQuery,
    String? brand,
    String? sellerId,
    ProductCondition? condition,
    @Default(0.0) double minRating,
    String? location,
  }) = _MarketplaceFilter;

  factory MarketplaceFilter.fromJson(Map<String, dynamic> json) =>
      _$MarketplaceFilterFromJson(json);
}

@freezed
abstract class ProductReview with _$ProductReview {
  const factory ProductReview({
    required String id,
    required String userId,
    required String userName,
    required String userAvatarUrl,
    required String productId,
    required double rating,
    required String comment,
    required DateTime reviewDate,
    List<String>? images,
  }) = _ProductReview;

  factory ProductReview.fromJson(Map<String, dynamic> json) =>
      _$ProductReviewFromJson(json);
}
