// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'marketplace_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Seller {

 String get id; String get name; String get avatarUrl; String? get description; double get rating; int get reviewCount; int get totalSales; bool get isVerified; bool get isPremium; String? get location; DateTime? get joinedDate; List<String> get specializations;
/// Create a copy of Seller
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SellerCopyWith<Seller> get copyWith => _$SellerCopyWithImpl<Seller>(this as Seller, _$identity);

  /// Serializes this Seller to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Seller&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.avatarUrl, avatarUrl) || other.avatarUrl == avatarUrl)&&(identical(other.description, description) || other.description == description)&&(identical(other.rating, rating) || other.rating == rating)&&(identical(other.reviewCount, reviewCount) || other.reviewCount == reviewCount)&&(identical(other.totalSales, totalSales) || other.totalSales == totalSales)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.isPremium, isPremium) || other.isPremium == isPremium)&&(identical(other.location, location) || other.location == location)&&(identical(other.joinedDate, joinedDate) || other.joinedDate == joinedDate)&&const DeepCollectionEquality().equals(other.specializations, specializations));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,avatarUrl,description,rating,reviewCount,totalSales,isVerified,isPremium,location,joinedDate,const DeepCollectionEquality().hash(specializations));

@override
String toString() {
  return 'Seller(id: $id, name: $name, avatarUrl: $avatarUrl, description: $description, rating: $rating, reviewCount: $reviewCount, totalSales: $totalSales, isVerified: $isVerified, isPremium: $isPremium, location: $location, joinedDate: $joinedDate, specializations: $specializations)';
}


}

/// @nodoc
abstract mixin class $SellerCopyWith<$Res>  {
  factory $SellerCopyWith(Seller value, $Res Function(Seller) _then) = _$SellerCopyWithImpl;
@useResult
$Res call({
 String id, String name, String avatarUrl, String? description, double rating, int reviewCount, int totalSales, bool isVerified, bool isPremium, String? location, DateTime? joinedDate, List<String> specializations
});




}
/// @nodoc
class _$SellerCopyWithImpl<$Res>
    implements $SellerCopyWith<$Res> {
  _$SellerCopyWithImpl(this._self, this._then);

  final Seller _self;
  final $Res Function(Seller) _then;

/// Create a copy of Seller
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? avatarUrl = null,Object? description = freezed,Object? rating = null,Object? reviewCount = null,Object? totalSales = null,Object? isVerified = null,Object? isPremium = null,Object? location = freezed,Object? joinedDate = freezed,Object? specializations = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,avatarUrl: null == avatarUrl ? _self.avatarUrl : avatarUrl // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,rating: null == rating ? _self.rating : rating // ignore: cast_nullable_to_non_nullable
as double,reviewCount: null == reviewCount ? _self.reviewCount : reviewCount // ignore: cast_nullable_to_non_nullable
as int,totalSales: null == totalSales ? _self.totalSales : totalSales // ignore: cast_nullable_to_non_nullable
as int,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,isPremium: null == isPremium ? _self.isPremium : isPremium // ignore: cast_nullable_to_non_nullable
as bool,location: freezed == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String?,joinedDate: freezed == joinedDate ? _self.joinedDate : joinedDate // ignore: cast_nullable_to_non_nullable
as DateTime?,specializations: null == specializations ? _self.specializations : specializations // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}

}


/// Adds pattern-matching-related methods to [Seller].
extension SellerPatterns on Seller {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Seller value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Seller() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Seller value)  $default,){
final _that = this;
switch (_that) {
case _Seller():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Seller value)?  $default,){
final _that = this;
switch (_that) {
case _Seller() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  String avatarUrl,  String? description,  double rating,  int reviewCount,  int totalSales,  bool isVerified,  bool isPremium,  String? location,  DateTime? joinedDate,  List<String> specializations)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Seller() when $default != null:
return $default(_that.id,_that.name,_that.avatarUrl,_that.description,_that.rating,_that.reviewCount,_that.totalSales,_that.isVerified,_that.isPremium,_that.location,_that.joinedDate,_that.specializations);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  String avatarUrl,  String? description,  double rating,  int reviewCount,  int totalSales,  bool isVerified,  bool isPremium,  String? location,  DateTime? joinedDate,  List<String> specializations)  $default,) {final _that = this;
switch (_that) {
case _Seller():
return $default(_that.id,_that.name,_that.avatarUrl,_that.description,_that.rating,_that.reviewCount,_that.totalSales,_that.isVerified,_that.isPremium,_that.location,_that.joinedDate,_that.specializations);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  String avatarUrl,  String? description,  double rating,  int reviewCount,  int totalSales,  bool isVerified,  bool isPremium,  String? location,  DateTime? joinedDate,  List<String> specializations)?  $default,) {final _that = this;
switch (_that) {
case _Seller() when $default != null:
return $default(_that.id,_that.name,_that.avatarUrl,_that.description,_that.rating,_that.reviewCount,_that.totalSales,_that.isVerified,_that.isPremium,_that.location,_that.joinedDate,_that.specializations);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Seller implements Seller {
  const _Seller({required this.id, required this.name, required this.avatarUrl, this.description, this.rating = 0.0, this.reviewCount = 0, this.totalSales = 0, this.isVerified = false, this.isPremium = false, this.location, this.joinedDate, final  List<String> specializations = const []}): _specializations = specializations;
  factory _Seller.fromJson(Map<String, dynamic> json) => _$SellerFromJson(json);

@override final  String id;
@override final  String name;
@override final  String avatarUrl;
@override final  String? description;
@override@JsonKey() final  double rating;
@override@JsonKey() final  int reviewCount;
@override@JsonKey() final  int totalSales;
@override@JsonKey() final  bool isVerified;
@override@JsonKey() final  bool isPremium;
@override final  String? location;
@override final  DateTime? joinedDate;
 final  List<String> _specializations;
@override@JsonKey() List<String> get specializations {
  if (_specializations is EqualUnmodifiableListView) return _specializations;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_specializations);
}


/// Create a copy of Seller
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SellerCopyWith<_Seller> get copyWith => __$SellerCopyWithImpl<_Seller>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SellerToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Seller&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.avatarUrl, avatarUrl) || other.avatarUrl == avatarUrl)&&(identical(other.description, description) || other.description == description)&&(identical(other.rating, rating) || other.rating == rating)&&(identical(other.reviewCount, reviewCount) || other.reviewCount == reviewCount)&&(identical(other.totalSales, totalSales) || other.totalSales == totalSales)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.isPremium, isPremium) || other.isPremium == isPremium)&&(identical(other.location, location) || other.location == location)&&(identical(other.joinedDate, joinedDate) || other.joinedDate == joinedDate)&&const DeepCollectionEquality().equals(other._specializations, _specializations));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,avatarUrl,description,rating,reviewCount,totalSales,isVerified,isPremium,location,joinedDate,const DeepCollectionEquality().hash(_specializations));

@override
String toString() {
  return 'Seller(id: $id, name: $name, avatarUrl: $avatarUrl, description: $description, rating: $rating, reviewCount: $reviewCount, totalSales: $totalSales, isVerified: $isVerified, isPremium: $isPremium, location: $location, joinedDate: $joinedDate, specializations: $specializations)';
}


}

/// @nodoc
abstract mixin class _$SellerCopyWith<$Res> implements $SellerCopyWith<$Res> {
  factory _$SellerCopyWith(_Seller value, $Res Function(_Seller) _then) = __$SellerCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, String avatarUrl, String? description, double rating, int reviewCount, int totalSales, bool isVerified, bool isPremium, String? location, DateTime? joinedDate, List<String> specializations
});




}
/// @nodoc
class __$SellerCopyWithImpl<$Res>
    implements _$SellerCopyWith<$Res> {
  __$SellerCopyWithImpl(this._self, this._then);

  final _Seller _self;
  final $Res Function(_Seller) _then;

/// Create a copy of Seller
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? avatarUrl = null,Object? description = freezed,Object? rating = null,Object? reviewCount = null,Object? totalSales = null,Object? isVerified = null,Object? isPremium = null,Object? location = freezed,Object? joinedDate = freezed,Object? specializations = null,}) {
  return _then(_Seller(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,avatarUrl: null == avatarUrl ? _self.avatarUrl : avatarUrl // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,rating: null == rating ? _self.rating : rating // ignore: cast_nullable_to_non_nullable
as double,reviewCount: null == reviewCount ? _self.reviewCount : reviewCount // ignore: cast_nullable_to_non_nullable
as int,totalSales: null == totalSales ? _self.totalSales : totalSales // ignore: cast_nullable_to_non_nullable
as int,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,isPremium: null == isPremium ? _self.isPremium : isPremium // ignore: cast_nullable_to_non_nullable
as bool,location: freezed == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String?,joinedDate: freezed == joinedDate ? _self.joinedDate : joinedDate // ignore: cast_nullable_to_non_nullable
as DateTime?,specializations: null == specializations ? _self._specializations : specializations // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}


}


/// @nodoc
mixin _$MarketplaceItem {

 String get id; String get name; double get price; ProductCategory get category; List<String> get images; String get sellerId; Seller get seller; String? get description; bool get isVerified; bool get isExclusive; bool get isFeatured; ProductCondition get condition; int get availableQuantity; int get soldQuantity; double get rating; int get reviewCount; String? get brand; String? get model; String? get year; String? get location; Map<String, dynamic>? get specifications; List<String>? get tags; DateTime? get createdAt; DateTime? get updatedAt;
/// Create a copy of MarketplaceItem
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MarketplaceItemCopyWith<MarketplaceItem> get copyWith => _$MarketplaceItemCopyWithImpl<MarketplaceItem>(this as MarketplaceItem, _$identity);

  /// Serializes this MarketplaceItem to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MarketplaceItem&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.price, price) || other.price == price)&&(identical(other.category, category) || other.category == category)&&const DeepCollectionEquality().equals(other.images, images)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.seller, seller) || other.seller == seller)&&(identical(other.description, description) || other.description == description)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.isExclusive, isExclusive) || other.isExclusive == isExclusive)&&(identical(other.isFeatured, isFeatured) || other.isFeatured == isFeatured)&&(identical(other.condition, condition) || other.condition == condition)&&(identical(other.availableQuantity, availableQuantity) || other.availableQuantity == availableQuantity)&&(identical(other.soldQuantity, soldQuantity) || other.soldQuantity == soldQuantity)&&(identical(other.rating, rating) || other.rating == rating)&&(identical(other.reviewCount, reviewCount) || other.reviewCount == reviewCount)&&(identical(other.brand, brand) || other.brand == brand)&&(identical(other.model, model) || other.model == model)&&(identical(other.year, year) || other.year == year)&&(identical(other.location, location) || other.location == location)&&const DeepCollectionEquality().equals(other.specifications, specifications)&&const DeepCollectionEquality().equals(other.tags, tags)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,name,price,category,const DeepCollectionEquality().hash(images),sellerId,seller,description,isVerified,isExclusive,isFeatured,condition,availableQuantity,soldQuantity,rating,reviewCount,brand,model,year,location,const DeepCollectionEquality().hash(specifications),const DeepCollectionEquality().hash(tags),createdAt,updatedAt]);

@override
String toString() {
  return 'MarketplaceItem(id: $id, name: $name, price: $price, category: $category, images: $images, sellerId: $sellerId, seller: $seller, description: $description, isVerified: $isVerified, isExclusive: $isExclusive, isFeatured: $isFeatured, condition: $condition, availableQuantity: $availableQuantity, soldQuantity: $soldQuantity, rating: $rating, reviewCount: $reviewCount, brand: $brand, model: $model, year: $year, location: $location, specifications: $specifications, tags: $tags, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $MarketplaceItemCopyWith<$Res>  {
  factory $MarketplaceItemCopyWith(MarketplaceItem value, $Res Function(MarketplaceItem) _then) = _$MarketplaceItemCopyWithImpl;
@useResult
$Res call({
 String id, String name, double price, ProductCategory category, List<String> images, String sellerId, Seller seller, String? description, bool isVerified, bool isExclusive, bool isFeatured, ProductCondition condition, int availableQuantity, int soldQuantity, double rating, int reviewCount, String? brand, String? model, String? year, String? location, Map<String, dynamic>? specifications, List<String>? tags, DateTime? createdAt, DateTime? updatedAt
});


$SellerCopyWith<$Res> get seller;

}
/// @nodoc
class _$MarketplaceItemCopyWithImpl<$Res>
    implements $MarketplaceItemCopyWith<$Res> {
  _$MarketplaceItemCopyWithImpl(this._self, this._then);

  final MarketplaceItem _self;
  final $Res Function(MarketplaceItem) _then;

/// Create a copy of MarketplaceItem
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? price = null,Object? category = null,Object? images = null,Object? sellerId = null,Object? seller = null,Object? description = freezed,Object? isVerified = null,Object? isExclusive = null,Object? isFeatured = null,Object? condition = null,Object? availableQuantity = null,Object? soldQuantity = null,Object? rating = null,Object? reviewCount = null,Object? brand = freezed,Object? model = freezed,Object? year = freezed,Object? location = freezed,Object? specifications = freezed,Object? tags = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,category: null == category ? _self.category : category // ignore: cast_nullable_to_non_nullable
as ProductCategory,images: null == images ? _self.images : images // ignore: cast_nullable_to_non_nullable
as List<String>,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,seller: null == seller ? _self.seller : seller // ignore: cast_nullable_to_non_nullable
as Seller,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,isExclusive: null == isExclusive ? _self.isExclusive : isExclusive // ignore: cast_nullable_to_non_nullable
as bool,isFeatured: null == isFeatured ? _self.isFeatured : isFeatured // ignore: cast_nullable_to_non_nullable
as bool,condition: null == condition ? _self.condition : condition // ignore: cast_nullable_to_non_nullable
as ProductCondition,availableQuantity: null == availableQuantity ? _self.availableQuantity : availableQuantity // ignore: cast_nullable_to_non_nullable
as int,soldQuantity: null == soldQuantity ? _self.soldQuantity : soldQuantity // ignore: cast_nullable_to_non_nullable
as int,rating: null == rating ? _self.rating : rating // ignore: cast_nullable_to_non_nullable
as double,reviewCount: null == reviewCount ? _self.reviewCount : reviewCount // ignore: cast_nullable_to_non_nullable
as int,brand: freezed == brand ? _self.brand : brand // ignore: cast_nullable_to_non_nullable
as String?,model: freezed == model ? _self.model : model // ignore: cast_nullable_to_non_nullable
as String?,year: freezed == year ? _self.year : year // ignore: cast_nullable_to_non_nullable
as String?,location: freezed == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String?,specifications: freezed == specifications ? _self.specifications : specifications // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,tags: freezed == tags ? _self.tags : tags // ignore: cast_nullable_to_non_nullable
as List<String>?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}
/// Create a copy of MarketplaceItem
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SellerCopyWith<$Res> get seller {
  
  return $SellerCopyWith<$Res>(_self.seller, (value) {
    return _then(_self.copyWith(seller: value));
  });
}
}


/// Adds pattern-matching-related methods to [MarketplaceItem].
extension MarketplaceItemPatterns on MarketplaceItem {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _MarketplaceItem value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _MarketplaceItem() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _MarketplaceItem value)  $default,){
final _that = this;
switch (_that) {
case _MarketplaceItem():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _MarketplaceItem value)?  $default,){
final _that = this;
switch (_that) {
case _MarketplaceItem() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  double price,  ProductCategory category,  List<String> images,  String sellerId,  Seller seller,  String? description,  bool isVerified,  bool isExclusive,  bool isFeatured,  ProductCondition condition,  int availableQuantity,  int soldQuantity,  double rating,  int reviewCount,  String? brand,  String? model,  String? year,  String? location,  Map<String, dynamic>? specifications,  List<String>? tags,  DateTime? createdAt,  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _MarketplaceItem() when $default != null:
return $default(_that.id,_that.name,_that.price,_that.category,_that.images,_that.sellerId,_that.seller,_that.description,_that.isVerified,_that.isExclusive,_that.isFeatured,_that.condition,_that.availableQuantity,_that.soldQuantity,_that.rating,_that.reviewCount,_that.brand,_that.model,_that.year,_that.location,_that.specifications,_that.tags,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  double price,  ProductCategory category,  List<String> images,  String sellerId,  Seller seller,  String? description,  bool isVerified,  bool isExclusive,  bool isFeatured,  ProductCondition condition,  int availableQuantity,  int soldQuantity,  double rating,  int reviewCount,  String? brand,  String? model,  String? year,  String? location,  Map<String, dynamic>? specifications,  List<String>? tags,  DateTime? createdAt,  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _MarketplaceItem():
return $default(_that.id,_that.name,_that.price,_that.category,_that.images,_that.sellerId,_that.seller,_that.description,_that.isVerified,_that.isExclusive,_that.isFeatured,_that.condition,_that.availableQuantity,_that.soldQuantity,_that.rating,_that.reviewCount,_that.brand,_that.model,_that.year,_that.location,_that.specifications,_that.tags,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  double price,  ProductCategory category,  List<String> images,  String sellerId,  Seller seller,  String? description,  bool isVerified,  bool isExclusive,  bool isFeatured,  ProductCondition condition,  int availableQuantity,  int soldQuantity,  double rating,  int reviewCount,  String? brand,  String? model,  String? year,  String? location,  Map<String, dynamic>? specifications,  List<String>? tags,  DateTime? createdAt,  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _MarketplaceItem() when $default != null:
return $default(_that.id,_that.name,_that.price,_that.category,_that.images,_that.sellerId,_that.seller,_that.description,_that.isVerified,_that.isExclusive,_that.isFeatured,_that.condition,_that.availableQuantity,_that.soldQuantity,_that.rating,_that.reviewCount,_that.brand,_that.model,_that.year,_that.location,_that.specifications,_that.tags,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _MarketplaceItem implements MarketplaceItem {
  const _MarketplaceItem({required this.id, required this.name, required this.price, required this.category, required final  List<String> images, required this.sellerId, required this.seller, this.description, this.isVerified = false, this.isExclusive = false, this.isFeatured = false, this.condition = ProductCondition.brandNew, this.availableQuantity = 1, this.soldQuantity = 0, this.rating = 0.0, this.reviewCount = 0, this.brand, this.model, this.year, this.location, final  Map<String, dynamic>? specifications, final  List<String>? tags, this.createdAt, this.updatedAt}): _images = images,_specifications = specifications,_tags = tags;
  factory _MarketplaceItem.fromJson(Map<String, dynamic> json) => _$MarketplaceItemFromJson(json);

@override final  String id;
@override final  String name;
@override final  double price;
@override final  ProductCategory category;
 final  List<String> _images;
@override List<String> get images {
  if (_images is EqualUnmodifiableListView) return _images;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_images);
}

@override final  String sellerId;
@override final  Seller seller;
@override final  String? description;
@override@JsonKey() final  bool isVerified;
@override@JsonKey() final  bool isExclusive;
@override@JsonKey() final  bool isFeatured;
@override@JsonKey() final  ProductCondition condition;
@override@JsonKey() final  int availableQuantity;
@override@JsonKey() final  int soldQuantity;
@override@JsonKey() final  double rating;
@override@JsonKey() final  int reviewCount;
@override final  String? brand;
@override final  String? model;
@override final  String? year;
@override final  String? location;
 final  Map<String, dynamic>? _specifications;
@override Map<String, dynamic>? get specifications {
  final value = _specifications;
  if (value == null) return null;
  if (_specifications is EqualUnmodifiableMapView) return _specifications;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

 final  List<String>? _tags;
@override List<String>? get tags {
  final value = _tags;
  if (value == null) return null;
  if (_tags is EqualUnmodifiableListView) return _tags;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override final  DateTime? createdAt;
@override final  DateTime? updatedAt;

/// Create a copy of MarketplaceItem
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MarketplaceItemCopyWith<_MarketplaceItem> get copyWith => __$MarketplaceItemCopyWithImpl<_MarketplaceItem>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$MarketplaceItemToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _MarketplaceItem&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.price, price) || other.price == price)&&(identical(other.category, category) || other.category == category)&&const DeepCollectionEquality().equals(other._images, _images)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.seller, seller) || other.seller == seller)&&(identical(other.description, description) || other.description == description)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.isExclusive, isExclusive) || other.isExclusive == isExclusive)&&(identical(other.isFeatured, isFeatured) || other.isFeatured == isFeatured)&&(identical(other.condition, condition) || other.condition == condition)&&(identical(other.availableQuantity, availableQuantity) || other.availableQuantity == availableQuantity)&&(identical(other.soldQuantity, soldQuantity) || other.soldQuantity == soldQuantity)&&(identical(other.rating, rating) || other.rating == rating)&&(identical(other.reviewCount, reviewCount) || other.reviewCount == reviewCount)&&(identical(other.brand, brand) || other.brand == brand)&&(identical(other.model, model) || other.model == model)&&(identical(other.year, year) || other.year == year)&&(identical(other.location, location) || other.location == location)&&const DeepCollectionEquality().equals(other._specifications, _specifications)&&const DeepCollectionEquality().equals(other._tags, _tags)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,name,price,category,const DeepCollectionEquality().hash(_images),sellerId,seller,description,isVerified,isExclusive,isFeatured,condition,availableQuantity,soldQuantity,rating,reviewCount,brand,model,year,location,const DeepCollectionEquality().hash(_specifications),const DeepCollectionEquality().hash(_tags),createdAt,updatedAt]);

@override
String toString() {
  return 'MarketplaceItem(id: $id, name: $name, price: $price, category: $category, images: $images, sellerId: $sellerId, seller: $seller, description: $description, isVerified: $isVerified, isExclusive: $isExclusive, isFeatured: $isFeatured, condition: $condition, availableQuantity: $availableQuantity, soldQuantity: $soldQuantity, rating: $rating, reviewCount: $reviewCount, brand: $brand, model: $model, year: $year, location: $location, specifications: $specifications, tags: $tags, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$MarketplaceItemCopyWith<$Res> implements $MarketplaceItemCopyWith<$Res> {
  factory _$MarketplaceItemCopyWith(_MarketplaceItem value, $Res Function(_MarketplaceItem) _then) = __$MarketplaceItemCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, double price, ProductCategory category, List<String> images, String sellerId, Seller seller, String? description, bool isVerified, bool isExclusive, bool isFeatured, ProductCondition condition, int availableQuantity, int soldQuantity, double rating, int reviewCount, String? brand, String? model, String? year, String? location, Map<String, dynamic>? specifications, List<String>? tags, DateTime? createdAt, DateTime? updatedAt
});


@override $SellerCopyWith<$Res> get seller;

}
/// @nodoc
class __$MarketplaceItemCopyWithImpl<$Res>
    implements _$MarketplaceItemCopyWith<$Res> {
  __$MarketplaceItemCopyWithImpl(this._self, this._then);

  final _MarketplaceItem _self;
  final $Res Function(_MarketplaceItem) _then;

/// Create a copy of MarketplaceItem
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? price = null,Object? category = null,Object? images = null,Object? sellerId = null,Object? seller = null,Object? description = freezed,Object? isVerified = null,Object? isExclusive = null,Object? isFeatured = null,Object? condition = null,Object? availableQuantity = null,Object? soldQuantity = null,Object? rating = null,Object? reviewCount = null,Object? brand = freezed,Object? model = freezed,Object? year = freezed,Object? location = freezed,Object? specifications = freezed,Object? tags = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_MarketplaceItem(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,category: null == category ? _self.category : category // ignore: cast_nullable_to_non_nullable
as ProductCategory,images: null == images ? _self._images : images // ignore: cast_nullable_to_non_nullable
as List<String>,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,seller: null == seller ? _self.seller : seller // ignore: cast_nullable_to_non_nullable
as Seller,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,isExclusive: null == isExclusive ? _self.isExclusive : isExclusive // ignore: cast_nullable_to_non_nullable
as bool,isFeatured: null == isFeatured ? _self.isFeatured : isFeatured // ignore: cast_nullable_to_non_nullable
as bool,condition: null == condition ? _self.condition : condition // ignore: cast_nullable_to_non_nullable
as ProductCondition,availableQuantity: null == availableQuantity ? _self.availableQuantity : availableQuantity // ignore: cast_nullable_to_non_nullable
as int,soldQuantity: null == soldQuantity ? _self.soldQuantity : soldQuantity // ignore: cast_nullable_to_non_nullable
as int,rating: null == rating ? _self.rating : rating // ignore: cast_nullable_to_non_nullable
as double,reviewCount: null == reviewCount ? _self.reviewCount : reviewCount // ignore: cast_nullable_to_non_nullable
as int,brand: freezed == brand ? _self.brand : brand // ignore: cast_nullable_to_non_nullable
as String?,model: freezed == model ? _self.model : model // ignore: cast_nullable_to_non_nullable
as String?,year: freezed == year ? _self.year : year // ignore: cast_nullable_to_non_nullable
as String?,location: freezed == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String?,specifications: freezed == specifications ? _self._specifications : specifications // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,tags: freezed == tags ? _self._tags : tags // ignore: cast_nullable_to_non_nullable
as List<String>?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

/// Create a copy of MarketplaceItem
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SellerCopyWith<$Res> get seller {
  
  return $SellerCopyWith<$Res>(_self.seller, (value) {
    return _then(_self.copyWith(seller: value));
  });
}
}


/// @nodoc
mixin _$CartItem {

 String get id; String get itemId; MarketplaceItem get item; int get quantity; DateTime get addedAt;
/// Create a copy of CartItem
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CartItemCopyWith<CartItem> get copyWith => _$CartItemCopyWithImpl<CartItem>(this as CartItem, _$identity);

  /// Serializes this CartItem to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CartItem&&(identical(other.id, id) || other.id == id)&&(identical(other.itemId, itemId) || other.itemId == itemId)&&(identical(other.item, item) || other.item == item)&&(identical(other.quantity, quantity) || other.quantity == quantity)&&(identical(other.addedAt, addedAt) || other.addedAt == addedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,itemId,item,quantity,addedAt);

@override
String toString() {
  return 'CartItem(id: $id, itemId: $itemId, item: $item, quantity: $quantity, addedAt: $addedAt)';
}


}

/// @nodoc
abstract mixin class $CartItemCopyWith<$Res>  {
  factory $CartItemCopyWith(CartItem value, $Res Function(CartItem) _then) = _$CartItemCopyWithImpl;
@useResult
$Res call({
 String id, String itemId, MarketplaceItem item, int quantity, DateTime addedAt
});


$MarketplaceItemCopyWith<$Res> get item;

}
/// @nodoc
class _$CartItemCopyWithImpl<$Res>
    implements $CartItemCopyWith<$Res> {
  _$CartItemCopyWithImpl(this._self, this._then);

  final CartItem _self;
  final $Res Function(CartItem) _then;

/// Create a copy of CartItem
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? itemId = null,Object? item = null,Object? quantity = null,Object? addedAt = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,itemId: null == itemId ? _self.itemId : itemId // ignore: cast_nullable_to_non_nullable
as String,item: null == item ? _self.item : item // ignore: cast_nullable_to_non_nullable
as MarketplaceItem,quantity: null == quantity ? _self.quantity : quantity // ignore: cast_nullable_to_non_nullable
as int,addedAt: null == addedAt ? _self.addedAt : addedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}
/// Create a copy of CartItem
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MarketplaceItemCopyWith<$Res> get item {
  
  return $MarketplaceItemCopyWith<$Res>(_self.item, (value) {
    return _then(_self.copyWith(item: value));
  });
}
}


/// Adds pattern-matching-related methods to [CartItem].
extension CartItemPatterns on CartItem {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CartItem value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CartItem() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CartItem value)  $default,){
final _that = this;
switch (_that) {
case _CartItem():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CartItem value)?  $default,){
final _that = this;
switch (_that) {
case _CartItem() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String itemId,  MarketplaceItem item,  int quantity,  DateTime addedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CartItem() when $default != null:
return $default(_that.id,_that.itemId,_that.item,_that.quantity,_that.addedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String itemId,  MarketplaceItem item,  int quantity,  DateTime addedAt)  $default,) {final _that = this;
switch (_that) {
case _CartItem():
return $default(_that.id,_that.itemId,_that.item,_that.quantity,_that.addedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String itemId,  MarketplaceItem item,  int quantity,  DateTime addedAt)?  $default,) {final _that = this;
switch (_that) {
case _CartItem() when $default != null:
return $default(_that.id,_that.itemId,_that.item,_that.quantity,_that.addedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _CartItem implements CartItem {
  const _CartItem({required this.id, required this.itemId, required this.item, required this.quantity, required this.addedAt});
  factory _CartItem.fromJson(Map<String, dynamic> json) => _$CartItemFromJson(json);

@override final  String id;
@override final  String itemId;
@override final  MarketplaceItem item;
@override final  int quantity;
@override final  DateTime addedAt;

/// Create a copy of CartItem
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CartItemCopyWith<_CartItem> get copyWith => __$CartItemCopyWithImpl<_CartItem>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CartItemToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CartItem&&(identical(other.id, id) || other.id == id)&&(identical(other.itemId, itemId) || other.itemId == itemId)&&(identical(other.item, item) || other.item == item)&&(identical(other.quantity, quantity) || other.quantity == quantity)&&(identical(other.addedAt, addedAt) || other.addedAt == addedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,itemId,item,quantity,addedAt);

@override
String toString() {
  return 'CartItem(id: $id, itemId: $itemId, item: $item, quantity: $quantity, addedAt: $addedAt)';
}


}

/// @nodoc
abstract mixin class _$CartItemCopyWith<$Res> implements $CartItemCopyWith<$Res> {
  factory _$CartItemCopyWith(_CartItem value, $Res Function(_CartItem) _then) = __$CartItemCopyWithImpl;
@override @useResult
$Res call({
 String id, String itemId, MarketplaceItem item, int quantity, DateTime addedAt
});


@override $MarketplaceItemCopyWith<$Res> get item;

}
/// @nodoc
class __$CartItemCopyWithImpl<$Res>
    implements _$CartItemCopyWith<$Res> {
  __$CartItemCopyWithImpl(this._self, this._then);

  final _CartItem _self;
  final $Res Function(_CartItem) _then;

/// Create a copy of CartItem
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? itemId = null,Object? item = null,Object? quantity = null,Object? addedAt = null,}) {
  return _then(_CartItem(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,itemId: null == itemId ? _self.itemId : itemId // ignore: cast_nullable_to_non_nullable
as String,item: null == item ? _self.item : item // ignore: cast_nullable_to_non_nullable
as MarketplaceItem,quantity: null == quantity ? _self.quantity : quantity // ignore: cast_nullable_to_non_nullable
as int,addedAt: null == addedAt ? _self.addedAt : addedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

/// Create a copy of CartItem
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MarketplaceItemCopyWith<$Res> get item {
  
  return $MarketplaceItemCopyWith<$Res>(_self.item, (value) {
    return _then(_self.copyWith(item: value));
  });
}
}


/// @nodoc
mixin _$Cart {

 String get id; String get userId; List<CartItem> get items; double get subtotal; double get tax; double get shipping; double get total; DateTime? get lastUpdated;
/// Create a copy of Cart
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CartCopyWith<Cart> get copyWith => _$CartCopyWithImpl<Cart>(this as Cart, _$identity);

  /// Serializes this Cart to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Cart&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&const DeepCollectionEquality().equals(other.items, items)&&(identical(other.subtotal, subtotal) || other.subtotal == subtotal)&&(identical(other.tax, tax) || other.tax == tax)&&(identical(other.shipping, shipping) || other.shipping == shipping)&&(identical(other.total, total) || other.total == total)&&(identical(other.lastUpdated, lastUpdated) || other.lastUpdated == lastUpdated));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,const DeepCollectionEquality().hash(items),subtotal,tax,shipping,total,lastUpdated);

@override
String toString() {
  return 'Cart(id: $id, userId: $userId, items: $items, subtotal: $subtotal, tax: $tax, shipping: $shipping, total: $total, lastUpdated: $lastUpdated)';
}


}

/// @nodoc
abstract mixin class $CartCopyWith<$Res>  {
  factory $CartCopyWith(Cart value, $Res Function(Cart) _then) = _$CartCopyWithImpl;
@useResult
$Res call({
 String id, String userId, List<CartItem> items, double subtotal, double tax, double shipping, double total, DateTime? lastUpdated
});




}
/// @nodoc
class _$CartCopyWithImpl<$Res>
    implements $CartCopyWith<$Res> {
  _$CartCopyWithImpl(this._self, this._then);

  final Cart _self;
  final $Res Function(Cart) _then;

/// Create a copy of Cart
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? items = null,Object? subtotal = null,Object? tax = null,Object? shipping = null,Object? total = null,Object? lastUpdated = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,items: null == items ? _self.items : items // ignore: cast_nullable_to_non_nullable
as List<CartItem>,subtotal: null == subtotal ? _self.subtotal : subtotal // ignore: cast_nullable_to_non_nullable
as double,tax: null == tax ? _self.tax : tax // ignore: cast_nullable_to_non_nullable
as double,shipping: null == shipping ? _self.shipping : shipping // ignore: cast_nullable_to_non_nullable
as double,total: null == total ? _self.total : total // ignore: cast_nullable_to_non_nullable
as double,lastUpdated: freezed == lastUpdated ? _self.lastUpdated : lastUpdated // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [Cart].
extension CartPatterns on Cart {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Cart value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Cart() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Cart value)  $default,){
final _that = this;
switch (_that) {
case _Cart():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Cart value)?  $default,){
final _that = this;
switch (_that) {
case _Cart() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  List<CartItem> items,  double subtotal,  double tax,  double shipping,  double total,  DateTime? lastUpdated)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Cart() when $default != null:
return $default(_that.id,_that.userId,_that.items,_that.subtotal,_that.tax,_that.shipping,_that.total,_that.lastUpdated);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  List<CartItem> items,  double subtotal,  double tax,  double shipping,  double total,  DateTime? lastUpdated)  $default,) {final _that = this;
switch (_that) {
case _Cart():
return $default(_that.id,_that.userId,_that.items,_that.subtotal,_that.tax,_that.shipping,_that.total,_that.lastUpdated);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  List<CartItem> items,  double subtotal,  double tax,  double shipping,  double total,  DateTime? lastUpdated)?  $default,) {final _that = this;
switch (_that) {
case _Cart() when $default != null:
return $default(_that.id,_that.userId,_that.items,_that.subtotal,_that.tax,_that.shipping,_that.total,_that.lastUpdated);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Cart implements Cart {
  const _Cart({required this.id, required this.userId, final  List<CartItem> items = const [], this.subtotal = 0.0, this.tax = 0.0, this.shipping = 0.0, this.total = 0.0, this.lastUpdated}): _items = items;
  factory _Cart.fromJson(Map<String, dynamic> json) => _$CartFromJson(json);

@override final  String id;
@override final  String userId;
 final  List<CartItem> _items;
@override@JsonKey() List<CartItem> get items {
  if (_items is EqualUnmodifiableListView) return _items;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_items);
}

@override@JsonKey() final  double subtotal;
@override@JsonKey() final  double tax;
@override@JsonKey() final  double shipping;
@override@JsonKey() final  double total;
@override final  DateTime? lastUpdated;

/// Create a copy of Cart
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CartCopyWith<_Cart> get copyWith => __$CartCopyWithImpl<_Cart>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CartToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Cart&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&const DeepCollectionEquality().equals(other._items, _items)&&(identical(other.subtotal, subtotal) || other.subtotal == subtotal)&&(identical(other.tax, tax) || other.tax == tax)&&(identical(other.shipping, shipping) || other.shipping == shipping)&&(identical(other.total, total) || other.total == total)&&(identical(other.lastUpdated, lastUpdated) || other.lastUpdated == lastUpdated));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,const DeepCollectionEquality().hash(_items),subtotal,tax,shipping,total,lastUpdated);

@override
String toString() {
  return 'Cart(id: $id, userId: $userId, items: $items, subtotal: $subtotal, tax: $tax, shipping: $shipping, total: $total, lastUpdated: $lastUpdated)';
}


}

/// @nodoc
abstract mixin class _$CartCopyWith<$Res> implements $CartCopyWith<$Res> {
  factory _$CartCopyWith(_Cart value, $Res Function(_Cart) _then) = __$CartCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, List<CartItem> items, double subtotal, double tax, double shipping, double total, DateTime? lastUpdated
});




}
/// @nodoc
class __$CartCopyWithImpl<$Res>
    implements _$CartCopyWith<$Res> {
  __$CartCopyWithImpl(this._self, this._then);

  final _Cart _self;
  final $Res Function(_Cart) _then;

/// Create a copy of Cart
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? items = null,Object? subtotal = null,Object? tax = null,Object? shipping = null,Object? total = null,Object? lastUpdated = freezed,}) {
  return _then(_Cart(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,items: null == items ? _self._items : items // ignore: cast_nullable_to_non_nullable
as List<CartItem>,subtotal: null == subtotal ? _self.subtotal : subtotal // ignore: cast_nullable_to_non_nullable
as double,tax: null == tax ? _self.tax : tax // ignore: cast_nullable_to_non_nullable
as double,shipping: null == shipping ? _self.shipping : shipping // ignore: cast_nullable_to_non_nullable
as double,total: null == total ? _self.total : total // ignore: cast_nullable_to_non_nullable
as double,lastUpdated: freezed == lastUpdated ? _self.lastUpdated : lastUpdated // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$Order {

 String get id; String get userId; String get sellerId; List<CartItem> get items; double get subtotal; double get tax; double get shipping; double get total; OrderStatus get status; PaymentStatus get paymentStatus; DateTime get orderDate; DateTime? get shippedDate; DateTime? get deliveredDate; String? get trackingNumber; String? get shippingAddress; String? get billingAddress; String? get notes;
/// Create a copy of Order
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OrderCopyWith<Order> get copyWith => _$OrderCopyWithImpl<Order>(this as Order, _$identity);

  /// Serializes this Order to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Order&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&const DeepCollectionEquality().equals(other.items, items)&&(identical(other.subtotal, subtotal) || other.subtotal == subtotal)&&(identical(other.tax, tax) || other.tax == tax)&&(identical(other.shipping, shipping) || other.shipping == shipping)&&(identical(other.total, total) || other.total == total)&&(identical(other.status, status) || other.status == status)&&(identical(other.paymentStatus, paymentStatus) || other.paymentStatus == paymentStatus)&&(identical(other.orderDate, orderDate) || other.orderDate == orderDate)&&(identical(other.shippedDate, shippedDate) || other.shippedDate == shippedDate)&&(identical(other.deliveredDate, deliveredDate) || other.deliveredDate == deliveredDate)&&(identical(other.trackingNumber, trackingNumber) || other.trackingNumber == trackingNumber)&&(identical(other.shippingAddress, shippingAddress) || other.shippingAddress == shippingAddress)&&(identical(other.billingAddress, billingAddress) || other.billingAddress == billingAddress)&&(identical(other.notes, notes) || other.notes == notes));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,sellerId,const DeepCollectionEquality().hash(items),subtotal,tax,shipping,total,status,paymentStatus,orderDate,shippedDate,deliveredDate,trackingNumber,shippingAddress,billingAddress,notes);

@override
String toString() {
  return 'Order(id: $id, userId: $userId, sellerId: $sellerId, items: $items, subtotal: $subtotal, tax: $tax, shipping: $shipping, total: $total, status: $status, paymentStatus: $paymentStatus, orderDate: $orderDate, shippedDate: $shippedDate, deliveredDate: $deliveredDate, trackingNumber: $trackingNumber, shippingAddress: $shippingAddress, billingAddress: $billingAddress, notes: $notes)';
}


}

/// @nodoc
abstract mixin class $OrderCopyWith<$Res>  {
  factory $OrderCopyWith(Order value, $Res Function(Order) _then) = _$OrderCopyWithImpl;
@useResult
$Res call({
 String id, String userId, String sellerId, List<CartItem> items, double subtotal, double tax, double shipping, double total, OrderStatus status, PaymentStatus paymentStatus, DateTime orderDate, DateTime? shippedDate, DateTime? deliveredDate, String? trackingNumber, String? shippingAddress, String? billingAddress, String? notes
});




}
/// @nodoc
class _$OrderCopyWithImpl<$Res>
    implements $OrderCopyWith<$Res> {
  _$OrderCopyWithImpl(this._self, this._then);

  final Order _self;
  final $Res Function(Order) _then;

/// Create a copy of Order
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? sellerId = null,Object? items = null,Object? subtotal = null,Object? tax = null,Object? shipping = null,Object? total = null,Object? status = null,Object? paymentStatus = null,Object? orderDate = null,Object? shippedDate = freezed,Object? deliveredDate = freezed,Object? trackingNumber = freezed,Object? shippingAddress = freezed,Object? billingAddress = freezed,Object? notes = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,items: null == items ? _self.items : items // ignore: cast_nullable_to_non_nullable
as List<CartItem>,subtotal: null == subtotal ? _self.subtotal : subtotal // ignore: cast_nullable_to_non_nullable
as double,tax: null == tax ? _self.tax : tax // ignore: cast_nullable_to_non_nullable
as double,shipping: null == shipping ? _self.shipping : shipping // ignore: cast_nullable_to_non_nullable
as double,total: null == total ? _self.total : total // ignore: cast_nullable_to_non_nullable
as double,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as OrderStatus,paymentStatus: null == paymentStatus ? _self.paymentStatus : paymentStatus // ignore: cast_nullable_to_non_nullable
as PaymentStatus,orderDate: null == orderDate ? _self.orderDate : orderDate // ignore: cast_nullable_to_non_nullable
as DateTime,shippedDate: freezed == shippedDate ? _self.shippedDate : shippedDate // ignore: cast_nullable_to_non_nullable
as DateTime?,deliveredDate: freezed == deliveredDate ? _self.deliveredDate : deliveredDate // ignore: cast_nullable_to_non_nullable
as DateTime?,trackingNumber: freezed == trackingNumber ? _self.trackingNumber : trackingNumber // ignore: cast_nullable_to_non_nullable
as String?,shippingAddress: freezed == shippingAddress ? _self.shippingAddress : shippingAddress // ignore: cast_nullable_to_non_nullable
as String?,billingAddress: freezed == billingAddress ? _self.billingAddress : billingAddress // ignore: cast_nullable_to_non_nullable
as String?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [Order].
extension OrderPatterns on Order {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Order value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Order() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Order value)  $default,){
final _that = this;
switch (_that) {
case _Order():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Order value)?  $default,){
final _that = this;
switch (_that) {
case _Order() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  String sellerId,  List<CartItem> items,  double subtotal,  double tax,  double shipping,  double total,  OrderStatus status,  PaymentStatus paymentStatus,  DateTime orderDate,  DateTime? shippedDate,  DateTime? deliveredDate,  String? trackingNumber,  String? shippingAddress,  String? billingAddress,  String? notes)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Order() when $default != null:
return $default(_that.id,_that.userId,_that.sellerId,_that.items,_that.subtotal,_that.tax,_that.shipping,_that.total,_that.status,_that.paymentStatus,_that.orderDate,_that.shippedDate,_that.deliveredDate,_that.trackingNumber,_that.shippingAddress,_that.billingAddress,_that.notes);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  String sellerId,  List<CartItem> items,  double subtotal,  double tax,  double shipping,  double total,  OrderStatus status,  PaymentStatus paymentStatus,  DateTime orderDate,  DateTime? shippedDate,  DateTime? deliveredDate,  String? trackingNumber,  String? shippingAddress,  String? billingAddress,  String? notes)  $default,) {final _that = this;
switch (_that) {
case _Order():
return $default(_that.id,_that.userId,_that.sellerId,_that.items,_that.subtotal,_that.tax,_that.shipping,_that.total,_that.status,_that.paymentStatus,_that.orderDate,_that.shippedDate,_that.deliveredDate,_that.trackingNumber,_that.shippingAddress,_that.billingAddress,_that.notes);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  String sellerId,  List<CartItem> items,  double subtotal,  double tax,  double shipping,  double total,  OrderStatus status,  PaymentStatus paymentStatus,  DateTime orderDate,  DateTime? shippedDate,  DateTime? deliveredDate,  String? trackingNumber,  String? shippingAddress,  String? billingAddress,  String? notes)?  $default,) {final _that = this;
switch (_that) {
case _Order() when $default != null:
return $default(_that.id,_that.userId,_that.sellerId,_that.items,_that.subtotal,_that.tax,_that.shipping,_that.total,_that.status,_that.paymentStatus,_that.orderDate,_that.shippedDate,_that.deliveredDate,_that.trackingNumber,_that.shippingAddress,_that.billingAddress,_that.notes);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Order implements Order {
  const _Order({required this.id, required this.userId, required this.sellerId, required final  List<CartItem> items, required this.subtotal, required this.tax, required this.shipping, required this.total, required this.status, required this.paymentStatus, required this.orderDate, this.shippedDate, this.deliveredDate, this.trackingNumber, this.shippingAddress, this.billingAddress, this.notes}): _items = items;
  factory _Order.fromJson(Map<String, dynamic> json) => _$OrderFromJson(json);

@override final  String id;
@override final  String userId;
@override final  String sellerId;
 final  List<CartItem> _items;
@override List<CartItem> get items {
  if (_items is EqualUnmodifiableListView) return _items;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_items);
}

@override final  double subtotal;
@override final  double tax;
@override final  double shipping;
@override final  double total;
@override final  OrderStatus status;
@override final  PaymentStatus paymentStatus;
@override final  DateTime orderDate;
@override final  DateTime? shippedDate;
@override final  DateTime? deliveredDate;
@override final  String? trackingNumber;
@override final  String? shippingAddress;
@override final  String? billingAddress;
@override final  String? notes;

/// Create a copy of Order
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OrderCopyWith<_Order> get copyWith => __$OrderCopyWithImpl<_Order>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$OrderToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Order&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&const DeepCollectionEquality().equals(other._items, _items)&&(identical(other.subtotal, subtotal) || other.subtotal == subtotal)&&(identical(other.tax, tax) || other.tax == tax)&&(identical(other.shipping, shipping) || other.shipping == shipping)&&(identical(other.total, total) || other.total == total)&&(identical(other.status, status) || other.status == status)&&(identical(other.paymentStatus, paymentStatus) || other.paymentStatus == paymentStatus)&&(identical(other.orderDate, orderDate) || other.orderDate == orderDate)&&(identical(other.shippedDate, shippedDate) || other.shippedDate == shippedDate)&&(identical(other.deliveredDate, deliveredDate) || other.deliveredDate == deliveredDate)&&(identical(other.trackingNumber, trackingNumber) || other.trackingNumber == trackingNumber)&&(identical(other.shippingAddress, shippingAddress) || other.shippingAddress == shippingAddress)&&(identical(other.billingAddress, billingAddress) || other.billingAddress == billingAddress)&&(identical(other.notes, notes) || other.notes == notes));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,sellerId,const DeepCollectionEquality().hash(_items),subtotal,tax,shipping,total,status,paymentStatus,orderDate,shippedDate,deliveredDate,trackingNumber,shippingAddress,billingAddress,notes);

@override
String toString() {
  return 'Order(id: $id, userId: $userId, sellerId: $sellerId, items: $items, subtotal: $subtotal, tax: $tax, shipping: $shipping, total: $total, status: $status, paymentStatus: $paymentStatus, orderDate: $orderDate, shippedDate: $shippedDate, deliveredDate: $deliveredDate, trackingNumber: $trackingNumber, shippingAddress: $shippingAddress, billingAddress: $billingAddress, notes: $notes)';
}


}

/// @nodoc
abstract mixin class _$OrderCopyWith<$Res> implements $OrderCopyWith<$Res> {
  factory _$OrderCopyWith(_Order value, $Res Function(_Order) _then) = __$OrderCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, String sellerId, List<CartItem> items, double subtotal, double tax, double shipping, double total, OrderStatus status, PaymentStatus paymentStatus, DateTime orderDate, DateTime? shippedDate, DateTime? deliveredDate, String? trackingNumber, String? shippingAddress, String? billingAddress, String? notes
});




}
/// @nodoc
class __$OrderCopyWithImpl<$Res>
    implements _$OrderCopyWith<$Res> {
  __$OrderCopyWithImpl(this._self, this._then);

  final _Order _self;
  final $Res Function(_Order) _then;

/// Create a copy of Order
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? sellerId = null,Object? items = null,Object? subtotal = null,Object? tax = null,Object? shipping = null,Object? total = null,Object? status = null,Object? paymentStatus = null,Object? orderDate = null,Object? shippedDate = freezed,Object? deliveredDate = freezed,Object? trackingNumber = freezed,Object? shippingAddress = freezed,Object? billingAddress = freezed,Object? notes = freezed,}) {
  return _then(_Order(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,items: null == items ? _self._items : items // ignore: cast_nullable_to_non_nullable
as List<CartItem>,subtotal: null == subtotal ? _self.subtotal : subtotal // ignore: cast_nullable_to_non_nullable
as double,tax: null == tax ? _self.tax : tax // ignore: cast_nullable_to_non_nullable
as double,shipping: null == shipping ? _self.shipping : shipping // ignore: cast_nullable_to_non_nullable
as double,total: null == total ? _self.total : total // ignore: cast_nullable_to_non_nullable
as double,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as OrderStatus,paymentStatus: null == paymentStatus ? _self.paymentStatus : paymentStatus // ignore: cast_nullable_to_non_nullable
as PaymentStatus,orderDate: null == orderDate ? _self.orderDate : orderDate // ignore: cast_nullable_to_non_nullable
as DateTime,shippedDate: freezed == shippedDate ? _self.shippedDate : shippedDate // ignore: cast_nullable_to_non_nullable
as DateTime?,deliveredDate: freezed == deliveredDate ? _self.deliveredDate : deliveredDate // ignore: cast_nullable_to_non_nullable
as DateTime?,trackingNumber: freezed == trackingNumber ? _self.trackingNumber : trackingNumber // ignore: cast_nullable_to_non_nullable
as String?,shippingAddress: freezed == shippingAddress ? _self.shippingAddress : shippingAddress // ignore: cast_nullable_to_non_nullable
as String?,billingAddress: freezed == billingAddress ? _self.billingAddress : billingAddress // ignore: cast_nullable_to_non_nullable
as String?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$CheckoutRequest {

 String get cartId; String get shippingAddress; String get billingAddress; String? get notes; String? get promoCode;
/// Create a copy of CheckoutRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CheckoutRequestCopyWith<CheckoutRequest> get copyWith => _$CheckoutRequestCopyWithImpl<CheckoutRequest>(this as CheckoutRequest, _$identity);

  /// Serializes this CheckoutRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CheckoutRequest&&(identical(other.cartId, cartId) || other.cartId == cartId)&&(identical(other.shippingAddress, shippingAddress) || other.shippingAddress == shippingAddress)&&(identical(other.billingAddress, billingAddress) || other.billingAddress == billingAddress)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.promoCode, promoCode) || other.promoCode == promoCode));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,cartId,shippingAddress,billingAddress,notes,promoCode);

@override
String toString() {
  return 'CheckoutRequest(cartId: $cartId, shippingAddress: $shippingAddress, billingAddress: $billingAddress, notes: $notes, promoCode: $promoCode)';
}


}

/// @nodoc
abstract mixin class $CheckoutRequestCopyWith<$Res>  {
  factory $CheckoutRequestCopyWith(CheckoutRequest value, $Res Function(CheckoutRequest) _then) = _$CheckoutRequestCopyWithImpl;
@useResult
$Res call({
 String cartId, String shippingAddress, String billingAddress, String? notes, String? promoCode
});




}
/// @nodoc
class _$CheckoutRequestCopyWithImpl<$Res>
    implements $CheckoutRequestCopyWith<$Res> {
  _$CheckoutRequestCopyWithImpl(this._self, this._then);

  final CheckoutRequest _self;
  final $Res Function(CheckoutRequest) _then;

/// Create a copy of CheckoutRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? cartId = null,Object? shippingAddress = null,Object? billingAddress = null,Object? notes = freezed,Object? promoCode = freezed,}) {
  return _then(_self.copyWith(
cartId: null == cartId ? _self.cartId : cartId // ignore: cast_nullable_to_non_nullable
as String,shippingAddress: null == shippingAddress ? _self.shippingAddress : shippingAddress // ignore: cast_nullable_to_non_nullable
as String,billingAddress: null == billingAddress ? _self.billingAddress : billingAddress // ignore: cast_nullable_to_non_nullable
as String,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,promoCode: freezed == promoCode ? _self.promoCode : promoCode // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [CheckoutRequest].
extension CheckoutRequestPatterns on CheckoutRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CheckoutRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CheckoutRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CheckoutRequest value)  $default,){
final _that = this;
switch (_that) {
case _CheckoutRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CheckoutRequest value)?  $default,){
final _that = this;
switch (_that) {
case _CheckoutRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String cartId,  String shippingAddress,  String billingAddress,  String? notes,  String? promoCode)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CheckoutRequest() when $default != null:
return $default(_that.cartId,_that.shippingAddress,_that.billingAddress,_that.notes,_that.promoCode);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String cartId,  String shippingAddress,  String billingAddress,  String? notes,  String? promoCode)  $default,) {final _that = this;
switch (_that) {
case _CheckoutRequest():
return $default(_that.cartId,_that.shippingAddress,_that.billingAddress,_that.notes,_that.promoCode);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String cartId,  String shippingAddress,  String billingAddress,  String? notes,  String? promoCode)?  $default,) {final _that = this;
switch (_that) {
case _CheckoutRequest() when $default != null:
return $default(_that.cartId,_that.shippingAddress,_that.billingAddress,_that.notes,_that.promoCode);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _CheckoutRequest implements CheckoutRequest {
  const _CheckoutRequest({required this.cartId, required this.shippingAddress, required this.billingAddress, this.notes, this.promoCode});
  factory _CheckoutRequest.fromJson(Map<String, dynamic> json) => _$CheckoutRequestFromJson(json);

@override final  String cartId;
@override final  String shippingAddress;
@override final  String billingAddress;
@override final  String? notes;
@override final  String? promoCode;

/// Create a copy of CheckoutRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CheckoutRequestCopyWith<_CheckoutRequest> get copyWith => __$CheckoutRequestCopyWithImpl<_CheckoutRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CheckoutRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CheckoutRequest&&(identical(other.cartId, cartId) || other.cartId == cartId)&&(identical(other.shippingAddress, shippingAddress) || other.shippingAddress == shippingAddress)&&(identical(other.billingAddress, billingAddress) || other.billingAddress == billingAddress)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.promoCode, promoCode) || other.promoCode == promoCode));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,cartId,shippingAddress,billingAddress,notes,promoCode);

@override
String toString() {
  return 'CheckoutRequest(cartId: $cartId, shippingAddress: $shippingAddress, billingAddress: $billingAddress, notes: $notes, promoCode: $promoCode)';
}


}

/// @nodoc
abstract mixin class _$CheckoutRequestCopyWith<$Res> implements $CheckoutRequestCopyWith<$Res> {
  factory _$CheckoutRequestCopyWith(_CheckoutRequest value, $Res Function(_CheckoutRequest) _then) = __$CheckoutRequestCopyWithImpl;
@override @useResult
$Res call({
 String cartId, String shippingAddress, String billingAddress, String? notes, String? promoCode
});




}
/// @nodoc
class __$CheckoutRequestCopyWithImpl<$Res>
    implements _$CheckoutRequestCopyWith<$Res> {
  __$CheckoutRequestCopyWithImpl(this._self, this._then);

  final _CheckoutRequest _self;
  final $Res Function(_CheckoutRequest) _then;

/// Create a copy of CheckoutRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? cartId = null,Object? shippingAddress = null,Object? billingAddress = null,Object? notes = freezed,Object? promoCode = freezed,}) {
  return _then(_CheckoutRequest(
cartId: null == cartId ? _self.cartId : cartId // ignore: cast_nullable_to_non_nullable
as String,shippingAddress: null == shippingAddress ? _self.shippingAddress : shippingAddress // ignore: cast_nullable_to_non_nullable
as String,billingAddress: null == billingAddress ? _self.billingAddress : billingAddress // ignore: cast_nullable_to_non_nullable
as String,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,promoCode: freezed == promoCode ? _self.promoCode : promoCode // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$MarketplaceFilter {

 Set<ProductCategory> get categories; double? get minPrice; double? get maxPrice; bool get verifiedOnly; bool get exclusiveOnly; bool get featuredOnly; String? get searchQuery; String? get brand; String? get sellerId; ProductCondition? get condition; double get minRating; String? get location;
/// Create a copy of MarketplaceFilter
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MarketplaceFilterCopyWith<MarketplaceFilter> get copyWith => _$MarketplaceFilterCopyWithImpl<MarketplaceFilter>(this as MarketplaceFilter, _$identity);

  /// Serializes this MarketplaceFilter to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MarketplaceFilter&&const DeepCollectionEquality().equals(other.categories, categories)&&(identical(other.minPrice, minPrice) || other.minPrice == minPrice)&&(identical(other.maxPrice, maxPrice) || other.maxPrice == maxPrice)&&(identical(other.verifiedOnly, verifiedOnly) || other.verifiedOnly == verifiedOnly)&&(identical(other.exclusiveOnly, exclusiveOnly) || other.exclusiveOnly == exclusiveOnly)&&(identical(other.featuredOnly, featuredOnly) || other.featuredOnly == featuredOnly)&&(identical(other.searchQuery, searchQuery) || other.searchQuery == searchQuery)&&(identical(other.brand, brand) || other.brand == brand)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.condition, condition) || other.condition == condition)&&(identical(other.minRating, minRating) || other.minRating == minRating)&&(identical(other.location, location) || other.location == location));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(categories),minPrice,maxPrice,verifiedOnly,exclusiveOnly,featuredOnly,searchQuery,brand,sellerId,condition,minRating,location);

@override
String toString() {
  return 'MarketplaceFilter(categories: $categories, minPrice: $minPrice, maxPrice: $maxPrice, verifiedOnly: $verifiedOnly, exclusiveOnly: $exclusiveOnly, featuredOnly: $featuredOnly, searchQuery: $searchQuery, brand: $brand, sellerId: $sellerId, condition: $condition, minRating: $minRating, location: $location)';
}


}

/// @nodoc
abstract mixin class $MarketplaceFilterCopyWith<$Res>  {
  factory $MarketplaceFilterCopyWith(MarketplaceFilter value, $Res Function(MarketplaceFilter) _then) = _$MarketplaceFilterCopyWithImpl;
@useResult
$Res call({
 Set<ProductCategory> categories, double? minPrice, double? maxPrice, bool verifiedOnly, bool exclusiveOnly, bool featuredOnly, String? searchQuery, String? brand, String? sellerId, ProductCondition? condition, double minRating, String? location
});




}
/// @nodoc
class _$MarketplaceFilterCopyWithImpl<$Res>
    implements $MarketplaceFilterCopyWith<$Res> {
  _$MarketplaceFilterCopyWithImpl(this._self, this._then);

  final MarketplaceFilter _self;
  final $Res Function(MarketplaceFilter) _then;

/// Create a copy of MarketplaceFilter
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? categories = null,Object? minPrice = freezed,Object? maxPrice = freezed,Object? verifiedOnly = null,Object? exclusiveOnly = null,Object? featuredOnly = null,Object? searchQuery = freezed,Object? brand = freezed,Object? sellerId = freezed,Object? condition = freezed,Object? minRating = null,Object? location = freezed,}) {
  return _then(_self.copyWith(
categories: null == categories ? _self.categories : categories // ignore: cast_nullable_to_non_nullable
as Set<ProductCategory>,minPrice: freezed == minPrice ? _self.minPrice : minPrice // ignore: cast_nullable_to_non_nullable
as double?,maxPrice: freezed == maxPrice ? _self.maxPrice : maxPrice // ignore: cast_nullable_to_non_nullable
as double?,verifiedOnly: null == verifiedOnly ? _self.verifiedOnly : verifiedOnly // ignore: cast_nullable_to_non_nullable
as bool,exclusiveOnly: null == exclusiveOnly ? _self.exclusiveOnly : exclusiveOnly // ignore: cast_nullable_to_non_nullable
as bool,featuredOnly: null == featuredOnly ? _self.featuredOnly : featuredOnly // ignore: cast_nullable_to_non_nullable
as bool,searchQuery: freezed == searchQuery ? _self.searchQuery : searchQuery // ignore: cast_nullable_to_non_nullable
as String?,brand: freezed == brand ? _self.brand : brand // ignore: cast_nullable_to_non_nullable
as String?,sellerId: freezed == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String?,condition: freezed == condition ? _self.condition : condition // ignore: cast_nullable_to_non_nullable
as ProductCondition?,minRating: null == minRating ? _self.minRating : minRating // ignore: cast_nullable_to_non_nullable
as double,location: freezed == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [MarketplaceFilter].
extension MarketplaceFilterPatterns on MarketplaceFilter {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _MarketplaceFilter value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _MarketplaceFilter() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _MarketplaceFilter value)  $default,){
final _that = this;
switch (_that) {
case _MarketplaceFilter():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _MarketplaceFilter value)?  $default,){
final _that = this;
switch (_that) {
case _MarketplaceFilter() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( Set<ProductCategory> categories,  double? minPrice,  double? maxPrice,  bool verifiedOnly,  bool exclusiveOnly,  bool featuredOnly,  String? searchQuery,  String? brand,  String? sellerId,  ProductCondition? condition,  double minRating,  String? location)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _MarketplaceFilter() when $default != null:
return $default(_that.categories,_that.minPrice,_that.maxPrice,_that.verifiedOnly,_that.exclusiveOnly,_that.featuredOnly,_that.searchQuery,_that.brand,_that.sellerId,_that.condition,_that.minRating,_that.location);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( Set<ProductCategory> categories,  double? minPrice,  double? maxPrice,  bool verifiedOnly,  bool exclusiveOnly,  bool featuredOnly,  String? searchQuery,  String? brand,  String? sellerId,  ProductCondition? condition,  double minRating,  String? location)  $default,) {final _that = this;
switch (_that) {
case _MarketplaceFilter():
return $default(_that.categories,_that.minPrice,_that.maxPrice,_that.verifiedOnly,_that.exclusiveOnly,_that.featuredOnly,_that.searchQuery,_that.brand,_that.sellerId,_that.condition,_that.minRating,_that.location);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( Set<ProductCategory> categories,  double? minPrice,  double? maxPrice,  bool verifiedOnly,  bool exclusiveOnly,  bool featuredOnly,  String? searchQuery,  String? brand,  String? sellerId,  ProductCondition? condition,  double minRating,  String? location)?  $default,) {final _that = this;
switch (_that) {
case _MarketplaceFilter() when $default != null:
return $default(_that.categories,_that.minPrice,_that.maxPrice,_that.verifiedOnly,_that.exclusiveOnly,_that.featuredOnly,_that.searchQuery,_that.brand,_that.sellerId,_that.condition,_that.minRating,_that.location);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _MarketplaceFilter implements MarketplaceFilter {
  const _MarketplaceFilter({final  Set<ProductCategory> categories = const {}, this.minPrice, this.maxPrice, this.verifiedOnly = false, this.exclusiveOnly = false, this.featuredOnly = false, this.searchQuery, this.brand, this.sellerId, this.condition, this.minRating = 0.0, this.location}): _categories = categories;
  factory _MarketplaceFilter.fromJson(Map<String, dynamic> json) => _$MarketplaceFilterFromJson(json);

 final  Set<ProductCategory> _categories;
@override@JsonKey() Set<ProductCategory> get categories {
  if (_categories is EqualUnmodifiableSetView) return _categories;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableSetView(_categories);
}

@override final  double? minPrice;
@override final  double? maxPrice;
@override@JsonKey() final  bool verifiedOnly;
@override@JsonKey() final  bool exclusiveOnly;
@override@JsonKey() final  bool featuredOnly;
@override final  String? searchQuery;
@override final  String? brand;
@override final  String? sellerId;
@override final  ProductCondition? condition;
@override@JsonKey() final  double minRating;
@override final  String? location;

/// Create a copy of MarketplaceFilter
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MarketplaceFilterCopyWith<_MarketplaceFilter> get copyWith => __$MarketplaceFilterCopyWithImpl<_MarketplaceFilter>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$MarketplaceFilterToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _MarketplaceFilter&&const DeepCollectionEquality().equals(other._categories, _categories)&&(identical(other.minPrice, minPrice) || other.minPrice == minPrice)&&(identical(other.maxPrice, maxPrice) || other.maxPrice == maxPrice)&&(identical(other.verifiedOnly, verifiedOnly) || other.verifiedOnly == verifiedOnly)&&(identical(other.exclusiveOnly, exclusiveOnly) || other.exclusiveOnly == exclusiveOnly)&&(identical(other.featuredOnly, featuredOnly) || other.featuredOnly == featuredOnly)&&(identical(other.searchQuery, searchQuery) || other.searchQuery == searchQuery)&&(identical(other.brand, brand) || other.brand == brand)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.condition, condition) || other.condition == condition)&&(identical(other.minRating, minRating) || other.minRating == minRating)&&(identical(other.location, location) || other.location == location));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_categories),minPrice,maxPrice,verifiedOnly,exclusiveOnly,featuredOnly,searchQuery,brand,sellerId,condition,minRating,location);

@override
String toString() {
  return 'MarketplaceFilter(categories: $categories, minPrice: $minPrice, maxPrice: $maxPrice, verifiedOnly: $verifiedOnly, exclusiveOnly: $exclusiveOnly, featuredOnly: $featuredOnly, searchQuery: $searchQuery, brand: $brand, sellerId: $sellerId, condition: $condition, minRating: $minRating, location: $location)';
}


}

/// @nodoc
abstract mixin class _$MarketplaceFilterCopyWith<$Res> implements $MarketplaceFilterCopyWith<$Res> {
  factory _$MarketplaceFilterCopyWith(_MarketplaceFilter value, $Res Function(_MarketplaceFilter) _then) = __$MarketplaceFilterCopyWithImpl;
@override @useResult
$Res call({
 Set<ProductCategory> categories, double? minPrice, double? maxPrice, bool verifiedOnly, bool exclusiveOnly, bool featuredOnly, String? searchQuery, String? brand, String? sellerId, ProductCondition? condition, double minRating, String? location
});




}
/// @nodoc
class __$MarketplaceFilterCopyWithImpl<$Res>
    implements _$MarketplaceFilterCopyWith<$Res> {
  __$MarketplaceFilterCopyWithImpl(this._self, this._then);

  final _MarketplaceFilter _self;
  final $Res Function(_MarketplaceFilter) _then;

/// Create a copy of MarketplaceFilter
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? categories = null,Object? minPrice = freezed,Object? maxPrice = freezed,Object? verifiedOnly = null,Object? exclusiveOnly = null,Object? featuredOnly = null,Object? searchQuery = freezed,Object? brand = freezed,Object? sellerId = freezed,Object? condition = freezed,Object? minRating = null,Object? location = freezed,}) {
  return _then(_MarketplaceFilter(
categories: null == categories ? _self._categories : categories // ignore: cast_nullable_to_non_nullable
as Set<ProductCategory>,minPrice: freezed == minPrice ? _self.minPrice : minPrice // ignore: cast_nullable_to_non_nullable
as double?,maxPrice: freezed == maxPrice ? _self.maxPrice : maxPrice // ignore: cast_nullable_to_non_nullable
as double?,verifiedOnly: null == verifiedOnly ? _self.verifiedOnly : verifiedOnly // ignore: cast_nullable_to_non_nullable
as bool,exclusiveOnly: null == exclusiveOnly ? _self.exclusiveOnly : exclusiveOnly // ignore: cast_nullable_to_non_nullable
as bool,featuredOnly: null == featuredOnly ? _self.featuredOnly : featuredOnly // ignore: cast_nullable_to_non_nullable
as bool,searchQuery: freezed == searchQuery ? _self.searchQuery : searchQuery // ignore: cast_nullable_to_non_nullable
as String?,brand: freezed == brand ? _self.brand : brand // ignore: cast_nullable_to_non_nullable
as String?,sellerId: freezed == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String?,condition: freezed == condition ? _self.condition : condition // ignore: cast_nullable_to_non_nullable
as ProductCondition?,minRating: null == minRating ? _self.minRating : minRating // ignore: cast_nullable_to_non_nullable
as double,location: freezed == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$ProductReview {

 String get id; String get userId; String get userName; String get userAvatarUrl; String get productId; double get rating; String get comment; DateTime get reviewDate; List<String>? get images;
/// Create a copy of ProductReview
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProductReviewCopyWith<ProductReview> get copyWith => _$ProductReviewCopyWithImpl<ProductReview>(this as ProductReview, _$identity);

  /// Serializes this ProductReview to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProductReview&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.userName, userName) || other.userName == userName)&&(identical(other.userAvatarUrl, userAvatarUrl) || other.userAvatarUrl == userAvatarUrl)&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.rating, rating) || other.rating == rating)&&(identical(other.comment, comment) || other.comment == comment)&&(identical(other.reviewDate, reviewDate) || other.reviewDate == reviewDate)&&const DeepCollectionEquality().equals(other.images, images));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,userName,userAvatarUrl,productId,rating,comment,reviewDate,const DeepCollectionEquality().hash(images));

@override
String toString() {
  return 'ProductReview(id: $id, userId: $userId, userName: $userName, userAvatarUrl: $userAvatarUrl, productId: $productId, rating: $rating, comment: $comment, reviewDate: $reviewDate, images: $images)';
}


}

/// @nodoc
abstract mixin class $ProductReviewCopyWith<$Res>  {
  factory $ProductReviewCopyWith(ProductReview value, $Res Function(ProductReview) _then) = _$ProductReviewCopyWithImpl;
@useResult
$Res call({
 String id, String userId, String userName, String userAvatarUrl, String productId, double rating, String comment, DateTime reviewDate, List<String>? images
});




}
/// @nodoc
class _$ProductReviewCopyWithImpl<$Res>
    implements $ProductReviewCopyWith<$Res> {
  _$ProductReviewCopyWithImpl(this._self, this._then);

  final ProductReview _self;
  final $Res Function(ProductReview) _then;

/// Create a copy of ProductReview
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? userName = null,Object? userAvatarUrl = null,Object? productId = null,Object? rating = null,Object? comment = null,Object? reviewDate = null,Object? images = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,userName: null == userName ? _self.userName : userName // ignore: cast_nullable_to_non_nullable
as String,userAvatarUrl: null == userAvatarUrl ? _self.userAvatarUrl : userAvatarUrl // ignore: cast_nullable_to_non_nullable
as String,productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String,rating: null == rating ? _self.rating : rating // ignore: cast_nullable_to_non_nullable
as double,comment: null == comment ? _self.comment : comment // ignore: cast_nullable_to_non_nullable
as String,reviewDate: null == reviewDate ? _self.reviewDate : reviewDate // ignore: cast_nullable_to_non_nullable
as DateTime,images: freezed == images ? _self.images : images // ignore: cast_nullable_to_non_nullable
as List<String>?,
  ));
}

}


/// Adds pattern-matching-related methods to [ProductReview].
extension ProductReviewPatterns on ProductReview {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ProductReview value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ProductReview() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ProductReview value)  $default,){
final _that = this;
switch (_that) {
case _ProductReview():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ProductReview value)?  $default,){
final _that = this;
switch (_that) {
case _ProductReview() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  String userName,  String userAvatarUrl,  String productId,  double rating,  String comment,  DateTime reviewDate,  List<String>? images)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ProductReview() when $default != null:
return $default(_that.id,_that.userId,_that.userName,_that.userAvatarUrl,_that.productId,_that.rating,_that.comment,_that.reviewDate,_that.images);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  String userName,  String userAvatarUrl,  String productId,  double rating,  String comment,  DateTime reviewDate,  List<String>? images)  $default,) {final _that = this;
switch (_that) {
case _ProductReview():
return $default(_that.id,_that.userId,_that.userName,_that.userAvatarUrl,_that.productId,_that.rating,_that.comment,_that.reviewDate,_that.images);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  String userName,  String userAvatarUrl,  String productId,  double rating,  String comment,  DateTime reviewDate,  List<String>? images)?  $default,) {final _that = this;
switch (_that) {
case _ProductReview() when $default != null:
return $default(_that.id,_that.userId,_that.userName,_that.userAvatarUrl,_that.productId,_that.rating,_that.comment,_that.reviewDate,_that.images);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ProductReview implements ProductReview {
  const _ProductReview({required this.id, required this.userId, required this.userName, required this.userAvatarUrl, required this.productId, required this.rating, required this.comment, required this.reviewDate, final  List<String>? images}): _images = images;
  factory _ProductReview.fromJson(Map<String, dynamic> json) => _$ProductReviewFromJson(json);

@override final  String id;
@override final  String userId;
@override final  String userName;
@override final  String userAvatarUrl;
@override final  String productId;
@override final  double rating;
@override final  String comment;
@override final  DateTime reviewDate;
 final  List<String>? _images;
@override List<String>? get images {
  final value = _images;
  if (value == null) return null;
  if (_images is EqualUnmodifiableListView) return _images;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}


/// Create a copy of ProductReview
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProductReviewCopyWith<_ProductReview> get copyWith => __$ProductReviewCopyWithImpl<_ProductReview>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ProductReviewToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProductReview&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.userName, userName) || other.userName == userName)&&(identical(other.userAvatarUrl, userAvatarUrl) || other.userAvatarUrl == userAvatarUrl)&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.rating, rating) || other.rating == rating)&&(identical(other.comment, comment) || other.comment == comment)&&(identical(other.reviewDate, reviewDate) || other.reviewDate == reviewDate)&&const DeepCollectionEquality().equals(other._images, _images));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,userName,userAvatarUrl,productId,rating,comment,reviewDate,const DeepCollectionEquality().hash(_images));

@override
String toString() {
  return 'ProductReview(id: $id, userId: $userId, userName: $userName, userAvatarUrl: $userAvatarUrl, productId: $productId, rating: $rating, comment: $comment, reviewDate: $reviewDate, images: $images)';
}


}

/// @nodoc
abstract mixin class _$ProductReviewCopyWith<$Res> implements $ProductReviewCopyWith<$Res> {
  factory _$ProductReviewCopyWith(_ProductReview value, $Res Function(_ProductReview) _then) = __$ProductReviewCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, String userName, String userAvatarUrl, String productId, double rating, String comment, DateTime reviewDate, List<String>? images
});




}
/// @nodoc
class __$ProductReviewCopyWithImpl<$Res>
    implements _$ProductReviewCopyWith<$Res> {
  __$ProductReviewCopyWithImpl(this._self, this._then);

  final _ProductReview _self;
  final $Res Function(_ProductReview) _then;

/// Create a copy of ProductReview
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? userName = null,Object? userAvatarUrl = null,Object? productId = null,Object? rating = null,Object? comment = null,Object? reviewDate = null,Object? images = freezed,}) {
  return _then(_ProductReview(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,userName: null == userName ? _self.userName : userName // ignore: cast_nullable_to_non_nullable
as String,userAvatarUrl: null == userAvatarUrl ? _self.userAvatarUrl : userAvatarUrl // ignore: cast_nullable_to_non_nullable
as String,productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String,rating: null == rating ? _self.rating : rating // ignore: cast_nullable_to_non_nullable
as double,comment: null == comment ? _self.comment : comment // ignore: cast_nullable_to_non_nullable
as String,reviewDate: null == reviewDate ? _self.reviewDate : reviewDate // ignore: cast_nullable_to_non_nullable
as DateTime,images: freezed == images ? _self._images : images // ignore: cast_nullable_to_non_nullable
as List<String>?,
  ));
}


}

// dart format on
