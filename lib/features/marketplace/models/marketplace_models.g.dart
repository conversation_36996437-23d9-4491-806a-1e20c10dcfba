// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'marketplace_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Seller _$SellerFromJson(Map<String, dynamic> json) => _Seller(
  id: json['id'] as String,
  name: json['name'] as String,
  avatarUrl: json['avatarUrl'] as String,
  description: json['description'] as String?,
  rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
  reviewCount: (json['reviewCount'] as num?)?.toInt() ?? 0,
  totalSales: (json['totalSales'] as num?)?.toInt() ?? 0,
  isVerified: json['isVerified'] as bool? ?? false,
  isPremium: json['isPremium'] as bool? ?? false,
  location: json['location'] as String?,
  joinedDate: json['joinedDate'] == null
      ? null
      : DateTime.parse(json['joinedDate'] as String),
  specializations:
      (json['specializations'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
);

Map<String, dynamic> _$SellerToJson(_Seller instance) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'avatarUrl': instance.avatarUrl,
  'description': instance.description,
  'rating': instance.rating,
  'reviewCount': instance.reviewCount,
  'totalSales': instance.totalSales,
  'isVerified': instance.isVerified,
  'isPremium': instance.isPremium,
  'location': instance.location,
  'joinedDate': instance.joinedDate?.toIso8601String(),
  'specializations': instance.specializations,
};

_MarketplaceItem _$MarketplaceItemFromJson(Map<String, dynamic> json) =>
    _MarketplaceItem(
      id: json['id'] as String,
      name: json['name'] as String,
      price: (json['price'] as num).toDouble(),
      category: $enumDecode(_$ProductCategoryEnumMap, json['category']),
      images: (json['images'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      sellerId: json['sellerId'] as String,
      seller: Seller.fromJson(json['seller'] as Map<String, dynamic>),
      description: json['description'] as String?,
      isVerified: json['isVerified'] as bool? ?? false,
      isExclusive: json['isExclusive'] as bool? ?? false,
      isFeatured: json['isFeatured'] as bool? ?? false,
      condition:
          $enumDecodeNullable(_$ProductConditionEnumMap, json['condition']) ??
          ProductCondition.brandNew,
      availableQuantity: (json['availableQuantity'] as num?)?.toInt() ?? 1,
      soldQuantity: (json['soldQuantity'] as num?)?.toInt() ?? 0,
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      reviewCount: (json['reviewCount'] as num?)?.toInt() ?? 0,
      brand: json['brand'] as String?,
      model: json['model'] as String?,
      year: json['year'] as String?,
      location: json['location'] as String?,
      specifications: json['specifications'] as Map<String, dynamic>?,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$MarketplaceItemToJson(_MarketplaceItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'price': instance.price,
      'category': _$ProductCategoryEnumMap[instance.category]!,
      'images': instance.images,
      'sellerId': instance.sellerId,
      'seller': instance.seller,
      'description': instance.description,
      'isVerified': instance.isVerified,
      'isExclusive': instance.isExclusive,
      'isFeatured': instance.isFeatured,
      'condition': _$ProductConditionEnumMap[instance.condition]!,
      'availableQuantity': instance.availableQuantity,
      'soldQuantity': instance.soldQuantity,
      'rating': instance.rating,
      'reviewCount': instance.reviewCount,
      'brand': instance.brand,
      'model': instance.model,
      'year': instance.year,
      'location': instance.location,
      'specifications': instance.specifications,
      'tags': instance.tags,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

const _$ProductCategoryEnumMap = {
  ProductCategory.watches: 'watches',
  ProductCategory.art: 'art',
  ProductCategory.realEstate: 'realEstate',
  ProductCategory.vehicles: 'vehicles',
  ProductCategory.nfts: 'nfts',
  ProductCategory.collectibles: 'collectibles',
  ProductCategory.designerItems: 'designerItems',
  ProductCategory.jewelry: 'jewelry',
  ProductCategory.wine: 'wine',
  ProductCategory.yachts: 'yachts',
  ProductCategory.privateJets: 'privateJets',
  ProductCategory.luxuryCars: 'luxuryCars',
  ProductCategory.antiques: 'antiques',
  ProductCategory.rareBooks: 'rareBooks',
  ProductCategory.sportsMemorabilia: 'sportsMemorabilia',
};

const _$ProductConditionEnumMap = {
  ProductCondition.brandNew: 'brandNew',
  ProductCondition.likeNew: 'likeNew',
  ProductCondition.excellent: 'excellent',
  ProductCondition.good: 'good',
  ProductCondition.fair: 'fair',
  ProductCondition.vintage: 'vintage',
  ProductCondition.antique: 'antique',
};

_CartItem _$CartItemFromJson(Map<String, dynamic> json) => _CartItem(
  id: json['id'] as String,
  itemId: json['itemId'] as String,
  item: MarketplaceItem.fromJson(json['item'] as Map<String, dynamic>),
  quantity: (json['quantity'] as num).toInt(),
  addedAt: DateTime.parse(json['addedAt'] as String),
);

Map<String, dynamic> _$CartItemToJson(_CartItem instance) => <String, dynamic>{
  'id': instance.id,
  'itemId': instance.itemId,
  'item': instance.item,
  'quantity': instance.quantity,
  'addedAt': instance.addedAt.toIso8601String(),
};

_Cart _$CartFromJson(Map<String, dynamic> json) => _Cart(
  id: json['id'] as String,
  userId: json['userId'] as String,
  items:
      (json['items'] as List<dynamic>?)
          ?.map((e) => CartItem.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  subtotal: (json['subtotal'] as num?)?.toDouble() ?? 0.0,
  tax: (json['tax'] as num?)?.toDouble() ?? 0.0,
  shipping: (json['shipping'] as num?)?.toDouble() ?? 0.0,
  total: (json['total'] as num?)?.toDouble() ?? 0.0,
  lastUpdated: json['lastUpdated'] == null
      ? null
      : DateTime.parse(json['lastUpdated'] as String),
);

Map<String, dynamic> _$CartToJson(_Cart instance) => <String, dynamic>{
  'id': instance.id,
  'userId': instance.userId,
  'items': instance.items,
  'subtotal': instance.subtotal,
  'tax': instance.tax,
  'shipping': instance.shipping,
  'total': instance.total,
  'lastUpdated': instance.lastUpdated?.toIso8601String(),
};

_Order _$OrderFromJson(Map<String, dynamic> json) => _Order(
  id: json['id'] as String,
  userId: json['userId'] as String,
  sellerId: json['sellerId'] as String,
  items: (json['items'] as List<dynamic>)
      .map((e) => CartItem.fromJson(e as Map<String, dynamic>))
      .toList(),
  subtotal: (json['subtotal'] as num).toDouble(),
  tax: (json['tax'] as num).toDouble(),
  shipping: (json['shipping'] as num).toDouble(),
  total: (json['total'] as num).toDouble(),
  status: $enumDecode(_$OrderStatusEnumMap, json['status']),
  paymentStatus: $enumDecode(_$PaymentStatusEnumMap, json['paymentStatus']),
  orderDate: DateTime.parse(json['orderDate'] as String),
  shippedDate: json['shippedDate'] == null
      ? null
      : DateTime.parse(json['shippedDate'] as String),
  deliveredDate: json['deliveredDate'] == null
      ? null
      : DateTime.parse(json['deliveredDate'] as String),
  trackingNumber: json['trackingNumber'] as String?,
  shippingAddress: json['shippingAddress'] as String?,
  billingAddress: json['billingAddress'] as String?,
  notes: json['notes'] as String?,
);

Map<String, dynamic> _$OrderToJson(_Order instance) => <String, dynamic>{
  'id': instance.id,
  'userId': instance.userId,
  'sellerId': instance.sellerId,
  'items': instance.items,
  'subtotal': instance.subtotal,
  'tax': instance.tax,
  'shipping': instance.shipping,
  'total': instance.total,
  'status': _$OrderStatusEnumMap[instance.status]!,
  'paymentStatus': _$PaymentStatusEnumMap[instance.paymentStatus]!,
  'orderDate': instance.orderDate.toIso8601String(),
  'shippedDate': instance.shippedDate?.toIso8601String(),
  'deliveredDate': instance.deliveredDate?.toIso8601String(),
  'trackingNumber': instance.trackingNumber,
  'shippingAddress': instance.shippingAddress,
  'billingAddress': instance.billingAddress,
  'notes': instance.notes,
};

const _$OrderStatusEnumMap = {
  OrderStatus.pending: 'pending',
  OrderStatus.confirmed: 'confirmed',
  OrderStatus.processing: 'processing',
  OrderStatus.shipped: 'shipped',
  OrderStatus.delivered: 'delivered',
  OrderStatus.cancelled: 'cancelled',
  OrderStatus.refunded: 'refunded',
};

const _$PaymentStatusEnumMap = {
  PaymentStatus.pending: 'pending',
  PaymentStatus.paid: 'paid',
  PaymentStatus.failed: 'failed',
  PaymentStatus.refunded: 'refunded',
};

_CheckoutRequest _$CheckoutRequestFromJson(Map<String, dynamic> json) =>
    _CheckoutRequest(
      cartId: json['cartId'] as String,
      shippingAddress: json['shippingAddress'] as String,
      billingAddress: json['billingAddress'] as String,
      notes: json['notes'] as String?,
      promoCode: json['promoCode'] as String?,
    );

Map<String, dynamic> _$CheckoutRequestToJson(_CheckoutRequest instance) =>
    <String, dynamic>{
      'cartId': instance.cartId,
      'shippingAddress': instance.shippingAddress,
      'billingAddress': instance.billingAddress,
      'notes': instance.notes,
      'promoCode': instance.promoCode,
    };

_MarketplaceFilter _$MarketplaceFilterFromJson(Map<String, dynamic> json) =>
    _MarketplaceFilter(
      categories:
          (json['categories'] as List<dynamic>?)
              ?.map((e) => $enumDecode(_$ProductCategoryEnumMap, e))
              .toSet() ??
          const {},
      minPrice: (json['minPrice'] as num?)?.toDouble(),
      maxPrice: (json['maxPrice'] as num?)?.toDouble(),
      verifiedOnly: json['verifiedOnly'] as bool? ?? false,
      exclusiveOnly: json['exclusiveOnly'] as bool? ?? false,
      featuredOnly: json['featuredOnly'] as bool? ?? false,
      searchQuery: json['searchQuery'] as String?,
      brand: json['brand'] as String?,
      sellerId: json['sellerId'] as String?,
      condition: $enumDecodeNullable(
        _$ProductConditionEnumMap,
        json['condition'],
      ),
      minRating: (json['minRating'] as num?)?.toDouble() ?? 0.0,
      location: json['location'] as String?,
    );

Map<String, dynamic> _$MarketplaceFilterToJson(_MarketplaceFilter instance) =>
    <String, dynamic>{
      'categories': instance.categories
          .map((e) => _$ProductCategoryEnumMap[e]!)
          .toList(),
      'minPrice': instance.minPrice,
      'maxPrice': instance.maxPrice,
      'verifiedOnly': instance.verifiedOnly,
      'exclusiveOnly': instance.exclusiveOnly,
      'featuredOnly': instance.featuredOnly,
      'searchQuery': instance.searchQuery,
      'brand': instance.brand,
      'sellerId': instance.sellerId,
      'condition': _$ProductConditionEnumMap[instance.condition],
      'minRating': instance.minRating,
      'location': instance.location,
    };

_ProductReview _$ProductReviewFromJson(Map<String, dynamic> json) =>
    _ProductReview(
      id: json['id'] as String,
      userId: json['userId'] as String,
      userName: json['userName'] as String,
      userAvatarUrl: json['userAvatarUrl'] as String,
      productId: json['productId'] as String,
      rating: (json['rating'] as num).toDouble(),
      comment: json['comment'] as String,
      reviewDate: DateTime.parse(json['reviewDate'] as String),
      images: (json['images'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$ProductReviewToJson(_ProductReview instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'userName': instance.userName,
      'userAvatarUrl': instance.userAvatarUrl,
      'productId': instance.productId,
      'rating': instance.rating,
      'comment': instance.comment,
      'reviewDate': instance.reviewDate.toIso8601String(),
      'images': instance.images,
    };
