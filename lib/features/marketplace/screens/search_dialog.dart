import 'package:flutter/material.dart';
import '../models/marketplace_models.dart';

class SearchDialog extends StatefulWidget {
  final Function(String) onSearch;

  const SearchDialog({super.key, required this.onSearch});

  @override
  State<SearchDialog> createState() => _SearchDialogState();
}

class _SearchDialogState extends State<SearchDialog> {
  final _searchController = TextEditingController();
  ProductCategory? _selectedCategory;
  String? _selectedBrand;
  ProductCondition? _selectedCondition;
  bool _verifiedOnly = false;
  bool _exclusiveOnly = false;

  final List<String> _brands = [
    'Rolex',
    'Patek Philippe',
    'Audemars Piguet',
    'Cartier',
    'Omega',
    'Ferrari',
    'Lamborghini',
    'Bentley',
    'Rolls Royce',
    'Bored Ape Yacht Club',
    'CryptoPunks',
  ];

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _performSearch() {
    final query = _searchController.text.trim();
    if (query.isNotEmpty) {
      widget.onSearch(query);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: double.maxFinite,
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Search Products',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Search Input
            TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search products, brands, or descriptions...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: IconButton(
                  onPressed: _performSearch,
                  icon: const Icon(Icons.arrow_forward),
                ),
                border: const OutlineInputBorder(),
              ),
              onSubmitted: (_) => _performSearch(),
            ),

            const SizedBox(height: 16),

            // Filters
            Text(
              'Filters',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),

            // Category Filter
            DropdownButtonFormField<ProductCategory?>(
              value: _selectedCategory,
              decoration: const InputDecoration(
                labelText: 'Category',
                border: OutlineInputBorder(),
              ),
              items: [
                const DropdownMenuItem(
                  value: null,
                  child: Text('All Categories'),
                ),
                ...ProductCategory.values.map(
                  (category) => DropdownMenuItem(
                    value: category,
                    child: Text(
                      category.name.replaceAll('_', ' ').toUpperCase(),
                    ),
                  ),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedCategory = value;
                });
              },
            ),

            const SizedBox(height: 12),

            // Brand Filter
            DropdownButtonFormField<String?>(
              value: _selectedBrand,
              decoration: const InputDecoration(
                labelText: 'Brand',
                border: OutlineInputBorder(),
              ),
              items: [
                const DropdownMenuItem(value: null, child: Text('All Brands')),
                ..._brands.map(
                  (brand) => DropdownMenuItem(value: brand, child: Text(brand)),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedBrand = value;
                });
              },
            ),

            const SizedBox(height: 12),

            // Condition Filter
            DropdownButtonFormField<ProductCondition?>(
              value: _selectedCondition,
              decoration: const InputDecoration(
                labelText: 'Condition',
                border: OutlineInputBorder(),
              ),
              items: [
                const DropdownMenuItem(
                  value: null,
                  child: Text('Any Condition'),
                ),
                ...ProductCondition.values.map(
                  (condition) => DropdownMenuItem(
                    value: condition,
                    child: Text(
                      condition.name.replaceAll('_', ' ').toUpperCase(),
                    ),
                  ),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedCondition = value;
                });
              },
            ),

            const SizedBox(height: 12),

            // Checkboxes
            CheckboxListTile(
              title: const Text('Verified Only'),
              value: _verifiedOnly,
              onChanged: (value) {
                setState(() {
                  _verifiedOnly = value ?? false;
                });
              },
              controlAffinity: ListTileControlAffinity.leading,
            ),

            CheckboxListTile(
              title: const Text('Exclusive Only'),
              value: _exclusiveOnly,
              onChanged: (value) {
                setState(() {
                  _exclusiveOnly = value ?? false;
                });
              },
              controlAffinity: ListTileControlAffinity.leading,
            ),

            const SizedBox(height: 20),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      setState(() {
                        _searchController.clear();
                        _selectedCategory = null;
                        _selectedBrand = null;
                        _selectedCondition = null;
                        _verifiedOnly = false;
                        _exclusiveOnly = false;
                      });
                    },
                    child: const Text('Clear'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _performSearch,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Search'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
