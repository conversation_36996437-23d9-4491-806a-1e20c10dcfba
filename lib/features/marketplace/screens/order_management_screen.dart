import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class OrderManagementScreen extends StatefulWidget {
  const OrderManagementScreen({super.key});

  @override
  State<OrderManagementScreen> createState() => _OrderManagementScreenState();
}

class _OrderManagementScreenState extends State<OrderManagementScreen> {
  final List<Order> _orders = [
    Order(
      id: 'ORD-001',
      buyer: 'Luxury Buyer',
      date: DateTime.now().subtract(const Duration(days: 1)),
      status: OrderStatus.pending,
      total: 25000000,
      product: 'Private Jet',
    ),
    Order(
      id: 'ORD-002',
      buyer: 'Elite Collector',
      date: DateTime.now().subtract(const Duration(days: 3)),
      status: OrderStatus.shipped,
      total: 80000000,
      product: 'Superyacht',
    ),
    Order(
      id: 'ORD-003',
      buyer: 'Art Enthusiast',
      date: DateTime.now().subtract(const Duration(days: 7)),
      status: OrderStatus.delivered,
      total: 3500000,
      product: 'Rare Art Piece',
    ),
    Order(
      id: 'ORD-004',
      buyer: 'Villa Owner',
      date: DateTime.now().subtract(const Duration(days: 2)),
      status: OrderStatus.cancelled,
      total: 12000000,
      product: 'Luxury Villa',
    ),
  ];

  String _searchQuery = '';
  OrderStatus? _selectedStatus;

  @override
  Widget build(BuildContext context) {
    final filteredOrders = _orders.where((order) {
      final matchesStatus =
          _selectedStatus == null || order.status == _selectedStatus;
      final matchesQuery =
          _searchQuery.isEmpty ||
          order.id.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          order.buyer.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          order.product.toLowerCase().contains(_searchQuery.toLowerCase());
      return matchesStatus && matchesQuery;
    }).toList();

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        title: const Text(
          'Order Management',
          style: TextStyle(color: Colors.white),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list, color: Colors.white),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Search bar
            TextField(
              style: const TextStyle(color: Colors.white),
              decoration: InputDecoration(
                hintText: 'Search orders... (ID, buyer, product)',
                hintStyle: TextStyle(color: Colors.grey[500]),
                prefixIcon: const Icon(Icons.search, color: Colors.white54),
                filled: true,
                fillColor: Colors.grey[900],
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(30),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(
                  vertical: 0,
                  horizontal: 16,
                ),
              ),
              onChanged: (value) => setState(() => _searchQuery = value),
            ),
            const SizedBox(height: 20),
            // Orders list
            Expanded(
              child: filteredOrders.isEmpty
                  ? const Center(
                      child: Text(
                        'No orders found.',
                        style: TextStyle(color: Colors.white54),
                      ),
                    )
                  : ListView.separated(
                      itemCount: filteredOrders.length,
                      separatorBuilder: (_, _) => const SizedBox(height: 16),
                      itemBuilder: (context, index) {
                        final order = filteredOrders[index];
                        return _buildOrderTile(order);
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderTile(Order order) {
    return GestureDetector(
      onTap: () => _showOrderDetails(order),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey[900],
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.white12),
        ),
        child: Row(
          children: [
            Icon(
              _getStatusIcon(order.status),
              color: _getStatusColor(order.status),
              size: 32,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    order.product,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Order: ${order.id} • Buyer: ${order.buyer}',
                    style: TextStyle(color: Colors.grey[400], fontSize: 13),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    'Total: \$${order.total.toStringAsFixed(0)}',
                    style: TextStyle(
                      color: Colors.amber[400],
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 12),
            Text(
              _statusText(order.status),
              style: TextStyle(
                color: _getStatusColor(order.status),
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showOrderDetails(Order order) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => _buildOrderDetailsModal(order),
    );
  }

  Widget _buildOrderDetailsModal(Order order) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
            child: Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                color: Colors.grey[700],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          Row(
            children: [
              Icon(
                _getStatusIcon(order.status),
                color: _getStatusColor(order.status),
                size: 32,
              ),
              const SizedBox(width: 12),
              Text(
                _statusText(order.status),
                style: TextStyle(
                  color: _getStatusColor(order.status),
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
              const Spacer(),
              Text(
                order.id,
                style: const TextStyle(color: Colors.white54, fontSize: 14),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Text(
            'Product: ${order.product}',
            style: const TextStyle(color: Colors.white, fontSize: 16),
          ),
          const SizedBox(height: 8),
          Text(
            'Buyer: ${order.buyer}',
            style: TextStyle(color: Colors.grey[400], fontSize: 15),
          ),
          const SizedBox(height: 8),
          Text(
            'Order Date: ${order.date.toLocal().toString().split(' ')[0]}',
            style: TextStyle(color: Colors.grey[400], fontSize: 15),
          ),
          const SizedBox(height: 8),
          Text(
            'Total: \$${order.total.toStringAsFixed(0)}',
            style: TextStyle(
              color: Colors.amber[400],
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),
          if (order.status == OrderStatus.pending)
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 14),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                    ),
                    onPressed: () {},
                    child: const Text('Mark as Shipped'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton(
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.red,
                      side: const BorderSide(color: Colors.red),
                      padding: const EdgeInsets.symmetric(vertical: 14),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                    ),
                    onPressed: () {},
                    child: const Text('Cancel Order'),
                  ),
                ),
              ],
            ),
          if (order.status == OrderStatus.shipped)
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.amber[700],
                foregroundColor: Colors.black,
                padding: const EdgeInsets.symmetric(vertical: 14),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
              ),
              onPressed: () {},
              child: const Text('Mark as Delivered'),
            ),
          if (order.status == OrderStatus.delivered)
            const Text(
              'Order delivered.',
              style: TextStyle(
                color: Colors.green,
                fontWeight: FontWeight.bold,
              ),
            ),
          if (order.status == OrderStatus.cancelled)
            const Text(
              'Order cancelled.',
              style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
            ),
          const SizedBox(height: 12),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: Colors.grey[900],
          title: const Text(
            'Filter by Status',
            style: TextStyle(color: Colors.white),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildStatusFilterTile(null, 'All'),
              _buildStatusFilterTile(OrderStatus.pending, 'Pending'),
              _buildStatusFilterTile(OrderStatus.shipped, 'Shipped'),
              _buildStatusFilterTile(OrderStatus.delivered, 'Delivered'),
              _buildStatusFilterTile(OrderStatus.cancelled, 'Cancelled'),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatusFilterTile(OrderStatus? status, String label) {
    final isSelected = _selectedStatus == status;
    return ListTile(
      title: Text(
        label,
        style: TextStyle(color: isSelected ? Colors.amber[400] : Colors.white),
      ),
      leading: isSelected
          ? const Icon(Icons.check_circle, color: Colors.amber)
          : const Icon(Icons.circle_outlined, color: Colors.white54),
      onTap: () {
        setState(() => _selectedStatus = status);
        Navigator.of(context).pop();
      },
    );
  }

  IconData _getStatusIcon(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return FontAwesomeIcons.clock;
      case OrderStatus.shipped:
        return FontAwesomeIcons.truck;
      case OrderStatus.delivered:
        return FontAwesomeIcons.circleCheck;
      case OrderStatus.cancelled:
        return FontAwesomeIcons.circleXmark;
    }
  }

  Color _getStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return Colors.amber;
      case OrderStatus.shipped:
        return Colors.blueAccent;
      case OrderStatus.delivered:
        return Colors.green;
      case OrderStatus.cancelled:
        return Colors.red;
    }
  }

  String _statusText(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 'Pending';
      case OrderStatus.shipped:
        return 'Shipped';
      case OrderStatus.delivered:
        return 'Delivered';
      case OrderStatus.cancelled:
        return 'Cancelled';
    }
  }
}

class Order {
  final String id;
  final String buyer;
  final DateTime date;
  final OrderStatus status;
  final double total;
  final String product;

  const Order({
    required this.id,
    required this.buyer,
    required this.date,
    required this.status,
    required this.total,
    required this.product,
  });
}

enum OrderStatus { pending, shipped, delivered, cancelled }
