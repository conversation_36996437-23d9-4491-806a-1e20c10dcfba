// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'events_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$eventsServiceHash() => r'cf2e11772bd5f30aeb80766605af25adbc77728f';

/// See also [eventsService].
@ProviderFor(eventsService)
final eventsServiceProvider = AutoDisposeProvider<EventsService>.internal(
  eventsService,
  name: r'eventsServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$eventsServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef EventsServiceRef = AutoDisposeProviderRef<EventsService>;
String _$eventByIdHash() => r'8a99755eba76032c7584372185b3bd45a6571e2d';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [eventById].
@ProviderFor(eventById)
const eventByIdProvider = EventByIdFamily();

/// See also [eventById].
class EventByIdFamily extends Family<AsyncValue<EventModel?>> {
  /// See also [eventById].
  const EventByIdFamily();

  /// See also [eventById].
  EventByIdProvider call(String eventId) {
    return EventByIdProvider(eventId);
  }

  @override
  EventByIdProvider getProviderOverride(covariant EventByIdProvider provider) {
    return call(provider.eventId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'eventByIdProvider';
}

/// See also [eventById].
class EventByIdProvider extends AutoDisposeFutureProvider<EventModel?> {
  /// See also [eventById].
  EventByIdProvider(String eventId)
    : this._internal(
        (ref) => eventById(ref as EventByIdRef, eventId),
        from: eventByIdProvider,
        name: r'eventByIdProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$eventByIdHash,
        dependencies: EventByIdFamily._dependencies,
        allTransitiveDependencies: EventByIdFamily._allTransitiveDependencies,
        eventId: eventId,
      );

  EventByIdProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.eventId,
  }) : super.internal();

  final String eventId;

  @override
  Override overrideWith(
    FutureOr<EventModel?> Function(EventByIdRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: EventByIdProvider._internal(
        (ref) => create(ref as EventByIdRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        eventId: eventId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<EventModel?> createElement() {
    return _EventByIdProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is EventByIdProvider && other.eventId == eventId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, eventId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin EventByIdRef on AutoDisposeFutureProviderRef<EventModel?> {
  /// The parameter `eventId` of this provider.
  String get eventId;
}

class _EventByIdProviderElement
    extends AutoDisposeFutureProviderElement<EventModel?>
    with EventByIdRef {
  _EventByIdProviderElement(super.provider);

  @override
  String get eventId => (origin as EventByIdProvider).eventId;
}

String _$eventAttendeesHash() => r'b4a970c6f5d803edfcaaf5311bdd6d94d359ac85';

/// See also [eventAttendees].
@ProviderFor(eventAttendees)
const eventAttendeesProvider = EventAttendeesFamily();

/// See also [eventAttendees].
class EventAttendeesFamily
    extends Family<AsyncValue<List<Map<String, dynamic>>>> {
  /// See also [eventAttendees].
  const EventAttendeesFamily();

  /// See also [eventAttendees].
  EventAttendeesProvider call(String eventId) {
    return EventAttendeesProvider(eventId);
  }

  @override
  EventAttendeesProvider getProviderOverride(
    covariant EventAttendeesProvider provider,
  ) {
    return call(provider.eventId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'eventAttendeesProvider';
}

/// See also [eventAttendees].
class EventAttendeesProvider
    extends AutoDisposeFutureProvider<List<Map<String, dynamic>>> {
  /// See also [eventAttendees].
  EventAttendeesProvider(String eventId)
    : this._internal(
        (ref) => eventAttendees(ref as EventAttendeesRef, eventId),
        from: eventAttendeesProvider,
        name: r'eventAttendeesProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$eventAttendeesHash,
        dependencies: EventAttendeesFamily._dependencies,
        allTransitiveDependencies:
            EventAttendeesFamily._allTransitiveDependencies,
        eventId: eventId,
      );

  EventAttendeesProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.eventId,
  }) : super.internal();

  final String eventId;

  @override
  Override overrideWith(
    FutureOr<List<Map<String, dynamic>>> Function(EventAttendeesRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: EventAttendeesProvider._internal(
        (ref) => create(ref as EventAttendeesRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        eventId: eventId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<Map<String, dynamic>>> createElement() {
    return _EventAttendeesProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is EventAttendeesProvider && other.eventId == eventId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, eventId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin EventAttendeesRef
    on AutoDisposeFutureProviderRef<List<Map<String, dynamic>>> {
  /// The parameter `eventId` of this provider.
  String get eventId;
}

class _EventAttendeesProviderElement
    extends AutoDisposeFutureProviderElement<List<Map<String, dynamic>>>
    with EventAttendeesRef {
  _EventAttendeesProviderElement(super.provider);

  @override
  String get eventId => (origin as EventAttendeesProvider).eventId;
}

String _$userAttendanceStatusHash() =>
    r'6c789123d26ab4671d55668554204c8dddda5e80';

/// See also [userAttendanceStatus].
@ProviderFor(userAttendanceStatus)
const userAttendanceStatusProvider = UserAttendanceStatusFamily();

/// See also [userAttendanceStatus].
class UserAttendanceStatusFamily extends Family<AsyncValue<bool>> {
  /// See also [userAttendanceStatus].
  const UserAttendanceStatusFamily();

  /// See also [userAttendanceStatus].
  UserAttendanceStatusProvider call(String eventId) {
    return UserAttendanceStatusProvider(eventId);
  }

  @override
  UserAttendanceStatusProvider getProviderOverride(
    covariant UserAttendanceStatusProvider provider,
  ) {
    return call(provider.eventId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'userAttendanceStatusProvider';
}

/// See also [userAttendanceStatus].
class UserAttendanceStatusProvider extends AutoDisposeFutureProvider<bool> {
  /// See also [userAttendanceStatus].
  UserAttendanceStatusProvider(String eventId)
    : this._internal(
        (ref) => userAttendanceStatus(ref as UserAttendanceStatusRef, eventId),
        from: userAttendanceStatusProvider,
        name: r'userAttendanceStatusProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$userAttendanceStatusHash,
        dependencies: UserAttendanceStatusFamily._dependencies,
        allTransitiveDependencies:
            UserAttendanceStatusFamily._allTransitiveDependencies,
        eventId: eventId,
      );

  UserAttendanceStatusProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.eventId,
  }) : super.internal();

  final String eventId;

  @override
  Override overrideWith(
    FutureOr<bool> Function(UserAttendanceStatusRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: UserAttendanceStatusProvider._internal(
        (ref) => create(ref as UserAttendanceStatusRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        eventId: eventId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<bool> createElement() {
    return _UserAttendanceStatusProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is UserAttendanceStatusProvider && other.eventId == eventId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, eventId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin UserAttendanceStatusRef on AutoDisposeFutureProviderRef<bool> {
  /// The parameter `eventId` of this provider.
  String get eventId;
}

class _UserAttendanceStatusProviderElement
    extends AutoDisposeFutureProviderElement<bool>
    with UserAttendanceStatusRef {
  _UserAttendanceStatusProviderElement(super.provider);

  @override
  String get eventId => (origin as UserAttendanceStatusProvider).eventId;
}

String _$eventFilterHash() => r'aa35d0cc9b642fc6afea909b3f7f94e6e1e2da81';

/// See also [EventFilter].
@ProviderFor(EventFilter)
final eventFilterProvider =
    AutoDisposeNotifierProvider<EventFilter, EventFilterState>.internal(
      EventFilter.new,
      name: r'eventFilterProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$eventFilterHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$EventFilter = AutoDisposeNotifier<EventFilterState>;
String _$eventStateHash() => r'87368409cf96c4ba89619da5a2142a653d2b490d';

/// See also [EventState].
@ProviderFor(EventState)
final eventStateProvider =
    AutoDisposeAsyncNotifierProvider<EventState, List<EventModel>>.internal(
      EventState.new,
      name: r'eventStateProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$eventStateHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$EventState = AutoDisposeAsyncNotifier<List<EventModel>>;
String _$eventsListHash() => r'7509f1028631b55e00219b553c796515a99496fc';

abstract class _$EventsList
    extends BuildlessAutoDisposeAsyncNotifier<List<EventModel>> {
  late final EventFilterState filter;

  FutureOr<List<EventModel>> build(EventFilterState filter);
}

/// See also [EventsList].
@ProviderFor(EventsList)
const eventsListProvider = EventsListFamily();

/// See also [EventsList].
class EventsListFamily extends Family<AsyncValue<List<EventModel>>> {
  /// See also [EventsList].
  const EventsListFamily();

  /// See also [EventsList].
  EventsListProvider call(EventFilterState filter) {
    return EventsListProvider(filter);
  }

  @override
  EventsListProvider getProviderOverride(
    covariant EventsListProvider provider,
  ) {
    return call(provider.filter);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'eventsListProvider';
}

/// See also [EventsList].
class EventsListProvider
    extends AutoDisposeAsyncNotifierProviderImpl<EventsList, List<EventModel>> {
  /// See also [EventsList].
  EventsListProvider(EventFilterState filter)
    : this._internal(
        () => EventsList()..filter = filter,
        from: eventsListProvider,
        name: r'eventsListProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$eventsListHash,
        dependencies: EventsListFamily._dependencies,
        allTransitiveDependencies: EventsListFamily._allTransitiveDependencies,
        filter: filter,
      );

  EventsListProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.filter,
  }) : super.internal();

  final EventFilterState filter;

  @override
  FutureOr<List<EventModel>> runNotifierBuild(covariant EventsList notifier) {
    return notifier.build(filter);
  }

  @override
  Override overrideWith(EventsList Function() create) {
    return ProviderOverride(
      origin: this,
      override: EventsListProvider._internal(
        () => create()..filter = filter,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        filter: filter,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<EventsList, List<EventModel>>
  createElement() {
    return _EventsListProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is EventsListProvider && other.filter == filter;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, filter.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin EventsListRef on AutoDisposeAsyncNotifierProviderRef<List<EventModel>> {
  /// The parameter `filter` of this provider.
  EventFilterState get filter;
}

class _EventsListProviderElement
    extends
        AutoDisposeAsyncNotifierProviderElement<EventsList, List<EventModel>>
    with EventsListRef {
  _EventsListProviderElement(super.provider);

  @override
  EventFilterState get filter => (origin as EventsListProvider).filter;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
