import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../models/event_model.dart';
import '../services/events_service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';

part 'events_provider.g.dart';

@riverpod
EventsService eventsService(Ref ref) {
  return EventsService();
}

enum EventFilterState { all, upcoming, past, hosted, attending }

@riverpod
class EventFilter extends _$EventFilter {
  @override
  EventFilterState build() => EventFilterState.all;

  void setFilter(EventFilterState filter) {
    state = filter;
  }
}

@riverpod
class EventState extends _$EventState {
  @override
  Future<List<EventModel>> build() async {
    final filter = ref.watch(eventFilterProvider);
    final eventsService = ref.watch(eventsServiceProvider);
    final currentUser = FirebaseAuth.instance.currentUser;

    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    switch (filter) {
      case EventFilterState.upcoming:
        return eventsService.getUpcomingEvents();
      case EventFilterState.past:
        return eventsService.getPastEvents();
      case EventFilterState.hosted:
        return eventsService.getEventsByHost(currentUser.uid);
      case EventFilterState.attending:
        return eventsService.getEventsByAttendee(currentUser.uid);
      case EventFilterState.all:
        return eventsService.getEvents();
    }
  }

  Future<void> refresh() async {
    ref.invalidateSelf();
  }

  Future<void> createEvent(EventModel event) async {
    final eventsService = ref.read(eventsServiceProvider);
    await eventsService.createEvent(event);
    ref.invalidateSelf();
  }

  Future<bool> rsvp(String eventId) async {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    final eventsService = ref.read(eventsServiceProvider);
    final success = await eventsService.rsvpToEvent(eventId, currentUser.uid);
    if (success) {
      ref.invalidateSelf();
    }
    return success;
  }

  Future<bool> cancelRsvp(String eventId) async {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    final eventsService = ref.read(eventsServiceProvider);
    final success = await eventsService.cancelRsvp(eventId, currentUser.uid);
    if (success) {
      ref.invalidateSelf();
    }
    return success;
  }

  Future<bool> isAttending(String eventId) async {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) {
      return false;
    }

    final eventsService = ref.read(eventsServiceProvider);
    return eventsService.isUserAttending(eventId, currentUser.uid);
  }
}

@riverpod
class EventsList extends _$EventsList {
  @override
  Future<List<EventModel>> build(EventFilterState filter) async {
    final eventsService = ref.watch(eventsServiceProvider);
    final currentUser = FirebaseAuth.instance.currentUser;

    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    switch (filter) {
      case EventFilterState.upcoming:
        return eventsService.getUpcomingEvents();
      case EventFilterState.past:
        return eventsService.getPastEvents();
      case EventFilterState.hosted:
        return eventsService.getEventsByHost(currentUser.uid);
      case EventFilterState.attending:
        return eventsService.getEventsByAttendee(currentUser.uid);
      case EventFilterState.all:
        return eventsService.getEvents();
    }
  }

  Future<void> createEvent(EventModel event) async {
    final eventsService = ref.read(eventsServiceProvider);
    await eventsService.createEvent(event);
    ref.invalidateSelf();
  }

  Future<bool> rsvp(String eventId) async {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    final eventsService = ref.read(eventsServiceProvider);
    final success = await eventsService.rsvpToEvent(eventId, currentUser.uid);
    if (success) {
      ref.invalidateSelf();
    }
    return success;
  }

  Future<bool> cancelRsvp(String eventId) async {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    final eventsService = ref.read(eventsServiceProvider);
    final success = await eventsService.cancelRsvp(eventId, currentUser.uid);
    if (success) {
      ref.invalidateSelf();
    }
    return success;
  }
}

// Provider for individual event
@riverpod
Future<EventModel?> eventById(Ref ref, String eventId) async {
  final eventsService = ref.watch(eventsServiceProvider);
  return eventsService.getEventById(eventId);
}

// Provider for event attendees
@riverpod
Future<List<Map<String, dynamic>>> eventAttendees(
  Ref ref,
  String eventId,
) async {
  final eventsService = ref.watch(eventsServiceProvider);
  return eventsService.getEventAttendees(eventId);
}

// Provider for user attendance status
@riverpod
Future<bool> userAttendanceStatus(Ref ref, String eventId) async {
  final eventsService = ref.watch(eventsServiceProvider);
  final currentUser = FirebaseAuth.instance.currentUser;

  if (currentUser == null) {
    return false;
  }

  return eventsService.isUserAttending(eventId, currentUser.uid);
}
