import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/event_model.dart';

class EventsService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get all events
  Future<List<EventModel>> getEvents() async {
    try {
      final snapshot = await _firestore
          .collection('events')
          .orderBy('dateTime', descending: true)
          .get();

      return snapshot.docs.map((doc) => _documentToEventModel(doc)).toList();
    } catch (e) {
      throw Exception('Failed to fetch events: $e');
    }
  }

  // Get upcoming events
  Future<List<EventModel>> getUpcomingEvents() async {
    try {
      final now = DateTime.now();
      final snapshot = await _firestore
          .collection('events')
          .where('dateTime', isGreaterThan: now)
          .orderBy('dateTime')
          .get();

      return snapshot.docs.map((doc) => _documentToEventModel(doc)).toList();
    } catch (e) {
      throw Exception('Failed to fetch upcoming events: $e');
    }
  }

  // Get past events
  Future<List<EventModel>> getPastEvents() async {
    try {
      final now = DateTime.now();
      final snapshot = await _firestore
          .collection('events')
          .where('dateTime', isLessThan: now)
          .orderBy('dateTime', descending: true)
          .get();

      return snapshot.docs.map((doc) => _documentToEventModel(doc)).toList();
    } catch (e) {
      throw Exception('Failed to fetch past events: $e');
    }
  }

  // Get events hosted by a specific user
  Future<List<EventModel>> getEventsByHost(String hostId) async {
    try {
      final snapshot = await _firestore
          .collection('events')
          .where('hostId', isEqualTo: hostId)
          .orderBy('dateTime', descending: true)
          .get();

      return snapshot.docs.map((doc) => _documentToEventModel(doc)).toList();
    } catch (e) {
      throw Exception('Failed to fetch host events: $e');
    }
  }

  // Get events where user is an attendee
  Future<List<EventModel>> getEventsByAttendee(String userId) async {
    try {
      final snapshot = await _firestore
          .collection('events')
          .where('attendeeIds', arrayContains: userId)
          .orderBy('dateTime', descending: true)
          .get();

      return snapshot.docs.map((doc) => _documentToEventModel(doc)).toList();
    } catch (e) {
      throw Exception('Failed to fetch attendee events: $e');
    }
  }

  // Get a specific event by ID
  Future<EventModel?> getEventById(String id) async {
    try {
      final doc = await _firestore.collection('events').doc(id).get();
      if (!doc.exists) return null;
      return _documentToEventModel(doc);
    } catch (e) {
      throw Exception('Failed to fetch event: $e');
    }
  }

  // Create a new event
  Future<void> createEvent(EventModel event) async {
    try {
      final eventData = {
        'title': event.title,
        'description': event.description,
        'imageUrl': event.imageUrl,
        'dateTime': Timestamp.fromDate(event.dateTime),
        'location': event.location,
        'price': event.price,
        'isVerified': event.isVerified,
        'isExclusive': event.isExclusive,
        'hostId': event.hostId,
        'host': {
          'id': event.host.id,
          'name': event.host.name,
          'avatarUrl': event.host.avatarUrl,
          'isVerified': event.host.isVerified,
        },
        'capacity': event.capacity,
        'attendeeIds': event.attendeeIds,
        'createdAt': FieldValue.serverTimestamp(),
      };

      await _firestore.collection('events').add(eventData);
    } catch (e) {
      throw Exception('Failed to create event: $e');
    }
  }

  // RSVP to an event
  Future<bool> rsvpToEvent(String eventId, String userId) async {
    try {
      final eventRef = _firestore.collection('events').doc(eventId);

      return await _firestore.runTransaction<bool>((transaction) async {
        final eventDoc = await transaction.get(eventRef);
        if (!eventDoc.exists) {
          throw Exception('Event not found');
        }

        final eventData = eventDoc.data()!;
        final attendeeIds = List<String>.from(eventData['attendeeIds'] ?? []);
        final capacity = eventData['capacity'] as int;

        // Check if user is already attending
        if (attendeeIds.contains(userId)) {
          return false; // Already attending
        }

        // Check if event is full
        if (attendeeIds.length >= capacity) {
          throw Exception('Event is at full capacity');
        }

        // Add user to attendees
        attendeeIds.add(userId);
        transaction.update(eventRef, {
          'attendeeIds': attendeeIds,
          'updatedAt': FieldValue.serverTimestamp(),
        });

        return true;
      });
    } catch (e) {
      throw Exception('Failed to RSVP: $e');
    }
  }

  // Cancel RSVP to an event
  Future<bool> cancelRsvp(String eventId, String userId) async {
    try {
      final eventRef = _firestore.collection('events').doc(eventId);

      return await _firestore.runTransaction<bool>((transaction) async {
        final eventDoc = await transaction.get(eventRef);
        if (!eventDoc.exists) {
          throw Exception('Event not found');
        }

        final eventData = eventDoc.data()!;
        final attendeeIds = List<String>.from(eventData['attendeeIds'] ?? []);

        // Check if user is attending
        if (!attendeeIds.contains(userId)) {
          return false; // Not attending
        }

        // Remove user from attendees
        attendeeIds.remove(userId);
        transaction.update(eventRef, {
          'attendeeIds': attendeeIds,
          'updatedAt': FieldValue.serverTimestamp(),
        });

        return true;
      });
    } catch (e) {
      throw Exception('Failed to cancel RSVP: $e');
    }
  }

  // Check if user is attending an event
  Future<bool> isUserAttending(String eventId, String userId) async {
    try {
      final doc = await _firestore.collection('events').doc(eventId).get();
      if (!doc.exists) return false;

      final eventData = doc.data()!;
      final attendeeIds = List<String>.from(eventData['attendeeIds'] ?? []);
      return attendeeIds.contains(userId);
    } catch (e) {
      throw Exception('Failed to check attendance: $e');
    }
  }

  // Get attendee profiles for an event
  Future<List<Map<String, dynamic>>> getEventAttendees(String eventId) async {
    try {
      final eventDoc = await _firestore.collection('events').doc(eventId).get();
      if (!eventDoc.exists) return [];

      final eventData = eventDoc.data()!;
      final attendeeIds = List<String>.from(eventData['attendeeIds'] ?? []);

      if (attendeeIds.isEmpty) return [];

      final attendees = <Map<String, dynamic>>[];

      // Fetch attendee profiles in batches
      for (int i = 0; i < attendeeIds.length; i += 10) {
        final batch = attendeeIds.skip(i).take(10).toList();
        final userDocs = await Future.wait(
          batch.map(
            (userId) => _firestore.collection('users').doc(userId).get(),
          ),
        );

        for (final userDoc in userDocs) {
          if (userDoc.exists) {
            final userData = userDoc.data()!;
            attendees.add({
              'id': userDoc.id,
              'name':
                  userData['name'] ?? userData['displayName'] ?? 'Unknown User',
              'avatarUrl':
                  userData['profilePictureUrl'] ?? userData['avatarUrl'] ?? '',
              'isVerified': userData['isVerified'] ?? false,
            });
          }
        }
      }

      return attendees;
    } catch (e) {
      throw Exception('Failed to fetch attendees: $e');
    }
  }

  // Update event
  Future<void> updateEvent(String eventId, EventModel updatedEvent) async {
    try {
      final eventData = {
        'title': updatedEvent.title,
        'description': updatedEvent.description,
        'imageUrl': updatedEvent.imageUrl,
        'dateTime': Timestamp.fromDate(updatedEvent.dateTime),
        'location': updatedEvent.location,
        'price': updatedEvent.price,
        'isVerified': updatedEvent.isVerified,
        'isExclusive': updatedEvent.isExclusive,
        'capacity': updatedEvent.capacity,
        'updatedAt': FieldValue.serverTimestamp(),
      };

      await _firestore.collection('events').doc(eventId).update(eventData);
    } catch (e) {
      throw Exception('Failed to update event: $e');
    }
  }

  // Delete event
  Future<void> deleteEvent(String eventId) async {
    try {
      await _firestore.collection('events').doc(eventId).delete();
    } catch (e) {
      throw Exception('Failed to delete event: $e');
    }
  }

  // Search events
  Future<List<EventModel>> searchEvents(String query) async {
    try {
      final snapshot = await _firestore
          .collection('events')
          .orderBy('dateTime', descending: true)
          .get();

      final events = snapshot.docs
          .map((doc) => _documentToEventModel(doc))
          .toList();

      // Filter by search query
      final lowercaseQuery = query.toLowerCase();
      return events.where((event) {
        return event.title.toLowerCase().contains(lowercaseQuery) ||
            event.description.toLowerCase().contains(lowercaseQuery) ||
            event.location.toLowerCase().contains(lowercaseQuery) ||
            event.host.name.toLowerCase().contains(lowercaseQuery);
      }).toList();
    } catch (e) {
      throw Exception('Failed to search events: $e');
    }
  }

  // Get events by category/filter
  Future<List<EventModel>> getEventsByFilter({
    bool? verifiedOnly,
    bool? exclusiveOnly,
    double? minPrice,
    double? maxPrice,
    String? location,
  }) async {
    try {
      Query query = _firestore.collection('events');

      if (verifiedOnly == true) {
        query = query.where('isVerified', isEqualTo: true);
      }

      if (exclusiveOnly == true) {
        query = query.where('isExclusive', isEqualTo: true);
      }

      if (minPrice != null) {
        query = query.where('price', isGreaterThanOrEqualTo: minPrice);
      }

      if (maxPrice != null) {
        query = query.where('price', isLessThanOrEqualTo: maxPrice);
      }

      final snapshot = await query.orderBy('dateTime', descending: true).get();
      final events = snapshot.docs
          .map((doc) => _documentToEventModel(doc))
          .toList();

      // Filter by location in memory if needed
      if (location != null && location.isNotEmpty) {
        final lowercaseLocation = location.toLowerCase();
        events.removeWhere(
          (event) => !event.location.toLowerCase().contains(lowercaseLocation),
        );
      }

      return events;
    } catch (e) {
      throw Exception('Failed to fetch filtered events: $e');
    }
  }

  // Helper method to convert Firestore document to EventModel
  EventModel _documentToEventModel(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    final hostData = data['host'] as Map<String, dynamic>;

    final host = EventHost(
      id: hostData['id'] as String,
      name: hostData['name'] as String,
      avatarUrl: hostData['avatarUrl'] as String,
      isVerified: hostData['isVerified'] as bool? ?? false,
    );

    return EventModel(
      id: doc.id,
      title: data['title'] as String,
      description: data['description'] as String,
      imageUrl: data['imageUrl'] as String,
      dateTime: (data['dateTime'] as Timestamp).toDate(),
      location: data['location'] as String,
      price: (data['price'] as num).toDouble(),
      isVerified: data['isVerified'] as bool? ?? false,
      isExclusive: data['isExclusive'] as bool? ?? false,
      hostId: data['hostId'] as String,
      host: host,
      capacity: data['capacity'] as int? ?? 0,
      attendeeIds: List<String>.from(data['attendeeIds'] ?? []),
      createdAt: data['createdAt'] != null
          ? (data['createdAt'] as Timestamp).toDate()
          : null,
    );
  }
}
