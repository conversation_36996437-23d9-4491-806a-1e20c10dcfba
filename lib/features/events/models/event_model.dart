import 'package:freezed_annotation/freezed_annotation.dart';

part 'event_model.freezed.dart';
part 'event_model.g.dart';

@freezed
abstract class EventModel with _$EventModel {
  const factory EventModel({
    required String id,
    required String title,
    required String description,
    required String imageUrl,
    required DateTime dateTime,
    required String location,
    required double price,
    @Default(false) bool isVerified,
    @Default(false) bool isExclusive,
    required String hostId,
    required EventHost host,
    required int capacity,
    @Default([]) List<String> attendeeIds,
    DateTime? createdAt,
  }) = _EventModel;

  factory EventModel.fromJson(Map<String, dynamic> json) =>
      _$EventModelFromJson(json);
}

@freezed
abstract class EventHost with _$EventHost {
  const factory EventHost({
    required String id,
    required String name,
    required String avatarUrl,
    @Default(false) bool isVerified,
  }) = _EventHost;

  factory EventHost.fromJson(Map<String, dynamic> json) =>
      _$EventHostFromJson(json);
}
