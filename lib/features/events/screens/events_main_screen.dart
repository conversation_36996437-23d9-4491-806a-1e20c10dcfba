import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/event_model.dart';
import '../providers/events_provider.dart';
import 'event_creation_screen.dart';
import 'event_detail_screen.dart';

class EventsMainScreen extends ConsumerStatefulWidget {
  const EventsMainScreen({super.key});

  @override
  ConsumerState<EventsMainScreen> createState() => _EventsMainScreenState();
}

class _EventsMainScreenState extends ConsumerState<EventsMainScreen> {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  bool _isBusinessAccount = false;

  @override
  void initState() {
    super.initState();
    _checkBusinessAccountStatus();
  }

  Future<void> _checkBusinessAccountStatus() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        setState(() {
          _isBusinessAccount = false;
        });
        return;
      }

      final userDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .get();
      if (userDoc.exists) {
        final userData = userDoc.data()!;
        final accountType = userData['accountType'] as String?;

        setState(() {
          _isBusinessAccount =
              accountType == 'business' || accountType == 'enterprise';
        });
      } else {
        setState(() {
          _isBusinessAccount = false;
        });
      }
    } catch (e) {
      setState(() {
        _isBusinessAccount = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final eventsAsync = ref.watch(eventStateProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Events'),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(50.0),
          child: _buildFilterTabs(context, ref),
        ),
      ),
      body: eventsAsync.when(
        data: (events) {
          if (events.isEmpty) {
            return _buildEmptyState(context, ref);
          }
          return RefreshIndicator(
            onRefresh: () => ref.read(eventStateProvider.notifier).refresh(),
            child: ListView.builder(
              padding: const EdgeInsets.all(12.0),
              itemCount: events.length,
              itemBuilder: (context, index) {
                return EventCard(event: events[index]);
              },
            ),
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (err, stack) => Center(child: Text('Error: $err')),
      ),
      floatingActionButton: _isBusinessAccount
          ? FloatingActionButton.extended(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const EventCreationScreen(),
                  ),
                );
              },
              label: const Text('Create Event'),
              icon: const Icon(Icons.add),
            )
          : null,
    );
  }

  Widget _buildFilterTabs(BuildContext context, WidgetRef ref) {
    final currentFilter = ref.watch(eventFilterProvider);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
      height: 50,
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: EventFilterState.values.map((filter) {
          final isSelected = currentFilter == filter;
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4.0),
            child: ChoiceChip(
              label: Text(toBeginningOfSentenceCase(filter.name)!),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  // This is the correct way to update the state,
                  // avoiding direct access to '.state'.
                  // Since my previous attempts to refactor the provider failed,
                  // I will leave the logic as is for now to avoid breaking it.
                  ref.read(eventFilterProvider.notifier).setFilter(filter);
                  ref.read(eventStateProvider.notifier).refresh();
                }
              },
              backgroundColor: isSelected
                  ? Theme.of(context).primaryColor.withAlpha(25)
                  : Colors.grey[200],
              selectedColor: Theme.of(context).primaryColor.withAlpha(51),
              labelStyle: TextStyle(
                color: isSelected
                    ? Theme.of(context).primaryColor
                    : Colors.black,
                fontWeight: FontWeight.bold,
              ),
              shape: StadiumBorder(
                side: BorderSide(
                  color: isSelected
                      ? Theme.of(context).primaryColor
                      : Colors.grey[300]!,
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, WidgetRef ref) {
    final filter = ref.watch(eventFilterProvider);
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.event_busy, size: 80, color: Colors.grey[400]),
          const SizedBox(height: 20),
          Text(
            'No ${toBeginningOfSentenceCase(filter.name)} Events',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 10),
          Text(
            'Check back later or try a different filter.',
            style: Theme.of(context).textTheme.bodyLarge,
          ),
        ],
      ),
    );
  }
}

class EventCard extends StatelessWidget {
  final EventModel event;
  const EventCard({super.key, required this.event});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => EventDetailScreen(eventId: event.id),
          ),
        );
      },
      child: Card(
        elevation: 2.0,
        margin: const EdgeInsets.only(bottom: 16.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.0),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [_buildImageHeader(), _buildInfoSection(context)],
        ),
      ),
    );
  }

  Widget _buildImageHeader() {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: const BorderRadius.vertical(top: Radius.circular(12.0)),
          child: Image.network(
            event.imageUrl,
            height: 180,
            width: double.infinity,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) => Container(
              height: 180,
              color: Colors.grey[300],
              child: const Icon(Icons.event, size: 50, color: Colors.grey),
            ),
          ),
        ),
        Positioned(top: 12, right: 12, child: _buildPriceTag()),
        Positioned(bottom: 12, left: 12, child: _buildHostAvatar()),
      ],
    );
  }

  Widget _buildPriceTag() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.black.withAlpha((255 * 0.7).toInt()),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        event.price == 0.0 ? 'FREE' : '\$${event.price.toStringAsFixed(0)}',
        style: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildHostAvatar() {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: Colors.white, width: 2),
      ),
      child: CircleAvatar(
        radius: 25,
        backgroundImage: NetworkImage(event.host.avatarUrl),
      ),
    );
  }

  Widget _buildInfoSection(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildBadges(),
          const SizedBox(height: 8),
          Text(
            event.title,
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 8),
          _buildInfoRow(
            context,
            Icons.calendar_today,
            DateFormat.yMMMd().add_jm().format(event.dateTime),
          ),
          const SizedBox(height: 6),
          _buildInfoRow(context, Icons.location_on, event.location),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildAttendeeSummary(),
              ElevatedButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) =>
                          EventDetailScreen(eventId: event.id),
                    ),
                  );
                },
                child: const Text('View Details'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBadges() {
    return Row(
      children: [
        if (event.isExclusive)
          _Badge(label: 'Exclusive', color: Colors.purple, icon: Icons.star),
        if (event.isExclusive) const SizedBox(width: 8),
        if (event.isVerified)
          _Badge(label: 'Verified', color: Colors.blue, icon: Icons.verified),
      ],
    );
  }

  Widget _buildInfoRow(BuildContext context, IconData icon, String text) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Theme.of(context).primaryColor),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: Theme.of(context).textTheme.bodyLarge,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildAttendeeSummary() {
    return Row(
      children: [
        Stack(
          children: List.generate(
            event.attendeeIds.length.clamp(0, 3),
            (index) => Padding(
              padding: EdgeInsets.only(left: index * 15.0),
              child: CircleAvatar(
                radius: 15,
                backgroundColor: Colors.accents[index % Colors.accents.length],
              ),
            ),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          '${event.attendeeIds.length} attending',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
      ],
    );
  }
}

class _Badge extends StatelessWidget {
  final String label;
  final Color color;
  final IconData icon;

  const _Badge({required this.label, required this.color, required this.icon});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withAlpha((255 * 0.15).toInt()),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 14),
          const SizedBox(width: 4),
          Text(
            label.toUpperCase(),
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.bold,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }
}
