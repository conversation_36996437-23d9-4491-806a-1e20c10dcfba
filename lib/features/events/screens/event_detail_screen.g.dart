// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'event_detail_screen.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$eventByIdHash() => r'bddf72db7d4b8be4038cebee12b723909daf7303';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [eventById].
@ProviderFor(eventById)
const eventByIdProvider = EventByIdFamily();

/// See also [eventById].
class EventByIdFamily extends Family<AsyncValue<EventModel>> {
  /// See also [eventById].
  const EventByIdFamily();

  /// See also [eventById].
  EventByIdProvider call(String eventId) {
    return EventByIdProvider(eventId);
  }

  @override
  EventByIdProvider getProviderOverride(covariant EventByIdProvider provider) {
    return call(provider.eventId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'eventByIdProvider';
}

/// See also [eventById].
class EventByIdProvider extends FutureProvider<EventModel> {
  /// See also [eventById].
  EventByIdProvider(String eventId)
    : this._internal(
        (ref) => eventById(ref as EventByIdRef, eventId),
        from: eventByIdProvider,
        name: r'eventByIdProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$eventByIdHash,
        dependencies: EventByIdFamily._dependencies,
        allTransitiveDependencies: EventByIdFamily._allTransitiveDependencies,
        eventId: eventId,
      );

  EventByIdProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.eventId,
  }) : super.internal();

  final String eventId;

  @override
  Override overrideWith(
    FutureOr<EventModel> Function(EventByIdRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: EventByIdProvider._internal(
        (ref) => create(ref as EventByIdRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        eventId: eventId,
      ),
    );
  }

  @override
  FutureProviderElement<EventModel> createElement() {
    return _EventByIdProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is EventByIdProvider && other.eventId == eventId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, eventId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin EventByIdRef on FutureProviderRef<EventModel> {
  /// The parameter `eventId` of this provider.
  String get eventId;
}

class _EventByIdProviderElement extends FutureProviderElement<EventModel>
    with EventByIdRef {
  _EventByIdProviderElement(super.provider);

  @override
  String get eventId => (origin as EventByIdProvider).eventId;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
