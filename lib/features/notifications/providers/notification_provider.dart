import 'package:billionaires_social/features/notifications/models/notification_model.dart';
import 'package:billionaires_social/features/notifications/services/notification_service.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

part 'notification_provider.g.dart';

@riverpod
NotificationService notificationService(Ref ref) {
  return NotificationService();
}

@riverpod
Stream<List<NotificationModel>> notifications(Ref ref) {
  final notificationService = getIt<NotificationService>();
  return notificationService.getNotificationsStream();
}

@Riverpod(keepAlive: true)
class NotificationNotifier extends _$NotificationNotifier {
  @override
  Future<void> build() async {
    return;
  }

  Future<void> markAsRead(String notificationId) async {
    final notificationService = getIt<NotificationService>();
    await notificationService.markNotificationAsRead(notificationId);
  }

  Future<void> markAllAsRead() async {
    final notificationService = getIt<NotificationService>();
    await notificationService.markAllNotificationsAsRead();
  }

  Future<void> deleteNotification(String notificationId) async {
    final notificationService = getIt<NotificationService>();
    await notificationService.deleteNotification(notificationId);
  }

  Future<void> deleteAllNotifications() async {
    final notificationService = getIt<NotificationService>();
    await notificationService.deleteAllNotifications();
  }

  Future<void> createFollowNotification(String targetUserId) async {
    final notificationService = getIt<NotificationService>();
    await notificationService.createFollowNotification(targetUserId);
  }

  Future<void> createLikeNotification(String postId, String postOwnerId) async {
    final notificationService = getIt<NotificationService>();
    await notificationService.createLikeNotification(postId, postOwnerId);
  }

  Future<void> createCommentNotification(
    String postId,
    String postOwnerId,
    String commentId,
  ) async {
    final notificationService = getIt<NotificationService>();
    await notificationService.createCommentNotification(
      postId,
      postOwnerId,
      commentId,
    );
  }

  Future<void> createRepostNotification(
    String postId,
    String postOwnerId,
    String? repostId,
  ) async {
    final notificationService = getIt<NotificationService>();
    await notificationService.createRepostNotification(
      postId,
      postOwnerId,
      repostId,
    );
  }

  Future<void> createRemixNotification(
    String originalPostId,
    String originalPostOwnerId,
    String remixId,
  ) async {
    final notificationService = getIt<NotificationService>();
    await notificationService.createRemixNotification(
      originalPostId,
      originalPostOwnerId,
      remixId,
    );
  }

  Future<void> createMentionNotification(
    String mentionedUserId,
    String postId,
  ) async {
    final notificationService = getIt<NotificationService>();
    await notificationService.createMentionNotification(
      mentionedUserId,
      postId,
    );
  }

  Future<void> createMessageNotification(
    String chatId,
    String recipientId,
    String messageId,
  ) async {
    final notificationService = getIt<NotificationService>();
    await notificationService.createMessageNotification(
      chatId,
      recipientId,
      messageId,
    );
  }

  Future<void> createStoryViewNotification(
    String storyOwnerId,
    String storyId,
  ) async {
    final notificationService = getIt<NotificationService>();
    await notificationService.createStoryViewNotification(
      storyOwnerId,
      storyId,
    );
  }

  Future<void> createBookingNotification(
    String placeOwnerId,
    String bookingId,
  ) async {
    final notificationService = getIt<NotificationService>();
    await notificationService.createBookingNotification(
      placeOwnerId,
      bookingId,
    );
  }
}

@riverpod
class NotificationPreferencesNotifier
    extends _$NotificationPreferencesNotifier {
  @override
  Future<NotificationPreferences> build() async {
    final notificationService = getIt<NotificationService>();
    return notificationService.getNotificationPreferences();
  }

  Future<void> updatePreferences(NotificationPreferences preferences) async {
    final notificationService = getIt<NotificationService>();
    await notificationService.saveNotificationPreferences(preferences);
    ref.invalidateSelf();
  }

  Future<void> toggleNotificationType(
    NotificationType type,
    bool enabled,
  ) async {
    final currentPreferences = state.valueOrNull;
    if (currentPreferences == null) return;

    NotificationPreferences newPreferences;
    switch (type) {
      case NotificationType.follow:
        newPreferences = currentPreferences.copyWith(
          followNotifications: enabled,
        );
        break;
      case NotificationType.like:
        newPreferences = currentPreferences.copyWith(
          likeNotifications: enabled,
        );
        break;
      case NotificationType.comment:
        newPreferences = currentPreferences.copyWith(
          commentNotifications: enabled,
        );
        break;
      case NotificationType.mention:
        newPreferences = currentPreferences.copyWith(
          mentionNotifications: enabled,
        );
        break;
      case NotificationType.message:
        newPreferences = currentPreferences.copyWith(
          messageNotifications: enabled,
        );
        break;
      case NotificationType.share:
        newPreferences = currentPreferences.copyWith(
          shareNotifications: enabled,
        );
        break;
      case NotificationType.admin:
        newPreferences = currentPreferences.copyWith(
          adminNotifications: enabled,
        );
        break;
      case NotificationType.story:
        newPreferences = currentPreferences.copyWith(
          storyNotifications: enabled,
        );
        break;
      case NotificationType.event:
        newPreferences = currentPreferences.copyWith(
          eventNotifications: enabled,
        );
        break;
      case NotificationType.system:
        newPreferences = currentPreferences.copyWith(
          systemNotifications: enabled,
        );
        break;
    }

    await updatePreferences(newPreferences);
  }

  Future<void> togglePushNotifications(bool enabled) async {
    final currentPreferences = state.valueOrNull;
    if (currentPreferences == null) return;

    final newPreferences = currentPreferences.copyWith(
      pushNotifications: enabled,
    );
    await updatePreferences(newPreferences);
  }

  Future<void> toggleInAppNotifications(bool enabled) async {
    final currentPreferences = state.valueOrNull;
    if (currentPreferences == null) return;

    final newPreferences = currentPreferences.copyWith(
      inAppNotifications: enabled,
    );
    await updatePreferences(newPreferences);
  }

  Future<void> toggleEmailNotifications(bool enabled) async {
    final currentPreferences = state.valueOrNull;
    if (currentPreferences == null) return;

    final newPreferences = currentPreferences.copyWith(
      emailNotifications: enabled,
    );
    await updatePreferences(newPreferences);
  }
}

@riverpod
class UnreadNotificationCount extends _$UnreadNotificationCount {
  @override
  Stream<int> build() {
    final notificationService = getIt<NotificationService>();
    return notificationService.getUserNotifications().map((notifications) {
      return notifications
          .where((n) => n.status == NotificationStatus.unread)
          .length;
    });
  }
}
