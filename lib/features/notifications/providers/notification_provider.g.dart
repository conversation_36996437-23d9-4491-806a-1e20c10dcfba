// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$notificationServiceHash() =>
    r'cda5ea9d196dce85bee56839a4a0f035021752e3';

/// See also [notificationService].
@ProviderFor(notificationService)
final notificationServiceProvider =
    AutoDisposeProvider<NotificationService>.internal(
      notificationService,
      name: r'notificationServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$notificationServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef NotificationServiceRef = AutoDisposeProviderRef<NotificationService>;
String _$notificationsHash() => r'ea0c83752a382f57c2a785603ad0138a792d6d54';

/// See also [notifications].
@ProviderFor(notifications)
final notificationsProvider =
    AutoDisposeStreamProvider<List<NotificationModel>>.internal(
      notifications,
      name: r'notificationsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$notificationsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef NotificationsRef =
    AutoDisposeStreamProviderRef<List<NotificationModel>>;
String _$notificationNotifierHash() =>
    r'd0340da5474ec729b05ab9c84f5e6d434a6a3d61';

/// See also [NotificationNotifier].
@ProviderFor(NotificationNotifier)
final notificationNotifierProvider =
    AsyncNotifierProvider<NotificationNotifier, void>.internal(
      NotificationNotifier.new,
      name: r'notificationNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$notificationNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$NotificationNotifier = AsyncNotifier<void>;
String _$notificationPreferencesNotifierHash() =>
    r'756371b6ca4320debd9bb39a65bdac01c99c0944';

/// See also [NotificationPreferencesNotifier].
@ProviderFor(NotificationPreferencesNotifier)
final notificationPreferencesNotifierProvider =
    AutoDisposeAsyncNotifierProvider<
      NotificationPreferencesNotifier,
      NotificationPreferences
    >.internal(
      NotificationPreferencesNotifier.new,
      name: r'notificationPreferencesNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$notificationPreferencesNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$NotificationPreferencesNotifier =
    AutoDisposeAsyncNotifier<NotificationPreferences>;
String _$unreadNotificationCountHash() =>
    r'ea9f8979181426a706449ee6b27602209e11cd9a';

/// See also [UnreadNotificationCount].
@ProviderFor(UnreadNotificationCount)
final unreadNotificationCountProvider =
    AutoDisposeStreamNotifierProvider<UnreadNotificationCount, int>.internal(
      UnreadNotificationCount.new,
      name: r'unreadNotificationCountProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$unreadNotificationCountHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$UnreadNotificationCount = AutoDisposeStreamNotifier<int>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
