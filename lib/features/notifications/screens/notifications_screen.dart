import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/features/notifications/models/notification_model.dart';
import 'package:billionaires_social/features/stories/widgets/story_aware_profile_image.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

enum NotificationFilter { all, mentions, interactions }

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notifications'),
        bottom: Tab<PERSON><PERSON>(
          controller: _tabController,
          tabs: const [
            Tab(text: 'All'),
            Tab(text: 'Mentions'),
            Tab(text: 'Interactions'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildNotificationList(NotificationFilter.all),
          _buildNotificationList(NotificationFilter.mentions),
          _buildNotificationList(NotificationFilter.interactions),
        ],
      ),
    );
  }

  Widget _buildNotificationList(NotificationFilter filter) {
    // In a real app, this data would come from a provider
    final notifications = _getFilteredNotifications(filter);

    if (notifications.isEmpty) {
      return const Center(child: Text('No new notifications.'));
    }

    return ListView.builder(
      itemCount: notifications.length,
      itemBuilder: (context, index) {
        return _NotificationTile(notification: notifications[index]);
      },
    );
  }
}

class _NotificationTile extends StatelessWidget {
  final NotificationModel notification;
  const _NotificationTile({required this.notification});

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: StoryAwareProfileImage(
        userId: notification.senderId,
        profileImageUrl: notification.senderAvatarUrl,
        size: 40,
      ),
      title: RichText(
        text: TextSpan(
          style: AppTheme.fontStyles.body,
          children: [
            TextSpan(
              text: '${notification.senderName} ',
              style: AppTheme.fontStyles.bodyBold,
            ),
            TextSpan(text: _notificationText()),
          ],
        ),
      ),
      subtitle: Text(
        DateFormat.yMMMd().add_jm().format(notification.timestamp),
        style: AppTheme.fontStyles.caption,
      ),
      trailing: _buildTrailingWidget(),
      onTap: () => _handleNotificationTap(context),
    );
  }

  void _handleNotificationTap(BuildContext context) {
    switch (notification.type) {
      case NotificationType.like:
      case NotificationType.comment:
      case NotificationType.mention:
        // Navigate to the post
        if (notification.postId != null) {
          _navigateToPost(context, notification.postId!);
        }
        break;
      case NotificationType.follow:
        // Navigate to the user's profile
        _navigateToProfile(context, notification.senderId);
        break;
      case NotificationType.message:
        // Navigate to chat with the user
        _navigateToChat(context, notification.senderId);
        break;
      default:
        // For other notification types, navigate to profile
        _navigateToProfile(context, notification.senderId);
        break;
    }
  }

  void _navigateToPost(BuildContext context, String postId) {
    // TODO: Implement navigation to specific post
    // For now, just show a message
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('Navigate to post: $postId')));
  }

  void _navigateToProfile(BuildContext context, String userId) {
    // TODO: Implement navigation to user profile
    // For now, just show a message
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('Navigate to profile: $userId')));
  }

  void _navigateToChat(BuildContext context, String userId) {
    // TODO: Implement navigation to chat
    // For now, just show a message
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('Navigate to chat with: $userId')));
  }

  String _notificationText() {
    switch (notification.type) {
      case NotificationType.like:
        return 'liked your post.';
      case NotificationType.comment:
        return 'commented: "${notification.body}"';
      case NotificationType.mention:
        return 'mentioned you in a comment.';
      case NotificationType.follow:
        return 'started following you.';
      case NotificationType.message:
        return 'sent you a message.';
      case NotificationType.share:
        return 'shared your post.';
      case NotificationType.admin:
        return 'sent an admin notification.';
      case NotificationType.story:
        return 'mentioned you in their story.';
      case NotificationType.event:
        return 'invited you to an event.';
      case NotificationType.system:
        return 'sent a system notification.';
    }
  }

  Widget? _buildTrailingWidget() {
    if (notification.postId != null) {
      return SizedBox(
        width: 50,
        height: 50,
        child: Container(
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(Icons.image, color: Colors.grey),
        ),
      );
    }
    if (notification.type == NotificationType.follow) {
      return ElevatedButton(onPressed: () {}, child: const Text('Follow Back'));
    }
    return null;
  }
}

// Mock data and filtering logic
List<NotificationModel> _getFilteredNotifications(NotificationFilter filter) {
  final allNotifications = _getMockNotifications();
  switch (filter) {
    case NotificationFilter.all:
      return allNotifications;
    case NotificationFilter.mentions:
      return allNotifications
          .where((n) => n.type == NotificationType.mention)
          .toList();
    case NotificationFilter.interactions:
      return allNotifications
          .where(
            (n) =>
                n.type == NotificationType.like ||
                n.type == NotificationType.comment,
          )
          .toList();
  }
}

List<NotificationModel> _getMockNotifications() {
  return [
    NotificationModel(
      id: '1',
      userId: 'current_user',
      title: 'New Like',
      body: 'Elon Musk liked your post',
      type: NotificationType.like,
      timestamp: DateTime.now().subtract(const Duration(minutes: 5)),
      status: NotificationStatus.unread,
      senderId: 'elon_musk',
      senderName: 'Elon Musk',
      senderAvatarUrl: 'https://i.pravatar.cc/150?u=elonmusk',
      postId: 'post_1',
    ),
    NotificationModel(
      id: '2',
      userId: 'current_user',
      title: 'New Comment',
      body: 'Jeff Bezos commented: "Great post!"',
      type: NotificationType.comment,
      timestamp: DateTime.now().subtract(const Duration(hours: 1)),
      status: NotificationStatus.unread,
      senderId: 'jeff_bezos',
      senderName: 'Jeff Bezos',
      senderAvatarUrl: 'https://i.pravatar.cc/150?u=jeffbezos',
      postId: 'post_2',
      commentId: 'comment_1',
    ),
    NotificationModel(
      id: '3',
      userId: 'current_user',
      title: 'New Mention',
      body: 'Bill Gates mentioned you in a comment',
      type: NotificationType.mention,
      timestamp: DateTime.now().subtract(const Duration(hours: 3)),
      status: NotificationStatus.unread,
      senderId: 'bill_gates',
      senderName: 'Bill Gates',
      senderAvatarUrl: 'https://i.pravatar.cc/150?u=billgates',
      commentId: 'comment_2',
    ),
    NotificationModel(
      id: '4',
      userId: 'current_user',
      title: 'New Follower',
      body: 'Mark Zuckerberg started following you',
      type: NotificationType.follow,
      timestamp: DateTime.now().subtract(const Duration(days: 1)),
      status: NotificationStatus.unread,
      senderId: 'mark_zuckerberg',
      senderName: 'Mark Zuckerberg',
      senderAvatarUrl: 'https://i.pravatar.cc/150?u=markzuckerberg',
    ),
  ];
}
