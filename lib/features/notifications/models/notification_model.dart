import 'package:freezed_annotation/freezed_annotation.dart';

part 'notification_model.freezed.dart';
part 'notification_model.g.dart';

enum NotificationType {
  follow,
  like,
  comment,
  mention,
  message,
  share,
  admin,
  story,
  event,
  system,
}

enum NotificationStatus { unread, read, dismissed }

enum NotificationFilter { all, mentions, interactions }

@freezed
abstract class NotificationModel with _$NotificationModel {
  const factory NotificationModel({
    required String id,
    required String userId,
    required String title,
    required String body,
    required NotificationType type,
    required DateTime timestamp,
    required NotificationStatus status,
    // User who triggered the notification
    required String senderId,
    required String senderName,
    required String senderAvatarUrl,
    // Related content (optional)
    String? postId,
    String? commentId,
    String? messageId,
    String? storyId,
    String? eventId,
    // Additional data
    Map<String, dynamic>? data,
    // Action data
    String? actionUrl,
    String? actionType,
  }) = _NotificationModel;

  factory NotificationModel.fromJson(Map<String, dynamic> json) =>
      _$NotificationModelFromJson(json);
}

@freezed
abstract class NotificationPreferences with _$NotificationPreferences {
  const factory NotificationPreferences({
    @Default(true) bool followNotifications,
    @Default(true) bool likeNotifications,
    @Default(true) bool commentNotifications,
    @Default(true) bool mentionNotifications,
    @Default(true) bool messageNotifications,
    @Default(true) bool shareNotifications,
    @Default(true) bool adminNotifications,
    @Default(true) bool storyNotifications,
    @Default(true) bool eventNotifications,
    @Default(true) bool systemNotifications,
    @Default(true) bool pushNotifications,
    @Default(true) bool inAppNotifications,
    @Default(true) bool emailNotifications,
  }) = _NotificationPreferences;

  factory NotificationPreferences.fromJson(Map<String, dynamic> json) =>
      _$NotificationPreferencesFromJson(json);

  factory NotificationPreferences.defaultSettings() =>
      const NotificationPreferences();
}
