// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'notification_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$NotificationModel {

 String get id; String get userId; String get title; String get body; NotificationType get type; DateTime get timestamp; NotificationStatus get status;// User who triggered the notification
 String get senderId; String get senderName; String get senderAvatarUrl;// Related content (optional)
 String? get postId; String? get commentId; String? get messageId; String? get storyId; String? get eventId;// Additional data
 Map<String, dynamic>? get data;// Action data
 String? get actionUrl; String? get actionType;
/// Create a copy of NotificationModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$NotificationModelCopyWith<NotificationModel> get copyWith => _$NotificationModelCopyWithImpl<NotificationModel>(this as NotificationModel, _$identity);

  /// Serializes this NotificationModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is NotificationModel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.title, title) || other.title == title)&&(identical(other.body, body) || other.body == body)&&(identical(other.type, type) || other.type == type)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.status, status) || other.status == status)&&(identical(other.senderId, senderId) || other.senderId == senderId)&&(identical(other.senderName, senderName) || other.senderName == senderName)&&(identical(other.senderAvatarUrl, senderAvatarUrl) || other.senderAvatarUrl == senderAvatarUrl)&&(identical(other.postId, postId) || other.postId == postId)&&(identical(other.commentId, commentId) || other.commentId == commentId)&&(identical(other.messageId, messageId) || other.messageId == messageId)&&(identical(other.storyId, storyId) || other.storyId == storyId)&&(identical(other.eventId, eventId) || other.eventId == eventId)&&const DeepCollectionEquality().equals(other.data, data)&&(identical(other.actionUrl, actionUrl) || other.actionUrl == actionUrl)&&(identical(other.actionType, actionType) || other.actionType == actionType));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,title,body,type,timestamp,status,senderId,senderName,senderAvatarUrl,postId,commentId,messageId,storyId,eventId,const DeepCollectionEquality().hash(data),actionUrl,actionType);

@override
String toString() {
  return 'NotificationModel(id: $id, userId: $userId, title: $title, body: $body, type: $type, timestamp: $timestamp, status: $status, senderId: $senderId, senderName: $senderName, senderAvatarUrl: $senderAvatarUrl, postId: $postId, commentId: $commentId, messageId: $messageId, storyId: $storyId, eventId: $eventId, data: $data, actionUrl: $actionUrl, actionType: $actionType)';
}


}

/// @nodoc
abstract mixin class $NotificationModelCopyWith<$Res>  {
  factory $NotificationModelCopyWith(NotificationModel value, $Res Function(NotificationModel) _then) = _$NotificationModelCopyWithImpl;
@useResult
$Res call({
 String id, String userId, String title, String body, NotificationType type, DateTime timestamp, NotificationStatus status, String senderId, String senderName, String senderAvatarUrl, String? postId, String? commentId, String? messageId, String? storyId, String? eventId, Map<String, dynamic>? data, String? actionUrl, String? actionType
});




}
/// @nodoc
class _$NotificationModelCopyWithImpl<$Res>
    implements $NotificationModelCopyWith<$Res> {
  _$NotificationModelCopyWithImpl(this._self, this._then);

  final NotificationModel _self;
  final $Res Function(NotificationModel) _then;

/// Create a copy of NotificationModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? title = null,Object? body = null,Object? type = null,Object? timestamp = null,Object? status = null,Object? senderId = null,Object? senderName = null,Object? senderAvatarUrl = null,Object? postId = freezed,Object? commentId = freezed,Object? messageId = freezed,Object? storyId = freezed,Object? eventId = freezed,Object? data = freezed,Object? actionUrl = freezed,Object? actionType = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,body: null == body ? _self.body : body // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as NotificationType,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as NotificationStatus,senderId: null == senderId ? _self.senderId : senderId // ignore: cast_nullable_to_non_nullable
as String,senderName: null == senderName ? _self.senderName : senderName // ignore: cast_nullable_to_non_nullable
as String,senderAvatarUrl: null == senderAvatarUrl ? _self.senderAvatarUrl : senderAvatarUrl // ignore: cast_nullable_to_non_nullable
as String,postId: freezed == postId ? _self.postId : postId // ignore: cast_nullable_to_non_nullable
as String?,commentId: freezed == commentId ? _self.commentId : commentId // ignore: cast_nullable_to_non_nullable
as String?,messageId: freezed == messageId ? _self.messageId : messageId // ignore: cast_nullable_to_non_nullable
as String?,storyId: freezed == storyId ? _self.storyId : storyId // ignore: cast_nullable_to_non_nullable
as String?,eventId: freezed == eventId ? _self.eventId : eventId // ignore: cast_nullable_to_non_nullable
as String?,data: freezed == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,actionUrl: freezed == actionUrl ? _self.actionUrl : actionUrl // ignore: cast_nullable_to_non_nullable
as String?,actionType: freezed == actionType ? _self.actionType : actionType // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [NotificationModel].
extension NotificationModelPatterns on NotificationModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _NotificationModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _NotificationModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _NotificationModel value)  $default,){
final _that = this;
switch (_that) {
case _NotificationModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _NotificationModel value)?  $default,){
final _that = this;
switch (_that) {
case _NotificationModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  String title,  String body,  NotificationType type,  DateTime timestamp,  NotificationStatus status,  String senderId,  String senderName,  String senderAvatarUrl,  String? postId,  String? commentId,  String? messageId,  String? storyId,  String? eventId,  Map<String, dynamic>? data,  String? actionUrl,  String? actionType)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _NotificationModel() when $default != null:
return $default(_that.id,_that.userId,_that.title,_that.body,_that.type,_that.timestamp,_that.status,_that.senderId,_that.senderName,_that.senderAvatarUrl,_that.postId,_that.commentId,_that.messageId,_that.storyId,_that.eventId,_that.data,_that.actionUrl,_that.actionType);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  String title,  String body,  NotificationType type,  DateTime timestamp,  NotificationStatus status,  String senderId,  String senderName,  String senderAvatarUrl,  String? postId,  String? commentId,  String? messageId,  String? storyId,  String? eventId,  Map<String, dynamic>? data,  String? actionUrl,  String? actionType)  $default,) {final _that = this;
switch (_that) {
case _NotificationModel():
return $default(_that.id,_that.userId,_that.title,_that.body,_that.type,_that.timestamp,_that.status,_that.senderId,_that.senderName,_that.senderAvatarUrl,_that.postId,_that.commentId,_that.messageId,_that.storyId,_that.eventId,_that.data,_that.actionUrl,_that.actionType);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  String title,  String body,  NotificationType type,  DateTime timestamp,  NotificationStatus status,  String senderId,  String senderName,  String senderAvatarUrl,  String? postId,  String? commentId,  String? messageId,  String? storyId,  String? eventId,  Map<String, dynamic>? data,  String? actionUrl,  String? actionType)?  $default,) {final _that = this;
switch (_that) {
case _NotificationModel() when $default != null:
return $default(_that.id,_that.userId,_that.title,_that.body,_that.type,_that.timestamp,_that.status,_that.senderId,_that.senderName,_that.senderAvatarUrl,_that.postId,_that.commentId,_that.messageId,_that.storyId,_that.eventId,_that.data,_that.actionUrl,_that.actionType);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _NotificationModel implements NotificationModel {
  const _NotificationModel({required this.id, required this.userId, required this.title, required this.body, required this.type, required this.timestamp, required this.status, required this.senderId, required this.senderName, required this.senderAvatarUrl, this.postId, this.commentId, this.messageId, this.storyId, this.eventId, final  Map<String, dynamic>? data, this.actionUrl, this.actionType}): _data = data;
  factory _NotificationModel.fromJson(Map<String, dynamic> json) => _$NotificationModelFromJson(json);

@override final  String id;
@override final  String userId;
@override final  String title;
@override final  String body;
@override final  NotificationType type;
@override final  DateTime timestamp;
@override final  NotificationStatus status;
// User who triggered the notification
@override final  String senderId;
@override final  String senderName;
@override final  String senderAvatarUrl;
// Related content (optional)
@override final  String? postId;
@override final  String? commentId;
@override final  String? messageId;
@override final  String? storyId;
@override final  String? eventId;
// Additional data
 final  Map<String, dynamic>? _data;
// Additional data
@override Map<String, dynamic>? get data {
  final value = _data;
  if (value == null) return null;
  if (_data is EqualUnmodifiableMapView) return _data;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

// Action data
@override final  String? actionUrl;
@override final  String? actionType;

/// Create a copy of NotificationModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$NotificationModelCopyWith<_NotificationModel> get copyWith => __$NotificationModelCopyWithImpl<_NotificationModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$NotificationModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _NotificationModel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.title, title) || other.title == title)&&(identical(other.body, body) || other.body == body)&&(identical(other.type, type) || other.type == type)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.status, status) || other.status == status)&&(identical(other.senderId, senderId) || other.senderId == senderId)&&(identical(other.senderName, senderName) || other.senderName == senderName)&&(identical(other.senderAvatarUrl, senderAvatarUrl) || other.senderAvatarUrl == senderAvatarUrl)&&(identical(other.postId, postId) || other.postId == postId)&&(identical(other.commentId, commentId) || other.commentId == commentId)&&(identical(other.messageId, messageId) || other.messageId == messageId)&&(identical(other.storyId, storyId) || other.storyId == storyId)&&(identical(other.eventId, eventId) || other.eventId == eventId)&&const DeepCollectionEquality().equals(other._data, _data)&&(identical(other.actionUrl, actionUrl) || other.actionUrl == actionUrl)&&(identical(other.actionType, actionType) || other.actionType == actionType));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,title,body,type,timestamp,status,senderId,senderName,senderAvatarUrl,postId,commentId,messageId,storyId,eventId,const DeepCollectionEquality().hash(_data),actionUrl,actionType);

@override
String toString() {
  return 'NotificationModel(id: $id, userId: $userId, title: $title, body: $body, type: $type, timestamp: $timestamp, status: $status, senderId: $senderId, senderName: $senderName, senderAvatarUrl: $senderAvatarUrl, postId: $postId, commentId: $commentId, messageId: $messageId, storyId: $storyId, eventId: $eventId, data: $data, actionUrl: $actionUrl, actionType: $actionType)';
}


}

/// @nodoc
abstract mixin class _$NotificationModelCopyWith<$Res> implements $NotificationModelCopyWith<$Res> {
  factory _$NotificationModelCopyWith(_NotificationModel value, $Res Function(_NotificationModel) _then) = __$NotificationModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, String title, String body, NotificationType type, DateTime timestamp, NotificationStatus status, String senderId, String senderName, String senderAvatarUrl, String? postId, String? commentId, String? messageId, String? storyId, String? eventId, Map<String, dynamic>? data, String? actionUrl, String? actionType
});




}
/// @nodoc
class __$NotificationModelCopyWithImpl<$Res>
    implements _$NotificationModelCopyWith<$Res> {
  __$NotificationModelCopyWithImpl(this._self, this._then);

  final _NotificationModel _self;
  final $Res Function(_NotificationModel) _then;

/// Create a copy of NotificationModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? title = null,Object? body = null,Object? type = null,Object? timestamp = null,Object? status = null,Object? senderId = null,Object? senderName = null,Object? senderAvatarUrl = null,Object? postId = freezed,Object? commentId = freezed,Object? messageId = freezed,Object? storyId = freezed,Object? eventId = freezed,Object? data = freezed,Object? actionUrl = freezed,Object? actionType = freezed,}) {
  return _then(_NotificationModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,body: null == body ? _self.body : body // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as NotificationType,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as NotificationStatus,senderId: null == senderId ? _self.senderId : senderId // ignore: cast_nullable_to_non_nullable
as String,senderName: null == senderName ? _self.senderName : senderName // ignore: cast_nullable_to_non_nullable
as String,senderAvatarUrl: null == senderAvatarUrl ? _self.senderAvatarUrl : senderAvatarUrl // ignore: cast_nullable_to_non_nullable
as String,postId: freezed == postId ? _self.postId : postId // ignore: cast_nullable_to_non_nullable
as String?,commentId: freezed == commentId ? _self.commentId : commentId // ignore: cast_nullable_to_non_nullable
as String?,messageId: freezed == messageId ? _self.messageId : messageId // ignore: cast_nullable_to_non_nullable
as String?,storyId: freezed == storyId ? _self.storyId : storyId // ignore: cast_nullable_to_non_nullable
as String?,eventId: freezed == eventId ? _self.eventId : eventId // ignore: cast_nullable_to_non_nullable
as String?,data: freezed == data ? _self._data : data // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,actionUrl: freezed == actionUrl ? _self.actionUrl : actionUrl // ignore: cast_nullable_to_non_nullable
as String?,actionType: freezed == actionType ? _self.actionType : actionType // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$NotificationPreferences {

 bool get followNotifications; bool get likeNotifications; bool get commentNotifications; bool get mentionNotifications; bool get messageNotifications; bool get shareNotifications; bool get adminNotifications; bool get storyNotifications; bool get eventNotifications; bool get systemNotifications; bool get pushNotifications; bool get inAppNotifications; bool get emailNotifications;
/// Create a copy of NotificationPreferences
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$NotificationPreferencesCopyWith<NotificationPreferences> get copyWith => _$NotificationPreferencesCopyWithImpl<NotificationPreferences>(this as NotificationPreferences, _$identity);

  /// Serializes this NotificationPreferences to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is NotificationPreferences&&(identical(other.followNotifications, followNotifications) || other.followNotifications == followNotifications)&&(identical(other.likeNotifications, likeNotifications) || other.likeNotifications == likeNotifications)&&(identical(other.commentNotifications, commentNotifications) || other.commentNotifications == commentNotifications)&&(identical(other.mentionNotifications, mentionNotifications) || other.mentionNotifications == mentionNotifications)&&(identical(other.messageNotifications, messageNotifications) || other.messageNotifications == messageNotifications)&&(identical(other.shareNotifications, shareNotifications) || other.shareNotifications == shareNotifications)&&(identical(other.adminNotifications, adminNotifications) || other.adminNotifications == adminNotifications)&&(identical(other.storyNotifications, storyNotifications) || other.storyNotifications == storyNotifications)&&(identical(other.eventNotifications, eventNotifications) || other.eventNotifications == eventNotifications)&&(identical(other.systemNotifications, systemNotifications) || other.systemNotifications == systemNotifications)&&(identical(other.pushNotifications, pushNotifications) || other.pushNotifications == pushNotifications)&&(identical(other.inAppNotifications, inAppNotifications) || other.inAppNotifications == inAppNotifications)&&(identical(other.emailNotifications, emailNotifications) || other.emailNotifications == emailNotifications));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,followNotifications,likeNotifications,commentNotifications,mentionNotifications,messageNotifications,shareNotifications,adminNotifications,storyNotifications,eventNotifications,systemNotifications,pushNotifications,inAppNotifications,emailNotifications);

@override
String toString() {
  return 'NotificationPreferences(followNotifications: $followNotifications, likeNotifications: $likeNotifications, commentNotifications: $commentNotifications, mentionNotifications: $mentionNotifications, messageNotifications: $messageNotifications, shareNotifications: $shareNotifications, adminNotifications: $adminNotifications, storyNotifications: $storyNotifications, eventNotifications: $eventNotifications, systemNotifications: $systemNotifications, pushNotifications: $pushNotifications, inAppNotifications: $inAppNotifications, emailNotifications: $emailNotifications)';
}


}

/// @nodoc
abstract mixin class $NotificationPreferencesCopyWith<$Res>  {
  factory $NotificationPreferencesCopyWith(NotificationPreferences value, $Res Function(NotificationPreferences) _then) = _$NotificationPreferencesCopyWithImpl;
@useResult
$Res call({
 bool followNotifications, bool likeNotifications, bool commentNotifications, bool mentionNotifications, bool messageNotifications, bool shareNotifications, bool adminNotifications, bool storyNotifications, bool eventNotifications, bool systemNotifications, bool pushNotifications, bool inAppNotifications, bool emailNotifications
});




}
/// @nodoc
class _$NotificationPreferencesCopyWithImpl<$Res>
    implements $NotificationPreferencesCopyWith<$Res> {
  _$NotificationPreferencesCopyWithImpl(this._self, this._then);

  final NotificationPreferences _self;
  final $Res Function(NotificationPreferences) _then;

/// Create a copy of NotificationPreferences
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? followNotifications = null,Object? likeNotifications = null,Object? commentNotifications = null,Object? mentionNotifications = null,Object? messageNotifications = null,Object? shareNotifications = null,Object? adminNotifications = null,Object? storyNotifications = null,Object? eventNotifications = null,Object? systemNotifications = null,Object? pushNotifications = null,Object? inAppNotifications = null,Object? emailNotifications = null,}) {
  return _then(_self.copyWith(
followNotifications: null == followNotifications ? _self.followNotifications : followNotifications // ignore: cast_nullable_to_non_nullable
as bool,likeNotifications: null == likeNotifications ? _self.likeNotifications : likeNotifications // ignore: cast_nullable_to_non_nullable
as bool,commentNotifications: null == commentNotifications ? _self.commentNotifications : commentNotifications // ignore: cast_nullable_to_non_nullable
as bool,mentionNotifications: null == mentionNotifications ? _self.mentionNotifications : mentionNotifications // ignore: cast_nullable_to_non_nullable
as bool,messageNotifications: null == messageNotifications ? _self.messageNotifications : messageNotifications // ignore: cast_nullable_to_non_nullable
as bool,shareNotifications: null == shareNotifications ? _self.shareNotifications : shareNotifications // ignore: cast_nullable_to_non_nullable
as bool,adminNotifications: null == adminNotifications ? _self.adminNotifications : adminNotifications // ignore: cast_nullable_to_non_nullable
as bool,storyNotifications: null == storyNotifications ? _self.storyNotifications : storyNotifications // ignore: cast_nullable_to_non_nullable
as bool,eventNotifications: null == eventNotifications ? _self.eventNotifications : eventNotifications // ignore: cast_nullable_to_non_nullable
as bool,systemNotifications: null == systemNotifications ? _self.systemNotifications : systemNotifications // ignore: cast_nullable_to_non_nullable
as bool,pushNotifications: null == pushNotifications ? _self.pushNotifications : pushNotifications // ignore: cast_nullable_to_non_nullable
as bool,inAppNotifications: null == inAppNotifications ? _self.inAppNotifications : inAppNotifications // ignore: cast_nullable_to_non_nullable
as bool,emailNotifications: null == emailNotifications ? _self.emailNotifications : emailNotifications // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [NotificationPreferences].
extension NotificationPreferencesPatterns on NotificationPreferences {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _NotificationPreferences value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _NotificationPreferences() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _NotificationPreferences value)  $default,){
final _that = this;
switch (_that) {
case _NotificationPreferences():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _NotificationPreferences value)?  $default,){
final _that = this;
switch (_that) {
case _NotificationPreferences() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( bool followNotifications,  bool likeNotifications,  bool commentNotifications,  bool mentionNotifications,  bool messageNotifications,  bool shareNotifications,  bool adminNotifications,  bool storyNotifications,  bool eventNotifications,  bool systemNotifications,  bool pushNotifications,  bool inAppNotifications,  bool emailNotifications)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _NotificationPreferences() when $default != null:
return $default(_that.followNotifications,_that.likeNotifications,_that.commentNotifications,_that.mentionNotifications,_that.messageNotifications,_that.shareNotifications,_that.adminNotifications,_that.storyNotifications,_that.eventNotifications,_that.systemNotifications,_that.pushNotifications,_that.inAppNotifications,_that.emailNotifications);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( bool followNotifications,  bool likeNotifications,  bool commentNotifications,  bool mentionNotifications,  bool messageNotifications,  bool shareNotifications,  bool adminNotifications,  bool storyNotifications,  bool eventNotifications,  bool systemNotifications,  bool pushNotifications,  bool inAppNotifications,  bool emailNotifications)  $default,) {final _that = this;
switch (_that) {
case _NotificationPreferences():
return $default(_that.followNotifications,_that.likeNotifications,_that.commentNotifications,_that.mentionNotifications,_that.messageNotifications,_that.shareNotifications,_that.adminNotifications,_that.storyNotifications,_that.eventNotifications,_that.systemNotifications,_that.pushNotifications,_that.inAppNotifications,_that.emailNotifications);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( bool followNotifications,  bool likeNotifications,  bool commentNotifications,  bool mentionNotifications,  bool messageNotifications,  bool shareNotifications,  bool adminNotifications,  bool storyNotifications,  bool eventNotifications,  bool systemNotifications,  bool pushNotifications,  bool inAppNotifications,  bool emailNotifications)?  $default,) {final _that = this;
switch (_that) {
case _NotificationPreferences() when $default != null:
return $default(_that.followNotifications,_that.likeNotifications,_that.commentNotifications,_that.mentionNotifications,_that.messageNotifications,_that.shareNotifications,_that.adminNotifications,_that.storyNotifications,_that.eventNotifications,_that.systemNotifications,_that.pushNotifications,_that.inAppNotifications,_that.emailNotifications);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _NotificationPreferences implements NotificationPreferences {
  const _NotificationPreferences({this.followNotifications = true, this.likeNotifications = true, this.commentNotifications = true, this.mentionNotifications = true, this.messageNotifications = true, this.shareNotifications = true, this.adminNotifications = true, this.storyNotifications = true, this.eventNotifications = true, this.systemNotifications = true, this.pushNotifications = true, this.inAppNotifications = true, this.emailNotifications = true});
  factory _NotificationPreferences.fromJson(Map<String, dynamic> json) => _$NotificationPreferencesFromJson(json);

@override@JsonKey() final  bool followNotifications;
@override@JsonKey() final  bool likeNotifications;
@override@JsonKey() final  bool commentNotifications;
@override@JsonKey() final  bool mentionNotifications;
@override@JsonKey() final  bool messageNotifications;
@override@JsonKey() final  bool shareNotifications;
@override@JsonKey() final  bool adminNotifications;
@override@JsonKey() final  bool storyNotifications;
@override@JsonKey() final  bool eventNotifications;
@override@JsonKey() final  bool systemNotifications;
@override@JsonKey() final  bool pushNotifications;
@override@JsonKey() final  bool inAppNotifications;
@override@JsonKey() final  bool emailNotifications;

/// Create a copy of NotificationPreferences
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$NotificationPreferencesCopyWith<_NotificationPreferences> get copyWith => __$NotificationPreferencesCopyWithImpl<_NotificationPreferences>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$NotificationPreferencesToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _NotificationPreferences&&(identical(other.followNotifications, followNotifications) || other.followNotifications == followNotifications)&&(identical(other.likeNotifications, likeNotifications) || other.likeNotifications == likeNotifications)&&(identical(other.commentNotifications, commentNotifications) || other.commentNotifications == commentNotifications)&&(identical(other.mentionNotifications, mentionNotifications) || other.mentionNotifications == mentionNotifications)&&(identical(other.messageNotifications, messageNotifications) || other.messageNotifications == messageNotifications)&&(identical(other.shareNotifications, shareNotifications) || other.shareNotifications == shareNotifications)&&(identical(other.adminNotifications, adminNotifications) || other.adminNotifications == adminNotifications)&&(identical(other.storyNotifications, storyNotifications) || other.storyNotifications == storyNotifications)&&(identical(other.eventNotifications, eventNotifications) || other.eventNotifications == eventNotifications)&&(identical(other.systemNotifications, systemNotifications) || other.systemNotifications == systemNotifications)&&(identical(other.pushNotifications, pushNotifications) || other.pushNotifications == pushNotifications)&&(identical(other.inAppNotifications, inAppNotifications) || other.inAppNotifications == inAppNotifications)&&(identical(other.emailNotifications, emailNotifications) || other.emailNotifications == emailNotifications));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,followNotifications,likeNotifications,commentNotifications,mentionNotifications,messageNotifications,shareNotifications,adminNotifications,storyNotifications,eventNotifications,systemNotifications,pushNotifications,inAppNotifications,emailNotifications);

@override
String toString() {
  return 'NotificationPreferences(followNotifications: $followNotifications, likeNotifications: $likeNotifications, commentNotifications: $commentNotifications, mentionNotifications: $mentionNotifications, messageNotifications: $messageNotifications, shareNotifications: $shareNotifications, adminNotifications: $adminNotifications, storyNotifications: $storyNotifications, eventNotifications: $eventNotifications, systemNotifications: $systemNotifications, pushNotifications: $pushNotifications, inAppNotifications: $inAppNotifications, emailNotifications: $emailNotifications)';
}


}

/// @nodoc
abstract mixin class _$NotificationPreferencesCopyWith<$Res> implements $NotificationPreferencesCopyWith<$Res> {
  factory _$NotificationPreferencesCopyWith(_NotificationPreferences value, $Res Function(_NotificationPreferences) _then) = __$NotificationPreferencesCopyWithImpl;
@override @useResult
$Res call({
 bool followNotifications, bool likeNotifications, bool commentNotifications, bool mentionNotifications, bool messageNotifications, bool shareNotifications, bool adminNotifications, bool storyNotifications, bool eventNotifications, bool systemNotifications, bool pushNotifications, bool inAppNotifications, bool emailNotifications
});




}
/// @nodoc
class __$NotificationPreferencesCopyWithImpl<$Res>
    implements _$NotificationPreferencesCopyWith<$Res> {
  __$NotificationPreferencesCopyWithImpl(this._self, this._then);

  final _NotificationPreferences _self;
  final $Res Function(_NotificationPreferences) _then;

/// Create a copy of NotificationPreferences
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? followNotifications = null,Object? likeNotifications = null,Object? commentNotifications = null,Object? mentionNotifications = null,Object? messageNotifications = null,Object? shareNotifications = null,Object? adminNotifications = null,Object? storyNotifications = null,Object? eventNotifications = null,Object? systemNotifications = null,Object? pushNotifications = null,Object? inAppNotifications = null,Object? emailNotifications = null,}) {
  return _then(_NotificationPreferences(
followNotifications: null == followNotifications ? _self.followNotifications : followNotifications // ignore: cast_nullable_to_non_nullable
as bool,likeNotifications: null == likeNotifications ? _self.likeNotifications : likeNotifications // ignore: cast_nullable_to_non_nullable
as bool,commentNotifications: null == commentNotifications ? _self.commentNotifications : commentNotifications // ignore: cast_nullable_to_non_nullable
as bool,mentionNotifications: null == mentionNotifications ? _self.mentionNotifications : mentionNotifications // ignore: cast_nullable_to_non_nullable
as bool,messageNotifications: null == messageNotifications ? _self.messageNotifications : messageNotifications // ignore: cast_nullable_to_non_nullable
as bool,shareNotifications: null == shareNotifications ? _self.shareNotifications : shareNotifications // ignore: cast_nullable_to_non_nullable
as bool,adminNotifications: null == adminNotifications ? _self.adminNotifications : adminNotifications // ignore: cast_nullable_to_non_nullable
as bool,storyNotifications: null == storyNotifications ? _self.storyNotifications : storyNotifications // ignore: cast_nullable_to_non_nullable
as bool,eventNotifications: null == eventNotifications ? _self.eventNotifications : eventNotifications // ignore: cast_nullable_to_non_nullable
as bool,systemNotifications: null == systemNotifications ? _self.systemNotifications : systemNotifications // ignore: cast_nullable_to_non_nullable
as bool,pushNotifications: null == pushNotifications ? _self.pushNotifications : pushNotifications // ignore: cast_nullable_to_non_nullable
as bool,inAppNotifications: null == inAppNotifications ? _self.inAppNotifications : inAppNotifications // ignore: cast_nullable_to_non_nullable
as bool,emailNotifications: null == emailNotifications ? _self.emailNotifications : emailNotifications // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
