import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:billionaires_social/features/profile/models/profile_model.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final userSearchServiceProvider = Provider<UserSearchService>((ref) {
  return UserSearchService();
});

class UserSearchService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  UserSearchService();

  /// Search users by username, name, or email with advanced filtering
  Future<List<ProfileModel>> searchUsers({
    required String query,
    int limit = 20,
    List<String>? excludeUserIds,
    bool verifiedOnly = false,
    String? userType,
    bool followersOnly = false,
    String? currentUserId,
  }) async {
    try {
      if (query.trim().isEmpty) return [];

      final searchQuery = query.toLowerCase().trim();
      final results = <ProfileModel>[];
      final seenUserIds = <String>{};

      // Add excluded user IDs to seen set
      if (excludeUserIds != null) {
        seenUserIds.addAll(excludeUserIds);
      }

      // Search by username (exact and prefix match)
      await _searchByUsername(
        searchQuery,
        results,
        seenUserIds,
        limit,
        verifiedOnly,
        userType,
      );

      // Search by display name if we need more results
      if (results.length < limit) {
        await _searchByDisplayName(
          searchQuery,
          results,
          seenUserIds,
          limit - results.length,
          verifiedOnly,
          userType,
        );
      }

      // Filter by followers if requested
      if (followersOnly && currentUserId != null) {
        final filteredResults = await _filterByFollowers(
          results,
          currentUserId,
        );
        return filteredResults.take(limit).toList();
      }

      return results.take(limit).toList();
    } catch (e) {
      debugPrint('❌ Error searching users: $e');
      return [];
    }
  }

  /// Search users by username with exact and prefix matching
  Future<void> _searchByUsername(
    String query,
    List<ProfileModel> results,
    Set<String> seenUserIds,
    int limit,
    bool verifiedOnly,
    String? userType,
  ) async {
    // Exact username match
    final exactQuery = _firestore
        .collection('users')
        .where('username', isEqualTo: query)
        .limit(5);

    final exactSnapshot = await exactQuery.get();
    await _processSearchResults(
      exactSnapshot,
      results,
      seenUserIds,
      verifiedOnly,
      userType,
    );

    // Prefix username match
    if (results.length < limit) {
      final prefixQuery = _firestore
          .collection('users')
          .where('username', isGreaterThanOrEqualTo: query)
          .where('username', isLessThan: '$query\uf8ff')
          .limit(limit - results.length);

      final prefixSnapshot = await prefixQuery.get();
      await _processSearchResults(
        prefixSnapshot,
        results,
        seenUserIds,
        verifiedOnly,
        userType,
      );
    }
  }

  /// Search users by display name
  Future<void> _searchByDisplayName(
    String query,
    List<ProfileModel> results,
    Set<String> seenUserIds,
    int limit,
    bool verifiedOnly,
    String? userType,
  ) async {
    final nameQuery = _firestore
        .collection('users')
        .where('searchKeywords', arrayContains: query)
        .limit(limit);

    final nameSnapshot = await nameQuery.get();
    await _processSearchResults(
      nameSnapshot,
      results,
      seenUserIds,
      verifiedOnly,
      userType,
    );
  }

  /// Process search results and apply filters
  Future<void> _processSearchResults(
    QuerySnapshot snapshot,
    List<ProfileModel> results,
    Set<String> seenUserIds,
    bool verifiedOnly,
    String? userType,
  ) async {
    for (final doc in snapshot.docs) {
      try {
        final data = doc.data() as Map<String, dynamic>;

        // Skip if already seen
        if (seenUserIds.contains(doc.id)) continue;

        // Apply verified filter
        if (verifiedOnly && (data['isVerified'] != true)) continue;

        // Apply user type filter
        if (userType != null && data['userType'] != userType) continue;

        final profile = _mapFirestoreToProfile(data, doc.id);

        results.add(profile);
        seenUserIds.add(doc.id);
      } catch (e) {
        debugPrint('❌ Error processing search result: $e');
      }
    }
  }

  /// Map Firestore document data to ProfileModel, handling Timestamp conversion
  ProfileModel _mapFirestoreToProfile(Map<String, dynamic> data, String docId) {
    // Helper function to safely convert Firestore Timestamp to DateTime
    DateTime? timestampToDateTime(dynamic timestamp) {
      if (timestamp == null) return null;
      if (timestamp is Timestamp) return timestamp.toDate();
      if (timestamp is String) return DateTime.tryParse(timestamp);
      return null;
    }

    return ProfileModel(
      id: docId,
      username: data['username'] ?? data['email']?.split('@')[0] ?? 'user',
      name: data['name'] ?? '',
      profilePictureUrl: data['profilePictureUrl'] ?? '',
      bio: data['bio'] ?? '',
      postCount: data['postCount'] ?? 0,
      followerCount: data['followerCount'] ?? 0,
      followingCount: data['followingCount'] ?? 0,
      isVerified: data['isVerified'] ?? false,
      isBillionaire: data['isBillionaire'] ?? false,
      isAdmin: data['isAdmin'] ?? false,
      isBusinessAccount: data['isBusinessAccount'] ?? false,
      businessName: data['businessName'],
      businessCategory: data['businessCategory'],
      website: data['website'],
      email: data['email'],
      phone: data['phone'],
      location: data['location'],
      bannerImageUrl: data['bannerImageUrl'],
      isPrivate: data['isPrivate'] ?? false,
      allowMessages: data['allowMessages'] ?? true,
      showActivityStatus: data['showActivityStatus'] ?? true,
      userType: data['userType'] ?? 'regular',
      createdAt: timestampToDateTime(data['createdAt']),
      updatedAt: timestampToDateTime(data['updatedAt']),
      businessCreatedAt: timestampToDateTime(data['businessCreatedAt']),
      businessVerifiedAt: timestampToDateTime(data['businessVerifiedAt']),
      businessVerified: data['businessVerified'] ?? false,
      businessExclusive: data['businessExclusive'] ?? false,
      businessLogoUrl: data['businessLogoUrl'],
      businessEmail: data['businessEmail'],
      businessPhone: data['businessPhone'],
      businessDescription: data['businessDescription'],
      businessWebsite: data['businessWebsite'],
      businessAddress: data['businessAddress'],
    );
  }

  /// Filter results by followers
  Future<List<ProfileModel>> _filterByFollowers(
    List<ProfileModel> users,
    String currentUserId,
  ) async {
    try {
      final followingDoc = await _firestore
          .collection('users')
          .doc(currentUserId)
          .collection('following')
          .get();

      final followingIds = followingDoc.docs.map((doc) => doc.id).toSet();

      return users.where((user) => followingIds.contains(user.id)).toList();
    } catch (e) {
      debugPrint('❌ Error filtering by followers: $e');
      return users;
    }
  }

  /// Search users for mentions (optimized for real-time search)
  Future<List<ProfileModel>> searchForMentions({
    required String query,
    String? currentUserId,
    int limit = 10,
  }) async {
    return searchUsers(
      query: query,
      limit: limit,
      excludeUserIds: currentUserId != null ? [currentUserId] : null,
      followersOnly: false,
    );
  }

  /// Search users for tagging (prioritize followers)
  Future<List<ProfileModel>> searchForTagging({
    required String query,
    String? currentUserId,
    int limit = 15,
  }) async {
    final results = <ProfileModel>[];

    // First, search followers
    if (currentUserId != null) {
      final followerResults = await searchUsers(
        query: query,
        limit: limit ~/ 2,
        excludeUserIds: [currentUserId],
        followersOnly: true,
        currentUserId: currentUserId,
      );
      results.addAll(followerResults);
    }

    // Then, search all users if we need more results
    if (results.length < limit) {
      final allResults = await searchUsers(
        query: query,
        limit: limit - results.length,
        excludeUserIds: [
          if (currentUserId != null) currentUserId,
          ...results.map((u) => u.id),
        ],
      );
      results.addAll(allResults);
    }

    return results;
  }

  /// Search users for collaboration invites (verified users only)
  Future<List<ProfileModel>> searchForCollaboration({
    required String query,
    String? currentUserId,
    int limit = 10,
  }) async {
    return searchUsers(
      query: query,
      limit: limit,
      excludeUserIds: currentUserId != null ? [currentUserId] : null,
      verifiedOnly: true,
    );
  }

  /// Get recent search history
  Future<List<ProfileModel>> getRecentSearches({
    String? currentUserId,
    int limit = 5,
  }) async {
    try {
      if (currentUserId == null) return [];

      final recentSearches = await _firestore
          .collection('users')
          .doc(currentUserId)
          .collection('recentSearches')
          .orderBy('timestamp', descending: true)
          .limit(limit)
          .get();

      final userIds = recentSearches.docs
          .map((doc) => doc.data()['userId'] as String)
          .toList();

      if (userIds.isEmpty) return [];

      final users = await _firestore
          .collection('users')
          .where(FieldPath.documentId, whereIn: userIds)
          .get();

      return users.docs
          .map((doc) => _mapFirestoreToProfile(doc.data(), doc.id))
          .toList();
    } catch (e) {
      debugPrint('❌ Error getting recent searches: $e');
      return [];
    }
  }

  /// Save search to history
  Future<void> saveSearchToHistory({
    required String currentUserId,
    required String searchedUserId,
  }) async {
    try {
      await _firestore
          .collection('users')
          .doc(currentUserId)
          .collection('recentSearches')
          .doc(searchedUserId)
          .set({
            'userId': searchedUserId,
            'timestamp': FieldValue.serverTimestamp(),
          });
    } catch (e) {
      debugPrint('❌ Error saving search to history: $e');
    }
  }

  /// Clear search history
  Future<void> clearSearchHistory(String currentUserId) async {
    try {
      final batch = _firestore.batch();
      final searches = await _firestore
          .collection('users')
          .doc(currentUserId)
          .collection('recentSearches')
          .get();

      for (final doc in searches.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
    } catch (e) {
      debugPrint('❌ Error clearing search history: $e');
    }
  }
}
