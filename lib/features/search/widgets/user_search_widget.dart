import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/profile/models/profile_model.dart';
import 'package:billionaires_social/features/search/services/user_search_service.dart';
import 'package:billionaires_social/features/auth/providers/auth_provider.dart';
import 'package:cached_network_image/cached_network_image.dart';

class UserSearchWidget extends ConsumerStatefulWidget {
  final String? hintText;
  final Function(ProfileModel) onUserSelected;
  final List<String>? excludeUserIds;
  final bool verifiedOnly;
  final bool followersOnly;
  final UserSearchType searchType;
  final int maxResults;

  const UserSearchWidget({
    super.key,
    this.hintText,
    required this.onUserSelected,
    this.excludeUserIds,
    this.verifiedOnly = false,
    this.followersOnly = false,
    this.searchType = UserSearchType.general,
    this.maxResults = 10,
  });

  @override
  ConsumerState<UserSearchWidget> createState() => _UserSearchWidgetState();
}

class _UserSearchWidgetState extends ConsumerState<UserSearchWidget> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  List<ProfileModel> _searchResults = [];
  List<ProfileModel> _recentSearches = [];
  bool _isLoading = false;
  bool _showResults = false;

  @override
  void initState() {
    super.initState();
    _loadRecentSearches();
    _focusNode.addListener(_onFocusChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _onFocusChanged() {
    if (_focusNode.hasFocus && _searchController.text.isEmpty) {
      setState(() => _showResults = true);
    } else if (!_focusNode.hasFocus) {
      // Delay hiding to allow for tap selection
      Future.delayed(const Duration(milliseconds: 200), () {
        if (mounted) setState(() => _showResults = false);
      });
    }
  }

  Future<void> _loadRecentSearches() async {
    final currentUser = ref.read(authProvider).value;
    if (currentUser == null) return;

    try {
      final userSearchService = ref.read(userSearchServiceProvider);
      final recent = await userSearchService.getRecentSearches(
        currentUserId: currentUser.uid,
        limit: 5,
      );

      if (mounted) {
        setState(() => _recentSearches = recent);
      }
    } catch (e) {
      debugPrint('Error loading recent searches: $e');
    }
  }

  Future<void> _performSearch(String query) async {
    if (query.trim().isEmpty) {
      setState(() {
        _searchResults = [];
        _isLoading = false;
      });
      return;
    }

    setState(() => _isLoading = true);

    try {
      final currentUser = ref.read(authProvider).value;
      final userSearchService = ref.read(userSearchServiceProvider);

      List<ProfileModel> results;

      switch (widget.searchType) {
        case UserSearchType.mentions:
          results = await userSearchService.searchForMentions(
            query: query,
            currentUserId: currentUser?.uid,
            limit: widget.maxResults,
          );
          break;
        case UserSearchType.tagging:
          results = await userSearchService.searchForTagging(
            query: query,
            currentUserId: currentUser?.uid,
            limit: widget.maxResults,
          );
          break;
        case UserSearchType.collaboration:
          results = await userSearchService.searchForCollaboration(
            query: query,
            currentUserId: currentUser?.uid,
            limit: widget.maxResults,
          );
          break;
        case UserSearchType.general:
          results = await userSearchService.searchUsers(
            query: query,
            limit: widget.maxResults,
            excludeUserIds: widget.excludeUserIds,
            verifiedOnly: widget.verifiedOnly,
            followersOnly: widget.followersOnly,
            currentUserId: currentUser?.uid,
          );
          break;
      }

      if (mounted) {
        setState(() {
          _searchResults = results;
          _isLoading = false;
          _showResults = true;
        });
      }
    } catch (e) {
      debugPrint('Error performing search: $e');
      if (mounted) {
        setState(() {
          _searchResults = [];
          _isLoading = false;
        });
      }
    }
  }

  void _onUserSelected(ProfileModel user) async {
    final currentUser = ref.read(authProvider).value;
    if (currentUser != null) {
      // Save to search history
      final userSearchService = ref.read(userSearchServiceProvider);
      await userSearchService.saveSearchToHistory(
        currentUserId: currentUser.uid,
        searchedUserId: user.id,
      );
    }

    widget.onUserSelected(user);
    _searchController.clear();
    setState(() {
      _showResults = false;
      _searchResults = [];
    });
    _focusNode.unfocus();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Search Input
        TextField(
          controller: _searchController,
          focusNode: _focusNode,
          decoration: InputDecoration(
            hintText: widget.hintText ?? 'Search users...',
            prefixIcon: const Icon(Icons.search),
            suffixIcon: _searchController.text.isNotEmpty
                ? IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      _searchController.clear();
                      _performSearch('');
                    },
                  )
                : null,
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
            filled: true,
            fillColor: Colors.grey[100],
          ),
          onChanged: (value) {
            _performSearch(value);
          },
          onTap: () {
            setState(() => _showResults = true);
          },
        ),

        // Search Results
        if (_showResults) ...[
          const SizedBox(height: 8),
          Container(
            constraints: const BoxConstraints(maxHeight: 300),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: _buildSearchResults(),
          ),
        ],
      ],
    );
  }

  Widget _buildSearchResults() {
    if (_isLoading) {
      return const Padding(
        padding: EdgeInsets.all(16),
        child: Center(child: CircularProgressIndicator()),
      );
    }

    final hasSearchQuery = _searchController.text.trim().isNotEmpty;
    final results = hasSearchQuery ? _searchResults : _recentSearches;

    if (results.isEmpty) {
      return Padding(
        padding: const EdgeInsets.all(16),
        child: Text(
          hasSearchQuery ? 'No users found' : 'No recent searches',
          style: TextStyle(color: Colors.grey[600], fontSize: 14),
          textAlign: TextAlign.center,
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      itemCount: results.length,
      itemBuilder: (context, index) {
        final user = results[index];
        return _buildUserTile(user, !hasSearchQuery);
      },
    );
  }

  Widget _buildUserTile(ProfileModel user, bool isRecent) {
    return ListTile(
      leading: CircleAvatar(
        radius: 20,
        backgroundImage: user.profilePictureUrl.isNotEmpty
            ? CachedNetworkImageProvider(user.profilePictureUrl)
            : null,
        child: user.profilePictureUrl.isEmpty ? const Icon(Icons.person) : null,
      ),
      title: Row(
        children: [
          Expanded(
            child: Text(
              user.name.isNotEmpty ? user.name : user.username,
              style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 14),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (user.isVerified) ...[
            const SizedBox(width: 4),
            const Icon(Icons.verified, color: Colors.blue, size: 16),
          ],
        ],
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '@${user.username}',
            style: TextStyle(color: Colors.grey[600], fontSize: 12),
          ),
          if (user.followerCount > 0)
            Text(
              '${_formatCount(user.followerCount)} followers',
              style: TextStyle(color: Colors.grey[500], fontSize: 11),
            ),
        ],
      ),
      trailing: isRecent
          ? Icon(Icons.history, color: Colors.grey[400], size: 16)
          : null,
      onTap: () => _onUserSelected(user),
    );
  }

  String _formatCount(int count) {
    if (count >= 1000000) {
      return '${(count / 1000000).toStringAsFixed(1)}M';
    } else if (count >= 1000) {
      return '${(count / 1000).toStringAsFixed(1)}K';
    }
    return count.toString();
  }
}

enum UserSearchType { general, mentions, tagging, collaboration }
