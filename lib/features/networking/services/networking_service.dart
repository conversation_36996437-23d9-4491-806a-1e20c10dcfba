import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:billionaires_social/features/networking/models/networking_models.dart';

class NetworkingService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;

  // Collections
  static const String _connectionsCollection = 'network_connections';
  static const String _profilesCollection = 'network_profiles';
  static const String _eventsCollection = 'networking_events';
  static const String _businessCardsCollection = 'business_cards';
  static const String _recommendationsCollection = 'networking_recommendations';
  static const String _messagesCollection = 'networking_messages';

  // Connection Management
  Future<NetworkConnection?> sendConnectionRequest({
    required String targetUserId,
    required NetworkConnectionType type,
    String? message,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) throw Exception('User not authenticated');

      // Check if connection already exists
      final existingConnection = await _firestore
          .collection(_connectionsCollection)
          .where('userId', isEqualTo: currentUser.uid)
          .where('connectedUserId', isEqualTo: targetUserId)
          .get();

      if (existingConnection.docs.isNotEmpty) {
        throw Exception('Connection request already exists');
      }

      final connection = NetworkConnection(
        id: '', // Will be set by Firestore
        userId: currentUser.uid,
        connectedUserId: targetUserId,
        status: ConnectionStatus.pending,
        createdAt: DateTime.now(),
        message: message,
        type: type,
      );

      final docRef = await _firestore
          .collection(_connectionsCollection)
          .add(connection.toFirestore());

      return connection.copyWith(id: docRef.id);
    } catch (e) {
      debugPrint('❌ Error sending connection request: $e');
      rethrow;
    }
  }

  Future<bool> acceptConnectionRequest(String connectionId) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) throw Exception('User not authenticated');

      await _firestore
          .collection(_connectionsCollection)
          .doc(connectionId)
          .update({
            'status': ConnectionStatus.accepted.name,
            'acceptedAt': Timestamp.fromDate(DateTime.now()),
          });

      // Update connection counts for both users
      await _updateConnectionCounts(connectionId);

      return true;
    } catch (e) {
      debugPrint('❌ Error accepting connection request: $e');
      return false;
    }
  }

  Future<bool> rejectConnectionRequest(String connectionId) async {
    try {
      await _firestore
          .collection(_connectionsCollection)
          .doc(connectionId)
          .update({'status': ConnectionStatus.rejected.name});
      return true;
    } catch (e) {
      debugPrint('❌ Error rejecting connection request: $e');
      return false;
    }
  }

  Future<bool> blockConnection(String connectionId) async {
    try {
      await _firestore
          .collection(_connectionsCollection)
          .doc(connectionId)
          .update({'status': ConnectionStatus.blocked.name});
      return true;
    } catch (e) {
      debugPrint('❌ Error blocking connection: $e');
      return false;
    }
  }

  Stream<List<NetworkConnection>> getConnectionRequests() {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return Stream.value([]);

    return _firestore
        .collection(_connectionsCollection)
        .where('connectedUserId', isEqualTo: currentUser.uid)
        .where('status', isEqualTo: ConnectionStatus.pending.name)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map(
                (doc) => NetworkConnectionFirestore.fromFirestore(
                  doc.id,
                  doc.data(),
                ),
              )
              .toList(),
        );
  }

  Stream<List<NetworkConnection>> getAcceptedConnections() {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return Stream.value([]);

    return _firestore
        .collection(_connectionsCollection)
        .where('status', isEqualTo: ConnectionStatus.accepted.name)
        .where(
          Filter.or(
            Filter('userId', isEqualTo: currentUser.uid),
            Filter('connectedUserId', isEqualTo: currentUser.uid),
          ),
        )
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map(
                (doc) => NetworkConnectionFirestore.fromFirestore(
                  doc.id,
                  doc.data(),
                ),
              )
              .toList(),
        );
  }

  // Profile Management
  Future<NetworkProfile?> createNetworkProfile(NetworkProfile profile) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) throw Exception('User not authenticated');

      final docRef = await _firestore
          .collection(_profilesCollection)
          .add(profile.toFirestore());

      return profile.copyWith(id: docRef.id);
    } catch (e) {
      debugPrint('❌ Error creating network profile: $e');
      return null;
    }
  }

  Future<NetworkProfile?> updateNetworkProfile(NetworkProfile profile) async {
    try {
      await _firestore
          .collection(_profilesCollection)
          .doc(profile.id)
          .update(profile.toFirestore());

      return profile;
    } catch (e) {
      debugPrint('❌ Error updating network profile: $e');
      return null;
    }
  }

  Future<NetworkProfile?> getNetworkProfile(String userId) async {
    try {
      final doc = await _firestore
          .collection(_profilesCollection)
          .where('userId', isEqualTo: userId)
          .get();

      if (doc.docs.isEmpty) return null;

      return NetworkProfileFirestore.fromFirestore(
        doc.docs.first.id,
        doc.docs.first.data(),
      );
    } catch (e) {
      debugPrint('❌ Error getting network profile: $e');
      return null;
    }
  }

  Stream<List<NetworkProfile>> searchNetworkProfiles({
    String? query,
    List<String>? industries,
    List<String>? skills,
    String? location,
  }) {
    Query queryRef = _firestore.collection(_profilesCollection);

    if (query != null && query.isNotEmpty) {
      queryRef = queryRef
          .where('name', isGreaterThanOrEqualTo: query)
          .where('name', isLessThan: '$query\uf8ff');
    }

    if (industries != null && industries.isNotEmpty) {
      queryRef = queryRef.where('industries', arrayContainsAny: industries);
    }

    if (skills != null && skills.isNotEmpty) {
      queryRef = queryRef.where('skills', arrayContainsAny: skills);
    }

    if (location != null && location.isNotEmpty) {
      queryRef = queryRef.where('location', isEqualTo: location);
    }

    return queryRef.snapshots().map(
      (snapshot) => snapshot.docs
          .map(
            (doc) => NetworkProfileFirestore.fromFirestore(
              doc.id,
              doc.data() as Map<String, dynamic>,
            ),
          )
          .toList(),
    );
  }

  // Event Management
  Future<NetworkingEvent?> createEvent(NetworkingEvent event) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) throw Exception('User not authenticated');

      final docRef = await _firestore
          .collection(_eventsCollection)
          .add(event.toFirestore());

      return event.copyWith(id: docRef.id);
    } catch (e) {
      debugPrint('❌ Error creating event: $e');
      return null;
    }
  }

  Future<NetworkingEvent?> updateEvent(NetworkingEvent event) async {
    try {
      await _firestore
          .collection(_eventsCollection)
          .doc(event.id)
          .update(event.toFirestore());

      return event;
    } catch (e) {
      debugPrint('❌ Error updating event: $e');
      return null;
    }
  }

  Future<bool> deleteEvent(String eventId) async {
    try {
      await _firestore.collection(_eventsCollection).doc(eventId).delete();
      return true;
    } catch (e) {
      debugPrint('❌ Error deleting event: $e');
      return false;
    }
  }

  Stream<List<NetworkingEvent>> getUpcomingEvents() {
    return _firestore
        .collection(_eventsCollection)
        .where('startDate', isGreaterThan: Timestamp.fromDate(DateTime.now()))
        .orderBy('startDate')
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map(
                (doc) => NetworkingEventFirestore.fromFirestore(
                  doc.id,
                  doc.data(),
                ),
              )
              .toList(),
        );
  }

  Stream<List<NetworkingEvent>> getEventsByOrganizer(String organizerId) {
    return _firestore
        .collection(_eventsCollection)
        .where('organizerId', isEqualTo: organizerId)
        .orderBy('startDate')
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map(
                (doc) => NetworkingEventFirestore.fromFirestore(
                  doc.id,
                  doc.data(),
                ),
              )
              .toList(),
        );
  }

  // Business Card Management
  Future<BusinessCard?> createBusinessCard(BusinessCard card) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) throw Exception('User not authenticated');

      final docRef = await _firestore
          .collection(_businessCardsCollection)
          .add(card.toFirestore());

      return card.copyWith(id: docRef.id);
    } catch (e) {
      debugPrint('❌ Error creating business card: $e');
      return null;
    }
  }

  Future<BusinessCard?> updateBusinessCard(BusinessCard card) async {
    try {
      await _firestore
          .collection(_businessCardsCollection)
          .doc(card.id)
          .update(card.toFirestore());

      return card;
    } catch (e) {
      debugPrint('❌ Error updating business card: $e');
      return null;
    }
  }

  Future<BusinessCard?> getBusinessCard(String userId) async {
    try {
      final doc = await _firestore
          .collection(_businessCardsCollection)
          .where('userId', isEqualTo: userId)
          .get();

      if (doc.docs.isEmpty) return null;

      return BusinessCardFirestore.fromFirestore(
        doc.docs.first.id,
        doc.docs.first.data(),
      );
    } catch (e) {
      debugPrint('❌ Error getting business card: $e');
      return null;
    }
  }

  Future<String?> generateQRCode(String cardId) async {
    try {
      // Generate QR code URL for the business card
      final qrCodeUrl = 'https://billionaires-social.com/card/$cardId';

      // Update the business card with QR code URL
      await _firestore.collection(_businessCardsCollection).doc(cardId).update({
        'qrCodeUrl': qrCodeUrl,
      });

      return qrCodeUrl;
    } catch (e) {
      debugPrint('❌ Error generating QR code: $e');
      return null;
    }
  }

  // Recommendations
  Stream<List<NetworkingRecommendation>> getRecommendations() {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return Stream.value([]);

    return _firestore
        .collection(_recommendationsCollection)
        .where('userId', isEqualTo: currentUser.uid)
        .orderBy('score', descending: true)
        .limit(20)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map(
                (doc) => NetworkingRecommendationFirestore.fromFirestore(
                  doc.id,
                  doc.data(),
                ),
              )
              .toList(),
        );
  }

  // Messaging
  Future<NetworkingMessage?> sendMessage(NetworkingMessage message) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) throw Exception('User not authenticated');

      final docRef = await _firestore
          .collection(_messagesCollection)
          .add(message.toFirestore());

      return message.copyWith(id: docRef.id);
    } catch (e) {
      debugPrint('❌ Error sending message: $e');
      return null;
    }
  }

  Stream<List<NetworkingMessage>> getMessages(String otherUserId) {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return Stream.value([]);

    return _firestore
        .collection(_messagesCollection)
        .where(
          Filter.or(
            Filter.and(
              Filter('senderId', isEqualTo: currentUser.uid),
              Filter('receiverId', isEqualTo: otherUserId),
            ),
            Filter.and(
              Filter('senderId', isEqualTo: otherUserId),
              Filter('receiverId', isEqualTo: currentUser.uid),
            ),
          ),
        )
        .orderBy('timestamp')
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map(
                (doc) => NetworkingMessageFirestore.fromFirestore(
                  doc.id,
                  doc.data(),
                ),
              )
              .toList(),
        );
  }

  Future<bool> markMessageAsRead(String messageId) async {
    try {
      await _firestore.collection(_messagesCollection).doc(messageId).update({
        'isRead': true,
      });
      return true;
    } catch (e) {
      debugPrint('❌ Error marking message as read: $e');
      return false;
    }
  }

  // Utility Methods
  Future<void> _updateConnectionCounts(String connectionId) async {
    try {
      final connectionDoc = await _firestore
          .collection(_connectionsCollection)
          .doc(connectionId)
          .get();

      if (!connectionDoc.exists) return;

      final connection = NetworkConnectionFirestore.fromFirestore(
        connectionId,
        connectionDoc.data()!,
      );

      // Update connection count for both users
      await Future.wait([
        _firestore
            .collection(_profilesCollection)
            .where('userId', isEqualTo: connection.userId)
            .get()
            .then((snapshot) {
              if (snapshot.docs.isNotEmpty) {
                final currentCount =
                    snapshot.docs.first.data()['connectionCount'] ?? 0;
                return _firestore
                    .collection(_profilesCollection)
                    .doc(snapshot.docs.first.id)
                    .update({'connectionCount': currentCount + 1});
              }
            }),
        _firestore
            .collection(_profilesCollection)
            .where('userId', isEqualTo: connection.connectedUserId)
            .get()
            .then((snapshot) {
              if (snapshot.docs.isNotEmpty) {
                final currentCount =
                    snapshot.docs.first.data()['connectionCount'] ?? 0;
                return _firestore
                    .collection(_profilesCollection)
                    .doc(snapshot.docs.first.id)
                    .update({'connectionCount': currentCount + 1});
              }
            }),
      ]);
    } catch (e) {
      debugPrint('❌ Error updating connection counts: $e');
    }
  }

  Future<String?> uploadProfileImage(File imageFile, String userId) async {
    try {
      final ref = _storage.ref().child('network_profiles/$userId/profile.jpg');
      final uploadTask = ref.putFile(imageFile);
      final snapshot = await uploadTask;
      return await snapshot.ref.getDownloadURL();
    } catch (e) {
      debugPrint('❌ Error uploading profile image: $e');
      return null;
    }
  }

  Future<String?> uploadEventCoverImage(File imageFile, String eventId) async {
    try {
      final ref = _storage.ref().child('events/$eventId/cover.jpg');
      final uploadTask = ref.putFile(imageFile);
      final snapshot = await uploadTask;
      return await snapshot.ref.getDownloadURL();
    } catch (e) {
      debugPrint('❌ Error uploading event cover image: $e');
      return null;
    }
  }
}
