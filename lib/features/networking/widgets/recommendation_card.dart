import 'package:flutter/material.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/features/networking/models/networking_models.dart';

class RecommendationCard extends StatelessWidget {
  final NetworkingRecommendation recommendation;
  final VoidCallback onConnect;

  const RecommendationCard({
    super.key,
    required this.recommendation,
    required this.onConnect,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.luxuryGrey,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: AppTheme.accentColor.withValues(alpha: 0.1),
                child: Icon(
                  _getRecommendationIcon(recommendation.type),
                  color: AppTheme.accentColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Recommended Connection',
                      style: AppTheme.fontStyles.title.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'User ID: ${recommendation.recommendedUserId}',
                      style: AppTheme.fontStyles.body.copyWith(
                        color: AppTheme.secondaryAccentColor.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getRecommendationColor(
                    recommendation.type,
                  ).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _getRecommendationTypeText(recommendation.type),
                  style: AppTheme.fontStyles.caption.copyWith(
                    color: _getRecommendationColor(recommendation.type),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            recommendation.reason,
            style: AppTheme.fontStyles.body.copyWith(
              color: AppTheme.secondaryAccentColor.withValues(alpha: 0.8),
              fontStyle: FontStyle.italic,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Icon(Icons.star, size: 16, color: AppTheme.accentColor),
              const SizedBox(width: 4),
              Text(
                'Match Score: ${(recommendation.score * 100).toStringAsFixed(0)}%',
                style: AppTheme.fontStyles.caption.copyWith(
                  color: AppTheme.accentColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              ElevatedButton.icon(
                onPressed: onConnect,
                icon: const Icon(Icons.person_add, size: 16),
                label: const Text('Connect'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.accentColor,
                  foregroundColor: AppTheme.luxuryBlack,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  IconData _getRecommendationIcon(RecommendationType type) {
    switch (type) {
      case RecommendationType.industry:
        return Icons.business;
      case RecommendationType.skill:
        return Icons.psychology;
      case RecommendationType.location:
        return Icons.location_on;
      case RecommendationType.mutual:
        return Icons.people;
      case RecommendationType.algorithm:
        return Icons.auto_awesome;
    }
  }

  Color _getRecommendationColor(RecommendationType type) {
    switch (type) {
      case RecommendationType.industry:
        return AppTheme.accentBlue;
      case RecommendationType.skill:
        return AppTheme.accentPurple;
      case RecommendationType.location:
        return AppTheme.accentEmerald;
      case RecommendationType.mutual:
        return AppTheme.accentColor;
      case RecommendationType.algorithm:
        return AppTheme.primaryGold;
    }
  }

  String _getRecommendationTypeText(RecommendationType type) {
    switch (type) {
      case RecommendationType.industry:
        return 'INDUSTRY';
      case RecommendationType.skill:
        return 'SKILL';
      case RecommendationType.location:
        return 'LOCATION';
      case RecommendationType.mutual:
        return 'MUTUAL';
      case RecommendationType.algorithm:
        return 'AI';
    }
  }
}
