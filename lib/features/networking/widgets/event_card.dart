import 'package:flutter/material.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/features/networking/models/networking_models.dart';

class EventCard extends StatelessWidget {
  final NetworkingEvent event;
  final VoidCallback onTap;

  const EventCard({super.key, required this.event, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: AppTheme.luxuryGrey,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: AppTheme.accentColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        _getEventIcon(event.type),
                        color: AppTheme.accentColor,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            event.title,
                            style: AppTheme.fontStyles.title.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            event.organizerName,
                            style: AppTheme.fontStyles.body.copyWith(
                              color: AppTheme.secondaryAccentColor.withValues(alpha: 
                                0.7,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (event.isExclusive)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: AppTheme.accentColor,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'EXCLUSIVE',
                          style: AppTheme.fontStyles.caption.copyWith(
                            color: AppTheme.luxuryBlack,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  event.description,
                  style: AppTheme.fontStyles.body.copyWith(
                    color: AppTheme.secondaryAccentColor.withValues(alpha: 0.8),
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      size: 16,
                      color: AppTheme.secondaryAccentColor.withValues(alpha: 0.6),
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        event.location,
                        style: AppTheme.fontStyles.caption.copyWith(
                          color: AppTheme.secondaryAccentColor.withValues(alpha: 0.6),
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      Icons.access_time,
                      size: 16,
                      color: AppTheme.secondaryAccentColor.withValues(alpha: 0.6),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _formatEventDate(event.startDate),
                      style: AppTheme.fontStyles.caption.copyWith(
                        color: AppTheme.secondaryAccentColor.withValues(alpha: 0.6),
                      ),
                    ),
                    const Spacer(),
                    if (event.price > 0)
                      Text(
                        '\$${event.price.toStringAsFixed(0)}',
                        style: AppTheme.fontStyles.bodyBold.copyWith(
                          color: AppTheme.accentColor,
                        ),
                      )
                    else
                      Text(
                        'FREE',
                        style: AppTheme.fontStyles.bodyBold.copyWith(
                          color: AppTheme.accentEmerald,
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Icon(
                      Icons.people,
                      size: 16,
                      color: AppTheme.secondaryAccentColor.withValues(alpha: 0.6),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${event.currentAttendees}/${event.maxAttendees}',
                      style: AppTheme.fontStyles.caption.copyWith(
                        color: AppTheme.secondaryAccentColor.withValues(alpha: 0.6),
                      ),
                    ),
                    const Spacer(),
                    if (event.isVerified)
                      Icon(
                        Icons.verified,
                        size: 16,
                        color: AppTheme.accentColor,
                      ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  IconData _getEventIcon(EventType type) {
    switch (type) {
      case EventType.conference:
        return Icons.business;
      case EventType.workshop:
        return Icons.work;
      case EventType.seminar:
        return Icons.school;
      case EventType.meetup:
        return Icons.groups;
      case EventType.gala:
        return Icons.celebration;
      case EventType.retreat:
        return Icons.beach_access;
      case EventType.summit:
        return Icons.trending_up;
      case EventType.forum:
        return Icons.forum;
      case EventType.webinar:
        return Icons.video_call;
      case EventType.networking:
        return Icons.network_check;
    }
  }

  String _formatEventDate(DateTime date) {
    final now = DateTime.now();
    final difference = date.difference(now).inDays;

    if (difference == 0) {
      return 'Today at ${_formatTime(date)}';
    } else if (difference == 1) {
      return 'Tomorrow at ${_formatTime(date)}';
    } else if (difference < 7) {
      return '${_getDayName(date)} at ${_formatTime(date)}';
    } else {
      return '${date.day}/${date.month}/${date.year} at ${_formatTime(date)}';
    }
  }

  String _formatTime(DateTime date) {
    return '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  String _getDayName(DateTime date) {
    switch (date.weekday) {
      case 1:
        return 'Monday';
      case 2:
        return 'Tuesday';
      case 3:
        return 'Wednesday';
      case 4:
        return 'Thursday';
      case 5:
        return 'Friday';
      case 6:
        return 'Saturday';
      case 7:
        return 'Sunday';
      default:
        return '';
    }
  }
}
