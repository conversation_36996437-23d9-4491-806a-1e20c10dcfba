import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/networking/models/networking_models.dart';
import 'package:billionaires_social/features/networking/services/networking_service.dart';
import 'package:billionaires_social/features/auth/providers/auth_provider.dart';

// Service Provider
final networkingServiceProvider = Provider<NetworkingService>((ref) {
  return NetworkingService();
});

// Connection Providers
final connectionRequestsProvider = StreamProvider<List<NetworkConnection>>((
  ref,
) {
  final service = ref.watch(networkingServiceProvider);
  return service.getConnectionRequests();
});

final acceptedConnectionsProvider = StreamProvider<List<NetworkConnection>>((
  ref,
) {
  final service = ref.watch(networkingServiceProvider);
  return service.getAcceptedConnections();
});

final connectionRequestsCountProvider = Provider<int>((ref) {
  final requests = ref.watch(connectionRequestsProvider);
  return requests.when(
    data: (requests) => requests.length,
    loading: () => 0,
    error: (_, _) => 0,
  );
});

// Profile Providers
final networkProfileProvider = FutureProvider.family<NetworkProfile?, String>((
  ref,
  userId,
) async {
  final service = ref.watch(networkingServiceProvider);
  return service.getNetworkProfile(userId);
});

final currentUserNetworkProfileProvider = FutureProvider<NetworkProfile?>((
  ref,
) async {
  final service = ref.watch(networkingServiceProvider);
  final currentUser = ref.watch(authProvider).value;

  if (currentUser == null) {
    return null;
  }

  return await service.getNetworkProfile(currentUser.uid);
});

final networkProfilesSearchProvider =
    StreamProvider.family<List<NetworkProfile>, NetworkSearchParams>((
      ref,
      params,
    ) {
      final service = ref.watch(networkingServiceProvider);
      return service.searchNetworkProfiles(
        query: params.query,
        industries: params.industries,
        skills: params.skills,
        location: params.location,
      );
    });

// Event Providers
final upcomingEventsProvider = StreamProvider<List<NetworkingEvent>>((ref) {
  final service = ref.watch(networkingServiceProvider);
  return service.getUpcomingEvents();
});

final userEventsProvider = StreamProvider.family<List<NetworkingEvent>, String>(
  (ref, organizerId) {
    final service = ref.watch(networkingServiceProvider);
    return service.getEventsByOrganizer(organizerId);
  },
);

// Business Card Providers
final businessCardProvider = FutureProvider.family<BusinessCard?, String>((
  ref,
  userId,
) async {
  final service = ref.watch(networkingServiceProvider);
  return service.getBusinessCard(userId);
});

final currentUserBusinessCardProvider = FutureProvider<BusinessCard?>((
  ref,
) async {
  final service = ref.watch(networkingServiceProvider);
  final currentUser = ref.watch(authProvider).value;

  if (currentUser == null) {
    return null;
  }

  return await service.getBusinessCard(currentUser.uid);
});

// Recommendation Providers
final networkingRecommendationsProvider =
    StreamProvider<List<NetworkingRecommendation>>((ref) {
      final service = ref.watch(networkingServiceProvider);
      return service.getRecommendations();
    });

// Messaging Providers
final networkingMessagesProvider =
    StreamProvider.family<List<NetworkingMessage>, String>((ref, otherUserId) {
      final service = ref.watch(networkingServiceProvider);
      return service.getMessages(otherUserId);
    });

// Notifier Providers
final networkingNotifierProvider =
    StateNotifierProvider<NetworkingNotifier, NetworkingState>((ref) {
      final service = ref.watch(networkingServiceProvider);
      return NetworkingNotifier(service);
    });

final connectionNotifierProvider =
    StateNotifierProvider<ConnectionNotifier, ConnectionState>((ref) {
      final service = ref.watch(networkingServiceProvider);
      return ConnectionNotifier(service);
    });

final eventNotifierProvider = StateNotifierProvider<EventNotifier, EventState>((
  ref,
) {
  final service = ref.watch(networkingServiceProvider);
  return EventNotifier(service);
});

final businessCardNotifierProvider =
    StateNotifierProvider<BusinessCardNotifier, BusinessCardState>((ref) {
      final service = ref.watch(networkingServiceProvider);
      return BusinessCardNotifier(service);
    });

final messagingNotifierProvider =
    StateNotifierProvider<MessagingNotifier, MessagingState>((ref) {
      final service = ref.watch(networkingServiceProvider);
      return MessagingNotifier(service);
    });

// State Classes
class NetworkingState {
  final bool isLoading;
  final String? error;
  final List<NetworkProfile> searchResults;
  final List<NetworkingRecommendation> recommendations;

  const NetworkingState({
    this.isLoading = false,
    this.error,
    this.searchResults = const [],
    this.recommendations = const [],
  });

  NetworkingState copyWith({
    bool? isLoading,
    String? error,
    List<NetworkProfile>? searchResults,
    List<NetworkingRecommendation>? recommendations,
  }) {
    return NetworkingState(
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      searchResults: searchResults ?? this.searchResults,
      recommendations: recommendations ?? this.recommendations,
    );
  }
}

class ConnectionState {
  final bool isLoading;
  final String? error;
  final bool isSendingRequest;
  final bool isAcceptingRequest;
  final bool isRejectingRequest;

  const ConnectionState({
    this.isLoading = false,
    this.error,
    this.isSendingRequest = false,
    this.isAcceptingRequest = false,
    this.isRejectingRequest = false,
  });

  ConnectionState copyWith({
    bool? isLoading,
    String? error,
    bool? isSendingRequest,
    bool? isAcceptingRequest,
    bool? isRejectingRequest,
  }) {
    return ConnectionState(
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      isSendingRequest: isSendingRequest ?? this.isSendingRequest,
      isAcceptingRequest: isAcceptingRequest ?? this.isAcceptingRequest,
      isRejectingRequest: isRejectingRequest ?? this.isRejectingRequest,
    );
  }
}

class EventState {
  final bool isLoading;
  final String? error;
  final bool isCreatingEvent;
  final bool isUpdatingEvent;
  final bool isDeletingEvent;

  const EventState({
    this.isLoading = false,
    this.error,
    this.isCreatingEvent = false,
    this.isUpdatingEvent = false,
    this.isDeletingEvent = false,
  });

  EventState copyWith({
    bool? isLoading,
    String? error,
    bool? isCreatingEvent,
    bool? isUpdatingEvent,
    bool? isDeletingEvent,
  }) {
    return EventState(
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      isCreatingEvent: isCreatingEvent ?? this.isCreatingEvent,
      isUpdatingEvent: isUpdatingEvent ?? this.isUpdatingEvent,
      isDeletingEvent: isDeletingEvent ?? this.isDeletingEvent,
    );
  }
}

class BusinessCardState {
  final bool isLoading;
  final String? error;
  final bool isCreatingCard;
  final bool isUpdatingCard;
  final bool isGeneratingQR;

  const BusinessCardState({
    this.isLoading = false,
    this.error,
    this.isCreatingCard = false,
    this.isUpdatingCard = false,
    this.isGeneratingQR = false,
  });

  BusinessCardState copyWith({
    bool? isLoading,
    String? error,
    bool? isCreatingCard,
    bool? isUpdatingCard,
    bool? isGeneratingQR,
  }) {
    return BusinessCardState(
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      isCreatingCard: isCreatingCard ?? this.isCreatingCard,
      isUpdatingCard: isUpdatingCard ?? this.isUpdatingCard,
      isGeneratingQR: isGeneratingQR ?? this.isGeneratingQR,
    );
  }
}

class MessagingState {
  final bool isLoading;
  final String? error;
  final bool isSendingMessage;
  final bool isMarkingAsRead;

  const MessagingState({
    this.isLoading = false,
    this.error,
    this.isSendingMessage = false,
    this.isMarkingAsRead = false,
  });

  MessagingState copyWith({
    bool? isLoading,
    String? error,
    bool? isSendingMessage,
    bool? isMarkingAsRead,
  }) {
    return MessagingState(
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      isSendingMessage: isSendingMessage ?? this.isSendingMessage,
      isMarkingAsRead: isMarkingAsRead ?? this.isMarkingAsRead,
    );
  }
}

// Search Parameters
class NetworkSearchParams {
  final String? query;
  final List<String>? industries;
  final List<String>? skills;
  final String? location;

  const NetworkSearchParams({
    this.query,
    this.industries,
    this.skills,
    this.location,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NetworkSearchParams &&
        other.query == query &&
        other.industries == industries &&
        other.skills == skills &&
        other.location == location;
  }

  @override
  int get hashCode {
    return Object.hash(query, industries, skills, location);
  }
}

// Notifier Classes
class NetworkingNotifier extends StateNotifier<NetworkingState> {
  final NetworkingService _service;

  NetworkingNotifier(this._service) : super(const NetworkingState());

  Future<void> searchProfiles(NetworkSearchParams params) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      // Validate search parameters before proceeding
      if (params.query?.trim().isEmpty ?? true) {
        throw Exception('Search query cannot be empty');
      }

      // The actual search is handled by the stream provider
      // This method just updates the loading state
      state = state.copyWith(isLoading: false);
    } catch (e) {
      debugPrint('❌ Error in searchProfiles: $e');
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  Future<void> createNetworkProfile(NetworkProfile profile) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final createdProfile = await _service.createNetworkProfile(profile);
      if (createdProfile != null) {
        state = state.copyWith(isLoading: false);
      } else {
        state = state.copyWith(
          isLoading: false,
          error: 'Failed to create profile',
        );
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  Future<void> updateNetworkProfile(NetworkProfile profile) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final updatedProfile = await _service.updateNetworkProfile(profile);
      if (updatedProfile != null) {
        state = state.copyWith(isLoading: false);
      } else {
        state = state.copyWith(
          isLoading: false,
          error: 'Failed to update profile',
        );
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }
}

class ConnectionNotifier extends StateNotifier<ConnectionState> {
  final NetworkingService _service;

  ConnectionNotifier(this._service) : super(const ConnectionState());

  Future<void> sendConnectionRequest({
    required String targetUserId,
    required NetworkConnectionType type,
    String? message,
  }) async {
    state = state.copyWith(isSendingRequest: true, error: null);
    try {
      final connection = await _service.sendConnectionRequest(
        targetUserId: targetUserId,
        type: type,
        message: message,
      );
      if (connection != null) {
        state = state.copyWith(isSendingRequest: false);
      } else {
        state = state.copyWith(
          isSendingRequest: false,
          error: 'Failed to send request',
        );
      }
    } catch (e) {
      state = state.copyWith(isSendingRequest: false, error: e.toString());
    }
  }

  Future<void> acceptConnectionRequest(String connectionId) async {
    state = state.copyWith(isAcceptingRequest: true, error: null);
    try {
      final success = await _service.acceptConnectionRequest(connectionId);
      if (success) {
        state = state.copyWith(isAcceptingRequest: false);
      } else {
        state = state.copyWith(
          isAcceptingRequest: false,
          error: 'Failed to accept request',
        );
      }
    } catch (e) {
      state = state.copyWith(isAcceptingRequest: false, error: e.toString());
    }
  }

  Future<void> rejectConnectionRequest(String connectionId) async {
    state = state.copyWith(isRejectingRequest: true, error: null);
    try {
      final success = await _service.rejectConnectionRequest(connectionId);
      if (success) {
        state = state.copyWith(isRejectingRequest: false);
      } else {
        state = state.copyWith(
          isRejectingRequest: false,
          error: 'Failed to reject request',
        );
      }
    } catch (e) {
      state = state.copyWith(isRejectingRequest: false, error: e.toString());
    }
  }

  Future<void> blockConnection(String connectionId) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final success = await _service.blockConnection(connectionId);
      if (success) {
        state = state.copyWith(isLoading: false);
      } else {
        state = state.copyWith(
          isLoading: false,
          error: 'Failed to block connection',
        );
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }
}

class EventNotifier extends StateNotifier<EventState> {
  final NetworkingService _service;

  EventNotifier(this._service) : super(const EventState());

  Future<void> createEvent(NetworkingEvent event) async {
    state = state.copyWith(isCreatingEvent: true, error: null);
    try {
      final createdEvent = await _service.createEvent(event);
      if (createdEvent != null) {
        state = state.copyWith(isCreatingEvent: false);
      } else {
        state = state.copyWith(
          isCreatingEvent: false,
          error: 'Failed to create event',
        );
      }
    } catch (e) {
      state = state.copyWith(isCreatingEvent: false, error: e.toString());
    }
  }

  Future<void> updateEvent(NetworkingEvent event) async {
    state = state.copyWith(isUpdatingEvent: true, error: null);
    try {
      final updatedEvent = await _service.updateEvent(event);
      if (updatedEvent != null) {
        state = state.copyWith(isUpdatingEvent: false);
      } else {
        state = state.copyWith(
          isUpdatingEvent: false,
          error: 'Failed to update event',
        );
      }
    } catch (e) {
      state = state.copyWith(isUpdatingEvent: false, error: e.toString());
    }
  }

  Future<void> deleteEvent(String eventId) async {
    state = state.copyWith(isDeletingEvent: true, error: null);
    try {
      final success = await _service.deleteEvent(eventId);
      if (success) {
        state = state.copyWith(isDeletingEvent: false);
      } else {
        state = state.copyWith(
          isDeletingEvent: false,
          error: 'Failed to delete event',
        );
      }
    } catch (e) {
      state = state.copyWith(isDeletingEvent: false, error: e.toString());
    }
  }
}

class BusinessCardNotifier extends StateNotifier<BusinessCardState> {
  final NetworkingService _service;

  BusinessCardNotifier(this._service) : super(const BusinessCardState());

  Future<void> createBusinessCard(BusinessCard card) async {
    state = state.copyWith(isCreatingCard: true, error: null);
    try {
      final createdCard = await _service.createBusinessCard(card);
      if (createdCard != null) {
        state = state.copyWith(isCreatingCard: false);
      } else {
        state = state.copyWith(
          isCreatingCard: false,
          error: 'Failed to create business card',
        );
      }
    } catch (e) {
      state = state.copyWith(isCreatingCard: false, error: e.toString());
    }
  }

  Future<void> updateBusinessCard(BusinessCard card) async {
    state = state.copyWith(isUpdatingCard: true, error: null);
    try {
      final updatedCard = await _service.updateBusinessCard(card);
      if (updatedCard != null) {
        state = state.copyWith(isUpdatingCard: false);
      } else {
        state = state.copyWith(
          isUpdatingCard: false,
          error: 'Failed to update business card',
        );
      }
    } catch (e) {
      state = state.copyWith(isUpdatingCard: false, error: e.toString());
    }
  }

  Future<void> generateQRCode(String cardId) async {
    state = state.copyWith(isGeneratingQR: true, error: null);
    try {
      final qrCodeUrl = await _service.generateQRCode(cardId);
      if (qrCodeUrl != null) {
        state = state.copyWith(isGeneratingQR: false);
      } else {
        state = state.copyWith(
          isGeneratingQR: false,
          error: 'Failed to generate QR code',
        );
      }
    } catch (e) {
      state = state.copyWith(isGeneratingQR: false, error: e.toString());
    }
  }
}

class MessagingNotifier extends StateNotifier<MessagingState> {
  final NetworkingService _service;

  MessagingNotifier(this._service) : super(const MessagingState());

  Future<void> sendMessage(NetworkingMessage message) async {
    state = state.copyWith(isSendingMessage: true, error: null);
    try {
      final sentMessage = await _service.sendMessage(message);
      if (sentMessage != null) {
        state = state.copyWith(isSendingMessage: false);
      } else {
        state = state.copyWith(
          isSendingMessage: false,
          error: 'Failed to send message',
        );
      }
    } catch (e) {
      state = state.copyWith(isSendingMessage: false, error: e.toString());
    }
  }

  Future<void> markMessageAsRead(String messageId) async {
    state = state.copyWith(isMarkingAsRead: true, error: null);
    try {
      final success = await _service.markMessageAsRead(messageId);
      if (success) {
        state = state.copyWith(isMarkingAsRead: false);
      } else {
        state = state.copyWith(
          isMarkingAsRead: false,
          error: 'Failed to mark message as read',
        );
      }
    } catch (e) {
      state = state.copyWith(isMarkingAsRead: false, error: e.toString());
    }
  }
}
