import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/features/networking/providers/networking_provider.dart';
import 'package:billionaires_social/features/networking/models/networking_models.dart';
import 'package:billionaires_social/features/networking/widgets/profile_card.dart';

class NetworkSearchScreen extends ConsumerStatefulWidget {
  const NetworkSearchScreen({super.key});

  @override
  ConsumerState<NetworkSearchScreen> createState() =>
      _NetworkSearchScreenState();
}

class _NetworkSearchScreenState extends ConsumerState<NetworkSearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  final List<String> _selectedIndustries = [];
  final List<String> _selectedSkills = [];
  String? _selectedLocation;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.primaryColor,
      appBar: AppBar(
        title: const Text('Find Connections'),
        backgroundColor: AppTheme.primaryColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Column(
        children: [
          _buildSearchBar(),
          _buildFilters(),
          Expanded(child: _buildSearchResults()),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        onChanged: (value) {
          setState(() {});
        },
        decoration: AppTheme.luxuryTextFieldDecoration(
          hintText: 'Search by name, company, or skills...',
          prefixIcon: Icons.search,
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    setState(() {});
                  },
                )
              : null,
        ),
      ),
    );
  }

  Widget _buildFilters() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Filters',
            style: AppTheme.fontStyles.title.copyWith(
              color: AppTheme.luxuryWhite,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildFilterChip('Technology', _selectedIndustries),
              _buildFilterChip('Finance', _selectedIndustries),
              _buildFilterChip('Healthcare', _selectedIndustries),
              _buildFilterChip('Education', _selectedIndustries),
              _buildFilterChip('Marketing', _selectedIndustries),
              _buildFilterChip('Sales', _selectedIndustries),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Skills',
            style: AppTheme.fontStyles.subtitle.copyWith(
              color: AppTheme.luxuryWhite,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildFilterChip('Leadership', _selectedSkills),
              _buildFilterChip('Strategy', _selectedSkills),
              _buildFilterChip('Innovation', _selectedSkills),
              _buildFilterChip('Analytics', _selectedSkills),
              _buildFilterChip('Communication', _selectedSkills),
              _buildFilterChip('Project Management', _selectedSkills),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Location',
            style: AppTheme.fontStyles.subtitle.copyWith(
              color: AppTheme.luxuryWhite,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildLocationChip('New York'),
              _buildLocationChip('San Francisco'),
              _buildLocationChip('London'),
              _buildLocationChip('Dubai'),
              _buildLocationChip('Singapore'),
              _buildLocationChip('Tokyo'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, List<String> selectedList) {
    final isSelected = selectedList.contains(label);
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          if (selected) {
            selectedList.add(label);
          } else {
            selectedList.remove(label);
          }
        });
      },
      backgroundColor: AppTheme.luxuryGrey,
      selectedColor: AppTheme.accentColor,
      labelStyle: TextStyle(
        color: isSelected ? AppTheme.luxuryBlack : AppTheme.luxuryWhite,
        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
      ),
    );
  }

  Widget _buildLocationChip(String location) {
    final isSelected = _selectedLocation == location;
    return FilterChip(
      label: Text(location),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedLocation = selected ? location : null;
        });
      },
      backgroundColor: AppTheme.luxuryGrey,
      selectedColor: AppTheme.accentColor,
      labelStyle: TextStyle(
        color: isSelected ? AppTheme.luxuryBlack : AppTheme.luxuryWhite,
        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
      ),
    );
  }

  Widget _buildSearchResults() {
    final searchParams = NetworkSearchParams(
      query: _searchController.text.isEmpty ? null : _searchController.text,
      industries: _selectedIndustries.isEmpty ? null : _selectedIndustries,
      skills: _selectedSkills.isEmpty ? null : _selectedSkills,
      location: _selectedLocation,
    );

    return Consumer(
      builder: (context, ref, child) {
        final searchResults = ref.watch(
          networkProfilesSearchProvider(searchParams),
        );

        return searchResults.when(
          data: (profiles) {
            if (profiles.isEmpty) {
              return _buildEmptyState();
            }

            return ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: profiles.length,
              itemBuilder: (context, index) {
                return ProfileCard(
                  profile: profiles[index],
                  onConnect: () => _handleConnect(profiles[index]),
                  onViewProfile: () => _handleViewProfile(profiles[index]),
                );
              },
            );
          },
          loading: () => const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppTheme.accentColor),
            ),
          ),
          error: (error, stack) => _buildErrorState(error.toString()),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: AppTheme.secondaryAccentColor.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'No results found',
            style: AppTheme.fontStyles.title.copyWith(
              color: AppTheme.luxuryWhite,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your search criteria or filters',
            style: AppTheme.fontStyles.body.copyWith(
              color: AppTheme.secondaryAccentColor.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            'Something went wrong',
            style: AppTheme.fontStyles.title.copyWith(
              color: AppTheme.luxuryWhite,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: AppTheme.fontStyles.body.copyWith(
              color: AppTheme.secondaryAccentColor.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _handleConnect(NetworkProfile profile) {
    ref
        .read(connectionNotifierProvider.notifier)
        .sendConnectionRequest(
          targetUserId: profile.userId,
          type: NetworkConnectionType.colleague,
          message: 'Hi! I found your profile and would love to connect.',
        );

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Connection request sent to ${profile.name}!'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _handleViewProfile(NetworkProfile profile) {
    // Navigate to profile details screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening ${profile.name}\'s profile'),
        backgroundColor: AppTheme.accentColor,
      ),
    );
  }
}
