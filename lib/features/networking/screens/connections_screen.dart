import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/features/networking/providers/networking_provider.dart';
import 'package:billionaires_social/features/networking/models/networking_models.dart';
import 'package:billionaires_social/features/networking/widgets/profile_card.dart';

class ConnectionsScreen extends ConsumerStatefulWidget {
  const ConnectionsScreen({super.key});

  @override
  ConsumerState<ConnectionsScreen> createState() => _ConnectionsScreenState();
}

class _ConnectionsScreenState extends ConsumerState<ConnectionsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.primaryColor,
      appBar: AppBar(
        title: const Text('My Connections'),
        backgroundColor: AppTheme.primaryColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(context),
          ),
        ],
      ),
      body: Column(
        children: [
          _buildStatsCard(),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: AppTheme.luxuryGrey,
              borderRadius: BorderRadius.circular(12),
            ),
            child: TabBar(
              controller: _tabController,
              indicator: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: AppTheme.accentColor,
              ),
              labelColor: Colors.white,
              unselectedLabelColor: AppTheme.secondaryAccentColor.withValues(
                alpha: 0.6,
              ),
              labelStyle: AppTheme.fontStyles.body.copyWith(
                fontWeight: FontWeight.w600,
              ),
              tabs: const [
                Tab(text: 'All'),
                Tab(text: 'Recent'),
                Tab(text: 'Favorites'),
              ],
            ),
          ),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAllConnectionsTab(),
                _buildRecentConnectionsTab(),
                _buildFavoriteConnectionsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsCard() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.accentColor,
            AppTheme.accentColor.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem('Total', '127', Icons.people),
          _buildStatItem('This Month', '12', Icons.trending_up),
          _buildStatItem('Mutual', '45', Icons.handshake),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.white, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: AppTheme.fontStyles.title.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: AppTheme.fontStyles.caption.copyWith(
            color: Colors.white.withValues(alpha: 0.9),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildAllConnectionsTab() {
    return Consumer(
      builder: (context, ref, child) {
        final connections = ref.watch(acceptedConnectionsProvider);

        return connections.when(
          data: (connectionsList) {
            if (connectionsList.isEmpty) {
              return _buildEmptyState(
                'No Connections Yet',
                'Start building your professional network by connecting with others.',
                Icons.people_outline,
              );
            }

            return ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: connectionsList.length,
              itemBuilder: (context, index) {
                final connection = connectionsList[index];
                // Fetch profile for the connected user
                final profileAsync = ref.watch(
                  networkProfileProvider(connection.connectedUserId),
                );

                return profileAsync.when(
                  data: (profile) {
                    if (profile == null) {
                      return _buildUnknownUserCard(connection);
                    }
                    return ProfileCard(
                      profile: profile,
                      onConnect: () => _handleMessageConnection(connection),
                      onViewProfile: () =>
                          _handleViewConnectionProfile(connection),
                    );
                  },
                  loading: () =>
                      const Center(child: CircularProgressIndicator()),
                  error: (error, stack) =>
                      _buildErrorCard(connection, error.toString()),
                );
              },
            );
          },
          loading: () => const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppTheme.accentColor),
            ),
          ),
          error: (error, stack) => _buildErrorState(error.toString()),
        );
      },
    );
  }

  Widget _buildRecentConnectionsTab() {
    return _buildEmptyState(
      'No Recent Connections',
      'Your recently added connections will appear here.',
      Icons.access_time,
    );
  }

  Widget _buildFavoriteConnectionsTab() {
    return _buildEmptyState(
      'No Favorite Connections',
      'Mark connections as favorites to see them here.',
      Icons.favorite_border,
    );
  }

  Widget _buildEmptyState(String title, String message, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: AppTheme.secondaryAccentColor.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: AppTheme.fontStyles.title.copyWith(
              color: AppTheme.luxuryWhite,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: AppTheme.fontStyles.body.copyWith(
              color: AppTheme.secondaryAccentColor.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            'Something went wrong',
            style: AppTheme.fontStyles.title.copyWith(
              color: AppTheme.luxuryWhite,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: AppTheme.fontStyles.body.copyWith(
              color: AppTheme.secondaryAccentColor.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _handleMessageConnection(dynamic connection) {
    // Navigate to messaging screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening chat with ${connection.connectedUserName}'),
        backgroundColor: AppTheme.accentColor,
      ),
    );
  }

  void _handleViewConnectionProfile(dynamic connection) {
    // Navigate to profile details screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening ${connection.connectedUserName}\'s profile'),
        backgroundColor: AppTheme.accentColor,
      ),
    );
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.luxuryGrey,
        title: Text(
          'Search Connections',
          style: AppTheme.fontStyles.title.copyWith(
            color: AppTheme.luxuryWhite,
          ),
        ),
        content: TextField(
          controller: _searchController,
          decoration: AppTheme.luxuryTextFieldDecoration(
            hintText: 'Search by name or company...',
            prefixIcon: Icons.search,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Implement search functionality
            },
            child: const Text('Search'),
          ),
        ],
      ),
    );
  }

  Widget _buildUnknownUserCard(NetworkConnection connection) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.luxuryGrey,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 24,
            backgroundColor: AppTheme.accentColor.withValues(alpha: 0.1),
            child: Icon(Icons.person, color: AppTheme.accentColor, size: 24),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Unknown User',
                  style: AppTheme.fontStyles.title.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'User ID: ${connection.connectedUserId}',
                  style: AppTheme.fontStyles.body.copyWith(
                    color: AppTheme.secondaryAccentColor.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorCard(NetworkConnection connection, String error) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.luxuryGrey,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: Colors.red, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Error loading profile: $error',
              style: AppTheme.fontStyles.body.copyWith(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}
