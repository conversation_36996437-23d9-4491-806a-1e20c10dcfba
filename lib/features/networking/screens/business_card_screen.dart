import 'package:flutter/material.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/features/explore/widgets/feature_list_widget.dart';

class BusinessCardScreen extends StatelessWidget {
  const BusinessCardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final cardItems = [
      FeatureListItem(
        title: '<PERSON>',
        subtitle: 'Founder & CEO, Elite Ventures',
        imageUrl: 'https://randomuser.me/api/portraits/men/32.jpg',
        onTap: () {},
        icon: Icons.account_circle,
      ),
      FeatureListItem(
        title: '<PERSON>',
        subtitle: 'Luxury Brand Consultant',
        imageUrl: 'https://randomuser.me/api/portraits/women/44.jpg',
        onTap: () {},
        icon: Icons.account_circle,
      ),
      FeatureListItem(
        title: '<PERSON>',
        subtitle: 'Private Equity Partner',
        imageUrl: 'https://randomuser.me/api/portraits/men/65.jpg',
        onTap: () {},
        icon: Icons.account_circle,
      ),
    ];

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Business Cards',
          style: AppTheme.fontStyles.title.copyWith(
            color: AppTheme.accentColor,
          ),
        ),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: FeatureListWidget(
          sectionTitle: 'Elite Network',
          items: cardItems,
        ),
      ),
    );
  }
}
