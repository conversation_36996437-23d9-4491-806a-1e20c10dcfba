import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/features/networking/providers/networking_provider.dart';
import 'package:billionaires_social/features/networking/widgets/event_card.dart';

class EventsScreen extends ConsumerStatefulWidget {
  const EventsScreen({super.key});

  @override
  ConsumerState<EventsScreen> createState() => _EventsScreenState();
}

class _EventsScreenState extends ConsumerState<EventsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.primaryColor,
      appBar: AppBar(
        title: const Text('Networking Events'),
        backgroundColor: AppTheme.primaryColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showCreateEventDialog(context),
          ),
        ],
      ),
      body: Column(
        children: [
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: AppTheme.luxuryGrey,
              borderRadius: BorderRadius.circular(12),
            ),
            child: TabBar(
              controller: _tabController,
              indicator: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: AppTheme.accentColor,
              ),
              labelColor: Colors.white,
              unselectedLabelColor: AppTheme.secondaryAccentColor.withValues(
                alpha: 0.6,
              ),
              labelStyle: AppTheme.fontStyles.body.copyWith(
                fontWeight: FontWeight.w600,
              ),
              tabs: const [
                Tab(text: 'Upcoming'),
                Tab(text: 'My Events'),
                Tab(text: 'Past'),
              ],
            ),
          ),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildUpcomingEventsTab(),
                _buildMyEventsTab(),
                _buildPastEventsTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showCreateEventDialog(context),
        backgroundColor: AppTheme.accentColor,
        child: const Icon(Icons.add, color: AppTheme.luxuryBlack),
      ),
    );
  }

  Widget _buildUpcomingEventsTab() {
    return Consumer(
      builder: (context, ref, child) {
        final events = ref.watch(upcomingEventsProvider);

        return events.when(
          data: (eventsList) {
            if (eventsList.isEmpty) {
              return _buildEmptyState(
                'No Upcoming Events',
                'Discover networking events and opportunities to grow your professional network.',
                Icons.event_outlined,
              );
            }

            return ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: eventsList.length,
              itemBuilder: (context, index) {
                return EventCard(
                  event: eventsList[index],
                  onTap: () => _handleEventTap(eventsList[index]),
                );
              },
            );
          },
          loading: () => const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppTheme.accentColor),
            ),
          ),
          error: (error, stack) => _buildErrorState(error.toString()),
        );
      },
    );
  }

  Widget _buildMyEventsTab() {
    // This would show events created by the current user
    return _buildEmptyState(
      'No Events Created',
      'Create your first networking event to bring professionals together.',
      Icons.create_outlined,
    );
  }

  Widget _buildPastEventsTab() {
    // This would show past events
    return _buildEmptyState(
      'No Past Events',
      'Events you\'ve attended will appear here.',
      Icons.history,
    );
  }

  Widget _buildEmptyState(String title, String message, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: AppTheme.secondaryAccentColor.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: AppTheme.fontStyles.title.copyWith(
              color: AppTheme.luxuryWhite,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: AppTheme.fontStyles.body.copyWith(
              color: AppTheme.secondaryAccentColor.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            'Something went wrong',
            style: AppTheme.fontStyles.title.copyWith(
              color: AppTheme.luxuryWhite,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: AppTheme.fontStyles.body.copyWith(
              color: AppTheme.secondaryAccentColor.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _handleEventTap(dynamic event) {
    // Navigate to event details screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening ${event.title}'),
        backgroundColor: AppTheme.accentColor,
      ),
    );
  }

  void _showCreateEventDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.luxuryGrey,
        title: Text(
          'Create Event',
          style: AppTheme.fontStyles.title.copyWith(
            color: AppTheme.luxuryWhite,
          ),
        ),
        content: const Text(
          'Event creation feature coming soon!',
          style: TextStyle(color: AppTheme.secondaryAccentColor),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
