import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/features/networking/providers/networking_provider.dart';
import 'package:billionaires_social/features/networking/models/networking_models.dart';
import 'package:billionaires_social/features/networking/widgets/connection_request_card.dart';
import 'package:billionaires_social/features/networking/widgets/event_card.dart';
import 'package:billionaires_social/features/networking/widgets/recommendation_card.dart';
import 'package:billionaires_social/features/networking/screens/network_search_screen.dart';
import 'package:billionaires_social/features/networking/screens/events_screen.dart';

class NetworkingMainScreen extends ConsumerStatefulWidget {
  const NetworkingMainScreen({super.key});

  @override
  ConsumerState<NetworkingMainScreen> createState() =>
      _NetworkingMainScreenState();
}

class _NetworkingMainScreenState extends ConsumerState<NetworkingMainScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.primaryColor,
      appBar: AppBar(
        title: Text(
          'Networking',
          style: AppTheme.fontStyles.title.copyWith(
            color: AppTheme.accentColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppTheme.primaryColor,
        elevation: 0,
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.search, color: AppTheme.accentColor),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const NetworkSearchScreen(),
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.person_add, color: AppTheme.accentColor),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const NetworkSearchScreen(),
                ),
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Quick Stats
          _buildQuickStats(),

          // Tab Bar
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: AppTheme.luxuryGrey,
              borderRadius: BorderRadius.circular(12),
            ),
            child: TabBar(
              controller: _tabController,
              indicator: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: AppTheme.accentColor,
              ),
              labelColor: Colors.white,
              unselectedLabelColor: AppTheme.secondaryAccentColor.withValues(
                alpha: 0.6,
              ),
              labelStyle: AppTheme.fontStyles.body.copyWith(
                fontWeight: FontWeight.w600,
              ),
              tabs: const [
                Tab(text: 'Requests'),
                Tab(text: 'Events'),
                Tab(text: 'Suggestions'),
              ],
            ),
          ),

          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildConnectionRequestsTab(),
                _buildEventsTab(),
                _buildRecommendationsTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const EventsScreen()),
          );
        },
        backgroundColor: AppTheme.accentColor,
        child: const Icon(Icons.event, color: Colors.white),
      ),
    );
  }

  Widget _buildQuickStats() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.accentColor,
            AppTheme.accentColor.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem(
            'Connection Requests',
            ref.watch(connectionRequestsCountProvider).toString(),
            Icons.person_add,
          ),
          _buildStatItem('Upcoming Events', '5', Icons.event),
          _buildStatItem('Connections', '127', Icons.people),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.white, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: AppTheme.fontStyles.title.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: AppTheme.fontStyles.caption.copyWith(
            color: Colors.white.withValues(alpha: 0.9),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildConnectionRequestsTab() {
    return Consumer(
      builder: (context, ref, child) {
        final connectionRequests = ref.watch(connectionRequestsProvider);

        return connectionRequests.when(
          data: (requests) {
            if (requests.isEmpty) {
              return _buildEmptyState(
                'No Connection Requests',
                'When people want to connect with you, their requests will appear here.',
                Icons.person_add_outlined,
              );
            }

            return ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: requests.length,
              itemBuilder: (context, index) {
                return ConnectionRequestCard(
                  connection: requests[index],
                  onAccept: () => _handleAcceptRequest(requests[index].id),
                  onReject: () => _handleRejectRequest(requests[index].id),
                );
              },
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => _buildErrorState(error.toString()),
        );
      },
    );
  }

  Widget _buildEventsTab() {
    return Consumer(
      builder: (context, ref, child) {
        final events = ref.watch(upcomingEventsProvider);

        return events.when(
          data: (eventsList) {
            if (eventsList.isEmpty) {
              return _buildEmptyState(
                'No Upcoming Events',
                'Discover networking events and opportunities to grow your professional network.',
                Icons.event_outlined,
              );
            }

            return ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: eventsList.length,
              itemBuilder: (context, index) {
                return EventCard(
                  event: eventsList[index],
                  onTap: () => _handleEventTap(eventsList[index]),
                );
              },
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => _buildErrorState(error.toString()),
        );
      },
    );
  }

  Widget _buildRecommendationsTab() {
    return Consumer(
      builder: (context, ref, child) {
        final recommendations = ref.watch(networkingRecommendationsProvider);

        return recommendations.when(
          data: (recommendationsList) {
            if (recommendationsList.isEmpty) {
              return _buildEmptyState(
                'No Recommendations Yet',
                'We\'ll suggest relevant connections based on your profile and network.',
                Icons.recommend_outlined,
              );
            }

            return ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: recommendationsList.length,
              itemBuilder: (context, index) {
                return RecommendationCard(
                  recommendation: recommendationsList[index],
                  onConnect: () => _handleConnectWithRecommendation(
                    recommendationsList[index],
                  ),
                );
              },
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => _buildErrorState(error.toString()),
        );
      },
    );
  }

  Widget _buildEmptyState(String title, String message, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: AppTheme.secondaryAccentColor.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: AppTheme.fontStyles.title.copyWith(
              color: AppTheme.luxuryWhite.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: AppTheme.fontStyles.body.copyWith(
              color: AppTheme.secondaryAccentColor.withValues(alpha: 0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            'Something went wrong',
            style: AppTheme.fontStyles.title.copyWith(color: Colors.red),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: AppTheme.fontStyles.body.copyWith(
              color: AppTheme.secondaryAccentColor.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _handleAcceptRequest(String connectionId) {
    ref
        .read(connectionNotifierProvider.notifier)
        .acceptConnectionRequest(connectionId);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Connection request accepted!'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _handleRejectRequest(String connectionId) {
    ref
        .read(connectionNotifierProvider.notifier)
        .rejectConnectionRequest(connectionId);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Connection request rejected'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _handleEventTap(NetworkingEvent event) {
    // Navigate to event details screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening ${event.title}'),
        backgroundColor: AppTheme.accentColor,
      ),
    );
  }

  void _handleConnectWithRecommendation(
    NetworkingRecommendation recommendation,
  ) {
    ref
        .read(connectionNotifierProvider.notifier)
        .sendConnectionRequest(
          targetUserId: recommendation.recommendedUserId,
          type: NetworkConnectionType.colleague,
          message:
              'Hi! I saw we have similar interests. Would love to connect!',
        );
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Connection request sent!'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
