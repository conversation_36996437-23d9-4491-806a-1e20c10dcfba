// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'networking_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_NetworkConnection _$NetworkConnectionFromJson(Map<String, dynamic> json) =>
    _NetworkConnection(
      id: json['id'] as String,
      userId: json['userId'] as String,
      connectedUserId: json['connectedUserId'] as String,
      status: $enumDecode(_$ConnectionStatusEnumMap, json['status']),
      createdAt: DateTime.parse(json['createdAt'] as String),
      acceptedAt: json['acceptedAt'] == null
          ? null
          : DateTime.parse(json['acceptedAt'] as String),
      message: json['message'] as String?,
      type: $enumDecode(_$NetworkConnectionTypeEnumMap, json['type']),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$NetworkConnectionToJson(_NetworkConnection instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'connectedUserId': instance.connectedUserId,
      'status': _$ConnectionStatusEnumMap[instance.status]!,
      'createdAt': instance.createdAt.toIso8601String(),
      'acceptedAt': instance.acceptedAt?.toIso8601String(),
      'message': instance.message,
      'type': _$NetworkConnectionTypeEnumMap[instance.type]!,
      'metadata': instance.metadata,
    };

const _$ConnectionStatusEnumMap = {
  ConnectionStatus.pending: 'pending',
  ConnectionStatus.accepted: 'accepted',
  ConnectionStatus.rejected: 'rejected',
  ConnectionStatus.blocked: 'blocked',
};

const _$NetworkConnectionTypeEnumMap = {
  NetworkConnectionType.colleague: 'colleague',
  NetworkConnectionType.mentor: 'mentor',
  NetworkConnectionType.mentee: 'mentee',
  NetworkConnectionType.investor: 'investor',
  NetworkConnectionType.entrepreneur: 'entrepreneur',
  NetworkConnectionType.consultant: 'consultant',
  NetworkConnectionType.advisor: 'advisor',
  NetworkConnectionType.partner: 'partner',
};

_NetworkProfile _$NetworkProfileFromJson(Map<String, dynamic> json) =>
    _NetworkProfile(
      id: json['id'] as String,
      userId: json['userId'] as String,
      name: json['name'] as String,
      title: json['title'] as String,
      company: json['company'] as String,
      bio: json['bio'] as String?,
      profilePictureUrl: json['profilePictureUrl'] as String?,
      coverPhotoUrl: json['coverPhotoUrl'] as String?,
      industries: (json['industries'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      skills: (json['skills'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      interests: (json['interests'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      location: json['location'] as String?,
      website: json['website'] as String?,
      linkedinUrl: json['linkedinUrl'] as String?,
      twitterUrl: json['twitterUrl'] as String?,
      instagramUrl: json['instagramUrl'] as String?,
      visibility: $enumDecode(
        _$NetworkProfileVisibilityEnumMap,
        json['visibility'],
      ),
      isVerified: json['isVerified'] as bool,
      isPremium: json['isPremium'] as bool,
      connectionCount: (json['connectionCount'] as num).toInt(),
      mutualConnectionCount: (json['mutualConnectionCount'] as num).toInt(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastActiveAt: json['lastActiveAt'] == null
          ? null
          : DateTime.parse(json['lastActiveAt'] as String),
      achievements: json['achievements'] as Map<String, dynamic>?,
      certifications: (json['certifications'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      languages: (json['languages'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      education: json['education'] as String?,
      experience: json['experience'] as String?,
      preferences: json['preferences'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$NetworkProfileToJson(_NetworkProfile instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'name': instance.name,
      'title': instance.title,
      'company': instance.company,
      'bio': instance.bio,
      'profilePictureUrl': instance.profilePictureUrl,
      'coverPhotoUrl': instance.coverPhotoUrl,
      'industries': instance.industries,
      'skills': instance.skills,
      'interests': instance.interests,
      'location': instance.location,
      'website': instance.website,
      'linkedinUrl': instance.linkedinUrl,
      'twitterUrl': instance.twitterUrl,
      'instagramUrl': instance.instagramUrl,
      'visibility': _$NetworkProfileVisibilityEnumMap[instance.visibility]!,
      'isVerified': instance.isVerified,
      'isPremium': instance.isPremium,
      'connectionCount': instance.connectionCount,
      'mutualConnectionCount': instance.mutualConnectionCount,
      'createdAt': instance.createdAt.toIso8601String(),
      'lastActiveAt': instance.lastActiveAt?.toIso8601String(),
      'achievements': instance.achievements,
      'certifications': instance.certifications,
      'languages': instance.languages,
      'education': instance.education,
      'experience': instance.experience,
      'preferences': instance.preferences,
    };

const _$NetworkProfileVisibilityEnumMap = {
  NetworkProfileVisibility.public: 'public',
  NetworkProfileVisibility.connections: 'connections',
  NetworkProfileVisibility.private: 'private',
};

_NetworkingEvent _$NetworkingEventFromJson(Map<String, dynamic> json) =>
    _NetworkingEvent(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      organizerId: json['organizerId'] as String,
      organizerName: json['organizerName'] as String,
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      location: json['location'] as String,
      virtualMeetingUrl: json['virtualMeetingUrl'] as String?,
      type: $enumDecode(_$EventTypeEnumMap, json['type']),
      visibility: $enumDecode(_$EventVisibilityEnumMap, json['visibility']),
      maxAttendees: (json['maxAttendees'] as num).toInt(),
      currentAttendees: (json['currentAttendees'] as num).toInt(),
      price: (json['price'] as num).toDouble(),
      currency: json['currency'] as String,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
      industries: (json['industries'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      coverImageUrl: json['coverImageUrl'] as String?,
      isExclusive: json['isExclusive'] as bool,
      isVerified: json['isVerified'] as bool,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      agenda: json['agenda'] as Map<String, dynamic>?,
      speakers: (json['speakers'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      venueDetails: json['venueDetails'] as String?,
      requirements: json['requirements'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$NetworkingEventToJson(_NetworkingEvent instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'organizerId': instance.organizerId,
      'organizerName': instance.organizerName,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'location': instance.location,
      'virtualMeetingUrl': instance.virtualMeetingUrl,
      'type': _$EventTypeEnumMap[instance.type]!,
      'visibility': _$EventVisibilityEnumMap[instance.visibility]!,
      'maxAttendees': instance.maxAttendees,
      'currentAttendees': instance.currentAttendees,
      'price': instance.price,
      'currency': instance.currency,
      'tags': instance.tags,
      'industries': instance.industries,
      'coverImageUrl': instance.coverImageUrl,
      'isExclusive': instance.isExclusive,
      'isVerified': instance.isVerified,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'agenda': instance.agenda,
      'speakers': instance.speakers,
      'venueDetails': instance.venueDetails,
      'requirements': instance.requirements,
    };

const _$EventTypeEnumMap = {
  EventType.conference: 'conference',
  EventType.workshop: 'workshop',
  EventType.seminar: 'seminar',
  EventType.meetup: 'meetup',
  EventType.gala: 'gala',
  EventType.retreat: 'retreat',
  EventType.summit: 'summit',
  EventType.forum: 'forum',
  EventType.webinar: 'webinar',
  EventType.networking: 'networking',
};

const _$EventVisibilityEnumMap = {
  EventVisibility.public: 'public',
  EventVisibility.private: 'private',
  EventVisibility.exclusive: 'exclusive',
  EventVisibility.inviteOnly: 'inviteOnly',
};

_BusinessCard _$BusinessCardFromJson(Map<String, dynamic> json) =>
    _BusinessCard(
      id: json['id'] as String,
      userId: json['userId'] as String,
      name: json['name'] as String,
      title: json['title'] as String,
      company: json['company'] as String,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      website: json['website'] as String?,
      linkedinUrl: json['linkedinUrl'] as String?,
      profilePictureUrl: json['profilePictureUrl'] as String?,
      qrCodeUrl: json['qrCodeUrl'] as String?,
      design: $enumDecode(_$BusinessCardDesignEnumMap, json['design']),
      isDigital: json['isDigital'] as bool,
      isShared: json['isShared'] as bool,
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastSharedAt: json['lastSharedAt'] == null
          ? null
          : DateTime.parse(json['lastSharedAt'] as String),
      customFields: json['customFields'] as Map<String, dynamic>?,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$BusinessCardToJson(_BusinessCard instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'name': instance.name,
      'title': instance.title,
      'company': instance.company,
      'email': instance.email,
      'phone': instance.phone,
      'website': instance.website,
      'linkedinUrl': instance.linkedinUrl,
      'profilePictureUrl': instance.profilePictureUrl,
      'qrCodeUrl': instance.qrCodeUrl,
      'design': _$BusinessCardDesignEnumMap[instance.design]!,
      'isDigital': instance.isDigital,
      'isShared': instance.isShared,
      'createdAt': instance.createdAt.toIso8601String(),
      'lastSharedAt': instance.lastSharedAt?.toIso8601String(),
      'customFields': instance.customFields,
      'tags': instance.tags,
      'notes': instance.notes,
    };

const _$BusinessCardDesignEnumMap = {
  BusinessCardDesign.classic: 'classic',
  BusinessCardDesign.modern: 'modern',
  BusinessCardDesign.luxury: 'luxury',
  BusinessCardDesign.minimalist: 'minimalist',
  BusinessCardDesign.creative: 'creative',
  BusinessCardDesign.professional: 'professional',
};

_NetworkingRecommendation _$NetworkingRecommendationFromJson(
  Map<String, dynamic> json,
) => _NetworkingRecommendation(
  id: json['id'] as String,
  userId: json['userId'] as String,
  recommendedUserId: json['recommendedUserId'] as String,
  reason: json['reason'] as String,
  score: (json['score'] as num).toDouble(),
  type: $enumDecode(_$RecommendationTypeEnumMap, json['type']),
  createdAt: DateTime.parse(json['createdAt'] as String),
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$NetworkingRecommendationToJson(
  _NetworkingRecommendation instance,
) => <String, dynamic>{
  'id': instance.id,
  'userId': instance.userId,
  'recommendedUserId': instance.recommendedUserId,
  'reason': instance.reason,
  'score': instance.score,
  'type': _$RecommendationTypeEnumMap[instance.type]!,
  'createdAt': instance.createdAt.toIso8601String(),
  'metadata': instance.metadata,
};

const _$RecommendationTypeEnumMap = {
  RecommendationType.industry: 'industry',
  RecommendationType.skill: 'skill',
  RecommendationType.location: 'location',
  RecommendationType.mutual: 'mutual',
  RecommendationType.algorithm: 'algorithm',
};

_NetworkingMessage _$NetworkingMessageFromJson(Map<String, dynamic> json) =>
    _NetworkingMessage(
      id: json['id'] as String,
      senderId: json['senderId'] as String,
      receiverId: json['receiverId'] as String,
      content: json['content'] as String,
      type: $enumDecode(_$MessageTypeEnumMap, json['type']),
      timestamp: DateTime.parse(json['timestamp'] as String),
      isRead: json['isRead'] as bool,
      attachmentUrl: json['attachmentUrl'] as String?,
      attachmentType: json['attachmentType'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$NetworkingMessageToJson(_NetworkingMessage instance) =>
    <String, dynamic>{
      'id': instance.id,
      'senderId': instance.senderId,
      'receiverId': instance.receiverId,
      'content': instance.content,
      'type': _$MessageTypeEnumMap[instance.type]!,
      'timestamp': instance.timestamp.toIso8601String(),
      'isRead': instance.isRead,
      'attachmentUrl': instance.attachmentUrl,
      'attachmentType': instance.attachmentType,
      'metadata': instance.metadata,
    };

const _$MessageTypeEnumMap = {
  MessageType.text: 'text',
  MessageType.image: 'image',
  MessageType.document: 'document',
  MessageType.contact: 'contact',
  MessageType.invitation: 'invitation',
};
