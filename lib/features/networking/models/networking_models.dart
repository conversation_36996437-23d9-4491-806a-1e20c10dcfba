import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'networking_models.freezed.dart';
part 'networking_models.g.dart';

@freezed
abstract class NetworkConnection with _$NetworkConnection {
  const factory NetworkConnection({
    required String id,
    required String userId,
    required String connectedUserId,
    required ConnectionStatus status,
    required DateTime createdAt,
    DateTime? acceptedAt,
    String? message,
    required NetworkConnectionType type,
    Map<String, dynamic>? metadata,
  }) = _NetworkConnection;

  factory NetworkConnection.fromJson(Map<String, dynamic> json) =>
      _$NetworkConnectionFromJson(json);
}

@freezed
abstract class NetworkProfile with _$NetworkProfile {
  const factory NetworkProfile({
    required String id,
    required String userId,
    required String name,
    required String title,
    required String company,
    String? bio,
    String? profilePictureUrl,
    String? coverPhotoUrl,
    required List<String> industries,
    required List<String> skills,
    required List<String> interests,
    String? location,
    String? website,
    String? linkedinUrl,
    String? twitterUrl,
    String? instagramUrl,
    required NetworkProfileVisibility visibility,
    required bool isVerified,
    required bool isPremium,
    required int connectionCount,
    required int mutualConnectionCount,
    required DateTime createdAt,
    DateTime? lastActiveAt,
    Map<String, dynamic>? achievements,
    List<String>? certifications,
    List<String>? languages,
    String? education,
    String? experience,
    Map<String, dynamic>? preferences,
  }) = _NetworkProfile;

  factory NetworkProfile.fromJson(Map<String, dynamic> json) =>
      _$NetworkProfileFromJson(json);
}

@freezed
abstract class NetworkingEvent with _$NetworkingEvent {
  const factory NetworkingEvent({
    required String id,
    required String title,
    required String description,
    required String organizerId,
    required String organizerName,
    required DateTime startDate,
    required DateTime endDate,
    required String location,
    String? virtualMeetingUrl,
    required EventType type,
    required EventVisibility visibility,
    required int maxAttendees,
    required int currentAttendees,
    required double price,
    required String currency,
    List<String>? tags,
    List<String>? industries,
    String? coverImageUrl,
    required bool isExclusive,
    required bool isVerified,
    required DateTime createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? agenda,
    List<String>? speakers,
    String? venueDetails,
    Map<String, dynamic>? requirements,
  }) = _NetworkingEvent;

  factory NetworkingEvent.fromJson(Map<String, dynamic> json) =>
      _$NetworkingEventFromJson(json);
}

@freezed
abstract class BusinessCard with _$BusinessCard {
  const factory BusinessCard({
    required String id,
    required String userId,
    required String name,
    required String title,
    required String company,
    String? email,
    String? phone,
    String? website,
    String? linkedinUrl,
    String? profilePictureUrl,
    String? qrCodeUrl,
    required BusinessCardDesign design,
    required bool isDigital,
    required bool isShared,
    required DateTime createdAt,
    DateTime? lastSharedAt,
    Map<String, dynamic>? customFields,
    List<String>? tags,
    String? notes,
  }) = _BusinessCard;

  factory BusinessCard.fromJson(Map<String, dynamic> json) =>
      _$BusinessCardFromJson(json);
}

@freezed
abstract class NetworkingRecommendation with _$NetworkingRecommendation {
  const factory NetworkingRecommendation({
    required String id,
    required String userId,
    required String recommendedUserId,
    required String reason,
    required double score,
    required RecommendationType type,
    required DateTime createdAt,
    Map<String, dynamic>? metadata,
  }) = _NetworkingRecommendation;

  factory NetworkingRecommendation.fromJson(Map<String, dynamic> json) =>
      _$NetworkingRecommendationFromJson(json);
}

@freezed
abstract class NetworkingMessage with _$NetworkingMessage {
  const factory NetworkingMessage({
    required String id,
    required String senderId,
    required String receiverId,
    required String content,
    required MessageType type,
    required DateTime timestamp,
    required bool isRead,
    String? attachmentUrl,
    String? attachmentType,
    Map<String, dynamic>? metadata,
  }) = _NetworkingMessage;

  factory NetworkingMessage.fromJson(Map<String, dynamic> json) =>
      _$NetworkingMessageFromJson(json);
}

// Enums
enum ConnectionStatus { pending, accepted, rejected, blocked }

enum NetworkConnectionType {
  colleague,
  mentor,
  mentee,
  investor,
  entrepreneur,
  consultant,
  advisor,
  partner,
}

enum NetworkProfileVisibility { public, connections, private }

enum EventType {
  conference,
  workshop,
  seminar,
  meetup,
  gala,
  retreat,
  summit,
  forum,
  webinar,
  networking,
}

enum EventVisibility { public, private, exclusive, inviteOnly }

enum BusinessCardDesign {
  classic,
  modern,
  luxury,
  minimalist,
  creative,
  professional,
}

enum RecommendationType { industry, skill, location, mutual, algorithm }

enum MessageType { text, image, document, contact, invitation }

// Extensions for Firestore conversion
extension NetworkConnectionFirestore on NetworkConnection {
  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'connectedUserId': connectedUserId,
      'status': status.name,
      'createdAt': Timestamp.fromDate(createdAt),
      'acceptedAt': acceptedAt != null ? Timestamp.fromDate(acceptedAt!) : null,
      'message': message,
      'type': type.name,
      'metadata': metadata,
    };
  }

  static NetworkConnection fromFirestore(String id, Map<String, dynamic> data) {
    return NetworkConnection(
      id: id,
      userId: data['userId'] as String,
      connectedUserId: data['connectedUserId'] as String,
      status: ConnectionStatus.values.firstWhere(
        (e) => e.name == data['status'],
        orElse: () => ConnectionStatus.pending,
      ),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      acceptedAt: data['acceptedAt'] != null
          ? (data['acceptedAt'] as Timestamp).toDate()
          : null,
      message: data['message'] as String?,
      type: NetworkConnectionType.values.firstWhere(
        (e) => e.name == data['type'],
        orElse: () => NetworkConnectionType.colleague,
      ),
      metadata: data['metadata'] as Map<String, dynamic>?,
    );
  }
}

extension NetworkProfileFirestore on NetworkProfile {
  Map<String, dynamic> toFirestore() {
    return {
      'id': id,
      'userId': userId,
      'name': name,
      'title': title,
      'company': company,
      'bio': bio,
      'profilePictureUrl': profilePictureUrl,
      'coverPhotoUrl': coverPhotoUrl,
      'industries': industries,
      'skills': skills,
      'interests': interests,
      'location': location,
      'website': website,
      'linkedinUrl': linkedinUrl,
      'twitterUrl': twitterUrl,
      'instagramUrl': instagramUrl,
      'visibility': visibility.name,
      'isVerified': isVerified,
      'isPremium': isPremium,
      'connectionCount': connectionCount,
      'mutualConnectionCount': mutualConnectionCount,
      'createdAt': Timestamp.fromDate(createdAt),
      'lastActiveAt': lastActiveAt != null
          ? Timestamp.fromDate(lastActiveAt!)
          : null,
      'achievements': achievements,
      'certifications': certifications,
      'languages': languages,
      'education': education,
      'experience': experience,
      'preferences': preferences,
    };
  }

  static NetworkProfile fromFirestore(String id, Map<String, dynamic> data) {
    return NetworkProfile(
      id: id,
      userId: data['userId'] as String,
      name: data['name'] as String,
      title: data['title'] as String,
      company: data['company'] as String,
      bio: data['bio'] as String?,
      profilePictureUrl: data['profilePictureUrl'] as String?,
      coverPhotoUrl: data['coverPhotoUrl'] as String?,
      industries: List<String>.from(data['industries'] ?? []),
      skills: List<String>.from(data['skills'] ?? []),
      interests: List<String>.from(data['interests'] ?? []),
      location: data['location'] as String?,
      website: data['website'] as String?,
      linkedinUrl: data['linkedinUrl'] as String?,
      twitterUrl: data['twitterUrl'] as String?,
      instagramUrl: data['instagramUrl'] as String?,
      visibility: NetworkProfileVisibility.values.firstWhere(
        (e) => e.name == data['visibility'],
        orElse: () => NetworkProfileVisibility.public,
      ),
      isVerified: data['isVerified'] as bool? ?? false,
      isPremium: data['isPremium'] as bool? ?? false,
      connectionCount: data['connectionCount'] as int? ?? 0,
      mutualConnectionCount: data['mutualConnectionCount'] as int? ?? 0,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      lastActiveAt: data['lastActiveAt'] != null
          ? (data['lastActiveAt'] as Timestamp).toDate()
          : null,
      achievements: data['achievements'] as Map<String, dynamic>?,
      certifications: data['certifications'] != null
          ? List<String>.from(data['certifications'])
          : null,
      languages: data['languages'] != null
          ? List<String>.from(data['languages'])
          : null,
      education: data['education'] as String?,
      experience: data['experience'] as String?,
      preferences: data['preferences'] as Map<String, dynamic>?,
    );
  }
}

extension NetworkingEventFirestore on NetworkingEvent {
  Map<String, dynamic> toFirestore() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'organizerId': organizerId,
      'organizerName': organizerName,
      'startDate': Timestamp.fromDate(startDate),
      'endDate': Timestamp.fromDate(endDate),
      'location': location,
      'virtualMeetingUrl': virtualMeetingUrl,
      'type': type.name,
      'visibility': visibility.name,
      'maxAttendees': maxAttendees,
      'currentAttendees': currentAttendees,
      'price': price,
      'currency': currency,
      'tags': tags,
      'industries': industries,
      'coverImageUrl': coverImageUrl,
      'isExclusive': isExclusive,
      'isVerified': isVerified,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'agenda': agenda,
      'speakers': speakers,
      'venueDetails': venueDetails,
      'requirements': requirements,
    };
  }

  static NetworkingEvent fromFirestore(String id, Map<String, dynamic> data) {
    return NetworkingEvent(
      id: id,
      title: data['title'] as String,
      description: data['description'] as String,
      organizerId: data['organizerId'] as String,
      organizerName: data['organizerName'] as String,
      startDate: (data['startDate'] as Timestamp).toDate(),
      endDate: (data['endDate'] as Timestamp).toDate(),
      location: data['location'] as String,
      virtualMeetingUrl: data['virtualMeetingUrl'] as String?,
      type: EventType.values.firstWhere(
        (e) => e.name == data['type'],
        orElse: () => EventType.networking,
      ),
      visibility: EventVisibility.values.firstWhere(
        (e) => e.name == data['visibility'],
        orElse: () => EventVisibility.public,
      ),
      maxAttendees: data['maxAttendees'] as int? ?? 0,
      currentAttendees: data['currentAttendees'] as int? ?? 0,
      price: (data['price'] as num?)?.toDouble() ?? 0.0,
      currency: data['currency'] as String? ?? 'USD',
      tags: data['tags'] != null ? List<String>.from(data['tags']) : null,
      industries: data['industries'] != null
          ? List<String>.from(data['industries'])
          : null,
      coverImageUrl: data['coverImageUrl'] as String?,
      isExclusive: data['isExclusive'] as bool? ?? false,
      isVerified: data['isVerified'] as bool? ?? false,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: data['updatedAt'] != null
          ? (data['updatedAt'] as Timestamp).toDate()
          : null,
      agenda: data['agenda'] as Map<String, dynamic>?,
      speakers: data['speakers'] != null
          ? List<String>.from(data['speakers'])
          : null,
      venueDetails: data['venueDetails'] as String?,
      requirements: data['requirements'] as Map<String, dynamic>?,
    );
  }
}

extension BusinessCardFirestore on BusinessCard {
  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'name': name,
      'title': title,
      'company': company,
      'email': email,
      'phone': phone,
      'website': website,
      'linkedinUrl': linkedinUrl,
      'profilePictureUrl': profilePictureUrl,
      'qrCodeUrl': qrCodeUrl,
      'design': design.name,
      'isDigital': isDigital,
      'isShared': isShared,
      'createdAt': Timestamp.fromDate(createdAt),
      'lastSharedAt': lastSharedAt != null
          ? Timestamp.fromDate(lastSharedAt!)
          : null,
      'customFields': customFields,
      'tags': tags,
      'notes': notes,
    };
  }

  static BusinessCard fromFirestore(String id, Map<String, dynamic> data) {
    return BusinessCard(
      id: id,
      userId: data['userId'] as String,
      name: data['name'] as String,
      title: data['title'] as String,
      company: data['company'] as String,
      email: data['email'] as String?,
      phone: data['phone'] as String?,
      website: data['website'] as String?,
      linkedinUrl: data['linkedinUrl'] as String?,
      profilePictureUrl: data['profilePictureUrl'] as String?,
      qrCodeUrl: data['qrCodeUrl'] as String?,
      design: BusinessCardDesign.values.firstWhere(
        (e) => e.name == data['design'],
        orElse: () => BusinessCardDesign.classic,
      ),
      isDigital: data['isDigital'] as bool? ?? false,
      isShared: data['isShared'] as bool? ?? false,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      lastSharedAt: data['lastSharedAt'] != null
          ? (data['lastSharedAt'] as Timestamp).toDate()
          : null,
      customFields: data['customFields'] as Map<String, dynamic>?,
      tags: data['tags'] != null ? List<String>.from(data['tags']) : null,
      notes: data['notes'] as String?,
    );
  }
}

extension NetworkingRecommendationFirestore on NetworkingRecommendation {
  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'recommendedUserId': recommendedUserId,
      'reason': reason,
      'score': score,
      'type': type.name,
      'createdAt': Timestamp.fromDate(createdAt),
      'metadata': metadata,
    };
  }

  static NetworkingRecommendation fromFirestore(
    String id,
    Map<String, dynamic> data,
  ) {
    return NetworkingRecommendation(
      id: id,
      userId: data['userId'] as String,
      recommendedUserId: data['recommendedUserId'] as String,
      reason: data['reason'] as String,
      score: (data['score'] as num).toDouble(),
      type: RecommendationType.values.firstWhere(
        (e) => e.name == data['type'],
        orElse: () => RecommendationType.algorithm,
      ),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      metadata: data['metadata'] as Map<String, dynamic>?,
    );
  }
}

extension NetworkingMessageFirestore on NetworkingMessage {
  Map<String, dynamic> toFirestore() {
    return {
      'senderId': senderId,
      'receiverId': receiverId,
      'content': content,
      'type': type.name,
      'timestamp': Timestamp.fromDate(timestamp),
      'isRead': isRead,
      'attachmentUrl': attachmentUrl,
      'attachmentType': attachmentType,
      'metadata': metadata,
    };
  }

  static NetworkingMessage fromFirestore(String id, Map<String, dynamic> data) {
    return NetworkingMessage(
      id: id,
      senderId: data['senderId'] as String,
      receiverId: data['receiverId'] as String,
      content: data['content'] as String,
      type: MessageType.values.firstWhere(
        (e) => e.name == data['type'],
        orElse: () => MessageType.text,
      ),
      timestamp: (data['timestamp'] as Timestamp).toDate(),
      isRead: data['isRead'] as bool? ?? false,
      attachmentUrl: data['attachmentUrl'] as String?,
      attachmentType: data['attachmentType'] as String?,
      metadata: data['metadata'] as Map<String, dynamic>?,
    );
  }
}
