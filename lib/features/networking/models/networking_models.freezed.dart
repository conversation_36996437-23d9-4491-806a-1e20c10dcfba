// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'networking_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$NetworkConnection {

 String get id; String get userId; String get connectedUserId; ConnectionStatus get status; DateTime get createdAt; DateTime? get acceptedAt; String? get message; NetworkConnectionType get type; Map<String, dynamic>? get metadata;
/// Create a copy of NetworkConnection
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$NetworkConnectionCopyWith<NetworkConnection> get copyWith => _$NetworkConnectionCopyWithImpl<NetworkConnection>(this as NetworkConnection, _$identity);

  /// Serializes this NetworkConnection to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is NetworkConnection&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.connectedUserId, connectedUserId) || other.connectedUserId == connectedUserId)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.acceptedAt, acceptedAt) || other.acceptedAt == acceptedAt)&&(identical(other.message, message) || other.message == message)&&(identical(other.type, type) || other.type == type)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,connectedUserId,status,createdAt,acceptedAt,message,type,const DeepCollectionEquality().hash(metadata));

@override
String toString() {
  return 'NetworkConnection(id: $id, userId: $userId, connectedUserId: $connectedUserId, status: $status, createdAt: $createdAt, acceptedAt: $acceptedAt, message: $message, type: $type, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $NetworkConnectionCopyWith<$Res>  {
  factory $NetworkConnectionCopyWith(NetworkConnection value, $Res Function(NetworkConnection) _then) = _$NetworkConnectionCopyWithImpl;
@useResult
$Res call({
 String id, String userId, String connectedUserId, ConnectionStatus status, DateTime createdAt, DateTime? acceptedAt, String? message, NetworkConnectionType type, Map<String, dynamic>? metadata
});




}
/// @nodoc
class _$NetworkConnectionCopyWithImpl<$Res>
    implements $NetworkConnectionCopyWith<$Res> {
  _$NetworkConnectionCopyWithImpl(this._self, this._then);

  final NetworkConnection _self;
  final $Res Function(NetworkConnection) _then;

/// Create a copy of NetworkConnection
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? connectedUserId = null,Object? status = null,Object? createdAt = null,Object? acceptedAt = freezed,Object? message = freezed,Object? type = null,Object? metadata = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,connectedUserId: null == connectedUserId ? _self.connectedUserId : connectedUserId // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ConnectionStatus,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,acceptedAt: freezed == acceptedAt ? _self.acceptedAt : acceptedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,message: freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as NetworkConnectionType,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [NetworkConnection].
extension NetworkConnectionPatterns on NetworkConnection {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _NetworkConnection value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _NetworkConnection() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _NetworkConnection value)  $default,){
final _that = this;
switch (_that) {
case _NetworkConnection():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _NetworkConnection value)?  $default,){
final _that = this;
switch (_that) {
case _NetworkConnection() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  String connectedUserId,  ConnectionStatus status,  DateTime createdAt,  DateTime? acceptedAt,  String? message,  NetworkConnectionType type,  Map<String, dynamic>? metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _NetworkConnection() when $default != null:
return $default(_that.id,_that.userId,_that.connectedUserId,_that.status,_that.createdAt,_that.acceptedAt,_that.message,_that.type,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  String connectedUserId,  ConnectionStatus status,  DateTime createdAt,  DateTime? acceptedAt,  String? message,  NetworkConnectionType type,  Map<String, dynamic>? metadata)  $default,) {final _that = this;
switch (_that) {
case _NetworkConnection():
return $default(_that.id,_that.userId,_that.connectedUserId,_that.status,_that.createdAt,_that.acceptedAt,_that.message,_that.type,_that.metadata);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  String connectedUserId,  ConnectionStatus status,  DateTime createdAt,  DateTime? acceptedAt,  String? message,  NetworkConnectionType type,  Map<String, dynamic>? metadata)?  $default,) {final _that = this;
switch (_that) {
case _NetworkConnection() when $default != null:
return $default(_that.id,_that.userId,_that.connectedUserId,_that.status,_that.createdAt,_that.acceptedAt,_that.message,_that.type,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _NetworkConnection implements NetworkConnection {
  const _NetworkConnection({required this.id, required this.userId, required this.connectedUserId, required this.status, required this.createdAt, this.acceptedAt, this.message, required this.type, final  Map<String, dynamic>? metadata}): _metadata = metadata;
  factory _NetworkConnection.fromJson(Map<String, dynamic> json) => _$NetworkConnectionFromJson(json);

@override final  String id;
@override final  String userId;
@override final  String connectedUserId;
@override final  ConnectionStatus status;
@override final  DateTime createdAt;
@override final  DateTime? acceptedAt;
@override final  String? message;
@override final  NetworkConnectionType type;
 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of NetworkConnection
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$NetworkConnectionCopyWith<_NetworkConnection> get copyWith => __$NetworkConnectionCopyWithImpl<_NetworkConnection>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$NetworkConnectionToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _NetworkConnection&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.connectedUserId, connectedUserId) || other.connectedUserId == connectedUserId)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.acceptedAt, acceptedAt) || other.acceptedAt == acceptedAt)&&(identical(other.message, message) || other.message == message)&&(identical(other.type, type) || other.type == type)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,connectedUserId,status,createdAt,acceptedAt,message,type,const DeepCollectionEquality().hash(_metadata));

@override
String toString() {
  return 'NetworkConnection(id: $id, userId: $userId, connectedUserId: $connectedUserId, status: $status, createdAt: $createdAt, acceptedAt: $acceptedAt, message: $message, type: $type, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$NetworkConnectionCopyWith<$Res> implements $NetworkConnectionCopyWith<$Res> {
  factory _$NetworkConnectionCopyWith(_NetworkConnection value, $Res Function(_NetworkConnection) _then) = __$NetworkConnectionCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, String connectedUserId, ConnectionStatus status, DateTime createdAt, DateTime? acceptedAt, String? message, NetworkConnectionType type, Map<String, dynamic>? metadata
});




}
/// @nodoc
class __$NetworkConnectionCopyWithImpl<$Res>
    implements _$NetworkConnectionCopyWith<$Res> {
  __$NetworkConnectionCopyWithImpl(this._self, this._then);

  final _NetworkConnection _self;
  final $Res Function(_NetworkConnection) _then;

/// Create a copy of NetworkConnection
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? connectedUserId = null,Object? status = null,Object? createdAt = null,Object? acceptedAt = freezed,Object? message = freezed,Object? type = null,Object? metadata = freezed,}) {
  return _then(_NetworkConnection(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,connectedUserId: null == connectedUserId ? _self.connectedUserId : connectedUserId // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ConnectionStatus,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,acceptedAt: freezed == acceptedAt ? _self.acceptedAt : acceptedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,message: freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as NetworkConnectionType,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}


/// @nodoc
mixin _$NetworkProfile {

 String get id; String get userId; String get name; String get title; String get company; String? get bio; String? get profilePictureUrl; String? get coverPhotoUrl; List<String> get industries; List<String> get skills; List<String> get interests; String? get location; String? get website; String? get linkedinUrl; String? get twitterUrl; String? get instagramUrl; NetworkProfileVisibility get visibility; bool get isVerified; bool get isPremium; int get connectionCount; int get mutualConnectionCount; DateTime get createdAt; DateTime? get lastActiveAt; Map<String, dynamic>? get achievements; List<String>? get certifications; List<String>? get languages; String? get education; String? get experience; Map<String, dynamic>? get preferences;
/// Create a copy of NetworkProfile
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$NetworkProfileCopyWith<NetworkProfile> get copyWith => _$NetworkProfileCopyWithImpl<NetworkProfile>(this as NetworkProfile, _$identity);

  /// Serializes this NetworkProfile to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is NetworkProfile&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.name, name) || other.name == name)&&(identical(other.title, title) || other.title == title)&&(identical(other.company, company) || other.company == company)&&(identical(other.bio, bio) || other.bio == bio)&&(identical(other.profilePictureUrl, profilePictureUrl) || other.profilePictureUrl == profilePictureUrl)&&(identical(other.coverPhotoUrl, coverPhotoUrl) || other.coverPhotoUrl == coverPhotoUrl)&&const DeepCollectionEquality().equals(other.industries, industries)&&const DeepCollectionEquality().equals(other.skills, skills)&&const DeepCollectionEquality().equals(other.interests, interests)&&(identical(other.location, location) || other.location == location)&&(identical(other.website, website) || other.website == website)&&(identical(other.linkedinUrl, linkedinUrl) || other.linkedinUrl == linkedinUrl)&&(identical(other.twitterUrl, twitterUrl) || other.twitterUrl == twitterUrl)&&(identical(other.instagramUrl, instagramUrl) || other.instagramUrl == instagramUrl)&&(identical(other.visibility, visibility) || other.visibility == visibility)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.isPremium, isPremium) || other.isPremium == isPremium)&&(identical(other.connectionCount, connectionCount) || other.connectionCount == connectionCount)&&(identical(other.mutualConnectionCount, mutualConnectionCount) || other.mutualConnectionCount == mutualConnectionCount)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.lastActiveAt, lastActiveAt) || other.lastActiveAt == lastActiveAt)&&const DeepCollectionEquality().equals(other.achievements, achievements)&&const DeepCollectionEquality().equals(other.certifications, certifications)&&const DeepCollectionEquality().equals(other.languages, languages)&&(identical(other.education, education) || other.education == education)&&(identical(other.experience, experience) || other.experience == experience)&&const DeepCollectionEquality().equals(other.preferences, preferences));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,userId,name,title,company,bio,profilePictureUrl,coverPhotoUrl,const DeepCollectionEquality().hash(industries),const DeepCollectionEquality().hash(skills),const DeepCollectionEquality().hash(interests),location,website,linkedinUrl,twitterUrl,instagramUrl,visibility,isVerified,isPremium,connectionCount,mutualConnectionCount,createdAt,lastActiveAt,const DeepCollectionEquality().hash(achievements),const DeepCollectionEquality().hash(certifications),const DeepCollectionEquality().hash(languages),education,experience,const DeepCollectionEquality().hash(preferences)]);

@override
String toString() {
  return 'NetworkProfile(id: $id, userId: $userId, name: $name, title: $title, company: $company, bio: $bio, profilePictureUrl: $profilePictureUrl, coverPhotoUrl: $coverPhotoUrl, industries: $industries, skills: $skills, interests: $interests, location: $location, website: $website, linkedinUrl: $linkedinUrl, twitterUrl: $twitterUrl, instagramUrl: $instagramUrl, visibility: $visibility, isVerified: $isVerified, isPremium: $isPremium, connectionCount: $connectionCount, mutualConnectionCount: $mutualConnectionCount, createdAt: $createdAt, lastActiveAt: $lastActiveAt, achievements: $achievements, certifications: $certifications, languages: $languages, education: $education, experience: $experience, preferences: $preferences)';
}


}

/// @nodoc
abstract mixin class $NetworkProfileCopyWith<$Res>  {
  factory $NetworkProfileCopyWith(NetworkProfile value, $Res Function(NetworkProfile) _then) = _$NetworkProfileCopyWithImpl;
@useResult
$Res call({
 String id, String userId, String name, String title, String company, String? bio, String? profilePictureUrl, String? coverPhotoUrl, List<String> industries, List<String> skills, List<String> interests, String? location, String? website, String? linkedinUrl, String? twitterUrl, String? instagramUrl, NetworkProfileVisibility visibility, bool isVerified, bool isPremium, int connectionCount, int mutualConnectionCount, DateTime createdAt, DateTime? lastActiveAt, Map<String, dynamic>? achievements, List<String>? certifications, List<String>? languages, String? education, String? experience, Map<String, dynamic>? preferences
});




}
/// @nodoc
class _$NetworkProfileCopyWithImpl<$Res>
    implements $NetworkProfileCopyWith<$Res> {
  _$NetworkProfileCopyWithImpl(this._self, this._then);

  final NetworkProfile _self;
  final $Res Function(NetworkProfile) _then;

/// Create a copy of NetworkProfile
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? name = null,Object? title = null,Object? company = null,Object? bio = freezed,Object? profilePictureUrl = freezed,Object? coverPhotoUrl = freezed,Object? industries = null,Object? skills = null,Object? interests = null,Object? location = freezed,Object? website = freezed,Object? linkedinUrl = freezed,Object? twitterUrl = freezed,Object? instagramUrl = freezed,Object? visibility = null,Object? isVerified = null,Object? isPremium = null,Object? connectionCount = null,Object? mutualConnectionCount = null,Object? createdAt = null,Object? lastActiveAt = freezed,Object? achievements = freezed,Object? certifications = freezed,Object? languages = freezed,Object? education = freezed,Object? experience = freezed,Object? preferences = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,company: null == company ? _self.company : company // ignore: cast_nullable_to_non_nullable
as String,bio: freezed == bio ? _self.bio : bio // ignore: cast_nullable_to_non_nullable
as String?,profilePictureUrl: freezed == profilePictureUrl ? _self.profilePictureUrl : profilePictureUrl // ignore: cast_nullable_to_non_nullable
as String?,coverPhotoUrl: freezed == coverPhotoUrl ? _self.coverPhotoUrl : coverPhotoUrl // ignore: cast_nullable_to_non_nullable
as String?,industries: null == industries ? _self.industries : industries // ignore: cast_nullable_to_non_nullable
as List<String>,skills: null == skills ? _self.skills : skills // ignore: cast_nullable_to_non_nullable
as List<String>,interests: null == interests ? _self.interests : interests // ignore: cast_nullable_to_non_nullable
as List<String>,location: freezed == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String?,website: freezed == website ? _self.website : website // ignore: cast_nullable_to_non_nullable
as String?,linkedinUrl: freezed == linkedinUrl ? _self.linkedinUrl : linkedinUrl // ignore: cast_nullable_to_non_nullable
as String?,twitterUrl: freezed == twitterUrl ? _self.twitterUrl : twitterUrl // ignore: cast_nullable_to_non_nullable
as String?,instagramUrl: freezed == instagramUrl ? _self.instagramUrl : instagramUrl // ignore: cast_nullable_to_non_nullable
as String?,visibility: null == visibility ? _self.visibility : visibility // ignore: cast_nullable_to_non_nullable
as NetworkProfileVisibility,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,isPremium: null == isPremium ? _self.isPremium : isPremium // ignore: cast_nullable_to_non_nullable
as bool,connectionCount: null == connectionCount ? _self.connectionCount : connectionCount // ignore: cast_nullable_to_non_nullable
as int,mutualConnectionCount: null == mutualConnectionCount ? _self.mutualConnectionCount : mutualConnectionCount // ignore: cast_nullable_to_non_nullable
as int,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,lastActiveAt: freezed == lastActiveAt ? _self.lastActiveAt : lastActiveAt // ignore: cast_nullable_to_non_nullable
as DateTime?,achievements: freezed == achievements ? _self.achievements : achievements // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,certifications: freezed == certifications ? _self.certifications : certifications // ignore: cast_nullable_to_non_nullable
as List<String>?,languages: freezed == languages ? _self.languages : languages // ignore: cast_nullable_to_non_nullable
as List<String>?,education: freezed == education ? _self.education : education // ignore: cast_nullable_to_non_nullable
as String?,experience: freezed == experience ? _self.experience : experience // ignore: cast_nullable_to_non_nullable
as String?,preferences: freezed == preferences ? _self.preferences : preferences // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [NetworkProfile].
extension NetworkProfilePatterns on NetworkProfile {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _NetworkProfile value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _NetworkProfile() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _NetworkProfile value)  $default,){
final _that = this;
switch (_that) {
case _NetworkProfile():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _NetworkProfile value)?  $default,){
final _that = this;
switch (_that) {
case _NetworkProfile() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  String name,  String title,  String company,  String? bio,  String? profilePictureUrl,  String? coverPhotoUrl,  List<String> industries,  List<String> skills,  List<String> interests,  String? location,  String? website,  String? linkedinUrl,  String? twitterUrl,  String? instagramUrl,  NetworkProfileVisibility visibility,  bool isVerified,  bool isPremium,  int connectionCount,  int mutualConnectionCount,  DateTime createdAt,  DateTime? lastActiveAt,  Map<String, dynamic>? achievements,  List<String>? certifications,  List<String>? languages,  String? education,  String? experience,  Map<String, dynamic>? preferences)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _NetworkProfile() when $default != null:
return $default(_that.id,_that.userId,_that.name,_that.title,_that.company,_that.bio,_that.profilePictureUrl,_that.coverPhotoUrl,_that.industries,_that.skills,_that.interests,_that.location,_that.website,_that.linkedinUrl,_that.twitterUrl,_that.instagramUrl,_that.visibility,_that.isVerified,_that.isPremium,_that.connectionCount,_that.mutualConnectionCount,_that.createdAt,_that.lastActiveAt,_that.achievements,_that.certifications,_that.languages,_that.education,_that.experience,_that.preferences);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  String name,  String title,  String company,  String? bio,  String? profilePictureUrl,  String? coverPhotoUrl,  List<String> industries,  List<String> skills,  List<String> interests,  String? location,  String? website,  String? linkedinUrl,  String? twitterUrl,  String? instagramUrl,  NetworkProfileVisibility visibility,  bool isVerified,  bool isPremium,  int connectionCount,  int mutualConnectionCount,  DateTime createdAt,  DateTime? lastActiveAt,  Map<String, dynamic>? achievements,  List<String>? certifications,  List<String>? languages,  String? education,  String? experience,  Map<String, dynamic>? preferences)  $default,) {final _that = this;
switch (_that) {
case _NetworkProfile():
return $default(_that.id,_that.userId,_that.name,_that.title,_that.company,_that.bio,_that.profilePictureUrl,_that.coverPhotoUrl,_that.industries,_that.skills,_that.interests,_that.location,_that.website,_that.linkedinUrl,_that.twitterUrl,_that.instagramUrl,_that.visibility,_that.isVerified,_that.isPremium,_that.connectionCount,_that.mutualConnectionCount,_that.createdAt,_that.lastActiveAt,_that.achievements,_that.certifications,_that.languages,_that.education,_that.experience,_that.preferences);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  String name,  String title,  String company,  String? bio,  String? profilePictureUrl,  String? coverPhotoUrl,  List<String> industries,  List<String> skills,  List<String> interests,  String? location,  String? website,  String? linkedinUrl,  String? twitterUrl,  String? instagramUrl,  NetworkProfileVisibility visibility,  bool isVerified,  bool isPremium,  int connectionCount,  int mutualConnectionCount,  DateTime createdAt,  DateTime? lastActiveAt,  Map<String, dynamic>? achievements,  List<String>? certifications,  List<String>? languages,  String? education,  String? experience,  Map<String, dynamic>? preferences)?  $default,) {final _that = this;
switch (_that) {
case _NetworkProfile() when $default != null:
return $default(_that.id,_that.userId,_that.name,_that.title,_that.company,_that.bio,_that.profilePictureUrl,_that.coverPhotoUrl,_that.industries,_that.skills,_that.interests,_that.location,_that.website,_that.linkedinUrl,_that.twitterUrl,_that.instagramUrl,_that.visibility,_that.isVerified,_that.isPremium,_that.connectionCount,_that.mutualConnectionCount,_that.createdAt,_that.lastActiveAt,_that.achievements,_that.certifications,_that.languages,_that.education,_that.experience,_that.preferences);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _NetworkProfile implements NetworkProfile {
  const _NetworkProfile({required this.id, required this.userId, required this.name, required this.title, required this.company, this.bio, this.profilePictureUrl, this.coverPhotoUrl, required final  List<String> industries, required final  List<String> skills, required final  List<String> interests, this.location, this.website, this.linkedinUrl, this.twitterUrl, this.instagramUrl, required this.visibility, required this.isVerified, required this.isPremium, required this.connectionCount, required this.mutualConnectionCount, required this.createdAt, this.lastActiveAt, final  Map<String, dynamic>? achievements, final  List<String>? certifications, final  List<String>? languages, this.education, this.experience, final  Map<String, dynamic>? preferences}): _industries = industries,_skills = skills,_interests = interests,_achievements = achievements,_certifications = certifications,_languages = languages,_preferences = preferences;
  factory _NetworkProfile.fromJson(Map<String, dynamic> json) => _$NetworkProfileFromJson(json);

@override final  String id;
@override final  String userId;
@override final  String name;
@override final  String title;
@override final  String company;
@override final  String? bio;
@override final  String? profilePictureUrl;
@override final  String? coverPhotoUrl;
 final  List<String> _industries;
@override List<String> get industries {
  if (_industries is EqualUnmodifiableListView) return _industries;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_industries);
}

 final  List<String> _skills;
@override List<String> get skills {
  if (_skills is EqualUnmodifiableListView) return _skills;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_skills);
}

 final  List<String> _interests;
@override List<String> get interests {
  if (_interests is EqualUnmodifiableListView) return _interests;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_interests);
}

@override final  String? location;
@override final  String? website;
@override final  String? linkedinUrl;
@override final  String? twitterUrl;
@override final  String? instagramUrl;
@override final  NetworkProfileVisibility visibility;
@override final  bool isVerified;
@override final  bool isPremium;
@override final  int connectionCount;
@override final  int mutualConnectionCount;
@override final  DateTime createdAt;
@override final  DateTime? lastActiveAt;
 final  Map<String, dynamic>? _achievements;
@override Map<String, dynamic>? get achievements {
  final value = _achievements;
  if (value == null) return null;
  if (_achievements is EqualUnmodifiableMapView) return _achievements;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

 final  List<String>? _certifications;
@override List<String>? get certifications {
  final value = _certifications;
  if (value == null) return null;
  if (_certifications is EqualUnmodifiableListView) return _certifications;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<String>? _languages;
@override List<String>? get languages {
  final value = _languages;
  if (value == null) return null;
  if (_languages is EqualUnmodifiableListView) return _languages;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override final  String? education;
@override final  String? experience;
 final  Map<String, dynamic>? _preferences;
@override Map<String, dynamic>? get preferences {
  final value = _preferences;
  if (value == null) return null;
  if (_preferences is EqualUnmodifiableMapView) return _preferences;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of NetworkProfile
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$NetworkProfileCopyWith<_NetworkProfile> get copyWith => __$NetworkProfileCopyWithImpl<_NetworkProfile>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$NetworkProfileToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _NetworkProfile&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.name, name) || other.name == name)&&(identical(other.title, title) || other.title == title)&&(identical(other.company, company) || other.company == company)&&(identical(other.bio, bio) || other.bio == bio)&&(identical(other.profilePictureUrl, profilePictureUrl) || other.profilePictureUrl == profilePictureUrl)&&(identical(other.coverPhotoUrl, coverPhotoUrl) || other.coverPhotoUrl == coverPhotoUrl)&&const DeepCollectionEquality().equals(other._industries, _industries)&&const DeepCollectionEquality().equals(other._skills, _skills)&&const DeepCollectionEquality().equals(other._interests, _interests)&&(identical(other.location, location) || other.location == location)&&(identical(other.website, website) || other.website == website)&&(identical(other.linkedinUrl, linkedinUrl) || other.linkedinUrl == linkedinUrl)&&(identical(other.twitterUrl, twitterUrl) || other.twitterUrl == twitterUrl)&&(identical(other.instagramUrl, instagramUrl) || other.instagramUrl == instagramUrl)&&(identical(other.visibility, visibility) || other.visibility == visibility)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.isPremium, isPremium) || other.isPremium == isPremium)&&(identical(other.connectionCount, connectionCount) || other.connectionCount == connectionCount)&&(identical(other.mutualConnectionCount, mutualConnectionCount) || other.mutualConnectionCount == mutualConnectionCount)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.lastActiveAt, lastActiveAt) || other.lastActiveAt == lastActiveAt)&&const DeepCollectionEquality().equals(other._achievements, _achievements)&&const DeepCollectionEquality().equals(other._certifications, _certifications)&&const DeepCollectionEquality().equals(other._languages, _languages)&&(identical(other.education, education) || other.education == education)&&(identical(other.experience, experience) || other.experience == experience)&&const DeepCollectionEquality().equals(other._preferences, _preferences));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,userId,name,title,company,bio,profilePictureUrl,coverPhotoUrl,const DeepCollectionEquality().hash(_industries),const DeepCollectionEquality().hash(_skills),const DeepCollectionEquality().hash(_interests),location,website,linkedinUrl,twitterUrl,instagramUrl,visibility,isVerified,isPremium,connectionCount,mutualConnectionCount,createdAt,lastActiveAt,const DeepCollectionEquality().hash(_achievements),const DeepCollectionEquality().hash(_certifications),const DeepCollectionEquality().hash(_languages),education,experience,const DeepCollectionEquality().hash(_preferences)]);

@override
String toString() {
  return 'NetworkProfile(id: $id, userId: $userId, name: $name, title: $title, company: $company, bio: $bio, profilePictureUrl: $profilePictureUrl, coverPhotoUrl: $coverPhotoUrl, industries: $industries, skills: $skills, interests: $interests, location: $location, website: $website, linkedinUrl: $linkedinUrl, twitterUrl: $twitterUrl, instagramUrl: $instagramUrl, visibility: $visibility, isVerified: $isVerified, isPremium: $isPremium, connectionCount: $connectionCount, mutualConnectionCount: $mutualConnectionCount, createdAt: $createdAt, lastActiveAt: $lastActiveAt, achievements: $achievements, certifications: $certifications, languages: $languages, education: $education, experience: $experience, preferences: $preferences)';
}


}

/// @nodoc
abstract mixin class _$NetworkProfileCopyWith<$Res> implements $NetworkProfileCopyWith<$Res> {
  factory _$NetworkProfileCopyWith(_NetworkProfile value, $Res Function(_NetworkProfile) _then) = __$NetworkProfileCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, String name, String title, String company, String? bio, String? profilePictureUrl, String? coverPhotoUrl, List<String> industries, List<String> skills, List<String> interests, String? location, String? website, String? linkedinUrl, String? twitterUrl, String? instagramUrl, NetworkProfileVisibility visibility, bool isVerified, bool isPremium, int connectionCount, int mutualConnectionCount, DateTime createdAt, DateTime? lastActiveAt, Map<String, dynamic>? achievements, List<String>? certifications, List<String>? languages, String? education, String? experience, Map<String, dynamic>? preferences
});




}
/// @nodoc
class __$NetworkProfileCopyWithImpl<$Res>
    implements _$NetworkProfileCopyWith<$Res> {
  __$NetworkProfileCopyWithImpl(this._self, this._then);

  final _NetworkProfile _self;
  final $Res Function(_NetworkProfile) _then;

/// Create a copy of NetworkProfile
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? name = null,Object? title = null,Object? company = null,Object? bio = freezed,Object? profilePictureUrl = freezed,Object? coverPhotoUrl = freezed,Object? industries = null,Object? skills = null,Object? interests = null,Object? location = freezed,Object? website = freezed,Object? linkedinUrl = freezed,Object? twitterUrl = freezed,Object? instagramUrl = freezed,Object? visibility = null,Object? isVerified = null,Object? isPremium = null,Object? connectionCount = null,Object? mutualConnectionCount = null,Object? createdAt = null,Object? lastActiveAt = freezed,Object? achievements = freezed,Object? certifications = freezed,Object? languages = freezed,Object? education = freezed,Object? experience = freezed,Object? preferences = freezed,}) {
  return _then(_NetworkProfile(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,company: null == company ? _self.company : company // ignore: cast_nullable_to_non_nullable
as String,bio: freezed == bio ? _self.bio : bio // ignore: cast_nullable_to_non_nullable
as String?,profilePictureUrl: freezed == profilePictureUrl ? _self.profilePictureUrl : profilePictureUrl // ignore: cast_nullable_to_non_nullable
as String?,coverPhotoUrl: freezed == coverPhotoUrl ? _self.coverPhotoUrl : coverPhotoUrl // ignore: cast_nullable_to_non_nullable
as String?,industries: null == industries ? _self._industries : industries // ignore: cast_nullable_to_non_nullable
as List<String>,skills: null == skills ? _self._skills : skills // ignore: cast_nullable_to_non_nullable
as List<String>,interests: null == interests ? _self._interests : interests // ignore: cast_nullable_to_non_nullable
as List<String>,location: freezed == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String?,website: freezed == website ? _self.website : website // ignore: cast_nullable_to_non_nullable
as String?,linkedinUrl: freezed == linkedinUrl ? _self.linkedinUrl : linkedinUrl // ignore: cast_nullable_to_non_nullable
as String?,twitterUrl: freezed == twitterUrl ? _self.twitterUrl : twitterUrl // ignore: cast_nullable_to_non_nullable
as String?,instagramUrl: freezed == instagramUrl ? _self.instagramUrl : instagramUrl // ignore: cast_nullable_to_non_nullable
as String?,visibility: null == visibility ? _self.visibility : visibility // ignore: cast_nullable_to_non_nullable
as NetworkProfileVisibility,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,isPremium: null == isPremium ? _self.isPremium : isPremium // ignore: cast_nullable_to_non_nullable
as bool,connectionCount: null == connectionCount ? _self.connectionCount : connectionCount // ignore: cast_nullable_to_non_nullable
as int,mutualConnectionCount: null == mutualConnectionCount ? _self.mutualConnectionCount : mutualConnectionCount // ignore: cast_nullable_to_non_nullable
as int,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,lastActiveAt: freezed == lastActiveAt ? _self.lastActiveAt : lastActiveAt // ignore: cast_nullable_to_non_nullable
as DateTime?,achievements: freezed == achievements ? _self._achievements : achievements // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,certifications: freezed == certifications ? _self._certifications : certifications // ignore: cast_nullable_to_non_nullable
as List<String>?,languages: freezed == languages ? _self._languages : languages // ignore: cast_nullable_to_non_nullable
as List<String>?,education: freezed == education ? _self.education : education // ignore: cast_nullable_to_non_nullable
as String?,experience: freezed == experience ? _self.experience : experience // ignore: cast_nullable_to_non_nullable
as String?,preferences: freezed == preferences ? _self._preferences : preferences // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}


/// @nodoc
mixin _$NetworkingEvent {

 String get id; String get title; String get description; String get organizerId; String get organizerName; DateTime get startDate; DateTime get endDate; String get location; String? get virtualMeetingUrl; EventType get type; EventVisibility get visibility; int get maxAttendees; int get currentAttendees; double get price; String get currency; List<String>? get tags; List<String>? get industries; String? get coverImageUrl; bool get isExclusive; bool get isVerified; DateTime get createdAt; DateTime? get updatedAt; Map<String, dynamic>? get agenda; List<String>? get speakers; String? get venueDetails; Map<String, dynamic>? get requirements;
/// Create a copy of NetworkingEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$NetworkingEventCopyWith<NetworkingEvent> get copyWith => _$NetworkingEventCopyWithImpl<NetworkingEvent>(this as NetworkingEvent, _$identity);

  /// Serializes this NetworkingEvent to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is NetworkingEvent&&(identical(other.id, id) || other.id == id)&&(identical(other.title, title) || other.title == title)&&(identical(other.description, description) || other.description == description)&&(identical(other.organizerId, organizerId) || other.organizerId == organizerId)&&(identical(other.organizerName, organizerName) || other.organizerName == organizerName)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.location, location) || other.location == location)&&(identical(other.virtualMeetingUrl, virtualMeetingUrl) || other.virtualMeetingUrl == virtualMeetingUrl)&&(identical(other.type, type) || other.type == type)&&(identical(other.visibility, visibility) || other.visibility == visibility)&&(identical(other.maxAttendees, maxAttendees) || other.maxAttendees == maxAttendees)&&(identical(other.currentAttendees, currentAttendees) || other.currentAttendees == currentAttendees)&&(identical(other.price, price) || other.price == price)&&(identical(other.currency, currency) || other.currency == currency)&&const DeepCollectionEquality().equals(other.tags, tags)&&const DeepCollectionEquality().equals(other.industries, industries)&&(identical(other.coverImageUrl, coverImageUrl) || other.coverImageUrl == coverImageUrl)&&(identical(other.isExclusive, isExclusive) || other.isExclusive == isExclusive)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&const DeepCollectionEquality().equals(other.agenda, agenda)&&const DeepCollectionEquality().equals(other.speakers, speakers)&&(identical(other.venueDetails, venueDetails) || other.venueDetails == venueDetails)&&const DeepCollectionEquality().equals(other.requirements, requirements));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,title,description,organizerId,organizerName,startDate,endDate,location,virtualMeetingUrl,type,visibility,maxAttendees,currentAttendees,price,currency,const DeepCollectionEquality().hash(tags),const DeepCollectionEquality().hash(industries),coverImageUrl,isExclusive,isVerified,createdAt,updatedAt,const DeepCollectionEquality().hash(agenda),const DeepCollectionEquality().hash(speakers),venueDetails,const DeepCollectionEquality().hash(requirements)]);

@override
String toString() {
  return 'NetworkingEvent(id: $id, title: $title, description: $description, organizerId: $organizerId, organizerName: $organizerName, startDate: $startDate, endDate: $endDate, location: $location, virtualMeetingUrl: $virtualMeetingUrl, type: $type, visibility: $visibility, maxAttendees: $maxAttendees, currentAttendees: $currentAttendees, price: $price, currency: $currency, tags: $tags, industries: $industries, coverImageUrl: $coverImageUrl, isExclusive: $isExclusive, isVerified: $isVerified, createdAt: $createdAt, updatedAt: $updatedAt, agenda: $agenda, speakers: $speakers, venueDetails: $venueDetails, requirements: $requirements)';
}


}

/// @nodoc
abstract mixin class $NetworkingEventCopyWith<$Res>  {
  factory $NetworkingEventCopyWith(NetworkingEvent value, $Res Function(NetworkingEvent) _then) = _$NetworkingEventCopyWithImpl;
@useResult
$Res call({
 String id, String title, String description, String organizerId, String organizerName, DateTime startDate, DateTime endDate, String location, String? virtualMeetingUrl, EventType type, EventVisibility visibility, int maxAttendees, int currentAttendees, double price, String currency, List<String>? tags, List<String>? industries, String? coverImageUrl, bool isExclusive, bool isVerified, DateTime createdAt, DateTime? updatedAt, Map<String, dynamic>? agenda, List<String>? speakers, String? venueDetails, Map<String, dynamic>? requirements
});




}
/// @nodoc
class _$NetworkingEventCopyWithImpl<$Res>
    implements $NetworkingEventCopyWith<$Res> {
  _$NetworkingEventCopyWithImpl(this._self, this._then);

  final NetworkingEvent _self;
  final $Res Function(NetworkingEvent) _then;

/// Create a copy of NetworkingEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? title = null,Object? description = null,Object? organizerId = null,Object? organizerName = null,Object? startDate = null,Object? endDate = null,Object? location = null,Object? virtualMeetingUrl = freezed,Object? type = null,Object? visibility = null,Object? maxAttendees = null,Object? currentAttendees = null,Object? price = null,Object? currency = null,Object? tags = freezed,Object? industries = freezed,Object? coverImageUrl = freezed,Object? isExclusive = null,Object? isVerified = null,Object? createdAt = null,Object? updatedAt = freezed,Object? agenda = freezed,Object? speakers = freezed,Object? venueDetails = freezed,Object? requirements = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,organizerId: null == organizerId ? _self.organizerId : organizerId // ignore: cast_nullable_to_non_nullable
as String,organizerName: null == organizerName ? _self.organizerName : organizerName // ignore: cast_nullable_to_non_nullable
as String,startDate: null == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime,endDate: null == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime,location: null == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String,virtualMeetingUrl: freezed == virtualMeetingUrl ? _self.virtualMeetingUrl : virtualMeetingUrl // ignore: cast_nullable_to_non_nullable
as String?,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as EventType,visibility: null == visibility ? _self.visibility : visibility // ignore: cast_nullable_to_non_nullable
as EventVisibility,maxAttendees: null == maxAttendees ? _self.maxAttendees : maxAttendees // ignore: cast_nullable_to_non_nullable
as int,currentAttendees: null == currentAttendees ? _self.currentAttendees : currentAttendees // ignore: cast_nullable_to_non_nullable
as int,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,tags: freezed == tags ? _self.tags : tags // ignore: cast_nullable_to_non_nullable
as List<String>?,industries: freezed == industries ? _self.industries : industries // ignore: cast_nullable_to_non_nullable
as List<String>?,coverImageUrl: freezed == coverImageUrl ? _self.coverImageUrl : coverImageUrl // ignore: cast_nullable_to_non_nullable
as String?,isExclusive: null == isExclusive ? _self.isExclusive : isExclusive // ignore: cast_nullable_to_non_nullable
as bool,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,agenda: freezed == agenda ? _self.agenda : agenda // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,speakers: freezed == speakers ? _self.speakers : speakers // ignore: cast_nullable_to_non_nullable
as List<String>?,venueDetails: freezed == venueDetails ? _self.venueDetails : venueDetails // ignore: cast_nullable_to_non_nullable
as String?,requirements: freezed == requirements ? _self.requirements : requirements // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [NetworkingEvent].
extension NetworkingEventPatterns on NetworkingEvent {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _NetworkingEvent value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _NetworkingEvent() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _NetworkingEvent value)  $default,){
final _that = this;
switch (_that) {
case _NetworkingEvent():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _NetworkingEvent value)?  $default,){
final _that = this;
switch (_that) {
case _NetworkingEvent() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String title,  String description,  String organizerId,  String organizerName,  DateTime startDate,  DateTime endDate,  String location,  String? virtualMeetingUrl,  EventType type,  EventVisibility visibility,  int maxAttendees,  int currentAttendees,  double price,  String currency,  List<String>? tags,  List<String>? industries,  String? coverImageUrl,  bool isExclusive,  bool isVerified,  DateTime createdAt,  DateTime? updatedAt,  Map<String, dynamic>? agenda,  List<String>? speakers,  String? venueDetails,  Map<String, dynamic>? requirements)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _NetworkingEvent() when $default != null:
return $default(_that.id,_that.title,_that.description,_that.organizerId,_that.organizerName,_that.startDate,_that.endDate,_that.location,_that.virtualMeetingUrl,_that.type,_that.visibility,_that.maxAttendees,_that.currentAttendees,_that.price,_that.currency,_that.tags,_that.industries,_that.coverImageUrl,_that.isExclusive,_that.isVerified,_that.createdAt,_that.updatedAt,_that.agenda,_that.speakers,_that.venueDetails,_that.requirements);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String title,  String description,  String organizerId,  String organizerName,  DateTime startDate,  DateTime endDate,  String location,  String? virtualMeetingUrl,  EventType type,  EventVisibility visibility,  int maxAttendees,  int currentAttendees,  double price,  String currency,  List<String>? tags,  List<String>? industries,  String? coverImageUrl,  bool isExclusive,  bool isVerified,  DateTime createdAt,  DateTime? updatedAt,  Map<String, dynamic>? agenda,  List<String>? speakers,  String? venueDetails,  Map<String, dynamic>? requirements)  $default,) {final _that = this;
switch (_that) {
case _NetworkingEvent():
return $default(_that.id,_that.title,_that.description,_that.organizerId,_that.organizerName,_that.startDate,_that.endDate,_that.location,_that.virtualMeetingUrl,_that.type,_that.visibility,_that.maxAttendees,_that.currentAttendees,_that.price,_that.currency,_that.tags,_that.industries,_that.coverImageUrl,_that.isExclusive,_that.isVerified,_that.createdAt,_that.updatedAt,_that.agenda,_that.speakers,_that.venueDetails,_that.requirements);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String title,  String description,  String organizerId,  String organizerName,  DateTime startDate,  DateTime endDate,  String location,  String? virtualMeetingUrl,  EventType type,  EventVisibility visibility,  int maxAttendees,  int currentAttendees,  double price,  String currency,  List<String>? tags,  List<String>? industries,  String? coverImageUrl,  bool isExclusive,  bool isVerified,  DateTime createdAt,  DateTime? updatedAt,  Map<String, dynamic>? agenda,  List<String>? speakers,  String? venueDetails,  Map<String, dynamic>? requirements)?  $default,) {final _that = this;
switch (_that) {
case _NetworkingEvent() when $default != null:
return $default(_that.id,_that.title,_that.description,_that.organizerId,_that.organizerName,_that.startDate,_that.endDate,_that.location,_that.virtualMeetingUrl,_that.type,_that.visibility,_that.maxAttendees,_that.currentAttendees,_that.price,_that.currency,_that.tags,_that.industries,_that.coverImageUrl,_that.isExclusive,_that.isVerified,_that.createdAt,_that.updatedAt,_that.agenda,_that.speakers,_that.venueDetails,_that.requirements);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _NetworkingEvent implements NetworkingEvent {
  const _NetworkingEvent({required this.id, required this.title, required this.description, required this.organizerId, required this.organizerName, required this.startDate, required this.endDate, required this.location, this.virtualMeetingUrl, required this.type, required this.visibility, required this.maxAttendees, required this.currentAttendees, required this.price, required this.currency, final  List<String>? tags, final  List<String>? industries, this.coverImageUrl, required this.isExclusive, required this.isVerified, required this.createdAt, this.updatedAt, final  Map<String, dynamic>? agenda, final  List<String>? speakers, this.venueDetails, final  Map<String, dynamic>? requirements}): _tags = tags,_industries = industries,_agenda = agenda,_speakers = speakers,_requirements = requirements;
  factory _NetworkingEvent.fromJson(Map<String, dynamic> json) => _$NetworkingEventFromJson(json);

@override final  String id;
@override final  String title;
@override final  String description;
@override final  String organizerId;
@override final  String organizerName;
@override final  DateTime startDate;
@override final  DateTime endDate;
@override final  String location;
@override final  String? virtualMeetingUrl;
@override final  EventType type;
@override final  EventVisibility visibility;
@override final  int maxAttendees;
@override final  int currentAttendees;
@override final  double price;
@override final  String currency;
 final  List<String>? _tags;
@override List<String>? get tags {
  final value = _tags;
  if (value == null) return null;
  if (_tags is EqualUnmodifiableListView) return _tags;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<String>? _industries;
@override List<String>? get industries {
  final value = _industries;
  if (value == null) return null;
  if (_industries is EqualUnmodifiableListView) return _industries;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override final  String? coverImageUrl;
@override final  bool isExclusive;
@override final  bool isVerified;
@override final  DateTime createdAt;
@override final  DateTime? updatedAt;
 final  Map<String, dynamic>? _agenda;
@override Map<String, dynamic>? get agenda {
  final value = _agenda;
  if (value == null) return null;
  if (_agenda is EqualUnmodifiableMapView) return _agenda;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

 final  List<String>? _speakers;
@override List<String>? get speakers {
  final value = _speakers;
  if (value == null) return null;
  if (_speakers is EqualUnmodifiableListView) return _speakers;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override final  String? venueDetails;
 final  Map<String, dynamic>? _requirements;
@override Map<String, dynamic>? get requirements {
  final value = _requirements;
  if (value == null) return null;
  if (_requirements is EqualUnmodifiableMapView) return _requirements;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of NetworkingEvent
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$NetworkingEventCopyWith<_NetworkingEvent> get copyWith => __$NetworkingEventCopyWithImpl<_NetworkingEvent>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$NetworkingEventToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _NetworkingEvent&&(identical(other.id, id) || other.id == id)&&(identical(other.title, title) || other.title == title)&&(identical(other.description, description) || other.description == description)&&(identical(other.organizerId, organizerId) || other.organizerId == organizerId)&&(identical(other.organizerName, organizerName) || other.organizerName == organizerName)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.location, location) || other.location == location)&&(identical(other.virtualMeetingUrl, virtualMeetingUrl) || other.virtualMeetingUrl == virtualMeetingUrl)&&(identical(other.type, type) || other.type == type)&&(identical(other.visibility, visibility) || other.visibility == visibility)&&(identical(other.maxAttendees, maxAttendees) || other.maxAttendees == maxAttendees)&&(identical(other.currentAttendees, currentAttendees) || other.currentAttendees == currentAttendees)&&(identical(other.price, price) || other.price == price)&&(identical(other.currency, currency) || other.currency == currency)&&const DeepCollectionEquality().equals(other._tags, _tags)&&const DeepCollectionEquality().equals(other._industries, _industries)&&(identical(other.coverImageUrl, coverImageUrl) || other.coverImageUrl == coverImageUrl)&&(identical(other.isExclusive, isExclusive) || other.isExclusive == isExclusive)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&const DeepCollectionEquality().equals(other._agenda, _agenda)&&const DeepCollectionEquality().equals(other._speakers, _speakers)&&(identical(other.venueDetails, venueDetails) || other.venueDetails == venueDetails)&&const DeepCollectionEquality().equals(other._requirements, _requirements));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,title,description,organizerId,organizerName,startDate,endDate,location,virtualMeetingUrl,type,visibility,maxAttendees,currentAttendees,price,currency,const DeepCollectionEquality().hash(_tags),const DeepCollectionEquality().hash(_industries),coverImageUrl,isExclusive,isVerified,createdAt,updatedAt,const DeepCollectionEquality().hash(_agenda),const DeepCollectionEquality().hash(_speakers),venueDetails,const DeepCollectionEquality().hash(_requirements)]);

@override
String toString() {
  return 'NetworkingEvent(id: $id, title: $title, description: $description, organizerId: $organizerId, organizerName: $organizerName, startDate: $startDate, endDate: $endDate, location: $location, virtualMeetingUrl: $virtualMeetingUrl, type: $type, visibility: $visibility, maxAttendees: $maxAttendees, currentAttendees: $currentAttendees, price: $price, currency: $currency, tags: $tags, industries: $industries, coverImageUrl: $coverImageUrl, isExclusive: $isExclusive, isVerified: $isVerified, createdAt: $createdAt, updatedAt: $updatedAt, agenda: $agenda, speakers: $speakers, venueDetails: $venueDetails, requirements: $requirements)';
}


}

/// @nodoc
abstract mixin class _$NetworkingEventCopyWith<$Res> implements $NetworkingEventCopyWith<$Res> {
  factory _$NetworkingEventCopyWith(_NetworkingEvent value, $Res Function(_NetworkingEvent) _then) = __$NetworkingEventCopyWithImpl;
@override @useResult
$Res call({
 String id, String title, String description, String organizerId, String organizerName, DateTime startDate, DateTime endDate, String location, String? virtualMeetingUrl, EventType type, EventVisibility visibility, int maxAttendees, int currentAttendees, double price, String currency, List<String>? tags, List<String>? industries, String? coverImageUrl, bool isExclusive, bool isVerified, DateTime createdAt, DateTime? updatedAt, Map<String, dynamic>? agenda, List<String>? speakers, String? venueDetails, Map<String, dynamic>? requirements
});




}
/// @nodoc
class __$NetworkingEventCopyWithImpl<$Res>
    implements _$NetworkingEventCopyWith<$Res> {
  __$NetworkingEventCopyWithImpl(this._self, this._then);

  final _NetworkingEvent _self;
  final $Res Function(_NetworkingEvent) _then;

/// Create a copy of NetworkingEvent
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? title = null,Object? description = null,Object? organizerId = null,Object? organizerName = null,Object? startDate = null,Object? endDate = null,Object? location = null,Object? virtualMeetingUrl = freezed,Object? type = null,Object? visibility = null,Object? maxAttendees = null,Object? currentAttendees = null,Object? price = null,Object? currency = null,Object? tags = freezed,Object? industries = freezed,Object? coverImageUrl = freezed,Object? isExclusive = null,Object? isVerified = null,Object? createdAt = null,Object? updatedAt = freezed,Object? agenda = freezed,Object? speakers = freezed,Object? venueDetails = freezed,Object? requirements = freezed,}) {
  return _then(_NetworkingEvent(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,organizerId: null == organizerId ? _self.organizerId : organizerId // ignore: cast_nullable_to_non_nullable
as String,organizerName: null == organizerName ? _self.organizerName : organizerName // ignore: cast_nullable_to_non_nullable
as String,startDate: null == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime,endDate: null == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime,location: null == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String,virtualMeetingUrl: freezed == virtualMeetingUrl ? _self.virtualMeetingUrl : virtualMeetingUrl // ignore: cast_nullable_to_non_nullable
as String?,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as EventType,visibility: null == visibility ? _self.visibility : visibility // ignore: cast_nullable_to_non_nullable
as EventVisibility,maxAttendees: null == maxAttendees ? _self.maxAttendees : maxAttendees // ignore: cast_nullable_to_non_nullable
as int,currentAttendees: null == currentAttendees ? _self.currentAttendees : currentAttendees // ignore: cast_nullable_to_non_nullable
as int,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,tags: freezed == tags ? _self._tags : tags // ignore: cast_nullable_to_non_nullable
as List<String>?,industries: freezed == industries ? _self._industries : industries // ignore: cast_nullable_to_non_nullable
as List<String>?,coverImageUrl: freezed == coverImageUrl ? _self.coverImageUrl : coverImageUrl // ignore: cast_nullable_to_non_nullable
as String?,isExclusive: null == isExclusive ? _self.isExclusive : isExclusive // ignore: cast_nullable_to_non_nullable
as bool,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,agenda: freezed == agenda ? _self._agenda : agenda // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,speakers: freezed == speakers ? _self._speakers : speakers // ignore: cast_nullable_to_non_nullable
as List<String>?,venueDetails: freezed == venueDetails ? _self.venueDetails : venueDetails // ignore: cast_nullable_to_non_nullable
as String?,requirements: freezed == requirements ? _self._requirements : requirements // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}


/// @nodoc
mixin _$BusinessCard {

 String get id; String get userId; String get name; String get title; String get company; String? get email; String? get phone; String? get website; String? get linkedinUrl; String? get profilePictureUrl; String? get qrCodeUrl; BusinessCardDesign get design; bool get isDigital; bool get isShared; DateTime get createdAt; DateTime? get lastSharedAt; Map<String, dynamic>? get customFields; List<String>? get tags; String? get notes;
/// Create a copy of BusinessCard
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BusinessCardCopyWith<BusinessCard> get copyWith => _$BusinessCardCopyWithImpl<BusinessCard>(this as BusinessCard, _$identity);

  /// Serializes this BusinessCard to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BusinessCard&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.name, name) || other.name == name)&&(identical(other.title, title) || other.title == title)&&(identical(other.company, company) || other.company == company)&&(identical(other.email, email) || other.email == email)&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.website, website) || other.website == website)&&(identical(other.linkedinUrl, linkedinUrl) || other.linkedinUrl == linkedinUrl)&&(identical(other.profilePictureUrl, profilePictureUrl) || other.profilePictureUrl == profilePictureUrl)&&(identical(other.qrCodeUrl, qrCodeUrl) || other.qrCodeUrl == qrCodeUrl)&&(identical(other.design, design) || other.design == design)&&(identical(other.isDigital, isDigital) || other.isDigital == isDigital)&&(identical(other.isShared, isShared) || other.isShared == isShared)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.lastSharedAt, lastSharedAt) || other.lastSharedAt == lastSharedAt)&&const DeepCollectionEquality().equals(other.customFields, customFields)&&const DeepCollectionEquality().equals(other.tags, tags)&&(identical(other.notes, notes) || other.notes == notes));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,userId,name,title,company,email,phone,website,linkedinUrl,profilePictureUrl,qrCodeUrl,design,isDigital,isShared,createdAt,lastSharedAt,const DeepCollectionEquality().hash(customFields),const DeepCollectionEquality().hash(tags),notes]);

@override
String toString() {
  return 'BusinessCard(id: $id, userId: $userId, name: $name, title: $title, company: $company, email: $email, phone: $phone, website: $website, linkedinUrl: $linkedinUrl, profilePictureUrl: $profilePictureUrl, qrCodeUrl: $qrCodeUrl, design: $design, isDigital: $isDigital, isShared: $isShared, createdAt: $createdAt, lastSharedAt: $lastSharedAt, customFields: $customFields, tags: $tags, notes: $notes)';
}


}

/// @nodoc
abstract mixin class $BusinessCardCopyWith<$Res>  {
  factory $BusinessCardCopyWith(BusinessCard value, $Res Function(BusinessCard) _then) = _$BusinessCardCopyWithImpl;
@useResult
$Res call({
 String id, String userId, String name, String title, String company, String? email, String? phone, String? website, String? linkedinUrl, String? profilePictureUrl, String? qrCodeUrl, BusinessCardDesign design, bool isDigital, bool isShared, DateTime createdAt, DateTime? lastSharedAt, Map<String, dynamic>? customFields, List<String>? tags, String? notes
});




}
/// @nodoc
class _$BusinessCardCopyWithImpl<$Res>
    implements $BusinessCardCopyWith<$Res> {
  _$BusinessCardCopyWithImpl(this._self, this._then);

  final BusinessCard _self;
  final $Res Function(BusinessCard) _then;

/// Create a copy of BusinessCard
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? name = null,Object? title = null,Object? company = null,Object? email = freezed,Object? phone = freezed,Object? website = freezed,Object? linkedinUrl = freezed,Object? profilePictureUrl = freezed,Object? qrCodeUrl = freezed,Object? design = null,Object? isDigital = null,Object? isShared = null,Object? createdAt = null,Object? lastSharedAt = freezed,Object? customFields = freezed,Object? tags = freezed,Object? notes = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,company: null == company ? _self.company : company // ignore: cast_nullable_to_non_nullable
as String,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,phone: freezed == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String?,website: freezed == website ? _self.website : website // ignore: cast_nullable_to_non_nullable
as String?,linkedinUrl: freezed == linkedinUrl ? _self.linkedinUrl : linkedinUrl // ignore: cast_nullable_to_non_nullable
as String?,profilePictureUrl: freezed == profilePictureUrl ? _self.profilePictureUrl : profilePictureUrl // ignore: cast_nullable_to_non_nullable
as String?,qrCodeUrl: freezed == qrCodeUrl ? _self.qrCodeUrl : qrCodeUrl // ignore: cast_nullable_to_non_nullable
as String?,design: null == design ? _self.design : design // ignore: cast_nullable_to_non_nullable
as BusinessCardDesign,isDigital: null == isDigital ? _self.isDigital : isDigital // ignore: cast_nullable_to_non_nullable
as bool,isShared: null == isShared ? _self.isShared : isShared // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,lastSharedAt: freezed == lastSharedAt ? _self.lastSharedAt : lastSharedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,customFields: freezed == customFields ? _self.customFields : customFields // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,tags: freezed == tags ? _self.tags : tags // ignore: cast_nullable_to_non_nullable
as List<String>?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [BusinessCard].
extension BusinessCardPatterns on BusinessCard {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _BusinessCard value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _BusinessCard() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _BusinessCard value)  $default,){
final _that = this;
switch (_that) {
case _BusinessCard():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _BusinessCard value)?  $default,){
final _that = this;
switch (_that) {
case _BusinessCard() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  String name,  String title,  String company,  String? email,  String? phone,  String? website,  String? linkedinUrl,  String? profilePictureUrl,  String? qrCodeUrl,  BusinessCardDesign design,  bool isDigital,  bool isShared,  DateTime createdAt,  DateTime? lastSharedAt,  Map<String, dynamic>? customFields,  List<String>? tags,  String? notes)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _BusinessCard() when $default != null:
return $default(_that.id,_that.userId,_that.name,_that.title,_that.company,_that.email,_that.phone,_that.website,_that.linkedinUrl,_that.profilePictureUrl,_that.qrCodeUrl,_that.design,_that.isDigital,_that.isShared,_that.createdAt,_that.lastSharedAt,_that.customFields,_that.tags,_that.notes);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  String name,  String title,  String company,  String? email,  String? phone,  String? website,  String? linkedinUrl,  String? profilePictureUrl,  String? qrCodeUrl,  BusinessCardDesign design,  bool isDigital,  bool isShared,  DateTime createdAt,  DateTime? lastSharedAt,  Map<String, dynamic>? customFields,  List<String>? tags,  String? notes)  $default,) {final _that = this;
switch (_that) {
case _BusinessCard():
return $default(_that.id,_that.userId,_that.name,_that.title,_that.company,_that.email,_that.phone,_that.website,_that.linkedinUrl,_that.profilePictureUrl,_that.qrCodeUrl,_that.design,_that.isDigital,_that.isShared,_that.createdAt,_that.lastSharedAt,_that.customFields,_that.tags,_that.notes);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  String name,  String title,  String company,  String? email,  String? phone,  String? website,  String? linkedinUrl,  String? profilePictureUrl,  String? qrCodeUrl,  BusinessCardDesign design,  bool isDigital,  bool isShared,  DateTime createdAt,  DateTime? lastSharedAt,  Map<String, dynamic>? customFields,  List<String>? tags,  String? notes)?  $default,) {final _that = this;
switch (_that) {
case _BusinessCard() when $default != null:
return $default(_that.id,_that.userId,_that.name,_that.title,_that.company,_that.email,_that.phone,_that.website,_that.linkedinUrl,_that.profilePictureUrl,_that.qrCodeUrl,_that.design,_that.isDigital,_that.isShared,_that.createdAt,_that.lastSharedAt,_that.customFields,_that.tags,_that.notes);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _BusinessCard implements BusinessCard {
  const _BusinessCard({required this.id, required this.userId, required this.name, required this.title, required this.company, this.email, this.phone, this.website, this.linkedinUrl, this.profilePictureUrl, this.qrCodeUrl, required this.design, required this.isDigital, required this.isShared, required this.createdAt, this.lastSharedAt, final  Map<String, dynamic>? customFields, final  List<String>? tags, this.notes}): _customFields = customFields,_tags = tags;
  factory _BusinessCard.fromJson(Map<String, dynamic> json) => _$BusinessCardFromJson(json);

@override final  String id;
@override final  String userId;
@override final  String name;
@override final  String title;
@override final  String company;
@override final  String? email;
@override final  String? phone;
@override final  String? website;
@override final  String? linkedinUrl;
@override final  String? profilePictureUrl;
@override final  String? qrCodeUrl;
@override final  BusinessCardDesign design;
@override final  bool isDigital;
@override final  bool isShared;
@override final  DateTime createdAt;
@override final  DateTime? lastSharedAt;
 final  Map<String, dynamic>? _customFields;
@override Map<String, dynamic>? get customFields {
  final value = _customFields;
  if (value == null) return null;
  if (_customFields is EqualUnmodifiableMapView) return _customFields;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

 final  List<String>? _tags;
@override List<String>? get tags {
  final value = _tags;
  if (value == null) return null;
  if (_tags is EqualUnmodifiableListView) return _tags;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override final  String? notes;

/// Create a copy of BusinessCard
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BusinessCardCopyWith<_BusinessCard> get copyWith => __$BusinessCardCopyWithImpl<_BusinessCard>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BusinessCardToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BusinessCard&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.name, name) || other.name == name)&&(identical(other.title, title) || other.title == title)&&(identical(other.company, company) || other.company == company)&&(identical(other.email, email) || other.email == email)&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.website, website) || other.website == website)&&(identical(other.linkedinUrl, linkedinUrl) || other.linkedinUrl == linkedinUrl)&&(identical(other.profilePictureUrl, profilePictureUrl) || other.profilePictureUrl == profilePictureUrl)&&(identical(other.qrCodeUrl, qrCodeUrl) || other.qrCodeUrl == qrCodeUrl)&&(identical(other.design, design) || other.design == design)&&(identical(other.isDigital, isDigital) || other.isDigital == isDigital)&&(identical(other.isShared, isShared) || other.isShared == isShared)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.lastSharedAt, lastSharedAt) || other.lastSharedAt == lastSharedAt)&&const DeepCollectionEquality().equals(other._customFields, _customFields)&&const DeepCollectionEquality().equals(other._tags, _tags)&&(identical(other.notes, notes) || other.notes == notes));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,userId,name,title,company,email,phone,website,linkedinUrl,profilePictureUrl,qrCodeUrl,design,isDigital,isShared,createdAt,lastSharedAt,const DeepCollectionEquality().hash(_customFields),const DeepCollectionEquality().hash(_tags),notes]);

@override
String toString() {
  return 'BusinessCard(id: $id, userId: $userId, name: $name, title: $title, company: $company, email: $email, phone: $phone, website: $website, linkedinUrl: $linkedinUrl, profilePictureUrl: $profilePictureUrl, qrCodeUrl: $qrCodeUrl, design: $design, isDigital: $isDigital, isShared: $isShared, createdAt: $createdAt, lastSharedAt: $lastSharedAt, customFields: $customFields, tags: $tags, notes: $notes)';
}


}

/// @nodoc
abstract mixin class _$BusinessCardCopyWith<$Res> implements $BusinessCardCopyWith<$Res> {
  factory _$BusinessCardCopyWith(_BusinessCard value, $Res Function(_BusinessCard) _then) = __$BusinessCardCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, String name, String title, String company, String? email, String? phone, String? website, String? linkedinUrl, String? profilePictureUrl, String? qrCodeUrl, BusinessCardDesign design, bool isDigital, bool isShared, DateTime createdAt, DateTime? lastSharedAt, Map<String, dynamic>? customFields, List<String>? tags, String? notes
});




}
/// @nodoc
class __$BusinessCardCopyWithImpl<$Res>
    implements _$BusinessCardCopyWith<$Res> {
  __$BusinessCardCopyWithImpl(this._self, this._then);

  final _BusinessCard _self;
  final $Res Function(_BusinessCard) _then;

/// Create a copy of BusinessCard
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? name = null,Object? title = null,Object? company = null,Object? email = freezed,Object? phone = freezed,Object? website = freezed,Object? linkedinUrl = freezed,Object? profilePictureUrl = freezed,Object? qrCodeUrl = freezed,Object? design = null,Object? isDigital = null,Object? isShared = null,Object? createdAt = null,Object? lastSharedAt = freezed,Object? customFields = freezed,Object? tags = freezed,Object? notes = freezed,}) {
  return _then(_BusinessCard(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,company: null == company ? _self.company : company // ignore: cast_nullable_to_non_nullable
as String,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,phone: freezed == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String?,website: freezed == website ? _self.website : website // ignore: cast_nullable_to_non_nullable
as String?,linkedinUrl: freezed == linkedinUrl ? _self.linkedinUrl : linkedinUrl // ignore: cast_nullable_to_non_nullable
as String?,profilePictureUrl: freezed == profilePictureUrl ? _self.profilePictureUrl : profilePictureUrl // ignore: cast_nullable_to_non_nullable
as String?,qrCodeUrl: freezed == qrCodeUrl ? _self.qrCodeUrl : qrCodeUrl // ignore: cast_nullable_to_non_nullable
as String?,design: null == design ? _self.design : design // ignore: cast_nullable_to_non_nullable
as BusinessCardDesign,isDigital: null == isDigital ? _self.isDigital : isDigital // ignore: cast_nullable_to_non_nullable
as bool,isShared: null == isShared ? _self.isShared : isShared // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,lastSharedAt: freezed == lastSharedAt ? _self.lastSharedAt : lastSharedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,customFields: freezed == customFields ? _self._customFields : customFields // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,tags: freezed == tags ? _self._tags : tags // ignore: cast_nullable_to_non_nullable
as List<String>?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$NetworkingRecommendation {

 String get id; String get userId; String get recommendedUserId; String get reason; double get score; RecommendationType get type; DateTime get createdAt; Map<String, dynamic>? get metadata;
/// Create a copy of NetworkingRecommendation
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$NetworkingRecommendationCopyWith<NetworkingRecommendation> get copyWith => _$NetworkingRecommendationCopyWithImpl<NetworkingRecommendation>(this as NetworkingRecommendation, _$identity);

  /// Serializes this NetworkingRecommendation to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is NetworkingRecommendation&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.recommendedUserId, recommendedUserId) || other.recommendedUserId == recommendedUserId)&&(identical(other.reason, reason) || other.reason == reason)&&(identical(other.score, score) || other.score == score)&&(identical(other.type, type) || other.type == type)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,recommendedUserId,reason,score,type,createdAt,const DeepCollectionEquality().hash(metadata));

@override
String toString() {
  return 'NetworkingRecommendation(id: $id, userId: $userId, recommendedUserId: $recommendedUserId, reason: $reason, score: $score, type: $type, createdAt: $createdAt, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $NetworkingRecommendationCopyWith<$Res>  {
  factory $NetworkingRecommendationCopyWith(NetworkingRecommendation value, $Res Function(NetworkingRecommendation) _then) = _$NetworkingRecommendationCopyWithImpl;
@useResult
$Res call({
 String id, String userId, String recommendedUserId, String reason, double score, RecommendationType type, DateTime createdAt, Map<String, dynamic>? metadata
});




}
/// @nodoc
class _$NetworkingRecommendationCopyWithImpl<$Res>
    implements $NetworkingRecommendationCopyWith<$Res> {
  _$NetworkingRecommendationCopyWithImpl(this._self, this._then);

  final NetworkingRecommendation _self;
  final $Res Function(NetworkingRecommendation) _then;

/// Create a copy of NetworkingRecommendation
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? recommendedUserId = null,Object? reason = null,Object? score = null,Object? type = null,Object? createdAt = null,Object? metadata = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,recommendedUserId: null == recommendedUserId ? _self.recommendedUserId : recommendedUserId // ignore: cast_nullable_to_non_nullable
as String,reason: null == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String,score: null == score ? _self.score : score // ignore: cast_nullable_to_non_nullable
as double,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as RecommendationType,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [NetworkingRecommendation].
extension NetworkingRecommendationPatterns on NetworkingRecommendation {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _NetworkingRecommendation value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _NetworkingRecommendation() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _NetworkingRecommendation value)  $default,){
final _that = this;
switch (_that) {
case _NetworkingRecommendation():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _NetworkingRecommendation value)?  $default,){
final _that = this;
switch (_that) {
case _NetworkingRecommendation() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  String recommendedUserId,  String reason,  double score,  RecommendationType type,  DateTime createdAt,  Map<String, dynamic>? metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _NetworkingRecommendation() when $default != null:
return $default(_that.id,_that.userId,_that.recommendedUserId,_that.reason,_that.score,_that.type,_that.createdAt,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  String recommendedUserId,  String reason,  double score,  RecommendationType type,  DateTime createdAt,  Map<String, dynamic>? metadata)  $default,) {final _that = this;
switch (_that) {
case _NetworkingRecommendation():
return $default(_that.id,_that.userId,_that.recommendedUserId,_that.reason,_that.score,_that.type,_that.createdAt,_that.metadata);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  String recommendedUserId,  String reason,  double score,  RecommendationType type,  DateTime createdAt,  Map<String, dynamic>? metadata)?  $default,) {final _that = this;
switch (_that) {
case _NetworkingRecommendation() when $default != null:
return $default(_that.id,_that.userId,_that.recommendedUserId,_that.reason,_that.score,_that.type,_that.createdAt,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _NetworkingRecommendation implements NetworkingRecommendation {
  const _NetworkingRecommendation({required this.id, required this.userId, required this.recommendedUserId, required this.reason, required this.score, required this.type, required this.createdAt, final  Map<String, dynamic>? metadata}): _metadata = metadata;
  factory _NetworkingRecommendation.fromJson(Map<String, dynamic> json) => _$NetworkingRecommendationFromJson(json);

@override final  String id;
@override final  String userId;
@override final  String recommendedUserId;
@override final  String reason;
@override final  double score;
@override final  RecommendationType type;
@override final  DateTime createdAt;
 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of NetworkingRecommendation
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$NetworkingRecommendationCopyWith<_NetworkingRecommendation> get copyWith => __$NetworkingRecommendationCopyWithImpl<_NetworkingRecommendation>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$NetworkingRecommendationToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _NetworkingRecommendation&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.recommendedUserId, recommendedUserId) || other.recommendedUserId == recommendedUserId)&&(identical(other.reason, reason) || other.reason == reason)&&(identical(other.score, score) || other.score == score)&&(identical(other.type, type) || other.type == type)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,recommendedUserId,reason,score,type,createdAt,const DeepCollectionEquality().hash(_metadata));

@override
String toString() {
  return 'NetworkingRecommendation(id: $id, userId: $userId, recommendedUserId: $recommendedUserId, reason: $reason, score: $score, type: $type, createdAt: $createdAt, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$NetworkingRecommendationCopyWith<$Res> implements $NetworkingRecommendationCopyWith<$Res> {
  factory _$NetworkingRecommendationCopyWith(_NetworkingRecommendation value, $Res Function(_NetworkingRecommendation) _then) = __$NetworkingRecommendationCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, String recommendedUserId, String reason, double score, RecommendationType type, DateTime createdAt, Map<String, dynamic>? metadata
});




}
/// @nodoc
class __$NetworkingRecommendationCopyWithImpl<$Res>
    implements _$NetworkingRecommendationCopyWith<$Res> {
  __$NetworkingRecommendationCopyWithImpl(this._self, this._then);

  final _NetworkingRecommendation _self;
  final $Res Function(_NetworkingRecommendation) _then;

/// Create a copy of NetworkingRecommendation
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? recommendedUserId = null,Object? reason = null,Object? score = null,Object? type = null,Object? createdAt = null,Object? metadata = freezed,}) {
  return _then(_NetworkingRecommendation(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,recommendedUserId: null == recommendedUserId ? _self.recommendedUserId : recommendedUserId // ignore: cast_nullable_to_non_nullable
as String,reason: null == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String,score: null == score ? _self.score : score // ignore: cast_nullable_to_non_nullable
as double,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as RecommendationType,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}


/// @nodoc
mixin _$NetworkingMessage {

 String get id; String get senderId; String get receiverId; String get content; MessageType get type; DateTime get timestamp; bool get isRead; String? get attachmentUrl; String? get attachmentType; Map<String, dynamic>? get metadata;
/// Create a copy of NetworkingMessage
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$NetworkingMessageCopyWith<NetworkingMessage> get copyWith => _$NetworkingMessageCopyWithImpl<NetworkingMessage>(this as NetworkingMessage, _$identity);

  /// Serializes this NetworkingMessage to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is NetworkingMessage&&(identical(other.id, id) || other.id == id)&&(identical(other.senderId, senderId) || other.senderId == senderId)&&(identical(other.receiverId, receiverId) || other.receiverId == receiverId)&&(identical(other.content, content) || other.content == content)&&(identical(other.type, type) || other.type == type)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.isRead, isRead) || other.isRead == isRead)&&(identical(other.attachmentUrl, attachmentUrl) || other.attachmentUrl == attachmentUrl)&&(identical(other.attachmentType, attachmentType) || other.attachmentType == attachmentType)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,senderId,receiverId,content,type,timestamp,isRead,attachmentUrl,attachmentType,const DeepCollectionEquality().hash(metadata));

@override
String toString() {
  return 'NetworkingMessage(id: $id, senderId: $senderId, receiverId: $receiverId, content: $content, type: $type, timestamp: $timestamp, isRead: $isRead, attachmentUrl: $attachmentUrl, attachmentType: $attachmentType, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $NetworkingMessageCopyWith<$Res>  {
  factory $NetworkingMessageCopyWith(NetworkingMessage value, $Res Function(NetworkingMessage) _then) = _$NetworkingMessageCopyWithImpl;
@useResult
$Res call({
 String id, String senderId, String receiverId, String content, MessageType type, DateTime timestamp, bool isRead, String? attachmentUrl, String? attachmentType, Map<String, dynamic>? metadata
});




}
/// @nodoc
class _$NetworkingMessageCopyWithImpl<$Res>
    implements $NetworkingMessageCopyWith<$Res> {
  _$NetworkingMessageCopyWithImpl(this._self, this._then);

  final NetworkingMessage _self;
  final $Res Function(NetworkingMessage) _then;

/// Create a copy of NetworkingMessage
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? senderId = null,Object? receiverId = null,Object? content = null,Object? type = null,Object? timestamp = null,Object? isRead = null,Object? attachmentUrl = freezed,Object? attachmentType = freezed,Object? metadata = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,senderId: null == senderId ? _self.senderId : senderId // ignore: cast_nullable_to_non_nullable
as String,receiverId: null == receiverId ? _self.receiverId : receiverId // ignore: cast_nullable_to_non_nullable
as String,content: null == content ? _self.content : content // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as MessageType,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,isRead: null == isRead ? _self.isRead : isRead // ignore: cast_nullable_to_non_nullable
as bool,attachmentUrl: freezed == attachmentUrl ? _self.attachmentUrl : attachmentUrl // ignore: cast_nullable_to_non_nullable
as String?,attachmentType: freezed == attachmentType ? _self.attachmentType : attachmentType // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [NetworkingMessage].
extension NetworkingMessagePatterns on NetworkingMessage {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _NetworkingMessage value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _NetworkingMessage() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _NetworkingMessage value)  $default,){
final _that = this;
switch (_that) {
case _NetworkingMessage():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _NetworkingMessage value)?  $default,){
final _that = this;
switch (_that) {
case _NetworkingMessage() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String senderId,  String receiverId,  String content,  MessageType type,  DateTime timestamp,  bool isRead,  String? attachmentUrl,  String? attachmentType,  Map<String, dynamic>? metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _NetworkingMessage() when $default != null:
return $default(_that.id,_that.senderId,_that.receiverId,_that.content,_that.type,_that.timestamp,_that.isRead,_that.attachmentUrl,_that.attachmentType,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String senderId,  String receiverId,  String content,  MessageType type,  DateTime timestamp,  bool isRead,  String? attachmentUrl,  String? attachmentType,  Map<String, dynamic>? metadata)  $default,) {final _that = this;
switch (_that) {
case _NetworkingMessage():
return $default(_that.id,_that.senderId,_that.receiverId,_that.content,_that.type,_that.timestamp,_that.isRead,_that.attachmentUrl,_that.attachmentType,_that.metadata);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String senderId,  String receiverId,  String content,  MessageType type,  DateTime timestamp,  bool isRead,  String? attachmentUrl,  String? attachmentType,  Map<String, dynamic>? metadata)?  $default,) {final _that = this;
switch (_that) {
case _NetworkingMessage() when $default != null:
return $default(_that.id,_that.senderId,_that.receiverId,_that.content,_that.type,_that.timestamp,_that.isRead,_that.attachmentUrl,_that.attachmentType,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _NetworkingMessage implements NetworkingMessage {
  const _NetworkingMessage({required this.id, required this.senderId, required this.receiverId, required this.content, required this.type, required this.timestamp, required this.isRead, this.attachmentUrl, this.attachmentType, final  Map<String, dynamic>? metadata}): _metadata = metadata;
  factory _NetworkingMessage.fromJson(Map<String, dynamic> json) => _$NetworkingMessageFromJson(json);

@override final  String id;
@override final  String senderId;
@override final  String receiverId;
@override final  String content;
@override final  MessageType type;
@override final  DateTime timestamp;
@override final  bool isRead;
@override final  String? attachmentUrl;
@override final  String? attachmentType;
 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of NetworkingMessage
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$NetworkingMessageCopyWith<_NetworkingMessage> get copyWith => __$NetworkingMessageCopyWithImpl<_NetworkingMessage>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$NetworkingMessageToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _NetworkingMessage&&(identical(other.id, id) || other.id == id)&&(identical(other.senderId, senderId) || other.senderId == senderId)&&(identical(other.receiverId, receiverId) || other.receiverId == receiverId)&&(identical(other.content, content) || other.content == content)&&(identical(other.type, type) || other.type == type)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.isRead, isRead) || other.isRead == isRead)&&(identical(other.attachmentUrl, attachmentUrl) || other.attachmentUrl == attachmentUrl)&&(identical(other.attachmentType, attachmentType) || other.attachmentType == attachmentType)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,senderId,receiverId,content,type,timestamp,isRead,attachmentUrl,attachmentType,const DeepCollectionEquality().hash(_metadata));

@override
String toString() {
  return 'NetworkingMessage(id: $id, senderId: $senderId, receiverId: $receiverId, content: $content, type: $type, timestamp: $timestamp, isRead: $isRead, attachmentUrl: $attachmentUrl, attachmentType: $attachmentType, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$NetworkingMessageCopyWith<$Res> implements $NetworkingMessageCopyWith<$Res> {
  factory _$NetworkingMessageCopyWith(_NetworkingMessage value, $Res Function(_NetworkingMessage) _then) = __$NetworkingMessageCopyWithImpl;
@override @useResult
$Res call({
 String id, String senderId, String receiverId, String content, MessageType type, DateTime timestamp, bool isRead, String? attachmentUrl, String? attachmentType, Map<String, dynamic>? metadata
});




}
/// @nodoc
class __$NetworkingMessageCopyWithImpl<$Res>
    implements _$NetworkingMessageCopyWith<$Res> {
  __$NetworkingMessageCopyWithImpl(this._self, this._then);

  final _NetworkingMessage _self;
  final $Res Function(_NetworkingMessage) _then;

/// Create a copy of NetworkingMessage
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? senderId = null,Object? receiverId = null,Object? content = null,Object? type = null,Object? timestamp = null,Object? isRead = null,Object? attachmentUrl = freezed,Object? attachmentType = freezed,Object? metadata = freezed,}) {
  return _then(_NetworkingMessage(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,senderId: null == senderId ? _self.senderId : senderId // ignore: cast_nullable_to_non_nullable
as String,receiverId: null == receiverId ? _self.receiverId : receiverId // ignore: cast_nullable_to_non_nullable
as String,content: null == content ? _self.content : content // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as MessageType,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,isRead: null == isRead ? _self.isRead : isRead // ignore: cast_nullable_to_non_nullable
as bool,attachmentUrl: freezed == attachmentUrl ? _self.attachmentUrl : attachmentUrl // ignore: cast_nullable_to_non_nullable
as String?,attachmentType: freezed == attachmentType ? _self.attachmentType : attachmentType // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}

// dart format on
