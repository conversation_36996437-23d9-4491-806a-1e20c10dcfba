import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../providers/analytics_provider.dart';
import '../services/analytics_service.dart';
import '../models/analytics_model.dart';

class AnalyticsDashboardScreen extends ConsumerWidget {
  const AnalyticsDashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsAsync = ref.watch(analyticsProvider);
    final selectedPeriod = ref.watch(analyticsPeriodFilterProvider);

    return Scaffold(
      appBar: AppBar(title: const Text('Analytics Dashboard'), elevation: 0),
      body: RefreshIndicator(
        onRefresh: () => ref.read(analyticsProvider.notifier).refresh(),
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            _buildFilterChips(context, ref, selectedPeriod),
            const SizedBox(height: 20),
            analyticsAsync.when(
              data: (data) => _buildDashboardGrid(context, data),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (err, stack) =>
                  Center(child: Text('Failed to load analytics: $err')),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterChips(
    BuildContext context,
    WidgetRef ref,
    AnalyticsPeriod selectedPeriod,
  ) {
    return SizedBox(
      height: 40,
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: AnalyticsPeriod.values.map((period) {
          final isSelected = selectedPeriod == period;
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4.0),
            child: ChoiceChip(
              label: Text(toBeginningOfSentenceCase(period.name)!),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  ref
                      .read(analyticsPeriodFilterProvider.notifier)
                      .setPeriod(period);
                }
              },
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildDashboardGrid(BuildContext context, AnalyticsData data) {
    final numberFormatter = NumberFormat.compact();
    final currencyFormatter = NumberFormat.currency(
      locale: 'en_US',
      symbol: '\$',
    );

    return Column(
      children: [
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.2,
          children: [
            _AnalyticsCard(
              title: 'Profile Visits',
              value: numberFormatter.format(data.profileVisits),
              icon: FontAwesomeIcons.eye,
              color: Colors.blue,
            ),
            _AnalyticsCard(
              title: 'Total Sales',
              value: currencyFormatter.format(data.totalSales),
              icon: FontAwesomeIcons.dollarSign,
              color: Colors.green,
            ),
            _AnalyticsCard(
              title: 'Posts Created',
              value: numberFormatter.format(data.postsCount),
              icon: FontAwesomeIcons.solidFileLines,
              color: Colors.orange,
            ),
            _AnalyticsCard(
              title: 'Events Created',
              value: numberFormatter.format(data.eventsCreated),
              icon: FontAwesomeIcons.calendarCheck,
              color: Colors.purple,
            ),
          ],
        ),
        const SizedBox(height: 24),
        _FollowerChartCard(followerData: data.followerGains),
      ],
    );
  }
}

class _AnalyticsCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;

  const _AnalyticsCard({
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Icon(icon, size: 28, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            Text(title, style: Theme.of(context).textTheme.bodyMedium),
          ],
        ),
      ),
    );
  }
}

class _FollowerChartCard extends StatelessWidget {
  final List<FollowerDataPoint> followerData;
  const _FollowerChartCard({required this.followerData});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.fromLTRB(16, 16, 16, 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Follower Growth',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            SizedBox(
              height: 150,
              child: LineChart(
                LineChartData(
                  gridData: const FlGridData(show: false),
                  titlesData: const FlTitlesData(show: false),
                  borderData: FlBorderData(show: false),
                  lineBarsData: [
                    LineChartBarData(
                      spots: followerData
                          .asMap()
                          .entries
                          .map(
                            (e) => FlSpot(
                              e.key.toDouble(),
                              e.value.count.toDouble(),
                            ),
                          )
                          .toList(),
                      isCurved: true,
                      color: Theme.of(context).primaryColor,
                      barWidth: 4,
                      isStrokeCapRound: true,
                      dotData: const FlDotData(show: false),
                      belowBarData: BarAreaData(
                        show: true,
                        color: Theme.of(context).primaryColor.withAlpha(77),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
