import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/analytics_model.dart';
import '../services/analytics_service.dart';
import 'package:billionaires_social/features/auth/providers/auth_provider.dart';
import 'package:billionaires_social/features/profile/providers/profile_provider.dart';

part 'analytics_provider.g.dart';

@riverpod
AnalyticsService analyticsService(Ref ref) {
  return AnalyticsService();
}

@riverpod
class AnalyticsPeriodFilter extends _$AnalyticsPeriodFilter {
  @override
  AnalyticsPeriod build() => AnalyticsPeriod.week;

  void setPeriod(AnalyticsPeriod newPeriod) {
    state = newPeriod;
  }
}

@riverpod
class Analytics extends _$Analytics {
  @override
  Future<AnalyticsData> build() {
    return _fetchAnalytics();
  }

  Future<AnalyticsData> _fetchAnalytics() async {
    final analyticsService = ref.watch(analyticsServiceProvider);
    final period = ref.watch(analyticsPeriodFilterProvider);

    // Get current user from auth provider
    final authState = ref.watch(authProvider);

    return authState.when(
      data: (user) async {
        if (user == null) {
          // Return empty analytics for unauthenticated users
          return AnalyticsData.empty();
        }

        // Get user profile to determine business status
        final userProfileState = ref.watch(userProfileProvider);

        return userProfileState.when(
          data: (userProfile) async {
            final userId = user.uid;
            final isBusiness = userProfile.isBusinessAccount;

            return analyticsService.getAnalytics(
              userId: userId,
              period: period,
              isBusiness: isBusiness,
            );
          },
          loading: () async {
            // Use basic user info while profile loads
            final userId = user.uid;
            const isBusiness = false; // Default to false while loading

            return analyticsService.getAnalytics(
              userId: userId,
              period: period,
              isBusiness: isBusiness,
            );
          },
          error: (error, stack) async {
            // Fallback to basic analytics on profile error
            final userId = user.uid;
            const isBusiness = false; // Default to false on error

            return analyticsService.getAnalytics(
              userId: userId,
              period: period,
              isBusiness: isBusiness,
            );
          },
        );
      },
      loading: () async {
        // Return empty analytics while auth loads
        return AnalyticsData.empty();
      },
      error: (error, stack) async {
        // Return empty analytics on auth error
        return AnalyticsData.empty();
      },
    );
  }

  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => _fetchAnalytics());
  }
}
