// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'analytics_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$analyticsServiceHash() => r'a78e9020e79b5e99632cc4cee7e5f7156c672acd';

/// See also [analyticsService].
@ProviderFor(analyticsService)
final analyticsServiceProvider = AutoDisposeProvider<AnalyticsService>.internal(
  analyticsService,
  name: r'analyticsServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$analyticsServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AnalyticsServiceRef = AutoDisposeProviderRef<AnalyticsService>;
String _$analyticsPeriodFilterHash() =>
    r'eec04b711fea95736742caa9377dd36258d40a34';

/// See also [AnalyticsPeriodFilter].
@ProviderFor(AnalyticsPeriodFilter)
final analyticsPeriodFilterProvider =
    AutoDisposeNotifierProvider<
      AnalyticsPeriodFilter,
      AnalyticsPeriod
    >.internal(
      AnalyticsPeriodFilter.new,
      name: r'analyticsPeriodFilterProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$analyticsPeriodFilterHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$AnalyticsPeriodFilter = AutoDisposeNotifier<AnalyticsPeriod>;
String _$analyticsHash() => r'5dcc07eafaf81058bac99aca11eb1450b68c45a6';

/// See also [Analytics].
@ProviderFor(Analytics)
final analyticsProvider =
    AutoDisposeAsyncNotifierProvider<Analytics, AnalyticsData>.internal(
      Analytics.new,
      name: r'analyticsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$analyticsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$Analytics = AutoDisposeAsyncNotifier<AnalyticsData>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
