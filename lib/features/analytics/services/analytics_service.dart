import 'package:faker/faker.dart';
import '../models/analytics_model.dart';

enum AnalyticsPeriod { today, week, month }

class AnalyticsService {
  final Faker _faker = Faker();

  Future<AnalyticsData> getAnalytics({
    required String userId,
    required AnalyticsPeriod period,
    bool isBusiness = false,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 600));

    final random = _faker.randomGenerator;
    final now = DateTime.now();

    // The multiplier will adjust data based on the period
    double multiplier;
    int days;
    switch (period) {
      case AnalyticsPeriod.today:
        multiplier = 1.2;
        days = 1;
        break;
      case AnalyticsPeriod.week:
        multiplier = 7.5;
        days = 7;
        break;
      case AnalyticsPeriod.month:
        multiplier = 30;
        days = 30;
        break;
    }

    // Generate follower data for a chart
    List<FollowerDataPoint> followerGains = List.generate(days, (i) {
      return FollowerDataPoint(
        date: now.subtract(Duration(days: days - 1 - i)),
        count: random.integer(100, min: 5),
      );
    });

    return AnalyticsData(
      profileVisits: (random.integer(500, min: 50) * multiplier).round(),
      postsCount: isBusiness
          ? (random.integer(20, min: 2) * multiplier).round()
          : (random.integer(5, min: 1) * multiplier).round(),
      productsListed: isBusiness
          ? (random.integer(15, min: 1) * multiplier).round()
          : 0,
      eventsCreated: isBusiness
          ? (random.integer(5, min: 0) * multiplier).round()
          : 0,
      totalSales: isBusiness
          ? (random.decimal(scale: 1000, min: 100) * multiplier)
          : 0.0,
      followerGains: followerGains,
    );
  }
}
