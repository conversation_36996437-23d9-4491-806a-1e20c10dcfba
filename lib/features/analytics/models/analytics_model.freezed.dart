// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'analytics_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AnalyticsData {

 int get profileVisits; int get postsCount; int get productsListed; int get eventsCreated; double get totalSales; List<FollowerDataPoint> get followerGains;
/// Create a copy of AnalyticsData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AnalyticsDataCopyWith<AnalyticsData> get copyWith => _$AnalyticsDataCopyWithImpl<AnalyticsData>(this as AnalyticsData, _$identity);

  /// Serializes this AnalyticsData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AnalyticsData&&(identical(other.profileVisits, profileVisits) || other.profileVisits == profileVisits)&&(identical(other.postsCount, postsCount) || other.postsCount == postsCount)&&(identical(other.productsListed, productsListed) || other.productsListed == productsListed)&&(identical(other.eventsCreated, eventsCreated) || other.eventsCreated == eventsCreated)&&(identical(other.totalSales, totalSales) || other.totalSales == totalSales)&&const DeepCollectionEquality().equals(other.followerGains, followerGains));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,profileVisits,postsCount,productsListed,eventsCreated,totalSales,const DeepCollectionEquality().hash(followerGains));

@override
String toString() {
  return 'AnalyticsData(profileVisits: $profileVisits, postsCount: $postsCount, productsListed: $productsListed, eventsCreated: $eventsCreated, totalSales: $totalSales, followerGains: $followerGains)';
}


}

/// @nodoc
abstract mixin class $AnalyticsDataCopyWith<$Res>  {
  factory $AnalyticsDataCopyWith(AnalyticsData value, $Res Function(AnalyticsData) _then) = _$AnalyticsDataCopyWithImpl;
@useResult
$Res call({
 int profileVisits, int postsCount, int productsListed, int eventsCreated, double totalSales, List<FollowerDataPoint> followerGains
});




}
/// @nodoc
class _$AnalyticsDataCopyWithImpl<$Res>
    implements $AnalyticsDataCopyWith<$Res> {
  _$AnalyticsDataCopyWithImpl(this._self, this._then);

  final AnalyticsData _self;
  final $Res Function(AnalyticsData) _then;

/// Create a copy of AnalyticsData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? profileVisits = null,Object? postsCount = null,Object? productsListed = null,Object? eventsCreated = null,Object? totalSales = null,Object? followerGains = null,}) {
  return _then(_self.copyWith(
profileVisits: null == profileVisits ? _self.profileVisits : profileVisits // ignore: cast_nullable_to_non_nullable
as int,postsCount: null == postsCount ? _self.postsCount : postsCount // ignore: cast_nullable_to_non_nullable
as int,productsListed: null == productsListed ? _self.productsListed : productsListed // ignore: cast_nullable_to_non_nullable
as int,eventsCreated: null == eventsCreated ? _self.eventsCreated : eventsCreated // ignore: cast_nullable_to_non_nullable
as int,totalSales: null == totalSales ? _self.totalSales : totalSales // ignore: cast_nullable_to_non_nullable
as double,followerGains: null == followerGains ? _self.followerGains : followerGains // ignore: cast_nullable_to_non_nullable
as List<FollowerDataPoint>,
  ));
}

}


/// Adds pattern-matching-related methods to [AnalyticsData].
extension AnalyticsDataPatterns on AnalyticsData {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _AnalyticsData value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _AnalyticsData() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _AnalyticsData value)  $default,){
final _that = this;
switch (_that) {
case _AnalyticsData():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _AnalyticsData value)?  $default,){
final _that = this;
switch (_that) {
case _AnalyticsData() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int profileVisits,  int postsCount,  int productsListed,  int eventsCreated,  double totalSales,  List<FollowerDataPoint> followerGains)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _AnalyticsData() when $default != null:
return $default(_that.profileVisits,_that.postsCount,_that.productsListed,_that.eventsCreated,_that.totalSales,_that.followerGains);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int profileVisits,  int postsCount,  int productsListed,  int eventsCreated,  double totalSales,  List<FollowerDataPoint> followerGains)  $default,) {final _that = this;
switch (_that) {
case _AnalyticsData():
return $default(_that.profileVisits,_that.postsCount,_that.productsListed,_that.eventsCreated,_that.totalSales,_that.followerGains);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int profileVisits,  int postsCount,  int productsListed,  int eventsCreated,  double totalSales,  List<FollowerDataPoint> followerGains)?  $default,) {final _that = this;
switch (_that) {
case _AnalyticsData() when $default != null:
return $default(_that.profileVisits,_that.postsCount,_that.productsListed,_that.eventsCreated,_that.totalSales,_that.followerGains);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _AnalyticsData implements AnalyticsData {
  const _AnalyticsData({required this.profileVisits, required this.postsCount, required this.productsListed, required this.eventsCreated, required this.totalSales, required final  List<FollowerDataPoint> followerGains}): _followerGains = followerGains;
  factory _AnalyticsData.fromJson(Map<String, dynamic> json) => _$AnalyticsDataFromJson(json);

@override final  int profileVisits;
@override final  int postsCount;
@override final  int productsListed;
@override final  int eventsCreated;
@override final  double totalSales;
 final  List<FollowerDataPoint> _followerGains;
@override List<FollowerDataPoint> get followerGains {
  if (_followerGains is EqualUnmodifiableListView) return _followerGains;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_followerGains);
}


/// Create a copy of AnalyticsData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AnalyticsDataCopyWith<_AnalyticsData> get copyWith => __$AnalyticsDataCopyWithImpl<_AnalyticsData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AnalyticsDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AnalyticsData&&(identical(other.profileVisits, profileVisits) || other.profileVisits == profileVisits)&&(identical(other.postsCount, postsCount) || other.postsCount == postsCount)&&(identical(other.productsListed, productsListed) || other.productsListed == productsListed)&&(identical(other.eventsCreated, eventsCreated) || other.eventsCreated == eventsCreated)&&(identical(other.totalSales, totalSales) || other.totalSales == totalSales)&&const DeepCollectionEquality().equals(other._followerGains, _followerGains));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,profileVisits,postsCount,productsListed,eventsCreated,totalSales,const DeepCollectionEquality().hash(_followerGains));

@override
String toString() {
  return 'AnalyticsData(profileVisits: $profileVisits, postsCount: $postsCount, productsListed: $productsListed, eventsCreated: $eventsCreated, totalSales: $totalSales, followerGains: $followerGains)';
}


}

/// @nodoc
abstract mixin class _$AnalyticsDataCopyWith<$Res> implements $AnalyticsDataCopyWith<$Res> {
  factory _$AnalyticsDataCopyWith(_AnalyticsData value, $Res Function(_AnalyticsData) _then) = __$AnalyticsDataCopyWithImpl;
@override @useResult
$Res call({
 int profileVisits, int postsCount, int productsListed, int eventsCreated, double totalSales, List<FollowerDataPoint> followerGains
});




}
/// @nodoc
class __$AnalyticsDataCopyWithImpl<$Res>
    implements _$AnalyticsDataCopyWith<$Res> {
  __$AnalyticsDataCopyWithImpl(this._self, this._then);

  final _AnalyticsData _self;
  final $Res Function(_AnalyticsData) _then;

/// Create a copy of AnalyticsData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? profileVisits = null,Object? postsCount = null,Object? productsListed = null,Object? eventsCreated = null,Object? totalSales = null,Object? followerGains = null,}) {
  return _then(_AnalyticsData(
profileVisits: null == profileVisits ? _self.profileVisits : profileVisits // ignore: cast_nullable_to_non_nullable
as int,postsCount: null == postsCount ? _self.postsCount : postsCount // ignore: cast_nullable_to_non_nullable
as int,productsListed: null == productsListed ? _self.productsListed : productsListed // ignore: cast_nullable_to_non_nullable
as int,eventsCreated: null == eventsCreated ? _self.eventsCreated : eventsCreated // ignore: cast_nullable_to_non_nullable
as int,totalSales: null == totalSales ? _self.totalSales : totalSales // ignore: cast_nullable_to_non_nullable
as double,followerGains: null == followerGains ? _self._followerGains : followerGains // ignore: cast_nullable_to_non_nullable
as List<FollowerDataPoint>,
  ));
}


}


/// @nodoc
mixin _$FollowerDataPoint {

 DateTime get date; int get count;
/// Create a copy of FollowerDataPoint
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$FollowerDataPointCopyWith<FollowerDataPoint> get copyWith => _$FollowerDataPointCopyWithImpl<FollowerDataPoint>(this as FollowerDataPoint, _$identity);

  /// Serializes this FollowerDataPoint to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is FollowerDataPoint&&(identical(other.date, date) || other.date == date)&&(identical(other.count, count) || other.count == count));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,date,count);

@override
String toString() {
  return 'FollowerDataPoint(date: $date, count: $count)';
}


}

/// @nodoc
abstract mixin class $FollowerDataPointCopyWith<$Res>  {
  factory $FollowerDataPointCopyWith(FollowerDataPoint value, $Res Function(FollowerDataPoint) _then) = _$FollowerDataPointCopyWithImpl;
@useResult
$Res call({
 DateTime date, int count
});




}
/// @nodoc
class _$FollowerDataPointCopyWithImpl<$Res>
    implements $FollowerDataPointCopyWith<$Res> {
  _$FollowerDataPointCopyWithImpl(this._self, this._then);

  final FollowerDataPoint _self;
  final $Res Function(FollowerDataPoint) _then;

/// Create a copy of FollowerDataPoint
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? date = null,Object? count = null,}) {
  return _then(_self.copyWith(
date: null == date ? _self.date : date // ignore: cast_nullable_to_non_nullable
as DateTime,count: null == count ? _self.count : count // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [FollowerDataPoint].
extension FollowerDataPointPatterns on FollowerDataPoint {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _FollowerDataPoint value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _FollowerDataPoint() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _FollowerDataPoint value)  $default,){
final _that = this;
switch (_that) {
case _FollowerDataPoint():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _FollowerDataPoint value)?  $default,){
final _that = this;
switch (_that) {
case _FollowerDataPoint() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( DateTime date,  int count)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _FollowerDataPoint() when $default != null:
return $default(_that.date,_that.count);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( DateTime date,  int count)  $default,) {final _that = this;
switch (_that) {
case _FollowerDataPoint():
return $default(_that.date,_that.count);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( DateTime date,  int count)?  $default,) {final _that = this;
switch (_that) {
case _FollowerDataPoint() when $default != null:
return $default(_that.date,_that.count);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _FollowerDataPoint implements FollowerDataPoint {
  const _FollowerDataPoint({required this.date, required this.count});
  factory _FollowerDataPoint.fromJson(Map<String, dynamic> json) => _$FollowerDataPointFromJson(json);

@override final  DateTime date;
@override final  int count;

/// Create a copy of FollowerDataPoint
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$FollowerDataPointCopyWith<_FollowerDataPoint> get copyWith => __$FollowerDataPointCopyWithImpl<_FollowerDataPoint>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$FollowerDataPointToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _FollowerDataPoint&&(identical(other.date, date) || other.date == date)&&(identical(other.count, count) || other.count == count));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,date,count);

@override
String toString() {
  return 'FollowerDataPoint(date: $date, count: $count)';
}


}

/// @nodoc
abstract mixin class _$FollowerDataPointCopyWith<$Res> implements $FollowerDataPointCopyWith<$Res> {
  factory _$FollowerDataPointCopyWith(_FollowerDataPoint value, $Res Function(_FollowerDataPoint) _then) = __$FollowerDataPointCopyWithImpl;
@override @useResult
$Res call({
 DateTime date, int count
});




}
/// @nodoc
class __$FollowerDataPointCopyWithImpl<$Res>
    implements _$FollowerDataPointCopyWith<$Res> {
  __$FollowerDataPointCopyWithImpl(this._self, this._then);

  final _FollowerDataPoint _self;
  final $Res Function(_FollowerDataPoint) _then;

/// Create a copy of FollowerDataPoint
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? date = null,Object? count = null,}) {
  return _then(_FollowerDataPoint(
date: null == date ? _self.date : date // ignore: cast_nullable_to_non_nullable
as DateTime,count: null == count ? _self.count : count // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

// dart format on
