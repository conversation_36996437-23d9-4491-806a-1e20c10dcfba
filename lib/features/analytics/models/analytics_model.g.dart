// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'analytics_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_AnalyticsData _$AnalyticsDataFromJson(Map<String, dynamic> json) =>
    _AnalyticsData(
      profileVisits: (json['profileVisits'] as num).toInt(),
      postsCount: (json['postsCount'] as num).toInt(),
      productsListed: (json['productsListed'] as num).toInt(),
      eventsCreated: (json['eventsCreated'] as num).toInt(),
      totalSales: (json['totalSales'] as num).toDouble(),
      followerGains: (json['followerGains'] as List<dynamic>)
          .map((e) => FollowerDataPoint.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$AnalyticsDataToJson(_AnalyticsData instance) =>
    <String, dynamic>{
      'profileVisits': instance.profileVisits,
      'postsCount': instance.postsCount,
      'productsListed': instance.productsListed,
      'eventsCreated': instance.eventsCreated,
      'totalSales': instance.totalSales,
      'followerGains': instance.followerGains,
    };

_FollowerDataPoint _$FollowerDataPointFromJson(Map<String, dynamic> json) =>
    _FollowerDataPoint(
      date: DateTime.parse(json['date'] as String),
      count: (json['count'] as num).toInt(),
    );

Map<String, dynamic> _$FollowerDataPointToJson(_FollowerDataPoint instance) =>
    <String, dynamic>{
      'date': instance.date.toIso8601String(),
      'count': instance.count,
    };
