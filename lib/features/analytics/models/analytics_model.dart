import 'package:freezed_annotation/freezed_annotation.dart';

part 'analytics_model.freezed.dart';
part 'analytics_model.g.dart';

@freezed
abstract class AnalyticsData with _$AnalyticsData {
  const factory AnalyticsData({
    required int profileVisits,
    required int postsCount,
    required int productsListed,
    required int eventsCreated,
    required double totalSales,
    required List<FollowerDataPoint> followerGains,
  }) = _AnalyticsData;

  factory AnalyticsData.fromJson(Map<String, dynamic> json) =>
      _$AnalyticsDataFromJson(json);

  // Factory constructor for empty analytics data
  factory AnalyticsData.empty() => const AnalyticsData(
    profileVisits: 0,
    postsCount: 0,
    productsListed: 0,
    eventsCreated: 0,
    totalSales: 0.0,
    followerGains: [],
  );
}

@freezed
abstract class FollowerDataPoint with _$FollowerDataPoint {
  const factory FollowerDataPoint({
    required DateTime date,
    required int count,
  }) = _FollowerDataPoint;

  factory FollowerDataPoint.fromJson(Map<String, dynamic> json) =>
      _$FollowerDataPointFromJson(json);
}
