import 'package:faker/faker.dart';
import 'package:billionaires_social/features/profile/models/profile_model.dart';
import 'package:billionaires_social/features/marketplace/models/marketplace_models.dart';
import 'package:billionaires_social/features/events/models/event_model.dart';
import 'package:billionaires_social/features/admin/models/admin_models.dart';
import 'package:flutter/foundation.dart';

class AdminService {
  final Faker _faker = Faker();

  // MOCK DATA GENERATION
  ProfileModel _generateMockUser() {
    final id = _faker.guid.guid();
    return ProfileModel(
      id: id,
      username: _faker.internet.userName(),
      name: _faker.person.name(),
      profilePictureUrl: 'https://picsum.photos/seed/$id/150/150',
      bio: _faker.lorem.sentence(),
      postCount: _faker.randomGenerator.integer(1000),
      followerCount: _faker.randomGenerator.integer(100000),
      followingCount: _faker.randomGenerator.integer(500),
      isVerified: _faker.randomGenerator.boolean(),
      isBillionaire: _faker.randomGenerator.boolean(),
      isAdmin: _faker.randomGenerator.boolean(),
      isBusinessAccount: _faker.randomGenerator.boolean(),
      businessName: _faker.company.name(),
      businessCategory: _faker.lorem.word(),
      website: _faker.internet.httpsUrl(),
      createdAt: DateTime.now(),
    );
  }

  MarketplaceItem _generateMockProduct(String sellerId) {
    return MarketplaceItem(
      id: _faker.guid.guid(),
      name: _faker.lorem.word(),
      description: _faker.lorem.sentences(3).join(' '),
      price: _faker.randomGenerator.decimal(scale: 2, min: 10),
      category:
          ProductCategory.values[_faker.randomGenerator.integer(
            ProductCategory.values.length - 1,
          )],
      sellerId: sellerId,
      seller: Seller(
        id: sellerId,
        name: _faker.company.name(),
        avatarUrl: 'https://picsum.photos/seed/$sellerId/150/150',
        isVerified: _faker.randomGenerator.boolean(),
      ),
      images: List.generate(
        _faker.randomGenerator.integer(5, min: 1),
        (index) =>
            'https://picsum.photos/seed/product_${_faker.guid.guid()}_$index/400/400',
      ),
      availableQuantity: _faker.randomGenerator.integer(100),
      soldQuantity: _faker.randomGenerator.integer(50),
      condition:
          ProductCondition.values[_faker.randomGenerator.integer(
            ProductCondition.values.length - 1,
          )],
      createdAt: DateTime.now(),
    );
  }

  EventModel _generateMockEvent(EventHost host) {
    return EventModel(
      id: _faker.guid.guid(),
      title: _faker.lorem.words(3).join(' ').toUpperCase(),
      description: _faker.lorem.sentences(3).join(' '),
      imageUrl: _faker.image.loremPicsum(),
      dateTime: _faker.date.dateTime(minYear: 2024, maxYear: 2025),
      location: '${_faker.address.city()}, ${_faker.address.country()}',
      host: host,
      hostId: host.id,
      price: _faker.randomGenerator.decimal(scale: 2, min: 0),
      capacity: _faker.randomGenerator.integer(1000),
      attendeeIds: List.generate(
        _faker.randomGenerator.integer(50),
        (_) => _faker.guid.guid(),
      ),
      createdAt: _faker.date.dateTime(minYear: 2023, maxYear: 2024),
    );
  }

  // API METHODS

  Future<AdminStats> getDashboardStats() async {
    await Future.delayed(
      const Duration(milliseconds: 500),
    ); // Simulate network delay
    return AdminStats(
      totalUsers: _faker.randomGenerator.integer(100000, min: 50000),
      totalPosts: _faker.randomGenerator.integer(25000, min: 10000),
      totalProducts: _faker.randomGenerator.integer(5000, min: 1000),
      totalEvents: _faker.randomGenerator.integer(1000, min: 200),
      pendingVerifications: _faker.randomGenerator.integer(50, min: 10),
      flaggedContent: _faker.randomGenerator.integer(100, min: 20),
      businessAccounts: _faker.randomGenerator.integer(2000, min: 500),
    );
  }

  Future<List<ProfileModel>> getUsers(UserFilterType filter) async {
    await Future.delayed(const Duration(milliseconds: 800));
    final users = List.generate(50, (_) => _generateMockUser());
    switch (filter) {
      case UserFilterType.all:
        return users;
      case UserFilterType.business:
        return users.where((u) => u.isBusinessAccount).toList();
      case UserFilterType.verified:
        return users.where((u) => u.isVerified).toList();
      case UserFilterType.flagged:
        return users.where((u) => !u.isVerified).toList();
    }
  }

  Future<List<MarketplaceItem>> getProducts() async {
    await Future.delayed(const Duration(milliseconds: 600));
    return List.generate(30, (_) => _generateMockProduct(_faker.guid.guid()));
  }

  Future<List<EventModel>> getEvents() async {
    await Future.delayed(const Duration(milliseconds: 700));
    return List.generate(20, (_) {
      final host = EventHost(
        id: _faker.guid.guid(),
        name: _faker.company.name(),
        avatarUrl: 'https://picsum.photos/seed/${_faker.guid.guid()}/150/150',
        isVerified: _faker.randomGenerator.boolean(),
      );
      return _generateMockEvent(host);
    });
  }

  // Admin Actions
  Future<void> updateUserRole(
    String userId, {
    bool? isAdmin,
    bool? isBillionaire,
  }) async {
    await Future.delayed(const Duration(milliseconds: 300));
    // Simulate API call to update user
    debugPrint(
      'Updating user $userId: isAdmin=$isAdmin, isBillionaire=$isBillionaire',
    );
  }

  Future<void> suspendUser(String userId) async {
    await Future.delayed(const Duration(milliseconds: 300));
    debugPrint('Suspending user $userId');
  }

  Future<void> deleteProduct(String productId) async {
    await Future.delayed(const Duration(milliseconds: 300));
    debugPrint('Deleting product $productId');
  }

  Future<void> cancelEvent(String eventId) async {
    await Future.delayed(const Duration(milliseconds: 300));
    debugPrint('Cancelling event $eventId');
  }

  Future<void> updateSystemSettings(Map<String, bool> settings) async {
    await Future.delayed(const Duration(milliseconds: 500));
    debugPrint('Updating system settings: $settings');
  }
}
