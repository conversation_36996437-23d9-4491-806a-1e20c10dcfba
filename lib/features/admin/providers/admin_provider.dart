import '../services/admin_service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:billionaires_social/features/profile/models/profile_model.dart';
import 'package:billionaires_social/features/marketplace/models/marketplace_models.dart';
import 'package:billionaires_social/features/events/models/event_model.dart';
import '../models/admin_models.dart';

part 'admin_provider.g.dart';

@Riverpod(keepAlive: true)
AdminService adminService(Ref ref) {
  return AdminService();
}

// Dashboard
@riverpod
Future<AdminStats> adminStats(Ref ref) {
  return ref.watch(adminServiceProvider).getDashboardStats();
}

// User Management
@riverpod
class UserFilter extends _$UserFilter {
  @override
  UserFilterType build() => UserFilterType.all;

  void setFilter(UserFilterType filter) {
    state = filter;
  }
}

@riverpod
Future<List<ProfileModel>> filteredUsers(Ref ref) {
  final filter = ref.watch(userFilterProvider);
  return ref.watch(adminServiceProvider).getUsers(filter);
}

// Content Management
@riverpod
Future<List<MarketplaceItem>> adminProducts(Ref ref) {
  return ref.watch(adminServiceProvider).getProducts();
}

@riverpod
Future<List<EventModel>> adminEvents(Ref ref) {
  return ref.watch(adminServiceProvider).getEvents();
}
