// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$adminServiceHash() => r'99f147fbe88a4a6a1ddb46830010085e09b69b6d';

/// See also [adminService].
@ProviderFor(adminService)
final adminServiceProvider = Provider<AdminService>.internal(
  adminService,
  name: r'adminServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$adminServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AdminServiceRef = ProviderRef<AdminService>;
String _$adminStatsHash() => r'64d3a4d7456c815e013d432a196be9c67c9fecdc';

/// See also [adminStats].
@ProviderFor(adminStats)
final adminStatsProvider = AutoDisposeFutureProvider<AdminStats>.internal(
  adminStats,
  name: r'adminStatsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$adminStatsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AdminStatsRef = AutoDisposeFutureProviderRef<AdminStats>;
String _$filteredUsersHash() => r'452c495f50b9de2c4cc0fd21342bfe7bd55373e4';

/// See also [filteredUsers].
@ProviderFor(filteredUsers)
final filteredUsersProvider =
    AutoDisposeFutureProvider<List<ProfileModel>>.internal(
      filteredUsers,
      name: r'filteredUsersProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$filteredUsersHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FilteredUsersRef = AutoDisposeFutureProviderRef<List<ProfileModel>>;
String _$adminProductsHash() => r'0c241815b41aa2b8d256d7411264d10923b72068';

/// See also [adminProducts].
@ProviderFor(adminProducts)
final adminProductsProvider =
    AutoDisposeFutureProvider<List<MarketplaceItem>>.internal(
      adminProducts,
      name: r'adminProductsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$adminProductsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AdminProductsRef = AutoDisposeFutureProviderRef<List<MarketplaceItem>>;
String _$adminEventsHash() => r'a26bc07e68081ad488c6ec129eb5643e9dda4bb3';

/// See also [adminEvents].
@ProviderFor(adminEvents)
final adminEventsProvider =
    AutoDisposeFutureProvider<List<EventModel>>.internal(
      adminEvents,
      name: r'adminEventsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$adminEventsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AdminEventsRef = AutoDisposeFutureProviderRef<List<EventModel>>;
String _$userFilterHash() => r'222a0905e79a85a9195d4ec8cffa46d8663b7e0f';

/// See also [UserFilter].
@ProviderFor(UserFilter)
final userFilterProvider =
    AutoDisposeNotifierProvider<UserFilter, UserFilterType>.internal(
      UserFilter.new,
      name: r'userFilterProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$userFilterHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$UserFilter = AutoDisposeNotifier<UserFilterType>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
