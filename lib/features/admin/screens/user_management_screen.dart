import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:billionaires_social/features/profile/models/profile_model.dart';
import 'package:billionaires_social/features/profile/screens/user_profile_screen.dart';
import '../providers/admin_provider.dart';
import '../models/admin_models.dart';

class UserManagementScreen extends ConsumerWidget {
  const UserManagementScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final usersAsync = ref.watch(filteredUsersProvider);
    final selectedFilter = ref.watch(userFilterProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('User Management'),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(50.0),
          child: _buildFilterChips(context, ref, selectedFilter),
        ),
      ),
      body: usersAsync.when(
        data: (users) => ListView.builder(
          itemCount: users.length,
          itemBuilder: (context, index) {
            return _UserCard(user: users[index]);
          },
        ),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (err, stack) => Center(child: Text('Error: $err')),
      ),
    );
  }

  Widget _buildFilterChips(
    BuildContext context,
    WidgetRef ref,
    UserFilterType selectedFilter,
  ) {
    return SizedBox(
      height: 50,
      child: ListView(
        padding: const EdgeInsets.symmetric(horizontal: 12),
        scrollDirection: Axis.horizontal,
        children: UserFilterType.values.map((filter) {
          final isSelected = selectedFilter == filter;
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4.0),
            child: ChoiceChip(
              label: Text(toBeginningOfSentenceCase(filter.name)!),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  ref.read(userFilterProvider.notifier).setFilter(filter);
                }
              },
            ),
          );
        }).toList(),
      ),
    );
  }
}

class _UserCard extends StatelessWidget {
  final ProfileModel user;
  const _UserCard({required this.user});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 25,
                  backgroundImage: NetworkImage(user.profilePictureUrl),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        user.name,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        '@${user.username}',
                        style: const TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                ),
                if (user.isAdmin)
                  const Chip(
                    label: Text('Admin'),
                    backgroundColor: Colors.purple,
                    labelStyle: TextStyle(color: Colors.white),
                    padding: EdgeInsets.zero,
                  ),
              ],
            ),
            const Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildActionButton(
                  context,
                  icon: FontAwesomeIcons.userPen,
                  label: 'Edit',
                  onTap: () => _editUser(context, user),
                ),
                _buildActionButton(
                  context,
                  icon: FontAwesomeIcons.userCheck,
                  label: 'Verify',
                  color: user.isVerified ? Colors.green : Colors.grey,
                  onTap: () => _toggleUserVerification(context, user),
                ),
                _buildActionButton(
                  context,
                  icon: FontAwesomeIcons.userLock,
                  label: 'Block',
                  color: Colors.red,
                  onTap: () => _blockUser(context, user),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _editUser(BuildContext context, ProfileModel user) async {
    try {
      // Navigate to user profile screen for editing/viewing
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => UserProfileScreen(userId: user.id),
        ),
      );
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to open user profile: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _toggleUserVerification(
    BuildContext context,
    ProfileModel user,
  ) async {
    try {
      final newVerificationStatus = !user.isVerified;

      // Update user verification status in Firestore
      await FirebaseFirestore.instance.collection('users').doc(user.id).update({
        'isVerified': newVerificationStatus,
        'verifiedAt': newVerificationStatus
            ? FieldValue.serverTimestamp()
            : null,
        'verifiedBy': newVerificationStatus
            ? FirebaseAuth.instance.currentUser?.uid
            : null,
      });

      // Add admin action log
      await FirebaseFirestore.instance.collection('admin_actions').add({
        'action': newVerificationStatus ? 'user_verified' : 'user_unverified',
        'targetUserId': user.id,
        'targetUserName': user.name,
        'adminId': FirebaseAuth.instance.currentUser?.uid,
        'timestamp': FieldValue.serverTimestamp(),
        'details': {
          'previousStatus': user.isVerified,
          'newStatus': newVerificationStatus,
        },
      });

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              newVerificationStatus
                  ? 'User ${user.name} verified successfully'
                  : 'User ${user.name} verification removed',
            ),
            backgroundColor: newVerificationStatus
                ? Colors.green
                : Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update verification: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _blockUser(BuildContext context, ProfileModel user) async {
    // Show confirmation dialog first
    final shouldBlock = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Block User'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Are you sure you want to block ${user.name}?'),
            const SizedBox(height: 16),
            const Text('This action will:'),
            const Text('• Prevent the user from logging in'),
            const Text('• Hide their content from other users'),
            const Text('• Disable their account functionality'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Block User'),
          ),
        ],
      ),
    );

    if (shouldBlock != true) return;

    try {
      // Update user blocked status in Firestore
      await FirebaseFirestore.instance.collection('users').doc(user.id).update({
        'isBlocked': true,
        'blockedAt': FieldValue.serverTimestamp(),
        'blockedBy': FirebaseAuth.instance.currentUser?.uid,
        'blockReason': 'Blocked by admin',
      });

      // Add admin action log
      await FirebaseFirestore.instance.collection('admin_actions').add({
        'action': 'user_blocked',
        'targetUserId': user.id,
        'targetUserName': user.name,
        'adminId': FirebaseAuth.instance.currentUser?.uid,
        'timestamp': FieldValue.serverTimestamp(),
        'details': {'reason': 'Blocked by admin', 'severity': 'high'},
      });

      // Create notification for user (they'll see it if they try to log in)
      await FirebaseFirestore.instance.collection('notifications').add({
        'userId': user.id,
        'type': 'account_blocked',
        'title': 'Account Blocked',
        'message':
            'Your account has been blocked by an administrator. Please contact support for more information.',
        'timestamp': FieldValue.serverTimestamp(),
        'isRead': false,
        'priority': 'high',
      });

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('User ${user.name} has been blocked'),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'UNDO',
              textColor: Colors.white,
              onPressed: () => _unblockUser(context, user),
            ),
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to block user: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _unblockUser(BuildContext context, ProfileModel user) async {
    try {
      // Remove blocked status from user
      await FirebaseFirestore.instance.collection('users').doc(user.id).update({
        'isBlocked': false,
        'unblockedAt': FieldValue.serverTimestamp(),
        'unblockedBy': FirebaseAuth.instance.currentUser?.uid,
      });

      // Add admin action log
      await FirebaseFirestore.instance.collection('admin_actions').add({
        'action': 'user_unblocked',
        'targetUserId': user.id,
        'targetUserName': user.name,
        'adminId': FirebaseAuth.instance.currentUser?.uid,
        'timestamp': FieldValue.serverTimestamp(),
        'details': {'reason': 'Unblocked by admin'},
      });

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('User ${user.name} has been unblocked'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to unblock user: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    Color? color,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            FaIcon(icon, size: 20, color: color),
            const SizedBox(height: 4),
            Text(label, style: const TextStyle(fontSize: 12)),
          ],
        ),
      ),
    );
  }
}
