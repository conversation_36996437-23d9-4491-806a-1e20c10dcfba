import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'admin_dashboard_screen.dart';
import 'user_management_screen.dart';
import 'content_moderation_screen.dart';
import 'system_settings_screen.dart';

class AdminMainScreen extends StatefulWidget {
  const AdminMainScreen({super.key});

  @override
  State<AdminMainScreen> createState() => _AdminMainScreenState();
}

class _AdminMainScreenState extends State<AdminMainScreen> {
  int _selectedIndex = 0;

  static const List<Widget> _adminScreens = [
    AdminDashboardScreen(),
    UserManagementScreen(),
    ContentModerationScreen(),
    SystemSettingsScreen(),
  ];

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(index: _selectedIndex, children: _adminScreens),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _selectedIndex,
        onTap: _onItemTapped,
        type: BottomNavigationBarType.fixed,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(FontAwesomeIcons.chartPie),
            label: 'Dashboard',
          ),
          BottomNavigationBarItem(
            icon: Icon(FontAwesomeIcons.usersGear),
            label: 'Users',
          ),
          BottomNavigationBarItem(
            icon: Icon(FontAwesomeIcons.gavel),
            label: 'Content',
          ),
          BottomNavigationBarItem(
            icon: Icon(FontAwesomeIcons.sliders),
            label: 'Settings',
          ),
        ],
      ),
    );
  }
}
