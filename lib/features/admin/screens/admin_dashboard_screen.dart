import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart';
import '../providers/admin_provider.dart';
import '../models/admin_models.dart';

class AdminDashboardScreen extends ConsumerWidget {
  const AdminDashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final statsAsync = ref.watch(adminStatsProvider);

    return Scaffold(
      appBar: AppBar(title: const Text('Admin Dashboard')),
      body: statsAsync.when(
        data: (stats) => _buildDashboard(context, stats, ref),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (err, stack) => Center(child: Text('Error: $err')),
      ),
    );
  }

  Widget _buildDashboard(
    BuildContext context,
    AdminStats stats,
    WidgetRef ref,
  ) {
    final formatter = NumberFormat.compact();
    return RefreshIndicator(
      onRefresh: () => ref.refresh(adminStatsProvider.future),
      child: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          Text(
            'System Overview',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          GridView.count(
            crossAxisCount: 2,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.5,
            children: [
              _StatCard(
                title: 'Total Users',
                value: formatter.format(stats.totalUsers),
                icon: FontAwesomeIcons.users,
                color: Colors.blue,
              ),
              _StatCard(
                title: 'Business Accounts',
                value: formatter.format(stats.businessAccounts),
                icon: FontAwesomeIcons.store,
                color: Colors.green,
              ),
              _StatCard(
                title: 'Total Products',
                value: formatter.format(stats.totalProducts),
                icon: FontAwesomeIcons.boxesStacked,
                color: Colors.orange,
              ),
              _StatCard(
                title: 'Total Events',
                value: formatter.format(stats.totalEvents),
                icon: FontAwesomeIcons.solidCalendar,
                color: Colors.purple,
              ),
              _StatCard(
                title: 'Pending Verifications',
                value: formatter.format(stats.pendingVerifications),
                icon: FontAwesomeIcons.userCheck,
                color: Colors.teal,
              ),
              _StatCard(
                title: 'Flagged Content',
                value: formatter.format(stats.flaggedContent),
                icon: FontAwesomeIcons.flag,
                color: Colors.red,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _StatCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;

  const _StatCard({
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 24, color: color),
            const Spacer(),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(title, style: Theme.of(context).textTheme.bodyMedium),
          ],
        ),
      ),
    );
  }
}
