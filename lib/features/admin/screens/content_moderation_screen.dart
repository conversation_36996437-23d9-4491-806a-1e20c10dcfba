import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:billionaires_social/features/marketplace/models/marketplace_models.dart';
import 'package:billionaires_social/features/events/models/event_model.dart';
import 'package:billionaires_social/features/profile/screens/user_profile_screen.dart';

import '../providers/admin_provider.dart';

class ContentModerationScreen extends StatelessWidget {
  const ContentModerationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Content Moderation'),
          bottom: const TabBar(
            tabs: [
              Tab(text: 'Products'),
              Tab(text: 'Events'),
            ],
          ),
        ),
        body: const TabBarView(children: [_ProductList(), _EventList()]),
      ),
    );
  }
}

class _ProductList extends ConsumerWidget {
  const _ProductList();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final productsAsync = ref.watch(adminProductsProvider);
    return productsAsync.when(
      data: (products) => ListView.builder(
        itemCount: products.length,
        itemBuilder: (context, index) {
          return _ProductCard(product: products[index]);
        },
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (err, stack) => Center(child: Text('Error: $err')),
    );
  }
}

class _ProductCard extends StatelessWidget {
  final MarketplaceItem product;
  const _ProductCard({required this.product});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        leading: Image.network(
          product.images.first,
          width: 50,
          height: 50,
          fit: BoxFit.cover,
        ),
        title: Text(product.name),
        subtitle: Text('Price: \$${product.price.toStringAsFixed(2)}'),
        trailing: IconButton(
          icon: const Icon(Icons.more_vert),
          onPressed: () => _showProductModerationOptions(context, product),
        ),
      ),
    );
  }

  void _showProductModerationOptions(
    BuildContext context,
    MarketplaceItem product,
  ) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Moderate: ${product.name}',
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            _ModerationOption(
              icon: Icons.visibility,
              title: 'Approve Product',
              subtitle: 'Make this product visible to all users',
              color: Colors.green,
              onTap: () {
                Navigator.pop(context);
                _approveProduct(context, product);
              },
            ),
            _ModerationOption(
              icon: Icons.visibility_off,
              title: 'Hide Product',
              subtitle: 'Hide this product from public view',
              color: Colors.orange,
              onTap: () {
                Navigator.pop(context);
                _hideProduct(context, product);
              },
            ),
            _ModerationOption(
              icon: Icons.delete,
              title: 'Delete Product',
              subtitle: 'Permanently remove this product',
              color: Colors.red,
              onTap: () {
                Navigator.pop(context);
                _deleteProduct(context, product);
              },
            ),
            _ModerationOption(
              icon: Icons.flag,
              title: 'Flag for Review',
              subtitle: 'Mark this product for further review',
              color: Colors.amber,
              onTap: () {
                Navigator.pop(context);
                _flagProduct(context, product);
              },
            ),
            _ModerationOption(
              icon: Icons.person,
              title: 'View Seller Profile',
              subtitle: 'Check the seller\'s account details',
              color: Colors.blue,
              onTap: () {
                Navigator.pop(context);
                _viewSellerProfile(context, product);
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _approveProduct(
    BuildContext context,
    MarketplaceItem product,
  ) async {
    try {
      // Update product status in Firestore
      await FirebaseFirestore.instance
          .collection('marketplace_items')
          .doc(product.id)
          .update({
            'status': 'approved',
            'isVisible': true,
            'moderatedAt': FieldValue.serverTimestamp(),
            'moderatedBy': FirebaseAuth.instance.currentUser?.uid,
            'moderationAction': 'approved',
          });

      // Add moderation log
      await FirebaseFirestore.instance.collection('moderation_logs').add({
        'contentType': 'product',
        'contentId': product.id,
        'action': 'approved',
        'moderatorId': FirebaseAuth.instance.currentUser?.uid,
        'timestamp': FieldValue.serverTimestamp(),
        'details': {'productName': product.name, 'sellerId': product.sellerId},
      });

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Product "${product.name}" approved successfully'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'UNDO',
              textColor: Colors.white,
              onPressed: () => _undoProductAction(context, product, 'pending'),
            ),
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to approve product: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _hideProduct(
    BuildContext context,
    MarketplaceItem product,
  ) async {
    try {
      // Update product visibility in Firestore
      await FirebaseFirestore.instance
          .collection('marketplace_items')
          .doc(product.id)
          .update({
            'status': 'hidden',
            'isVisible': false,
            'moderatedAt': FieldValue.serverTimestamp(),
            'moderatedBy': FirebaseAuth.instance.currentUser?.uid,
            'moderationAction': 'hidden',
          });

      // Add moderation log
      await FirebaseFirestore.instance.collection('moderation_logs').add({
        'contentType': 'product',
        'contentId': product.id,
        'action': 'hidden',
        'moderatorId': FirebaseAuth.instance.currentUser?.uid,
        'timestamp': FieldValue.serverTimestamp(),
        'details': {
          'productName': product.name,
          'sellerId': product.sellerId,
          'reason': 'Hidden by admin',
        },
      });

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Product "${product.name}" hidden from public view'),
            backgroundColor: Colors.orange,
            action: SnackBarAction(
              label: 'UNDO',
              textColor: Colors.white,
              onPressed: () => _undoProductAction(context, product, 'approved'),
            ),
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to hide product: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _deleteProduct(BuildContext context, MarketplaceItem product) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Product'),
        content: Text(
          'Are you sure you want to permanently delete "${product.name}"?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await _performProductDeletion(context, product);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Future<void> _flagProduct(
    BuildContext context,
    MarketplaceItem product,
  ) async {
    try {
      // Update product status to flagged
      await FirebaseFirestore.instance
          .collection('marketplace_items')
          .doc(product.id)
          .update({
            'status': 'flagged',
            'flaggedAt': FieldValue.serverTimestamp(),
            'flaggedBy': FirebaseAuth.instance.currentUser?.uid,
            'requiresReview': true,
          });

      // Add moderation log
      await FirebaseFirestore.instance.collection('moderation_logs').add({
        'contentType': 'product',
        'contentId': product.id,
        'action': 'flagged',
        'moderatorId': FirebaseAuth.instance.currentUser?.uid,
        'timestamp': FieldValue.serverTimestamp(),
        'details': {
          'productName': product.name,
          'sellerId': product.sellerId,
          'reason': 'Flagged for further review',
        },
      });

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Product "${product.name}" flagged for review'),
            backgroundColor: Colors.amber,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to flag product: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _viewSellerProfile(
    BuildContext context,
    MarketplaceItem product,
  ) async {
    try {
      // Navigate to seller profile screen
      if (context.mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => UserProfileScreen(userId: product.sellerId),
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to open seller profile: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _performProductDeletion(
    BuildContext context,
    MarketplaceItem product,
  ) async {
    try {
      // Delete product from Firestore
      await FirebaseFirestore.instance
          .collection('marketplace_items')
          .doc(product.id)
          .delete();

      // Add moderation log
      await FirebaseFirestore.instance.collection('moderation_logs').add({
        'contentType': 'product',
        'contentId': product.id,
        'action': 'deleted',
        'moderatorId': FirebaseAuth.instance.currentUser?.uid,
        'timestamp': FieldValue.serverTimestamp(),
        'details': {
          'productName': product.name,
          'sellerId': product.sellerId,
          'reason': 'Permanently deleted by admin',
        },
      });

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Product "${product.name}" deleted permanently'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete product: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _undoProductAction(
    BuildContext context,
    MarketplaceItem product,
    String newStatus,
  ) async {
    try {
      await FirebaseFirestore.instance
          .collection('marketplace_items')
          .doc(product.id)
          .update({
            'status': newStatus,
            'isVisible': newStatus == 'approved',
            'moderatedAt': FieldValue.serverTimestamp(),
            'moderatedBy': FirebaseAuth.instance.currentUser?.uid,
            'moderationAction': 'undo_$newStatus',
          });

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Product "${product.name}" action undone'),
            backgroundColor: Colors.blue,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to undo action: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

class _EventList extends ConsumerWidget {
  const _EventList();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final eventsAsync = ref.watch(adminEventsProvider);
    return eventsAsync.when(
      data: (events) => ListView.builder(
        itemCount: events.length,
        itemBuilder: (context, index) {
          return _EventCard(event: events[index]);
        },
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (err, stack) => Center(child: Text('Error: $err')),
    );
  }
}

class _EventCard extends StatelessWidget {
  final EventModel event;
  const _EventCard({required this.event});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        leading: Image.network(
          event.imageUrl,
          width: 50,
          height: 50,
          fit: BoxFit.cover,
        ),
        title: Text(event.title),
        subtitle: Text('Hosted by: ${event.host.name}'),
        trailing: IconButton(
          icon: const Icon(Icons.more_vert),
          onPressed: () => _showEventModerationOptions(context, event),
        ),
      ),
    );
  }

  void _showEventModerationOptions(BuildContext context, EventModel event) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Moderate: ${event.title}',
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            _ModerationOption(
              icon: Icons.check_circle,
              title: 'Approve Event',
              subtitle: 'Make this event visible to all users',
              color: Colors.green,
              onTap: () {
                Navigator.pop(context);
                _approveEvent(context, event);
              },
            ),
            _ModerationOption(
              icon: Icons.visibility_off,
              title: 'Hide Event',
              subtitle: 'Hide this event from public view',
              color: Colors.orange,
              onTap: () {
                Navigator.pop(context);
                _hideEvent(context, event);
              },
            ),
            _ModerationOption(
              icon: Icons.cancel,
              title: 'Cancel Event',
              subtitle: 'Cancel this event and notify attendees',
              color: Colors.red,
              onTap: () {
                Navigator.pop(context);
                _cancelEvent(context, event);
              },
            ),
            _ModerationOption(
              icon: Icons.flag,
              title: 'Flag for Review',
              subtitle: 'Mark this event for further review',
              color: Colors.amber,
              onTap: () {
                Navigator.pop(context);
                _flagEvent(context, event);
              },
            ),
            _ModerationOption(
              icon: Icons.person,
              title: 'View Host Profile',
              subtitle: 'Check the event host\'s account details',
              color: Colors.blue,
              onTap: () {
                Navigator.pop(context);
                _viewHostProfile(context, event);
              },
            ),
            _ModerationOption(
              icon: Icons.people,
              title: 'View Attendees',
              subtitle: 'See who is attending this event',
              color: Colors.purple,
              onTap: () {
                Navigator.pop(context);
                _viewAttendees(context, event);
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _approveEvent(BuildContext context, EventModel event) async {
    try {
      // Update event status in Firestore
      await FirebaseFirestore.instance
          .collection('events')
          .doc(event.id)
          .update({
            'status': 'approved',
            'isVisible': true,
            'moderatedAt': FieldValue.serverTimestamp(),
            'moderatedBy': FirebaseAuth.instance.currentUser?.uid,
            'moderationAction': 'approved',
          });

      // Add moderation log
      await FirebaseFirestore.instance.collection('moderation_logs').add({
        'contentType': 'event',
        'contentId': event.id,
        'action': 'approved',
        'moderatorId': FirebaseAuth.instance.currentUser?.uid,
        'timestamp': FieldValue.serverTimestamp(),
        'details': {
          'eventTitle': event.title,
          'hostId': event.host.id,
          'eventDate': event.dateTime,
        },
      });

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Event "${event.title}" approved successfully'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'UNDO',
              textColor: Colors.white,
              onPressed: () => _undoEventAction(context, event, 'pending'),
            ),
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to approve event: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _hideEvent(BuildContext context, EventModel event) async {
    try {
      // Update event visibility in Firestore
      await FirebaseFirestore.instance
          .collection('events')
          .doc(event.id)
          .update({
            'status': 'hidden',
            'isVisible': false,
            'moderatedAt': FieldValue.serverTimestamp(),
            'moderatedBy': FirebaseAuth.instance.currentUser?.uid,
            'moderationAction': 'hidden',
          });

      // Add moderation log
      await FirebaseFirestore.instance.collection('moderation_logs').add({
        'contentType': 'event',
        'contentId': event.id,
        'action': 'hidden',
        'moderatorId': FirebaseAuth.instance.currentUser?.uid,
        'timestamp': FieldValue.serverTimestamp(),
        'details': {
          'eventTitle': event.title,
          'hostId': event.host.id,
          'reason': 'Hidden by admin',
        },
      });

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Event "${event.title}" hidden from public view'),
            backgroundColor: Colors.orange,
            action: SnackBarAction(
              label: 'UNDO',
              textColor: Colors.white,
              onPressed: () => _undoEventAction(context, event, 'approved'),
            ),
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to hide event: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _cancelEvent(BuildContext context, EventModel event) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Event'),
        content: Text(
          'Are you sure you want to cancel "${event.title}"? All attendees will be notified.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Keep Event'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await _performEventCancellation(context, event);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Cancel Event'),
          ),
        ],
      ),
    );
  }

  Future<void> _flagEvent(BuildContext context, EventModel event) async {
    try {
      // Update event status to flagged
      await FirebaseFirestore.instance
          .collection('events')
          .doc(event.id)
          .update({
            'status': 'flagged',
            'flaggedAt': FieldValue.serverTimestamp(),
            'flaggedBy': FirebaseAuth.instance.currentUser?.uid,
            'requiresReview': true,
          });

      // Add moderation log
      await FirebaseFirestore.instance.collection('moderation_logs').add({
        'contentType': 'event',
        'contentId': event.id,
        'action': 'flagged',
        'moderatorId': FirebaseAuth.instance.currentUser?.uid,
        'timestamp': FieldValue.serverTimestamp(),
        'details': {
          'eventTitle': event.title,
          'hostId': event.host.id,
          'reason': 'Flagged for further review',
        },
      });

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Event "${event.title}" flagged for review'),
            backgroundColor: Colors.amber,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to flag event: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _viewHostProfile(BuildContext context, EventModel event) async {
    try {
      // Navigate to host profile screen
      if (context.mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => UserProfileScreen(userId: event.host.id),
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to open host profile: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _viewAttendees(BuildContext context, EventModel event) async {
    try {
      // Fetch attendees from Firestore
      final attendeesSnapshot = await FirebaseFirestore.instance
          .collection('event_attendees')
          .where('eventId', isEqualTo: event.id)
          .get();

      if (context.mounted) {
        // Show detailed attendee management dialog
        await _showAttendeeManagementDialog(context, event, attendeesSnapshot);
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load attendees: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _performEventCancellation(
    BuildContext context,
    EventModel event,
  ) async {
    try {
      // Update event status to cancelled
      await FirebaseFirestore.instance
          .collection('events')
          .doc(event.id)
          .update({
            'status': 'cancelled',
            'isCancelled': true,
            'cancelledAt': FieldValue.serverTimestamp(),
            'cancelledBy': FirebaseAuth.instance.currentUser?.uid,
            'moderationAction': 'cancelled',
          });

      // Add moderation log
      await FirebaseFirestore.instance.collection('moderation_logs').add({
        'contentType': 'event',
        'contentId': event.id,
        'action': 'cancelled',
        'moderatorId': FirebaseAuth.instance.currentUser?.uid,
        'timestamp': FieldValue.serverTimestamp(),
        'details': {
          'eventTitle': event.title,
          'hostId': event.host.id,
          'reason': 'Cancelled by admin',
        },
      });

      // Send notifications to attendees
      await _notifyEventAttendees(event, 'cancelled');

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Event "${event.title}" cancelled and attendees notified',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to cancel event: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _undoEventAction(
    BuildContext context,
    EventModel event,
    String newStatus,
  ) async {
    try {
      await FirebaseFirestore.instance
          .collection('events')
          .doc(event.id)
          .update({
            'status': newStatus,
            'isVisible': newStatus == 'approved',
            'moderatedAt': FieldValue.serverTimestamp(),
            'moderatedBy': FirebaseAuth.instance.currentUser?.uid,
            'moderationAction': 'undo_$newStatus',
          });

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Event "${event.title}" action undone'),
            backgroundColor: Colors.blue,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to undo action: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _notifyEventAttendees(EventModel event, String action) async {
    try {
      // Get all attendees for this event
      final attendeesSnapshot = await FirebaseFirestore.instance
          .collection('event_attendees')
          .where('eventId', isEqualTo: event.id)
          .get();

      // Create notification batch
      final batch = FirebaseFirestore.instance.batch();

      for (final doc in attendeesSnapshot.docs) {
        final attendeeId = doc.data()['userId'] as String;

        // Add notification for each attendee
        final notificationRef = FirebaseFirestore.instance
            .collection('notifications')
            .doc();

        batch.set(notificationRef, {
          'userId': attendeeId,
          'type': 'event_$action',
          'title': 'Event ${action.toUpperCase()}',
          'message': 'The event "${event.title}" has been $action.',
          'eventId': event.id,
          'eventTitle': event.title,
          'timestamp': FieldValue.serverTimestamp(),
          'isRead': false,
        });
      }

      // Commit all notifications
      await batch.commit();
    } catch (e) {
      // Log error but don't throw to avoid breaking the main operation
      debugPrint('Failed to notify attendees: $e');
    }
  }

  Future<void> _showAttendeeManagementDialog(
    BuildContext context,
    EventModel event,
    QuerySnapshot attendeesSnapshot,
  ) async {
    final attendeeCount = attendeesSnapshot.docs.length;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.8,
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Event Attendees',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          event.title,
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(color: Colors.grey[600]),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const Divider(),

              // Stats Row
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildStatItem('Total Attendees', attendeeCount.toString()),
                    _buildStatItem(
                      'Event Date',
                      _formatEventDate(event.dateTime),
                    ),
                    _buildStatItem('Host', event.host.name),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _notifyAllAttendees(
                        context,
                        event,
                        attendeesSnapshot,
                      ),
                      icon: const Icon(Icons.notifications),
                      label: const Text('Notify All'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _exportAttendeeList(
                        context,
                        event,
                        attendeesSnapshot,
                      ),
                      icon: const Icon(Icons.download),
                      label: const Text('Export List'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Attendee List
              Expanded(
                child: attendeeCount > 0
                    ? _buildAttendeeList(context, event, attendeesSnapshot)
                    : const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.people_outline,
                              size: 64,
                              color: Colors.grey,
                            ),
                            SizedBox(height: 16),
                            Text(
                              'No attendees yet',
                              style: TextStyle(
                                fontSize: 18,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 4),
        Text(label, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
      ],
    );
  }

  String _formatEventDate(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
  }

  Widget _buildAttendeeList(
    BuildContext context,
    EventModel event,
    QuerySnapshot attendeesSnapshot,
  ) {
    return ListView.builder(
      itemCount: attendeesSnapshot.docs.length,
      itemBuilder: (context, index) {
        final attendeeDoc = attendeesSnapshot.docs[index];
        final attendeeData = attendeeDoc.data() as Map<String, dynamic>;
        final userId = attendeeData['userId'] as String;
        final joinedAt = attendeeData['joinedAt'] as Timestamp?;
        final status = attendeeData['status'] as String? ?? 'confirmed';

        return _AttendeeListItem(
          userId: userId,
          joinedAt: joinedAt?.toDate(),
          status: status,
          onViewProfile: () => _navigateToUserProfile(context, userId),
          onRemoveAttendee: () => _removeAttendee(context, event, userId),
          onChangeStatus: (newStatus) =>
              _changeAttendeeStatus(context, event, userId, newStatus),
        );
      },
    );
  }

  Future<void> _navigateToUserProfile(
    BuildContext context,
    String userId,
  ) async {
    try {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => UserProfileScreen(userId: userId),
        ),
      );
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to open profile: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _notifyAllAttendees(
    BuildContext context,
    EventModel event,
    QuerySnapshot attendeesSnapshot,
  ) async {
    try {
      await _notifyEventAttendees(event, 'update');

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Notified ${attendeesSnapshot.docs.length} attendees',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to notify attendees: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _exportAttendeeList(
    BuildContext context,
    EventModel event,
    QuerySnapshot attendeesSnapshot,
  ) async {
    // For now, show a summary dialog
    if (context.mounted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Export Attendee List'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Event: ${event.title}'),
              Text('Total Attendees: ${attendeesSnapshot.docs.length}'),
              const SizedBox(height: 16),
              const Text('Export functionality would include:'),
              const Text('• CSV file with attendee details'),
              const Text('• Email addresses for notifications'),
              const Text('• Join dates and status information'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Close'),
            ),
          ],
        ),
      );
    }
  }

  Future<void> _removeAttendee(
    BuildContext context,
    EventModel event,
    String userId,
  ) async {
    try {
      // Remove attendee from event
      await FirebaseFirestore.instance
          .collection('event_attendees')
          .where('eventId', isEqualTo: event.id)
          .where('userId', isEqualTo: userId)
          .get()
          .then((snapshot) {
            for (final doc in snapshot.docs) {
              doc.reference.delete();
            }
          });

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Attendee removed successfully'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to remove attendee: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _changeAttendeeStatus(
    BuildContext context,
    EventModel event,
    String userId,
    String newStatus,
  ) async {
    try {
      // Update attendee status
      await FirebaseFirestore.instance
          .collection('event_attendees')
          .where('eventId', isEqualTo: event.id)
          .where('userId', isEqualTo: userId)
          .get()
          .then((snapshot) {
            for (final doc in snapshot.docs) {
              doc.reference.update({'status': newStatus});
            }
          });

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Attendee status updated to $newStatus'),
            backgroundColor: Colors.blue,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update status: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

class _AttendeeListItem extends StatelessWidget {
  final String userId;
  final DateTime? joinedAt;
  final String status;
  final VoidCallback onViewProfile;
  final VoidCallback onRemoveAttendee;
  final Function(String) onChangeStatus;

  const _AttendeeListItem({
    required this.userId,
    required this.joinedAt,
    required this.status,
    required this.onViewProfile,
    required this.onRemoveAttendee,
    required this.onChangeStatus,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: ListTile(
        leading: CircleAvatar(
          child: Text(userId.substring(0, 2).toUpperCase()),
        ),
        title: Text('User: $userId'),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Status: ${status.toUpperCase()}'),
            if (joinedAt != null) Text('Joined: ${_formatDate(joinedAt!)}'),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'profile':
                onViewProfile();
                break;
              case 'confirmed':
              case 'pending':
              case 'cancelled':
                onChangeStatus(value);
                break;
              case 'remove':
                onRemoveAttendee();
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'profile',
              child: Row(
                children: [
                  Icon(Icons.person),
                  SizedBox(width: 8),
                  Text('View Profile'),
                ],
              ),
            ),
            const PopupMenuDivider(),
            const PopupMenuItem(
              value: 'confirmed',
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green),
                  SizedBox(width: 8),
                  Text('Mark Confirmed'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'pending',
              child: Row(
                children: [
                  Icon(Icons.schedule, color: Colors.orange),
                  SizedBox(width: 8),
                  Text('Mark Pending'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'cancelled',
              child: Row(
                children: [
                  Icon(Icons.cancel, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Mark Cancelled'),
                ],
              ),
            ),
            const PopupMenuDivider(),
            const PopupMenuItem(
              value: 'remove',
              child: Row(
                children: [
                  Icon(Icons.delete, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Remove Attendee'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

class _ModerationOption extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final Color color;
  final VoidCallback onTap;

  const _ModerationOption({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.color,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: color.withValues(alpha: 0.1),
        child: Icon(icon, color: color),
      ),
      title: Text(title),
      subtitle: Text(subtitle),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 0, vertical: 4),
    );
  }
}
