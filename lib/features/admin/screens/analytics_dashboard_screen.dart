import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';

/// Real-time analytics dashboard for monitoring app performance
class AnalyticsDashboardScreen extends ConsumerStatefulWidget {
  const AnalyticsDashboardScreen({super.key});

  @override
  ConsumerState<AnalyticsDashboardScreen> createState() =>
      _AnalyticsDashboardScreenState();
}

class _AnalyticsDashboardScreenState
    extends ConsumerState<AnalyticsDashboardScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  String _selectedTimeRange = '24h';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Analytics Dashboard'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        actions: [
          _buildTimeRangeSelector(),
          IconButton(icon: const Icon(Icons.refresh), onPressed: _refreshData),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.grey,
          tabs: const [
            Tab(text: 'Overview'),
            Tab(text: 'Users'),
            Tab(text: 'Content'),
            Tab(text: 'Performance'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(),
          _buildUsersTab(),
          _buildContentTab(),
          _buildPerformanceTab(),
        ],
      ),
    );
  }

  Widget _buildTimeRangeSelector() {
    return PopupMenuButton<String>(
      initialValue: _selectedTimeRange,
      onSelected: (value) {
        setState(() {
          _selectedTimeRange = value;
        });
        _refreshData();
      },
      itemBuilder: (context) => [
        const PopupMenuItem(value: '1h', child: Text('Last Hour')),
        const PopupMenuItem(value: '24h', child: Text('Last 24 Hours')),
        const PopupMenuItem(value: '7d', child: Text('Last 7 Days')),
        const PopupMenuItem(value: '30d', child: Text('Last 30 Days')),
      ],
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(_getTimeRangeLabel()),
            const Icon(Icons.arrow_drop_down),
          ],
        ),
      ),
    );
  }

  String _getTimeRangeLabel() {
    switch (_selectedTimeRange) {
      case '1h':
        return '1H';
      case '24h':
        return '24H';
      case '7d':
        return '7D';
      case '30d':
        return '30D';
      default:
        return '24H';
    }
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMetricsGrid(),
          const SizedBox(height: 24),
          _buildActiveUsersChart(),
          const SizedBox(height: 24),
          _buildEngagementChart(),
          const SizedBox(height: 24),
          _buildRealtimeActivity(),
        ],
      ),
    );
  }

  Widget _buildMetricsGrid() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.5,
      children: [
        _buildMetricCard(
          title: 'Active Users',
          value: '1,247',
          change: '+12.5%',
          isPositive: true,
          icon: Icons.people,
          color: Colors.blue,
        ),
        _buildMetricCard(
          title: 'Total Posts',
          value: '8,932',
          change: '+8.2%',
          isPositive: true,
          icon: Icons.article,
          color: Colors.green,
        ),
        _buildMetricCard(
          title: 'Engagement Rate',
          value: '94.7%',
          change: '+2.1%',
          isPositive: true,
          icon: Icons.favorite,
          color: Colors.red,
        ),
        _buildMetricCard(
          title: 'Revenue',
          value: '\$127K',
          change: '+15.3%',
          isPositive: true,
          icon: Icons.attach_money,
          color: Colors.amber,
        ),
      ],
    );
  }

  Widget _buildMetricCard({
    required String title,
    required String value,
    required String change,
    required bool isPositive,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: isPositive
                        ? Colors.green.withValues(alpha: 0.1)
                        : Colors.red.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    change,
                    style: TextStyle(
                      color: isPositive ? Colors.green : Colors.red,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            Text(
              title,
              style: TextStyle(color: Colors.grey[600], fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActiveUsersChart() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Active Users Over Time',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: LineChart(
                LineChartData(
                  gridData: FlGridData(show: true),
                  titlesData: FlTitlesData(show: true),
                  borderData: FlBorderData(show: true),
                  lineBarsData: [
                    LineChartBarData(
                      spots: _generateActiveUsersData(),
                      isCurved: true,
                      color: Colors.blue,
                      barWidth: 3,
                      dotData: FlDotData(show: false),
                      belowBarData: BarAreaData(
                        show: true,
                        color: Colors.blue.withValues(alpha: 0.1),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEngagementChart() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Engagement Breakdown',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: PieChart(
                PieChartData(
                  sections: [
                    PieChartSectionData(
                      value: 45,
                      title: 'Likes\n45%',
                      color: Colors.red,
                      radius: 80,
                    ),
                    PieChartSectionData(
                      value: 25,
                      title: 'Comments\n25%',
                      color: Colors.blue,
                      radius: 80,
                    ),
                    PieChartSectionData(
                      value: 20,
                      title: 'Shares\n20%',
                      color: Colors.green,
                      radius: 80,
                    ),
                    PieChartSectionData(
                      value: 10,
                      title: 'Saves\n10%',
                      color: Colors.orange,
                      radius: 80,
                    ),
                  ],
                  centerSpaceRadius: 40,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRealtimeActivity() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Text(
                  'Real-time Activity',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                Container(
                  width: 12,
                  height: 12,
                  decoration: const BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),
                const Text('Live'),
              ],
            ),
            const SizedBox(height: 16),
            _buildActivityItem(
              'New user registration',
              '2 seconds ago',
              Icons.person_add,
            ),
            _buildActivityItem(
              'Post published by @elonmusk',
              '15 seconds ago',
              Icons.article,
            ),
            _buildActivityItem(
              'Story viewed 1K times',
              '32 seconds ago',
              Icons.visibility,
            ),
            _buildActivityItem(
              'Premium subscription purchased',
              '1 minute ago',
              Icons.star,
            ),
            _buildActivityItem(
              'New message sent',
              '2 minutes ago',
              Icons.message,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(String activity, String time, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Expanded(child: Text(activity)),
          Text(time, style: TextStyle(color: Colors.grey[600], fontSize: 12)),
        ],
      ),
    );
  }

  Widget _buildUsersTab() {
    return const Center(child: Text('User Analytics Coming Soon'));
  }

  Widget _buildContentTab() {
    return const Center(child: Text('Content Analytics Coming Soon'));
  }

  Widget _buildPerformanceTab() {
    return const Center(child: Text('Performance Analytics Coming Soon'));
  }

  List<FlSpot> _generateActiveUsersData() {
    // Generate sample data - replace with real data
    return [
      const FlSpot(0, 1000),
      const FlSpot(1, 1050),
      const FlSpot(2, 1100),
      const FlSpot(3, 1200),
      const FlSpot(4, 1150),
      const FlSpot(5, 1300),
      const FlSpot(6, 1247),
    ];
  }

  void _refreshData() {
    // Implement data refresh logic
    setState(() {
      // Trigger rebuild with new data
    });
  }
}
