import 'package:flutter/material.dart';

class SystemSettingsScreen extends StatelessWidget {
  const SystemSettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('System Settings')),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          _SettingsGroup(
            title: 'General',
            children: [
              _SwitchTile(
                title: 'Maintenance Mode',
                subtitle: 'Temporarily disable user access to the app.',
                value: false,
                onChanged: (val) {},
              ),
              _SettingsTile(
                title: 'Clear Cache',
                subtitle: 'Force clear all cached data across the system.',
                onTap: () {},
              ),
            ],
          ),
          _SettingsGroup(
            title: 'Content Moderation',
            children: [
              _SwitchTile(
                title: 'Auto-approve new products',
                value: true,
                onChanged: (val) {},
              ),
              _SwitchTile(
                title: 'Auto-approve new events',
                value: false,
                onChanged: (val) {},
              ),
            ],
          ),
          _SettingsGroup(
            title: 'User Management',
            children: [
              _SwitchTile(
                title: 'New user registrations enabled',
                value: true,
                onChanged: (val) {},
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _SettingsGroup extends StatelessWidget {
  final String title;
  final List<Widget> children;
  const _SettingsGroup({required this.title, required this.children});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 8.0),
          child: Text(
            title.toUpperCase(),
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ),
        Card(child: Column(children: children)),
        const SizedBox(height: 16),
      ],
    );
  }
}

class _SettingsTile extends StatelessWidget {
  final String title;
  final String? subtitle;
  final VoidCallback onTap;

  const _SettingsTile({
    required this.title,
    this.subtitle,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      title: Text(title),
      subtitle: subtitle != null ? Text(subtitle!) : null,
      onTap: onTap,
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
    );
  }
}

class _SwitchTile extends StatelessWidget {
  final String title;
  final String? subtitle;
  final bool value;
  final ValueChanged<bool> onChanged;

  const _SwitchTile({
    required this.title,
    this.subtitle,
    required this.value,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return SwitchListTile(
      title: Text(title),
      subtitle: subtitle != null ? Text(subtitle!) : null,
      value: value,
      onChanged: onChanged,
    );
  }
}
