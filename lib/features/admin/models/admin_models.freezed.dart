// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'admin_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AdminStats {

 int get totalUsers; int get businessAccounts; int get totalProducts; int get totalEvents; int get totalPosts; int get pendingVerifications; int get flaggedContent;
/// Create a copy of AdminStats
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AdminStatsCopyWith<AdminStats> get copyWith => _$AdminStatsCopyWithImpl<AdminStats>(this as AdminStats, _$identity);

  /// Serializes this AdminStats to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AdminStats&&(identical(other.totalUsers, totalUsers) || other.totalUsers == totalUsers)&&(identical(other.businessAccounts, businessAccounts) || other.businessAccounts == businessAccounts)&&(identical(other.totalProducts, totalProducts) || other.totalProducts == totalProducts)&&(identical(other.totalEvents, totalEvents) || other.totalEvents == totalEvents)&&(identical(other.totalPosts, totalPosts) || other.totalPosts == totalPosts)&&(identical(other.pendingVerifications, pendingVerifications) || other.pendingVerifications == pendingVerifications)&&(identical(other.flaggedContent, flaggedContent) || other.flaggedContent == flaggedContent));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalUsers,businessAccounts,totalProducts,totalEvents,totalPosts,pendingVerifications,flaggedContent);

@override
String toString() {
  return 'AdminStats(totalUsers: $totalUsers, businessAccounts: $businessAccounts, totalProducts: $totalProducts, totalEvents: $totalEvents, totalPosts: $totalPosts, pendingVerifications: $pendingVerifications, flaggedContent: $flaggedContent)';
}


}

/// @nodoc
abstract mixin class $AdminStatsCopyWith<$Res>  {
  factory $AdminStatsCopyWith(AdminStats value, $Res Function(AdminStats) _then) = _$AdminStatsCopyWithImpl;
@useResult
$Res call({
 int totalUsers, int businessAccounts, int totalProducts, int totalEvents, int totalPosts, int pendingVerifications, int flaggedContent
});




}
/// @nodoc
class _$AdminStatsCopyWithImpl<$Res>
    implements $AdminStatsCopyWith<$Res> {
  _$AdminStatsCopyWithImpl(this._self, this._then);

  final AdminStats _self;
  final $Res Function(AdminStats) _then;

/// Create a copy of AdminStats
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? totalUsers = null,Object? businessAccounts = null,Object? totalProducts = null,Object? totalEvents = null,Object? totalPosts = null,Object? pendingVerifications = null,Object? flaggedContent = null,}) {
  return _then(_self.copyWith(
totalUsers: null == totalUsers ? _self.totalUsers : totalUsers // ignore: cast_nullable_to_non_nullable
as int,businessAccounts: null == businessAccounts ? _self.businessAccounts : businessAccounts // ignore: cast_nullable_to_non_nullable
as int,totalProducts: null == totalProducts ? _self.totalProducts : totalProducts // ignore: cast_nullable_to_non_nullable
as int,totalEvents: null == totalEvents ? _self.totalEvents : totalEvents // ignore: cast_nullable_to_non_nullable
as int,totalPosts: null == totalPosts ? _self.totalPosts : totalPosts // ignore: cast_nullable_to_non_nullable
as int,pendingVerifications: null == pendingVerifications ? _self.pendingVerifications : pendingVerifications // ignore: cast_nullable_to_non_nullable
as int,flaggedContent: null == flaggedContent ? _self.flaggedContent : flaggedContent // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [AdminStats].
extension AdminStatsPatterns on AdminStats {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _AdminStats value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _AdminStats() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _AdminStats value)  $default,){
final _that = this;
switch (_that) {
case _AdminStats():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _AdminStats value)?  $default,){
final _that = this;
switch (_that) {
case _AdminStats() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int totalUsers,  int businessAccounts,  int totalProducts,  int totalEvents,  int totalPosts,  int pendingVerifications,  int flaggedContent)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _AdminStats() when $default != null:
return $default(_that.totalUsers,_that.businessAccounts,_that.totalProducts,_that.totalEvents,_that.totalPosts,_that.pendingVerifications,_that.flaggedContent);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int totalUsers,  int businessAccounts,  int totalProducts,  int totalEvents,  int totalPosts,  int pendingVerifications,  int flaggedContent)  $default,) {final _that = this;
switch (_that) {
case _AdminStats():
return $default(_that.totalUsers,_that.businessAccounts,_that.totalProducts,_that.totalEvents,_that.totalPosts,_that.pendingVerifications,_that.flaggedContent);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int totalUsers,  int businessAccounts,  int totalProducts,  int totalEvents,  int totalPosts,  int pendingVerifications,  int flaggedContent)?  $default,) {final _that = this;
switch (_that) {
case _AdminStats() when $default != null:
return $default(_that.totalUsers,_that.businessAccounts,_that.totalProducts,_that.totalEvents,_that.totalPosts,_that.pendingVerifications,_that.flaggedContent);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _AdminStats implements AdminStats {
  const _AdminStats({required this.totalUsers, required this.businessAccounts, required this.totalProducts, required this.totalEvents, required this.totalPosts, required this.pendingVerifications, required this.flaggedContent});
  factory _AdminStats.fromJson(Map<String, dynamic> json) => _$AdminStatsFromJson(json);

@override final  int totalUsers;
@override final  int businessAccounts;
@override final  int totalProducts;
@override final  int totalEvents;
@override final  int totalPosts;
@override final  int pendingVerifications;
@override final  int flaggedContent;

/// Create a copy of AdminStats
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AdminStatsCopyWith<_AdminStats> get copyWith => __$AdminStatsCopyWithImpl<_AdminStats>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AdminStatsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AdminStats&&(identical(other.totalUsers, totalUsers) || other.totalUsers == totalUsers)&&(identical(other.businessAccounts, businessAccounts) || other.businessAccounts == businessAccounts)&&(identical(other.totalProducts, totalProducts) || other.totalProducts == totalProducts)&&(identical(other.totalEvents, totalEvents) || other.totalEvents == totalEvents)&&(identical(other.totalPosts, totalPosts) || other.totalPosts == totalPosts)&&(identical(other.pendingVerifications, pendingVerifications) || other.pendingVerifications == pendingVerifications)&&(identical(other.flaggedContent, flaggedContent) || other.flaggedContent == flaggedContent));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalUsers,businessAccounts,totalProducts,totalEvents,totalPosts,pendingVerifications,flaggedContent);

@override
String toString() {
  return 'AdminStats(totalUsers: $totalUsers, businessAccounts: $businessAccounts, totalProducts: $totalProducts, totalEvents: $totalEvents, totalPosts: $totalPosts, pendingVerifications: $pendingVerifications, flaggedContent: $flaggedContent)';
}


}

/// @nodoc
abstract mixin class _$AdminStatsCopyWith<$Res> implements $AdminStatsCopyWith<$Res> {
  factory _$AdminStatsCopyWith(_AdminStats value, $Res Function(_AdminStats) _then) = __$AdminStatsCopyWithImpl;
@override @useResult
$Res call({
 int totalUsers, int businessAccounts, int totalProducts, int totalEvents, int totalPosts, int pendingVerifications, int flaggedContent
});




}
/// @nodoc
class __$AdminStatsCopyWithImpl<$Res>
    implements _$AdminStatsCopyWith<$Res> {
  __$AdminStatsCopyWithImpl(this._self, this._then);

  final _AdminStats _self;
  final $Res Function(_AdminStats) _then;

/// Create a copy of AdminStats
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? totalUsers = null,Object? businessAccounts = null,Object? totalProducts = null,Object? totalEvents = null,Object? totalPosts = null,Object? pendingVerifications = null,Object? flaggedContent = null,}) {
  return _then(_AdminStats(
totalUsers: null == totalUsers ? _self.totalUsers : totalUsers // ignore: cast_nullable_to_non_nullable
as int,businessAccounts: null == businessAccounts ? _self.businessAccounts : businessAccounts // ignore: cast_nullable_to_non_nullable
as int,totalProducts: null == totalProducts ? _self.totalProducts : totalProducts // ignore: cast_nullable_to_non_nullable
as int,totalEvents: null == totalEvents ? _self.totalEvents : totalEvents // ignore: cast_nullable_to_non_nullable
as int,totalPosts: null == totalPosts ? _self.totalPosts : totalPosts // ignore: cast_nullable_to_non_nullable
as int,pendingVerifications: null == pendingVerifications ? _self.pendingVerifications : pendingVerifications // ignore: cast_nullable_to_non_nullable
as int,flaggedContent: null == flaggedContent ? _self.flaggedContent : flaggedContent // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

// dart format on
