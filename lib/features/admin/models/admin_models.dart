import 'package:freezed_annotation/freezed_annotation.dart';

part 'admin_models.freezed.dart';
part 'admin_models.g.dart';

enum UserFilterType { all, business, verified, flagged }

enum ContentFilterType { all, pending, verified, removed }

// Admin stats model for dashboard analytics

@freezed
abstract class AdminStats with _$AdminStats {
  const factory AdminStats({
    required int totalUsers,
    required int businessAccounts,
    required int totalProducts,
    required int totalEvents,
    required int totalPosts,
    required int pendingVerifications,
    required int flaggedContent,
  }) = _AdminStats;

  factory AdminStats.fromJson(Map<String, dynamic> json) =>
      _$AdminStatsFromJson(json);
}
