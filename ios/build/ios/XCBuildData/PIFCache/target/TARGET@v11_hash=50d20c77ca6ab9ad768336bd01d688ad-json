{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981c3400b11e6515bf4f6102f5dddbb8ff", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9848f2de18393774ea803e1ed1ab633633", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98079b4a2005dcd7af1806b7df1e734a1d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98eb0cc18cf49cc6ee4a49367d928db00e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98079b4a2005dcd7af1806b7df1e734a1d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985f953eb3592f2526247d2db4ac9b1751", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9837c85b047daa520f5712b5711a8fb62d", "guid": "bfdfe7dc352907fc980b868725387e985925020f6d688681687a656771fc4d5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcc23f4c34f74e16cfea8eca39ab044b", "guid": "bfdfe7dc352907fc980b868725387e9843757c4703e936cb53edcc9f11efa956", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e678dd678aa553853ed1aabb030a1de8", "guid": "bfdfe7dc352907fc980b868725387e9882c4a843c6d3c95b22e1ab6c01b2881f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef90878df34edd6ed3f8b38ebe8cffcd", "guid": "bfdfe7dc352907fc980b868725387e982060193675229ad4a8cb280b354f92a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f680f6393eb30573446ce5dd18e90ca1", "guid": "bfdfe7dc352907fc980b868725387e98821f7e2a49a651176954aad7a1e81f0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98929d331978a48138d5db4e2f53e34066", "guid": "bfdfe7dc352907fc980b868725387e9892663e1cd315ba248584881be68a56e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cf7bb48ab93a83a078668dd009d888d", "guid": "bfdfe7dc352907fc980b868725387e98ec14fed1adf7ce6c8dd1c6fba7bdc413"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98369f93aab38b00e2804d77eaf8cabd77", "guid": "bfdfe7dc352907fc980b868725387e98b6b56d603b471d847522b9f44a24354a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98714018a97a4649a747c89d20f264a777", "guid": "bfdfe7dc352907fc980b868725387e98f08d7bfdf0963e450d88fc119b758370", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d9934dcef3b53ffdff9810846704ab8", "guid": "bfdfe7dc352907fc980b868725387e98873fe076d8eb96db32fd4a820f974fd4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c946ad084ba1990123fb746faa1c5447", "guid": "bfdfe7dc352907fc980b868725387e98b4e9b0f388f8e3f84d6568f9aae17169"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c792bb29097ade1095612b26452fbf2e", "guid": "bfdfe7dc352907fc980b868725387e98c1826dabff8743288590c12e4666e453", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c47d33b504f45d3573cdba9d83c222e0", "guid": "bfdfe7dc352907fc980b868725387e9833c24180bb019d30163a84cb8e5a8a36", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98610d4ac3d36dbf187f41e8606eb9fadd", "guid": "bfdfe7dc352907fc980b868725387e98315fc788fd9b7eb09217fa65d0a0a87a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897bd55d0d4bbeb5b4c78e82989dae157", "guid": "bfdfe7dc352907fc980b868725387e983df3b5b59b04e523063ce2e59a73c34e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98369fc84ce7fd63b04bc03bb2e0475e68", "guid": "bfdfe7dc352907fc980b868725387e98c8890a0f3c7e2c93be11815fab4f2bfc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e91769dfd68f1a46b73b676c7ea0bafa", "guid": "bfdfe7dc352907fc980b868725387e980b0e1cf81662ec87f19b961caed0b66e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b5b5c687fd42d4c2c4c9db0b99e89de", "guid": "bfdfe7dc352907fc980b868725387e9805fe9a44b7876f1155b924b7a289516c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892da8cfa3d37c59c439c1a0553984f8c", "guid": "bfdfe7dc352907fc980b868725387e983965788d324dd5c35628daf67450109e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838a29882aff6a65f5489b0b71cf17834", "guid": "bfdfe7dc352907fc980b868725387e985aa322f88b2a7c38016904e5b4ece8dc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98def053770cde7000f95cf8bbcd6d2f52", "guid": "bfdfe7dc352907fc980b868725387e98be569692ae3c0beb32445175a8a9b88a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864583b7b7253d4bf2d9a9e9fe98fa487", "guid": "bfdfe7dc352907fc980b868725387e98126b0df2d3727bfdcc3aaa2d14c6d2c8", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e985dfd8e0d5c560d2bb1029521fb53a2f0", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b332dd0db11c8e28ca46abf22529023f", "guid": "bfdfe7dc352907fc980b868725387e98ec34413383490872d79fe4fbca98ccd1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813fda8be43102dbb4cc4ca31906c9053", "guid": "bfdfe7dc352907fc980b868725387e98fada58543f3824d0346d91782168d632"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb9b337d3d69e997e2bbecec0b03ec63", "guid": "bfdfe7dc352907fc980b868725387e98b72194ac1df8212cf76ad14203ac1d37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b500711ebff8276f54ec5bf91ba34b19", "guid": "bfdfe7dc352907fc980b868725387e98b696f6d9ab3e04c7d4a32b9bbba90fb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e531655d9c165c3fbc6a16b3ff595b4", "guid": "bfdfe7dc352907fc980b868725387e986825c4657f0de698fca8ca60aaa30d30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d099b4314cf4790a0e275837c47e7561", "guid": "bfdfe7dc352907fc980b868725387e9870a561fb4fb392465600b2cc0b835e38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cebf461d177874203a5978cb87141539", "guid": "bfdfe7dc352907fc980b868725387e9823742a54c7b9d964308d4914f1c82959"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b610ea380668798957c684b164e800a", "guid": "bfdfe7dc352907fc980b868725387e98e856a97cf184b29bc6961f8ec337fa53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c96a321e989e5876e647d77625f55c93", "guid": "bfdfe7dc352907fc980b868725387e98ca46b6ad844e7e4426956d6d66074f5b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca573bc569e4620e216519d1ab9b5a3b", "guid": "bfdfe7dc352907fc980b868725387e984147e395ed780c1f2b284bf7a08c5fa6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817224f207480db5355e6457375777fe2", "guid": "bfdfe7dc352907fc980b868725387e9833cd4d15df10c93d3d3d56a82f2fe131"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3b59cbad7363c9985e5c1b87e76185b", "guid": "bfdfe7dc352907fc980b868725387e986443c183ae8ddcfe2786584499367f58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860a8d8cd7fa01737257231ba5b368e8a", "guid": "bfdfe7dc352907fc980b868725387e98f24b674e5155adea49229a8cccf7c766"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b6a54a35a775980eb46f4049caeeada", "guid": "bfdfe7dc352907fc980b868725387e98acd5b7eef969ca68c1a2c82639bb8c3a"}], "guid": "bfdfe7dc352907fc980b868725387e98fe77d296f7ef1a13331df388756128c7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e98aea1ddcc5b7fd7fda54a77e27172c092"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e51514501e0f0d62f7ef6096d26bdc0", "guid": "bfdfe7dc352907fc980b868725387e989d7f80c3159600c8e6864a0f3b3def7b"}], "guid": "bfdfe7dc352907fc980b868725387e98f71ee5456329a12194686e999f8263fd", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e988cf9408f8ad95f793f7cb67184fb36c5", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98e9422ea3b10dc52853fcd72b2cf801df", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}