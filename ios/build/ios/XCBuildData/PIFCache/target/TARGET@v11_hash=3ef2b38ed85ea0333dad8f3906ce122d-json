{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986944f40c8c797e913de5faac6d6b76bf", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983036d6ba1a6e4158e2ac729c2fc47f04", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98aff698e66e099b2cd71bb790b1d98f88", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987fe8e331ae95f8806aaefc9d75fb9de0", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98aff698e66e099b2cd71bb790b1d98f88", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98071282eb167887535a1b8a21f272b9b8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983646584783b7eab94aa783950560da99", "guid": "bfdfe7dc352907fc980b868725387e982d43a11a9981ab88350755a6165481d7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984247ea0ed398f8dfa2a55a8f87b6281b", "guid": "bfdfe7dc352907fc980b868725387e9872784053ef0ac86e3e0a4c7064f99215", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98096c91dfbb6c219d1be6893dc6686400", "guid": "bfdfe7dc352907fc980b868725387e989ee5619a7e040598196a3e3d1dc383b0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6ac11d55780fbfa1280a5f3b33f93bf", "guid": "bfdfe7dc352907fc980b868725387e9811a09cf33b7f0889f4affdb3803794f9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c42b3d76347ea52bacfb1d852f4eeceb", "guid": "bfdfe7dc352907fc980b868725387e9811563a6480c67945db3d9816b49967d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8c664da28fe0089f75bb4b7206a5bc0", "guid": "bfdfe7dc352907fc980b868725387e98718c12fd779c5d009ccebe83f815d6d0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a9517350f6f29eb693eb1fd6247e585", "guid": "bfdfe7dc352907fc980b868725387e98e56d7c349f5e1b0c92d23ee7a97a8d6b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989563571712cdc9f4ecc149e863f35f01", "guid": "bfdfe7dc352907fc980b868725387e98f9bbe5fb314e048144041e7ca70f1e6e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835cdbc1b61a693b0ce4c59cca79fec42", "guid": "bfdfe7dc352907fc980b868725387e98def9531f3eb06d876bfb0bf08b696832", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aca20a629d9118b0aa5af4cd1c76bbe0", "guid": "bfdfe7dc352907fc980b868725387e9872a865f171c183cd192d8797d1cc43d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e43951fd1f3ce2c0a60b4ba154d12b0", "guid": "bfdfe7dc352907fc980b868725387e98ec879ee76ad5151804ebaa300cfb1391", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d171b083f390d1087369c5fd110a040", "guid": "bfdfe7dc352907fc980b868725387e9857651a04132555bce8f1a816ccd713cc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886c967b80aecda752f107e2bca552885", "guid": "bfdfe7dc352907fc980b868725387e98a29715c4261c71a976778679d661b04d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ed2ccebfb0ac94dfbc0220d0a925be0", "guid": "bfdfe7dc352907fc980b868725387e9809cac3b747e2a6673b8a8940cd2803f7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a493e06c8a9496795be6628966c75790", "guid": "bfdfe7dc352907fc980b868725387e988cef5c20e424faeaf5e067bf72628c1e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bce94ecf81accf175e62eb228d9155c6", "guid": "bfdfe7dc352907fc980b868725387e98fae3f9a324cf5ceda39ff4cb49291889", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cd0e088a50fa2d5e24248517cff0de4", "guid": "bfdfe7dc352907fc980b868725387e987e301ec30e863aa5a2de0e74addc940f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c51452ee3cad388049f9426c57d8520", "guid": "bfdfe7dc352907fc980b868725387e9871f9f87ff3a4815cd5244cc6281252d3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cab725a38bcac580bef76278f5aac2a7", "guid": "bfdfe7dc352907fc980b868725387e98f9f3b8480ccb29ffacb0cab86a932a33", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d18461c52a0c51acc30deb3e7e27a4fb", "guid": "bfdfe7dc352907fc980b868725387e987bb0e5ea9f3ce3b25b79ad16cea48d4f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb8d1b287fbbe006f753a25daec25f05", "guid": "bfdfe7dc352907fc980b868725387e98ef5af0eb2b94c9be49b0ec8cc77f659b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98278581d03a7032cd99919b48aae58e9a", "guid": "bfdfe7dc352907fc980b868725387e98bce8744f20b3776153e8ee39d3154312", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aba7eed81175888bdfefdc80eafd8440", "guid": "bfdfe7dc352907fc980b868725387e98456caf14d3d9c8c2bd56e538d67b2571", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98193c5bd1301c1d79abb41f36964c109c", "guid": "bfdfe7dc352907fc980b868725387e9887c0a42c0b90813bec9b45da21c63575", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849ef58dd1981dd5b5da54491c3a2d74b", "guid": "bfdfe7dc352907fc980b868725387e98c29ebd2307a54f5faa8cd0b10ec41511", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892df517fcf40dcccd51b32fa344c0659", "guid": "bfdfe7dc352907fc980b868725387e98185d909132ef83d43745bea64f0cfcb4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f4758ed7636659fc323b0b66c7aa36e", "guid": "bfdfe7dc352907fc980b868725387e98c3c5fdbc77f81cb2de983793b64bd10c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2b737cea0922ef7b434a8557af474a1", "guid": "bfdfe7dc352907fc980b868725387e9845295cbefd33f039c096e3e8cc8e8111", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b61953f01c697ffc4c8ea37b50d15b7", "guid": "bfdfe7dc352907fc980b868725387e9862badf53591742858178fb46395552fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e86a981e590cf75a00c4bb02a8b96f7", "guid": "bfdfe7dc352907fc980b868725387e981a34431be750b82158e45859b09df0e3", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98266b5917eb8ec9b4f2c6b5f7c6b6ad40", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988b569acc78ac887ecf9a3b019ad60906", "guid": "bfdfe7dc352907fc980b868725387e98dc3e5f083c25eac3e21bbe65bc1fd2a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbdc1eee51ecd799ed8343a80155521e", "guid": "bfdfe7dc352907fc980b868725387e9813b3a16cfc13652f830ce7194c80ca37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6f181a9268bd646b21e61c11dc19e5c", "guid": "bfdfe7dc352907fc980b868725387e98e865bbb71439f56ef047601dafbb31c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848da72ebd0f100d08c52f4096d64df74", "guid": "bfdfe7dc352907fc980b868725387e98f71303504b84ef1b30e572333dbb4fe0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c9a0a96d80eed9de20ba7b7d2c8143a", "guid": "bfdfe7dc352907fc980b868725387e98a66f6b30eb2401b0992bb7d8e1b063fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1c5589fcb0ed13a00eb7dcf4c88eae8", "guid": "bfdfe7dc352907fc980b868725387e98ec0268dfc614d759fff0bde3d248fab8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98816451e9bc89deed786809c72378f729", "guid": "bfdfe7dc352907fc980b868725387e985a105aa4ed412afc0688afcb9049e0c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8087b0f83de88213d7443c5d8624bf5", "guid": "bfdfe7dc352907fc980b868725387e985372170544f385f03ac3a4dae5ca7f6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862bdcd2344095da7c5592e2be8ff5fc1", "guid": "bfdfe7dc352907fc980b868725387e987e5013ee1b6c3f5ffb0f2ecc227c0866"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98972a5f01f4b7fb79a7876dae36ca43dc", "guid": "bfdfe7dc352907fc980b868725387e98074f56549f4a9e9cbf7344a32c03f77a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b022592d75279da3d70b9e9bddfb514", "guid": "bfdfe7dc352907fc980b868725387e98dbce7d7d092fc9a902cd0ef7d8c10675"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824094ef9bd24a2099668d531df972f04", "guid": "bfdfe7dc352907fc980b868725387e982fde76b9ad775216304459d3b27b3201"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ee9f39619e80abcd61af2a0a8123e57", "guid": "bfdfe7dc352907fc980b868725387e98cff6306e9445273e12ecf25cec5b8127"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c3f1e85f680eafd65d99efb95935f36", "guid": "bfdfe7dc352907fc980b868725387e98742110fb7ab8424f4508154d03bb6d2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98069bc1579b77100c74f5f8b40746b41c", "guid": "bfdfe7dc352907fc980b868725387e98ddf7cef4a084eb321a5aaff2a6f7c0cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98113bda1735c7dbc4ac35e39a98e0b568", "guid": "bfdfe7dc352907fc980b868725387e987657e739370f812dc7aba9b9789222e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98083b916aa6c5c71b7020f76ae37c8950", "guid": "bfdfe7dc352907fc980b868725387e980cc9e41cff9794c99e229b275a300a6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a3ab220fdc14d9f535fcf260121d0e0", "guid": "bfdfe7dc352907fc980b868725387e98c6fe982801da1569932728da77fdd662"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d2c18c5f2c7b620e9bcf25ba7120800", "guid": "bfdfe7dc352907fc980b868725387e98ccc718ccb19cf9503e812a0397f27c5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df60d3a26a28cebb737d73590b592837", "guid": "bfdfe7dc352907fc980b868725387e98bdc300697b660efb08463050b9065ce2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98485e59aad6c78e5ec517654a5972f1a2", "guid": "bfdfe7dc352907fc980b868725387e98641e0d1d62dd42ced5567e5c1545e05b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846d15c5539bbbc4f0d4a962a27e375e4", "guid": "bfdfe7dc352907fc980b868725387e98918d2ed56f8d28717b00eed2b9ec5676"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98454b7cba09374bb2b51d15835a308690", "guid": "bfdfe7dc352907fc980b868725387e98c96c4b41820cf94e3471899923f1c9bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c330fd2b449ad6ca0fefb0116ab5c08c", "guid": "bfdfe7dc352907fc980b868725387e980daba580bbc7f8e1059c925fba1d1ccf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bbd4dc4a41bd159556538139c28c05a", "guid": "bfdfe7dc352907fc980b868725387e985743852335e2aea90a3bf9779bd55738"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a03698e34256414b7122aa7972544715", "guid": "bfdfe7dc352907fc980b868725387e98456e34634a5a5279a73a535d92cb9998"}], "guid": "bfdfe7dc352907fc980b868725387e9864ce370629571d62d0fee0ebeca91ccc", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e983731a5d8d93b4c572a5131c12ec6ae96"}], "guid": "bfdfe7dc352907fc980b868725387e98d1ed702b66739cdfbd2ceff7f37d5c50", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98145377c82b4442dad4da20d89ddd5e12", "targetReference": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340"}], "guid": "bfdfe7dc352907fc980b868725387e98161f63c794389e910e63ad0a2a6b22f4", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340", "name": "FirebaseFirestore-FirebaseFirestore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98919212c22943df12241906dd601cdff4", "name": "FirebaseFirestoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}], "guid": "bfdfe7dc352907fc980b868725387e98c075cc473fa5680b867d51f1363214ff", "name": "FirebaseFirestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9809dfd848e2259e061e90089e1647f5b7", "name": "FirebaseFirestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}