{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98017b32e51e5f42fcf86e44b9412e621e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9815216333d71ee9e77c70e1880eb2e332", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b28c89be589b8e2450537a538e4a3ee3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9808a97d77e13ce31e24b52efa1f41e9db", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b28c89be589b8e2450537a538e4a3ee3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988da5e5f0fc58a8ac1497ac0a76a0a646", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9863dfa8fefda7f66e8a75ae112823d990", "guid": "bfdfe7dc352907fc980b868725387e9897c3a7689ce1b5838146e2fac3ade674", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803baf5008195ca7e22aa92d49318faca", "guid": "bfdfe7dc352907fc980b868725387e9854e86171449c985ab77e26874ac9b020", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98444be44d796f7d3f5495675826a82d4a", "guid": "bfdfe7dc352907fc980b868725387e985a5cea04f9781a9bbc40e84e42dcc7d0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98112ec2d63a840e1f981a0188980b25d8", "guid": "bfdfe7dc352907fc980b868725387e98ecbe6d201a1224b931f788fd932a15e0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f530a42aaab1dddb5718a42ec2b82718", "guid": "bfdfe7dc352907fc980b868725387e9836e5821e88de770e642f282583003b89", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1c02031bcf18d125de2b95ba099d23d", "guid": "bfdfe7dc352907fc980b868725387e98918a2e140cceee9039bb7fbdc11a8521", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcb4a73f497a2d5a31191b9d856a916a", "guid": "bfdfe7dc352907fc980b868725387e98295bd87cfddc86b7275f8f0e69170df4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98319249654d823803a7ab637952492e05", "guid": "bfdfe7dc352907fc980b868725387e98e16e90c5ba5a2fc8767b64ed9efac1dd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827a93e3257289d2a490ee90a7cb1ef69", "guid": "bfdfe7dc352907fc980b868725387e985479bf62e182870f2ee9ade8f67ccf27", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a82708abc1b9c073141767cb9a7d79c", "guid": "bfdfe7dc352907fc980b868725387e98b1c1f51a978820e6990a1632d232c225", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b7d5460a3606dfa8f2fd08252da772b", "guid": "bfdfe7dc352907fc980b868725387e98c2bcbebd3afe52599245900d99f7c294", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98c1bc810d572ce21130f0c17726e21276", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a1924ea7a96b67bc83532df0a86f600c", "guid": "bfdfe7dc352907fc980b868725387e9863f887fcd8cf6376debcc03909f2b7d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bff47cfc1c6bfb1d0f3816d96430595c", "guid": "bfdfe7dc352907fc980b868725387e98e884b42e315fad510399144c24f236d7"}], "guid": "bfdfe7dc352907fc980b868725387e982b42c590bacd1246ed260d33c51737d9", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e987f23e8ab320feff78e8cadc7218191d5"}], "guid": "bfdfe7dc352907fc980b868725387e9838071733551b655aeb6eae8501253fb2", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98a9280432cad30ecc74830795e868efe1", "targetReference": "bfdfe7dc352907fc980b868725387e98c04ead258c2ba3f656422d1784107881"}], "guid": "bfdfe7dc352907fc980b868725387e9881b56803daa0569de0be0e7dfa5d71a9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98c04ead258c2ba3f656422d1784107881", "name": "FirebaseCoreExtension-FirebaseCoreExtension_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98311e6292af5af43c801705cd189cc184", "name": "FirebaseCoreExtension.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}