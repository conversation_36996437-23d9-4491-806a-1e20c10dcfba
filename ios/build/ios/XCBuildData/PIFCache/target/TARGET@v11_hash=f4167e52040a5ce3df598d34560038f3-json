{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c11483edb96148811ea9e4332dafbfe3", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PromisesSwift/PromisesSwift-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesSwift/PromisesSwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesSwift/PromisesSwift.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Promises", "PRODUCT_NAME": "Promises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.2", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980ca48f83063ab8c1f8b0d060b137bd0f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dd650d10eca9ddf68739db5a938df77a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PromisesSwift/PromisesSwift-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesSwift/PromisesSwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesSwift/PromisesSwift.modulemap", "PRODUCT_MODULE_NAME": "Promises", "PRODUCT_NAME": "Promises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.2", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b37e0b06853e2198ffbad6dfa8248e82", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dd650d10eca9ddf68739db5a938df77a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PromisesSwift/PromisesSwift-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesSwift/PromisesSwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesSwift/PromisesSwift.modulemap", "PRODUCT_MODULE_NAME": "Promises", "PRODUCT_NAME": "Promises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.2", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984f32d7e12eec91eb535e2da40e9f3320", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cc6e63169948532d8517a3f0815d9222", "guid": "bfdfe7dc352907fc980b868725387e98ff8fe549afc6d52ee29149f49aa7872f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b728b0453900d78d6c0b2499f8568b0b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9876c1b4d3f7747b51b06fc14a68fc099b", "guid": "bfdfe7dc352907fc980b868725387e982395b0fb462778d6e4ba5c6e06a9dec3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ec05cc717cbd0faeab0e427f3f64871", "guid": "bfdfe7dc352907fc980b868725387e988dff781cace377cc8f492c0d044dfc1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c969bc4af793c6e7eb321913ed84a8b0", "guid": "bfdfe7dc352907fc980b868725387e981c9e072b3ce2bd6bc2f2ef53caeb76ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804231b58cabbbe804783c135d3817890", "guid": "bfdfe7dc352907fc980b868725387e98527f481cfca21ac7350bb70b297170d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809965ffeb88797a914c35827d5ca552e", "guid": "bfdfe7dc352907fc980b868725387e9832ee8584aef2f87e7d7ce664489d90f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fd474ab48b6ab25cc6de3b22708627f", "guid": "bfdfe7dc352907fc980b868725387e981b18282317d795c3bd299a59193bc170"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4a6b883f3915725369f8e79295ccc51", "guid": "bfdfe7dc352907fc980b868725387e988d3928298b57fe093503ea5b6aa0457f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98568b47071a2d73f8b7732200ab0c91a2", "guid": "bfdfe7dc352907fc980b868725387e9880cb8f12ffe9354ca7b0d6fec97685f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdf65233795c80977d313a06028e2492", "guid": "bfdfe7dc352907fc980b868725387e9807b9df8b2e37d89894baaf10cffb3160"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980af32e6756e3058f29e27f338c9cf8f9", "guid": "bfdfe7dc352907fc980b868725387e985d44aaddb447bbaf6456998ec7b94b93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afd299e9fc84940a6801e7d761e7eb8e", "guid": "bfdfe7dc352907fc980b868725387e98c3f38e63f2209c30d990e3a7c2292b2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7cb72bf421152607e6b0420404e4ec1", "guid": "bfdfe7dc352907fc980b868725387e98712e3f58511e00bbb132a35353b07169"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd3563eba521d634b5e72b548b5666cd", "guid": "bfdfe7dc352907fc980b868725387e9853de52056eb09a7e50f6e398ad2829b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e02038ab298a35e2c3e64cd4f628cbf0", "guid": "bfdfe7dc352907fc980b868725387e985464ac8e3072f205017ba869e13d0935"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fde6a54469bb580e3721fd523785049", "guid": "bfdfe7dc352907fc980b868725387e986e5aee29298e99686c7556feb7acdf18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d19290fcc6b31178f237ea7e23144b19", "guid": "bfdfe7dc352907fc980b868725387e98f8b95d26c8d1188e5290d951ff55a56f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9b7f15492f542d83a41342a87a97f68", "guid": "bfdfe7dc352907fc980b868725387e98f645ec6cf699c45c63495df70fd1969e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b16ab7088cdad4907b7740cf57f4c85d", "guid": "bfdfe7dc352907fc980b868725387e982d74876635144ad18d1a0096f1d4162b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816eb50c342e7ad66595c683a11f57569", "guid": "bfdfe7dc352907fc980b868725387e98a8ef00802c8eff5587c90edb8d2c0831"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0bf54ca894c1e2a1695d1d8adc1dec3", "guid": "bfdfe7dc352907fc980b868725387e98c10c9149d860ab7071ce856a7c37feff"}], "guid": "bfdfe7dc352907fc980b868725387e985594edf19bd2cb911ed7a56dd49e08b3", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e9812fc790a983a165ee902cadf0c3d83d9"}], "guid": "bfdfe7dc352907fc980b868725387e9838ce4a4af0b8ad8cb05d50c7747ff955", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e989fcf3ab16792add8f765cb8265bf99ca", "targetReference": "bfdfe7dc352907fc980b868725387e982423904c0fec8d69fb48f8811a58f1b3"}], "guid": "bfdfe7dc352907fc980b868725387e9821b80a1ff78f6c853c98985c964090dd", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}, {"guid": "bfdfe7dc352907fc980b868725387e982423904c0fec8d69fb48f8811a58f1b3", "name": "PromisesSwift-Promises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ed40b4d6efca84b18a65efda8999ea5d", "name": "PromisesSwift", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e982bfe7b75487d9ef7158f28fa2f89d57f", "name": "Promises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}