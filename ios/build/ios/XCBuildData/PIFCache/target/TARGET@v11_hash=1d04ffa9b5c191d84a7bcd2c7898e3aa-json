{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982f0cfa1f37f550f56008d18fc58d97bf", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98efa895a5fbd2702528ffbe2a08141ec5", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9888830edafd4826ecab1185b64635f2c2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832afea3a2dd5ed632435d0e8f4426b94", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9888830edafd4826ecab1185b64635f2c2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98afdad605084d291a0912311bab87675b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9896f7f6aba04d6eaada70fb31d08334f3", "guid": "bfdfe7dc352907fc980b868725387e985ed8a02a1aa494d7eb60d93b28064ddd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c96fe0ada80d4e9873fe1a3993e2d83", "guid": "bfdfe7dc352907fc980b868725387e98f4db200f4394a57a021dd46cd725b91b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877c123eff6484b262e00520836a6145a", "guid": "bfdfe7dc352907fc980b868725387e98b86a9c96e7882feb86e383e62f9be121", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9890ae9690a1d7997d325220185099bb78", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98df008776b160c527c8dd0a0dfc5272d7", "guid": "bfdfe7dc352907fc980b868725387e982075b7f164cf46f30cc8a7e37bf2f049"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f8a3b52fe81748cd07c1a57d5ff27c2", "guid": "bfdfe7dc352907fc980b868725387e98f71449b9d1e7496716ed2ec23d52010e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829c98c57be90037531ff63a6d858676e", "guid": "bfdfe7dc352907fc980b868725387e98f8c48fee2abce498826115b89cfa785c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bc9c3f0b102f3cfc1f21d5235703b86", "guid": "bfdfe7dc352907fc980b868725387e9836e221226e78dc34dd3a6085599f22dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838770d5c172effce010c041ecdee8399", "guid": "bfdfe7dc352907fc980b868725387e98083ede22be9d8e0ca84418fe0199d7ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98113049e8f5397d61329c7c6bc8e16605", "guid": "bfdfe7dc352907fc980b868725387e98250fa796c7afba9b549462d7d40eddf5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d1165e961eeb00a68820d2380560fc4", "guid": "bfdfe7dc352907fc980b868725387e983e6ab7c95877c14e02ee1e7b916f13cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fff6ded4e3ae5d6bdfeed70638152287", "guid": "bfdfe7dc352907fc980b868725387e98c390a0d15f5896b64cf6b7edb179079e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8f1b57eb739ecef019b588d33b7bde0", "guid": "bfdfe7dc352907fc980b868725387e98a850aaa43456422a1cdf86747a2aa13a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989456214442c969344eb2c8d58ab2aa52", "guid": "bfdfe7dc352907fc980b868725387e98ef995848c7102243200eb3f67fd1d20f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d73c46873f256b28709436609da4159d", "guid": "bfdfe7dc352907fc980b868725387e983ab5e97dd81ff39ad23497a8c9bf714e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986207c677cd7148696b924e71bc64efb3", "guid": "bfdfe7dc352907fc980b868725387e989a20121ca988af63b3a13c449f8cc7d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f8ab750121c59e61f9c3e179df59fdd", "guid": "bfdfe7dc352907fc980b868725387e9848d4bbd15def055eab87fb02b14f860b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816bcc807adb8fe09589aa7f0c6e0f4e8", "guid": "bfdfe7dc352907fc980b868725387e98a65b54bcb8148455752b04c12fce429f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814de56051977c576b03609db96befb29", "guid": "bfdfe7dc352907fc980b868725387e982862d77e499ceaee91363cbb6225d001"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d291cdf6928a1394aebf65a715d0f4f6", "guid": "bfdfe7dc352907fc980b868725387e98bbc49d4142e67b2edd084b86944e5486"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806748759f45de9778c517fcd36e7653c", "guid": "bfdfe7dc352907fc980b868725387e987f3d3b80417487b2de067ebaddd9aed6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879392ca885e1cd6f3472697847da3057", "guid": "bfdfe7dc352907fc980b868725387e9819ac6b0b7fd02afbe9118313630d836a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db478099bcbea03b2efaa78fa6511be4", "guid": "bfdfe7dc352907fc980b868725387e987354aebda443ac4d7be2a9d99db5c0c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9dc332bd31278bd58528028a9f81c40", "guid": "bfdfe7dc352907fc980b868725387e981fe54338d2424dd431e36101fd95147f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857df3f257637b1dff9a1d5139d32af3e", "guid": "bfdfe7dc352907fc980b868725387e98803fbe07be1ef6fd25961ad5c42fc6da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bdef57fd393d7f191f97391a454b7f7", "guid": "bfdfe7dc352907fc980b868725387e98cb97f58c8dac77bf53a32b8beaa8ac3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a80bbd140b48db13e1538f9f13f5b38", "guid": "bfdfe7dc352907fc980b868725387e982f44ea73ea7535b0543e2f4be7b2f6c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98614926b607a187af4d03aecfab13f368", "guid": "bfdfe7dc352907fc980b868725387e9807cde1d3fe5f37d17453c82e48922a76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98200763761bec2f0e7c26565fa364d952", "guid": "bfdfe7dc352907fc980b868725387e98feeaac8c7b9fbaaea3fc803a1dddeae7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9397432d330b36a49b3a384cce3911c", "guid": "bfdfe7dc352907fc980b868725387e980467a645beab2bd8af1b256b97b6577d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b517d53fc9ae0b6e21ddf70c6d61a58a", "guid": "bfdfe7dc352907fc980b868725387e9898eb00a26de4a102d5e66c85656269e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a23cbbe3cb2ba8f8ba68f0a6cb357de", "guid": "bfdfe7dc352907fc980b868725387e988911dae723a2f569b5bef33d4b23fc3f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bed4d4c9dc54795858e7c229ced5401", "guid": "bfdfe7dc352907fc980b868725387e98639b003e9e370e26345c0e98f9c14fea"}], "guid": "bfdfe7dc352907fc980b868725387e9868462fbf95ca98d0de3c793e7412cdd2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e98623e067d88964890d513c3432d5b1a87"}], "guid": "bfdfe7dc352907fc980b868725387e9806fffcca15a621f1af46572589e91e06", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e984c8c17fd4d82f64ce59123696c6f9f5e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98ed40b4d6efca84b18a65efda8999ea5d", "name": "PromisesSwift"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e98424a0579f05b8aa7b116a0e1ae14c72d", "name": "FirebaseSessions", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98a41ba860aa6fc56673ac239987133d67", "name": "FirebaseSessions.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}