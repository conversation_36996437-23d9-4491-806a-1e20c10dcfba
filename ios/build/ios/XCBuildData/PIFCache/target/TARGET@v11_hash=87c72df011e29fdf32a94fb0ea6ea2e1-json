{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983029ce6bf845ce2590feb2d8c8323c2c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98398bac938fb47b84c16f19633c15c92f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989c2442434108842d804e41258a19462a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9839d79a52f24f54321de12379511903ab", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989c2442434108842d804e41258a19462a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9894fef8acb7de742f96aaab49fd64da62", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9846cab8002b7dde4cfb1ba341baa1dcf0", "guid": "bfdfe7dc352907fc980b868725387e980223af070454170a0b7d3b1361ba6800", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b126d59226baf980c6ea47b34732dba", "guid": "bfdfe7dc352907fc980b868725387e98af9baa1a214e18f0740673a53ee3a1e7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884d6b27c2b26bfc6887ad54b25b774cc", "guid": "bfdfe7dc352907fc980b868725387e98d361524d0bbc0eae975162423ea39391", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfbadf8ae3b978eda3bdee1bab98a721", "guid": "bfdfe7dc352907fc980b868725387e98592724f0aead9aed6184ccba926c3358", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829877af27d0fc7a1cb73d14f59dc9c90", "guid": "bfdfe7dc352907fc980b868725387e985f4c9ea9715d308ccf7cfb4fa38d39f1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989df698a43faf5744778980dfea547b50", "guid": "bfdfe7dc352907fc980b868725387e985f4eb06ccf7b0e6b6f62b42e6e567952", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c3a7f04f5f780621111b7a7e5d0a235", "guid": "bfdfe7dc352907fc980b868725387e98200c518de51f12b07c10a1568b24e4a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6bc23868d691544f4b7801273b913a4", "guid": "bfdfe7dc352907fc980b868725387e987423cabde9be5c9ce6ad4211d85a7a9c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863baec5c0af3820045b01d808dc87345", "guid": "bfdfe7dc352907fc980b868725387e986622024fa4c5026bce3d6bedb93708bf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff4b175bfa1a483311b6a37b3251ddf7", "guid": "bfdfe7dc352907fc980b868725387e988baaa201e8200465d5cad48cfc617506", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98710ac1c7bd6ba983303b2279535a2380", "guid": "bfdfe7dc352907fc980b868725387e9875fe1bb25210e274e094b1d5a28f48af", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ef4f59f2b485327ad04631a5aaaa175", "guid": "bfdfe7dc352907fc980b868725387e98a3013bb633bcb8861c4af070676454c4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982392d15eb26d0810a6c7d8c3099a206c", "guid": "bfdfe7dc352907fc980b868725387e986b370a394be613ba5799fa4d11c919ef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843332806e5334759f3710de4825c83bf", "guid": "bfdfe7dc352907fc980b868725387e98fb07394129e8e15c4a276f6c926ae120", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf8a6b20d75b10206d159d5e0af08295", "guid": "bfdfe7dc352907fc980b868725387e989314e081c3f5bfab1279e3c2c1139a24", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983145ad27ab9a5d703e29269b9b2b583a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983b2b3bf821864f6e57ad6c679e199722", "guid": "bfdfe7dc352907fc980b868725387e98462d72be51e2d42e9f0113c685cf957a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f458153c391bb7c274b0f4c4ee05e008", "guid": "bfdfe7dc352907fc980b868725387e989851f21dc1881e64d21e0223601a3ea9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817da60d1a44cbc479bead322a786dc9a", "guid": "bfdfe7dc352907fc980b868725387e988b2b0d3293fe5fa5163e848e73287fa9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fe0b260342d05d418b23aa7d19998b4", "guid": "bfdfe7dc352907fc980b868725387e9878fe5122da43e13355af702578c47c6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c8f842a8f31f5c1ee5436d038d5e4e5", "guid": "bfdfe7dc352907fc980b868725387e98152a413d79815b6b7e16f8222b059af3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f26882c93649a4cb99c6cd9b5913036", "guid": "bfdfe7dc352907fc980b868725387e98d8a60e9dc307978e06d0976c668337a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df3fe53fd87e232a407e992c11e29840", "guid": "bfdfe7dc352907fc980b868725387e985c4b75f26b14508b53e12df60d30b440"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989081bbb972d3335047ad099067620708", "guid": "bfdfe7dc352907fc980b868725387e9870213b2a7f693ef9848866859b406466"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0e3953b1d891a92df59a7c9d7c84489", "guid": "bfdfe7dc352907fc980b868725387e98f6ab12171d8d5fc29fc520257bd34abc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f3debd470eb70b117b83976e95fb439", "guid": "bfdfe7dc352907fc980b868725387e9806ef2c8ff6903129ffb8c9def9edfc4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be126dce40c203ee743ea187f0233349", "guid": "bfdfe7dc352907fc980b868725387e98a89106363ecf1c82c77a5e97d941ae89"}], "guid": "bfdfe7dc352907fc980b868725387e9884539da3bdf2ed4ad426ca202aaf6bc7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e98c1dae7e29df157eddda815ae5462108c"}], "guid": "bfdfe7dc352907fc980b868725387e98119e723bd2257d68f10fcea70feb1342", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e981c10b7a3ff26395a834064a0172d6124", "targetReference": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04"}], "guid": "bfdfe7dc352907fc980b868725387e98a3435bfc3b1be550d78f0653eca9c848", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04", "name": "video_player_avfoundation-video_player_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988a0a5b40b007f81bee1472e4d0fb23da", "name": "video_player_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b5f237537920ce49888f6f7f73c80a6c", "name": "video_player_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}