{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e9c040e4fceedcec633a342fed2fbc46", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/RecaptchaInterop/RecaptchaInterop-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/RecaptchaInterop/RecaptchaInterop-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/RecaptchaInterop/RecaptchaInterop.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "RecaptchaInterop", "PRODUCT_NAME": "RecaptchaInterop", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98de8861e6192d64ea7b2d582a82cb9f85", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a6f227dc7a45efa1de5231d1fb65e89f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/RecaptchaInterop/RecaptchaInterop-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/RecaptchaInterop/RecaptchaInterop-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/RecaptchaInterop/RecaptchaInterop.modulemap", "PRODUCT_MODULE_NAME": "RecaptchaInterop", "PRODUCT_NAME": "RecaptchaInterop", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98357bd1322b3bcf574dff3411994edac8", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a6f227dc7a45efa1de5231d1fb65e89f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/RecaptchaInterop/RecaptchaInterop-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/RecaptchaInterop/RecaptchaInterop-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/RecaptchaInterop/RecaptchaInterop.modulemap", "PRODUCT_MODULE_NAME": "RecaptchaInterop", "PRODUCT_NAME": "RecaptchaInterop", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bf0218de7409435a650cb6cb544a95ad", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a0551d0bb32a01506008a63b0a182c71", "guid": "bfdfe7dc352907fc980b868725387e98ac480640b977a0e4e0f49de6449e580b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f2237139c9b7cc28a36bf08a04e610e", "guid": "bfdfe7dc352907fc980b868725387e98641699afc09567a7f255800a899af7ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827e10ddc4f97d518d7ff49b887049a41", "guid": "bfdfe7dc352907fc980b868725387e981b34a545ae64548f733ca0119bb2e93d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b11c795c27b6906cf7283cd12306024", "guid": "bfdfe7dc352907fc980b868725387e98315e4bb068acc4864887654b1e8c413d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9e60f059651aafbe81cd6ff55178c48", "guid": "bfdfe7dc352907fc980b868725387e98cfa23d1541db88c34ad79f5f6ef99d5d", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98ad3c8655943de5fc9196a6d1d2f5892e", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98126c3b9583be6dc59daa8922d404c202", "guid": "bfdfe7dc352907fc980b868725387e981691de3b3a00a512f6a88f3be513e98e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833e49681cbbd1b221cbaf2d9e38f3ea7", "guid": "bfdfe7dc352907fc980b868725387e98f0e7997f6313a0db971212192492c7c8"}], "guid": "bfdfe7dc352907fc980b868725387e98b4163a95cb8d6019d131f3e4292e486b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e98447a19c904c06f29907a790b5358a1f9"}], "guid": "bfdfe7dc352907fc980b868725387e9886ff6e736ff2621a4e5b435acfd502f5", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98ad0450a8c9ed60024874c744d6d9dbc2", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98da29652de71b686743df2bd56decf7ef", "name": "RecaptchaInterop", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988c2a56cd963a48a63ab689fe60f47236", "name": "RecaptchaInterop.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}