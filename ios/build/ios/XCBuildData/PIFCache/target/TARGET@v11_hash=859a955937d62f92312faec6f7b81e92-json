{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988f6228e19ea3c1ea2075916fbd073280", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98af0081f877d8e4eb244d9e5c9370fb37", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982fd07630332d963150170cb53a0c22ad", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fbe8a38de7768280821f1c009619a94f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982fd07630332d963150170cb53a0c22ad", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98339acf9be6e8439ef18fa28c33bdfb85", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9865d1c630dd6ff29e14459e0cbdb205ae", "guid": "bfdfe7dc352907fc980b868725387e98a6fdd76c5e6c3a26024ef5e772063ce1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3097c62d4445eaf3cc04d457b4ba08b", "guid": "bfdfe7dc352907fc980b868725387e987caaa70717dd6d5362e0838aaf5ac823"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877ba53b9c2c1cc38d7bfdd86dfe72eb2", "guid": "bfdfe7dc352907fc980b868725387e98ced2e0a31564943ff9c69ba587c5a886"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98289cc04ccd7a71977865d3479be16601", "guid": "bfdfe7dc352907fc980b868725387e98340d7dc9fb3966c78737a378850db82d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848c51be01d5696cf2f9fb6f689883a9e", "guid": "bfdfe7dc352907fc980b868725387e98b631792e457ac816eb3979bc522aec8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ebc2ca07ba1b82e7fb2c85cf36c8bb3", "guid": "bfdfe7dc352907fc980b868725387e9894a55a5ff6c22db1d7c029f9c7f46f33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837893fcdcb26ed315cb6ea43f2c20028", "guid": "bfdfe7dc352907fc980b868725387e98d5a1cb051a750408db1edf48095632d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877e4a827c74fa1b5a4f8724d33d0a4cf", "guid": "bfdfe7dc352907fc980b868725387e981232ba1a110d5dc5e43d0357b02db098"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f3b229140e145ede61e37851ba6e119", "guid": "bfdfe7dc352907fc980b868725387e984ed700ca5b53c8303ae8700d4aebde06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98429c1d1948d3a1e956ee006bf667caad", "guid": "bfdfe7dc352907fc980b868725387e98f71cff978ea719de720bfddc71a5c041"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988857a3c7af3596247adf76dd2c6469c8", "guid": "bfdfe7dc352907fc980b868725387e986483e0d788b1adc53af962da3e774b58", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98433526b62e893e0b60b2c8271b886e8d", "guid": "bfdfe7dc352907fc980b868725387e98e526c583c3a2f7da13cf9f18d6242c64", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9a097f25ac5e7fec28810d09d24cb2e", "guid": "bfdfe7dc352907fc980b868725387e98956bbb7322b08897b9ab532001fceb83"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98489788fafdfbd28162eaac10e3bee3cb", "guid": "bfdfe7dc352907fc980b868725387e98556b589f3e8418cf3269cd6ed9d9dba5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0e1da40c4849d0977cbd1c069243300", "guid": "bfdfe7dc352907fc980b868725387e982cb2cfe53669fa6829e854d4d58ddcb5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983abfb0c511def665f9543dd1781d15bc", "guid": "bfdfe7dc352907fc980b868725387e9805363a0d1b20ac4fa54a79c42abb1172"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a84472e5f701e1b10a234ad82a302c0e", "guid": "bfdfe7dc352907fc980b868725387e98dd43dbf505ebdd30caf6d910cb6f6953"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d73b9c93be6d4a03716a33fa8224afe6", "guid": "bfdfe7dc352907fc980b868725387e98226e949eabdce1515ead9bd03fed6405", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e931cc353dd9614347155428190cc19", "guid": "bfdfe7dc352907fc980b868725387e98832654a60c3ed8513d922839dc1d6530"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bb704d024f7ff26ec7f967793262737", "guid": "bfdfe7dc352907fc980b868725387e9893d6140d0721d959e284aeef5b2a8881"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df9b1da5444e4457c9a2dff8ae40e620", "guid": "bfdfe7dc352907fc980b868725387e98f5ad8f7e817fe07b7749a513bbd6f710"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c36ebdc3ab3653e871162dae8c33de7", "guid": "bfdfe7dc352907fc980b868725387e98afe408f449413edc07352ca551e2b031"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98670075f902cf81ad38328428a91a5e7d", "guid": "bfdfe7dc352907fc980b868725387e98983ac1e0420300a083fa25a63751f31f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a7c67428f804b25e9985dca2be5db50", "guid": "bfdfe7dc352907fc980b868725387e98405323fde63e4e5053dd46cfbdbc14f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822e6ae96bcd4f2bd2ccd21be3e02355f", "guid": "bfdfe7dc352907fc980b868725387e98a58a7eb568f1f1528232d3fad0ea2078"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f36dfbf703760f024716c1760942306", "guid": "bfdfe7dc352907fc980b868725387e98aa0037607db9e59070dbca4792f275b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860696b98b4568e4b90dd87ac48e649cb", "guid": "bfdfe7dc352907fc980b868725387e98d034b93733a009da76b5c6c2b910b689"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983daad98e1dfe2457e21f9d46292404f0", "guid": "bfdfe7dc352907fc980b868725387e980a8ac17c36bebe774b2e54974f3a602f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f58d2239ea7cbd9483349fbf76f3845a", "guid": "bfdfe7dc352907fc980b868725387e98999889d795550ccb82b730a909d38174"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca7a6bea4f702806516ef7abae19f855", "guid": "bfdfe7dc352907fc980b868725387e9851c1f35d8f25dd16bab6ff7a3ee66adf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98892fdb40c30019f414f074357e1c75f3", "guid": "bfdfe7dc352907fc980b868725387e9873c095f34898b12ee097a70f808f7288"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869c51ca72ff2853ce333126bdcfca445", "guid": "bfdfe7dc352907fc980b868725387e98f60bf3f3ca1bfc9eba173b79b3d9be13", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a94132edc9de6cce35c5e529d6eb46c", "guid": "bfdfe7dc352907fc980b868725387e9895572108b552646a369ff9b114e87d77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813f72fe417c5222de1c0d42e37e0a945", "guid": "bfdfe7dc352907fc980b868725387e980ab3ad3b13025a120930a739a7643420"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ae367afb03bde8d1487061e4997a2e1", "guid": "bfdfe7dc352907fc980b868725387e983ce9fb9ce5ff973a2c7bbaf139911d38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d26b00acc97e66170cd344dd7a3a393d", "guid": "bfdfe7dc352907fc980b868725387e9880615c64369d1e5503287fcdda63fb54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fff98b1244f03a58e74b587a30ce124e", "guid": "bfdfe7dc352907fc980b868725387e986d04207f05cccd0060b4ac1b03bdebc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7c9f906a454c03dfcb11cf05f17ff46", "guid": "bfdfe7dc352907fc980b868725387e983da4293888462c66f4376fbfbf170ea9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98562e18c0f39c7bc8982afe797f1691a2", "guid": "bfdfe7dc352907fc980b868725387e986efe23d84e405fe63723cd60f72c7707"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee9f7b3271be78ac8e0bdc6f4cf3aef8", "guid": "bfdfe7dc352907fc980b868725387e9802809fa80da178b45810b16bd159b844"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98244b531ef0e9d7ee7a204769c240a204", "guid": "bfdfe7dc352907fc980b868725387e9844d3a7ca3bd4bdd59540262a4ee64f02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980311fecd79dd61bda06c3943c01d264f", "guid": "bfdfe7dc352907fc980b868725387e98f10497b518a9b1ddf46b5d7e7a29c01a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad57701ebc4b4c6b12d7b9ac3ff9cd3d", "guid": "bfdfe7dc352907fc980b868725387e9877aba607c860261a24c07c3e18bbbe4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b9cda6b733697a50ba6d7e76a72fb7a", "guid": "bfdfe7dc352907fc980b868725387e98a88267bcb2b4cbdeef515f09fe461242"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853362cffec5b9591225414689e027cf1", "guid": "bfdfe7dc352907fc980b868725387e98774d07973169a50dfaf4387f70e1daac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824486e8c73a822d620f5253f1d3fb7f9", "guid": "bfdfe7dc352907fc980b868725387e98abc71f61eacdc7260ea3f729413385a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b34f894aea46935910dbfbe0571c40be", "guid": "bfdfe7dc352907fc980b868725387e98ea93175892ab28206e8df372a312b003"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7e7c37e9dcb1dfcde7362b5f7e23250", "guid": "bfdfe7dc352907fc980b868725387e98113a65033414091a4a9a0277cb4765df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984eb2f0a0558917b0aa4ad885792e0b87", "guid": "bfdfe7dc352907fc980b868725387e98ed8c4c73d0a4ab9a15922646039525e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b8f0c2bd175c463bcb029d733887a07", "guid": "bfdfe7dc352907fc980b868725387e981f531209c17146ab07f30fd1c2c0a740"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b8e5f0d1f4e4796147469cb6ae35352", "guid": "bfdfe7dc352907fc980b868725387e982362dfe93afc5931446e45419a86d6f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bff1a018ffcbc4d921bae4811141173", "guid": "bfdfe7dc352907fc980b868725387e98b15e7f031201c783b1320f158a262f10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e43c5e3eb032d529e0b7b9d2edc8fbc1", "guid": "bfdfe7dc352907fc980b868725387e98aef5978694db20dc84e1cf86a37378d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980411d1a2aa415177c01243dba9019df7", "guid": "bfdfe7dc352907fc980b868725387e989684c3f57c0f15f676e70c06981ea0bc"}], "guid": "bfdfe7dc352907fc980b868725387e98320bf21a3618303b440a77b08d90da0e", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9830e4b45e90c659fe4967c238c687ba5d", "guid": "bfdfe7dc352907fc980b868725387e9890efbd934e16b84cee32b4afe931f7d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e1516e49c5e1b4b7a0b9f03f057ab58", "guid": "bfdfe7dc352907fc980b868725387e98b307bd97a9725c60a807149ff8c4ad30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986dda89b637cafdbd76ce52539304320d", "guid": "bfdfe7dc352907fc980b868725387e985c04840879f76d5972ff7cf1e21b8c87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a35282e3272fdc503524c0b3f1b0a449", "guid": "bfdfe7dc352907fc980b868725387e98d156592d352205d0d326f1e27f62978a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98979db98f08e9cba8d77b7364d252bb74", "guid": "bfdfe7dc352907fc980b868725387e98392a1b42dfbbcf050206d491548d078f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f801be72b1d9003462d965f86ecefc0", "guid": "bfdfe7dc352907fc980b868725387e9843a2265d74ba3dcb8636da97c6340408"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b8c9a70eac236ccf4fce38a2c9c3655", "guid": "bfdfe7dc352907fc980b868725387e98f2858ab85d62e6018766942e0254e005"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a40bd6ba261549298b9563305c93466", "guid": "bfdfe7dc352907fc980b868725387e98aae4b2822ae17e5ccd561e89b9301d04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fbfc830c1360163e94878285a0e1244", "guid": "bfdfe7dc352907fc980b868725387e9824dfcdfe8c3ce78fc4df393392164f77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984770dc1364fd1d6e7eaa3ada30169e0f", "guid": "bfdfe7dc352907fc980b868725387e98bd00440e7e7c34cbc9d8985c3a9fb027"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb758e7471491bd26c4a5cdf1695a6d4", "guid": "bfdfe7dc352907fc980b868725387e98cd854cedf4a27554f95a0120eca24cb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d527f515047eb9ddbcaffc682d8fc4c", "guid": "bfdfe7dc352907fc980b868725387e98bc162e87c1560c5b7c24c85b778ecb44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fb10c6ba8af16f2864501a8236d9ccc", "guid": "bfdfe7dc352907fc980b868725387e9865e38b1551b5b5d45154ff1dfcbd849f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ffe86127918827a192b1dec5760ef76", "guid": "bfdfe7dc352907fc980b868725387e9851746afefc01333b2ddea67b13873844"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cfe4fae806fce0e088e6dd55073075c", "guid": "bfdfe7dc352907fc980b868725387e987697ee5909bdaa8f6ac4e58c1d2e8e76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831f2446d05e2105703b250bac6e54921", "guid": "bfdfe7dc352907fc980b868725387e98799b2baf88367aa85b6d973c801a42c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882fad6ad1478f8b7c1493476e9a87e3d", "guid": "bfdfe7dc352907fc980b868725387e983808ac928231e3aad24d76ad546e6e9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985229bf8cb80ad3a5b2df3c30aa4f3b2c", "guid": "bfdfe7dc352907fc980b868725387e98cd0d6977e5ed0e173db6b6066dff974a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98126fe08f44dd28f5fd3338923de6ebb3", "guid": "bfdfe7dc352907fc980b868725387e98f0507d7a7b43ce38092b38cfc05bedb9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987981967f20548e8d4381014d71a4e17e", "guid": "bfdfe7dc352907fc980b868725387e986435d97c015ce4d1d4f38c2bdcf57279"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4117d850940d01e226758451f1d886c", "guid": "bfdfe7dc352907fc980b868725387e9849c120d4f18a074078b4e4de50621e5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da0c51043edeaf7777741c8b8582f52d", "guid": "bfdfe7dc352907fc980b868725387e981d1cbecdb4748d5fd4381dd471ef842f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98274868e92e86523d1f49840a688439cc", "guid": "bfdfe7dc352907fc980b868725387e98a23704bc8c2c3cfb7566df7456504cbc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f0dce685aa2c64c73eed718054e9d81", "guid": "bfdfe7dc352907fc980b868725387e9890a869c4de620acb5508f99d9ec2be44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b45101d9889e1fa529c387360fb0a363", "guid": "bfdfe7dc352907fc980b868725387e98550c2f30de95f1af4287993c889de95a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8d11ccff2097b4d6bd4d45c88729ee1", "guid": "bfdfe7dc352907fc980b868725387e9867ea4005bfac2122d5d38f7c5b0d09da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9ad9230982fec5ad2f14eaa6eec85f2", "guid": "bfdfe7dc352907fc980b868725387e9891dd369fe43797fcd5916cec063dd73d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7120e91ca3c3a038922fb81355ced7f", "guid": "bfdfe7dc352907fc980b868725387e9802cd5d34f323d964429b94dfceff80cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98400264321bb8bb0e8915d3a99f5dc077", "guid": "bfdfe7dc352907fc980b868725387e9806fea879d99f08238708d68d45605d90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a70bdc287cf59b93bbf6a4440257308a", "guid": "bfdfe7dc352907fc980b868725387e982d2167d2645889bf7c653cf7b1c67199"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982878e6a0f1d815634f0a906ece865885", "guid": "bfdfe7dc352907fc980b868725387e98a60f93e183d40cca0f8fa238fe288e56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98189107f34c815fe556d8e40f8cec0157", "guid": "bfdfe7dc352907fc980b868725387e98c766a9f7d64018132d950d7f943f16d3"}], "guid": "bfdfe7dc352907fc980b868725387e9814b30a8c771905218718e86e24776208", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e9866695d6ebe3d92d45db07c1f09f9984d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843a8de0ab70f2c0fbe5d9ba1a1011119", "guid": "bfdfe7dc352907fc980b868725387e9857db37c1ac8f940b028cfc39ef641676"}], "guid": "bfdfe7dc352907fc980b868725387e9868a4a2578ea6935a034a38769203578c", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98ad42491af218d149fb2df1a98b97a9ef", "targetReference": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab"}], "guid": "bfdfe7dc352907fc980b868725387e983cc8d541fcc58e68363bb2f6e54d3e47", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab", "name": "FirebaseMessaging-FirebaseMessaging_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}