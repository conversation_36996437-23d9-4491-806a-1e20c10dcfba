{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9878792191c3b997c2f0070e8d84bd00ba", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98706f9a6907a46397a29624f33f5efe6e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e78e37a506bc8372c21b7e9e362cf12d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d5e92053e8f68f6a03d8ac70ff9ab016", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e78e37a506bc8372c21b7e9e362cf12d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986958d6e1425bb4700ee1c94a528899dc", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e3cdb0f3dfc8cad794064e642474235b", "guid": "bfdfe7dc352907fc980b868725387e98312abf9e78b5ea2f8d47b59d4cf7d71a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98346d3029b96a475f9ff43e5f1be7136e", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dc827fdb5a7038f158bd46ff4c8e0f84", "guid": "bfdfe7dc352907fc980b868725387e98f9a8b3e0d8a865b8f1bc6620965ad4d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98347a5df4066e683c00052498ddf33ab5", "guid": "bfdfe7dc352907fc980b868725387e9801832229a55cd661b701558919798216"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ea765e1576b38f330d7c9267344e1c6", "guid": "bfdfe7dc352907fc980b868725387e98796ea89c8e04eb5e48f364c74db8fcae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984de678ff2d76bfaf9e8857eb13f430d7", "guid": "bfdfe7dc352907fc980b868725387e98c19e6825873ac7ec66e6f8711e082439"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e65e49da46859ea90301a8cd76082645", "guid": "bfdfe7dc352907fc980b868725387e98e9c3473390148e706ae7b9f9b76f0136"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983969325c5849d5d4f6e1efa3fd196ddf", "guid": "bfdfe7dc352907fc980b868725387e981a65b970dfc0ceae6cbe2c8df90551dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d47ac4e0e9995e136ee0009290128e92", "guid": "bfdfe7dc352907fc980b868725387e9897304bb9a577096da320cfd6bf60ad4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a0a3f0a9ba3d880c50231bccf1160e7", "guid": "bfdfe7dc352907fc980b868725387e98299e15ca6941c1dd25c78e37a95447d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec4a0dcd5c4d2f9bd0ea7bbdd15f3229", "guid": "bfdfe7dc352907fc980b868725387e9883e5680013db2a3d352d2641e009d0a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d9efc48365a5244cdb3925c4865fb83", "guid": "bfdfe7dc352907fc980b868725387e98f092f93d65cc1119692484f6c84ab2c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98933fa6937ee04b27539f921fd39a19a0", "guid": "bfdfe7dc352907fc980b868725387e9822d2d5f69acb940f4c890edd8a242fe9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae73e1b4ef1c44170180b517dfb5cdff", "guid": "bfdfe7dc352907fc980b868725387e98e06f81e65e91d6253053664d09ce3077"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f509079d8774c04c87f98feb4456ef0b", "guid": "bfdfe7dc352907fc980b868725387e9894ef938a7d2f1282141445d7d55a94a9"}], "guid": "bfdfe7dc352907fc980b868725387e980dd3b45d8162bf17acaa2b38e408ff19", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e981449f90e066ae4b1f689e60097dcd4df"}], "guid": "bfdfe7dc352907fc980b868725387e9804c88637c2207e80cb1d0cbb29ce414f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98768ab39ab313de78de6c04f30c2a9c66", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e986d21572dee4e5bdeee3d9b1a1a5c7744", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}