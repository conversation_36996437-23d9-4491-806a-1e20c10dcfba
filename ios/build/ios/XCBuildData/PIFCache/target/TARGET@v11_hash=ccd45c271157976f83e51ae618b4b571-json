{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9809baf278ee3f1fac32fe8cfd47bd9665", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/firebase_auth/firebase_auth-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/firebase_auth/firebase_auth-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/firebase_auth/firebase_auth.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "firebase_auth", "PRODUCT_NAME": "firebase_auth", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98789c94038e1d85916fd3c87e93070fdf", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98747afcdfca8dbd8801ebd62c2ed1408c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/firebase_auth/firebase_auth-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/firebase_auth/firebase_auth-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/firebase_auth/firebase_auth.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "firebase_auth", "PRODUCT_NAME": "firebase_auth", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f05bcb80abf8d2ff78437d5ec7d8ff06", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98747afcdfca8dbd8801ebd62c2ed1408c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/firebase_auth/firebase_auth-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/firebase_auth/firebase_auth-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/firebase_auth/firebase_auth.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "firebase_auth", "PRODUCT_NAME": "firebase_auth", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9841e8ae84b26a2be1f0eff502daeac66d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823244147ef5d71ee21ccf5ad3bb8dc65", "guid": "bfdfe7dc352907fc980b868725387e9880377aabe872d9fc86e0856fae440526", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c49682cc86df765d1da0953b3fea7493", "guid": "bfdfe7dc352907fc980b868725387e985d1e77a9e0f21d15d95d15d10c36a7ac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98344947466d00c99e3411b82813b644a5", "guid": "bfdfe7dc352907fc980b868725387e98a89d7b582dad1ffbd774a68124bbff79", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e69be7281f66e90b5cd3c3b494433f5", "guid": "bfdfe7dc352907fc980b868725387e989708b643a6e91392ca0c1fd8c0ddff14", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894cb9accda925c8ca08b379fd0f490d7", "guid": "bfdfe7dc352907fc980b868725387e9894ae4349aa757662cca2e956d7896383", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98735d714e705c40ad7bd3cec7655e9d4c", "guid": "bfdfe7dc352907fc980b868725387e98c24713b8d5cf8dfb46999e1b6fdf1d19", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986899903e4679bfcb8d57b8bb2eef8865", "guid": "bfdfe7dc352907fc980b868725387e98f9a07653dae78f9583344fc763ed9b01", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4f52d617b240f1b09db412ad637a590", "guid": "bfdfe7dc352907fc980b868725387e9803538f0ae7ac01b76fd5e8b85fecdcd7", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e988f67ebbfe9a44a955f668d3a5eb4c0bb", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98657e8b0ede26910a304f3d9c9173fa73", "guid": "bfdfe7dc352907fc980b868725387e98ecb41fbfb930c36ae115d09d3f3d730e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98760fee5ee8bc940ccce430d15374e425", "guid": "bfdfe7dc352907fc980b868725387e982209332a227b4ba85167bf822d967b85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e6229c9252cbccf8e48f4528f7b3dcb", "guid": "bfdfe7dc352907fc980b868725387e983e73eefe05783457ab3e4e4aa0874894"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a12887b2367f2a066223d4b77b6ab45", "guid": "bfdfe7dc352907fc980b868725387e9868d1b6fc7f4d39dd61430a4b50f6c459"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b69657c35178567b9ec8b1620c14f1e1", "guid": "bfdfe7dc352907fc980b868725387e98b48f8e8fad3850a20f32e72a8f3a3b03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1e1427cd882c56cc2a82c9b871be7dd", "guid": "bfdfe7dc352907fc980b868725387e98a771af2500fff533d0ff1b5e30913cd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5d184a5b2f0750e9b0702bd156d4bc0", "guid": "bfdfe7dc352907fc980b868725387e98d4fdf6bc3a23b63329c0a41ab0154950"}], "guid": "bfdfe7dc352907fc980b868725387e98d0a3957f9e1e76017912b92f0fab46b9", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e98daa3520e3d9f06a110e8f01981af6f59"}], "guid": "bfdfe7dc352907fc980b868725387e98ce97f70f77fd0c53336335476b6c74ce", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98021c1a71a666361c883fecc3ff1b4e14", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core"}], "guid": "bfdfe7dc352907fc980b868725387e983788d8769c821650606514be955fca93", "name": "firebase_auth", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e984496a3f7661d89567ff8250961054e8f", "name": "firebase_auth.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}