{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9864b7e7702511e378a2c74b9159aef360", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984936d7cb8feef48513f6131e211caece", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ebd2589961c8f8e12fc2db4bcdd729e7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98beaaf634ac9c45a28ea8b53ae8cea921", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ebd2589961c8f8e12fc2db4bcdd729e7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9879f34f1e012c285b844492b857411475", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9871169c3ed76ee0a3df3f86852bd61e25", "guid": "bfdfe7dc352907fc980b868725387e985176eb7aa89517dd79b622378e05c296", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d22c2eca806f553dc8483424454bbca", "guid": "bfdfe7dc352907fc980b868725387e98af69fb08e313617716d0d228cf215245", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c945099f0d251a3437cc03d1ef12f5a9", "guid": "bfdfe7dc352907fc980b868725387e985e2e93614b4b998292a11ff2a2c13b6d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818ad8b7e3ad9709133135c63e19f87b9", "guid": "bfdfe7dc352907fc980b868725387e98f39594556e495895558fa1a08b655ba5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98183e1f8bdf7ff7ebf69c06dfd8d32c33", "guid": "bfdfe7dc352907fc980b868725387e9892d2936f43a59179d3e2fc2a4021e53e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801bbd1d66e9739990903e7fb6982ff55", "guid": "bfdfe7dc352907fc980b868725387e98de9bba16cdb7cae0aadac8b6746bb22f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1935dba012e17775fb00e85cd40f8d0", "guid": "bfdfe7dc352907fc980b868725387e986f2fd5217ee9ef9b9f743d7150a25c60", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa3b71aa66bfdf700311ea2028e098f8", "guid": "bfdfe7dc352907fc980b868725387e9838280bfcd8b736867eaa4077fbc0d61c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821addbb665a5d78dcb4ee696c334cc6d", "guid": "bfdfe7dc352907fc980b868725387e985212bf823b2dbcc093385db1f1b30dec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b88978a549400e83aee1306740b1b7a3", "guid": "bfdfe7dc352907fc980b868725387e983b3e9d753ef701196ebaaa81a1e77d66", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d17f5269bb940b26cdd0e62ac876fd1b", "guid": "bfdfe7dc352907fc980b868725387e985cf8cb83875e6694983c229dac8bc9fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a5db8cff36286c4c507885e846f88c7", "guid": "bfdfe7dc352907fc980b868725387e988145d23612ab237ea91016460d73945a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8c4bb79708dd0ff3f85085bb6771d93", "guid": "bfdfe7dc352907fc980b868725387e982690b7c043bffc9f7499a3599fc5db55", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e755106a4f9528c399f14cec13f4bfc", "guid": "bfdfe7dc352907fc980b868725387e984bcf2821c445a13f1ca5ddd1387c63d6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df357e31480243f61f55dc47ecfd38b1", "guid": "bfdfe7dc352907fc980b868725387e9814e771116f0efcc61da8fc335aa4ca3b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98395220871c7e733aa9724b805a31c36e", "guid": "bfdfe7dc352907fc980b868725387e9896786428e6a8d6c7862d939cae65f773", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d21bed0b855bb137ab09d6e2cfd90108", "guid": "bfdfe7dc352907fc980b868725387e98790199ae3165d72360ab21230766b727", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9859ab3ec029f964cc4041f98492a33760", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986b4e61c370c17b28c701934fc33c9305", "guid": "bfdfe7dc352907fc980b868725387e98ac5c52be3190abee156605bfd48912ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980977ba07ded8c5b14fdc945d2c55a647", "guid": "bfdfe7dc352907fc980b868725387e98e1de61ab1ff97cc94cd06a5371923da8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a1623dd0cd70c0ac7d57e6b8492d174", "guid": "bfdfe7dc352907fc980b868725387e98d0242bdacab0261eec7e248947d90b7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fbfd04a6ec251b3a049582e6394996b", "guid": "bfdfe7dc352907fc980b868725387e982d942064c23276142327490fa33537b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c95b74c5d5556fc321bf6fc99117984", "guid": "bfdfe7dc352907fc980b868725387e98d9f834483786fc0c92f7593bcd6dd83c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fda5638b82926b8c993dd14179e0f0c4", "guid": "bfdfe7dc352907fc980b868725387e987f77bace7e1c1bfd0b3f575d26dfb06f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a385997f98bbb32ceb5f0ddad299413", "guid": "bfdfe7dc352907fc980b868725387e9815ed68575bdb30e9bf33141f83bd83b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843eb5a42df8879cc72bbbb730006b1a9", "guid": "bfdfe7dc352907fc980b868725387e98590b8f41b31928b0a4926ce46a04415a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0fef9fa41bd8213c916251cdf23ff5d", "guid": "bfdfe7dc352907fc980b868725387e98f6d629ffc18995e49d3d77a3aef0dcb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a444407d392d19e542924b4eca2948a0", "guid": "bfdfe7dc352907fc980b868725387e98af9f015ea26609a22120bcae2d16071a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f76e03b2cca0ec58130a032eb49de37d", "guid": "bfdfe7dc352907fc980b868725387e989c0bfab68da1fedec51cf36689157069"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822fcde86b1ee4844069ce867c7100d96", "guid": "bfdfe7dc352907fc980b868725387e98c5820ac9b4d0a4a76d9404b84c0ec2b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca262c8f6c1449480a2a933d8d68d5a1", "guid": "bfdfe7dc352907fc980b868725387e98df9697c7b74a7cf4593f9d66eda4c228"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d15cfbb78131d3c8d8ecaef94e94cbb9", "guid": "bfdfe7dc352907fc980b868725387e98df10a50fc47d08262ba22e5741f99c0e"}], "guid": "bfdfe7dc352907fc980b868725387e989d91e5abbad637a65716b7fef9466ccd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e9847d3b3fdbf8c6c03a84819a79f7ae5aa"}], "guid": "bfdfe7dc352907fc980b868725387e988ec00323e57392a3c283b014e048e33b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c4b351c20ac090098b0dcaf5fd3c18e2", "targetReference": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3"}], "guid": "bfdfe7dc352907fc980b868725387e98053f546dc39b1661db608be4eb007f0a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "geolocator_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}