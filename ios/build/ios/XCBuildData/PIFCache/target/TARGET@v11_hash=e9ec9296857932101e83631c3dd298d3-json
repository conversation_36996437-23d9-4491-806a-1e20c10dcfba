{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98eb897d5167734d65364bda864ac00038", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/nanopb/nanopb-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/nanopb/nanopb-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/nanopb/nanopb.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "nanopb", "PRODUCT_NAME": "nanopb", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b5ee8abe918e6294e11357faefeab390", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b0da3c3fe7094a602a7cfe9ddf580968", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/nanopb/nanopb-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/nanopb/nanopb-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/nanopb/nanopb.modulemap", "PRODUCT_MODULE_NAME": "nanopb", "PRODUCT_NAME": "nanopb", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98dfb4862699230a7a63bd29944d5edb13", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b0da3c3fe7094a602a7cfe9ddf580968", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/nanopb/nanopb-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/nanopb/nanopb-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/nanopb/nanopb.modulemap", "PRODUCT_MODULE_NAME": "nanopb", "PRODUCT_NAME": "nanopb", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987631fac05e37ad40b0e79abd3dc8477d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98af68da03427b9853b7ec01928c7daf44", "guid": "bfdfe7dc352907fc980b868725387e98f12208dc4f75cd433e587c4906111412", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bf7ce88fe788f2c0547b614f9156e97", "guid": "bfdfe7dc352907fc980b868725387e98cd0123b7a0f91f5f438252cf76b9b781", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843a8fb635d0a7058f01bf1db1a6b6a0a", "guid": "bfdfe7dc352907fc980b868725387e98b6ddcc8d997d8c202cea32e88c880a71", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985738ba0114ec06bf39851685b00cef5d", "guid": "bfdfe7dc352907fc980b868725387e98ddfc51dab76d9ed15daca5858c0eb2d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c69b84d1a55d22db1ce10bf00bab670", "guid": "bfdfe7dc352907fc980b868725387e985ff6fe1b51f6947fd8ebe9588b16e03a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e7cb00b82b025163b8ac9d4631d61866", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e2c72b5224911f9cf97db5e5dc3cc8a2", "guid": "bfdfe7dc352907fc980b868725387e986ea46925440592d56a08d0f1ad82f37a"}, {"additionalCompilerOptions": "-fno-objc-arc -fno-objc-arc -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98825fe3c150b094f79ec5592e2d0e3424", "guid": "bfdfe7dc352907fc980b868725387e987923a162358e9e3b8f040d20ae51219e"}, {"additionalCompilerOptions": "-fno-objc-arc -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98e09868e0e1cb73d8c3b65314004b1ee8", "guid": "bfdfe7dc352907fc980b868725387e9869d9e38f13d720dcd69f03523a45f5a1"}, {"additionalCompilerOptions": "-fno-objc-arc -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98a35467f364cb1f23dedf0d3e8c1deb95", "guid": "bfdfe7dc352907fc980b868725387e984f298debad32ea4c5585a988373879b3"}], "guid": "bfdfe7dc352907fc980b868725387e988eda625f8584c5e51b9b1548ca2e6ad9", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e980951bb8436aac3772ae990673f989cd5"}], "guid": "bfdfe7dc352907fc980b868725387e98767f7847f5a8323e7fdcd341a7d35734", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98bdfa9082f25789d16a3401510c19d606", "targetReference": "bfdfe7dc352907fc980b868725387e98c9e4d77647dbd2f60d4df5fb297112b6"}], "guid": "bfdfe7dc352907fc980b868725387e98031f1f8ca166446787fd9b3596dcf10e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98c9e4d77647dbd2f60d4df5fb297112b6", "name": "nanopb-nanopb_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98edeb236a6bea2a184984d344e4936f7f", "name": "nanopb.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}