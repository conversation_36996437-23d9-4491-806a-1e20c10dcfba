{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9854483a2ea2e704c59470be497a058be3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseRemoteConfig", "PRODUCT_NAME": "FirebaseRemoteConfig", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98167590c815cd942d496f6eff9b011587", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f8c6ecd93c5027a9ec4842bf84ab8ff4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig.modulemap", "PRODUCT_MODULE_NAME": "FirebaseRemoteConfig", "PRODUCT_NAME": "FirebaseRemoteConfig", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9820d9aa47cafdd5dc1eb538ddc87ec393", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f8c6ecd93c5027a9ec4842bf84ab8ff4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig.modulemap", "PRODUCT_MODULE_NAME": "FirebaseRemoteConfig", "PRODUCT_NAME": "FirebaseRemoteConfig", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988e193b9439d5b243b9321fdaff4aaa96", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987e1722bdacb5b913451a17d00a0e2d6d", "guid": "bfdfe7dc352907fc980b868725387e98a4595fd0b3de46d285f591b6ff7dc885"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb07d6fac3c19a4211a364507bdccfd9", "guid": "bfdfe7dc352907fc980b868725387e98b37612fa32ab36b0956e6f6caf18341c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e3cb6ad77e8ef2d68282614b08fc38c", "guid": "bfdfe7dc352907fc980b868725387e9830b4b1b1990dc9828b14f97902a623a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815d99ccb6c5813fe3f1864fcc6f082bc", "guid": "bfdfe7dc352907fc980b868725387e98927316ee68e88f0e3f204ebfa71825e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98097e0fc51103010304228bc9c9c98d3c", "guid": "bfdfe7dc352907fc980b868725387e98c1160bf0d28e4cbd163bd083cba82e57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980044cea44247fef7316f6801f038ea8a", "guid": "bfdfe7dc352907fc980b868725387e98fa3ce97b92681d2e3f8ac087cc773da1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98690eb5e67fb1f249d41fdcebb63dad3d", "guid": "bfdfe7dc352907fc980b868725387e98244bb8ed1ad4810751f087c2c826f1aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895476b7f08cfd0ca93e525eb6873b0a1", "guid": "bfdfe7dc352907fc980b868725387e9854f91259b5d79365c31d551639634f64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b674e3f0adbcca5c55d9bbe9ded8bd85", "guid": "bfdfe7dc352907fc980b868725387e98db1bcfb7a027bb4fffd2ec8aaa43faf5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b6dcc16e0342cb00e811dce23c5398f", "guid": "bfdfe7dc352907fc980b868725387e98e85e58393320e4cb11b57d063e7f21bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855e606637adea309ce3d457bd2324427", "guid": "bfdfe7dc352907fc980b868725387e984236babe4f909963c4684befed2ba393"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801e6ee377fdc7cf3bd1350186699ed73", "guid": "bfdfe7dc352907fc980b868725387e98c3d0f9110413aaec6e8c707f0f461461", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9640944cadfc6993058cf6e281738ac", "guid": "bfdfe7dc352907fc980b868725387e9809bb67499c8c45167d5622d1523a5e7b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aba591b7a44b97989975601008f51a1d", "guid": "bfdfe7dc352907fc980b868725387e985db2b5a119abc877ee85e74d7a02195b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f18566568e36a56324f2a28006b228a", "guid": "bfdfe7dc352907fc980b868725387e9872a47c21fd0572aa4c2342a05e9295e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888608e8adabb34010742e36e71477a4c", "guid": "bfdfe7dc352907fc980b868725387e98b36c253a8bd4c190d9e8d487d6e4038c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b45819815c7a1b56d3fcfe8444477cd3", "guid": "bfdfe7dc352907fc980b868725387e9826b7f392f5282f0f5abd72646767e7f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889e247e032221965728872f64b2b5c7a", "guid": "bfdfe7dc352907fc980b868725387e987ed152a1bb423da7229b9de2120b7817"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e877d0cbaacf1a59e2d9f8a9dd8fcc4", "guid": "bfdfe7dc352907fc980b868725387e98719929ea2fa2414c7b933d0479e06a91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e937798bad2e86bc54c80cfd4e0aa380", "guid": "bfdfe7dc352907fc980b868725387e98e082185f25be5ce6e56e960c8a8f45cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bcd2d20e1870d90ae201c981e442d14", "guid": "bfdfe7dc352907fc980b868725387e98a52a891d882a3895e42664190f68a946"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f16258ba87d6cf6b593648312c5bc949", "guid": "bfdfe7dc352907fc980b868725387e98174eaba36f66a0ab802d436531d1a4f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ad6ab1626d8ba63e3efc7f53067bfc7", "guid": "bfdfe7dc352907fc980b868725387e98d518b07347a7547ed7c861262d54d0c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864f5a7a262b5043699e6df9805ada608", "guid": "bfdfe7dc352907fc980b868725387e98a133b56e7fb17301b7b5690604501b85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816398d37bd9a3be4a2507314dd6f51d5", "guid": "bfdfe7dc352907fc980b868725387e9882f9fb7bacb5486e9cd4e67b92e52fcb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1699222fb10456929add439d68374a8", "guid": "bfdfe7dc352907fc980b868725387e98e17a66dae69cd56de13667b144a667cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ab1c383635713c391017caf99f94690", "guid": "bfdfe7dc352907fc980b868725387e986f701b3bc484beeca6f14f174d59930b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e908d8a66ecad0f07a0ea9e3ba63bc9", "guid": "bfdfe7dc352907fc980b868725387e98ba9cb6d1b0d8c3168ffca4b99b16f524"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986808ab7e8901d589a62eb0e0696108a5", "guid": "bfdfe7dc352907fc980b868725387e9894b86ffac90d93bfe2fdcf2865644b5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc0caeaddd64906ece773d012a40411b", "guid": "bfdfe7dc352907fc980b868725387e98d6f492828fb04a79cb0fee40c2e90a73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcb352a7674a173da001e267a6bc1028", "guid": "bfdfe7dc352907fc980b868725387e98c3c7c85047f2e3085a3fec82831ffcdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5c105c1931ef6a8e666d28399db67fc", "guid": "bfdfe7dc352907fc980b868725387e9861bf9f190c7a5238c737008f1c941614"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ae0f98e118c240446d58fa7ebf3e95c", "guid": "bfdfe7dc352907fc980b868725387e987d03f3df966bc2250a96e0688e4a8927"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efca13546e52c0109eedcadfeba90103", "guid": "bfdfe7dc352907fc980b868725387e98a6e0fd8c699c4755d320446269f51ecb"}], "guid": "bfdfe7dc352907fc980b868725387e98f2d57e3e8ba3dad7a4899489aea5ee16", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982cbfe60d8872db10eda0aed0c9a389a3", "guid": "bfdfe7dc352907fc980b868725387e9884036a5455c7a209b1cb57f5208f1466"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983440c53b89b1c529ca902da4c1b19d24", "guid": "bfdfe7dc352907fc980b868725387e9827808c996da87c010112bc2a4f6f3f22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cfa19da118fa5278700479da1ed88ec", "guid": "bfdfe7dc352907fc980b868725387e98ee1cf2f0f680db4787813a8993789efd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4cef08cdae736e1e9c8d023642713c4", "guid": "bfdfe7dc352907fc980b868725387e9865f62b70b1fee17b1e7431c4cb29a234"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f301eb1dab63c6cd079dad7b05ae9be", "guid": "bfdfe7dc352907fc980b868725387e986c6f4ff684c10765a30e6c7709a761e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5cc5ca4d92a5ea84db2441f773ec2f6", "guid": "bfdfe7dc352907fc980b868725387e98506627944963d28a931dd92b7034a74e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816fb8128df1c4e698b71369b06166874", "guid": "bfdfe7dc352907fc980b868725387e98ddb525d92f733a929ff7e8d4a416fe61"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce9d680b165a40d6f893fe5d8b1be472", "guid": "bfdfe7dc352907fc980b868725387e9837f4e46e11b08d64bdf07b72ec8ec21c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889a58c4c968c1331d72942773066511f", "guid": "bfdfe7dc352907fc980b868725387e980db693e0ad9f92ae11f2a5bf7061232f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841a65523d0d3fe77f250057d2f98d753", "guid": "bfdfe7dc352907fc980b868725387e98fa1e633478fd06fe0e7acf8b4bc9898f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982137bdc9260148870719122478290429", "guid": "bfdfe7dc352907fc980b868725387e98bd57015327592885f1f5b5a87731fd75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982912aec636338aaff7839c93c6d6df5b", "guid": "bfdfe7dc352907fc980b868725387e98c9e0799b266876ac4e2e4749aafdfc7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985592a2d00fa9e7a1991f8a68be20104b", "guid": "bfdfe7dc352907fc980b868725387e987c5581dce7ed25f7f491033302c36c10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899550fcfbc908d8843442d2c09dad3c1", "guid": "bfdfe7dc352907fc980b868725387e98c58d037fde4192b4edf1a4a64c1cc2d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f33a6d62da46f41594fa0d0cd705c92d", "guid": "bfdfe7dc352907fc980b868725387e9882de07ffcc74fde3ad5f144e67d2c3e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98129fed2f5a59cfb97fdf56b1550262d5", "guid": "bfdfe7dc352907fc980b868725387e984c904bd782203ac4e41c181dc79d9422"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d77a7c9c375051fb64ee005757d1beb", "guid": "bfdfe7dc352907fc980b868725387e98ee32aa45392a6205bc8258e21eec766c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827e8a75091ce902a9841192606827ea6", "guid": "bfdfe7dc352907fc980b868725387e981f498e014083aaa6c493e8ef57847c1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836b9db7f2350a023f5941af3c327f3fa", "guid": "bfdfe7dc352907fc980b868725387e98db800e6fc29a656c80817e0c4946307a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984feaaf46bc22990e0a3d551e51919ebb", "guid": "bfdfe7dc352907fc980b868725387e9846ab007b3ebf26c11e50ee62f6001887"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a390b66a767b1f5005388446dd3216ca", "guid": "bfdfe7dc352907fc980b868725387e986f7da7db6c0eef0c05acf268be534bf5"}], "guid": "bfdfe7dc352907fc980b868725387e985d135b293558073e6b9679911b36f1a4", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e98bf91b64db72d21225f194f5238a21786"}], "guid": "bfdfe7dc352907fc980b868725387e98b2e8864728eb744c2b1937bc7ae1155b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98f394aecf0be7c60cf8e15ff87f6af901", "targetReference": "bfdfe7dc352907fc980b868725387e98012330f90a37c9d15a390c6b73b6dbca"}], "guid": "bfdfe7dc352907fc980b868725387e9864a7ddb88c214783742fc91412488315", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e984d1b80eb520d7ec9828b3cb4e14dcb65", "name": "FirebaseABTesting"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98012330f90a37c9d15a390c6b73b6dbca", "name": "FirebaseRemoteConfig-FirebaseRemoteConfig_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e984b1e8e5f67fa144e5e34058df6e2f50c", "name": "FirebaseRemoteConfigInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98928855ae8620d13300183deed96c33a1", "name": "FirebaseRemoteConfig", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980b80126605cba44506bfa90fbbd69742", "name": "FirebaseRemoteConfig.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}