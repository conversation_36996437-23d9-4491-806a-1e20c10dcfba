{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983467980cd5bd871f96b4cdb83fa17982", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980b61465c9a4c5a16e9fea142c88e3afb", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9863b7c5389ef32f0d6b97131bc35c5612", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a5627045cbe5659bc39a78fbf08a6e52", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9863b7c5389ef32f0d6b97131bc35c5612", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983f5a02c0526e4abc017e89af39b28f21", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ba6e75ae2997e798e76bbaa2d168d57b", "guid": "bfdfe7dc352907fc980b868725387e98aca9512e08cc7f25d27755e240a6bfee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a7c7f96dac42dd3bb5003beb5e0f261", "guid": "bfdfe7dc352907fc980b868725387e9843e6bd1152e1446ac31f4b3dfc1ba152", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9839db968d552c14b5a2b239c50a77cb97", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983f4a10c3dab6181a094d120cae5e24b4", "guid": "bfdfe7dc352907fc980b868725387e98d1f3750c71b723e6f7c12eb469674c2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f132b8e385e2c510a4927ebfd608b67", "guid": "bfdfe7dc352907fc980b868725387e982246e095caca4652c564dcb5f22dda65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98771b963e0c4467304456f876e5faf8f3", "guid": "bfdfe7dc352907fc980b868725387e983e7e6d2279a462cc3e31a87ac978c0c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811573fe8c08e9731f6a51cbd07eecdce", "guid": "bfdfe7dc352907fc980b868725387e98f61df6890ca617295b136aa739893b7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983824c9f8a5f2e17d224f0cd01fbbfe8e", "guid": "bfdfe7dc352907fc980b868725387e9812f1ca825c7c34084a8fc8a134f33020"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee5d6354af25c84485125b8f2d11a15c", "guid": "bfdfe7dc352907fc980b868725387e989305f44472682f73d8d9d211fba12c32"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd32f393d6027424b032ebf081367fe6", "guid": "bfdfe7dc352907fc980b868725387e98fe49b0ea0a32433cdf3bcccccf19418f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f626695caefd961450a41e2d2e28209", "guid": "bfdfe7dc352907fc980b868725387e98cd9a0c01c8be2b189268fcbdd5660906"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826d4287dcd6cfc091183596b9d9301be", "guid": "bfdfe7dc352907fc980b868725387e986b38bb64eaeadf5981c03d24ea163912"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98739cd02303e686e1b7266cee5cb7c301", "guid": "bfdfe7dc352907fc980b868725387e98965f3c970d745b927ed1ea36d0002437"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864a49dc30818c168dbd6e488b5cd6278", "guid": "bfdfe7dc352907fc980b868725387e986e5e55a5347d8ad599b6fa8944b9eaca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c88e89f4ff90427813a06267fde73c49", "guid": "bfdfe7dc352907fc980b868725387e98d88e2c4c438e9a8f73275b271c9a1f2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98737dcb0416c424b181211d8084db71f6", "guid": "bfdfe7dc352907fc980b868725387e989049b1878f5bd7998a3f4e97c725060c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897e5345809f512b0f2cb1e99598c1dba", "guid": "bfdfe7dc352907fc980b868725387e98bb1cd5747315fc81874838d559d01c8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98928183c330c6c0311f8fac6e8a85a2cf", "guid": "bfdfe7dc352907fc980b868725387e98a20b4127d89ccded5c06c104db0cc4d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810a1b38f84d1cf6ea8ce118255c4d080", "guid": "bfdfe7dc352907fc980b868725387e989cd1e8df8b9f260ee8c2fa9fdf302894"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4bcc2ef50a87947ae725b34ba08b09d", "guid": "bfdfe7dc352907fc980b868725387e98b8d02d9f86a8800c0888815d18d4581e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6dc81d83c6aea4ddc59216d755f2efc", "guid": "bfdfe7dc352907fc980b868725387e98b80b774e334c996da799c40a2d4394d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899da5ac39f01c83ad55cdf66b66fe80c", "guid": "bfdfe7dc352907fc980b868725387e980c429434d23cee64fe676ea3002cf92b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b64ba2ce5168d1ef5144d4d41bd956a", "guid": "bfdfe7dc352907fc980b868725387e98a54e96a0dac2b17c26dd78dc867e83d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4180a58f233f635a1334021b3e0c064", "guid": "bfdfe7dc352907fc980b868725387e9838dc138c352cd803d91486208bf52085"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981da19c28c36f96b55c48d9195b4a56b5", "guid": "bfdfe7dc352907fc980b868725387e98ec7b1471150597fa0673a20ab4231927"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808dc9774050e9430d22ef2bc6ac76c98", "guid": "bfdfe7dc352907fc980b868725387e9868b44dee32e155375621f083fb5006b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880d8a4cc24956c23117d1b1f325f77be", "guid": "bfdfe7dc352907fc980b868725387e98f74aa3b1f169464e680615b9e5f63b9a"}], "guid": "bfdfe7dc352907fc980b868725387e9821eddb57d0abae81530a10acb29dbd46", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e987ed5ed0d996b1843a6f47b1e1771586f"}], "guid": "bfdfe7dc352907fc980b868725387e98141d3034950c8b21ceceaf5811fb3f7d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98f54ce496b3066da18a0a5a428e883abc", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981f0a8508efd61386103314ddbb82a530", "name": "FirebaseAppCheckInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e988e935c81efc4686179f554b8fe37864a", "name": "FirebaseAuthInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98f1e09b32067e7d86144abdaf0d62fddc", "name": "FirebaseStorage", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9861b2e033fd71c20add064527e8a82b5a", "name": "FirebaseStorage.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}