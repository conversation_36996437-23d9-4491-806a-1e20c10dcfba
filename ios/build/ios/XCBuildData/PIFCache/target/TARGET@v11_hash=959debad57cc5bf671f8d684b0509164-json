{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9823c4e1e21df45565faae8aea5336d4ad", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9863da02b7d9b5c0879f6fb681d3508f39", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9839b34ae999e22a58b3d35b63ee6607fe", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b0171ed7e0597c2236b0f38c809b4004", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9839b34ae999e22a58b3d35b63ee6607fe", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e6764c0f170333e3f037c6be1c3d1f8f", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987d67e7f661b064b23f0797a79d5c560a", "guid": "bfdfe7dc352907fc980b868725387e98a9ca21cedb68b91a0e11bfa418e9aac8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd4a4a0f0b984a620858ff5fe92be33a", "guid": "bfdfe7dc352907fc980b868725387e98888c69f3ecd255f263c59919fa2789d4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98591adbb2a0b1782a748dc215b052af6b", "guid": "bfdfe7dc352907fc980b868725387e98091edde2777f7fa3da93b0982ed26a56", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a617372387cdf454ee3d3a7632311a7d", "guid": "bfdfe7dc352907fc980b868725387e9817f101ad728c145ee8a1b9db2b33ae27", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98228328ac4665345c5642d56bd2407118", "guid": "bfdfe7dc352907fc980b868725387e989d8ae614923a385d3a0928d39c0a578d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98135e7cf9da8100fec7c5d705bf97497e", "guid": "bfdfe7dc352907fc980b868725387e986e8f3eea1660c291fb2edd886bb85d90", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846f0b9314d5ebd4242be098d28114484", "guid": "bfdfe7dc352907fc980b868725387e98959eac3cd75bfee89bcc2753fe503843", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820bbda3a4c177fbc6c917edd401959de", "guid": "bfdfe7dc352907fc980b868725387e98394c7a632aa49dc414e183c5b5dc1a8f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889ae260ca8f08455ebf91653b069185b", "guid": "bfdfe7dc352907fc980b868725387e980faafc663b1860418e646e817fbb4b3d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989947c40ede690643326f826ddf6d7057", "guid": "bfdfe7dc352907fc980b868725387e98ceb2687260cc832a1da4e570fb45f47b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826d2646acaff285499ed8d5df274f8c4", "guid": "bfdfe7dc352907fc980b868725387e98035aca48b1523bc510936336082cc98e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fa88e6c0555431a8aab82ee1f995a5d", "guid": "bfdfe7dc352907fc980b868725387e9879b2d16c332552005bccbc3cbe1fa24b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888114f24a11987de31cbd3911f66aa34", "guid": "bfdfe7dc352907fc980b868725387e98b1e8b46265f8d9ec34c4e8dad9f0ad30", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98400129f36e5e30435b476a4a2a8ba06c", "guid": "bfdfe7dc352907fc980b868725387e98cdd11a4b318dd6698966df6d5b9323b0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f60fe4944b08274b8020eaa6153da48", "guid": "bfdfe7dc352907fc980b868725387e98f8fabf403c11871bce7b5d6b8db37f99", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b28a30c076748ef063d96b52664c452c", "guid": "bfdfe7dc352907fc980b868725387e98e5a56b1f865d0661fec917164d75f4b1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877c69109390165a90fccbd46d569e662", "guid": "bfdfe7dc352907fc980b868725387e989beb2ed812e07eaa058c0ac91e47243c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98502af4e394eaf0ebe4b3753d2a24f059", "guid": "bfdfe7dc352907fc980b868725387e98b83aed924688a188a9d91f176abe1ab3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a73a3d2d98b47bacf8832d1bb73ea9ed", "guid": "bfdfe7dc352907fc980b868725387e9838b64ef68b56bd9d049d56d1b069007e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98971f696b71599a82d9bf509f11a0f81d", "guid": "bfdfe7dc352907fc980b868725387e9878e44d657ebb0dde1e95a9a08db52f1c", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98928a2e77cea715f1e42d141b8549f2d4", "guid": "bfdfe7dc352907fc980b868725387e98ea6dd123d358dbe166c0175c0a007f2b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5a7564dc57cc5c5e3b2e3a19c19a3cc", "guid": "bfdfe7dc352907fc980b868725387e98cf8b57e8031c2e1021b2307b4ce6b58d", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e987f12ed4c44e90d52efa14586004b8fe1", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9846a4e47f5b05b419f760710896fbb201", "guid": "bfdfe7dc352907fc980b868725387e9806176b54e8f3b37a3bc860305d5b51bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c94ae6c324d53b90fdd4a6c6326a8e9d", "guid": "bfdfe7dc352907fc980b868725387e98489d013e7db028ece519dfa373c23842"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f9761fe72b04730e841ccfea4353a4c", "guid": "bfdfe7dc352907fc980b868725387e9801236771af3da14618b14bad3f7fd3a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987be3a1625b192ba2740a48cdc3e65b91", "guid": "bfdfe7dc352907fc980b868725387e9823ce036676de96dddb041a99c37722f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fe92cc85333cb0e03402a073694d3e9", "guid": "bfdfe7dc352907fc980b868725387e9873b9025d0bc6f1ed614a08ea5aade567"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98756da345b393d71776c561394b71029c", "guid": "bfdfe7dc352907fc980b868725387e983c37344173d53becca592bd3ec19b36b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864ea5e90f04e6ebecb9124f1f102c3ef", "guid": "bfdfe7dc352907fc980b868725387e98b755ba901e2146f4963047bdca0543b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831d553ff9c8c7c18b7dea07b1035ba4a", "guid": "bfdfe7dc352907fc980b868725387e9877199ce6b0ce73f4448fc0b12c333ed2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98190592c651b9d8f6a0ec141f2cd12852", "guid": "bfdfe7dc352907fc980b868725387e9834c209f88ab7c5f7261d19b04cc5675c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef8c26941079afc29ded80f432f75712", "guid": "bfdfe7dc352907fc980b868725387e984fcd238f9e6bbde95ac733b013a7b516"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98777daae14410eabef0ff6bac34dfda1f", "guid": "bfdfe7dc352907fc980b868725387e98619572e24c5a19a9dc3b36085e72aa1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b420d7a45e0df08ca19bc1a1260d2873", "guid": "bfdfe7dc352907fc980b868725387e9814574309424c14cd1d0064a2c9f70b26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864db13676af17f53aa834beaac32b653", "guid": "bfdfe7dc352907fc980b868725387e98c361acf6e68ede944b743ff1be65a0e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c61be3c2f4295a02811e1be347257be", "guid": "bfdfe7dc352907fc980b868725387e9882746333feae84091c0882c29ce0ed28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e04f3e97257787d1d2a60709cae103aa", "guid": "bfdfe7dc352907fc980b868725387e98f15c46c51486fed6b7db49151267a362"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0d157ce99b2de572da4fb3e59e36006", "guid": "bfdfe7dc352907fc980b868725387e982e1eae2ef7443c099c7cbe3304ce2fee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1c30aa66afcaab924c966ded1d25224", "guid": "bfdfe7dc352907fc980b868725387e98606ed686ced64ca6b18a07c8d4e03af5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbdc5a59f6eb39d722b6c51a15c45f97", "guid": "bfdfe7dc352907fc980b868725387e98154580436deb6330b0ffe7ded9a71a06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9a133ec9a456f804d642523912fb7ab", "guid": "bfdfe7dc352907fc980b868725387e985a3c37c273dc8e60c55783fab189c3a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bbeabebf48552284f4829c69d249c13", "guid": "bfdfe7dc352907fc980b868725387e9866988d80573ae024c637d8194621953e"}], "guid": "bfdfe7dc352907fc980b868725387e98f0050656ffbcbacec21db02d4ed8bea8", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e989adb3c9dddf087bd11d13d03e467482c"}], "guid": "bfdfe7dc352907fc980b868725387e98191aad70a72c4433fd209de152f550b3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9899001292b59ba176e976d8853d9ef51f", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e980f10e0a893bc08fc9f40aa1edfd037da", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}