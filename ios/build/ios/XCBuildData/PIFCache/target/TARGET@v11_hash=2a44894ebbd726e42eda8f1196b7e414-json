{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988cbf54dd21c0a89d15be2400fd97e39d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983edca915d292849e0d4cdad770eff49d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986940c7510f2acb4de37a412084a3266b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989b1faccbeb8d7c776b5700b9562d4368", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986940c7510f2acb4de37a412084a3266b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981e5767821df8acdf2432b89dc8d2a756", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9841cd67ba93c3f150f9e4ae263e198fa9", "guid": "bfdfe7dc352907fc980b868725387e98cac6d1da4821b45de4da04d9a14ed845", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876d756bcf3bdbc1cc6b348d18c60df25", "guid": "bfdfe7dc352907fc980b868725387e9812db936f2c76c49d6b3bf5db9ed85777", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983532f47d2dfea1af37741feb55d13038", "guid": "bfdfe7dc352907fc980b868725387e9870f6201d0270cacaa045b495da88f3da", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cc406c0c0e4a898a7064f20306f83e8", "guid": "bfdfe7dc352907fc980b868725387e98cef23d9f6cbf928353d120c4a95e7796", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847482e61327a1c0522f87eb4b957e177", "guid": "bfdfe7dc352907fc980b868725387e9841f2413e0742a50c995cd982ff64f040"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d8d58c0c48d55d6075a3480129081f4", "guid": "bfdfe7dc352907fc980b868725387e98405b07d2e38668ce9c61b5d607b5e931", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e33207afc425a2ca624a2a0f2d72e12e", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981b5f1f83ebc140ac3ce94e827998f967", "guid": "bfdfe7dc352907fc980b868725387e98ebd931445757252c5442600005abd147"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834b6adff2d0660034de927332c1f6228", "guid": "bfdfe7dc352907fc980b868725387e98c28aea0b8afe83539c206edd20437f0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e065afbe6c903f457f032ea98258a9ce", "guid": "bfdfe7dc352907fc980b868725387e98467e81f0ca37c08173d19cc02df6b3f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879ba63863fa7f72cfdeebe8a2611a5fb", "guid": "bfdfe7dc352907fc980b868725387e985ff11b4f7751361629d55ba522990150"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98571ce41d25ad49e3afc3d4a6392f4c78", "guid": "bfdfe7dc352907fc980b868725387e98882dc3c775fc8815c92a490a96d27e30"}], "guid": "bfdfe7dc352907fc980b868725387e98ea430272d37b47282dea39a1e6920f10", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e98e2c0047915744347c76f87c77debea0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2b2d6d2a2281a64563abbb1eabdd5a3", "guid": "bfdfe7dc352907fc980b868725387e98a1ced7bc0996fcf6f9f39ff3761c6c4c"}], "guid": "bfdfe7dc352907fc980b868725387e980fde7ef4983662066aa00feebbb7b895", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e984ed5d6fe8cfffbc5680153762ad869a0", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}], "guid": "bfdfe7dc352907fc980b868725387e9885a4549ee277e8b62dfc8988b0c240fb", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}