{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985e8b38787a8cb00ad0f1f9f5fc0c8baa", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9818629d6603430b0b3967e9ba795f5157", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bb373729a0c8af758f83eda8d4de331d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e8319addb897251758815d041e7a8543", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bb373729a0c8af758f83eda8d4de331d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988c6ec5ed6e90c86e61a1821622409c1e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9820b97c5092cf9d22d5e4d84b34269f0c", "guid": "bfdfe7dc352907fc980b868725387e9860e67ed67227a0a4f0cb34fc6d730026", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ee6b630bf789c241b2f90dd3ea37d6c", "guid": "bfdfe7dc352907fc980b868725387e988e0b6d9262f07d6928dbfcae12989bbc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5ac33cba2c3fc0f5cc04ed0cb529243", "guid": "bfdfe7dc352907fc980b868725387e98e8c795916e378879930d3794fa0b7009", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6bea16ed18834017b05dca4334b63fc", "guid": "bfdfe7dc352907fc980b868725387e9844d0bf83dfaf6bf192ca04a4ebec51a2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b30571371b8ae83917100351c30c7a83", "guid": "bfdfe7dc352907fc980b868725387e9880b3105b947b220b494e157d15db063b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c38824286e038eed1c8fbd5eccbcb36e", "guid": "bfdfe7dc352907fc980b868725387e9889f8478a2c07af865b650f793308f28f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98554c56dd3197346c4a50588df6b882a0", "guid": "bfdfe7dc352907fc980b868725387e9826163bca1a3411aa1fcffc38813df253", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e5bd4c50708afc72f6a2797254905fc", "guid": "bfdfe7dc352907fc980b868725387e9869991bd9293bca05952ee21c717143db", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1a316f0c2711fda5b5f2af8eb321e08", "guid": "bfdfe7dc352907fc980b868725387e98d0346820b02f16a49c7082c9ae081da9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989745bb7014db025b861e0b0430e5ed85", "guid": "bfdfe7dc352907fc980b868725387e988405fbb349d38eb53b896f38bb517810", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a1717995dedbe446da67b2f8771e41d", "guid": "bfdfe7dc352907fc980b868725387e9830a6837f17bc379c19b10e12f06f94fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801d6764bbf8ff6a52cda623ac7b446f2", "guid": "bfdfe7dc352907fc980b868725387e98ef16fd950af81bc9ff63984f19444691", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846e96b31f82c32bdb223615c4984549d", "guid": "bfdfe7dc352907fc980b868725387e9854ed864b6db7260c3b916adabda9b501", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98084fbf626d64e6266921921d91be0cd5", "guid": "bfdfe7dc352907fc980b868725387e98bb990799d5d840de1d6cee5a9a66ddb9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1f2e1c030da18a887f13be8ba1ada16", "guid": "bfdfe7dc352907fc980b868725387e983c0f0710533b65eccc24f8848dae5082", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bd7acf0a1ef1571f090f0a56ec3edf6", "guid": "bfdfe7dc352907fc980b868725387e98455558d987ca2769d91f0a59dc99683d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848979f4cce205f75213b3408725763b4", "guid": "bfdfe7dc352907fc980b868725387e9820e3eefb2ed08e931e2bdef920ea2a18", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986953c9832935d8f318383523b74bf51c", "guid": "bfdfe7dc352907fc980b868725387e9861f19da55516e31c8de7b9b22316c3a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f5aa6f5baca8293a55715162c645b5f", "guid": "bfdfe7dc352907fc980b868725387e9883ba5637c2ca84d8de9c46256d73f76d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878db8c9c5d4989081bdd576e019e7971", "guid": "bfdfe7dc352907fc980b868725387e986cd1f953b912922ff5c41027b0d2e253", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a470ff9c8ad9758d5de5beb4582c2f8c", "guid": "bfdfe7dc352907fc980b868725387e98ff4be5d47bf60841f1e25652d394e508", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cf637a93d690b27d80319b009a0e42b", "guid": "bfdfe7dc352907fc980b868725387e98c3b10c4d91f215d71217c8fae713684d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98805d76fb712dabefdda04e23d9bb17ab", "guid": "bfdfe7dc352907fc980b868725387e98fd09ec336671c5eb9ce8401619a6983e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef1127a8c86e9f70baf6243bcb0ea58b", "guid": "bfdfe7dc352907fc980b868725387e983698b9a91f46d361946aa26a2840c771", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822b9bd72510c8874283158170518becf", "guid": "bfdfe7dc352907fc980b868725387e9867a5da6b1cabed97574cbcee386f69b4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9a27a8b263d54baf077e90beb7103ec", "guid": "bfdfe7dc352907fc980b868725387e985753f8adbfdc18a303c139835e3c18b3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889505d94fd48acd71f3a1b7499e7ee9a", "guid": "bfdfe7dc352907fc980b868725387e988b54ce4a9f7a075fb39a5251153de707", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4ca528b17e00089b607adc4e4bf44c2", "guid": "bfdfe7dc352907fc980b868725387e987f24653cb73cfcc54e317cc3ba29e636", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a83cab10db6a544bfbf93710a04643ba", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983b9545796c42fa21037d0504400ceaf2", "guid": "bfdfe7dc352907fc980b868725387e98e165e39a0401425e8f9df0f151d6bff1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f0665f53b5a0bb9dbf8d96f107e246f", "guid": "bfdfe7dc352907fc980b868725387e98c99f26a40343d9bd89c11218e46043f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c871ac9096a1ba2b59bed9920eb94334", "guid": "bfdfe7dc352907fc980b868725387e98294402b843b350a76012e2e9dad561e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989697624f56c0d47a156e945d0d1c2ff4", "guid": "bfdfe7dc352907fc980b868725387e988419b5fe193144039cf5531195ddf843"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5f50342a9a628f352062aeff2e50317", "guid": "bfdfe7dc352907fc980b868725387e985f0980fa11e1350ac529bc1193368b91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a90cb69447942147eb16284901fb4a9a", "guid": "bfdfe7dc352907fc980b868725387e98edb7c25dcb32311d09770308a13f9b6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6754a8e14a9fca823165538bbad58ce", "guid": "bfdfe7dc352907fc980b868725387e98bc8c2d9d57d51071901ff60499cdf180"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3adc9d7ccb994d7112399c4dbd9f15e", "guid": "bfdfe7dc352907fc980b868725387e984187eb0520c3ab03f9b138f5eac5ee4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982affb26daba6102126707decadbc8840", "guid": "bfdfe7dc352907fc980b868725387e98ce3c9b423aba2c04acc42693dbba562f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4e4aaeaa761fb36a5661e77b00dd650", "guid": "bfdfe7dc352907fc980b868725387e98b2b786f8e30f4f0295f1fc715aa30f8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5ed3c5d8708be2067b5f63f015e9507", "guid": "bfdfe7dc352907fc980b868725387e9817ee75c9b9430deb759056ef0d6ba7b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d830b275966c9883c14a6f5e4a40c31c", "guid": "bfdfe7dc352907fc980b868725387e98cf05ade7bc11abf07d489865ad310714"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98055d80d159a0c56e700b3172569d8497", "guid": "bfdfe7dc352907fc980b868725387e98827ffc202bcd75547ea215833f31d831"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a1d489aafacb997fb82a5405f046524", "guid": "bfdfe7dc352907fc980b868725387e98546fb1a217f2405dc6bf94f06815fd8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987209f595e71a63dbbde35f26f0099d94", "guid": "bfdfe7dc352907fc980b868725387e98608e830f26612424f2319921f74ff741"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983af0a43668c01a3498dfe08bf7bf486f", "guid": "bfdfe7dc352907fc980b868725387e98b55f1074f1e5e615c0f04199745d7520"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdfac83033c161f21d5d2fb59475f37a", "guid": "bfdfe7dc352907fc980b868725387e9846c63738fdd0349134fdc5a7699b8529"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885b3bf65f331ab5dcd3ca0fecafd73e4", "guid": "bfdfe7dc352907fc980b868725387e98d66712f675932769d0f0e677ec2ca57f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dce3c37df571dd1cf18586c52cb3e2ed", "guid": "bfdfe7dc352907fc980b868725387e98b524a29452036a60e044d7d78feffb43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f281d98f24140425fca796b882e3f41", "guid": "bfdfe7dc352907fc980b868725387e989336ba25a775ad98824ccf1afbc56b17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98743622223851530ad34d872852f4357e", "guid": "bfdfe7dc352907fc980b868725387e98a3bd4cde6f2f77a4e061f11668f6e37f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bca2e952c14c43a6e9e78876c9ff1dd9", "guid": "bfdfe7dc352907fc980b868725387e9869350723e1690d450f2df2103a05a611"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c62162340d42882b5650e4d974994bc4", "guid": "bfdfe7dc352907fc980b868725387e98bbd9f584a9f9ac94e42e5865c449e955"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98557f1c380eb00045766211069fa3859e", "guid": "bfdfe7dc352907fc980b868725387e98acf9737f017ab5d168c9f30a0faa5f90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5bac1eb68fbcb389e386b69ecaeaa2d", "guid": "bfdfe7dc352907fc980b868725387e98ae4fe099fe56a44462e6388770eb7124"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838e7202358670fbefd61b52c096cb953", "guid": "bfdfe7dc352907fc980b868725387e984f1b971599272f77f45811332ea42c13"}], "guid": "bfdfe7dc352907fc980b868725387e981471165fa844fa1faa3347093109f47e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e9874931db5d8dbb437369dfcf894dc68e2"}], "guid": "bfdfe7dc352907fc980b868725387e9888e32946d8019edf8bd28d10364dc9d5", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9867ce76049bbd164e30060a283f08bba4", "targetReference": "bfdfe7dc352907fc980b868725387e98b9038e7e871b75150c57ed973218b158"}], "guid": "bfdfe7dc352907fc980b868725387e983a8a5e1cfed65dc4530dce11917c56f4", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98b9038e7e871b75150c57ed973218b158", "name": "camera_avfoundation-camera_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f08a09402d437c098acddc7bcf497e64", "name": "camera_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983903b9d6299cde09dc2b081ad04abafe", "name": "camera_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}