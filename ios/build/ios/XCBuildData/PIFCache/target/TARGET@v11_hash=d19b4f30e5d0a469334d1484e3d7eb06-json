{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b6cd472b1a5048f215adef9b15989297", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982f2356d833fa6b2b3f611a978e6174fc", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98eaec75abd828429e4165b009d267252d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985d6a1f3488da8a3c7be735e6b47af82d", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98eaec75abd828429e4165b009d267252d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98792cab6ac65d52f02bd12cd3010ff770", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9867c7cac24bdfd76a061a44febf70da64", "guid": "bfdfe7dc352907fc980b868725387e988e99f2ce6d9e65acbafc37fbbdeb6675"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4d8370191c7c6fd3191c51f4ba6201b", "guid": "bfdfe7dc352907fc980b868725387e9825542e74b959b49c5a0dfac13f23113c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828c55d1f4b4740f90b922661d97eb47b", "guid": "bfdfe7dc352907fc980b868725387e989b2ea4b38631471232df86595af2da06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816edc6b29fbb06182836c4b13de46978", "guid": "bfdfe7dc352907fc980b868725387e98d4ffc7e8c9078b40a48988369e45c97d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986984b170b6b1d2c5ad315d8db388501f", "guid": "bfdfe7dc352907fc980b868725387e98f748835ab68cc4a38cbc9c3f9ed0f3c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981dfe6bef84cbd6fb1992afd9f0959ab7", "guid": "bfdfe7dc352907fc980b868725387e98f6faf7f6d43f0570374b6f4c6133f9ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5dea6e725fc80a334ac76fef0f2890e", "guid": "bfdfe7dc352907fc980b868725387e985a4c2ea412afc3db6c2e7ad3faf85486"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98faf8cc7e5596d2764d9de5b33de67f22", "guid": "bfdfe7dc352907fc980b868725387e98f65ec29c530310d0cd1b274bd978e27c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd33a37d6267da67552b8718d41f6cd0", "guid": "bfdfe7dc352907fc980b868725387e98b3191fb14dbbbc008b200867df4ec532", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9663408cd5d61a42412a370e0e59911", "guid": "bfdfe7dc352907fc980b868725387e98b892d42d2159af3bc8f211957bd08400"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bcbc86254eacc8c26a4ed9382dcc7a5", "guid": "bfdfe7dc352907fc980b868725387e982044ac6b6286df78e0879dd3b4d3c2f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7e401e1963ead5a8e3d46003c43fcb7", "guid": "bfdfe7dc352907fc980b868725387e984e76532c9fa20b75fcc4ce4f0fdbf2f4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986acea8dd23937e6a185bfeb037c2c0d7", "guid": "bfdfe7dc352907fc980b868725387e989616e5000e9029c41b57f0683a9f90d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e9ae0ba84c8603edd8aaf0c7d13e5a3", "guid": "bfdfe7dc352907fc980b868725387e98eb0e1936e84e2d78cbb9fcaf6dcc5320", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d745b4747e30224cfe7a864e5bbe631", "guid": "bfdfe7dc352907fc980b868725387e985d72e576a3ca0261cf94e10f2ee27702"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98995ff16541cacb16e2e3a09455e3d703", "guid": "bfdfe7dc352907fc980b868725387e980e9174ee05ec7909c57151c3c62230b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984daeadc80bbe5c785dc2300fdd7925cc", "guid": "bfdfe7dc352907fc980b868725387e986ca4e609b4fdeb37c0c472a42457963d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98594e357486db3fc33af6606fc0e9e516", "guid": "bfdfe7dc352907fc980b868725387e98403937dfac057b77acf1a2fb7d20d542"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bbd30eec76fd2bc030daf98ad511c38", "guid": "bfdfe7dc352907fc980b868725387e98760577d20255bfff1f4bd9f9401e55ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ad1e4d707360d684344c6905d57a461", "guid": "bfdfe7dc352907fc980b868725387e98bb7aaa997ebaeb391ff8a0c4d754fbfe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e285d7bd9df9e430b3198a1d4fba3dac", "guid": "bfdfe7dc352907fc980b868725387e98599037abc92a398e319b66b392615c9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d475cc2d5dead12ea4bc2b1e31e2c8e", "guid": "bfdfe7dc352907fc980b868725387e9834bf68ec6770189b15be5ab74b2b8de8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843b3a76734b2df8ee878fc616dfe4317", "guid": "bfdfe7dc352907fc980b868725387e98a327a49b2f84d11e7fd65b630d86d3a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f9628ea146dcee58f215fdafbc10531", "guid": "bfdfe7dc352907fc980b868725387e980ccff499983f96093ef91fa71b786793"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f08c743a82d7a4988d06cf3923dad60", "guid": "bfdfe7dc352907fc980b868725387e987f54342731805ba01161c3b6c7f0567a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a7dfb816ea4cf9af86336129dfc2c52", "guid": "bfdfe7dc352907fc980b868725387e98c9e87fbb4fb3e951f84d75e5e9b513c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c76c91117eb07d843fa3137713bec3b2", "guid": "bfdfe7dc352907fc980b868725387e9832797abef203df2ae67ff4b1478f1228"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fd24f6abf69ca32d15d213c209264b3", "guid": "bfdfe7dc352907fc980b868725387e98665ecddb825ad937f4fc1c70415444c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3fce75ff9722cad51982c2ee0df3986", "guid": "bfdfe7dc352907fc980b868725387e98d0d777de34e1ea9da11bc9715951b257"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98678c9b7583b1d67fe661f4a8a97ffc2a", "guid": "bfdfe7dc352907fc980b868725387e98d842d33966a24c6686f3fe5ad5c607c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecb819e6f83fdcdd22bb254e31519674", "guid": "bfdfe7dc352907fc980b868725387e9810ef08e6127e572884cc52031e6d50e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98840278e81cae7cb566006ceb1cbdddb4", "guid": "bfdfe7dc352907fc980b868725387e98da696b055394f3c3c23be0ae56b160d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98721f20d7b3f492c26204e860b2a242b5", "guid": "bfdfe7dc352907fc980b868725387e985dc636ee0525d1dc9b5108c103042510"}], "guid": "bfdfe7dc352907fc980b868725387e9849b1621a4e6e3f4604f9217275a8bc1b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989888e6e0957894984f761e7a1e501265", "guid": "bfdfe7dc352907fc980b868725387e988bd1637d26c62f6e3a902a04479aaf9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b93457d081f9591cf85e835e412d4d3", "guid": "bfdfe7dc352907fc980b868725387e98431c14ae6c412f1857aa8f426b45dbdb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec16b220e00934eac627c6f9aa010baa", "guid": "bfdfe7dc352907fc980b868725387e986b186a3ed6929f24119cced76188356f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813ec44abeadbfab9d49520684a3c3989", "guid": "bfdfe7dc352907fc980b868725387e98fa8d102767f9988cca7fd14833b26aa4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b9c8d80071e8d0ce4d0885559a21308", "guid": "bfdfe7dc352907fc980b868725387e9803358d131ebcfadb445c7d591771fc82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d2970ad2e753d97001d45a504acf961", "guid": "bfdfe7dc352907fc980b868725387e98cc88d4f50dea5c2ca040134067e3aa24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ad6cd2daccd7224fe47715bd22f5fcf", "guid": "bfdfe7dc352907fc980b868725387e98a68688f5b55d95bbfd1af919427a1204"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988309f4d03c9f530b67dc021a7fc8856c", "guid": "bfdfe7dc352907fc980b868725387e98fc931fb265628d8c2ccfc503cb346261"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804c131b206754696ca4c806f73257d37", "guid": "bfdfe7dc352907fc980b868725387e98fb5d16580e3bd296dfeb4d38960c6056"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d550d5377fd493c3b4a9940bf41393df", "guid": "bfdfe7dc352907fc980b868725387e98121c2d6d61f3f96bc679e0871efffa85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98545b3e0a4bcebb551ffc5ad2302149c6", "guid": "bfdfe7dc352907fc980b868725387e98a939f9d8a3de2d75b08a273e3d8cb6ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804092aaf0e9059b5952d471c72ba5516", "guid": "bfdfe7dc352907fc980b868725387e987578424320d59c10412269570127280f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98932a6b17c85ef8f66620acdadb1ca736", "guid": "bfdfe7dc352907fc980b868725387e983c66ab380a6588017a5f34183c7910c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858958bebdca8301b2b905073b49fbb96", "guid": "bfdfe7dc352907fc980b868725387e98b2d70e35c9163902830351770e8dd37a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800adcf3a9c89adb909034cb189229e38", "guid": "bfdfe7dc352907fc980b868725387e989f1da654789884eab28b82bce072b7ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987507a92926c20960ba4d91894c92932a", "guid": "bfdfe7dc352907fc980b868725387e98e19cf1b07b9368c0e50ff5b5e4a36c7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a56c937ff643f9d16fdb2feff55eedff", "guid": "bfdfe7dc352907fc980b868725387e98b1eb45e14dc548393583bfaac9e73f8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839cc3392d923f1e0308583c39d0915b8", "guid": "bfdfe7dc352907fc980b868725387e985d5a4f57275188d83cca50449dae70ae"}], "guid": "bfdfe7dc352907fc980b868725387e98da315a7ffd2d4da89287c16eaeaef104", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e9846abbc93c4f0552d8bc426ed9c32fd11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2b2d6d2a2281a64563abbb1eabdd5a3", "guid": "bfdfe7dc352907fc980b868725387e985be524ae8883a1ad94a665e124d20d42"}], "guid": "bfdfe7dc352907fc980b868725387e98e62cabf336d00488049aba8d6215aceb", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e985bc12c8341469f29eff18431980f1ecf", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e983e8aa6b7c9f581c841ea95bb575da37f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}