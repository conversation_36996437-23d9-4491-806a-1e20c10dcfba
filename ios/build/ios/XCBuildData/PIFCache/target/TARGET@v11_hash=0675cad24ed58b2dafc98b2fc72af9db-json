{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9834f71d96241faef56d9728e6fd3b7a6f", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/wakelock_plus", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "wakelock_plus", "INFOPLIST_FILE": "Target Support Files/wakelock_plus/ResourceBundle-wakelock_plus_privacy-wakelock_plus-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "wakelock_plus_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e983d417320aba0f955f0e417c229aca2b3", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a651ae2a531da6de6d5f56ca6789719d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/wakelock_plus", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "wakelock_plus", "INFOPLIST_FILE": "Target Support Files/wakelock_plus/ResourceBundle-wakelock_plus_privacy-wakelock_plus-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "wakelock_plus_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98a057556c8fd5b9dd4b1f51ca266281d9", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a651ae2a531da6de6d5f56ca6789719d", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/wakelock_plus", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "wakelock_plus", "INFOPLIST_FILE": "Target Support Files/wakelock_plus/ResourceBundle-wakelock_plus_privacy-wakelock_plus-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "wakelock_plus_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98f6b3a58a0e50b3c13472b7df17e6aa29", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9833ee1ec26f45b6a9deac0eb9766eb2b8", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98489d4dd1d91826613f3b1ee05f5ac6fc", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9838f4eb8951e0a0a8eed8ff626e1ffebb", "guid": "bfdfe7dc352907fc980b868725387e98255f5f26cb90604c8c10dd7858ad3c90"}], "guid": "bfdfe7dc352907fc980b868725387e98b39e71a76513b59a56745a1b0de549ad", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98581b37173e8b6c1bb9293721a603b6cb", "name": "wakelock_plus-wakelock_plus_privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e985bb85bab05ce137e1d4577ccc5273f1c", "name": "wakelock_plus_privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}