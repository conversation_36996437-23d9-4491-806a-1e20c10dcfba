{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980d79262d4628f7d53022009acab568c2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseABTesting", "PRODUCT_NAME": "FirebaseABTesting", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fbdc7fabc8ab1f3fe3ee478f4407bd2d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982687370a4e14c04a888b6b7d43009075", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting.modulemap", "PRODUCT_MODULE_NAME": "FirebaseABTesting", "PRODUCT_NAME": "FirebaseABTesting", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f859688e329da53fee1d7f29dfe76a3c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982687370a4e14c04a888b6b7d43009075", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting.modulemap", "PRODUCT_MODULE_NAME": "FirebaseABTesting", "PRODUCT_NAME": "FirebaseABTesting", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d35570c97c6b0081b4335a943d8da1ed", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98eb5991172706ebbbaff5d9f7c86141b6", "guid": "bfdfe7dc352907fc980b868725387e980b62d9b696f0b16973c9fea5b4309c92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3007c4efc24a28c692f737ee4268100", "guid": "bfdfe7dc352907fc980b868725387e98c434251cb25fc7886260d8093ec337aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbbd46efd438d3fc5a981fb08efb99bf", "guid": "bfdfe7dc352907fc980b868725387e98908efda3c1a837d8331f2403cf7bf1b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7b658de0c24a8cd6ddee2409fb351c8", "guid": "bfdfe7dc352907fc980b868725387e9838be365d41fdaf81bf50d43a1bfe251e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3a83528fa07752a2292473fa3b91ea8", "guid": "bfdfe7dc352907fc980b868725387e98cd2f4740f0b73b9138e0967805523e39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98534fc4e93114f501e51535f2d0d753d7", "guid": "bfdfe7dc352907fc980b868725387e98d675ff82bf43fa3965e44e1f6bb0b172"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa3d51c05a11c9c6ed572ace5131f6ad", "guid": "bfdfe7dc352907fc980b868725387e98e1255be828a8a8ad2beb9c0cf8c7a79c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984518e971b096cb8e2e18379a2a52601e", "guid": "bfdfe7dc352907fc980b868725387e98ed28aec22557ec28f35b3f2ffccb4f24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98449a0c979f33dbef2656b7262108c510", "guid": "bfdfe7dc352907fc980b868725387e981f0be385c0b12c6c405521f96957b13c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e365d961e7f06ea6f6be391634852af", "guid": "bfdfe7dc352907fc980b868725387e98450651c883f32d24c948fc524249f64a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98964b6fed6836cb2e68cb002977091e96", "guid": "bfdfe7dc352907fc980b868725387e986cd0cf5ac66779846e827ae01487f632", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a52a22383ee843e31996ae3da29f021", "guid": "bfdfe7dc352907fc980b868725387e98c0f17d20129202bed19a5bb7b0d540ef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c869bb64a75df1113b5594981d63543", "guid": "bfdfe7dc352907fc980b868725387e9833c8bc5ebe757d4863d58d9b6e320101"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880edc026bddfd2677500dc9c9f840cdd", "guid": "bfdfe7dc352907fc980b868725387e9868ba8d6aef58720e7f9d891086074218"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b29fa4912f19467884a559c963fd1f4", "guid": "bfdfe7dc352907fc980b868725387e981c26a762e35b04984e13cc3fe893325d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98453780e0cbc7ce2e74577ba7d237b0a5", "guid": "bfdfe7dc352907fc980b868725387e9813f1ca090d316fc3e0853b3b9e9e5773"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a7bcff22d482f4d75ca9517c156a724", "guid": "bfdfe7dc352907fc980b868725387e9833b8dbd8e7e16d8d2b8cb266ff7a0f27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddc7c046eecf984a9716333ee629d4f7", "guid": "bfdfe7dc352907fc980b868725387e98076b9eb6d8ab910ca1a69e894500a8ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f222783f45899eefd3c17fed7f0cc5bc", "guid": "bfdfe7dc352907fc980b868725387e9800e62e12e740757b0728d39156ce1d87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988824fa2062c5c9717076416dd7018810", "guid": "bfdfe7dc352907fc980b868725387e9844225e39a2503e54d8cce82191fc7ede", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874b607181dd71d2ec3373bb8d016db53", "guid": "bfdfe7dc352907fc980b868725387e980d4a72027a1a0c9b66f5a60bd55959b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981477e84c23e6537c60b2bc15c394608a", "guid": "bfdfe7dc352907fc980b868725387e98ff0a6c3bc3b6b4a15837af3b5e608d09"}], "guid": "bfdfe7dc352907fc980b868725387e9898a73203e7e92c9b758c7c4d32a6f35a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c0e432ed4a51e4c60e273bcd9e59f662", "guid": "bfdfe7dc352907fc980b868725387e981593bdce3a1b50fc76c222b9bbf704a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98038970a989f956160d16d5350ef9bf75", "guid": "bfdfe7dc352907fc980b868725387e985d7757e6d19dffb72311819c7dcb2e17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a15dc915f4b5e69c634b97ef4faa168", "guid": "bfdfe7dc352907fc980b868725387e986ec110dbc460496a950ea156d168a16c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859058f64483849c43ce4ef8331649ecb", "guid": "bfdfe7dc352907fc980b868725387e98d2dceadcea6741e9294a4757cec436ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980802a009f087b8a7e6ed1b5c341e5cee", "guid": "bfdfe7dc352907fc980b868725387e988e3b35644844d7322df10504fc6424a9"}], "guid": "bfdfe7dc352907fc980b868725387e986e93e4b5e570a69fe38d82137ffd1165", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e98cbd7b146dffdaa2f2b39c661b76f0919"}], "guid": "bfdfe7dc352907fc980b868725387e98fea2c4203514bd42f0e64f898e22e0b2", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e987aa92c269b5e189940f43d3fdbca4e5e", "targetReference": "bfdfe7dc352907fc980b868725387e98b9cce05bc25808e2e3952904a4034443"}], "guid": "bfdfe7dc352907fc980b868725387e983965dd7a674ef8f302f62891828ec985", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98b9cce05bc25808e2e3952904a4034443", "name": "FirebaseABTesting-FirebaseABTesting_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}], "guid": "bfdfe7dc352907fc980b868725387e984d1b80eb520d7ec9828b3cb4e14dcb65", "name": "FirebaseABTesting", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98388ecc0b6beee3823c42c78ba6025714", "name": "FirebaseABTesting.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}