rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isAdmin() {
      return isAuthenticated() && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true;
    }
    
    function isVerified() {
      return isAuthenticated() && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isVerified == true;
    }
    
    function isBillionaire() {
      return isAuthenticated() && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isBillionaire == true;
    }

    // Users collection - Enhanced security with better access control
    match /users/{userId} {
      // Allow authenticated users to read basic profile info for app functionality
      allow read: if isAuthenticated();

      // Full write access only to own profile
      allow create: if isAuthenticated() && isOwner(userId);
      allow update: if isAuthenticated() && isOwner(userId);
      allow delete: if isAuthenticated() && (isOwner(userId) || isAdmin());

      // Allow updating follower/following counts for follow/unfollow operations
      // This allows any authenticated user to update these specific fields only
      allow update: if isAuthenticated() &&
        request.resource.data.diff(resource.data).affectedKeys().hasAny(['followerCount', 'followingCount']) &&
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['followerCount', 'followingCount', 'updatedAt']);

      // Allow updating post count for post count recalculation (owner only)
      allow update: if isAuthenticated() && isOwner(userId) &&
        request.resource.data.diff(resource.data).affectedKeys().hasAny(['postCount']) &&
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['postCount', 'updatedAt']);
      
      // User subcollections
      match /{subcollection}/{document=**} {
        allow read, write: if isAuthenticated() && isOwner(userId);
      }
      
      // Special access for followers/following
      match /followers/{followerId} {
        allow read: if isAuthenticated() && (isOwner(userId) || request.auth.uid == followerId);
        allow write: if isAuthenticated() && request.auth.uid == followerId;
      }
      
      match /following/{followingId} {
        allow read: if isAuthenticated() && (isOwner(userId) || request.auth.uid == followingId);
        allow write: if isAuthenticated() && isOwner(userId);
      }
    }
    
    // Posts collection - Enhanced with content moderation
    match /posts/{postId} {
      allow read: if isAuthenticated();

      allow create: if isAuthenticated() &&
        request.auth.uid == request.resource.data.userId &&
        // Content moderation checks - using caption field instead of content
        request.resource.data.caption.size() <= 2000 &&
        !request.resource.data.caption.matches('.*spam.*');

      allow update: if isAuthenticated() &&
        (request.auth.uid == resource.data.userId ||
         // Allow migration updates for status, isDeleted, and updatedAt fields
         (request.resource.data.diff(resource.data).affectedKeys().hasOnly(['status', 'isDeleted', 'updatedAt']) &&
          request.auth.uid == resource.data.userId)) &&
        // Content moderation for caption updates
        (!request.resource.data.diff(resource.data).affectedKeys().hasAny(['caption']) ||
         request.resource.data.caption.size() <= 2000);

      allow delete: if isAuthenticated() &&
        (request.auth.uid == resource.data.userId || isAdmin());

      // Post subcollections (likes, comments, reactions)
      match /{subcollection}/{document=**} {
        allow read: if isAuthenticated();
        allow write: if isAuthenticated();
      }
    }
    
    // Events collection - Enhanced with verification
    match /events/{eventId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && 
        (isVerified() || isBillionaire() || isAdmin());
      allow update, delete: if isAuthenticated() && 
        (request.auth.uid == resource.data.hostId || isAdmin());
      
      // Event attendees
      match /attendees/{attendeeId} {
        allow read: if isAuthenticated();
        allow write: if isAuthenticated() && request.auth.uid == attendeeId;
      }
    }
    
    // Stories collection - Enhanced privacy with view tracking
    match /stories/{storyId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && request.auth.uid == request.resource.data.userId;

      // Allow story owner to update/delete their stories
      allow update, delete: if isAuthenticated() && request.auth.uid == resource.data.userId;

      // Allow deletion of expired stories (for cleanup service)
      allow delete: if isAuthenticated() &&
        resource.data.expiresAt != null &&
        resource.data.expiresAt < request.time;

      // Allow any authenticated user to mark stories as viewed (for view tracking)
      allow update: if isAuthenticated() &&
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['isSeen', 'viewCount', 'lastViewedAt']);
    }
    
    // Story views collection for analytics
    match /story_views/{storyId} {
      allow read: if isAuthenticated() && 
        (request.auth.uid == resource.data.storyOwnerId || isAdmin());
      
      match /viewers/{viewerId} {
        allow read, write: if isAuthenticated() && request.auth.uid == viewerId;
      }
    }
    
    // Marketplace collection - Enhanced with verification
    match /marketplace/{itemId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && 
        (isVerified() || isBillionaire() || isAdmin()) &&
        request.auth.uid == resource.data.sellerId;
      allow update, delete: if isAuthenticated() && 
        (request.auth.uid == resource.data.sellerId || isAdmin());
    }
    
    // Notifications collection - Enhanced for social interactions
    match /notifications/{notificationId} {
      // Allow users to read their own notifications
      allow read: if isAuthenticated() && 
        request.auth.uid == resource.data.userId;
      
      // Allow users to create notifications for other users (for social interactions)
      allow create: if isAuthenticated() && 
        request.auth.uid == request.resource.data.fromUserId &&
        request.auth.uid != request.resource.data.userId;
      
      // Allow users to update their own notifications (mark as read, etc.)
      allow update: if isAuthenticated() && 
        request.auth.uid == resource.data.userId;
      
      // Allow users to delete their own notifications
      allow delete: if isAuthenticated() && 
        request.auth.uid == resource.data.userId;
    }

    // Follows collection - For follower/following relationships
    match /follows/{followId} {
      // Allow reading follow relationships for authenticated users
      allow read: if isAuthenticated();

      // Allow creating follow relationships if user is the follower
      allow create: if isAuthenticated() &&
        request.auth.uid == request.resource.data.followerId;

      // Allow updating/deleting follow relationships if user is the follower
      allow update, delete: if isAuthenticated() &&
        request.auth.uid == resource.data.followerId;
    }

    // Chat collection for messaging
    match /chats/{chatId} {
      allow read, write: if isAuthenticated();
      
      // Chat participants
      match /participants/{participantId} {
        allow read, write: if isAuthenticated();
      }
      
      // Chat messages
      match /messages/{messageId} {
        allow read, write: if isAuthenticated();
      }
    }
    
    // Admin collection - Restricted to admins only
    match /admin/{document=**} {
      allow read, write: if isAdmin();
    }
    
    // Analytics collection - Restricted access
    match /analytics/{document=**} {
      allow read, write: if isAuthenticated() && 
        (isAdmin() || request.auth.uid == resource.data.userId);
    }
    
    // System settings - Admin only
    match /system/{document=**} {
      allow read, write: if isAdmin();
    }
  }
} 