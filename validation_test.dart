// ignore_for_file: avoid_print

// Quick validation test - add this to a test screen or main.dart

import 'package:billionaires_social/core/services/input_validation_service.dart';
import 'package:billionaires_social/core/services/rate_limiting_service.dart';
import 'package:billionaires_social/core/services/memory_management_service.dart';

void validateImplementations() {
  print('🧪 Running Implementation Validation...');

  try {
    // Test Input Validation
    final validator = InputValidationService();
    final emailResult = validator.validateEmail('<EMAIL>');
    print('Email validation: ${emailResult.isValid ? "✅ PASS" : "❌ FAIL"}');

    // Test Rate Limiting
    final rateLimiter = RateLimitingService();
    final rateResult = rateLimiter.checkRateLimit('test', 'user1');
    print('Rate limiting: ${rateResult.allowed ? "✅ PASS" : "❌ FAIL"}');

    // Test Memory Management
    final memoryService = MemoryManagementService();
    final stats = memoryService.getMemoryStats();
    print(
      'Memory management: ${stats.currentMemoryMB >= 0 ? "✅ PASS" : "❌ FAIL"}',
    );

    print('🎉 All validations completed!');
  } catch (e) {
    print('❌ Validation error: $e');
  }
}
