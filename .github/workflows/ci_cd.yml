name: Flutter CI/CD Pipeline

on:
  push:
    branches: [ main, release ]
  pull_request:
    branches: [ main ]

env:
  FLUTTER_VERSION: '3.28.5'

jobs:
  analyze_and_test:
    name: Analyze & Test
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          
      - name: Get dependencies
        run: flutter pub get
        
      - name: Analyze code
        run: flutter analyze --no-fatal-infos
        
      - name: Run tests
        run: flutter test --coverage

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: coverage/lcov.info

      - name: Run E2E tests
        uses: reactivecircus/android-emulator-runner@v2
        with:
          api-level: 29
          script: flutter test integration_test/

      - name: Run accessibility tests
        run: flutter test test/accessibility/

      - name: Run performance tests
        run: |
          dart run performance_monitor.dart
          dart run test_validation.dart

      - name: Run analytics validator
        run: dart run lib/core/services/analytics_validator.dart

  build_android:
    name: Build Android APK
    runs-on: ubuntu-latest
    needs: analyze_and_test
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/release'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          
      - name: Get dependencies
        run: flutter pub get
        
      - name: Build APK
        run: flutter build apk --release
        
      - name: Upload APK artifact
        uses: actions/upload-artifact@v4
        with:
          name: app-release
          path: build/app/outputs/flutter-apk/app-release.apk
          
      - name: Upload to Firebase App Distribution
        uses: wzieba/Firebase-Distribution-Github-Action@v1
        with:
          appId: ${{ secrets.FIREBASE_APP_ID_ANDROID }}
          serviceCredentialsFileContent: ${{ secrets.FIREBASE_SERVICE_ACCOUNT_KEY }}
          groups: internal-testers
          file: build/app/outputs/flutter-apk/app-release.apk
          releaseNotes: |
            Automated build from commit: ${{ github.sha }}
            Branch: ${{ github.ref_name }}
            Build Date: ${{ github.event.head_commit.timestamp }}

  build_ios:
    name: Build iOS Archive
    runs-on: macos-latest
    needs: analyze_and_test
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/release'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          
      - name: Get dependencies
        run: flutter pub get
        
      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: '3.0'
          
      - name: Install CocoaPods
        run: |
          cd ios
          pod install
          
      - name: Build iOS Archive
        run: |
          flutter build ios --release --no-codesign
          
      - name: Upload iOS build artifact
        uses: actions/upload-artifact@v4
        with:
          name: ios-build
          path: build/ios/archive/Runner.xcarchive

  security_scan:
    name: Security & Dependency Scan
    runs-on: ubuntu-latest
    needs: analyze_and_test
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          
      - name: Get dependencies
        run: flutter pub get
        
      - name: Run dependency vulnerability scan
        run: flutter pub deps --style=tree
        
      - name: Check for outdated dependencies
        run: flutter pub outdated

  deploy_production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build_android, build_ios, security_scan]
    if: github.event_name == 'release'

    steps:
      - name: Download Android artifacts
        uses: actions/download-artifact@v4
        with:
          name: app-release

      - name: Download iOS artifacts
        uses: actions/download-artifact@v4
        with:
          name: ios-build

      - name: Deploy to Google Play Store
        uses: r0adkll/upload-google-play@v1
        with:
          serviceAccountJsonPlainText: ${{ secrets.GOOGLE_PLAY_SERVICE_ACCOUNT }}
          packageName: com.billionaires.social
          releaseFiles: app-release.apk
          track: production
          status: completed

      - name: Deploy to App Store Connect
        uses: apple-actions/upload-testflight-build@v1
        with:
          app-path: ios-build/Runner.xcarchive
          issuer-id: ${{ secrets.APPSTORE_ISSUER_ID }}
          api-key-id: ${{ secrets.APPSTORE_API_KEY_ID }}
          api-private-key: ${{ secrets.APPSTORE_API_PRIVATE_KEY }}

  notify_success:
    name: Notify Success
    runs-on: ubuntu-latest
    needs: [build_android, build_ios, security_scan]
    if: success() && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/release')

    steps:
      - name: Notify team via Slack
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          fields: repo,message,commit,author,action,eventName,ref,workflow
          text: |
            ✅ Build successful for ${{ github.ref_name }}
            📱 Android APK uploaded to Firebase App Distribution
            🍎 iOS archive ready for TestFlight upload
            🔍 Security scan completed
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}