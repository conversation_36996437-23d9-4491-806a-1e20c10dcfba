#!/usr/bin/env dart
// ignore_for_file: avoid_print

/// Simple test validation script to verify our implementations
/// This runs independently of Flutter's test framework to avoid dependency issues
library;

import 'dart:io';

// Mock classes for testing without Flutter dependencies
class ValidationResult {
  final bool isValid;
  final String message;
  ValidationResult(this.isValid, this.message);
  @override
  String toString() => 'ValidationResult(isValid: $isValid, message: $message)';
}

class InputValidationService {
  ValidationResult validateEmail(String email) {
    if (email.isEmpty) {
      return ValidationResult(false, 'Email is required');
    }

    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(email)) {
      return ValidationResult(false, 'Invalid email format');
    }

    if (email.length > 254) {
      return ValidationResult(false, 'Email is too long');
    }

    if (_containsDangerousPatterns(email)) {
      return ValidationResult(false, 'Email contains invalid characters');
    }

    return ValidationResult(true, 'Valid email');
  }

  ValidationResult validatePassword(String password) {
    if (password.isEmpty) {
      return ValidationResult(false, 'Password is required');
    }

    if (password.length < 8) {
      return ValidationResult(false, 'Password must be at least 8 characters');
    }

    if (password.length > 128) {
      return ValidationResult(false, 'Password is too long');
    }

    if (!RegExp(r'[A-Z]').hasMatch(password)) {
      return ValidationResult(
        false,
        'Password must contain at least one uppercase letter',
      );
    }

    if (!RegExp(r'[a-z]').hasMatch(password)) {
      return ValidationResult(
        false,
        'Password must contain at least one lowercase letter',
      );
    }

    if (!RegExp(r'[0-9]').hasMatch(password)) {
      return ValidationResult(
        false,
        'Password must contain at least one number',
      );
    }

    if (!RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) {
      return ValidationResult(
        false,
        'Password must contain at least one special character',
      );
    }

    if (_isCommonPassword(password)) {
      return ValidationResult(
        false,
        'Password is too common, please choose a stronger password',
      );
    }

    return ValidationResult(true, 'Strong password');
  }

  bool _containsDangerousPatterns(String input) {
    final dangerousPatterns = [
      r'<script[^>]*>.*?</script>',
      r'javascript:',
      r'onclick\s*=',
      r'onerror\s*=',
      r'onload\s*=',
    ];

    for (final pattern in dangerousPatterns) {
      if (RegExp(pattern, caseSensitive: false).hasMatch(input)) {
        return true;
      }
    }
    return false;
  }

  bool _isCommonPassword(String password) {
    final commonPasswords = [
      'password',
      '123456',
      '123456789',
      'qwerty',
      'abc123',
      'password123',
      'admin',
      'letmein',
      'welcome',
      'monkey',
    ];
    return commonPasswords.contains(password.toLowerCase());
  }
}

class RateLimitResult {
  final bool allowed;
  final int remainingRequests;
  final DateTime resetTime;
  final String? message;

  RateLimitResult({
    required this.allowed,
    required this.remainingRequests,
    required this.resetTime,
    this.message,
  });
}

class RateLimitConfig {
  final int maxRequests;
  final int windowMinutes;
  const RateLimitConfig({
    required this.maxRequests,
    required this.windowMinutes,
  });
}

class RateLimitingService {
  final Map<String, List<DateTime>> _requestHistory = {};
  static const Map<String, RateLimitConfig> _rateLimits = {
    'login': RateLimitConfig(maxRequests: 5, windowMinutes: 15),
    'post_creation': RateLimitConfig(maxRequests: 10, windowMinutes: 60),
  };

  RateLimitResult checkRateLimit(String action, String identifier) {
    final config = _rateLimits[action];
    if (config == null) {
      return RateLimitResult(
        allowed: true,
        remainingRequests: 999,
        resetTime: DateTime.now().add(const Duration(hours: 1)),
      );
    }

    final key = '${action}_$identifier';
    final now = DateTime.now();
    final windowStart = now.subtract(Duration(minutes: config.windowMinutes));

    _requestHistory[key] ??= [];
    _requestHistory[key]!.removeWhere((time) => time.isBefore(windowStart));

    final currentRequests = _requestHistory[key]!.length;
    final remainingRequests = config.maxRequests - currentRequests;

    if (currentRequests >= config.maxRequests) {
      final oldestRequest = _requestHistory[key]!.first;
      final resetTime = oldestRequest.add(
        Duration(minutes: config.windowMinutes),
      );

      return RateLimitResult(
        allowed: false,
        remainingRequests: 0,
        resetTime: resetTime,
        message: 'Rate limit exceeded',
      );
    }

    _requestHistory[key]!.add(now);
    return RateLimitResult(
      allowed: true,
      remainingRequests: remainingRequests - 1,
      resetTime: now.add(Duration(minutes: config.windowMinutes)),
    );
  }

  void clearAllRateLimits() {
    _requestHistory.clear();
  }
}

// Test runner
void main() {
  print('🧪 Running Implementation Validation Tests...\n');

  int passed = 0;
  int failed = 0;

  void test(String description, bool Function() testFunction) {
    try {
      final result = testFunction();
      if (result) {
        print('✅ $description');
        passed++;
      } else {
        print('❌ $description');
        failed++;
      }
    } catch (e) {
      print('❌ $description - Error: $e');
      failed++;
    }
  }

  // Input Validation Tests
  print('📧 Input Validation Service Tests:');
  final validator = InputValidationService();

  test('Valid email should pass', () {
    final result = validator.validateEmail('<EMAIL>');
    return result.isValid;
  });

  test('Invalid email should fail', () {
    final result = validator.validateEmail('invalid-email');
    return !result.isValid;
  });

  test('Strong password should pass', () {
    final result = validator.validatePassword('StrongPass123!');
    return result.isValid;
  });

  test('Weak password should fail', () {
    final result = validator.validatePassword('weak');
    return !result.isValid;
  });

  test('XSS attempt should be blocked', () {
    final result = validator.validateEmail(
      'test<script>alert(1)</script>@example.com',
    );
    return !result.isValid;
  });

  // Rate Limiting Tests
  print('\n🚦 Rate Limiting Service Tests:');
  final rateLimiter = RateLimitingService();

  test('First request should be allowed', () {
    final result = rateLimiter.checkRateLimit('login', 'user1');
    return result.allowed;
  });

  test('Requests within limit should be allowed', () {
    rateLimiter.clearAllRateLimits();
    for (int i = 0; i < 4; i++) {
      rateLimiter.checkRateLimit('login', 'user2');
    }
    final result = rateLimiter.checkRateLimit('login', 'user2');
    return result.allowed;
  });

  test('Requests exceeding limit should be blocked', () {
    rateLimiter.clearAllRateLimits();
    for (int i = 0; i < 5; i++) {
      rateLimiter.checkRateLimit('login', 'user3');
    }
    final result = rateLimiter.checkRateLimit('login', 'user3');
    return !result.allowed;
  });

  test('Different users should have independent limits', () {
    rateLimiter.clearAllRateLimits();
    for (int i = 0; i < 5; i++) {
      rateLimiter.checkRateLimit('login', 'user4');
    }
    final result = rateLimiter.checkRateLimit('login', 'user5');
    return result.allowed;
  });

  // Performance Tests
  print('\n⚡ Performance Tests:');

  test('Email validation performance', () {
    final stopwatch = Stopwatch()..start();
    for (int i = 0; i < 1000; i++) {
      validator.validateEmail('test$<EMAIL>');
    }
    stopwatch.stop();
    final timePerValidation = stopwatch.elapsedMicroseconds / 1000;
    print(
      '   📊 Average validation time: ${timePerValidation.toStringAsFixed(2)} μs',
    );
    return timePerValidation < 100; // Should be under 100 microseconds
  });

  test('Rate limiting performance', () {
    final stopwatch = Stopwatch()..start();
    for (int i = 0; i < 1000; i++) {
      rateLimiter.checkRateLimit('test', 'user$i');
    }
    stopwatch.stop();
    final timePerCheck = stopwatch.elapsedMicroseconds / 1000;
    print(
      '   📊 Average rate limit check time: ${timePerCheck.toStringAsFixed(2)} μs',
    );
    return timePerCheck < 50; // Should be under 50 microseconds
  });

  // Summary
  print('\n📊 Test Results Summary:');
  print('✅ Passed: $passed');
  print('❌ Failed: $failed');
  print(
    '📈 Success Rate: ${(passed / (passed + failed) * 100).toStringAsFixed(1)}%',
  );

  if (failed == 0) {
    print('\n🎉 All tests passed! Implementations are working correctly.');
    exit(0);
  } else {
    print('\n⚠️  Some tests failed. Please review the implementations.');
    exit(1);
  }
}
