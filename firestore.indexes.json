{"indexes": [{"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "createdAt", "order": "DESCENDING"}, {"fieldPath": "userId", "order": "ASCENDING"}]}, {"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isDeleted", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "stories", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "events", "queryScope": "COLLECTION", "fields": [{"fieldPath": "dateTime", "order": "ASCENDING"}, {"fieldPath": "hostId", "order": "ASCENDING"}]}, {"collectionGroup": "stories", "queryScope": "COLLECTION", "fields": [{"fieldPath": "createdAt", "order": "DESCENDING"}, {"fieldPath": "userId", "order": "ASCENDING"}]}, {"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "isArchived", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "trending", "order": "ASCENDING"}, {"fieldPath": "trendingScore", "order": "DESCENDING"}]}, {"collectionGroup": "follows", "queryScope": "COLLECTION", "fields": [{"fieldPath": "followerId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "followedAt", "order": "DESCENDING"}]}, {"collectionGroup": "follows", "queryScope": "COLLECTION", "fields": [{"fieldPath": "followerId", "order": "ASCENDING"}, {"fieldPath": "followingId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}]}, {"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "follows", "queryScope": "COLLECTION", "fields": [{"fieldPath": "followingId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "followedAt", "order": "DESCENDING"}]}, {"collectionGroup": "follows", "queryScope": "COLLECTION", "fields": [{"fieldPath": "followerId", "order": "ASCENDING"}, {"fieldPath": "followingId", "order": "ASCENDING"}]}, {"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "stories", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "stories", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "expiresAt", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isVerified", "order": "ASCENDING"}, {"fieldPath": "followerCount", "order": "DESCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isBillionaire", "order": "ASCENDING"}, {"fieldPath": "followerCount", "order": "DESCENDING"}]}, {"collectionGroup": "chats", "queryScope": "COLLECTION", "fields": [{"fieldPath": "participants", "order": "ASCENDING"}, {"fieldPath": "lastMessageAt", "order": "DESCENDING"}]}, {"collectionGroup": "messages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "chatId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "read", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "comments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "postId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}], "fieldOverrides": []}