# iOS Push Notifications Setup Guide

## Prerequisites
- Xcode 15.0 or later
- Apple Developer Account
- Physical iOS device (APNs tokens are only generated on real devices, not simulators)

## Step 1: Open Project in Xcode

1. Navigate to your Flutter project directory
2. Open the iOS project in Xcode:
   ```bash
   open ios/Runner.xcworkspace
   ```

## Step 2: Configure Signing & Capabilities

1. In Xcode, select the **Runner** project in the navigator
2. Select the **Runner** target
3. Go to the **Signing & Capabilities** tab
4. Make sure you have a valid **Team** selected (Apple Developer Account)

## Step 3: Add Push Notifications Capability

1. Click the **+ Capability** button
2. Search for and add **"Push Notifications"**
3. This will automatically add the necessary entitlements

## Step 4: Add Background Modes Capability

1. Click the **+ Capability** button again
2. Search for and add **"Background Modes"**
3. In the Background Modes section, check:
   - ✅ **Remote notifications**

## Step 5: Verify Entitlements

1. The entitlements file should now include:
   - `aps-environment` (automatically added)
   - `com.apple.developer.usernotifications.time-sensitive` (already added)

## Step 6: Build and Run on Device

1. Connect your iOS device to your Mac
2. Select your device as the target in Xcode
3. Build and run the project (⌘+R)

## Step 7: Test Push Notifications

1. When the app launches, it will request notification permissions
2. Grant permission when prompted
3. Use the Debug Tools screen in the app:
   - Navigate to Debug Tools
   - Tap "Test Push Notifications"
   - Check the console for FCM and APNs tokens

## Expected Console Output

You should see logs like:
```
✅ Firebase initialized successfully
✅ Firebase Analytics collection enabled
✅ Firebase Analytics session timeout set to 30 minutes
✅ Debug test event sent to Firebase Analytics
✅ Service locator setup completed
Notification permission status: AuthorizationStatus.authorized
FCM Token: [your-fcm-token]
APNs Token: [your-apns-token]
```

## Troubleshooting

### If you see `[firebase_messaging/apns-token-not-set]` error:

1. **Make sure you're testing on a real device** (not simulator)
2. **Check that Push Notifications capability is added** in Xcode
3. **Verify the app has notification permissions** (Settings > Notifications > Your App)
4. **Check that the app is properly signed** with your Apple Developer account

### If FCM token is null:

1. **Check internet connection**
2. **Verify Firebase configuration** (GoogleService-Info.plist)
3. **Make sure Firebase is properly initialized** in main.dart

### If APNs token is null:

1. **Ensure you're testing on a real device**
2. **Check that Push Notifications capability is enabled**
3. **Verify the app has notification permissions**
4. **Check that the device has internet connection**

## Additional Notes

- APNs tokens are only generated on physical devices, never on simulators
- The app must be properly signed with an Apple Developer account
- Push notifications require an active internet connection
- The `aps-environment` will be `development` for debug builds and `production` for release builds

## Next Steps

After successful setup:
1. Test push notifications using the Debug Tools
2. Verify tokens appear in Firebase Console
3. Set up your backend to send push notifications using the FCM tokens
4. Test end-to-end push notification delivery 