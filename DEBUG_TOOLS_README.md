# Debug Tools Screen - User Guide

## Overview
The Debug Tools Screen is a comprehensive testing utility that allows you to test analytics events, trigger crashes, simulate performance issues, and validate analytics implementation. It's only available in debug mode and is hidden from production users.

## How to Access
1. **Debug Mode Only**: The debug tools are only available when running the app in debug mode
2. **Floating Action Button**: Look for a red bug icon (🐛) floating action button on the main navigation screen
3. **Tap the Bug Icon**: This will open the Debug Tools Screen

## Features

### 📊 Analytics Testing
- **Test Standard Events**: Tests basic app events (posts, messages, search, etc.)
- **Test Billionaire Events**: Tests luxury-specific events (VIP upgrades, jet bookings, investments, etc.)
- **Test User Properties**: Sets test user properties for analytics
- **Test Screen Tracking**: Tests automatic screen view tracking
- **Test Error Events**: Tests error logging functionality
- **Test Performance Events**: Tests performance metric logging
- **Test Engagement Events**: Tests user engagement tracking
- **Test Feature Usage**: Tests feature usage analytics
- **Test Conversion Events**: Tests conversion tracking
- **Test App Lifecycle**: Tests app lifecycle event tracking

### 🚨 Crash Testing
- **Trigger Test Crash**: Simulates a fatal crash (will crash the app)
- **Trigger Non-Fatal Error**: Logs a non-fatal error to Crashlytics
- **Trigger Custom Exception**: Throws and logs a custom exception

### ⚡ Performance Testing
- **Simulate Slow Network**: Simulates slow network conditions
- **Simulate Heavy Computation**: Performs heavy computation to test performance
- **Simulate Memory Usage**: Simulates high memory usage
- **Test Performance Traces**: Tests various performance monitoring traces

### ✅ Analytics Validation
- **Run Full Analytics Validation**: Runs comprehensive analytics validation
- **Generate Analytics Report**: Generates a detailed analytics implementation report
- **Test Parameter Validation**: Tests analytics parameter validation logic

## Test Results
All test results are displayed in the "Test Results" section at the bottom of the screen. Results show:
- ✅ Success messages for passed tests
- ❌ Error messages for failed tests
- 🚨 Warning messages for important actions (like crashes)

## Usage Tips

### For QA Testing
1. **Analytics Validation**: Run "Run Full Analytics Validation" before each release
2. **Crash Testing**: Use "Trigger Non-Fatal Error" to test error reporting without crashing
3. **Performance Testing**: Use "Simulate Slow Network" to test app behavior under poor conditions

### For Development
1. **Event Testing**: Use individual event tests to verify specific analytics implementations
2. **Parameter Validation**: Use "Test Parameter Validation" to ensure analytics parameters are correct
3. **Performance Monitoring**: Use performance tests to verify monitoring is working

### For Debugging
1. **Analytics Issues**: Use "Generate Analytics Report" to get a comprehensive overview
2. **Crash Issues**: Use crash tests to verify Crashlytics integration
3. **Performance Issues**: Use performance tests to identify bottlenecks

## Safety Features
- **Debug Mode Only**: Tools are completely hidden in production builds
- **Confirmation Delays**: Crash tests have built-in delays to prevent accidental triggers
- **Error Handling**: All tests include proper error handling and user feedback
- **Non-Destructive**: Most tests are non-destructive and safe to run repeatedly

## Integration with Firebase
All tests integrate with your Firebase project:
- **Analytics**: Events are sent to Firebase Analytics
- **Crashlytics**: Crashes and errors are logged to Firebase Crashlytics
- **Performance**: Performance metrics are sent to Firebase Performance Monitoring

## Troubleshooting
- **Tests Not Working**: Ensure you're in debug mode
- **Analytics Not Showing**: Check Firebase Console DebugView
- **Crashes Not Logging**: Verify Crashlytics is properly initialized
- **Performance Not Tracking**: Check Performance Monitoring console

## Best Practices
1. **Regular Testing**: Run analytics validation before each release
2. **Documentation**: Use the analytics event documentation for reference
3. **Monitoring**: Check Firebase Console regularly for test results
4. **Team Coordination**: Coordinate crash testing with your team to avoid confusion

---

**Note**: This debug screen is a development tool and should not be used in production builds. All functionality is automatically disabled in release builds. 