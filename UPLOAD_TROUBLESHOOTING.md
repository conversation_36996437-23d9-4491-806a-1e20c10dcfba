# Upload Troubleshooting Guide

## 🚨 Common Upload Issues Fixed

### 1. **PathNotFoundException: Cannot retrieve length of file**

**Problem**: Compressed image file path becomes invalid after compression
```
PathNotFoundException: Cannot retrieve length of file, path = 
'/private/var/mobile/Containers/Data/Application/.../image_picker_..._compressed.jpg'
(OS Error: No such file or directory, errno = 2)
```

**Root Cause**: Image compression creates a new file but the path construction was incorrect

**✅ Solution Implemented:**
- Fixed `_compressImage()` method with proper path handling
- Added file existence checks before and after compression
- Improved error handling with fallback to original file
- Better directory and filename parsing

```dart
Future<File> _compressImage(File file) async {
  try {
    // Check if original file exists
    if (!await file.exists()) {
      return file;
    }

    // Create proper compressed file path
    final directory = file.parent;
    final fileName = file.path.split('/').last;
    final nameWithoutExtension = fileName.split('.').first;
    final compressedPath = '${directory.path}/${nameWithoutExtension}_compressed.jpg';
    
    // Compress with verification
    final result = await FlutterImageCompress.compressAndGetFile(
      file.absolute.path,
      compressedPath,
      quality: 70,
      minWidth: 800,
      minHeight: 600,
    );
    
    // Verify compressed file exists before returning
    if (result != null && await File(result.path).exists()) {
      return File(result.path);
    }
    
    return file; // Fallback to original
  } catch (e) {
    return file; // Fallback on any error
  }
}
```

### 2. **Firebase Storage Authorization Error**

**Problem**: `[firebase_storage/unauthorized] User is not authorized to perform the desired action`

**Root Causes:**
- User not properly authenticated
- Incorrect Firebase Storage security rules
- Using wrong storage paths

**✅ Solutions Implemented:**

#### A. Enhanced Authentication Checks
```dart
Future<String> uploadImage(String filePath, String fileName) async {
  // Check authentication first
  final user = _auth.currentUser;
  if (user == null) {
    throw Exception('User must be authenticated to upload files');
  }
  
  // Use user-specific paths for security
  final ref = _storage.ref().child('user_uploads/${user.uid}/$fileName');
  // ... rest of upload logic
}
```

#### B. Better Error Handling
```dart
} on FirebaseException catch (e) {
  switch (e.code) {
    case 'storage/unauthorized':
      throw Exception('You do not have permission to upload files. Please check your authentication.');
    case 'storage/quota-exceeded':
      throw Exception('Storage quota exceeded');
    // ... other cases
  }
}
```

#### C. File Validation
```dart
// Verify file exists before upload
if (!await file.exists()) {
  throw Exception('File does not exist: $filePath');
}

// Log file info for debugging
final fileSizeInBytes = await file.length();
debugPrint('File size: ${(fileSizeInBytes / (1024 * 1024)).toStringAsFixed(2)} MB');
```

### 3. **Media Type Detection Issues**

**Problem**: Videos being treated as images, causing compression failures

**✅ Solution:**
```dart
// Improved media type detection
final isVideo = pickedFile.path.toLowerCase().contains('.mp4') ||
               pickedFile.path.toLowerCase().contains('.mov') ||
               pickedFile.path.toLowerCase().contains('.avi') ||
               pickedFile.path.toLowerCase().contains('.mkv');

final mediaType = isVideo ? MediaType.video : MediaType.image;

// Only compress images, not videos
if (mediaType == MediaType.image) {
  file = await _compressImage(file);
}
```

## 🔧 Firebase Storage Security Rules

To fix authorization issues, ensure your Firebase Storage rules allow authenticated users to upload:

```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Allow authenticated users to upload to their own folder
    match /user_uploads/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Legacy paths for backwards compatibility
    match /posts/{allPaths=**} {
      allow read, write: if request.auth != null;
    }
    
    match /reels/{allPaths=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

## 🚀 Testing the Fixes

### 1. **Test Image Upload**
```bash
# Test post creation with image
1. Tap "+" button
2. Select "Post" 
3. Pick an image from gallery
4. Add caption and create post
5. Verify: No PathNotFoundException, successful upload
```

### 2. **Test Video Upload**
```bash
# Test reel creation with video
1. Tap "+" button
2. Select "Reel"
3. Pick a video from gallery  
4. Add caption and create reel
5. Verify: No compression on video, successful upload
```

### 3. **Test Authentication**
```bash
# Test with signed-out user
1. Sign out of the app
2. Try to create content
3. Verify: Proper error message about authentication
```

## 📱 User-Friendly Error Messages

The fixes now provide clear error messages:

- ✅ **File Issues**: "Selected file is no longer available. Please select a new file."
- ✅ **Auth Issues**: "You need to be logged in to upload media"
- ✅ **Storage Issues**: "Upload failed: Storage quota exceeded"
- ✅ **Network Issues**: "Upload failed: Check your internet connection"

## 🔍 Debug Logging

Enhanced logging helps track upload progress:

```dart
debugPrint('🔥 Firebase: Starting upload for user: ${user.uid}');
debugPrint('🔥 Firebase: File path: $filePath');
debugPrint('🔥 Firebase: File size: ${fileSizeInMB.toStringAsFixed(2)} MB');
debugPrint('🔥 Firebase: Storage reference: ${ref.fullPath}');
debugPrint('🔥 Firebase: Upload successful: $downloadUrl');
```

## ✅ Validation Checklist

- [x] File compression path issues fixed
- [x] Firebase Storage authorization handled
- [x] Media type detection improved
- [x] Error messages user-friendly
- [x] File existence validation added
- [x] Authentication checks implemented
- [x] Debug logging enhanced
- [x] Fallback mechanisms added

The upload system is now robust and handles edge cases gracefully while providing clear feedback to users.
