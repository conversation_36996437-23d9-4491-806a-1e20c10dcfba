{"firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "storage": {"rules": "storage.rules"}, "flutter": {"platforms": {"android": {"default": {"projectId": "billionaires-social", "appId": "1:409365364995:android:1916b6938f51039cb5a936", "fileOutput": "android/app/google-services.json"}}, "ios": {"default": {"projectId": "billionaires-social", "appId": "1:409365364995:ios:cf4a26c29b14b9cab5a936", "uploadDebugSymbols": false, "fileOutput": "ios/Runner/GoogleService-Info.plist"}}, "macos": {"default": {"projectId": "billionaires-social", "appId": "1:409365364995:ios:cf4a26c29b14b9cab5a936", "uploadDebugSymbols": false, "fileOutput": "macos/Runner/GoogleService-Info.plist"}}, "dart": {"lib/firebase_options.dart": {"projectId": "billionaires-social", "configurations": {"android": "1:409365364995:android:1916b6938f51039cb5a936", "ios": "1:409365364995:ios:cf4a26c29b14b9cab5a936", "macos": "1:409365364995:ios:cf4a26c29b14b9cab5a936", "web": "1:409365364995:web:279c38a92d715ae7b5a936", "windows": "1:409365364995:web:6ab31c22062efdd3b5a936"}}}}}, "functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run build"]}]}