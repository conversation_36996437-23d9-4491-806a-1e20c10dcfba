# Universal Architecture Guide

## Overview

This guide establishes the universal, inheritance-based architecture for the Billionaires Social app. The architecture eliminates hardcoded user-specific logic and ensures all features work consistently across different user accounts and roles.

## Core Principles

### 1. Universal Logic
- **No hardcoded user IDs**: Never use `if (userId == "specific_id")` checks
- **Role-based behavior**: Use account types and permissions instead of specific user checks
- **Inheritance-based features**: New users automatically inherit all existing functionality
- **Scalable patterns**: All logic must work for any number of users

### 2. Service-Based Architecture
- **Universal Services**: Centralized services handle all business logic
- **Consistent APIs**: All services follow the same patterns and conventions
- **Error Handling**: Universal error handling and user feedback
- **Testing**: All services are testable and validated

### 3. Account-Agnostic Design
- **Default Settings**: Universal defaults for all new accounts
- **Feature Inheritance**: Bug fixes and new features apply to all users
- **Permission-Based Access**: Content and features based on relationships and permissions
- **Consistent UI**: UI adapts based on user context, not hardcoded conditions

## Universal Services

### UniversalUserRoleService
**Purpose**: Manages user roles, relationships, and permissions

**Key Methods**:
- `getCurrentUserId()`: Get current authenticated user ID
- `isCurrentUser(userId)`: Check if user is the current user
- `getUserRelationship(targetUserId)`: Get relationship to target user
- `getUserPermissions(targetUserId)`: Get permissions for user actions
- `getUserAccountType(userId)`: Get user's account type and privileges
- `getContentLimits(userId)`: Get content limits based on account type

**Usage Example**:
```dart
// ❌ WRONG - Hardcoded logic
if (userId == currentUserId) {
  showEditButton();
}

// ✅ CORRECT - Universal logic
final permissions = await UniversalUserRoleService.getUserPermissions(userId);
if (permissions.canEdit) {
  showEditButton();
}
```

### UniversalNavigationService
**Purpose**: Handles all navigation with context awareness

**Key Methods**:
- `navigateToProfile(context, userId)`: Navigate to appropriate profile screen
- `navigateToMessage(context, targetUserId)`: Navigate to messaging with permission checks
- `navigateToContentCreation(context, type)`: Navigate to content creation with limits
- `navigateToSettings(context, userId)`: Navigate to settings with permission checks

**Usage Example**:
```dart
// ❌ WRONG - Manual navigation logic
if (UniversalUserRoleService.isCurrentUser(userId)) {
  Navigator.push(context, MaterialPageRoute(builder: (_) => MainProfileScreen()));
} else {
  Navigator.push(context, MaterialPageRoute(builder: (_) => UserProfileScreen(userId: userId)));
}

// ✅ CORRECT - Universal navigation
UniversalNavigationService.navigateToProfile(context, userId);
```

### UniversalContentService
**Purpose**: Manages content creation and management universally

**Key Methods**:
- `createPost(...)`: Create posts with universal limits and validation
- `createStory(...)`: Create stories with universal limits and validation
- `deleteContent(contentId, type)`: Delete content with ownership validation
- `archiveContent(contentId, type, archive)`: Archive content with ownership validation

### UniversalSocialInteractionService
**Purpose**: Handles all social interactions (follow, like, etc.)

**Key Methods**:
- `followUser(targetUserId)`: Follow user with validation
- `unfollowUser(targetUserId)`: Unfollow user with validation
- `likePost(postId, postOwnerId)`: Like post with validation
- `unlikePost(postId)`: Unlike post with validation
- `blockUser(targetUserId)`: Block user with validation

### UniversalUIService
**Purpose**: Provides context-aware UI components

**Key Methods**:
- `getProfileActionButtons(context, targetUserId)`: Get appropriate action buttons
- `getPostActionButtons(...)`: Get post action buttons based on permissions
- `getContentCreationOptions(context)`: Get available content creation options

### UniversalAccountInitializationService
**Purpose**: Initializes new accounts with universal defaults

**Key Methods**:
- `initializeNewAccount(...)`: Set up new account with all required defaults
- `_createUniversalUserData(...)`: Create universal user document structure
- `_setUniversalPreferences(...)`: Set universal preferences based on account type

## Implementation Standards

### 1. Service Usage Patterns

#### Always Use Services for Business Logic
```dart
// ❌ WRONG - Direct Firebase calls
final userDoc = await FirebaseFirestore.instance.collection('users').doc(userId).get();
if (userDoc.data()['isAdmin'] == true) {
  // Admin logic
}

// ✅ CORRECT - Use universal service
final accountType = await UniversalUserRoleService.getUserAccountType(userId);
if (accountType == UserAccountType.admin) {
  // Admin logic
}
```

#### Error Handling
```dart
// ✅ CORRECT - Consistent error handling
try {
  final success = await UniversalSocialInteractionService.followUser(targetUserId);
  if (success) {
    // Handle success
  } else {
    // Handle already following case
  }
} catch (e) {
  // Handle error with user-friendly message
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(content: Text('Error: ${e.toString()}')),
  );
}
```

### 2. UI Component Patterns

#### Context-Aware Components
```dart
// ✅ CORRECT - Universal UI component
Widget buildActionButtons(String targetUserId) {
  return FutureBuilder<List<Widget>>(
    future: UniversalUIService.getProfileActionButtons(
      context: context,
      targetUserId: targetUserId,
    ),
    builder: (context, snapshot) {
      if (snapshot.hasData) {
        return Row(children: snapshot.data!);
      }
      return const CircularProgressIndicator();
    },
  );
}
```

#### Permission-Based Rendering
```dart
// ✅ CORRECT - Permission-based UI
Widget buildPostActions(Post post) {
  return FutureBuilder<UserPermissions>(
    future: UniversalUserRoleService.getUserPermissions(post.userId),
    builder: (context, snapshot) {
      if (snapshot.hasData) {
        final permissions = snapshot.data!;
        return Row(
          children: [
            if (permissions.canEdit) EditButton(),
            if (permissions.canDelete) DeleteButton(),
            if (permissions.canArchive) ArchiveButton(),
          ],
        );
      }
      return const SizedBox.shrink();
    },
  );
}
```

### 3. Navigation Patterns

#### Universal Navigation
```dart
// ✅ CORRECT - Always use universal navigation
void navigateToUserProfile(String userId) {
  UniversalNavigationService.navigateToProfile(context, userId);
}

void navigateToCreateContent(ContentCreationType type) {
  UniversalNavigationService.navigateToContentCreation(context, type);
}
```

### 4. Data Structure Standards

#### Universal User Document Structure
```dart
// ✅ CORRECT - Universal user document
{
  // Core identity
  'uid': userId,
  'email': email,
  'name': name,
  'username': username,
  
  // Account type and permissions
  'isAdmin': false,
  'isVerified': false,
  'isBillionaire': false,
  'isBusinessAccount': false,
  'userType': 'regular',
  
  // Universal counters
  'followerCount': 0,
  'followingCount': 0,
  'postCount': 0,
  
  // Universal preferences
  'feedPreferences': {...},
  'uiPreferences': {...},
  'privacySettings': {...},
  'notificationSettings': {...},
  
  // Universal flags
  'isNewUser': true,
  'onboardingCompleted': false,
  
  // Universal timestamps
  'createdAt': FieldValue.serverTimestamp(),
  'updatedAt': FieldValue.serverTimestamp(),
}
```

## Testing and Validation

### Universal Testing Service
Use `UniversalTestingService` to validate universal behavior:

```dart
// Run comprehensive tests
final results = await UniversalTestingService.runUniversalTests();

// Test specific user account
final userResults = await UniversalTestingService.testUserAccount(userId);
```

### Validation Widget
Use `UniversalValidationWidget` in debug screens:

```dart
// Add to debug screen
UniversalValidationWidget(
  testUserId: currentUserId,
  showDetailedResults: true,
)
```

## Migration Guidelines

### Refactoring Existing Code

1. **Identify Hardcoded Logic**: Look for user ID comparisons and hardcoded conditions
2. **Replace with Services**: Use appropriate universal services
3. **Update UI Components**: Make components context-aware
4. **Test Universal Behavior**: Validate with different user accounts
5. **Document Changes**: Update code comments and documentation

### Code Review Checklist

- [ ] No hardcoded user ID checks
- [ ] Uses universal services for business logic
- [ ] UI components are context-aware
- [ ] Navigation uses universal service
- [ ] Error handling is consistent
- [ ] New features work for all account types
- [ ] Code is tested with multiple user accounts

## Best Practices

### 1. Always Think Universal
- Will this work for any user?
- Does this scale to millions of users?
- Are there any hardcoded assumptions?

### 2. Use Type Safety
- Use enums for account types and permissions
- Use proper data classes for service responses
- Validate inputs and outputs

### 3. Handle Edge Cases
- What if the user is not authenticated?
- What if the target user doesn't exist?
- What if permissions change during the operation?

### 4. Performance Considerations
- Cache user permissions when possible
- Use efficient queries for relationship checks
- Minimize service calls in UI components

### 5. Security First
- Always validate permissions server-side
- Never trust client-side permission checks
- Log security-relevant actions

## Quick Reference

### Common Anti-Patterns to Avoid
```dart
// ❌ NEVER DO THESE
if (userId == "specific_user_id") { ... }
if (user.email == "<EMAIL>") { ... }
if (user.name == "Admin") { ... }

// Hardcoded media limits
int mediaLimit = user.isAdmin ? 0 : user.isBusiness ? 10 : 5;

// Direct navigation without context
Navigator.push(context, MaterialPageRoute(builder: (_) => ProfileScreen()));

// Manual permission checks
if (currentUserId == postOwnerId) { showEditButton(); }
```

### Universal Patterns to Use
```dart
// ✅ ALWAYS DO THESE
final accountType = await UniversalUserRoleService.getUserAccountType(userId);
final limits = await UniversalUserRoleService.getContentLimits(userId);
final permissions = await UniversalUserRoleService.getUserPermissions(userId);

UniversalNavigationService.navigateToProfile(context, userId);

if (permissions.canEdit) { showEditButton(); }
```

## Conclusion

This universal architecture ensures that:
- All features work consistently across user accounts
- New users automatically inherit all functionality
- Bug fixes apply universally without user-specific patches
- The codebase is maintainable and scalable
- Development follows consistent patterns and standards

By following these guidelines, developers can create robust, scalable features that work universally for all users while maintaining security and performance.
