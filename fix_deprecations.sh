#!/bin/bash

# <PERSON>ript to fix deprecated members and other issues in Flutter codebase
echo "🔧 Starting systematic fix of deprecated members and issues..."

# Fix surfaceVariant to surfaceContainerHighest
echo "📱 Fixing surfaceVariant deprecation..."
sed -i '' 's/surfaceVariant/surfaceContainerHighest/g' lib/core/app_themes.dart

# Fix withOpacity to withValues
echo "🎨 Fixing withOpacity deprecations..."
find lib -name "*.dart" -type f -exec sed -i '' 's/\.withOpacity(/\.withValues(alpha: /g' {} \;

# Fix FontAwesome icon deprecations
echo "🔤 Fixing FontAwesome icon deprecations..."
find lib -name "*.dart" -type f -exec sed -i '' 's/FontAwesomeIcons\.cog/FontAwesomeIcons.gear/g' {} \;
find lib -name "*.dart" -type f -exec sed -i '' 's/FontAwesomeIcons\.checkCircle/FontAwesomeIcons.circleCheck/g' {} \;
find lib -name "*.dart" -type f -exec sed -i '' 's/FontAwesomeIcons\.timesCircle/FontAwesomeIcons.circleXmark/g' {} \;
find lib -name "*.dart" -type f -exec sed -i '' 's/FontAwesomeIcons\.times/FontAwesomeIcons.xmark/g' {} \;
find lib -name "*.dart" -type f -exec sed -i '' 's/FontAwesomeIcons\.history/FontAwesomeIcons.clockRotateLeft/g' {} \;

# Fix Share package deprecations
echo "📤 Fixing Share package deprecations..."
find lib -name "*.dart" -type f -exec sed -i '' 's/Share\.share(/SharePlus.instance.share(/g' {} \;
find lib -name "*.dart" -type f -exec sed -i '' 's/Share\.shareXFiles(/SharePlus.instance.shareXFiles(/g' {} \;

echo "✅ Basic deprecations fixed! Running manual fixes for complex cases..."
