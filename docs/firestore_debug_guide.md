# Firestore Post Creation Debug Guide

## Issue Summary
The app was getting `[cloud_firestore/permission-denied]` errors when creating posts due to mismatched field names in Firestore rules.

## Root Cause
1. **Field Name Mismatch**: Rules were checking `content` field, but app sends `caption` field
2. **Resource Reference Error**: Rules used `resource.data.userId` instead of `request.resource.data.userId` for create operations

## Fixed Firestore Rules

### Before (Broken)
```javascript
allow create: if isAuthenticated() && 
  request.auth.uid == resource.data.userId &&  // ❌ Wrong reference
  resource.data.content.size() <= 2000 &&      // ❌ Wrong field name
  !resource.data.content.matches('.*spam.*');  // ❌ Wrong field name
```

### After (Fixed)
```javascript
allow create: if isAuthenticated() && 
  request.auth.uid == request.resource.data.userId &&  // ✅ Correct reference
  request.resource.data.caption.size() <= 2000 &&      // ✅ Correct field name
  !request.resource.data.caption.matches('.*spam.*');  // ✅ Correct field name
```

## Valid Post Data Structure

The app sends this exact structure to Firestore:

```json
{
  "userId": "firebase_auth_uid",
  "username": "User Display Name",
  "userAvatarUrl": "https://example.com/avatar.jpg",
  "mediaType": "image",
  "mediaUrl": "https://example.com/post-media.jpg",
  "caption": "Post caption text here",
  "location": "Optional location string",
  "likeCount": 0,
  "commentCount": 0,
  "isLiked": false,
  "isBookmarked": false,
  "createdAt": "serverTimestamp()",
  "updatedAt": "serverTimestamp()"
}
```

## Validation Rules

### Content Length
- **Maximum**: 2000 characters
- **Field**: `caption` (not `content`)

### Spam Detection
- **Pattern**: `.*spam.*` (case-insensitive)
- **Field**: `caption` (not `content`)

### Authentication
- User must be authenticated
- `userId` must match `request.auth.uid`

## Debug Tools

### 1. Use Debug Screen
Navigate to Debug Tools screen and use "Test Firestore Rules" button to test post creation.

### 2. Check Console Logs
Look for these debug messages:
```
📝 Creating post for user: [uid]
✅ User profile fetched successfully: [username]
👤 Using username: [name], avatar: [url]
📋 Post data being sent to Firestore:
  - userId: [uid]
  - username: [name]
  - caption: [text] (length: [number])
  - mediaType: [type]
  - mediaUrl: [url]
  - location: [location]
✅ Post created successfully with ID: [postId]
```

### 3. Common Error Patterns

#### Permission Denied
```
[cloud_firestore/permission-denied] The caller does not have permission to execute the specified operation.
```
**Causes:**
- User not authenticated
- `userId` doesn't match `request.auth.uid`
- Caption too long (>2000 chars)
- Caption contains "spam"

#### Field Validation Errors
```
❌ Caption is too long. Maximum 2000 characters allowed.
❌ Caption contains prohibited content.
```

## Testing Steps

1. **Deploy Updated Rules**
   ```bash
   firebase deploy --only firestore:rules
   ```

2. **Test with Debug Tools**
   - Open app → Debug Tools → Test Firestore Rules

3. **Test Manual Post Creation**
   - Create a post with short caption
   - Verify success in console logs

4. **Test Edge Cases**
   - Try caption > 2000 chars (should fail)
   - Try caption with "spam" (should fail)
   - Try while logged out (should fail)

## Production Checklist

- [ ] Firestore rules deployed
- [ ] Debug tools tested
- [ ] Manual post creation works
- [ ] Error handling tested
- [ ] Analytics events firing
- [ ] Performance monitoring active

## Troubleshooting

### Still Getting Permission Denied?
1. Check if user is authenticated: `_auth.currentUser != null`
2. Verify `userId` field matches `request.auth.uid`
3. Check caption length and content
4. Ensure Firestore rules are deployed
5. Check Firebase console for rule evaluation logs

### Rules Not Updating?
1. Deploy rules: `firebase deploy --only firestore:rules`
2. Wait 1-2 minutes for propagation
3. Clear app cache and restart
4. Check Firebase console for deployment status 