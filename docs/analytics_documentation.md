# Billionaires Social - Analytics Documentation

## Overview

This document provides comprehensive documentation for all analytics events, tracking implementations, and best practices for the Billionaires Social app.

## Table of Contents

1. [Analytics Setup](#analytics-setup)
2. [Core Events](#core-events)
3. [Billionaire-Specific Events](#billionaire-specific-events)
4. [Content Events](#content-events)
5. [Social Events](#social-events)
6. [Marketplace Events](#marketplace-events)
7. [Event Events](#event-events)
8. [Explore Events](#explore-events)
9. [Error Tracking](#error-tracking)
10. [Performance Monitoring](#performance-monitoring)
11. [Best Practices](#best-practices)
12. [Debug Tools](#debug-tools)

## Analytics Setup

### Firebase Analytics Configuration

- **Project ID**: `billionaires-social`
- **Enabled Services**: Analytics, Crashlytics, Performance Monitoring
- **Collection**: Enabled for all platforms (iOS, Android, Web)

### Initialization

```dart
// Main app initialization
await Firebase.initializeApp();
await setupServiceLocator();

// Analytics service initialization
final analyticsService = getIt<AnalyticsService>();
await analyticsService.initialize();
```

## Core Events

### App Lifecycle Events

| Event Name | Parameters | Description |
|------------|------------|-------------|
| `app_open` | `user_id`, `timestamp` | App opened by user |
| `user_login` | `user_id`, `login_method`, `timestamp` | User successfully logged in |
| `user_register` | `user_id`, `registration_method`, `timestamp` | New user registration |
| `screen_view` | `screen_name`, `screen_class` | Screen view tracking |
| `feature_usage` | `feature_name`, `user_id` | Feature usage tracking |

### Implementation Example

```dart
// Log app open
await analyticsService.logAppOpen(userId: 'user_123');

// Log screen view
await analyticsService.logScreenView(
  screenName: 'Profile',
  screenClass: 'ProfileScreen',
);

// Log feature usage
await analyticsService.logFeatureUsage(
  featureName: 'post_creation',
  parameters: {'media_type': 'image'},
);
```

## Billionaire-Specific Events

### Exclusive Club Membership

| Event Name | Parameters | Description |
|------------|------------|-------------|
| `exclusive_club_membership` | `user_id`, `club_name`, `membership_fee`, `membership_type`, `timestamp` | VIP club membership upgrade |

### Luxury Travel Booking

| Event Name | Parameters | Description |
|------------|------------|-------------|
| `luxury_travel_booking` | `user_id`, `destination`, `cost`, `travel_type`, `accommodation_type`, `timestamp` | Private jet, yacht, luxury travel bookings |

### Exclusive Dining Experience

| Event Name | Parameters | Description |
|------------|------------|-------------|
| `exclusive_dining_experience` | `user_id`, `restaurant_name`, `cost`, `chef_name`, `wine_pairing`, `timestamp` | Michelin-starred restaurant bookings |

### Wellness & Spa Experience

| Event Name | Parameters | Description |
|------------|------------|-------------|
| `wellness_spa_experience` | `user_id`, `spa_name`, `cost`, `treatment_type`, `therapist_name`, `timestamp` | Luxury spa and wellness bookings |

### Exclusive Entertainment Booking

| Event Name | Parameters | Description |
|------------|------------|-------------|
| `exclusive_entertainment_booking` | `user_id`, `event_name`, `cost`, `event_type`, `performer_name`, `timestamp` | VIP events, concerts, shows |

### Luxury Service Booking

| Event Name | Parameters | Description |
|------------|------------|-------------|
| `luxury_service_booking` | `user_id`, `service_type`, `cost`, `provider_name`, `service_details`, `timestamp` | Concierge, personal services |

### Implementation Example

```dart
// Log exclusive club membership
await analyticsService.logExclusiveClubMembership(
  userId: 'user_123',
  clubName: 'Elite Club',
  membershipFee: 9999.0,
  membershipType: 'diamond',
);

// Log luxury travel booking
await analyticsService.logLuxuryTravelBooking(
  userId: 'user_123',
  destination: 'Monaco',
  cost: 50000.0,
  travelType: 'private_jet',
  accommodationType: 'luxury_hotel',
);
```

## Content Events

### Post Events

| Event Name | Parameters | Description |
|------------|------------|-------------|
| `post_created` | `user_id`, `post_type`, `has_media`, `timestamp` | New post creation |
| `post_liked` | `user_id`, `post_id`, `timestamp` | Post like action |
| `post_shared` | `user_id`, `post_id`, `share_method`, `timestamp` | Post sharing |

### Story Events

| Event Name | Parameters | Description |
|------------|------------|-------------|
| `story_created` | `user_id`, `story_type`, `has_media`, `timestamp` | New story creation |
| `story_viewed` | `user_id`, `story_id`, `timestamp` | Story view action |

### Implementation Example

```dart
// Log post creation
await analyticsService.logPostCreated(
  postId: 'post_123',
  contentType: 'image',
  hasMedia: true,
  location: 'Monaco',
);

// Log post like
await analyticsService.logPostLiked(
  postId: 'post_123',
  postAuthorId: 'author_456',
);
```

## Social Events

### User Interaction Events

| Event Name | Parameters | Description |
|------------|------------|-------------|
| `user_followed` | `user_id`, `followed_user_id`, `timestamp` | User follow action |
| `user_unfollowed` | `user_id`, `unfollowed_user_id`, `timestamp` | User unfollow action |
| `message_sent` | `user_id`, `chat_id`, `message_type`, `timestamp` | Message sent |

### Implementation Example

```dart
// Log user follow
await analyticsService.logFollow(
  followerId: 'follower_123',
  followedId: 'followed_456',
);

// Log message sent
await analyticsService.logMessageSent(
  chatId: 'chat_789',
  recipientId: 'recipient_101',
  messageType: 'text',
);
```

## Marketplace Events

### Product Events

| Event Name | Parameters | Description |
|------------|------------|-------------|
| `product_viewed` | `user_id`, `product_id`, `category`, `timestamp` | Product view |
| `product_purchased` | `user_id`, `product_id`, `price`, `timestamp` | Product purchase |
| `product_listed` | `user_id`, `product_id`, `category`, `price`, `timestamp` | Product listing |

### Implementation Example

```dart
// Log product view
await analyticsService.logFeatureUsage(
  featureName: 'product_viewed',
  parameters: {
    'product_id': 'product_123',
    'category': 'luxury_watches',
  },
);

// Log product purchase
await analyticsService.logFeatureUsage(
  featureName: 'product_purchased',
  parameters: {
    'product_id': 'product_123',
    'price': 25000,
    'currency': 'USD',
  },
);
```

## Event Events

### Event Management

| Event Name | Parameters | Description |
|------------|------------|-------------|
| `event_viewed` | `user_id`, `event_id`, `event_type`, `timestamp` | Event view |
| `event_rsvp` | `user_id`, `event_id`, `response`, `timestamp` | Event RSVP |
| `event_created` | `user_id`, `event_id`, `event_type`, `timestamp` | Event creation |

### Implementation Example

```dart
// Log event creation
await analyticsService.logEventCreated(
  eventId: 'event_123',
  eventTitle: 'Luxury Networking Event',
  price: 5000.0,
  location: 'Dubai',
);

// Log event RSVP
await analyticsService.logEventRSVP(
  eventId: 'event_123',
  eventTitle: 'Luxury Networking Event',
);
```

## Explore Events

### Place Events

| Event Name | Parameters | Description |
|------------|------------|-------------|
| `place_viewed` | `user_id`, `place_id`, `category`, `timestamp` | Place view |
| `place_booked` | `user_id`, `place_id`, `booking_type`, `cost`, `timestamp` | Place booking |
| `place_reviewed` | `user_id`, `place_id`, `rating`, `timestamp` | Place review |

### Implementation Example

```dart
// Log place view
await analyticsService.logFeatureUsage(
  featureName: 'place_viewed',
  parameters: {
    'place_id': 'place_123',
    'category': 'restaurant',
  },
);

// Log place booking
await analyticsService.logFeatureUsage(
  featureName: 'place_booked',
  parameters: {
    'place_id': 'place_123',
    'booking_type': 'reservation',
    'cost': 500,
  },
);
```

## Error Tracking

### Crashlytics Integration

- **Automatic Crash Collection**: Enabled
- **Non-fatal Error Logging**: Manual implementation
- **Custom Error Context**: User actions, app state

### Implementation Example

```dart
// Log non-fatal error
await FirebaseCrashlytics.instance.recordError(
  'Custom error message',
  StackTrace.current,
  reason: 'User action failed',
);

// Set custom keys
await FirebaseCrashlytics.instance.setCustomKey('user_id', 'user_123');
await FirebaseCrashlytics.instance.setCustomKey('screen', 'ProfileScreen');
```

## Performance Monitoring

### Performance Traces

- **App Startup**: Automatic tracking
- **Screen Load**: Manual implementation
- **API Calls**: Automatic tracking
- **Custom Traces**: Manual implementation

### Implementation Example

```dart
// Start custom trace
final trace = FirebasePerformance.instance.newTrace('custom_trace');
await trace.start();

// Add custom attributes
trace.putAttribute('user_id', 'user_123');
trace.putAttribute('action', 'data_processing');

// Stop trace
await trace.stop();
```

## Best Practices

### Event Naming

- Use snake_case for event names
- Be descriptive and specific
- Follow consistent naming patterns

### Parameter Guidelines

- Maximum 25 parameters per event
- Use snake_case for parameter names
- Avoid null values
- Use appropriate data types

### Validation

```dart
// Use safe event logging
await analyticsService.logEventSafely(
  eventName: 'custom_event',
  parameters: {
    'user_id': 'user_123',
    'action_type': 'button_click',
    'timestamp': DateTime.now().toIso8601String(),
  },
);
```

### Error Handling

```dart
try {
  await analyticsService.logEvent(name: 'test_event', parameters: {});
} catch (e) {
  // Analytics errors shouldn't break the app
  debugPrint('Analytics error: $e');
}
```

## Debug Tools

### Debug Tools Screen

Access the debug tools screen in debug mode to test:

1. **Analytics Testing**
   - Test all analytics events
   - Validate analytics implementation
   - Test billionaire events
   - Test standard events

2. **Crash & Performance Testing**
   - Trigger test crashes
   - Simulate performance issues
   - Test performance traces

3. **Major Flow Testing**
   - VIP flow testing
   - Investment flow testing
   - Marketplace flow testing
   - Booking flow testing
   - Social flow testing

4. **System Testing**
   - Cache service testing
   - Version control testing
   - Firebase connection testing

### Analytics Validator

Run the analytics validator to ensure all events are properly implemented:

```bash
dart run lib/core/services/analytics_validator.dart
```

### CI/CD Integration

The analytics validator is automatically run in CI/CD pipelines:

```yaml
- name: Run analytics validator
  run: dart run lib/core/services/analytics_validator.dart
```

## Monitoring & Reporting

### Firebase Console

- **Analytics Dashboard**: Real-time event tracking
- **Crashlytics**: Error monitoring and reporting
- **Performance**: Performance metrics and traces

### Key Metrics to Monitor

1. **User Engagement**
   - Daily/Monthly Active Users
   - Session duration
   - Screen views per session

2. **Feature Usage**
   - Most used features
   - Feature adoption rates
   - User journey analysis

3. **Business Metrics**
   - VIP membership conversions
   - Luxury booking rates
   - Marketplace transactions

4. **Technical Metrics**
   - App crashes and errors
   - Performance bottlenecks
   - API response times

## Troubleshooting

### Common Issues

1. **Events not appearing in Firebase Console**
   - Check internet connection
   - Verify Firebase configuration
   - Check event parameter validation

2. **Performance issues**
   - Monitor trace durations
   - Check for memory leaks
   - Analyze API response times

3. **Crash reporting issues**
   - Verify Crashlytics configuration
   - Check symbol uploads
   - Review crash reports

### Support

For analytics-related issues:

1. Check the debug tools screen
2. Run the analytics validator
3. Review Firebase Console logs
4. Contact the development team

---

**Last Updated**: December 2024  
**Version**: 1.0.0  
**Maintained By**: Development Team 