# Firebase Storage Unauthorized Error Debug Guide

## Issue Summary
The app is getting `[firebase_storage/unauthorized] User is not authorized to perform the desired action.` errors when uploading story media to Firebase Storage.

## Root Causes & Solutions

### 1. Authentication Issues

#### Problem: User not properly authenticated
**Symptoms:**
- User appears logged in but Firebase Auth token is invalid/expired
- User ID mismatch between auth and storage path

**Solutions:**
```dart
// Check authentication status
final currentUser = FirebaseAuth.instance.currentUser;
if (currentUser == null) {
  // User not authenticated
  throw Exception('User must be authenticated to upload media');
}

// Verify user ID matches
if (currentUser.uid != userId) {
  // User ID mismatch
  throw Exception('User ID mismatch');
}
```

**Debug Steps:**
1. Check if user is logged in: `FirebaseAuth.instance.currentUser != null`
2. Verify user email is verified: `currentUser.emailVerified`
3. Check user ID consistency across the app

### 2. Firebase Storage Rules Issues

#### Problem: Storage rules not deployed or incorrect
**Current Rules Status:** ✅ Deployed and up to date

**Storage Rules for Stories:**
```javascript
// Story media - Enhanced privacy controls
match /stories/{userId}/{fileName} {
  allow read: if isAuthenticated();
  allow write: if isAuthenticated() && 
    (isValidImageType() || isValidVideoType()) && 
    isValidFileSize(20); // 20MB limit for stories
}
```

**Debug Steps:**
1. Verify rules are deployed: `firebase deploy --only storage`
2. Check Firebase Console → Storage → Rules
3. Test rules in Firebase Console

### 3. File Type & Size Issues

#### Problem: File doesn't meet storage rules requirements
**Allowed File Types:**
- Images: `.jpg`, `.jpeg`, `.png`, `.gif`, `.webp`
- Videos: `.mp4`, `.mov`, `.avi`, `.mkv`

**File Size Limits:**
- Stories: 20MB maximum
- Posts: 10MB maximum
- Profile images: 5MB maximum

**Debug Steps:**
1. Check file extension: `file.path.split('/').last.toLowerCase()`
2. Check file size: `await file.length() / (1024 * 1024)`
3. Validate file exists: `await file.exists()`

### 4. Storage Path Issues

#### Problem: Incorrect storage path structure
**Correct Path Structure:**
```
stories/{userId}/{timestamp}_{uuid}.{extension}
```

**Example:**
```
stories/abc123/1703123456789_550e8400-e29b-41d4-a716-************.jpg
```

**Debug Steps:**
1. Verify path matches storage rules pattern
2. Check for special characters in filenames
3. Ensure user ID is valid Firebase UID

### 5. Network & Configuration Issues

#### Problem: Firebase configuration or network issues
**Debug Steps:**
1. Check Firebase configuration in `firebase_options.dart`
2. Verify internet connection
3. Check Firebase project settings
4. Test with Firebase Console

## Enhanced Error Handling

### Updated Upload Method
The `_uploadMedia` method now includes comprehensive error handling:

```dart
Future<String?> _uploadMedia(File file, String userId) async {
  try {
    // 1. Authentication checks
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User must be authenticated to upload media');
    }
    
    if (currentUser.uid != userId) {
      throw Exception('User ID mismatch');
    }

    // 2. File validation
    if (!await file.exists()) {
      throw Exception('File does not exist');
    }
    
    final fileSize = await file.length();
    final fileSizeMB = fileSize / (1024 * 1024);
    if (fileSizeMB > 20) {
      throw Exception('File size exceeds 20MB limit');
    }

    // 3. File type validation
    final fileName = file.path.split('/').last.toLowerCase();
    final isImage = fileName.endsWith('.jpg') || fileName.endsWith('.jpeg') || 
                   fileName.endsWith('.png') || fileName.endsWith('.gif') || 
                   fileName.endsWith('.webp');
    final isVideo = fileName.endsWith('.mp4') || fileName.endsWith('.mov') || 
                   fileName.endsWith('.avi') || fileName.endsWith('.mkv');

    if (!isImage && !isVideo) {
      throw Exception('Invalid file type. Only images and videos are allowed');
    }

    // 4. Upload with metadata
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final uuid = _uuid.v4();
    final extension = fileName.split('.').last;
    final uniqueFileName = '${timestamp}_${uuid}.$extension';
    
    final ref = _storage.ref().child('stories/$userId/$uniqueFileName');
    
    final metadata = SettableMetadata(
      contentType: isImage ? 'image/$extension' : 'video/$extension',
      customMetadata: {
        'uploadedBy': userId,
        'uploadedAt': DateTime.now().toIso8601String(),
        'originalFileName': fileName,
      },
    );

    final uploadTask = ref.putFile(file, metadata);
    final snapshot = await uploadTask;
    final downloadUrl = await snapshot.ref.getDownloadURL();

    return downloadUrl;
  } on FirebaseException catch (e) {
    switch (e.code) {
      case 'storage/unauthorized':
        throw Exception('Storage access denied. Please check your authentication and try again.');
      case 'storage/quota-exceeded':
        throw Exception('Storage quota exceeded. Please try again later.');
      case 'storage/retry-limit-exceeded':
        throw Exception('Upload failed due to network issues. Please check your connection and try again.');
      default:
        throw Exception('Storage error: ${e.message}');
    }
  }
}
```

### Storage Access Test Method
```dart
Future<bool> testStorageAccess() async {
  try {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      return false;
    }

    final testRef = _storage.ref().child('stories/${currentUser.uid}/test.txt');
    
    try {
      await testRef.getMetadata();
      return true;
    } catch (e) {
      if (e.toString().contains('object does not exist')) {
        return true; // Access allowed, file just doesn't exist
      }
      return false;
    }
  } catch (e) {
    return false;
  }
}
```

## Debugging Steps

### 1. Check Authentication
```dart
final user = FirebaseAuth.instance.currentUser;
print('User authenticated: ${user != null}');
print('User ID: ${user?.uid}');
print('Email verified: ${user?.emailVerified}');
```

### 2. Test Storage Access
```dart
final storageService = UnifiedStoryService();
final hasAccess = await storageService.testStorageAccess();
print('Storage access: $hasAccess');
```

### 3. Validate File
```dart
final file = File(filePath);
print('File exists: ${await file.exists()}');
print('File size: ${await file.length()} bytes');
print('File name: ${file.path.split('/').last}');
```

### 4. Check Firebase Configuration
```dart
// Verify Firebase is initialized
try {
  await Firebase.initializeApp();
  print('Firebase initialized successfully');
} catch (e) {
  print('Firebase initialization failed: $e');
}
```

## Common Solutions

### 1. Re-authenticate User
```dart
// Force token refresh
await FirebaseAuth.instance.currentUser?.getIdToken(true);
```

### 2. Clear App Cache
```dart
// Clear Firebase cache
await FirebaseStorage.instance.ref().bucket;
```

### 3. Check Network
```dart
// Test network connectivity
try {
  final result = await InternetAddress.lookup('google.com');
  print('Network available: ${result.isNotEmpty}');
} catch (e) {
  print('Network unavailable: $e');
}
```

### 4. Verify Firebase Project
1. Check Firebase Console → Project Settings
2. Verify Storage bucket name
3. Check API keys and configuration
4. Ensure Storage API is enabled

## Testing Checklist

- [ ] User is authenticated (`FirebaseAuth.instance.currentUser != null`)
- [ ] User email is verified (`currentUser.emailVerified`)
- [ ] User ID matches storage path
- [ ] File exists and is readable
- [ ] File type is allowed (image/video)
- [ ] File size is within limits (≤20MB for stories)
- [ ] Storage rules are deployed
- [ ] Network connection is stable
- [ ] Firebase configuration is correct
- [ ] Storage API is enabled in Firebase Console

## Error Messages Reference

| Error Code | Meaning | Solution |
|------------|---------|----------|
| `storage/unauthorized` | User not authorized | Check authentication and storage rules |
| `storage/quota-exceeded` | Storage quota full | Wait or upgrade plan |
| `storage/retry-limit-exceeded` | Network issues | Check connection and retry |
| `storage/invalid-checksum` | File corrupted | Re-upload file |
| `storage/object-not-found` | File doesn't exist | Check file path |
| `storage/bucket-not-found` | Wrong bucket | Check Firebase configuration |

## Production Checklist

- [ ] Enhanced error handling implemented
- [ ] Storage access testing added
- [ ] File validation improved
- [ ] User authentication verified
- [ ] Storage rules deployed
- [ ] Error messages user-friendly
- [ ] Analytics tracking added
- [ ] Retry logic implemented
- [ ] Network error handling
- [ ] File size limits enforced 