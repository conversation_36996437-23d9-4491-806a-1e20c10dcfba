# 🔥 Trending Content Automation System

This Firebase Functions project implements a comprehensive trending content detection and notification system for the Billionaires Social app.

## 📋 Overview

The system automatically detects trending posts based on engagement metrics and sends real-time notifications to authors and followers.

### Features
- **Scheduled Detection**: Hourly trending analysis of recent posts
- **Real-time Triggers**: Immediate trending updates on engagement changes
- **FCM Notifications**: Push notifications to authors and followers
- **Configurable Thresholds**: Adjustable trending criteria via Firestore
- **Event Logging**: Comprehensive audit trail of trending events
- **Test Functions**: Built-in testing and validation tools

## 🏗️ Architecture

### Core Functions

1. **`detectTrendingPosts`** (Scheduled)
   - Runs every 60 minutes
   - Analyzes posts from last 3 hours
   - Updates trending status and scores
   - Archives old trending posts (>24h)
   - Sends notifications for newly trending posts

2. **`onLikeUpdate`** (Firestore Trigger)
   - Triggers on likes subcollection changes
   - Recalculates trending score immediately
   - Updates trending status if changed

3. **`onCommentUpdate`** (Firestore Trigger)
   - Triggers on comments subcollection changes
   - Recalculates trending score immediately
   - Updates trending status if changed

4. **`onShareUpdate`** (Firestore Trigger)
   - Triggers on shares subcollection changes
   - Recalculates trending score immediately
   - Updates trending status if changed

### Trending Algorithm

```
Trending Score = (likes × 2) + (comments × 3) + (shares × 5)
Trending Status = Score ≥ Threshold (default: 10)
```

### Notification System

- **Author Notifications**: When their post becomes trending
- **Follower Notifications**: Top 5 followers notified of trending posts
- **Configurable**: Enable/disable via Firestore settings
- **FCM Integration**: Uses user FCM tokens from profiles

## ⚙️ Configuration

### Firestore Settings Document: `settings/trending_config`

```json
{
  "scoreThreshold": 10,
  "windowHours": 3,
  "archiveHours": 24,
  "likeWeight": 2,
  "commentWeight": 3,
  "shareWeight": 5,
  "notifyAuthor": true,
  "notifyFollowers": true,
  "maxFollowerNotifications": 5,
  "notificationTitle": "🔥 Your post is trending!",
  "notificationBody": "Your post has gained significant engagement and is now trending!",
  "followerNotificationTitle": "🔥 Trending post from {authorName}",
  "followerNotificationBody": "Check out this trending post from someone you follow!"
}
```

## 🧪 Testing

### Test Functions

1. **`createTestTrendingPost`**
   - Creates a post with high engagement
   - Parameters: `userId`, `username`, `caption` (optional)

2. **`testTrendingDetection`**
   - Manually triggers trending detection for a post
   - Parameters: `postId`

3. **`getTrendingPosts`**
   - Retrieves currently trending posts
   - Parameters: `limit` (optional, default: 10)

4. **`resetTrendingStatus`**
   - Resets trending status for a post
   - Parameters: `postId`

### Testing Workflow

1. Create a test post with high engagement
2. Trigger trending detection manually
3. Verify trending status and notifications
4. Check trending events in Firestore
5. Reset status for cleanup

## 📊 Monitoring

### Firestore Collections

- **`posts`**: Post documents with trending fields
- **`trending_events`**: Audit trail of trending status changes
- **`notification_events`**: Log of sent notifications
- **`settings/trending_config`**: System configuration

### Trending Fields in Posts

```typescript
{
  trending: boolean,           // Current trending status
  trendingScore: number,       // Calculated trending score
  trendingUpdatedAt: Timestamp // Last trending update
}
```

## 🚀 Deployment

### Prerequisites

1. Firebase project configured
2. Firestore enabled
3. Cloud Functions enabled
4. FCM configured (for notifications)

### Deploy Commands

```bash
# Deploy all functions
firebase deploy --only functions

# Deploy specific functions
firebase deploy --only functions:detectTrendingPosts
firebase deploy --only functions:onLikeUpdate
firebase deploy --only functions:onCommentUpdate
firebase deploy --only functions:onShareUpdate

# Deploy test functions
firebase deploy --only functions:createTestTrendingPost
firebase deploy --only functions:testTrendingDetection
firebase deploy --only functions:getTrendingPosts
firebase deploy --only functions:resetTrendingStatus
```

### Environment Setup

1. **Initialize Configuration**:
   ```bash
   # Create trending config document
   firebase firestore:set settings/trending_config ./config/trending-config.json
   ```

2. **Set FCM Configuration**:
   - Ensure user profiles have `fcmToken` field
   - Configure FCM in Firebase Console

## 🔧 Development

### Local Development

```bash
# Install dependencies
npm install

# Start emulator
firebase emulators:start --only functions

# Run tests
npm test

# Lint code
npm run lint
```

### Code Structure

```
functions/src/
├── index.ts              # Main scheduled function
├── engagementTriggers.ts # Real-time triggers
├── notificationService.ts # FCM notification logic
├── testFunctions.ts      # Testing utilities
└── README.md            # This file
```

## 📈 Performance Considerations

- **Batch Operations**: Uses Firestore batches for efficiency
- **Indexed Queries**: Requires composite indexes for trending queries
- **Rate Limiting**: Configurable max instances per function
- **Error Handling**: Comprehensive try-catch blocks with logging

## 🔒 Security

- **Authentication**: Functions require Firebase Auth
- **Authorization**: Validate user permissions in triggers
- **Input Validation**: Sanitize all function parameters
- **Error Logging**: Secure error handling without data exposure

## 🐛 Troubleshooting

### Common Issues

1. **Trending not updating**: Check Firestore indexes
2. **Notifications not sending**: Verify FCM tokens in user profiles
3. **High function costs**: Adjust batch sizes and query limits
4. **Trigger not firing**: Verify subcollection structure

### Debug Commands

```bash
# View function logs
firebase functions:log

# Test specific function
firebase functions:shell
> createTestTrendingPost({userId: "test", username: "testuser"})

# Check Firestore data
firebase firestore:get posts/{postId}
```

## 📝 Changelog

### v1.0.0
- Initial trending detection system
- Real-time engagement triggers
- FCM notification integration
- Comprehensive testing suite
- Configurable thresholds and settings

## 🤝 Contributing

1. Follow TypeScript best practices
2. Add comprehensive error handling
3. Include unit tests for new functions
4. Update documentation for changes
5. Test in staging before production

## 📄 License

This project is part of the Billionaires Social app and follows the same licensing terms. 