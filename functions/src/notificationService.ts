import * as admin from "firebase-admin";

function getDb() {
  return admin.firestore();
}

// Notification config defaults
const DEFAULT_NOTIFICATION_CONFIG = {
  notifyAuthor: true,
  notifyFollowers: true,
  maxFollowerNotifications: 5,
  notificationTitle: "🔥 Your post is trending!",
  notificationBody: "Your post has gained significant engagement and is now trending!",
  followerNotificationTitle: "🔥 Trending post from {authorName}",
  followerNotificationBody: "Check out this trending post from someone you follow!",
};

// Helper to get notification config from Firestore
async function getNotificationConfig() {
  const doc = await getDb().doc("settings/trending_config").get();
  if (doc.exists) {
    const config = doc.data();
    return {...DEFAULT_NOTIFICATION_CONFIG, ...config};
  }
  return DEFAULT_NOTIFICATION_CONFIG;
}

// Get user FCM token
async function getUserFCMToken(userId: string): Promise<string | null> {
  try {
    const userDoc = await getDb().collection("users").doc(userId).get();
    if (userDoc.exists) {
      const userData = userDoc.data();
      return userData?.fcmToken || null;
    }
    return null;
  } catch (error) {
    console.error(`Error fetching FCM token for user ${userId}:`, error);
    return null;
  }
}

// Get top followers for a user
async function getTopFollowers(userId: string, limit = 5): Promise<string[]> {
  try {
    const followersSnapshot = await getDb()
      .collection("users")
      .doc(userId)
      .collection("followers")
      .orderBy("followedAt", "desc")
      .limit(limit)
      .get();

    return followersSnapshot.docs.map((doc) => doc.id);
  } catch (error) {
    console.error(`Error fetching followers for user ${userId}:`, error);
    return [];
  }
}

// Send FCM notification
async function sendFCMNotification(
  token: string,
  title: string,
  body: string,
  data?: Record<string, string>
): Promise<boolean> {
  try {
    const message: admin.messaging.Message = {
      token,
      notification: {
        title,
        body,
      },
      data,
      android: {
        notification: {
          channelId: "trending_notifications",
          priority: "high",
        },
      },
      apns: {
        payload: {
          aps: {
            badge: 1,
            sound: "default",
          },
        },
      },
    };

    const response = await admin.messaging().send(message);
    console.log(`FCM notification sent successfully: ${response}`);
    return true;
  } catch (error) {
    console.error("Error sending FCM notification:", error);
    return false;
  }
}

// Send trending notification to post author
export async function notifyTrendingAuthor(postId: string, userId: string, postData: any): Promise<void> {
  try {
    const config = await getNotificationConfig();

    if (!config.notifyAuthor) {
      console.log(`Author notifications disabled for post ${postId}`);
      return;
    }

    const fcmToken = await getUserFCMToken(userId);
    if (!fcmToken) {
      console.log(`No FCM token found for user ${userId}`);
      return;
    }

    const success = await sendFCMNotification(
      fcmToken,
      config.notificationTitle,
      config.notificationBody,
      {
        postId,
        type: "trending_post",
        userId,
      }
    );

    if (success) {
      console.log(`Trending notification sent to author ${userId} for post ${postId}`);

      // Log notification event
      await getDb().collection("notification_events").add({
        userId,
        postId,
        type: "trending_author_notification",
        timestamp: admin.firestore.Timestamp.now(),
        success: true,
      });
    }
  } catch (error) {
    console.error(`Error notifying trending author for post ${postId}:`, error);
  }
}

// Send trending notification to followers
export async function notifyTrendingFollowers(
  postId: string,
  userId: string,
  authorName: string,
  postData: any
): Promise<void> {
  try {
    const config = await getNotificationConfig();

    if (!config.notifyFollowers) {
      console.log(`Follower notifications disabled for post ${postId}`);
      return;
    }

    const followers = await getTopFollowers(userId, config.maxFollowerNotifications);
    if (followers.length === 0) {
      console.log(`No followers found for user ${userId}`);
      return;
    }

    let notificationCount = 0;

    for (const followerId of followers) {
      const fcmToken = await getUserFCMToken(followerId);
      if (!fcmToken) {
        continue;
      }

      const title = config.followerNotificationTitle.replace("{authorName}", authorName);
      const success = await sendFCMNotification(
        fcmToken,
        title,
        config.followerNotificationBody,
        {
          postId,
          type: "trending_post_follower",
          authorId: userId,
        }
      );

      if (success) {
        notificationCount++;

        // Log notification event
        await getDb().collection("notification_events").add({
          userId: followerId,
          postId,
          type: "trending_follower_notification",
          timestamp: admin.firestore.Timestamp.now(),
          success: true,
          authorId: userId,
        });
      }
    }

    console.log(`Sent trending notifications to ${notificationCount} followers for post ${postId}`);
  } catch (error) {
    console.error(`Error notifying trending followers for post ${postId}:`, error);
  }
}

// Main function to handle all trending notifications
export async function handleTrendingNotifications(
  postId: string,
  userId: string,
  postData: any
): Promise<void> {
  try {
    console.log(`Handling trending notifications for post ${postId}`);

    // Get author name for notifications
    const authorName = postData.username || postData.name || "Someone you follow";

    // Notify author
    await notifyTrendingAuthor(postId, userId, postData);

    // Notify followers
    await notifyTrendingFollowers(postId, userId, authorName, postData);

    console.log(`Completed trending notifications for post ${postId}`);
  } catch (error) {
    console.error(`Error handling trending notifications for post ${postId}:`, error);
  }
}
