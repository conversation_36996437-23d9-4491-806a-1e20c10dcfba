import {onDocumentWritten} from "firebase-functions/v2/firestore";
import {logger} from "firebase-functions";
import {getFirestore} from "firebase-admin/firestore";
import {notifyTrendingAuthor} from "./notificationService";

function getDb() {
  return getFirestore();
}

/**
 * Triggered when a like is added to a post
 */
export const onPostLikeUpdate = onDocumentWritten(
  "posts/{postId}/likes/{likeId}",
  async (event) => {
    try {
      const postId = event.params.postId;
      const likeData = event.data?.after?.data();

      if (!likeData) {
        logger.info("No like data found, skipping trending calculation");
        return;
      }

      // Recalculate trending score for the post
      await recalculateTrendingScore(postId);

      // Send notification to post author
      const postDoc = await getDb().collection("posts").doc(postId).get();
      if (postDoc.exists) {
        const postData = postDoc.data();
        if (postData) {
          const authorId = postData.authorId;
          const likerId = likeData.userId;

          if (authorId !== likerId) {
            await notifyTrendingAuthor(postId, authorId, postData);
          }
        }
      }

      logger.info(`Trending score recalculated for post ${postId}`);
    } catch (error) {
      logger.error("Error in onPostLikeUpdate:", error);
    }
  },
);

/**
 * Triggered when a comment is added to a post
 */
export const onPostCommentUpdate = onDocumentWritten(
  "posts/{postId}/comments/{commentId}",
  async (event) => {
    try {
      const postId = event.params.postId;
      const commentData = event.data?.after?.data();

      if (!commentData) {
        logger.info("No comment data found, skipping trending calculation");
        return;
      }

      // Recalculate trending score for the post
      await recalculateTrendingScore(postId);

      // Send notification to post author
      const postDoc = await getDb().collection("posts").doc(postId).get();
      if (postDoc.exists) {
        const postData = postDoc.data();
        if (postData) {
          const authorId = postData.authorId;
          const commenterId = commentData.userId;

          if (authorId !== commenterId) {
            await notifyTrendingAuthor(postId, authorId, postData);
          }
        }
      }

      logger.info(`Trending score recalculated for post ${postId}`);
    } catch (error) {
      logger.error("Error in onPostCommentUpdate:", error);
    }
  },
);

/**
 * Triggered when a share is added to a post
 */
export const onPostShareUpdate = onDocumentWritten(
  "posts/{postId}/shares/{shareId}",
  async (event) => {
    try {
      const postId = event.params.postId;
      const shareData = event.data?.after?.data();

      if (!shareData) {
        logger.info("No share data found, skipping trending calculation");
        return;
      }

      // Recalculate trending score for the post
      await recalculateTrendingScore(postId);

      // Send notification to post author
      const postDoc = await getDb().collection("posts").doc(postId).get();
      if (postDoc.exists) {
        const postData = postDoc.data();
        if (postData) {
          const authorId = postData.authorId;
          const sharerId = shareData.userId;

          if (authorId !== sharerId) {
            await notifyTrendingAuthor(postId, authorId, postData);
          }
        }
      }

      logger.info(`Trending score recalculated for post ${postId}`);
    } catch (error) {
      logger.error("Error in onPostShareUpdate:", error);
    }
  },
);

/**
 * Recalculates the trending score for a given post
 */
async function recalculateTrendingScore(postId: string): Promise<void> {
  try {
    const postRef = getDb().collection("posts").doc(postId);
    const postDoc = await postRef.get();

    if (!postDoc.exists) {
      logger.warn(`Post ${postId} not found for trending calculation`);
      return;
    }

    const postData = postDoc.data();
    if (!postData) {
      logger.warn(`No data found for post ${postId}`);
      return;
    }

    const now = new Date();
    const postTime = postData.createdAt?.toDate() || now;
    const timeDiff = (now.getTime() - postTime.getTime()) / (1000 * 60 * 60);

    // Get engagement counts
    const [likesSnapshot, commentsSnapshot, sharesSnapshot] = await Promise.all([
      postRef.collection("likes").get(),
      postRef.collection("comments").get(),
      postRef.collection("shares").get(),
    ]);

    const likesCount = likesSnapshot.size;
    const commentsCount = commentsSnapshot.size;
    const sharesCount = sharesSnapshot.size;

    // Calculate trending score
    const engagementScore = likesCount + (commentsCount * 2) + (sharesCount * 3);
    const timeDecay = Math.max(0.1, 1 - timeDiff / 24);
    const trendingScore = Math.round(engagementScore * timeDecay);

    // Update the post with new trending data
    await postRef.update({
      trendingScore,
      lastTrendingUpdate: now,
      isTrending: trendingScore >= 50,
    });

    // Log the trending event
    await getDb().collection("trending_events").add({
      postId,
      trendingScore,
      engagementScore,
      timeDecay,
      likesCount,
      commentsCount,
      sharesCount,
      timestamp: now,
      eventType: "recalculation",
    });

    logger.info(`Updated trending score for post ${postId}: ${trendingScore}`);
  } catch (error) {
    logger.error(`Error recalculating trending score for post ${postId}:`, error);
  }
}
