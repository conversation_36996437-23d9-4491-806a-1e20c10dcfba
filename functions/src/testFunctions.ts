import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import {onCall} from "firebase-functions/v2/https";

function getDb() {
  return admin.firestore();
}

// Test function to create a dummy trending post
export const createTestTrendingPost = onCall(async (request) => {
  try {
    const {userId, username, caption} = request.data;

    if (!userId || !username) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "userId and username are required"
      );
    }

    // Create a test post with high engagement to trigger trending
    const testPost = {
      userId,
      username,
      caption: caption || "🔥 This is a test trending post!",
      likeCount: 15, // High enough to trigger trending
      commentCount: 8,
      shareCount: 12,
      trending: false,
      trendingScore: 0,
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now(),
    };

    const postRef = await getDb().collection("posts").add(testPost);

    console.log(`Created test trending post: ${postRef.id}`);

    return {
      success: true,
      postId: postRef.id,
      message: "Test trending post created successfully",
    };
  } catch (error) {
    console.error("Error creating test trending post:", error);
    throw new functions.https.HttpsError("internal", "Failed to create test post");
  }
});

// Test function to manually trigger trending detection
export const testTrendingDetection = onCall(async (request) => {
  try {
    const {postId} = request.data;

    if (!postId) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "postId is required"
      );
    }

    // Get the post
    const postDoc = await getDb().collection("posts").doc(postId).get();
    if (!postDoc.exists) {
      throw new functions.https.HttpsError("not-found", "Post not found");
    }

    const post = postDoc.data()!;
    const likes = post.likeCount || 0;
    const comments = post.commentCount || 0;
    const shares = post.shareCount || 0;

    // Calculate trending score using the same formula
    const score = (likes * 2) + (comments * 3) + (shares * 5);
    const isTrending = score >= 10; // Default threshold
    const wasTrending = post.trending || false;

    // Update post trending status
    await getDb().collection("posts").doc(postId).update({
      trending: isTrending,
      trendingScore: score,
      trendingUpdatedAt: admin.firestore.Timestamp.now(),
    });

    // Log trending event if status changed
    if (isTrending !== wasTrending) {
      await getDb().collection("trending_events").add({
        postId,
        userId: post.userId,
        score,
        timestamp: admin.firestore.Timestamp.now(),
        eventType: isTrending ? "became_trending" : "stopped_trending",
      });
    }

    console.log(`Test trending detection for post ${postId}: score=${score}, trending=${isTrending}`);

    return {
      success: true,
      postId,
      score,
      isTrending,
      wasTrending,
      statusChanged: isTrending !== wasTrending,
    };
  } catch (error) {
    console.error("Error testing trending detection:", error);
    throw new functions.https.HttpsError("internal", "Failed to test trending detection");
  }
});

// Test function to get trending posts
export const getTrendingPosts = onCall(async (request) => {
  try {
    const {limit = 10} = request.data;

    const trendingSnapshot = await getDb().collection("posts")
      .where("trending", "==", true)
      .orderBy("trendingScore", "desc")
      .limit(limit)
      .get();

    const trendingPosts = trendingSnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));

    return {
      success: true,
      count: trendingPosts.length,
      posts: trendingPosts,
    };
  } catch (error) {
    console.error("Error getting trending posts:", error);
    throw new functions.https.HttpsError("internal", "Failed to get trending posts");
  }
});

// Test function to reset trending status
export const resetTrendingStatus = onCall(async (request) => {
  try {
    const {postId} = request.data;

    if (!postId) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "postId is required"
      );
    }

    await getDb().collection("posts").doc(postId).update({
      trending: false,
      trendingScore: 0,
      trendingUpdatedAt: admin.firestore.Timestamp.now(),
    });

    console.log(`Reset trending status for post ${postId}`);

    return {
      success: true,
      postId,
      message: "Trending status reset successfully",
    };
  } catch (error) {
    console.error("Error resetting trending status:", error);
    throw new functions.https.HttpsError("internal", "Failed to reset trending status");
  }
});

// Test function to manually add a like to trigger onPostLikeUpdate
export const addTestLike = onCall(async (request) => {
  try {
    const {postId, userId} = request.data;

    if (!postId || !userId) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "postId and userId are required"
      );
    }

    // Add a like document to trigger the onPostLikeUpdate function
    const likeData = {
      userId,
      postId,
      createdAt: admin.firestore.Timestamp.now(),
    };

    const likeRef = await getDb().collection("posts").doc(postId).collection("likes").add(likeData);

    console.log(`Added test like: ${likeRef.id} to post: ${postId}`);

    return {
      success: true,
      likeId: likeRef.id,
      postId,
      message: "Test like added successfully",
    };
  } catch (error) {
    console.error("Error adding test like:", error);
    throw new functions.https.HttpsError("internal", "Failed to add test like");
  }
});

// Test function to manually add a comment to trigger onPostCommentUpdate
export const addTestComment = onCall(async (request) => {
  try {
    const {postId, userId, username, content} = request.data;

    if (!postId || !userId || !username) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "postId, userId, and username are required"
      );
    }

    // Add a comment document to trigger the onPostCommentUpdate function
    const commentData = {
      userId,
      username,
      content: content || "🔥 This is a test comment!",
      createdAt: admin.firestore.Timestamp.now(),
    };

    const commentRef = await getDb().collection("posts").doc(postId).collection("comments").add(commentData);

    console.log(`Added test comment: ${commentRef.id} to post: ${postId}`);

    return {
      success: true,
      commentId: commentRef.id,
      postId,
      message: "Test comment added successfully",
    };
  } catch (error) {
    console.error("Error adding test comment:", error);
    throw new functions.https.HttpsError("internal", "Failed to add test comment");
  }
});

// Test function to manually add a share to trigger onPostShareUpdate
export const addTestShare = onCall(async (request) => {
  try {
    const {postId, userId, username} = request.data;

    if (!postId || !userId || !username) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "postId, userId, and username are required"
      );
    }

    // Add a share document to trigger the onPostShareUpdate function
    const shareData = {
      userId,
      username,
      createdAt: admin.firestore.Timestamp.now(),
    };

    const shareRef = await getDb().collection("posts").doc(postId).collection("shares").add(shareData);

    console.log(`Added test share: ${shareRef.id} to post: ${postId}`);

    return {
      success: true,
      shareId: shareRef.id,
      postId,
      message: "Test share added successfully",
    };
  } catch (error) {
    console.error("Error adding test share:", error);
    throw new functions.https.HttpsError("internal", "Failed to add test share");
  }
});

// Test function to check trending events
export const getTrendingEvents = onCall(async (request) => {
  try {
    const {limit = 10} = request.data;

    const eventsSnapshot = await getDb().collection("trending_events")
      .orderBy("timestamp", "desc")
      .limit(limit)
      .get();

    const events = eventsSnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));

    return {
      success: true,
      count: events.length,
      events,
    };
  } catch (error) {
    console.error("Error getting trending events:", error);
    throw new functions.https.HttpsError("internal", "Failed to get trending events");
  }
});

// Test function to check trending posts (simpler version)
export const getTrendingPostsSimple = onCall(async (request) => {
  try {
    const {limit = 10} = request.data;

    const postsSnapshot = await getDb().collection("posts")
      .where("trending", "==", true)
      .limit(limit)
      .get();

    const posts = postsSnapshot.docs.map((doc) => ({
      id: doc.id,
      trendingScore: doc.data().trendingScore || 0,
      trending: doc.data().trending || false,
      userId: doc.data().userId,
      username: doc.data().username,
      caption: doc.data().caption,
    }));

    return {
      success: true,
      count: posts.length,
      posts,
    };
  } catch (error) {
    console.error("Error getting trending posts:", error);
    throw new functions.https.HttpsError("internal", "Failed to get trending posts");
  }
});
